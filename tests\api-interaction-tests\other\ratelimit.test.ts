import request from 'supertest';
import * as socialApi from '../integration/lib/social-api';
import { TwokAccounts } from '../integration/lib/config';
import { StatusCodes } from 'http-status-codes';


describe('', () => {
  let usersTwok: TwokAccounts;
  let mgroupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(1, ["leader"]);
    await usersTwok.loginAll({});
  
    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 10,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mgroupId = socialApi.getGroupId(r);
  });
  
  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mgroupId);
    await usersTwok.logoutAll({});
  });
  


  it('rate limit test', async () => {
    let promises: Promise<request.Response>[] = [];

    // firing off a bunch of calls
    for (let i = 0; i < 500; i++) {
      promises.push(
        socialApi.getGroupInfo(
          usersTwok.acct["leader"],
          mgroupId
        )
      );
    }

    // wait for all calls to finish
    let results = await Promise.all(promises);

    // tally up occurrences for status codes
    let statusCodeTally: {[idx: number]: number} = {};
    for (let r of results) {
      if (!(r.status in statusCodeTally)){
        statusCodeTally[r.status] = 1;
      } else {
        statusCodeTally[r.status] += 1;
      }
    }

    // report
    console.log(statusCodeTally);

  }, 60000);




  it('one call', async () => {
    const actualGroupInfo = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mgroupId
    );

    console.log(actualGroupInfo.status);
    console.log(actualGroupInfo.body);
  });

});