import {
  DnaAccount, DnaAccountInfo, PlatformAccount, PlatformAccountInfo,
  FullAccount,
  TwokAccounts,TwokAccount,
  SteamAccountInfo,
} from '../../integration/lib/config';
import * as socialApi from '../../integration/lib/social-api';
import { StatusCodes } from 'http-status-codes';
import { writeFileSync, readFileSync } from 'fs';
import { join } from 'path';


/**
 * Return Codes:
 *
 * 0: no junk
 * 1: junk found and successfully cleaned up
 * 2: junk found but failed to clean up. Follow-up action required.
 */

/**
 * Note:
 *
 * - Health check isn't functional tests. The goal is to clean junks but not find bugs.
 *   So just attempt to delete without acting upon bad return code.
 * - Running in parallel (with async) means potential race condition.
 *   For example, both users get friends.  The faster user unfriended.  The slower user then would unfriend on an ID that's no longer a friend (hence error)
 *   And these kind of errors can be safely ignored.
 */


/**
 * Log to file
 */
function log(entry: string) {
  writeFileSync(join(__dirname, "account_health_check_log.txt"), `${entry}\n`, {flag: 'a'});
}

/**
 * Dump log to console
 */
function dumpLog() {
  const log = readFileSync(join(__dirname, "account_health_check_log.txt"), "utf-8");
  console.log("vvvvvv BEGIN account health check log dump vvvvvv");
  console.log(log);
  console.log("^^^^^^ END account health check log dump ^^^^^^");
}

/**
 * Replace a bad 2k account with a good one
 */
function replaceBadAccount(owner: string, id: string, remark: string) {
  const { execSync } = require('child_process');
  const fs = require('fs');

  console.log(`attemping to replace bad account ${id}`);
  execSync(`./replace_bad_account.sh "${owner}" "${id}" "${remark} ${Date.now()}"`, {stdio: "inherit", cwd: __dirname});
}

/**
 * Block Lists Cleanup
 */
const cleanBlockListDesc = "block list cleanup";

async function cleanBlockList(idx: string, twokAccount: TwokAccount) {
  // Find junks on blocklists and attempt to unblock them.
  let logPrefix: string = `[${twokAccount.publicId} - ${cleanBlockListDesc}]`;
  let foundJunksOnBlocklists: boolean = false;
  const blocklistInfo = await socialApi.getBlockList(twokAccount, {});

  if ((blocklistInfo.body.hasOwnProperty("items")) && (blocklistInfo.body.items.length > 0)) {
    foundJunksOnBlocklists = true;

    const r = await socialApi.delBlockList(twokAccount);

    log(`${logPrefix} found junks on blocklists and attempt to unblock them.\n` +
        `<unblock user result> status ${r.status} body ${JSON.stringify(r.body, null, 4)}\n` +
        `<blocked ids> ${JSON.stringify(blocklistInfo.body.items, null, 4)}`);
  }

  // Check for junks on blocklists again.
  if (foundJunksOnBlocklists == true) {
    const blocklistInfo = await socialApi.getBlockList(twokAccount, {});

    if ((blocklistInfo.body.hasOwnProperty("items")) && (blocklistInfo.body.items.length > 0)) {
      log(`${logPrefix} still found junks on blocklist.\n` +
          `<blocklist info> ${JSON.stringify(blocklistInfo.body, null, 4)}`);
      return 2;
    } else {
      return 1;
    }
  } else {
    return 0;
  }
}

/**
 * Friend Lists Cleanup
 */
const cleanFriendListDesc = "friend list cleanup";

async function cleanFriendList(idx: string, twokAccount: TwokAccount) {
  // Find junk friends and attempt to delete them.
  let logPrefix: string = `[${twokAccount.publicId} - ${cleanFriendListDesc}]`;
  let foundJunkFriends: boolean = false;
  const friendsInfo = await socialApi.getFriends(twokAccount, {});

  if (friendsInfo.body.hasOwnProperty("items")) {
    for (let f of friendsInfo.body.items) {
      foundJunkFriends = true;

    const r = await socialApi.deleteFriend(twokAccount, f.friendid);

      log(`${logPrefix} found junk friends and attempt to delete.\n` +
          `<delete friend result> status ${r.status} body ${JSON.stringify(r.body, null, 4)}\n` +
          `<friend id> ${f.friendid}`);
    }
  }

  // Check for junk friends again.
  if (foundJunkFriends == true) {
    const friendsInfo = await socialApi.getFriends(twokAccount, {});

    if ((friendsInfo.body.hasOwnProperty("items")) && (friendsInfo.body.items.length > 0)) {
      log(`${logPrefix} still found junk friends.\n` +
          `<friend info> ${JSON.stringify(friendsInfo.body, null, 4)}`);
      return 2;
    } else {
      return 1;
    }
  } else {
    return 0;
  }
}

/**
 * Group Lists Cleanup
 */
const cleanGroupListDesc = "group list cleanup";

async function cleanGroupList(idx: string, twokAccount: TwokAccount) {
  // Find junk groups. Attempt to delete regardless of whether user is leader or not.
  // Attempting to delete group even if user is a member, is to cover the scenario of "orphan group".
  // That is, a group without leader but no one is promoted. In that case, any member can delete the group.
  // If user is member and group is not orphan, leave the group.
  let logPrefix: string = `[${twokAccount.publicId} - ${cleanGroupListDesc}]`;
  let foundJunkGroups: boolean = false;
  const groupsInfo = await socialApi.getGroupsInfo(twokAccount);

  if (groupsInfo.body.hasOwnProperty("items")) {
    for (let g of groupsInfo.body.items) {
      foundJunkGroups = true;

    const resDeleteGroup = await socialApi.deleteGroup(twokAccount, g.groupid);

      log(`${logPrefix} found junk group and attempt to delete.\n` +
      `<delete group result> status ${resDeleteGroup.status} body ${JSON.stringify(resDeleteGroup.body, null, 4)}\n` +
      `<group id> ${g.groupid}`);

      // determine if the user is the leader
      let isLeader: boolean = false;
      for (let m of g.members) {
        if ((m.role == "leader") && (m.userid == twokAccount.publicId)) {
          isLeader = true;
          break;
        }
      }

      if (isLeader == true) {
        log(`${logPrefix} user is leader of ${g.groupid}.`);
      } else {
        if (resDeleteGroup.status == 200) {
          log(`${logPrefix} user is member and group successfully deleted. Orphan group ${g.groupid}.`);
        } else if (resDeleteGroup.status == 403) {
          const resLeaveGroup = await socialApi.leaveGroup(twokAccount, g.groupid);

          log(`${logPrefix} user is member therefore cannot delete. Attempt to leave\n` +
          `<leave group result> status ${resLeaveGroup.status} body ${JSON.stringify(resLeaveGroup.body, null, 4)}\n` +
          `<group id> ${g.groupid}`);
        } else {
          log(`${logPrefix} unexpected status code ${resDeleteGroup.status} when deleting group ${g.groupid}`);
        }
      }
    }
  }

  // Check for junk groups again.
  if (foundJunkGroups == true) {
    const groupsInfo = await socialApi.getGroupsInfo(twokAccount);

    if ((groupsInfo.body.hasOwnProperty("items")) && (groupsInfo.body.items.length > 0)) {
      log(`${logPrefix} still found junk groups.\n` +
          `<groups info> ${JSON.stringify(groupsInfo.body, null, 4)}`);
      return 2;
    } else {
      return 1;
    }
  } else {
    return 0;
  }
}

/**
 * Children Accounts Cleanup
 */
const unlinkChildrenAccountsDesc = "children accounts cleanup";

async function unlinkChildrenAccounts(idx: string, twokAccount: TwokAccount) {
  // Find linked accounts and attemp to unlink them.
  let logPrefix: string = `[${twokAccount.publicId} - ${unlinkChildrenAccountsDesc}]`;
  let foundLinkedAccounts: boolean = false;
  const linkedAccounts = await DnaAccount.getLinkedAccounts(twokAccount, 'platform');

  // need to check body size since status is always 200
  // TODO: see if the "if" is necessary and conform to the same style as friend group etc.
  //       simplify logging. Some msgs might not be necessary.
  if (linkedAccounts.body.length > 0) {
    foundLinkedAccounts = true;

    log(`${logPrefix} linked accounts info:\n` +
        `status ${linkedAccounts.status} body ${JSON.stringify(linkedAccounts.body, null, 4)}`);

    for (let a of linkedAccounts.body) {
      log(`${logPrefix} unlinking account ${a.accountId}:\n` +
          `linkType ${a.linkType} / firstPartyId ${a.firstPartyId} / alias ${a.alias}`);

      const r = await FullAccount.unlinkPlatformAccount(twokAccount, a.linkType);

      log(`${logPrefix} unlink result:\n` +
          `status ${r.status} body ${JSON.stringify(r.body, null, 4)}`);
    }
  }

  // Check for linked accounts again.
  if (foundLinkedAccounts == true) {
    const linkedAccounts = await DnaAccount.getLinkedAccounts(twokAccount, 'platform');

    if (linkedAccounts.body.length > 0) {
      log(`${logPrefix} still found linked accounts.\n` +
          `<linked accounts> ${JSON.stringify(linkedAccounts.body, null, 4)}`);
      return 2;
    } else {
      return 1;
    }
  } else {
    return 0;
  }
}

// TODO: transform unlinkParentAccount like others: add return codes, and log to file.
/**
 * Parent Accounts Cleanup
 */
async function unlinkParentAccount(idx: string, platformAccountInfo: PlatformAccountInfo) {
  // Find linked parents and attempt to unlink them.
  let foundLinkedParent: boolean = false;
  const linkedParent = await PlatformAccount.getLinkedParent(platformAccountInfo);
  if (linkedParent.status == StatusCodes.OK) {
    foundLinkedParent = true;
    console.log(`linked parent info: ${JSON.stringify(linkedParent.body, null, 4)}`);

    const r = await PlatformAccount.unlinkParent(platformAccountInfo);
    console.log(`unlink result: status ${r.status} body ${JSON.stringify(r.body, null, 4)}`);
  }

  // Check for linked parents again.
  if (foundLinkedParent == true) {
    console.log("After clean up, check again for linked parents.");
    const linkedParent = await PlatformAccount.getLinkedParent(platformAccountInfo);
    if (linkedParent.status == StatusCodes.OK) {
      let id: string = platformAccountInfo.publicId;

      throw new Error(`user index ${idx} (*${id.substring(id.length-4, id.length)}): still found linked parents.\n${JSON.stringify(linkedParent.body, null, 4)}`);
    } else {
      console.log(`user index ${idx}: no more linked parents.`);
    }
  } else {
    console.log("No linked parents!");
  }
}

/**
 * Twok Accounts User Info Check
 */
const twokAccountInfoCheckDesc = "2k account info check";

async function twokAccountInfoCheck(idx: string, twokAccount: TwokAccount) {
  let id: string = twokAccount.publicId;
  let em: string = twokAccount.email;
  let dn: string = twokAccount.displayName;

  console.log(`Public ID:    *${id.substring(id.length-4, id.length)}`);
  console.log(`Email:        *${socialApi.maskEmailAddr(em)}*`);
  console.log(`Display Name: *${dn.substring(dn.length-10, dn.length)}`);

  return 0;
}

/**
 * Steam Accounts User Info Check
 */
async function steamAccountInfoCheck(idx: string, steamAccountInfo: SteamAccountInfo) {
  let pid: string = steamAccountInfo.publicId;
  let sid: string = steamAccountInfo.platformId;
  let sname: string = steamAccountInfo.alias;

  console.log(`Public ID:          *${pid.substring(pid.length-4, pid.length)}`);
  console.log(`Steam ID:           *${sid.substring(sid.length-4, sid.length)}`);
  console.log(`Steam Account Name: *${sname.substring(sname.length-4, sname.length)}`);

  return 0;
}


/**
 * Run health checks concurrently across multiple accounts
 */

interface HealthCheckFunc {
  func: (idx: string, twokAccount: TwokAccount) => Promise<number>;
  desc: string;
}

// a dictionary to look up health check functions with a short string.
// a health check function consists of the actual function and a longer description.
const healthCheckFuncDict: Record<string, HealthCheckFunc> = {
  "block":        {func: cleanBlockList, desc: cleanBlockListDesc},
  "friend":       {func: cleanFriendList, desc: cleanFriendListDesc},
  "group":        {func: cleanGroupList, desc: cleanGroupListDesc},
  "childAccount": {func: unlinkChildrenAccounts, desc: unlinkChildrenAccountsDesc},
  "twokInfo":     {func: twokAccountInfoCheck, desc: twokAccountInfoCheckDesc},
};

// concurrent health checks per account
async function twokAcctHealthCheck(idx: string, twokAccount: TwokAccount, healthCheckFuncList: HealthCheckFunc[]) {
  let result: Promise<number>[] = [];

  for (let f of healthCheckFuncList) {
    result.push(f["func"](idx, twokAccount));
  }

  return result;
}

// concurrent health checks for all accounts
// this function takes in a list of strings indicating which health checks to be performed.
// they are used to look up healthCheckFuncDict and create a list of health check funcs.
export async function allTwokAcctHealthCheck(healthCheckList: string[], isReplacingBadAccounts: boolean, owner: string, isDumpingLog: boolean) {
  log(`==== ${new Date().toLocaleString()} =========================================`);

  // create a list of health check funcs from the input list of keys
  const healthCheckFuncList: HealthCheckFunc[] = [];
  for (let hc of healthCheckList) {
    healthCheckFuncList.push(healthCheckFuncDict[hc]);
  }

  // displaying the health checks that will be performed
  console.log("Health Checks:")
  for (let f of healthCheckFuncList) {
    console.log(f["desc"]);
  }

  // array of health check results. Each item is the list of health checks for an account.
  let healthCheckResult:Promise<Promise<number>[]>[] = [];

  // object representing all 2k accounts
  let allTwokAccounts = new TwokAccounts();

  // wait for all accounts to complete login
  await allTwokAccounts.loginAll({});

  // perform a list of health checks for each 2k account
  for (let [idx, twokAccount] of Object.entries(allTwokAccounts.acct)) {
    healthCheckResult.push(twokAcctHealthCheck(idx, twokAccount, healthCheckFuncList));
  }

  // wait for health checks to complete for all accounts
  let allResults = await Promise.all(healthCheckResult);

  // get an array of 2k accounts
  let acctArray = Object.values(allTwokAccounts.acct);

  // a dictionary of bad accounts, with keys being email, and vals being remarks.
  // these are params to the replace bad account function.
  let badAccountDict: Record<string, string> = {};

  // process health check results for all accounts
  for (let allResultsIdx in allResults) {
    let email = acctArray[allResultsIdx].email;

    // wait for all health checks to complete for the current account
    let result = await Promise.all(allResults[allResultsIdx]);

    console.log(`${email}`);

    // process the health check results for the current account
    // if all 0, then no junks for all checks
    // otherwise, take follow-up actions for non-zero results
    const isAllZero = result.every(item => item === 0);
    if (isAllZero == true) {
      console.log(`no junks!`);
    } else {
      for (let resultIdx in result) {
        if (result[resultIdx] == 1) {
          console.log(`${healthCheckFuncList[resultIdx]["desc"]}: found junks and successfully cleaned up`);
        } else if (result[resultIdx] == 2) {
          console.log(`${healthCheckFuncList[resultIdx]["desc"]}: found junks but failed to clean up`);

          if (email in badAccountDict) {
            badAccountDict[email] += `|${healthCheckFuncList[resultIdx]["desc"]}`
          } else {
            badAccountDict[email] = healthCheckFuncList[resultIdx]["desc"]
          }
        }
      }
    }

    // one blank line between results of each account
    console.log();
  }

  // wait for all accounts to complete logout
  await allTwokAccounts.logoutAll({});

  // replace bad accounts
  if (isReplacingBadAccounts == true) {
    for (let [id, remark] of Object.entries(badAccountDict)) {
      replaceBadAccount(owner, id, remark);
    }
  } else {
    if (Object.keys(badAccountDict).length > 0) {
      console.log("bad accounts found, but you've opted not to replace them with good accounts:");
      console.log(badAccountDict);
    }
  }

  // dump log to console
  if (isDumpingLog == true) {
    dumpLog();
  }
}