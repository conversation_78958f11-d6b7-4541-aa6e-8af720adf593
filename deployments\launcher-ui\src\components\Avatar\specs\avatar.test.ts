import { render } from '@testing-library/svelte';
import Avatar from '../Avatar.svelte';

describe('Avatar', () => {
  it('should render UI with initials', () => {
    const initials = 'ts';
    const { container } = render(Avatar, {
      props: { initials },
    });
    const initialsTag = container.querySelector('.innerInitials');
    expect(initialsTag.innerHTML).toEqual(initials);
  });

  it('should render UI with image src', () => {
    const imageSrc = 'imageSrc';
    const { container } = render(Avatar, {
      props: { src: imageSrc },
    });
    const imgTag = container.querySelector('.innerImage');
    expect(imgTag.getAttribute('src')).toEqual(imageSrc);
  });

  it('should render online status', () => {
    const imageSrc = 'imageSrc';
    const { container } = render(Avatar, {
      props: { src: imageSrc, status: 'online' },
    });

    const statusDiv = container.querySelector('.online');
    expect(statusDiv).not.toBeNull();
  });

  it('should render playing status', () => {
    const imageSrc = 'imageSrc';
    const { container } = render(Avatar, {
      props: { src: imageSrc, status: 'playing' },
    });

    const statusDiv = container.querySelector('.online .playing');

    expect(statusDiv).not.toBeNull();
  });
});
