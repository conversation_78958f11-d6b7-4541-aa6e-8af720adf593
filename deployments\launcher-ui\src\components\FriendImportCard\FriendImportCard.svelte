<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { SVGSteam } from '../../assets/icons';
  import { Avatar } from '../Avatar';
  import type { CustomEventMap } from '../CustomInput';
  import { CustomInput } from '../CustomInput';

  export let initials = '';
  export let avatar = '';
  export let displayName = '';
  export let userId = '';
  export let platform = '';
  export let selected = false;

  const dispatch = createEventDispatcher<CustomEventMap>();

  const dispatchChangeEvent = (event: Event, checked: boolean) => {
    event.preventDefault();
    event.stopPropagation();

    const payload: CustomEventMap['tileClick'] = {
      checked,
      value: userId,
    };
    dispatch('tileClick', payload);
  };

  function onTileClicked(event: Event) {
    dispatchChangeEvent(event, !selected);
  }
</script>

<style>
  .import-friend-card {
    background-color: var(
      --social-bg-color-import-friend-card,
      var(--default-bg-color-import-friend-card)
    );
    display: flex;
    align-items: center;
    padding: 0.625rem 1rem;
    box-sizing: border-box;
    border-radius: 0.25rem;
    margin-block-start: 0.5rem;
    min-width: 20.75rem;
    min-height: 3.75rem;
  }

  .import-friend-card:hover {
    cursor: pointer;
  }

  .import-friend-card .detail {
    color: rgba(255, 255, 255, 1);
    margin-left: 0.75rem;
    display: flex;
    flex-direction: column;
    line-height: 150%;
    text-transform: capitalize;
  }

  .detail .displayName {
    font-size: 1rem;
    font-weight: bold;
  }

  .detail .platform {
    font-size: 0.875rem;
    opacity: 0.6;
    display: flex;
    align-items: center;
  }

  .platform :global(svg) {
    width: 12px;
    height: 12px;
    margin-inline-end: 0.4rem;
  }
</style>

<div class="import-friend-card" on:click="{onTileClicked}">
  <div class="checkbox-container">
    <CustomInput type="checkbox" value="{userId}" checked="{selected}" />
  </div>
  <Avatar
    initials="{initials}"
    size="35px"
    src="{avatar}"
    showPresence="{false}"
  />
  <div class="detail">
    <span class="displayName">{displayName}</span>
    <span class="platform">
      {#if platform === 'steam'}
        <SVGSteam />
      {/if}
      {displayName}
    </span>
  </div>
</div>
