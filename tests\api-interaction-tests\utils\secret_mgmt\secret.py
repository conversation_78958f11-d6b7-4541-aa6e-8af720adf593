#!/usr/bin/env python3

from base64 import b64encode
from nacl import encoding, public
import requests
import json
import time
import re


repo_secrets_url = 'https://api.github.com/repos/take-two-t2gp/t2gp-social-service/actions/secrets'
h = {'Accept' : 'application/vnd.github.v3+json'}
a = ('', '')  # user name and personal access token

key_id=''
key=''

# source: https://docs.github.com/en/rest/actions/secrets#create-or-update-a-repository-secret
def encrypt(public_key: str, secret_value: str) -> str:
  """Encrypt a Unicode string using the public key."""
  public_key = public.PublicKey(public_key.encode("utf-8"), encoding.Base64Encoder())
  sealed_box = public.SealedBox(public_key)
  encrypted = sealed_box.encrypt(secret_value.encode("utf-8"))
  return b64encode(encrypted).decode("utf-8")


# For the API wrappers, see https://docs.github.com/en/rest/actions/secrets for references

#
def get_public_key():
    url='{}/public-key'.format(repo_secrets_url)
    r = requests.get(url, headers=h, auth=a)
    if r.status_code != 200:
        raise Exception("get public key failed. status {} body {}".format(r.status_code, r.text))
    else:
        return r

#
def get_all_secrets():
    url='{}{}'.format(repo_secrets_url, '?per_page=100')
    r = requests.get(url, headers=h, auth=a)
    if r.status_code != 200:
        raise Exception("get all secrets failed. status {} body {}".format(r.status_code, r.text))
    else:
        return r

#
def update_secret(secret_name, encrypted_value):
    url='{}/{}'.format(repo_secrets_url, secret_name)
    d = {
        'encrypted_value' : encrypted_value,
        'key_id' : key_id
    }
    return requests.put(url, headers=h, auth=a, json=d)

#
def delete_secret(secret_name):
    url='{}/{}'.format(repo_secrets_url, secret_name)
    r = requests.delete(url, headers=h, auth=a)
    if r.status_code != 204:
        raise Exception("delete secret failed. status {} body {}".format(r.status_code, r.text))
    else:
        return r

#
def read_twok_account_info(filename):
    result = []
    with open(filename, 'r') as f:
        for line in f:
            for x in line.split('/'):
                result.append(x.strip())
    return result

#
def read_twok_secret_keys(filename):
    result = []
    with open(filename, 'r') as f:
        for x in f.readline().split(','):
            result.append(x.strip())

    # remove the last item which is empty
    result.pop()
    return result



# Get public key and key id
r = get_public_key()
k = json.loads(r.text)
key_id = k['key_id']
key = k['key']


## Sample code for deleting secrets #########################
#r = get_all_secrets()
#r = json.loads(r.text)
#delete_list = []
#for secret in r['secrets']:
#    print(secret['name'])
#    m = re.search("INTERACTION_API_TEST_.*", secret['name'])
#    if m != None:
#        delete_list.append(m.group(0))
#
#for i in delete_list:
#    print('delete secret {}'.format(i))
#    delete_secret(i)
##########################################################



# Create/update secrets
acct_types = ['twok', 'steam']
for at in acct_types:
    al = read_twok_account_info('{}_account_info'.format(at))
    sl = read_twok_secret_keys('{}_secret_keys'.format(at))

    if len(al) != len(sl):
        print(">>> {} account info and secret keys have different item counts. Skip processing for this account type. <<<".format(at))
        break
    
    d = { sl[i]:al[i] for i in range(0, len(al)) }
    
    for k, v in d.items():
        r = update_secret(k, encrypt(key, v))

        if r.status_code == 201:
            action = 'CREATED'
        elif r.status_code == 204:
            action = 'UPDATED'
        else:
            raise Exception('update secret failed. status {} body {}'.format(r.status_code, r.text))

        print('secret: {} / {} {}'.format(k, v, action))

