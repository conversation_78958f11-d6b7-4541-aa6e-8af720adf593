package middleware

import (
	"bytes"
	"io"
	"net/http"
	"testing"

	"github.com/franela/goblin"
	"github.com/rs/zerolog"
)

func TestRecoverer(t *testing.T) {
	g := goblin.Goblin(t)
	zerolog.SetGlobalLevel(zerolog.FatalLevel)

	g.Describe("Recoverer", func() {
		g.It("should recover from panic", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			jsonbytes := []byte("{\"foo\":\"bar\"}")
			mock.r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))

			handler := Recoverer

			cb := func(w http.ResponseWriter, r *http.Request) {
				// cause panic
				var v *int = nil
				*v = 0
			}

			nextHandler := &TestHandler{CallCount: 0, Callback: cb}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.<PERSON><PERSON><PERSON>(nextHandler.CallCount).Equal(1)

			g.<PERSON><PERSON><PERSON>(mock.w.Code).Equal(http.StatusInternalServerError)

		})
	})
}
