/server/groups:
  post:
    summary: Create a Group
    operationId: serverCreateGroup
    tags:
      - Groups
    requestBody:      
      $ref: '../requests/trusted-requests.yaml#/serverCreateGroupRequestBody'
    responses:      
      $ref: '../responses/trusted-responses.yaml#/serverCreateGroupResponseBody'
/server/groups/{pGroupid}:
  delete:
    tags:
      - Groups
    summary: Disband/Delete a Group
    operationId: serverDeleteGroup
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverDeleteGroupResponseBody'      
  get:
    tags:
      - Groups
    summary: Get a Group
    operationId: serverGetGroup
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverGetGroupResponseBody'
  patch:
    tags:
      - Groups
    summary: Update a Group's size or metadata
    operationId: serverUpdateGroup
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverUpdateGroupRequestBody'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverUpdateGroupResponseBody'
/server/groups/{pGroupid}/members/{pMemberid}:
  patch:
    tags:
      - Groups
    summary: Update group member
    operationId: serverUpdateGroupMember
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
      - $ref: '../parameters/path.yaml#/pMemberid'
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverUpdateGroupMemberRequestBody'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverUpdateGroupMemberResponseBody'
  delete:
    tags:
      - Groups
    summary: Kick a member from group
    operationId: serverKickMemberFromGroup
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
      - $ref: '../parameters/path.yaml#/pMemberid'
      - name: reason
        in: query
        description: reason for removing a group member
        required: false
        schema:
          type: string
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverKickGroupMemberResponseBody'
/server/groups/{pGroupid}/control:
  post:
    tags:
      - Groups
    summary: Send a control message (json or binary)
    operationId: serverSendControlMessage
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverSendControlMessageRequestBody'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverSendControlMessageResponseBody'
/server/memberships/invites/groups/{pGroupid}:
  post:
    summary: Send invitation for group impersonating a user
    description: S2S method that works exactly like the Social API to send an invite to a group but the server impersonats a user.
      Does not naturally grant permissions for non members to invite.  Only works impersonating leader unless the group setting is set to allow member invites.
    tags:
      - Memberships
    operationId: serverSendInvitesForGroup
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverSendInviteRequestBody'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverSendInviteResponseBody'
/server/memberships/requests/groups/{pGroupid}:
  post:
    summary: Joins a user to group
    description: Join requests made from the Social Trusted API will add users to the group no matter what the joinRequestAction is on the group.
    tags:
      - Memberships
    operationId: serverSendJoinRequestsForGroup
    parameters:
      - $ref: '../parameters/path.yaml#/pGroupid'
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverJoinRequestRequestBody'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverJoinRequestResponseBody'
/server/endorsements/{pEndorsementName}/users/{pUserid}/reset:
  delete:
    tags:
      - Endorsements
    summary: reset endorsement for user.  this sets the resetable counter to 0.
    parameters:
      - $ref: '../parameters/path.yaml#/pEndorsementName'
      - $ref: '../parameters/path.yaml#/pUserid'
    operationId: resetEndorsementForUser
    responses:
      $ref: '../responses/public-responses.yaml#/resetEndorsementResponseBody'
/server/endorsements/{pEndorsementName}/users/{pUserid}/remove:
  delete:
    tags:
      - Endorsements
    summary: completely removes an endorsement for user.  as opposed to just reseting the resetable counter.
    parameters:
      - $ref: '../parameters/path.yaml#/pEndorsementName'
      - $ref: '../parameters/path.yaml#/pUserid'
    operationId: removeEndorsementForUser
    responses:
      $ref: '../responses/public-responses.yaml#/resetEndorsementResponseBody'
/server/discovery:
  get:
    tags:
      - Discovery
    summary: Find endpoints to use.  Nil discoveryid or Empty '?discoveryid=' will return all discovery. The trusted API Ignores the canList=true flag in the discovery list and will return all values.
    operationId: serverGetDiscovery
    parameters:
      - $ref: '../parameters/query.yaml#/discoveryid'
      - $ref: '../parameters/query.yaml#/discoveryPid'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverGetDiscoveryResponseBody'
  patch:
    tags:
      - Discovery
    summary: Upsert discovery entries.  It will add any new ids and update existing ids.
    operationId: serverUpsertDiscovery
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverUpsertDiscoveryRequestBody'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverUpsertDiscoveryResponseBody'
  delete:
    tags:
      - Discovery
    summary: Delete discovery entry. Nil or Empty '?id=' will delete all discovery info.
    operationId: serverDeleteDiscovery
    parameters:
      - $ref: '../parameters/query.yaml#/discoveryid'
      - $ref: '../parameters/query.yaml#/discoveryPid'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverDeleteDiscoveryResponseBody'
/server/version:
  get:
    security: []
    tags:
      - Status
    summary: Get the server version
    operationId: serverGetVersion
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverVersionResponseBody'
/server/health:
  get:
    tags:
      - Status
    security: []
    summary: Get the server health status
    operationId: serverGetHealth
    parameters:
      - $ref: '../parameters/query.yaml#/healthid'
    responses:
      $ref: '../responses/trusted-responses.yaml#/serverHealthResponseBody'
/server/auth/token:
  get:
    description: Get a JWT token for the current server instance
    operationId: serverGetToken
    tags:
      - Authentication
    responses:      
      $ref: '../responses/trusted-responses.yaml#/serverGetTokenResponseBody'
/server/auth/logout:
  post:
    description: Logs the current user out of the session
    operationId: serverLogout
    tags:
      - Authentication
    responses:      
      $ref: '../responses/trusted-responses.yaml#/serverLogoutResponseBody'
/server/auth/refresh:
  post:
    description: Refresh the JWT token. You will need to send the `refreshToken` from the login response. After logout, the `refreshToken` will not longer work. Recommended that game teams use DCL rather than our API to refresh token.
    operationId: serverRefreshToken
    tags:
      - Authentication
    requestBody:
      $ref: '../requests/trusted-requests.yaml#/serverRefreshTokenRequestBody'
    responses:      
      $ref: '../responses/trusted-responses.yaml#/serverGetTokenResponseBody'