package middleware

import (
	"testing"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/franela/goblin"
	"github.com/rs/zerolog"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
)

var TestJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjk5OTk5OTk5OTksImlhdCI6MTYyOTczNzk2MCwianRpIjoiMWY5ODlhMjk0ODZmNDg0Yjk0ZTA0ZWVlMWJiMzlhMDciLCJ0dHkiOjAsInBpZCI6IjkzZTk1NWM0MWY1MDRhNjNhNDFjNDY0OWFiZWUyMGE4IiwiZ2lkIjoiMjcyZGFjMTQ4NGRiNGZhZTk4ZTg5NWI1MThkYjQyYjAiLCJsb2MiOiJlbi1HQiIsImN0eSI6IlNoZWZmaWVsZCIsImN0ciI6IkdCIiwibGF0Ijo1My4zNzkzLCJsb24iOi0xLjQ2MDIsInJ0aSI6IjgyMTJlNjg1MjE1MjRjNmVhZjM2ZGJiNmRkOTM4ZjkzIiwicmV4IjoxNjI5NzQ1MTYwLCJpc3MiOiI3YjVkMmEwYmYxMmM0OGU4YWEzMTEwZTFlY2FkM2E1NCIsInN1YiI6ImUzZTliZTI5YjhhMTQ4MTc5NzAyMzQ4NDAxMDViYjU3IiwiYXR5IjoyLCJvdHkiOjk5LCJhZ3AiOjksInNpZCI6IjNlNWMwYWRkMTVhYjQyZjA5MmIwMmJkM2RhMTQzNGI3IiwiYWdyIjoxMDMwLCJmcGkiOiJXbm5FM2h0Ui9IQnkwWFNZemQ5N0d3SWxDSE13UEl0R2FWTjZpZzBaaWFVPSIsImZwYSI6IkJWMlQrcEhBaldYZGpYcWxZN0RXa3F3N1QrZDFyVkRlVk1pTTFYSnQ1YXM9IiwiZG9iIjoiL3lTem0vVzZ2STgzdGgyMlk2aXRFUT09IiwicGZpIjoiN2FlNDRlMDM2NWQ0NGE2NzhmNmQzOWM0OWQzY2M5YzQifQ.FHd5r4KbS61gvbAZ_EWCbpBuYi2IYJjfBNgPZG_jAm8"

func TestBearerJWT(t *testing.T) {
	g := goblin.Goblin(t)
	zerolog.SetGlobalLevel(zerolog.FatalLevel)

	g.Describe("BearerJWT", func() {
		g.It("should add jwt to request context", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			cfg := config.ConfigForTests()

			handler := BearerJWT(cfg, nil)

			mock.r.Header.Add("Authorization", "Bearer "+TestJWT)

			nextHandler := &TestHandler{CallCount: 0}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.Assert(nextHandler.CallCount).Equal(1)

			ctx := nextHandler.Request.Context()
			g.Assert(ctx.Value(constants.BearerAuthJWT).(*jwt.Token)).IsNotNil()
			g.Assert(ctx.Value(constants.BearerAuthString).(string)).Equal(TestJWT)
		})
	})
}
