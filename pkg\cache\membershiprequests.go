package cache

import (
	"context"
	"github.com/aws/aws-sdk-go-v2/aws"
	"net/http"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

// DeleteMembership delete membership index.  In most cases, it is better to call RemoveMembershipRequestFromGroup to delete a membership request.
// This call exists publicly to allow for the deletion of a membership request that has not been added to a group or if a group is nil.
func (rc *RedisCache) DeleteMembership(ctx context.Context, request *apipub.MembershipRequest) error {
	if request == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsMembershipNotFound)
	}

	if request.Productid == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsInvalidProductID)
	}

	return rc.deleteSingleMembershipRequest(ctx, request)
}

func (rc *RedisCache) setMembership(ctx context.Context, request *apipub.MembershipRequest) error {
	log := logger.FromContext(ctx)
	if request == nil {
		return errs.New(http.StatusInternalServerError, errs.EGroupsModifyMembershipFailed)
	}
	if request.Productid == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsInvalidProductID)
	}

	timeNow := time.Now()
	//expireAt is for expiration tracker.  ttl is for object ttl
	expireAt := timeNow.Add(time.Duration(rc.cfg.TtlMembership) * time.Second)
	ttl := time.Duration(rc.cfg.TtlMembership) * time.Second
	if request.Ttl != nil && *request.Ttl > 0 {
		expireAt = timeNow.Add(time.Duration(*request.Ttl) * time.Second)
		ttl = time.Duration(*request.Ttl) * time.Second
	}
	expireAtUnix := expireAt.Unix()

	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	productid := *request.Productid
	groupid := request.Groupid
	approverid := request.Approverid
	status := request.Status
	memberid := request.Memberid

	request.TeleMeta = nil
	key := request.RedisKey(tenant)
	//set Object.
	err2 := setCachedObject(ctx, rc, request, key, ttl)
	if err2 != nil {
		log.Error().Err(err2).Interface("membership", request).Msg("failed to set group")
	}

	//set Indexes
	memberSubject := index.NewUserSubject(tenant, productid, memberid)
	if memberSubject == nil {
		log.Error().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for memberssub").Msg("failed to create secondary index for memberssub")
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidMemberID)
	}
	approverSubject := index.NewUserSubject(tenant, productid, approverid)
	if approverSubject == nil {
		log.Error().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for approversub").Msg("failed to create secondary index for approversub")
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
	}
	if status == apipub.Requested {
		//set Requested By
		requestedByKey := memberSubject.JoinRequestedByKey()
		if requestedByKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedby").Msg("failed to create secondary index for requestedby")
		} else {
			requestedByIdx := index.NewSecondaryIndex(*requestedByKey, apipub.BuildUserRedisKey(tenant, memberid))
			err2 = rc.setSecondaryIndex(ctx, requestedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedByIdx.IdxKey()).Msg("failed to set secondary index for join request")
			}
		}

		// set Requested Of
		requestedOfKey := approverSubject.JoinRequestedOfKey()
		if requestedOfKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedof").Msg("failed to create secondary index for requestedof")
		} else {
			requestedOfIdx := index.NewSecondaryIndex(*requestedOfKey, apipub.BuildUserRedisKey(tenant, approverid))
			err2 = rc.setSecondaryIndex(ctx, requestedOfIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedOfIdx.IdxKey()).Msg("failed to set secondary index for join request")
			}
		}

		// set Requested To
		requestedToKey := memberSubject.RequestedToJoinKey()
		if requestedToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedtojoinvia").Msg("failed to create secondary index for requestedtojoinvia")
		} else {
			requestedToIdx := index.NewSecondaryIndex(*requestedToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.setSecondaryIndex(ctx, requestedToIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedToIdx.IdxKey()).Msg("failed to set secondary index for join request")
			}
		}

		// set Requested To Group Via
		requestedToViaKey := memberSubject.RequestedViaToJoinKey(approverid)
		if requestedToViaKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedviatojoin").Msg("failed to create secondary index for requestedviatojoin")
		} else {
			requestedToViaIdx := index.NewSecondaryIndex(*requestedToViaKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.setSecondaryIndex(ctx, requestedToViaIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedToViaIdx.IdxKey()).Msg("failed to set secondary index for join request")
			}
		}
		// set Group Requests
		groupSubject := index.NewGroupSubject(tenant, productid, groupid)
		if groupSubject == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for groupsub").Msg("failed to create secondary index for groupsub")
		} else {
			groupMembersKey := groupSubject.GroupJoinRequestsKey()
			if groupMembersKey == nil {
				log.Error().Str("groupid", groupid).Msg("failed to create secondary index for join request")
			} else {
				groupMembersIdx := index.NewSecondaryIndex(*groupMembersKey, apipub.BuildUserRedisKey(tenant, approverid))
				err2 = rc.setSecondaryIndex(ctx, groupMembersIdx)
				if err2 != nil {
					log.Error().Err(err2).Str("idxKey", groupMembersIdx.IdxKey()).Msg("failed to set secondary index for join request")
				}
			}
		}
	} else if status == apipub.Invited {

		//set Invited By
		invitedKey := memberSubject.InvitedByKey()
		if invitedKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedby").Msg("failed to create secondary index for invitedby")
		} else {
			invitedByIdx := index.NewSecondaryIndex(*invitedKey, apipub.BuildUserRedisKey(tenant, memberid))
			err2 := rc.setSecondaryIndex(ctx, invitedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedByIdx.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}

		// set Invited
		invitedByKey := approverSubject.InvitedKey()
		if invitedByKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invited").Msg("failed to create secondary index for invited")
		} else {
			invitedByIdx := index.NewSecondaryIndex(*invitedByKey, apipub.BuildUserRedisKey(tenant, approverid))
			err2 = rc.setSecondaryIndex(ctx, invitedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedByIdx.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}
		// set Invited To
		invitedToKey := memberSubject.InvitedToKey()
		if invitedToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedto").Msg("failed to create secondary index for invitedto")
		} else {
			invitedToIdx := index.NewSecondaryIndex(*invitedToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.setSecondaryIndex(ctx, invitedToIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedToIdx.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}
		// set Invited By To
		invitedByToKey := memberSubject.InvitedByToKey(approverid, request.IsFirstPartyInvite)
		if invitedByToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedbyto").Msg("failed to create secondary index for invitedbyto")
		} else {
			invitedByIdx := index.NewSecondaryIndex(*invitedByToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.setSecondaryIndex(ctx, invitedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedByIdx.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}

		// set User Invites
		invitesToKey := memberSubject.InvitesKey()
		if invitesToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitesto").Msg("failed to create secondary index for invitesto")
		} else {
			invitesToIdx := index.NewSecondaryIndex(*invitesToKey, apipub.BuildMembershipRequestKey(tenant, productid, groupid, memberid, approverid, request.IsFirstPartyInvite))
			err2 = rc.setSecondaryIndex(ctx, invitesToIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitesToIdx.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}

		// set Group Invites
		groupSubject := index.NewGroupSubject(tenant, productid, groupid)
		if groupSubject == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for groupsub").Msg("failed to create secondary index for groupsub")
		} else {
			groupMembersKey := groupSubject.GroupInvitesKey()
			if groupMembersKey == nil {
				log.Error().Str("groupid", groupid).Msg("failed to create secondary index for invite")
			} else {
				groupMembersIdx := index.NewSecondaryIndex(*groupMembersKey, apipub.BuildUserRedisKey(tenant, approverid))
				err2 = rc.setSecondaryIndex(ctx, groupMembersIdx)
				if err2 != nil {
					log.Error().Err(err2).Str("idxKey", groupMembersIdx.IdxKey()).Msg("failed to set secondary index for invite")
				}
			}
		}
	}
	rc.writeMembershipExpirationIdx(ctx, request, expireAtUnix)
	return nil
}

// func setMembershipRequest(rdb *RedisCache, ctx context.Context, idxKey, groupKey, groupId, approverId string, ttl time.Duration) error {
// 	_, err := rdb.ecWriteClient.pipelined(ctx, func(p redis.Pipeliner) error {
// 		err := p.zAdd(ctx, idxKey, redis.Z{
// 			Score:  1,
// 			Member: groupKey,
// 		}).Err()
// 		if err != nil {
// 			return err
// 		}
// 		err = p.expire(ctx, idxKey, ttl).Err()
// 		if err != nil {
// 			return err
// 		}
// 		return err
// 	})

// 	return err
// }

func (rc *RedisCache) GetInvitesForUser(ctx context.Context, memberid, productid string, limit *int64, next *string) (*[]*apipub.MembershipRequest, string, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	memberSubject := index.NewUserSubject(tenant, productid, memberid)
	if memberSubject == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	invitesKey := memberSubject.InvitesKey()
	if invitesKey == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	invitesIdx := index.NewSecondaryIndex(*invitesKey, "")

	results, it, err := getObjsFromSecondaryIndex[apipub.MembershipRequest](ctx, rc, invitesIdx, limit, next, false)
	//if group related redis object is missing, ignore error
	if err != nil && errs.IsEqual(err, errs.ERedisObjectMissing) {
		err = nil
	}
	if err != nil {
		log.Error().Err(err).Str("key", *invitesKey).Msgf("failed to get groups from cache")
	}

	return results, it, err
}

func (rc *RedisCache) GetJoinRequestsByApproverId(ctx context.Context, approverid, productid string, limit *int64, next *string) (*[]*apipub.MembershipRequest, string, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	approverSubject := index.NewUserSubject(tenant, productid, approverid)
	if approverSubject == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	requestedOfKey := approverSubject.RequestedViaToJoinKey(approverid)
	if requestedOfKey == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	results, it, err2 := rc.getGroupsFromIdxKey(ctx, *requestedOfKey, limit, next)
	if err2 != nil {
		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed)
	}
	if results == nil {
		return nil, "", nil
	}

	var memberships []*apipub.MembershipRequest
	includedGroups := make(map[string]bool)
	for j, group := range *results {
		if group == nil || *group.MembershipRequests == nil {
			continue
		}
		for i, request := range *group.MembershipRequests {
			if includedGroups[group.Groupid] {
				continue
			}
			if request.Memberid == approverid && request.Status == apipub.Requested {
				memberships = append(memberships, &(*(*results)[j].MembershipRequests)[i])
				includedGroups[group.Groupid] = true
			}
		}
	}

	return &memberships, it, nil
}

func (rc *RedisCache) getGroupsFromIdxKey(ctx context.Context, key string, limit *int64, next *string) (*[]*apipub.GroupResponse, string, error) {
	log := logger.FromContext(ctx)
	idx := index.NewSecondaryIndex(key, "")

	results, it, err := getObjsFromSecondaryIndex[apipub.GroupResponse](ctx, rc, idx, limit, next, false)
	//if group related redis object is missing, ignore error
	if err != nil && errs.IsEqual(err, errs.ERedisObjectMissing) {
		err = nil
	}
	if err != nil {
		log.Error().Err(err).Str("key", key).Msgf("failed to get groups from cache")
	}
	return results, it, err
}

func (rc *RedisCache) JoinRequestExistsInCache(ctx context.Context, memberid, productid, groupid, approverid string) bool {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	memberSubject := index.NewUserSubject(tenant, productid, memberid)
	if memberSubject == nil {
		return false
	}
	key := memberSubject.RequestedViaToJoinKey(approverid)
	if key == nil {
		return false
	}
	return rc.groupMembershipRequestExists(ctx, *key, productid, groupid)
}

func (rc *RedisCache) InviteExistsInCache(ctx context.Context, memberid, productid, groupid, approverid string, isFirstParty *bool) bool {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	memberSubject := index.NewUserSubject(tenant, productid, memberid)
	if memberSubject == nil {
		return false
	}
	key := memberSubject.InvitedByToKey(approverid, isFirstParty)
	if key == nil {
		return false
	}

	return rc.groupMembershipRequestExists(ctx, *key, productid, groupid)
}

func (rc *RedisCache) groupMembershipRequestExists(ctx context.Context, key, productid, groupid string) bool {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	groupKey := apipub.BuildGroupRedisKey(tenant, productid, groupid)
	return rc.existsInSortedSet(ctx, key, groupKey)
}

// ClearAllMemberships delete all memberships for user in group
func (rc *RedisCache) ClearAllMemberships(ctx context.Context, memberid, productid, groupid string) {
	log := logger.FromContext(ctx)
	group, err := rc.GetGroup(ctx, productid, groupid)
	if group == nil || group.Groupid != groupid || err != nil {
		return
	}
	if group.MembershipRequests != nil {
		tenant := identity.GetTenantFromCtx(ctx, rc.id)
		groupKey := apipub.BuildGroupRedisKey(tenant, productid, groupid)
		//Iterate to delete indexes
		for _, membership := range *group.MembershipRequests {
			if membership.Memberid != memberid {
				continue
			}
			membershipJSONPath := buildMembershipJSONPath(productid, groupid, memberid, membership.Approverid)
			ret := rc.jSONDel(ctx, groupKey, membershipJSONPath)
			log.Debug().Int64("ret", ret.Val()).Interface("membership", membership).Msg("delete membership return")
			err := rc.deleteSingleMembershipRequest(ctx, &membership)
			if err != nil {
				log.Error().Err(err).Interface("membership", membership).Msg("failed to delete membership")
			}
		}
	}
}

func (rc *RedisCache) deleteSingleMembershipRequest(ctx context.Context, request *apipub.MembershipRequest) error {

	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	if request == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsMembershipNotFound)
	}

	// for invites, isFirstPartyTrue is not always able to be sent.   to ensure we delete the membership correctly we will attempt to delete the index for both first party and nont.
	if request.Status == apipub.Invited || request.Status == apipub.Revoked {
		request.IsFirstPartyInvite = aws.Bool(true)
	}
	err2 := rc.DeleteCachedObj(ctx, request.RedisKey(tenant))
	if err2 != nil {
		log.Error().Err(err2).Interface("membership", request).Bool("isFirstParty", true).Msg("failed to set group")
	}
	if request.Status == apipub.Invited || request.Status == apipub.Revoked {
		request.IsFirstPartyInvite = aws.Bool(false)
		err2 = rc.DeleteCachedObj(ctx, request.RedisKey(tenant))
		if err2 != nil {
			log.Error().Err(err2).Interface("membership", request).Bool("isFirstParty", false).Msg("failed to set group")
		}
	}

	groupid := request.Groupid
	productid := *request.Productid
	memberid := request.Memberid
	approverid := request.Approverid
	status := request.Status

	// del Indexes
	memberSubject := index.NewUserSubject(tenant, productid, memberid)
	if memberSubject == nil {
		log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for membersub").Msg("failed to create secondary index for membersub")
	}
	approverSubject := index.NewUserSubject(tenant, productid, approverid)
	if approverSubject == nil {
		log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for approversub").Msg("failed to create secondary index for approversub")
	}

	if status == apipub.Requested {
		//Delete Requested By
		requestedByKey := memberSubject.JoinRequestedByKey()
		if requestedByKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for joinrequestedby").Msg("failed to create secondary index for joinrequestedby")
		} else {
			requestedByIdx := index.NewSecondaryIndex(*requestedByKey, apipub.BuildUserRedisKey(tenant, memberid))
			err2 := rc.delSecondaryIndex(ctx, requestedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedByIdx.IdxKey()).Msg("failed to del secondary index for join request")
			}
		}

		// Delete Requested Of
		requestedOfKey := approverSubject.JoinRequestedOfKey()
		if requestedOfKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedof").Msg("failed to create secondary index for requestedof")
		} else {
			requestedOfIdx := index.NewSecondaryIndex(*requestedOfKey, apipub.BuildUserRedisKey(tenant, approverid))
			err2 = rc.delSecondaryIndex(ctx, requestedOfIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedOfIdx.IdxKey()).Msg("failed to del secondary index for join request")
			}
		}

		// Delete Requested To
		requestedToKey := memberSubject.RequestedToJoinKey()
		if requestedToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedtojoin").Msg("failed to create secondary index for requestedtojoin")
		} else {
			requestedToIdx := index.NewSecondaryIndex(*requestedToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.delSecondaryIndex(ctx, requestedToIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedToIdx.IdxKey()).Msg("failed to del secondary index for join request")
			}
		}

		// Delete Requested To Group Via
		requestedToViaKey := memberSubject.RequestedViaToJoinKey(approverid)
		if requestedToViaKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for requestedviatojoin").Msg("failed to create secondary index for requestedviatojoin")
		} else {
			requestedToViaIdx := index.NewSecondaryIndex(*requestedToViaKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.delSecondaryIndex(ctx, requestedToViaIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", requestedToViaIdx.IdxKey()).Msg("failed to del secondary index for join request")
			}
		}

		// Delete Group Requests
		groupSubject := index.NewGroupSubject(tenant, productid, groupid)
		if groupSubject == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for groupsub").Msg("failed to create secondary index for groupsub")
		} else {
			groupMembersKey := groupSubject.GroupJoinRequestsKey()
			if groupMembersKey == nil {
				log.Error().Err(err2).Str("groupid", groupid).Msg("failed to create secondary index for join request")
			} else {

				groupMembersIdx := index.NewSecondaryIndex(*groupMembersKey, apipub.BuildUserRedisKey(tenant, approverid))
				err2 = rc.delSecondaryIndex(ctx, groupMembersIdx)
				if err2 != nil {
					log.Error().Err(err2).Str("idxKey", groupMembersIdx.IdxKey()).Msg("failed to del secondary index for join request")

				}
			}
		}
	} else if status == apipub.Invited || status == apipub.Revoked {
		//Delete Invited By
		invitedKey := memberSubject.InvitedByKey()
		if invitedKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for membersub").Msg("failed to create secondary index for membersub")
		} else {
			invitedByIdx := index.NewSecondaryIndex(*invitedKey, apipub.BuildUserRedisKey(tenant, memberid))
			err2 := rc.delSecondaryIndex(ctx, invitedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedByIdx.IdxKey()).Msg("failed to del secondary index for invite")
			}
		}

		// Delete Invited
		invitedByKey := approverSubject.InvitedKey()
		if invitedByKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedby").Msg("failed to create secondary index for invitedby")
		} else {
			invitedByIdx := index.NewSecondaryIndex(*invitedByKey, apipub.BuildUserRedisKey(tenant, approverid))
			err2 = rc.delSecondaryIndex(ctx, invitedByIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedByIdx.IdxKey()).Msg("failed to del secondary index for invite")
			}
		}

		// Delete Invited To
		invitedToKey := memberSubject.InvitedToKey()
		if invitedToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedto").Msg("failed to create secondary index for invitedto")
		} else {
			invitedToIdx := index.NewSecondaryIndex(*invitedToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.delSecondaryIndex(ctx, invitedToIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedToIdx.IdxKey()).Msg("failed to del secondary index for invite")
			}
		}

		// Delete Invited By To
		invitedByToKey := memberSubject.InvitedByToKey(approverid, aws.Bool(true))
		if invitedByToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedbyto").Msg("failed to create secondary index for invitedbyto")
		} else {
			invitedByTo := index.NewSecondaryIndex(*invitedByToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.delSecondaryIndex(ctx, invitedByTo)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitedByTo.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}

		// Delete Invited By To key for reverse of isfirstparty.  we try to delete both cache obj earlier.  this is for th index.
		revInvitedByToKey := memberSubject.InvitedByToKey(approverid, aws.Bool(false))
		if revInvitedByToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitedbyto").Msg("failed to create secondary index for invitedbyto")
		} else {
			revInvitedByTo := index.NewSecondaryIndex(*revInvitedByToKey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 = rc.delSecondaryIndex(ctx, revInvitedByTo)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", revInvitedByTo.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}

		// Delete User Invites
		invitesToKey := memberSubject.InvitesKey()
		if invitesToKey == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for invitesto").Msg("failed to create secondary index for invitesto")
		} else {
			invitesTo := index.NewSecondaryIndex(*invitesToKey, apipub.BuildMembershipRequestKey(tenant, productid, groupid, memberid, approverid, aws.Bool(true)))
			err2 = rc.delSecondaryIndex(ctx, invitesTo)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", invitesTo.IdxKey()).Msg("failed to set secondary index for invite")
			}
			// delete opposite of isFirstParty as well
			revInvitesTo := index.NewSecondaryIndex(*invitesToKey, apipub.BuildMembershipRequestKey(tenant, productid, groupid, memberid, approverid, aws.Bool(false)))
			err2 = rc.delSecondaryIndex(ctx, revInvitesTo)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", revInvitesTo.IdxKey()).Msg("failed to set secondary index for invite")
			}
		}

		// Delete Group Invites
		groupSubject := index.NewGroupSubject(tenant, productid, groupid)
		if groupSubject == nil {
			log.Warn().Str("memberid", memberid).Str("groupid", groupid).Str("approverid", approverid).Str("status", string(status)).Str("event", "failed to create secondary index for groupssub").Msg("failed to create secondary index for grouppssub")
		} else {
			groupMembersKey := groupSubject.GroupInvitesKey()
			if groupMembersKey == nil {
				log.Error().Err(err2).Str("groupid", groupid).Msg("failed to create secondary index for invite")
			} else {
				groupMembersIdx := index.NewSecondaryIndex(*groupMembersKey, apipub.BuildUserRedisKey(tenant, approverid))
				err2 = rc.delSecondaryIndex(ctx, groupMembersIdx)
				if err2 != nil {
					log.Error().Err(err2).Str("idxKey", groupMembersIdx.IdxKey()).Msg("failed to del secondary index for invite")
				}
			}
		}
	}

	return nil
}

// GetInvite get pending invite from cache
func (rc *RedisCache) GetInvite(ctx context.Context, memberid, productid, groupid, approverid string, isFirstParty *bool) (*apipub.MembershipRequest, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	return getCachedObject[apipub.MembershipRequest](ctx, rc, apipub.BuildMembershipRequestKey(tenant, productid, groupid, memberid, approverid, isFirstParty))
}

// writeMembershipExpirationIdx writes to a sharded key based on the first character of userid do not cause a hotshard with jsut 1 list
func (rc *RedisCache) writeMembershipExpirationIdx(ctx context.Context, membership *apipub.MembershipRequest, expireAtUnix int64) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	if membership == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsMembershipNotFound)
	}
	if membership.Memberid == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EInvalidUserID)
	}

	return rc.writeExpirationIdx(ctx, membership.RedisKey(tenant), membership.Memberid, expireAtUnix)
}
