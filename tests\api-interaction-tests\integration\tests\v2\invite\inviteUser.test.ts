import request from 'supertest';
import { TwokAccounts } from '../../../lib/config';
import * as socialApi from '../../../lib/social-api';
import { StatusCodes } from 'http-status-codes';

// eslint-disable-next-line max-lines-per-function
describe('[public v2]', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('Invite can be sent after invite was declined', async () => {
    let testCase = {
      description: "leader sends another invitation to member after invite was declined",
      expected: "member gets the invitation"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId,
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 declines the invitation.
    r = await socialApi.declineInvite(usersTwok.acct["member1"], groupId, usersTwok.acct["leader"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    // Leader sends another invitation to member1.
    r = await socialApi.invite(usersTwok.acct["leader"], groupId, {}, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualInviteInfo: request.Response = await socialApi.getInvite(
      usersTwok.acct["member1"]
    );

    // Expect member1 gets the invitation.
    const expectedInviteInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.arrayContaining([
          expect.objectContaining({
            groupid: groupId,
            memberid: usersTwok.acct["member1"].publicId,
            // status: 'invited',
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualInviteInfo).toMatchObject(expectedInviteInfo)},
      testCase,
      {
        resp: actualInviteInfo,
        additionalInfo: {
          "fail reason": "member doesn't get the invitation"
        }
      }
    );
  });

  it('Invite self', async () => {
    let testCase = {
      description: "user sends an invitation to self",
      expected: "the invitation is not sent out"
    };

    // Leader sends an invitation to himself/herself.
    const r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["leader"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    const actualInviteInfo: request.Response = await socialApi.getInvite(
      usersTwok.acct["leader"]
    );

    // The leader isn't invited.
    socialApi.testStatus(StatusCodes.OK, actualInviteInfo);
    socialApi.expectMore(
      () => {expect(actualInviteInfo.body).toEqual({})},
      testCase,
      {
        resp: actualInviteInfo,
        additionalInfo: {
          "fail reason": "the invite list is not empty"
        }
      }
    );
  });
});

// eslint-disable-next-line max-lines-per-function
describe('[public v2]', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('User accepts invite[happy]', async () => {
    let testCase = {
      description: "send group invitation to a user; the user accepts the invitation",
      expected: "the user joins the group"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect member1 joins the group.
    // previous membership request status is no longer present.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
        membershipRequests: null,
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "user doesn't join the group"
        }
      }
    );
  });

  it('User declines invite[happy]', async () => {
    let testCase = {
      description: "send group invitation to a user; the user declines the invitation",
      expected: "the user does not join the group"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 declines the invitation.
    r = await socialApi.declineInvite(usersTwok.acct["member1"], groupId, usersTwok.acct["leader"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect member1 does not join the group.
    // previous membership request status is no longer present.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: [
          {
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          },
        ],
        membershipRequests: null,
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "leader isn't the only member in the group; or the rejected invitation might still be present in the membershipRequests list"
        }
      }
    );
  });

  it('Accepting invite that does not belong to the logged-in user is not allowed', async () => {
    let testCase = {
      description: "user tries to accept an invitation that not belong to herself/himself",
      expected: "the user does not join the group"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member2 who doesn't own the Member1 account and tries to accept an invitation sent to Member1.
    // - use member2Token to accept the invitation
    r = await socialApi.acceptInvite(
      usersTwok.acct["member2"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.NOT_FOUND, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );
    // Expect member1 does not join the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: [
          {
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected user is present in the group"
        }
      }
    );
  });

  it('User can not accept the invitation as the group is deleted', async () => {
    let testCase = {
      description: "user tries to accept the invitation after the group is deleted",
      expected: "the invitation doesn't exist"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Leader deletes the group.
    r = await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 tries to accept the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.NOT_FOUND, r);

    const actualInviteInfo: request.Response = await socialApi.getInvite(
      usersTwok.acct["member1"]
    );

    // The invitation does not exist.
    socialApi.testStatus(StatusCodes.OK, actualInviteInfo);
    socialApi.expectMore(
      () => {expect(actualInviteInfo.body).toEqual({})},
      testCase,
      {
        resp: actualInviteInfo,
        additionalInfo: {
          "fail reason": "the invitation list is not empty"
        }
      }
    );
  });

  it('Invite cannot be sent to an existing group member', async () => {
    let testCase = {
      description: "send an invitation to an existing group member",
      expected: "the existing group member does not get the invitation"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );
    // Expect member1 joins the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the member does not join the group"
        }
      }
    );

    // Leader sends an invitation to member1 who's already a memeber.
    r = await socialApi.invite(usersTwok.acct["leader"], groupId, {}, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualInviteInfo: request.Response = await socialApi.getInvite(
      usersTwok.acct["member1"]
    );

    // Expect member1 does not get the invitation.
    socialApi.testStatus(StatusCodes.OK, actualInviteInfo);
    socialApi.expectMore(
      () => {expect(actualInviteInfo.body).toEqual({})},
      testCase,
      {
        resp: actualInviteInfo,
        additionalInfo: {
          "fail reason": "the invitation list is not empty"
        }
      }
    );
  });

  it('User can not accept the nonexisting invitation', async () => {
    let testCase = {
      description: "user tries to accept the nonexisting invitation",
      expected: "the user does not join the group"
    };

    // Member2 tries to accept the nonexisting invitation.
    const r: request.Response = await socialApi.acceptInvite(
      usersTwok.acct["member2"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.NOT_FOUND, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect member2 does not join the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: [
          {
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected user is present in group"
        }
      }
    );
  });
});

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 2,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('Can not invite user when the group is full[public v2]', async () => {
    let testCase = {
      description: "invite user when the group is full",
      expected: "the invitation is not sent out"
    };

    // Leader sends an invitation to member1.
    let r = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Leader sends an invitation to member2.
    r = await socialApi.invite(usersTwok.acct["leader"], groupId, {}, usersTwok.acct["member2"].publicId);
    socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, r);

    let actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["leader"], groupId);

    // Expect member2 does not join the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the original leader and member1 are not in the group"
        }
      }
    );
    socialApi.expectMore(
      () => {expect(actualGroupInfo.body.members.length).toEqual(2)}, // 2 members: leader and member1
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the member invited when group was full might have joined the group"
        }
      }
    );
  });
});

// eslint-disable-next-line max-lines-per-function
describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canMembersInvite: true,
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('Member can invite[public v2 happy]', async () => {
    let testCase = {
      description: "group member can send invite",
      expected: "invited user can accept the invite and join the group"
    };

    // Leader sends an invitation to member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 sends an invitation to member2.
    r = await socialApi.invite(usersTwok.acct["member1"], groupId, {}, usersTwok.acct["member2"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    // Member2 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member2"],
      groupId,
      usersTwok.acct["member1"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.getGroupInfo(usersTwok.acct["leader"], groupId);

    // Expect member1 and member2 join the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member2"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(r).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: r,
        additionalInfo: {
          "fail reason": "unexpected group member list"
        }
      }
    );
  });
});

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 2,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async done => {
    await socialApi.delBlockList(usersTwok.acct["member1"]);
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('Blocked user cannot send group invite to the blocker[public v2 happy]', async () => {
    let testCase = {
      description: "blocked user sends group invite to the blocker",
      expected: "blocker does not receive the group invitation from the blocked user"
    };

    // Member1 adds the leaderUserId to the block list
    let r: request.Response = await socialApi.updateBlockList(
      usersTwok.acct["member1"],
      { userids: [usersTwok.acct["leader"].publicId] }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Leader sends an invitation to member1.
    r = await socialApi.invite(usersTwok.acct["leader"], groupId, {}, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualInviteInfo: request.Response = await socialApi.getInvite(
      usersTwok.acct["member1"]
    );

    // Expect member1 does not get the invitation.
    socialApi.testStatus(StatusCodes.OK, actualInviteInfo);
    socialApi.expectMore(
      () => {expect(actualInviteInfo.body).toEqual({})},
      testCase,
      {
        resp: actualInviteInfo,
        additionalInfo: {
          "fail reason": "blocker gets unexpected the group invitation"
        }
      }
    );
  });
});

describe('[public v2]', () => {
  let groupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);

    groupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
  });

  describe('', () => {
    const inviteExpiresInMin: number = 1;
    const inviteExpiresInMax: number = 2628288;
    const inviteExpiresInDefault: number = 3600;

    it.each`
      scenario                        | expiresIn
      ${"Set minimum TTL for invite"} | ${inviteExpiresInMin}
      ${"Set maximum TTL for invite"} | ${inviteExpiresInMax}
      ${"Set default TTL for invite"} | ${inviteExpiresInDefault}
    `('[happy][trusted]$scenario', async ({ expiresIn }) => {
      let testCase = {
        description: `send a group invite with expiresIn ${expiresIn}sec to user`,
        expected: "the sent invite contains the correct expiration time"
      };

      // Leader sends an invitation to member.
      let r: request.Response = await socialApi.invite(
        usersTwok.acct["leader"],
        groupId,
        {
          ttl: expiresIn
        },
        usersTwok.acct["member"].publicId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
        usersTwok.acct["leader"],
        groupId
      );

      // Expect the invitation with correct TTL is present in membershipRequests list.
      const expectedInviteInfo = {
        status: StatusCodes.OK,
        body: {
          membershipRequests: [
            {
              approverid: usersTwok.acct["leader"].publicId,
              ttl: expiresIn,
              groupid: groupId,
              memberid: usersTwok.acct["member"].publicId,
              status: 'invited'
            }
          ]
        },
      };
      socialApi.expectMore(
        () => {expect(actualGroupInfo).toMatchObject(expectedInviteInfo)},
        testCase,
        {
          resp: actualGroupInfo,
          additionalInfo: {
            "fail reason": "the group info contains unexpected membership request"
          }
        }
      );
    });

    it.each`
      scenario                            | expiresIn
      ${"Set minimum - 1 TTL for invite"} | ${inviteExpiresInMin - 1}
      ${"Set maximum + 1 TTL for invite"} | ${inviteExpiresInMax + 1}
    `('$scenario', async ({ expiresIn }) => {
      let testCase = {
        description: `send a group invite with expiresIn ${expiresIn}sec to user`,
        expected: "user doesn't get the invitation"
      };

      // Leader sends an invitation to member with expiresIn time.
      let r: request.Response = await socialApi.invite(
        usersTwok.acct["leader"],
        groupId,
        {
          ttl: expiresIn
        },
        usersTwok.acct["member"].publicId
      );
      socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, r);

      const actualInviteInfo: request.Response = await socialApi.getInvite(
        usersTwok.acct["member"]
      );

      // Expect member doesn't get the invitation.
      socialApi.testStatus(StatusCodes.OK, actualInviteInfo);
      socialApi.expectMore(
        () => {expect(actualInviteInfo.body).toEqual({})},
        testCase,
        {
          resp: actualInviteInfo,
          additionalInfo: {
            "fail reason": "the invite list is not empty"
          }
        }
      );
    });
  });

  describe('', () => {
    let expiresIn = 4;
    let timeDelta = 2;
    // Extra time in seconds allowed for invitation to become expired.
    let maxSlack = 15;
    // Timeout in ms for each test. Adjust according to expiresIn.
    let testTimeout = 40000;

    it.each`
      scenario                                                                     | waitTime                   | expInvExist
      ${"Get invite before expiresIn elapsed; attempt to accept the invite[happy]"}| ${(expiresIn - timeDelta)} | ${true}
      ${"Get invite after expiresIn elapsed; attempt to accept the invite"}        | ${(expiresIn + timeDelta)} | ${false}
    `('$scenario', async ({ waitTime, expInvExist }) => {
      let testCase = {
        description: `send an invite with expiresIn ${expiresIn}sec; time before get invite: ${waitTime}sec;
check the invite in memberships table; the user attempts to accept the invite`,
        expected: `expected group invite existence: ${expInvExist}; the invite is ${expInvExist == true ? 'accepted' : 'unacceptable'}`
      };

      let inviteItem, groupMembershipRequests, groupMembers;

      // Create an expected array of group membershipRequest.
      let membershipRequestArray = [
        expect.objectContaining({
          ttl: expiresIn,
          groupid: groupId,
          memberid: usersTwok.acct["member"].publicId
        })
      ];

      // Create an expected array of group member.
      let memberArray = [
        expect.objectContaining({
          role: "member",
          userid: usersTwok.acct["member"].publicId
        })
      ];

      switch (expInvExist) {
        case true:
          inviteItem = {
            items:
              [{ memberid: usersTwok.acct["member"].publicId }]
          };
          groupMembershipRequests = { membershipRequests: expect.arrayContaining(membershipRequestArray) };
          groupMembers = { members: expect.arrayContaining(memberArray) };
          break;
        case false:
          inviteItem = {};
          groupMembershipRequests = { membershipRequests: expect.not.arrayContaining(membershipRequestArray) };
          groupMembers = { members: expect.not.arrayContaining(memberArray) };
          break;
      }

      // Leader sends an invitation to member with expiresIn time.
      let r: request.Response = await socialApi.invite(
        usersTwok.acct["leader"],
        groupId,
        { ttl: expiresIn },
        usersTwok.acct["member"].publicId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // Wait for a moment.
      await new Promise(r => setTimeout(r, waitTime * 1000));

      // Some extra amount of time is allowed before invitation info is gone when it expires.
      if (expInvExist == false) {
        await socialApi.waitWhile(async () => {
          let p = await socialApi.getInvite(
            usersTwok.acct['member']
          );
          return (p.body != '{}');
        }, maxSlack, 1000);
      }

      const actualInviteInfo: request.Response = await socialApi.getInvite(
        usersTwok.acct["member"]
      );

      /**
       * Invitee checks
       * Before the invitation expires: the invite is in memberships table;
       * After the invitation expires: the invite is no longer in memberships table.
      */
      const expectedInviteInfo = {
        status: StatusCodes.OK,
        body: inviteItem
      };
      socialApi.expectMore(
        () => {expect(actualInviteInfo).toMatchObject(expectedInviteInfo)},
        testCase,
        {
          resp: actualInviteInfo,
          additionalInfo: {
            "fail reason": "unexpected group invitation"
          }
        }
      );

      let actualGroupInfo: request.Response = await socialApi.getGroupInfo(
        usersTwok.acct["leader"],
        groupId
      );

      /**
       * Inviter checks
       * Before the invitation expires: the invite is in memberships table;
       * After the invitation expires: the invite is no longer in memberships table.
      */
      const expectedMembershipInfo = {
        status: StatusCodes.OK,
        body: groupMembershipRequests
      };
      socialApi.expectMore(
        () => {expect(actualGroupInfo).toMatchObject(expectedMembershipInfo)},
        testCase,
        {
          resp: actualGroupInfo,
          additionalInfo: {
            "fail reason": "unexpected group member info"
          }
        }
      );

      /**
      * Before the invitation expires:
      * - user accepts the invitation and joins the group;
      * After the invitation expires:
      * - user tries to accept the invitation
      * - user doesn't join the group.
      */
      r = await socialApi.acceptInvite(
        usersTwok.acct["member"],
        groupId,
        usersTwok.acct["leader"].publicId,
        {
          canCrossPlay: true
        }
      );

      actualGroupInfo = await socialApi.getGroupInfo(
        usersTwok.acct["leader"],
        groupId
      );

      const expectedMemberInfo = {
        status: StatusCodes.OK,
        body: groupMembers
      };
      socialApi.expectMore(
        () => {expect(actualGroupInfo).toMatchObject(expectedMemberInfo)},
        testCase,
        {
          resp: actualGroupInfo,
          additionalInfo: {
            "fail reason": "unexpected group member info"
          }
        }
      );
    }, testTimeout);
  });
});