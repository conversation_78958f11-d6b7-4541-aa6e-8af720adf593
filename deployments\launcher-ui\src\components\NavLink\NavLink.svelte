<script lang="ts">
  import { Link } from 'svelte-routing';

  export let to = '';
  export let className = '';
  const getProps = ({
    href,
    isPartiallyCurrent,
    isCurrent,
  }: {
    href: string;
    isPartiallyCurrent: boolean;
    isCurrent: boolean;
  }) => {
    const isActive = href === '/' ? isCurrent : isPartiallyCurrent || isCurrent;

    // The object returned here is spread on the anchor element's attributes
    if (isActive) {
      return { class: 'active' };
    }
    return {};
  };
</script>

<div class="{className}">
  <Link to="{to}" getProps="{getProps}">
    <slot />
  </Link>
</div>
