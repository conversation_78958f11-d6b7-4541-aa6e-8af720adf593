package store

import (
	"context"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// GetUserProfile fetches a profile object from the database
func (ds *DataStore) GetUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	log := logger.FromContext(ctx)
	var profile = &apipub.UserProfileResponse{
		Userid: userid,
	}

	pk := tenant + "#user#" + userid
	sk := tenant + "#profile#" + userid

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.GetItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("GetUserProfile"))
	span.SetTag("query_pk", pk)
	span.SetTag("query_sk", sk)
	item, err := ds.ddb.GetItem(ctx, &dynamodb.GetItemInput{
		Key: map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: pk},
			"sk": &types.AttributeValueMemberS{Value: sk},
		},
		TableName: &ds.cfg.ProfileTable,
	})
	span.Finish()
	if err != nil {
		logger.FromContext(ctx).Error().Err(err).Str("pk", pk).Str("sk", sk).Msg("ddb.GetItem() failed")
		return nil, err
	}
	if item == nil || item.Item == nil {
		return nil, nil
	}

	err = attributevalue.UnmarshalMap(item.Item, &profile)
	if err != nil {
		log.Error().Err(err).Caller().Interface("item", item).Msg("UnmarshalMap failed to parse profile")
		return nil, errs.New(http.StatusInternalServerError, errs.EDynamodbUnmarshalFailed)
	}

	return profile, nil
}

// PutUserProfile create a new user profile
func (ds *DataStore) PutUserProfile(ctx context.Context, profile *apipub.UserProfileResponse) error {
	if profile.Userid == "" {
		return errs.New(http.StatusUnauthorized, errs.EInvalidUserID)
	}

	profile.Dob = nil
	profile.Email = nil

	items := []DataStoreItem{
		profile,
	}

	err := ds.PutItems(ctx, items)
	if err != nil {
		return err
	}

	return nil
}

// GetUserProfiles fetch multiple profiles
func (ds *DataStore) GetUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	if len(userids) == 0 {
		return nil, nil
	}
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	items, err := ds.BatchGetItems(ctx, userids, tenant+"#user", tenant+"#profile")
	if err != nil {
		return nil, err
	}

	profiles := make([]*apipub.UserProfileResponse, 0, len(userids))
	profileMap := map[string]apipub.UserProfileResponse{}

	// the order returned is not guaranteed to be the same as the requested array
	for _, item := range items {
		var profile apipub.UserProfileResponse
		err = attributevalue.UnmarshalMap(item, &profile)
		if err != nil {
			logger.FromContext(ctx).Error().Caller().Err(err).Msgf("UnmarshalMap failed")
			return nil, err
		}
		profileMap[profile.Userid] = profile
	}

	for _, userid := range userids {
		if profile, ok := profileMap[userid]; ok {
			profiles = append(profiles, &profile)
		}
	}

	return &profiles, nil
}

// PutUserProfiles create new user profiles
func (ds *DataStore) PutUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse) error {

	if profiles == nil {
		return errs.New(http.StatusUnauthorized, errs.EInvalidUserID)
	}

	items := make([]DataStoreItem, 0, len(*profiles))
	for _, profile := range *profiles {
		profile.Dob = nil
		profile.Email = nil
		items = append(items, profile)
	}

	err := ds.PutItems(ctx, items)
	if err != nil {
		return err
	}

	return nil
}
