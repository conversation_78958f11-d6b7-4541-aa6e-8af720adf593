/* eslint-disable max-lines-per-function */
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccounts, SteamAccounts, config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

/**
 * NOTE:
 * Only membershipRequests of statuses "requested" and "invited" are kept around. For empty membershipRequests, there could be either an empty array or no property at all.
 */

//
describe('', () => {
  let authGroupId: string;
  const authGroupPwd: string = 'somepassword';
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        password: authGroupPwd,
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    authGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], authGroupId);
    await usersTwok.logoutAll({});
  });

  it('user sends request to join a group (manual with pwd)[public v2 happy trusted]', async () => {
    let testCase = {
      description: "user requests to join an 'manual' group with the correct password",
      expected: "the user joins the group (manual)"
    };

    // Member1 requests to join an "manual" group with the correct password.
    const resp = await socialApi.requestToJoin(
      usersTwok.acct["member1"],
      authGroupId,
      {
        canCrossPlay: true,
        password: authGroupPwd,
      }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      authGroupId
    );

    // Expect Member1 joins the group as a "member"
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user doesn't join the group"
        }
      }
    );

    // Expect member1's previous and current membership request statuses are not present.
    socialApi.expectMore(
      () => {expect(actualGroupInfo.body.membershipRequests).toEqual(null)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected membership request statuses are present"
        }
      }
    );
  });
});


//
describe('', () => {
  let autoApproveGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'auto-approve',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    autoApproveGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], autoApproveGroupId);
    await usersTwok.logoutAll({});
  });

  it('user sends request to join a group (auto-approve)[public v2 happy trusted]', async () => {
    let testCase = {
      description: "user requests to join an 'auto-approve' group",
      expected: "the user joins the group (auto-approve)"
    };

    // Member1 requests to join an "auto-approve" group.
    const resp = await socialApi.requestToJoin(
      usersTwok.acct["member1"],
      autoApproveGroupId,
      {
        canCrossPlay: true
      });
    socialApi.testStatus(StatusCodes.OK, resp);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      autoApproveGroupId
    );

    // Expect Member1 joins the group as a "member"
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user doesn't join the group"
        }
      }
    );

    // Expect member1's current membership request status is not present.
    socialApi.expectMore(
      () => {expect(actualGroupInfo.body.membershipRequests).toEqual(null)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected membership request status is present"
        }
      }
    );
  });
});


//
describe('', () => {
  let autoRejectGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'auto-reject',
        canCrossPlay: true,
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    autoRejectGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], autoRejectGroupId);
    await usersTwok.logoutAll({});
  });

  //
  it('user sends request to join a group (auto-reject)[public v2 happy trusted]', async () => {
    let testCase = {
      description: "user requests to join an 'auto-reject' group",
      expected: `${config.socialAPIAccessLevel == 'public' ? `the user doesn't join the group (auto-reject)` :
        'Join requests made from the Social Trusted API will add users to the group no matter what the joinRequestAction is on the group.'}`
    };

    // create expected array and status
    let memberArray;
    let status;

    if (config.socialAPIAccessLevel == 'public') {
      memberArray = expect.not.arrayContaining([
        expect.objectContaining({
          role: 'member',
          userid: usersTwok.acct["member1"].publicId,
        }),
      ]);
      status = StatusCodes.FORBIDDEN;
    } else {
      memberArray = expect.arrayContaining([
        expect.objectContaining({
          role: 'member',
          userid: usersTwok.acct["member1"].publicId,
        }),
      ]);
      status = StatusCodes.OK;
    }

    // Member1 requests to join an "auto-reject" group.
    const resp = await socialApi.requestToJoin(
      usersTwok.acct["member1"],
      autoRejectGroupId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(status, resp);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      autoRejectGroupId
    );

    // Expect
    // - public: member1 is not in the group
    // - trusted: member1 is in the group
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: memberArray
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user joins the group"
        }
      }
    );

    // Expect member1's current membership request status is not present.
    socialApi.expectMore(
      () => {expect(actualGroupInfo.body.membershipRequests).toEqual(null)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected membership request status is present"
        }
      }
    );
  });
});


describe('[public v2]', () => {
  let mGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await usersTwok.logoutAll({});
  });

  it('user sends request to join a group (manual)[happy trusted]', async () => {
    let testCase = {
      description: "user requests to join a 'manual' group",
      expected: `${config.socialAPIAccessLevel != 'public' ? 'the user is not group member, membership request status is "requested"' :
        'Join requests made from the Social Trusted API will add users to the group no matter what the joinRequestAction is on the group.'}`
    };

    // create expected array and status
    let bodyObject;
    let status;

    if (config.socialAPIAccessLevel == 'public') {
      bodyObject = {
        members: expect.not.arrayContaining([
          expect.objectContaining({
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
        membershipRequests: [
          {
            groupid: mGroupId,
            memberid: usersTwok.acct["member1"].publicId,
            status: 'requested',
          },
        ],
      };
      status = StatusCodes.OK;
    } else {
      bodyObject = {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      };
      status = StatusCodes.OK;
    }

    // Member1 requests to join a "manual" group.
    const r = await socialApi.requestToJoin(
      usersTwok.acct["member1"],
      mGroupId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(status, r);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mGroupId
    );

    // Expect Member1:
    // public:
    // - user is not in the group.
    // - membership request status is "requested".
    // trusted:
    // - user joins the group
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: bodyObject
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user joins the group unexpectedly, or unexpected membership request status in the membershipRequests list"
        }
      }
    );
  });

  it('user sends repeated requests to join a group', async () => {
    // Member1 requests to join a "manual" group.
    let r = await socialApi.requestToJoin(usersTwok.acct["member1"], mGroupId, { canCrossPlay: true });
    // The first request should return CREATED status
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 repeated requests to join the group.
    r = await socialApi.requestToJoin(usersTwok.acct["member1"], mGroupId, { canCrossPlay: true });
    // It returns OK for repeated request
    socialApi.testStatus(StatusCodes.OK, r);
  });
});


describe('[public v2]', () => {
  let mGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await usersTwok.logoutAll({});
  });

  it('Group leader sends invitation[happy trusted]', async () => {
    let testCase = {
      description: "group leader invites user to join the group",
      expected: "the user is not yet a group member, membership request status is 'invited'"
    };

    // Group leader invites Member1 to join the group.
    const r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      mGroupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mGroupId
    );

    // Expect Member1:
    // - is not in the group.
    // - membership request status is "invited".
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.not.arrayContaining([
          expect.objectContaining({
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
        membershipRequests: [
          {
            groupid: mGroupId,
            memberid: usersTwok.acct["member1"].publicId,
            status: 'invited',
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user joins the group unexpectedly, or unexpected membership request status in the membershipRequests list"
        }
      }
    );
  });

  it('Group leader sends repeated invitation[trusted]', async () => {
    // Group leader invites Member1 to join the group.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      mGroupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    // It returns CREATED status code for first time invite
    socialApi.testStatus(StatusCodes.OK, r);

    //Group leader repeated invitation for Member1 to join the group
    r = await socialApi.invite(
      usersTwok.acct["leader"],
      mGroupId,
      {},
      usersTwok.acct["member1"].publicId
    );

    // It returns OK status code for repeated invite
    socialApi.testStatus(StatusCodes.OK, r);
  });
});


describe('[public v2]', () => {
  let mGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await usersTwok.logoutAll({});
  });

  it('Group leader revokes an invitation[happy]', async () => {
    let testCase = {
      description: "group leader invites user to join the group; group leader revokes the invitation",
      expected: "the user is not in the group"
    };

    // Group leader invites Member1 to join the group, and then revokes
    // that invitation.
    let r = await socialApi.invite(usersTwok.acct["leader"], mGroupId, {}, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.revokeInvite(usersTwok.acct["leader"], mGroupId, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mGroupId
    );

    // Expect Member1 is not in the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.not.arrayContaining([
          expect.objectContaining({
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
        // Expect the membershipRequests list is null.
        membershipRequests: null
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user is in the group unexpectedly, or unexpected membership request statuses are present"
        }
      }
    );
  });

  it('Group leader makes new invite after revoking an invitation', async () => {
    // Group leader invites Member1 to join the group, and then revokes
    // that invitation.
    let r = await socialApi.invite(usersTwok.acct["leader"], mGroupId, {}, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.revokeInvite(usersTwok.acct["leader"], mGroupId, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    // It returns CREATED for new invitation
    r = await socialApi.invite(usersTwok.acct["leader"], mGroupId, {}, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  });
});


//
describe('[public v2]', () => {
  let mGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await usersTwok.logoutAll({});
  });

  it('Group leader rejects a join request[happy]', async () => {
    let testCase = {
      description: "user requests to join a group; group leader rejects the request",
      expected: "the user is not in the group"
    };

    // Member1 requests to join a "manual" group.  Group leader rejects that request.
    let r = await socialApi.requestToJoin(usersTwok.acct["member1"], mGroupId, { canCrossPlay: true });
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.rejectRequest(usersTwok.acct["leader"], mGroupId, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mGroupId
    );

    // Expect Member1 is not in the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.not.arrayContaining([
          expect.objectContaining({
            groupid: mGroupId,
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
        // Expect member1's previous and current membership request statuses are not present.
        membershipRequests: null
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user is in the group unexpectedly, or unexpected membership request statuses are present"
        }
      }
    );
  });

  it('User makes a new request after join request rejected', async () => {
    // Member1 requests to join a "manual" group.  Group leader rejects that request.
    let r = await socialApi.requestToJoin(usersTwok.acct["member1"], mGroupId, { canCrossPlay: true });
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.rejectRequest(usersTwok.acct["leader"], mGroupId, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 makes new request to join the group
    r = await socialApi.requestToJoin(usersTwok.acct["member1"], mGroupId, { canCrossPlay: true });
    // It returns CREATED for new request
    socialApi.testStatus(StatusCodes.OK, r);
  });
});


//
describe('', () => {
  let mGroupId: string;
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await usersTwok.logoutAll({});
  });

  it('Group leader approves a join request[public v2 happy]', async () => {
    let testCase = {
      description: "user requests to join a group; group leader approves the request",
      expected: "the user joins the group"
    };

    // Member1 requests to join a "manual" group.  Group leader approves that request.
    let r = await socialApi.requestToJoin(usersTwok.acct["member1"], mGroupId, { canCrossPlay: true });
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.approveRequest(usersTwok.acct["leader"], mGroupId, usersTwok.acct["member1"].publicId, {});
    socialApi.testStatus(StatusCodes.OK, r);

    //
    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mGroupId
    );

    // Expect Member1 joins the group as a "member"
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
        // Expect member1's previous and current membership request statuses are not present.
        membershipRequests: null
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the user doesn't join the group, or unexpected membership request statuses are present"
        }
      }
    );
  });
});

describe(`membership management[public v2]${_ds}`, () => {
  let usersTwok: TwokAccounts;
  let usersSteam: SteamAccounts;
  let groupId: string;
  // Extra time in seconds allowed for identity refresh.
  let maxSlack = 5;

  beforeAll(async () => {
    usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
    usersSteam = new SteamAccounts(3, ["leader", "member1", "member2"]);
    await usersSteam.loginAll({});

    await usersSteam.linkParentAccts(usersTwok);

    // Steam account login again
    await usersSteam.loginAll({});

    // Wait for a moment.
    await socialApi.waitWhile(async () => {
      let p = await socialApi.getProfile(
        usersSteam.acct['leader']
      );
      let m = await socialApi.getProfile(
        usersSteam.acct['member1']
      );
      return (p.body.links.length == 0 && m.body.links.length == 0);
    }, maxSlack, 1000);
  });

  afterAll(async () => {
    await usersSteam.unlinkParentAccts();

    await usersSteam.logoutAll();
  });

  beforeEach(async () => {
    const r = await socialApi.createGroup(
      usersSteam.acct["leader"],
      {
        canMembersInvite: true,
        maxMembers: 6,
        joinRequestAction: 'manual'
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);

    groupId = socialApi.getGroupId(r);
  });

  describe(`happy cases${_ds}`, () => { 
    afterEach(async () => {
      await socialApi.deleteGroup(usersSteam.acct["leader"], groupId);
    });

    async function inviteAndAccept(inviter: string, invitee: string) {
      let r = await socialApi.invite(
        usersSteam.acct[inviter],
        groupId,
        { isFirstPartyInvite: true },
        usersSteam.acct[invitee].platformId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // Member accepts the invitation.
      r = await socialApi.acceptInvite(
        usersSteam.acct[invitee],
        groupId,
        usersTwok.acct[inviter].publicId,
        {
          isFirstPartyInvite: true,
          canCrossPlay: true
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    async function inviteAndDecline() {
      let r = await socialApi.invite(
        usersSteam.acct["leader"],
        groupId,
        { isFirstPartyInvite: true },
        usersSteam.acct["member1"].platformId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // Member declines the invitation.
      r = await socialApi.declineInvite(
        usersSteam.acct["member1"],
        groupId,
        usersTwok.acct["leader"].publicId
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    async function inviteAndRevoke() {
      let r = await socialApi.invite(
        usersSteam.acct["leader"],
        groupId,
        { isFirstPartyInvite: true },
        usersSteam.acct["member1"].platformId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // Leader revokes the invitation.
      r = await socialApi.revokeInvite(
        usersSteam.acct["leader"],
        groupId,
        usersTwok.acct["member1"].publicId
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    it.each`
      testSubjectFunc     | scenario                            | joinOrNot
      ${inviteAndAccept}  | ${`can accept first party invite`}  | ${true}
      ${inviteAndDecline} | ${`can decline first party invite`} | ${false}
      ${inviteAndRevoke}  | ${`can revoke first party invite`}  | ${false}
    `(`$scenario`, async ({testSubjectFunc, joinOrNot}) => {
      let testCase = {
        description: `send group invitation to a user with 'isFirstPartyInvite = true';
  the user ${joinOrNot == 'true' ? 'accepts' : (testSubjectFunc.name == 'inviteAndDecline' ? 'declines' : 'revokes')} the invitation`,
        expected: `the user ${joinOrNot == true ? 'is' : 'is not'} in the group`
      };

      // Main test subject
      await testSubjectFunc("leader", "member1");

      // Create an expected array of group member.
      let memberArray = [
        expect.objectContaining({
          role: "member",
          userid: usersTwok.acct["member1"].publicId
        })
      ];

      let groupMembers;
      if (joinOrNot == false) {
        groupMembers = { members: expect.not.arrayContaining(memberArray) };
      } else {
        groupMembers = { members: expect.arrayContaining(memberArray) };
      }

      const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
        usersSteam.acct["leader"],
        groupId
      );

      // Expect
      // - accept invite: user joins the group;
      // - decline invite: user doesn't join the group;
      // - revoke invite: user doesn't join the group.
      const expectedGroupInfo = {
        status: StatusCodes.OK,
        body: groupMembers
      };
      socialApi.expectMore(
        () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
        testCase,
        {
          resp: actualGroupInfo,
          additionalInfo: {
            "fail reason": "unexpected group member info"
          }
        }
      );
    });

    it('can send first party invite by member', async () => {
      let testCase = {
        description: "group member sends first party invite",
        expected: "invited user accepts the first party invite and joins the group"
      };

      // Leader invites member1 and member1 accepts the first party invite.
      await inviteAndAccept("leader", "member1");

      // Member1 invites member2 and member2 accepts the first party invite.
      await inviteAndAccept("member1", "member2");

      const actualGroupInfo = await socialApi.getGroupInfo(usersSteam.acct["leader"], groupId);

      // Expect member1 and member2 join the group.
      const expectedGroupInfo = {
        status: StatusCodes.OK,
        body: {
          members: expect.arrayContaining([
            expect.objectContaining({
              role: 'leader',
              userid: usersTwok.acct["leader"].publicId,
            }),
            expect.objectContaining({
              role: 'member',
              userid: usersTwok.acct["member1"].publicId,
            }),
            expect.objectContaining({
              role: 'member',
              userid: usersTwok.acct["member2"].publicId,
            }),
          ]),
        },
      };
      socialApi.expectMore(
        () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
        testCase,
        {
          resp: actualGroupInfo,
          additionalInfo: {
            "fail reason": "unexpected group member list"
          }
        }
      );
    });
  });

  describe(`corner cases${_ds}`, () => {
    afterEach(async () => {
      await socialApi.delBlockList(usersSteam.acct["member1"]);
      await socialApi.deleteGroup(usersSteam.acct["leader"], groupId);
    });

    it('cannot send first party invite to the blocker by blocked user', async () => {
      let testCase = {
        description: "blocked user sends first party invite to the blocker",
        expected: "blocker does not receive the first party invitation from the blocked user"
      };

      // Member1 adds the leaderUserId to the block list
      let r: request.Response = await socialApi.updateBlockList(
        usersSteam.acct["member1"],
        {
          userids: [usersSteam.acct["leader"].platformId],
          isFirstParty: true
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // Leader sends an invitation to member1.
      r = await socialApi.invite(
        usersSteam.acct["leader"],
        groupId,
        {
          isFirstPartyInvite: true
        },
        usersSteam.acct["member1"].platformId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      const actualInviteInfo: request.Response = await socialApi.getInvite(
        usersSteam.acct["member1"]
      );

      // Expect member1 does not get the invitation.
      socialApi.testStatus(StatusCodes.OK, actualInviteInfo);
      socialApi.expectMore(
        () => {expect(actualInviteInfo.body).toEqual({})},
        testCase,
        {
          resp: actualInviteInfo,
          additionalInfo: {
            "fail reason": "blocker gets unexpected the first party invitation"
          }
        }
      );
    });
  });
});


// TODO: Move test cases from "inviteUser" here, since this test suite is about
// membership status management.  Rename this test suite accordingly.

