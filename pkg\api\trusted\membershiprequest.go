package api

import (
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

// ServerSendInvitesForGroup sends invites to group
func (tApi *SocialTrustedAPI) ServerSendInvitesForGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := jwt.ProductID
	log := logger.Get(r)

	var inviteRequest apitrusted.ServerSendInviteRequestBody
	if !api.DecodeBody(w, r, &inviteRequest) {
		log.Error().Msg("Error decoding ServerGroupMembershipRequest")
		return
	}

	expiredIn := tApi.SocialApi.Cfg.TtlMembership
	if inviteRequest.Ttl != nil {
		expiredIn = int(*inviteRequest.Ttl)
	}

	members := inviteRequest.Members

	for _, member := range members {
		if member.Memberid == "" {
			log.Error().Msg("One or more group membership invited without a memberid")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EInvalidUserID))
			return
		}
	}

	httpReturn := false
	if inviteRequest.ReturnMembershipErrors != nil && *inviteRequest.ReturnMembershipErrors {
		httpReturn = true
	}

	for _, member := range members {
		memberid := member.Memberid
		approverid := inviteRequest.Inviterid
		isFirstPartyInvite := false
		if member.IsFirstPartyInvite != nil {
			isFirstPartyInvite = *member.IsFirstPartyInvite
		}

		err2 := tApi.SocialApi.SendInviteWithHttpReturn(w, r, g, productid, constants.TrustedServer, apipub.OnlineServiceType(inviteRequest.OnlineServiceType), memberid, approverid, httpReturn, isFirstPartyInvite, expiredIn, inviteRequest.TeleMeta)
		if err2 != nil {
			errs.Return(w, r, err2)
			return
		}
	}
	api.ReturnEmptyOK(w, r)
}

// ServerSendJoinRequestsForGroup sends join request for group
func (tApi *SocialTrustedAPI) ServerSendJoinRequestsForGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	log := logger.Get(r)
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := jwt.ProductID

	var groupMemberReq apitrusted.ServerJoinRequestRequestBody
	if !api.DecodeBody(w, r, &groupMemberReq) {
		log.Error().Msg("Error decoding ServerGroupMembershipRequest")
		return
	}

	members := groupMemberReq.Members

	for _, member := range members {
		if member.Memberid == "" {
			log.Error().Msg("One or more group membership requested without a memberid")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EInvalidUserID))
			return
		}
	}

	for _, member := range members {
		memberid := member.Memberid
		ost := apipub.OnlineServiceType(member.OnlineServiceType)
		password := ""
		if groupMemberReq.Password != nil {
			password = *groupMemberReq.Password
		}

		var sendJoinRequest apipub.RequestJoinRequestBody
		sendJoinRequest.Password = &password
		sendJoinRequest.TeleMeta = groupMemberReq.TeleMeta
		sendJoinRequest.CanCrossPlay = member.CanCrossPlay

		tApi.SocialApi.SendJoinRequestByStrings(w, r, g, productid, constants.TrustedServer, ost, nil, memberid, "leader", sendJoinRequest)
	}
}
