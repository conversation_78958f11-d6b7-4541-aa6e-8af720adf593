import babel from '@rollup/plugin-babel';
import commonjs from '@rollup/plugin-commonjs';
import image from '@rollup/plugin-image';
import json from '@rollup/plugin-json';
import resolve from '@rollup/plugin-node-resolve';
import typescript from '@rollup/plugin-typescript';
import analyze from 'rollup-plugin-analyzer';
import css from 'rollup-plugin-css-only';
import livereload from 'rollup-plugin-livereload';
import replace from 'rollup-plugin-replace';
import { sizeSnapshot } from 'rollup-plugin-size-snapshot';
import svelte from 'rollup-plugin-svelte';
import svelteSVG from 'rollup-plugin-svelte-svg';
import { terser } from 'rollup-plugin-terser';
import pkg from './package.json';
const svelteConfig = require('./svelte.config');

const name = pkg.name
  .replace(/^(@\S+\/)?(svelte-)?(\S+)/, '$3')
  .replace(/^\w/, m => m.toUpperCase())
  .replace(/-\w/g, m => m[1].toUpperCase());

const production = !process.env.ROLLUP_WATCH;

function serve() {
  let server;

  function toExit() {
    if (server) server.kill(0);
  }

  return {
    writeBundle() {
      if (server) return;
      server = require('child_process').spawn(
        'npm',
        ['run', 'start', '--', '--dev'],
        {
          stdio: ['ignore', 'inherit', 'inherit'],
          shell: true,
        }
      );

      process.on('SIGTERM', toExit);
      process.on('exit', toExit);
    },
  };
}

/**
 * production outputs defines es, umd and iife bundles.
 * dev output is generated in the public folder for local development
 */
const output = production
  ? [
      { file: pkg.module, format: 'es', sourcemap: true }, // dist/index
      {
        file: pkg.main,
        format: 'umd',
        name,
        sourcemap: true,
      }, // dist/index.umd.js
      {
        file: pkg.main.replace('.umd', ''), // dist/index.min.js
        format: 'iife',
        name,
        sourcemap: true,
      },
    ]
  : {
      sourcemap: true,
      format: 'iife',
      name: 'social',
      file: 'public/build/bundle.js',
    };

const input = production ? 'src/index.ts' : 'src/main.ts';

export default {
  input,
  output,
  plugins: [
    // some packages, like emotion, is using proccess determine if it should run in productuon
    // or developement mode
    replace({
      'process.env.NODE_ENV': JSON.stringify(
        production ? 'production' : 'development'
      ),
    }),
    svelte({
      preprocess: svelteConfig.preprocess,
      compilerOptions: {
        // enable run-time checks when not in production
        dev: !production,
      },
    }),
    // we'll extract any component CSS out into
    // a separate file - better for performance
    css({ output: 'social.css' }),

    // If you have external dependencies installed from
    // npm, you'll most likely need these plugins. In
    // some cases you'll need additional configuration -
    // consult the documentation for details:
    // https://github.com/rollup/plugins/tree/master/packages/commonjs
    resolve({
      browser: true,
      dedupe: ['svelte'],
      preferBuiltins: true,
    }),
    commonjs(),
    typescript({
      inlineSources: !production,
    }),
    babel({
      extensions: ['.js', '.ts', '.mjs', '.svelte'],
      exclude: 'node_modules/**',
      babelHelpers: 'runtime',
    }),

    // In dev mode, call `npm run start` once
    // the bundle has been generated
    !production && serve(),

    // Watch the `public` directory and refresh the
    // browser on changes when not in production
    !production && livereload('public'),

    // If we're building for production (npm run build
    // instead of npm run dev), minify
    production && terser({ module: true }),

    sizeSnapshot(),
    analyze({ summaryOnly: true }),
    json(),
    svelteSVG(),
    image({
      exclude: 'src/assets/icons/**',
    }),
  ],
  watch: {
    clearScreen: false,
  },
};
