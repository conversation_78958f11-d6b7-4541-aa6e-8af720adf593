package cache

import (
	"context"
	"fmt"
	api "github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"

	redis "github.com/redis/go-redis/v9"
)

func BuildTsClientIdRedisKey(clientid string) string {
	return fmt.Sprintf("ts:%s:clientid:%s", api.GetTsEnv(), clientid)
}

func BuildTsProductIdRedisKey(productid string) string {
	return fmt.Sprintf("ts:%s:productid:%s", api.GetTsEnv(), productid)
}

func (rc *RedisCache) GetTsClientId(ctx context.Context, clientid string) (*api.TsClientIdInfo, error) {
	key := BuildTsClientIdRedisKey(clientid)
	return getCachedObject[api.TsClientIdInfo](ctx, rc, key)
}

func (rc *RedisCache) SetTsClientId(ctx context.Context, info *api.TsClientIdInfo) error {
	key := BuildTsClientIdRedisKey(info.ClientId)
	return setCachedObject(ctx, rc, info, key, -1)
}

func (rc *RedisCache) DelTsClientId(ctx context.Context, clientid string) error {
	key := BuildTsClientIdRedisKey(clientid)
	return rc.DeleteCachedObj(ctx, key)
}

func (rc *RedisCache) SetRangeTsClientId(ctx context.Context, info *api.TsClientIdInfo) error {
	key := BuildTsProductIdRedisKey(info.ProductId)
	value := BuildTsClientIdRedisKey(info.ClientId)
	return rc.zAdd(ctx, key, redis.Z{
		Score:  0,
		Member: value,
	}).Err()
}

func (rc *RedisCache) DelRangeTsClientId(ctx context.Context, info *api.TsClientIdInfo) error {
	key := BuildTsProductIdRedisKey(info.ProductId)
	value := BuildTsClientIdRedisKey(info.ClientId)
	return rc.zRem(ctx, key, value).Err()
}
