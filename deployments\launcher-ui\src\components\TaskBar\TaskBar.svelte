<script>
  import { createEventDispatcher } from 'svelte';
  import { SVGClose, SVGMinimize } from '../../assets/icons';
  import { useTranslator } from '../../hooks';

  export let className = '';
  const t = useTranslator();

  const dispatch = createEventDispatcher();

  const onCloseClicked = () => {
    dispatch('close');
  };

  const onMinimizeClicked = () => {
    dispatch('minimize');
  };
</script>

<style>
  .container {
    display: flex;
    padding: 0.75rem 0.875rem 0;
    justify-content: flex-end;
    align-items: center;
    background-color: var(
      --social-bg-color-top-bar,
      var(--default-bg-color-top-bar)
    );
  }

  button {
    border: none;
    outline: transparent solid 0.125rem;
    margin: 0;
    transition: background-color 0.1s ease-out;
  }

  button:last-child {
    margin-inline-end: -0.5rem;
  }

  button:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.3);
  }

  .icon {
    padding: 0 0.25rem;
    border-radius: 0.25rem;
    color: var(--social-color, var(--default-color));
  }

  .icon :global(svg) {
    width: 1rem;
    height: 1rem;
  }
</style>

<div class="container {className}">
  <button
    data-testid="minimize-button"
    title="{$t('Minimize')}"
    type="button"
    class="icon"
    tabIndex="{-1}"
    on:click="{onMinimizeClicked}"
  >
    <SVGMinimize />
  </button>
  <button
    data-testid="close-button"
    title="{$t('Close')}"
    type="button"
    class="icon"
    tabIndex="{-1}"
    on:click="{onCloseClicked}"
  >
    <SVGClose />
  </button>
</div>
