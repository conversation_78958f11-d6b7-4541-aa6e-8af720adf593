// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/health/monitor.go

// Package health is a generated GoMock package.
package health

import (
	bytes "bytes"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockDependentService is a mock of DependentService interface.
type MockDependentService struct {
	ctrl     *gomock.Controller
	recorder *MockDependentServiceMockRecorder
}

// MockDependentServiceMockRecorder is the mock recorder for MockDependentService.
type MockDependentServiceMockRecorder struct {
	mock *MockDependentService
}

// NewMockDependentService creates a new mock instance.
func NewMockDependentService(ctrl *gomock.Controller) *MockDependentService {
	mock := &MockDependentService{ctrl: ctrl}
	mock.recorder = &MockDependentServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDependentService) EXPECT() *MockDependentServiceMockRecorder {
	return m.recorder
}

// CheckHealth mocks base method.
func (m *MockDependentService) CheckHealth() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHealth")
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckHealth indicates an expected call of CheckHealth.
func (mr *MockDependentServiceMockRecorder) CheckHealth() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHealth", reflect.TypeOf((*MockDependentService)(nil).CheckHealth))
}

// IsCritical mocks base method.
func (m *MockDependentService) IsCritical() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCritical")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsCritical indicates an expected call of IsCritical.
func (mr *MockDependentServiceMockRecorder) IsCritical() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCritical", reflect.TypeOf((*MockDependentService)(nil).IsCritical))
}

// LastStatus mocks base method.
func (m *MockDependentService) LastStatus() *ServiceStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LastStatus")
	ret0, _ := ret[0].(*ServiceStatus)
	return ret0
}

// LastStatus indicates an expected call of LastStatus.
func (mr *MockDependentServiceMockRecorder) LastStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LastStatus", reflect.TypeOf((*MockDependentService)(nil).LastStatus))
}

// MockServiceMonitorInterface is a mock of ServiceMonitorInterface interface.
type MockServiceMonitorInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMonitorInterfaceMockRecorder
}

// MockServiceMonitorInterfaceMockRecorder is the mock recorder for MockServiceMonitorInterface.
type MockServiceMonitorInterfaceMockRecorder struct {
	mock *MockServiceMonitorInterface
}

// NewMockServiceMonitorInterface creates a new mock instance.
func NewMockServiceMonitorInterface(ctrl *gomock.Controller) *MockServiceMonitorInterface {
	mock := &MockServiceMonitorInterface{ctrl: ctrl}
	mock.recorder = &MockServiceMonitorInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceMonitorInterface) EXPECT() *MockServiceMonitorInterfaceMockRecorder {
	return m.recorder
}

// AddDependentService mocks base method.
func (m *MockServiceMonitorInterface) AddDependentService(name string, service DependentService) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddDependentService", name, service)
}

// AddDependentService indicates an expected call of AddDependentService.
func (mr *MockServiceMonitorInterfaceMockRecorder) AddDependentService(name, service interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddDependentService", reflect.TypeOf((*MockServiceMonitorInterface)(nil).AddDependentService), name, service)
}

// GetHealthReport mocks base method.
func (m *MockServiceMonitorInterface) GetHealthReport(reportType HealthReportType, idProvider *string) *bytes.Buffer {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthReport", reportType, idProvider)
	ret0, _ := ret[0].(*bytes.Buffer)
	return ret0
}

// GetHealthReport indicates an expected call of GetHealthReport.
func (mr *MockServiceMonitorInterfaceMockRecorder) GetHealthReport(reportType, idProvider interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthReport", reflect.TypeOf((*MockServiceMonitorInterface)(nil).GetHealthReport), reportType, idProvider)
}

// GetOverallHealth mocks base method.
func (m *MockServiceMonitorInterface) GetOverallHealth(idProvider *string) Health {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOverallHealth", idProvider)
	ret0, _ := ret[0].(Health)
	return ret0
}

// GetOverallHealth indicates an expected call of GetOverallHealth.
func (mr *MockServiceMonitorInterfaceMockRecorder) GetOverallHealth(idProvider interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOverallHealth", reflect.TypeOf((*MockServiceMonitorInterface)(nil).GetOverallHealth), idProvider)
}

// Start mocks base method.
func (m *MockServiceMonitorInterface) Start() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Start")
}

// Start indicates an expected call of Start.
func (mr *MockServiceMonitorInterfaceMockRecorder) Start() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockServiceMonitorInterface)(nil).Start))
}

// Stop mocks base method.
func (m *MockServiceMonitorInterface) Stop() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Stop")
}

// Stop indicates an expected call of Stop.
func (mr *MockServiceMonitorInterfaceMockRecorder) Stop() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Stop", reflect.TypeOf((*MockServiceMonitorInterface)(nil).Stop))
}
