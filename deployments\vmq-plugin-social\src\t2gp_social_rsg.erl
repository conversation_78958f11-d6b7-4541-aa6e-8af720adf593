-module(t2gp_social_rsg).

-include_lib("t2gp_social.hrl").

%% gen_server callbacks
-export([
    start_link/0,
    stop/0,
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

-record(state, {
    config_url,
    config
}).

-define(APM_REQUEST, <<"pd">>).

%
% gen_server
%

-spec start_link() -> {ok, pid()} | {error, {already_started, pid()}}.
start_link() ->
    lager:info("t2gp_social_rsg:start_link"),
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

-spec stop() -> ok.
stop() ->
    gen_server:stop(?MODULE).

-spec init(term()) -> {ok, state()}.
init(_) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:info("t2gp_social_rsg:init"),
        DefaultState = #state{
            config_url = undefined,
            config = []
        },
        case application:get_env(t2gp_social, rs_config_url) of
            {ok, ConfigURL} ->
                NewState = update_config(ConfigURL, DefaultState),
                {ok, NewState};
            _ ->
                {ok, DefaultState}
        end
    end).

-spec handle_call({atom(), term()}, {pid(), term()}, state()) -> {reply, term(), state()}.
handle_call({validate_jwt, JWT}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{ cmd => <<"validate_jwt">>}),
        try
            PK = proplists:get_value(<<"pk">>, proplists:get_value(<<"t2gp-rs">>, State#state.config)),
            {reply, jwt:decode(JWT, PK), State}
        of
            Result -> Result
        catch Exception:Reason:Stack ->
            lager:error("t2gp_social_rsg:validate_jwt invalid response: ~p ~p ~p ", [Exception, Reason, Stack]),
            {reply, {error, invalid_token}, State}
        end
    end);
handle_call({update_config}, From, State) ->
    handle_call({update_config, State#state.config_url}, From, State);
handle_call({update_config, ConfigURL}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{ cmd => <<"update_config">>}),
        lager:info("Updating rsg config ~p", [ConfigURL]),
        NewState = update_config(ConfigURL, State),
        {reply, ok, NewState}
    end);
handle_call(Msg, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:info("Unhandled message: ~p", [Msg]),
        {reply, {error, invalid_msg}, State}
    end).

-spec handle_cast(term(), state()) -> {noreply, state()}.
handle_cast(_Msg, State) ->
    {noreply, State}.

-spec handle_info(term(), state()) -> {noreply, state()}.
handle_info(_Msg, State) ->
    {noreply, State}.

-spec terminate(normal | shutdown | {shutdown, term()} | term(), term()) -> ok.
terminate(Reason, State) ->
    lager:info("t2gp_social_rsg:terminate ~p, ~p", [Reason, State]),
    ok.

-spec code_change(term() | {down, term()}, state(), term()) -> {ok, state()} | {error, term()}.
code_change(OldVsn, State, Extra) ->
    lager:info("t2gp_social_rsg:code_change ~p, ~p, ~p", [OldVsn, State, Extra]),
    {ok, State}.

update_config(ConfigURL, State) ->
    try
        URL = uri_string:parse(ConfigURL),
        Bucket = maps:get(host, URL),
        Key = lists:delete($/, maps:get(path, URL)),
        Result = erlcloud_s3:get_object(Bucket, Key),
        Content = zlib:gunzip(proplists:get_value(content, Result)),
        Config = jsx:decode(Content),
        NewState = #state{
            config_url = ConfigURL,
            config = Config
        },
        NewState
    of
        S -> S
    catch Exception:Reason:Stack ->
        lager:error("t2gp_social_rsg:update_config invalid response: ~p ~p ~p ", [Exception, Reason, Stack]),
        State
    end.
