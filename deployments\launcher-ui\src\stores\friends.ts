import { writable } from 'svelte/store';
import type {
  PendingFriend,
  QueryResult,
  SocialFriend,
  SteamFriend,
} from '../services';

export const pendingFriendsQueryResultStore = writable<
  QueryResult<PendingFriend[]>
>(null);

export const friendsQueryResultStore = writable<QueryResult<SocialFriend[]>>(
  null
);

export const steamFriendsQueryResultStore = writable<
  QueryResult<SteamFriend[]>
>(null);

export const searchTermStore = writable('');
