package apipub

import (
	"fmt"
	openapi_types "github.com/oapi-codegen/runtime/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

//
//type UserProfile struct {
//	*UserProfileReturn
//	Dob             *string `json:"dob"`
//	Email           *string `json:"email"`
//	ParentAccountId *string `json:"parent_account_id"`
//}

// DnaSession DNA session object
type DnaSession struct {
	AppId       string `json:"appId"`
	CreatedTime int64  `json:"createdTime"`
	ExpiresTime int64  `json:"expiresTime"`
	ProductId   string `json:"productId"`
	SessionId   string `json:"sessionId"`
}

// RedisKey redis key
func (profile *UserProfileResponse) RedisKey(tenant string) string {
	return fmt.Sprintf("%s:user:{%s}", tenant, profile.Userid)
}

func BuildUserRedisKey(tenant, userid string) string {
	return fmt.Sprintf("%s:user:{%s}", tenant, userid)
}

func (played *RecentlyPlayedUserResponse) RedisKey(tenant string) string {
	if played == nil || played.Productid == nil || played.ForUserid == nil {
		return ""
	}
	return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:played:%s", tenant, *played.Productid, utils.GetEnvironment(), *played.ForUserid, played.Userid)
}

func BuildRecentlyPlayedRedisKey(tenant, productid, userid, playedWithid string) string {
	return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:played:%s", tenant, productid, utils.GetEnvironment(), userid, playedWithid)
}

func (profile *UserProfileResponse) Topic(tenant string) string {
	return fmt.Sprintf("%s/user/%s", tenant, profile.Userid)
}

func BuildUserTopic(tenant, userid string) string {
	return fmt.Sprintf("%s/user/%s", tenant, userid)
}

// PK get partition key
func (profile *UserProfileResponse) PK(tenant string) string {
	return tenant + "#user#" + profile.Userid
}

// SK get sort key
func (profile *UserProfileResponse) SK(tenant string) string {
	return tenant + "#profile#" + profile.Userid
}

// Defines values for SearchAccountRequestType.
const (
	AccountsByFirstPartyAlias SearchAccountRequestType = "accountsByFirstPartyAlias"
	AccountsByFirstPartyId    SearchAccountRequestType = "accountsByFirstPartyId"
	AccountsById              SearchAccountRequestType = "accountsById"
	FullAccountByDisplayName  SearchAccountRequestType = "fullAccountByDisplayName"
	//FullAccountIdByFirstPartyId SearchAccountRequestType = "fullAccountIdByFirstPartyId"
)

// SearchAccountRequest defines model for searchAccountRequest.
type SearchAccountRequest struct {
	// Criterias A list of Criterias.
	Criterias *[]SearchAccountCriteria `json:"criterias,omitempty"`

	// Type Account Type:
	// * accountsById - get accounts by Account Id
	// * accountsByFirstPartyId - get accounts by First Party Id
	// * fullAccountIdByFirstPartyId - get full accounts Id by First Party Id
	// * accountsByFirstPartyAlias - get accounts by First Paty Alias
	// * accountsByDisplayName - get accounts by Display Name
	Type *SearchAccountRequestType `json:"type,omitempty"`
}

// SearchAccountRequestType Account Type:
// * accountsById - get accounts by Account Id
// * accountsByFirstPartyId - get accounts by First Party Id
// * fullAccountIdByFirstPartyId - get full accounts Id by First Party Id
// * accountsByFirstPartyAlias - get accounts by First Paty Alias
// * accountsByDisplayName - get accounts by Display Name
type SearchAccountRequestType string

// SearchAccountResponseList A list of account by first party Id or account Id which linked to DNA account. This response is yielded when accountsById or accountsByFirstPartyId type is requested.
type SearchAccountResponseList = []SearchAccountResponse

// LoginRequestDNA LoginRequestDNA defines the login request for DNA. Please refer to https://dev.take2games.com/docs/social/dna/api-dna-client#/User%20Account%20Tokens/post_auth_tokens
type LoginRequestDNA struct {
	// AccountType User account type
	AccountType LoginRequestDNAAccountType `json:"accountType"`

	// Credentials User credentials
	Credentials struct {
		// Email User email address
		Email openapi_types.Email `json:"email"`

		// Password User password
		Password string `json:"password"`

		// Type Credential type
		Type string `json:"type"`
	} `json:"credentials"`

	// Locale User locale
	Locale string `json:"locale"`
}

// LoginRequestDNAAccountType User account type
type LoginRequestDNAAccountType string

// RefreshTokenRequestDNA RefreshTokenDNA defines the refresh token request for DNA. Please refer to https://dev.take2games.com/docs/social/dna/api-dna-client#/User%20Account%20Tokens/post_auth_tokens
type RefreshTokenRequestDNA struct {
	// AccountType User account type
	AccountType RefreshTokenRequestDNAAccountType `json:"accountType"`

	// Credentials User credentials
	Credentials struct {
		// RefreshToken Refresh token
		RefreshToken string `json:"refreshToken"`

		// Type Credential type
		Type string `json:"type"`
	} `json:"credentials"`

	// Locale User locale
	Locale string `json:"locale"`
}

// RefreshTokenRequestDNAAccountType User account type
type RefreshTokenRequestDNAAccountType string

// SearchAccountCriteria defines model for searchAccountCriteria.
type SearchAccountCriteria struct {
	// AccountId Account Identifier. Mandatory if the type is accountsById
	AccountId *string `json:"accountId,omitempty"`

	// DisplayName Display Name. Mandatory if the type is fullAccountByDisplayName
	DisplayName *string `json:"displayName,omitempty"`

	// FirstPartyAlias First Party Alias. Mandatory if the type is equal to accountsByFirstPartyAlias
	FirstPartyAlias *string `json:"firstPartyAlias,omitempty"`

	// FirstPartyId First Party Identifier. Mandatory if the type is equal to accountsByFirstPartyId or fullAccountIdByFirstPartyId
	FirstPartyId *string `json:"firstPartyId,omitempty"`

	// OnlineServiceType The online service platform of the account. Mandatory if the type is equal to accountsByFirstPartyId or fullAccountIdByFirstPartyId
	OnlineServiceType *int `json:"onlineServiceType,omitempty"`
}
