import { render, waitFor } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  MOCKED_STEAM_FRIENDS,
  transportServiceMock,
} from '../../../services/__mocks__';
import ButtonMock from '../../Button/__mock__/Button.svelte';
import CustomInputMock from '../../CustomInput/__mock__/CustomInput.svelte';
import { FriendImportCardMock } from '../../FriendImportCard/__mock__';
import SteamImportWrapper from './SteamImportWrapper.svelte';

const socialServicesMock = new SocialServices({
  transportService: transportServiceMock,
  friendsService: friendsServiceMock,
});

jest.mock('../../../assets/icons', () => ({
  SVGSteam: SVGIconMock,
  SVGEmptyFace: SVGIconMock,
  SVGRefresh: SVGIconMock,
  SVGChecked: SVGIconMock,
}));

jest.mock('../../FriendImportCard', () => ({
  FriendImportCard: FriendImportCardMock,
}));

jest.mock('../../CustomInput', () => ({
  CustomInput: CustomInputMock,
}));

jest.mock('../../Button', () => ({
  Button: ButtonMock,
}));

describe('SteamImport', () => {
  it('should render connect page', () => {
    const { getByText } = render(SteamImportWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    expect(getByText('Connect')).not.toBeNull();
  });

  it('should render empty steam friends page', () => {
    const { getByText } = render(SteamImportWrapper, {
      props: {
        context: socialServicesMock,
        steamAccountLinked: true,
      },
    });

    expect(getByText('no steam friends to add')).not.toBeNull();
  });

  it('should render steam friends page', async () => {
    const { getAllByTestId } = render(SteamImportWrapper, {
      props: {
        context: socialServicesMock,
        steamAccountLinked: true,
        linkedSteamAccountId: 'steamId',
      },
    });

    await waitFor(() => {
      expect(getAllByTestId('friend-import-card-mock').length).toBe(
        MOCKED_STEAM_FRIENDS.filter(m => !!m.profileDNA).length
      );
    });
  });
});
