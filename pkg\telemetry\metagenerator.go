package telemetry

import (
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// GenericTelemetryMeta defines model for friendTelemetryMeta.
type GenericTelemetryMeta struct {

	// AdditionalInfo Additional optional telemetry information sent with T2GP web requests
	AdditionalInfo *map[string]string `json:"additional_info,omitempty"`

	// AppId 2K app id.  best effort.  in cases of Trusted API call or with expired presence kick, this won't be available.
	AppId *string `json:"app_id,omitempty"`

	// Env environment used for s3 partitioning
	Env string `json:"env,omitempty"`

	// Event telemetry event
	Event string `json:"event,omitempty"`

	// EventId T2GP ID to track group events
	EventId string `json:"event_id,omitempty"`

	// ProductId Product Id of the title
	ProductId string `json:"product_id,omitempty"`

	// Subject The player Id or server Id that is triggering the friend event
	Subject string `json:"subject,omitempty"`

	// SubjectOst The online service type of the subject
	//
	// List of types for reference:
	// * 0 - UNKNOWN
	// * 1 - XBOX LIVE
	// * 2 - SONY ENTERTAINMENT NETWORK
	// * 3 - STEAM
	// * 4 - WEB
	// * 5 - LEGACY GAME CENTER
	// * 6 - GOOGLE PLAY
	// * 9 - WINDOWS PHONE
	// * 10 - CALICO
	// * 11 - NINTENDO
	// * 12 - GAME CENTER
	// * 13 - WEGAME
	// * 14 - VORTEX
	// * 15 - EPIC
	// * 16 - STADIA
	// * 17 - FACEBOOK
	// * 18 - GOOGLE
	// * 19 - TWITTER
	// * 20 - TWITCH
	// * 21 - DEVICE
	// * 22 - APPLE
	// * 23 - ZENDESK
	// * 24 - T2GP
	// * 99 - WINDOWS DEVELOPER
	SubjectOst int `json:"subject_ost,omitempty"`

	// DatePosted unix time stamp of event
	DatePosted int64 `json:"date_posted,omitempty"`
}

// GroupTelemetryMeta Metadata included with a group telemetry event
type GroupTelemetryMeta struct {
	GenericTelemetryMeta

	// GroupCompositionId Incrementing counter of group member changes
	GroupCompositionId int64 `json:"group_composition_id,omitempty"`

	// GroupInstanceId T2GP Group Id
	GroupInstanceId string `json:"group_instance_id,omitempty"`

	// GroupMaxSize Max number of members allowed for group
	GroupMaxSize int `json:"group_max_size,omitempty"`

	// GroupPrivacySetting T2GP Groups privacy setting
	GroupPrivacySetting string `json:"group_privacy_setting,omitempty"`

	// GroupSize Number of current group members
	GroupSize int `json:"group_size,omitempty"`

	// IsCrossplayEnabled is cross play enabled for the T2GP group
	IsCrossplayEnabled *bool `json:"is_crossplay_enabled,omitempty"`

	// IsGroupHost is Subject the group leader
	IsGroupHost bool `json:"is_group_host,omitempty"`

	// Targets Array of player ids being targeted by the event.  Single player will be in 1 length array.
	Targets []string `json:"targets,omitempty"`
}

// FriendTelemetryMeta defines model for friendTelemetryMeta.
type FriendTelemetryMeta struct {
	GenericTelemetryMeta

	// Targets Array of player ids being targeted by the event.  Single player will be in 1 length array
	Targets []string `json:"targets,omitempty"`
}

// BlocklistTelemetryMeta defines model for blocklistTelemetryMeta.
type BlocklistTelemetryMeta struct {
	GenericTelemetryMeta

	// Targets Array of player ids being targeted by the event.  Single player will be in 1 length array
	Targets []string `json:"targets,omitempty"`
}

// ReportTelemetryMeta defines model for friendTelemetryMeta.
type ReportTelemetryMeta struct {
	GenericTelemetryMeta

	// Targets Array of player ids being targeted by the event.  Single player will be in 1 length array
	Targets []string `json:"targets,omitempty"`

	// ReportBody JSON parseable object passed by game with abuse reports.  This only exists for the report player event
	ReportContent *apipub.AbuseReport `json:"reportContent,omitempty"`

	ReportingUserId       string            `json:"reporting_user_id,omitempty"`
	ReportingUserLocale   string            `json:"reporting_user_locale,omitempty"`
	ReportingCategory     string            `json:"reporting_category,omitempty"`
	ReportMessage         string            `json:"report_message,omitempty"`
	ReportingUserPlatform string            `json:"reporting_user_platform,omitempty"`
	SubjectTitle          string            `json:"subject_title,omitempty"`
	VersionNumber         string            `json:"version_number,omitempty"`
	ReportingContentType  string            `json:"reporting_content_type,omitempty"`
	Os                    string            `json:"reporting_user_os,omitempty"`
	GameSessionInfo       map[string]string `json:"game_session_info,omitempty"`
}

// EndorsementTelemetryMeta defines model for friendTelemetryMeta.
type EndorsementTelemetryMeta struct {
	GenericTelemetryMeta

	// Targets Array of player ids being targeted by the event.  Single player will be in 1 length array
	Targets                 []string `json:"targets,omitempty"`
	EndorsementName         string   `json:"endorsement_name,omitempty"`
	CurrentEndorsementCount int      `json:"current_endorsement_count,omitempty"`
	TotalEndorsementCount   int      `json:"total_endorsement_count,omitempty"`
	IsPositive              bool     `json:"is_positive,omitempty"`
	IsPrivate               bool     `json:"is_private,omitempty"`
}

// BuildGenericTeleMeta function to generate meta data to submit on friend telemetry events
func BuildGenericTeleMeta(event EventType, productid, subject string, subjectOst apipub.OnlineServiceType, appid *string, additionalInfo *map[string]string) *GenericTelemetryMeta {

	return &GenericTelemetryMeta{
		AdditionalInfo: additionalInfo,
		AppId:          appid,
		Env:            utils.GetEnvironment(),
		Event:          string(event),
		EventId:        utils.GenerateNewULID(),
		ProductId:      productid,
		Subject:        subject,
		SubjectOst:     int(subjectOst),
		DatePosted:     time.Now().Unix(),
	}
}

// BuildReportTeleMeta function to generate meta data to submit on abuse report telemetry events
func BuildReportTeleMeta(ar *apipub.AbuseReport, event EventType, eventid string, productid string, subjectOst apipub.OnlineServiceType, appid *string, additionalInfo *map[string]string) *ReportTelemetryMeta {
	if ar == nil {
		return nil
	}

	reportContentType := ""
	if ar.ReportingContentType != nil {
		reportContentType = *ar.ReportingContentType
	}
	os := ""
	if ar.Os != nil {
		os = *ar.Os
	}
	// process telemeta if exists for telemetry
	gameSessionInfo := make(map[string]string)
	if ar.GameSessionInfo != nil {
		utils.ConvertMapInterfaceToMapString(*ar.GameSessionInfo, &gameSessionInfo)
	}

	tMeta := &ReportTelemetryMeta{}
	tMeta.AppId = appid
	tMeta.Env = utils.GetEnvironment()
	tMeta.Event = string(event)
	tMeta.EventId = eventid
	tMeta.ProductId = productid
	tMeta.Subject = ar.ReportingUserId
	tMeta.SubjectOst = int(subjectOst)
	tMeta.DatePosted = time.Now().Unix()
	tMeta.ReportContent = ar
	tMeta.Targets = []string{ar.ReportedUserId}
	tMeta.AdditionalInfo = additionalInfo
	tMeta.ReportingUserId = ar.ReportingUserId
	tMeta.ReportingUserLocale = ar.ReportingUserLocale
	tMeta.ReportingCategory = string(ar.ReportingCategory)
	tMeta.ReportMessage = ar.ReportMessage
	tMeta.ReportingUserPlatform = ar.ReportingUserPlatform
	tMeta.SubjectTitle = ar.SubjectTitle
	tMeta.ReportingContentType = reportContentType
	tMeta.Os = os
	tMeta.GameSessionInfo = gameSessionInfo

	return tMeta
}

// BuildBlocklistTeleMeta function to generate meta data to submit on blocklist telemetry events
func BuildBlocklistTeleMeta(event EventType, productid string, subject string, subjectOst apipub.OnlineServiceType, targets []string, appid *string, additionalInfo *map[string]string) *BlocklistTelemetryMeta {

	tMeta := &BlocklistTelemetryMeta{}
	tMeta.AdditionalInfo = additionalInfo
	tMeta.AppId = appid
	tMeta.Env = utils.GetEnvironment()
	tMeta.Event = string(event)
	tMeta.EventId = utils.GenerateNewULID()
	tMeta.ProductId = productid
	tMeta.Subject = subject
	tMeta.SubjectOst = int(subjectOst)
	tMeta.DatePosted = time.Now().Unix()
	tMeta.Targets = targets

	return tMeta
}

// BuildFriendTeleMeta function to generate meta data to submit on friend telemetry events
func BuildFriendTeleMeta(event EventType, productid string, subject string, subjectOst apipub.OnlineServiceType, targets []string, appid *string, additionalInfo *map[string]string) *FriendTelemetryMeta {

	tMeta := &FriendTelemetryMeta{}
	tMeta.AdditionalInfo = additionalInfo
	tMeta.AppId = appid
	tMeta.Env = utils.GetEnvironment()
	tMeta.Event = string(event)
	tMeta.EventId = utils.GenerateNewULID()
	tMeta.ProductId = productid
	tMeta.Subject = subject
	tMeta.SubjectOst = int(subjectOst)
	tMeta.DatePosted = time.Now().Unix()
	tMeta.Targets = targets

	return tMeta
}

// BuildEndorsementTeleMeta function to generate meta data to submit on friend telemetry events
func BuildEndorsementTeleMeta(event EventType, productid string, subject string, subjectOst apipub.OnlineServiceType, targets []string, endorsement apipub.EndorsementResponse, appid *string, additionalInfo *map[string]string) *EndorsementTelemetryMeta {

	tMeta := &EndorsementTelemetryMeta{}
	tMeta.AdditionalInfo = additionalInfo
	tMeta.AppId = appid
	tMeta.Env = utils.GetEnvironment()
	tMeta.Event = string(event)
	tMeta.EventId = utils.GenerateNewULID()
	tMeta.ProductId = productid
	tMeta.Subject = subject
	tMeta.SubjectOst = int(subjectOst)
	tMeta.DatePosted = time.Now().Unix()
	tMeta.Targets = targets
	tMeta.EndorsementName = endorsement.EndorsementName
	tMeta.CurrentEndorsementCount = endorsement.CurrentEndorsementCount
	tMeta.TotalEndorsementCount = endorsement.TotalEndorsementCount
	tMeta.IsPositive = endorsement.IsPositive
	tMeta.IsPrivate = endorsement.IsPrivate

	return tMeta
}

// BuildGroupTeleMeta function to generate meta data to submit on group telemetry events
func BuildGroupTeleMeta(group *apipub.GroupResponse, event EventType, subject string, subjectOst apipub.OnlineServiceType, targets []string, appid *string, additionalInfo *map[string]string) *GroupTelemetryMeta {
	if group == nil {
		return nil
	}

	compositionId := int64(0)
	if group.GroupCompositionId != nil {
		compositionId = *group.GroupCompositionId
	}

	size := 0
	if group.Members != nil && len(*group.Members) > 0 {
		size = len(*group.Members)
	}

	tMeta := &GroupTelemetryMeta{
		GroupCompositionId:  compositionId,
		GroupInstanceId:     group.Groupid,
		GroupMaxSize:        group.MaxMembers,
		GroupPrivacySetting: string(group.JoinRequestAction),
		GroupSize:           size,
		IsCrossplayEnabled:  group.CanCrossPlay,
		IsGroupHost:         group.GetMemberRole(subject) == apipub.Leader,
		Targets:             targets,
	}

	tMeta.AdditionalInfo = additionalInfo
	tMeta.AppId = appid
	tMeta.Env = utils.GetEnvironment()
	tMeta.Event = string(event)
	tMeta.EventId = utils.GenerateNewULID()
	tMeta.ProductId = group.Productid
	tMeta.Subject = subject
	tMeta.SubjectOst = int(subjectOst)
	tMeta.DatePosted = time.Now().Unix()

	return tMeta
}
