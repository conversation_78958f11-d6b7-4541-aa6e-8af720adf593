name: Reusable Post Deploy Steps

on:
  workflow_call:
    inputs:
      skip_notification:
        required: false
        default: true
        type: boolean
      version:
        required: true
        type: string
      environment_name:
        required: true
        type: string
      parent_ghaction_run_id:
        required: true
        type: string
      api_test_note:
        required: false
        type: string

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  post_deploy_steps:
    name: Post Deploy Steps
    runs-on: [t2gp-arc-linux]
    env:
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      SLACK_CHANNEL_ID: C03CTP9JY5V # https://tk2-d2c.slack.com/archives/C01BWL2EKGV
      PINNED_MSG_TS: '1677531090.605559' # https://tk2-d2c.slack.com/archives/C01BWL2EKGV/p1676569457606469
    outputs:
      deployment_info: ${{ steps.fetch_deployment_info.outputs.item }}
    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Fetch Deployment Info
        id: fetch_deployment_info
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: get
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          key: '{ "env_label": "${{ inputs.environment_name}}" }'
      - name: Fetch Deployment Info (cli)
        run: |
          result=$(aws dynamodb scan --table-name ${{env.ENV_VER_MAPPING_TABLE}} --scan-filter '{"env_label":{"AttributeValueList":[{"S":"pr-"}],"ComparisonOperator":"NOT_CONTAINS"}}' --query "Items[*].{environment:env_label.S,version:version.S,api_url:api_url.S,mqtt_url:mqtt_url.S}" --output json | sed 's/\"/\\"/g')
          result="${result//'%'/'%25'}"
          result="${result//$'\n'/'\n'}"
          result="${result//$'\r'/'%0D'}"
          echo "ENV_MAPPING="\`\`\`${result}\`\`\`"" >> $GITHUB_ENV
      - name: Slack Post Message
        if: inputs.skip_notification == false
        id: slack
        uses: slackapi/slack-github-action@v1.23.0
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          # Slack channel id, channel name, or user id to post message.
          # See also: https://api.slack.com/methods/chat.postMessage#channels
          # You can pass in multiple channels to post to by providing a comma-delimited list of channel IDs.
          channel-id: ${{ env.SLACK_CHANNEL_ID }}
          payload: |
            {
              "text": "Deployment for ${{inputs.environment_name}} Complete",
              "attachments": [
                {
                  "color": "#2c6e49",
                  "blocks": [
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": ":green_ball:\tSocial Backend Deployment\t:green_ball:",
                        "emoji": true
                      }
                    },
                    {
                      "type": "header",
                      "text": {
                        "type": "plain_text",
                        "text": "Environment: ${{inputs.environment_name}}",
                        "emoji": true
                      }
                    },
                    {
                      "type": "divider"
                    },
                    {
                      "type": "section",
                      "text": {
                        "type": "mrkdwn",
                        "text": "*Info*\nAPI Version: ${{ fromJson(steps.fetch_deployment_info.outputs.item).version }}\nAction: <https://github.com/${{ github.repository }}/actions/runs/${{ inputs.parent_ghaction_run_id }}|${{ inputs.parent_ghaction_run_id }}>\n"
                      }
                    },
                    {
                      "type": "section",
                      "text": {
                        "type": "mrkdwn",
                        "text": "*Links*\nAPI: ${{ fromJson(steps.fetch_deployment_info.outputs.item).api_url }}\nAdmin Portal: ${{ fromJson(steps.fetch_deployment_info.outputs.item).api_private_url }} \nMQTT: ${{ fromJson(steps.fetch_deployment_info.outputs.item).mqtt_url }}"
                      }
                    }

                  ]
                }
              ]
            }

      - name: Slack Update Message
        id: slack_update_pin
        uses: slackapi/slack-github-action@v1.23.0
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
        with:
          channel-id: ${{ env.SLACK_CHANNEL_ID }}
          update-ts: ${{ env.PINNED_MSG_TS }}
          payload: |
            {
              "attachments": [{
                "color": "#2c6e49",
                "blocks": [
                  {
                    "type": "header",
                    "text": {
                      "type": "plain_text",
                      "text": "Social Backend Environment Mapping",
                      "emoji": true
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "${{ env.ENV_MAPPING }}"
                    }
                  }
                ]
              }]
            }

  interaction_api_test_dispatch:
    name: Interaction API Test Dispatch
    needs: post_deploy_steps
    runs-on: [t2gp-arc-linux]
    steps:
      - name: Repository Dispatch
        uses: benc-uk/workflow-dispatch@v1
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          workflow: test-interaction-api.yml
          inputs: '{ "note": "${{ inputs.api_test_note }}", "mandatoryTag": "public-v2", "optionalInclusionTag": "happy", "optionalExclusionTag": "broken,lowprio", "loopType": "retries if any tests fail", "maxNumber": "0", "socialServiceEnvLabel":"${{ fromJson(needs.post_deploy_steps.outputs.deployment_info).env_label }}" }'
