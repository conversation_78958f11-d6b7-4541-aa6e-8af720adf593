package cache

import (
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func Test_SecondaryIndex(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockRC(t)
	defer mock.ctrl.Finish()

	g.Describe("setSecondaryIndex", func() {

		var groupid, productid, memberid string
		var group *apipub.GroupResponse
		var err error

		// buildup obj
		g.Before(func() {
			groupid = utils.GenerateRandomDNAID()
			productid = utils.GenerateRandomDNAID()
			memberid = utils.GenerateRandomDNAID()

			//create group that will be indexed and insert into cache
			group = &apipub.GroupResponse{
				Groupid:   groupid,
				Productid: productid,
				Members: &[]apipub.GroupMemberResponse{
					{
						Userid:    memberid,
						Productid: productid,
						Role:      "leader",
					},
				},
			}

			err = setCachedObject(ctx, rc, group, group.RedisKey("test"), time.Duration(cfg.TtlDefault)*time.Second)
			g.Assert(err).IsNil()
		})

		// teardown group
		g.After(
			func() {
				err = rc.DeleteCachedObj(ctx, group.RedisKey("test"))
				g.Assert(err).IsNil()
			})

		g.It("should return err if nil idx", func() {
			err = rc.setSecondaryIndex(ctx, nil)
			g.Assert(err).IsNotNil()
			g.Assert(errs.IsEqual(err, errs.ERedisNilIndex)).IsTrue()
		})

		g.It("should return err if empty key", func() {
			idx := index.NewSecondaryIndex("", group.RedisKey("test"))
			err2 := rc.setSecondaryIndex(ctx, idx)
			g.Assert(err2).IsNotNil()
			g.Assert(errs.IsEqual(err2, errs.ERedisNilIndex)).IsTrue()
		})

		// g.It("should return err if empty value", func() {

		// 	idx, _ := index.NewSecondaryIndex(apipub.BuildUsersGroupsRedisKey("test", memberid, productid), "")
		// 	idx.SetScore(0)

		// 	err = rdb.setSecondaryIndex(ctx, idx)
		// 	g.Assert(err).IsNotNil()
		// 	g.Assert(errs.IsEqual(err, errs.ERedisInvalidKey)).IsTrue()
		// })

		// 	g.It("set and get secondary index for usergroup", func() {
		//

		// 		idx, _ := index.NewSecondaryIndex(apipub.BuildUsersGroupsRedisKey(memberid, productid), apipub.BuildGroupRedisKey(tenant, productid, groupid))

		// 		mock.redisMock.ClearExpect()
		// 		mock.redisMock.ExpectZAdd(idx.IdxKey(), redis.Z{
		// 			Member: idx.ValKey(),
		// 		})
		// 		err = rdb.setSecondaryIndex(ctx, idx)
		// 		g.Assert(err).IsNil()

		// 		rangeBy := &redis.ZRangeBy{
		// 			Min: "-",
		// 			Max: "+",
		// 		}

		// 		var userGroups *[]*apipub.Group
		// 		mock.redisMock.ClearExpect()
		// 		mock.redisMock.ExpectZRangeByLex(idx.IdxKey(), rangeBy).SetVal([]string{apipub.BuildUsersGroupsRedisKey(memberid, productid)})

		// 		limit := int64(0)
		// 		next := ""

		// 		userGroups, _, err = getObjsFromSecondaryIndex[apipub.Group](ctx, rdb, idx, &limit, &next, false)
		// 		g.Assert(err).IsNil()
		// 		g.Assert(userGroups).IsNotNil()
		// 		g.Assert(len(*userGroups)).Equal(1)
		// 		g.Assert((*userGroups)[0].Groupid).Equal(groupid)

		// 		err = rdb.delSecondaryIndex(ctx, idx)
		// 		g.Assert(err).IsNil()

		// 	})
	})
}
