package cache

import (
	"context"
	"errors"
	"sync"
	"time"

	"github.com/bsm/redislock"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

var expirationWg sync.WaitGroup

//func (rc *RedisCache) hasLock(ctx context.Context, lock *redislock.Lock) bool {
//	log := logger.FromContext(ctx)
//
//	if lock == nil {
//		return false
//	}
//	if ttl, err := lock.TTL(ctx); err != nil {
//		log.Error().Err(err).Msgf("failed to get ttl")
//		return false
//	} else if ttl > 0 {
//		return true
//	}
//	return false
//}

// getLockCluster get lock on redis for ctx, key, config and ttl.
func (rc *RedisCache) getLockCluster(ctx context.Context, key string, ttl time.Duration) *redislock.Lock {
	log := logger.FromContext(ctx)

	//no retry on lock since we're not trying to force it.  just try again next ticker.
	lock, err := rc.lockClient.Obtain(ctx, key, ttl, &redislock.Options{
		RetryStrategy: redislock.NoRetry(),
		Metadata:      "",
	})
	if errors.Is(err, redislock.ErrNotObtained) {
		// log.Debug().Msgf("failed to obtain lock for key %s", key)
		return nil
	} else if err != nil {
		log.Error().Err(err).Msgf("fatal obtain lock for key %s", key)
		return nil
	}

	return lock
}

// GetSyncLock get sync lock on redis for ctx, client, key, config and ttl.
func (rc *RedisCache) GetSyncLock(ctx context.Context, key string, ttl time.Duration) *redislock.Lock {
	return rc.getLockCluster(ctx, key, ttl)
}
