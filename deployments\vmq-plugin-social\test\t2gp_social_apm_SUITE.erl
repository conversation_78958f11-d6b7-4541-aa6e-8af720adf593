-module(t2gp_social_apm_SUITE).

-include_lib("opencensus/include/opencensus.hrl").
-include_lib("eunit/include/eunit.hrl").

-compile(nowarn_export_all).
-compile(export_all).

init_per_suite(Config) ->
    t2gp_social_test:configure(),
    application:ensure_all_started(hackney),
    application:ensure_all_started(opencensus),
    application:ensure_all_started(ssl),
    cover:start(),
    Config.

end_per_suite(_Config) ->
    ok.

all() -> [
    test_init,
    test_reload,
    test_span,
    test_span_start_finish,
    test_tags,
    test_http_client,
    test_reporter,
    test_stats_export,
    test_stats_export_tags,
    test_stats_misc
].

test_init(_) ->
    os:putenv("DD_AGENT_HOST", "localhost"),
    ok = t2gp_social_apm:init(),
    {state, [{t2gp_social_apm, Config}], _, _} = sys:get_state(oc_reporter),
    "localhost" = maps:get(host, Config, ""),
    os:unsetenv("DD_AGENT_HOST"),
    ok = t2gp_social_apm:init(),
    ok.


test_reload(_) ->
    os:unsetenv("DD_AGENT_HOST"),
    ok = t2gp_social_apm:on_reload(),
    ok.

test_span(_) ->
    % test w/o datadog agent
    os:unsetenv("DD_AGENT_HOST"),
    false = os:getenv("DD_AGENT_HOST"),
    1234 = t2gp_social_apm:span(<<"name">>, <<"resource">>, fun() -> 1234 end),
    % test w/ datadog agent
    os:putenv("DD_AGENT_HOST", "foo"),
    "foo" = os:getenv("DD_AGENT_HOST"),
    4567 = t2gp_social_apm:span(<<"name">>, <<"resource">>, fun() -> 4567 end),
    [{_, Buffer}] = ets:lookup(oc_report_status, current_buffer),
    S = ets:tab2list(Buffer),
    1 = length(S),
    ets:delete_all_objects(Buffer),
    os:unsetenv("DD_AGENT_HOST"),
    ok.

test_span_start_finish(_) ->
    % no agent
    os:unsetenv("DD_AGENT_HOST"),
    undefined =  t2gp_social_apm:span_start(<<"name">>, <<"resource">>),
    true =  t2gp_social_apm:span_finish(),
    
    % with agent
    os:putenv("DD_AGENT_HOST", "dd-agent"),
    undefined =  t2gp_social_apm:span_start(<<"name">>, <<"resource">>),
    #span_ctx{trace_id = _} = ocp:current_span_ctx(),
    true =  t2gp_social_apm:span_finish(),
    undefined = ocp:current_span_ctx(),

    os:unsetenv("DD_AGENT_HOST"),
    ok.

test_tags(_) ->
    ocp:with_child_span(<<"test">>),
    M = t2gp_social_apm:tags(#{foo => <<"bar">>}),
    M = #{foo => true},
    #span_ctx{span_id = SpanId} = ocp:current_span_ctx(),
    [Span] = ets:lookup(oc_span_tab, SpanId),
    #span{ name = Name, attributes = Attr} = Span,
    Name = <<"test">>,
    Attr = #{<<"foo">> => <<"bar">>},
    ocp:finish_span(),
    ok.

test_http_client(_) ->
    ok = t2gp_social_apm:http_client("http://httpbin/put", [], <<>>),
    {error, {http_error, 500, _, _}} = t2gp_social_apm:http_client("http://httpbin/status/500", [], <<>>),
    {error, _} = t2gp_social_apm:http_client("bad-url", [], <<>>),
    ok = t2gp_social_apm:http_client("", [], <<>>),
    ok.

test_reporter(_) ->
    [] = t2gp_social_apm:group([]),
    <<"123">> = t2gp_social_apm:to_meta(foo, 123),
    <<"3.140000000000000124344978758017532527446746826171875">> = t2gp_social_apm:to_meta(foo, 3.14),
    <<"true">> = t2gp_social_apm:to_meta(foo, true),
    <<"false">> = t2gp_social_apm:to_meta(foo, false),
    <<"test">> = t2gp_social_apm:to_tag(foo, fun () -> <<"test">> end),
    <<"test">> = t2gp_social_apm:to_tag(foo, "test"),
    undefined = t2gp_social_apm:span_field(<<"error">>, #span{status = undefined}),
    undefined = t2gp_social_apm:span_field(<<"error">>, #span{status = #status{code = 0}}) ,
    1 = t2gp_social_apm:span_field(<<"error">>, []),
    ok.

test_stats_export(_) ->
    _ = application:ensure_all_started(oc_datadog),
    {ok, Socket} = gen_udp:open(8125, [{active, once}]),
    Measure = oc_stat_measure:new('datadog/test', "Test", foos),
    {ok, View} = oc_stat_view:subscribe(#{
                  name => 'datadog/test',
                  measure => Measure,
                  description => "Test",
                  tags => [foo, bar],
                  aggregation => oc_stat_aggregation_count}),

    %% normal export
    _ = oc_stat:record(#{}, 'datadog/test', 1),
    Data = oc_stat_view:export(View),
    t2gp_social_apm:export([Data], []),
    receive
        {udp, _, _, _, "datadog.test:1|g"} -> ok;
        Msg1 ->
            ct:fail("Unknown message: ~p", [Msg1])
    after
        1000 -> ct:fail("Didn't received message in 1s")
    end,

    oc_stat_view:unsubscribe(View),
    oc_stat_view:deregister(View),
    gen_udp:close(Socket),
    ok.

test_stats_export_tags(_) ->
    %% export w/ tags
    _ = application:ensure_all_started(oc_datadog),
    {ok, Socket} = gen_udp:open(8125, [{active, once}]),
    Measure = oc_stat_measure:new('datadog/test', "Test", foos),
    {ok, View} = oc_stat_view:subscribe(#{
                  name => 'datadog/test',
                  measure => Measure,
                  description => "Test",
                  tags => [foo, bar],
                  aggregation => oc_stat_aggregation_count}),
    
    _ = oc_stat:record(#{foo => "1", bar => "2"}, 'datadog/test', 1),
    Data = oc_stat_view:export(View),
    t2gp_social_apm:export([Data], []),
    receive
        {udp, _, _, _, "datadog.test:1|g|#bar:2,foo:1"} -> ok;
        {udp, _, _, _, "datadog.test:1|g|#foo:1,bar:2"} -> ok;
        Msg ->
            ct:fail("Unknown message: ~p", [Msg])
    after
        1000 -> ct:fail("Didn't received message in 1s")
    end,

    oc_stat_view:unsubscribe(View),
    oc_stat_view:deregister(View),
    gen_udp:close(Socket),
    ok.

test_stats_misc(_) ->
    ["|#", "foo"] = t2gp_social_apm:tags_append([], "foo"),
    ["foo", $,, "bar"] = t2gp_social_apm:tags_append("foo", "bar"),
    <<"infinity">> = t2gp_social_apm:format_num(infinity),
    <<"3.14">> = t2gp_social_apm:format_num(3.14),
    [<<"foo">>,<<".">>|<<"bar">>] = t2gp_social_apm:to_key("foo@!@#bar"),
    [[".count:",<<"10">>,"|g",[]],
        [".mean:",<<"3">>,"|g",[]],
        [".sum:",<<"4">>,"|g",[]]] = t2gp_social_apm:build_row(sum, #{count=>10,mean=>3,sum=>4}, []),
    ok.
