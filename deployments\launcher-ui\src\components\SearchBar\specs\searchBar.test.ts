import { render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import SearchBarTestWrapper from './SearchBarTestWrapper.svelte';
jest.mock('../../../assets/icons', () => ({
  SVGSearch: SVGIconMock,
}));

jest.mock('lodash.debounce', () => {
  return {
    default: jest.fn(fn => fn),
  };
});

describe('SearchBar', () => {
  it('should render UI with placeholder', () => {
    const PLACEHOLDER_TEXT = 'Search your fiends';
    const { getByPlaceholderText } = render(SearchBarTestWrapper, {
      props: {
        placeholder: PLACEHOLDER_TEXT,
      },
    });
    expect(getByPlaceholderText(PLACEHOLDER_TEXT)).not.toBeNull();
  });
});
