<script lang="ts">
  import type { SocialFriend } from '../../services';
  import { isOnline, isPlaying } from '../../utils';
  import { Avatar } from '../Avatar';

  export let friend: SocialFriend;

  $: status = friend?.presence[0]?.status;
  $: gamePlaying = friend?.presence[0]?.gameName;
  $: playing = isPlaying(status);
  $: online = isOnline(status) || isPlaying(status);
  $: initials = friend?.name[0];
  $: avatar = friend?.avatar;
  $: displayName = friend?.name;
</script>

<style>
  .friend-card {
    height: 3.625rem;
    background-color: rgba(54, 54, 54, 1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    padding: 0.625rem 1rem;
    width: 100%;
    box-sizing: border-box;
  }

  .friend-card.online {
    background-color: var(
      --social-bg-color-friend-card,
      var(--default-bg-color-friend-bar)
    );
  }

  .friend-card .detail {
    color: rgba(255, 255, 255, 1);
    margin-left: 1rem;
    display: flex;
    flex-direction: column;
  }

  .friend-card .detail .name {
    font-size: 1rem;
    line-height: 150%;
    font-weight: bold;
    opacity: 0.6;
    text-transform: capitalize;
  }

  .friend-card .detail .status {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.875rem;
    line-height: 150%;
    text-transform: capitalize;
  }

  .friend-card .detail .name.online {
    opacity: 1;
  }
</style>

<div class="friend-card" class:online>
  <Avatar initials="{initials}" size="35px" status="{status}" src="{avatar}" />
  <div class="detail">
    <span class="name" class:online>{displayName}</span>
    {#if playing}<span class="status">Playing: {gamePlaying}</span>{/if}
  </div>
</div>
