package api

import (
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/go-test/deep"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	gomock "go.uber.org/mock/gomock"
)

type PresenceMatcher struct {
	presence apipub.PresenceResponse
	g        *goblin.G
}

func (p *PresenceMatcher) Matches(x interface{}) bool {
	if pr, ok := x.(*apipub.PresenceResponse); ok {
		if pr != nil {
			p1 := *pr
			p2 := p.presence
			now := time.Now().UTC()
			p1.Timestamp = now
			p2.Timestamp = now
			diff := deep.Equal(&p1, &p2)
			p.g.Assert(diff).IsNil()
			return diff == nil
		}
	}
	return false
}

func (p *PresenceMatcher) String() string {
	return fmt.Sprintf("PresenceMatcher: %v", p.presence)
}

func TestSetUserPresence(t *testing.T) {
	g := goblin.Goblin(t)

	UserID1 := "b287e655461f4b3085c8f244e394ff7e"
	ProductID := "4029a6ffe9924f969955aa2e1c0782aa"

	g.Describe("SetUserPresence", func() {
		g.It("should succeed with nil presence", func() {
			g.Timeout(20 * time.Second)
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			reqBody := apipub.SetPresenceRequestBody{
				GameData: aws.String("gameData"),
				GameName: "gameName",
				Ttl:      aws.Int64(120),
				Meta: &map[string]interface{}{
					"foo": "bar",
				},
				RichPresence: aws.String("richPresence"),
				Status:       apipub.Online,
			}
			w, r := AddBodyToRequest(reqBody, User1JWT)

			// matching presence to store
			//presence := apipub.PresenceResponse{
			//	Userid:       UserID1,
			//	Productid:    ProductID,
			//	Status:       reqBody.Status,
			//	RichPresence: reqBody.RichPresence,
			//	GameData:     reqBody.GameData,
			//	GameName:     reqBody.GameName,
			//	Meta:         reqBody.Meta,
			//	Priority:     apipub.PresencePriorityGameSetStart,
			//	Ttl:          reqBody.Ttl,
			//	Platformid:   aws.String(""),
			//}
			//presenceMatcher := &PresenceMatcher{g: g, presence: presence}

			mock.rc.EXPECT().GetPresence(r.Context(), UserID1, ProductID, gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetLowestAvailablePriority(r.Context(), UserID1, ProductID).AnyTimes().Return(apipub.PresencePriorityGameSetStart, nil)
			mock.tele.EXPECT().SendGenericEvent(gomock.Any(), gomock.Any()).Return(nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().SavePresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			//mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			//mock.rc.EXPECT().SetPresence(r.Context(), presenceMatcher, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			//mock.rc.EXPECT().GetUserGroups(r.Context(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", nil)
			mock.api.SetUserPresence(w, r)

			g.Assert(w.Code).Equal(http.StatusOK)
		})

		g.It("should fail w/ bad JWT", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			w, r := Login(BadJWT)
			mock.api.SetUserPresence(w, r)

			g.Assert(w.Code).Equal(http.StatusUnauthorized)
		})
	})
}
