import { render } from '@testing-library/svelte';
import { AccordionMock, AccordionSectionMock } from '../../Accordion/__mock__';
import FriendRequestCardMock from '../../FriendRequestCard/__mock__/FriendRequestCard.svelte';
import FriendRequestsActionBarMock from '../../FriendRequestsActionBar/__mock__/FriendRequestsActionBar.svelte';
import LoadingSpinnerMock from '../../LoadingSpinner/__mock__/LoadingSpinner.svelte';
import FriendRequestsWrapper from './FriendRequestsWrapper.svelte';

jest.mock('../../Accordion', () => ({
  Accordion: AccordionMock,
  AccordionSection: AccordionSectionMock,
}));

jest.mock('../../FriendRequestCard', () => ({
  FriendRequestCard: FriendRequestCardMock,
}));

jest.mock('../../FriendRequestsActionBar', () => ({
  FriendRequestsActionBar: FriendRequestsActionBarMock,
}));

jest.mock('../../LoadingSpinner', () => ({
  LoadingSpinner: LoadingSpinnerMock,
}));

describe('FriendRequests', () => {
  it('should render UI', () => {
    expect(() => render(FriendRequestsWrapper)).not.toThrow();
  });
});
