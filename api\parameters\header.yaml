platform-identifier:
  name: platform-identifier
  description: this optional header is for tracking the platform being used for server-side metrics and debugging purposes
  in: header
  requred: false
  x-go-name: PlatformIdentifier
  schema:
    type: string
    required: false
client-version:
  type: string
  name: client-version
  description: this optional header is for tracking game client versions for server-side metrics and debugging purposes
  in: header
  requred: false
  x-go-name: ClientVersion
  schema:
    type: string
    required: false
t2gp-plugin-version:
  type: string
  name: t2gp-plugin-version
  description: this optional header is for tracking t2gp plugin version, if it is used, for server-side metrics and debugging purposes
  in: header
  required: false
  x-go-name: PluginVersion
  schema:
    type: string
    required: false
engine-identifier:
  type: string
  name: engine-identifier
  description: this optional header is for tracking the game engine name for server-side metrics and debugging purposes
  in: header
  requred: false
  x-go-name: EngineIdentifier
  schema:
    type: string
    required: false
engine-version:
  type: string
  name: engine-version
  description: this optional header is for tracking the game engine version for server-side metrics and debugging purposes
  in: header
  requred: false
  x-go-name: EngineVersion
  schema:
    type: string
    required: false