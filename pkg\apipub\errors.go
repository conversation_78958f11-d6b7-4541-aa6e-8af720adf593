package apipub

import (
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// UnescapedCookieParamError

// GetHttpErrorCode return http error code
func (e *UnescapedCookieParamError) GetHttpErrorCode() int {
	return http.StatusBadRequest
}

// GetSocialErrorCode return social error code
func (e *UnescapedCookieParamError) GetSocialErrorCode() errs.SocialError {
	return errs.EOapiUnescapedCookieParam
}

// GetStack return stack for error
func (e *UnescapedCookieParamError) GetStack() *string {
	return utils.GetStack()
}

/* UnmarshalingParamError struct */

// GetHttpErrorCode return http error code
func (e *UnmarshalingParamError) GetHttpErrorCode() int {
	return http.StatusBadRequest
}

// GetSocialErrorCode return social error code
func (e *UnmarshalingParamError) GetSocialErrorCode() errs.SocialError {
	return errs.EOapiUnmarshalingParam
}

// GetStack return stack for error
func (e *UnmarshalingParamError) GetStack() *string {
	return utils.GetStack()
}

/* RequiredParamError */

// GetHttpErrorCode return http error code
func (e *RequiredParamError) GetHttpErrorCode() int {
	return http.StatusBadRequest
}

// GetSocialErrorCode return social error code
func (e *RequiredParamError) GetSocialErrorCode() errs.SocialError {
	return errs.EOapiRequireParam
}

// GetStack return stack for error
func (e *RequiredParamError) GetStack() *string {
	return utils.GetStack()
}

/* RequiredHeaderError */

// GetHttpErrorCode return http error code
func (e *RequiredHeaderError) GetHttpErrorCode() int {
	return http.StatusBadRequest
}

// GetSocialErrorCode return social error code
func (e *RequiredHeaderError) GetSocialErrorCode() errs.SocialError {
	return errs.EOApiRequireHeader
}

// GetStack return stack for error
func (e *RequiredHeaderError) GetStack() *string {
	return utils.GetStack()
}

/* InvalidParamFormatError */

// GetHttpErrorCode return http error code
func (e *InvalidParamFormatError) GetHttpErrorCode() int {
	return http.StatusBadRequest
}

// GetSocialErrorCode return social error code
func (e *InvalidParamFormatError) GetSocialErrorCode() errs.SocialError {
	return errs.EOapiInvalidParamFormat
}

// GetStack return stack for error
func (e *InvalidParamFormatError) GetStack() *string {
	return utils.GetStack()
}

/* TooManyValuesForParamError */

// GetHttpErrorCode return http error code
func (e *TooManyValuesForParamError) GetHttpErrorCode() int {
	return http.StatusBadRequest
}

// GetSocialErrorCode return social error code
func (e *TooManyValuesForParamError) GetSocialErrorCode() errs.SocialError {
	return errs.EOapiTooManyValuesForParam
}

// GetStack return stack for error
func (e *TooManyValuesForParamError) GetStack() *string {
	return utils.GetStack()
}
