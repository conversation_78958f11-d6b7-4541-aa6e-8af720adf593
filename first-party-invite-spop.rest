# @baseUrl=https://social-service-develop.d2dragon.net
@baseUrl=http://localhost:8000
@accesstoken={{login.response.body.$.accessToken}}
@accesstokensteam={{loginSteam.response.body.$.accessToken}}

@accesstokensteamSPOP={{loginSteamSPOP.response.body.$.accessToken}}

@accesstokensteam2nd={{loginSteam2nd.response.body.$.accessToken}}

@groupid={{createGroup.response.body.$.groupid}}

@approverid={{login.response.body.$.accountId}}

### login 2k to create group
# @name login
POST {{baseUrl}}/v1/auth/login

{
  "email": "<EMAIL>",
  "password": "D2CTesting",
  "locale": "en-US",
  "appId": "955e2f6c7ce345a6ba0d4d4f77d9257d"
}


### login steam
# @name loginSteam
POST https://sso.api.2kcoretech.online/sso/v2.0/auth/tokens
Content-Type: application/json
Authorization: Application 955e2f6c7ce345a6ba0d4d4f77d9257d

{
	"locale": "en-GB",
	"accountType": "platform",
    "instanceId": "444e747fce9147af94d702956de0b7ab",
	"credentials":
	{
        "type": "steam",
	    "steamUserId": "*****************",
	    "steamProfileName": "d2cfriendstest002"
	}
}

### login steam SPOP
# @name loginSteamSPOP
POST https://sso.api.2kcoretech.online/sso/v2.0/auth/tokens
Content-Type: application/json
Authorization: Application 955e2f6c7ce345a6ba0d4d4f77d9257d

{
	"locale": "en-GB",
	"accountType": "platform",
    "instanceId": "fd0de02d706f43d094b441c8baed699f",
    "overrideSession": true,
	"credentials":
	{
        "type": "steam",
	    "steamUserId": "*****************",
	    "steamProfileName": "d2cfriendstest002"
	}
}

### Another login steam
# @name loginSteam2nd
POST https://sso.api.2kcoretech.online/sso/v2.0/auth/tokens
Content-Type: application/json
Authorization: Application 955e2f6c7ce345a6ba0d4d4f77d9257d

{
	"locale": "en-GB",
	"accountType": "platform",
    "instanceId": "bf794e8ee9614a678a5c9c07e9885730",
	"credentials":
	{
        "type": "steam",
	    "steamUserId": "*****************",
	    "steamProfileName": "steve.jiang"
	}
}

###
# @name createGroup
POST {{baseUrl}}/v1/groups
Authorization: Bearer {{accesstoken}}
Content-Type: application/json

{
    "maxMembers": 5,
    "joinRequestAction": "auto-reject"
}

###
# @name createGroupStream
POST {{baseUrl}}/v1/groups
Authorization: Bearer {{accesstokensteam}}
Content-Type: application/json

{
    "maxMembers": 5,
    "joinRequestAction": "auto-approve"
}

###
# @name createGroupStreamSPOP
POST {{baseUrl}}/v1/groups
Authorization: Bearer {{accesstokensteamSPOP}}
Content-Type: application/json

{
    "maxMembers": 5,
    "joinRequestAction": "auto-approve"
}

###
# @name createGroupStream2nd
POST {{baseUrl}}/v1/groups
Authorization: Bearer {{accesstokensteam2nd}}
Content-Type: application/json

{
    "maxMembers": 5,
    "joinRequestAction": "auto-approve"
}

###
# @name sendFirstPartyInvite
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships
Authorization: Bearer {{accesstokensteam}}

{
    "expireIn":3600,
    "isFirstPartyInvite":true,
    "memberid":"*****************",
    "onlineServiceType":3,
    "status":"invited"
}

###
# @name acceptFirstPartyInviteSPOP
PATCH {{baseUrl}}/v1/user/invites/01GKAA8SH6343SNE656ZE5C7AG
Authorization: Bearer {{accesstokensteamSPOP}}

###
# @name firstPartyJoinRequest
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships
Authorization: Bearer {{accesstokensteam}}

{
    "approverid": "{{approverid}}",
    "canCrossPlay": true,
    "isFirstPartyInvite": true,
    "memberid": "*****************",
    "onlineServiceType": 3,
    "password": "",
    "status": "requested"
}

### view group
GET {{baseUrl}}/v1/groups/{{groupid}}
Authorization: Bearer {{accesstoken}}
