import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';
import { MqttWrapper } from '../../../lib/mqttWrapper';

let tokenHost: string;
let tokenInvited: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  tokenInvited = await socialApi.loginIn(
    config.invitedUsername,
    config.invitedPassword
  );
});

afterEach(async () => {
  await socialApi.deleteFriend(tokenHost, config.invitedUserId);
  await socialApi.loginOut(tokenHost);
  await socialApi.loginOut(tokenInvited);
});
describe('', () => {
  /**
   * Checking get Message
   * - Create room
   * - Get roomMember=1
   * - joinRoon
   * - Get roomMember=2
   * - B getMessage length
   * - A send message B
   * - B getMessage add 1 to the original length
   */
  it('get Message', async () => {
    let resp: request.Response = await socialApi.makeFriends(
      tokenHost,
      config.invitedUserId
    );
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({ status: 'pending' });
    resp = await socialApi.makeFriends(tokenInvited, config.inviteUserId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({ status: 'friend' });
    resp = await socialApi.getMessage(tokenHost, config.invitedUserId);
    const itemL = resp.body.items.length;
    resp = await socialApi.sendMessage(tokenHost, config.invitedUserId);
    resp = await socialApi.getMessage(tokenHost, config.invitedUserId);
    expect(resp.body.items.length).toEqual(itemL + 1);
  });
});
