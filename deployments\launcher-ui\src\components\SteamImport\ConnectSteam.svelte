<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { SVGSteam } from '../../assets/icons';
  import {
    EVENT_STEAM_ACCOUNT_LINKING,
    EVENT_STEAM_CONNECT_PAGE_EXIT,
    EVENT_STEAM_LINK_WINDOW_CLOSE,
    EVENT_STEAM_LINK_WINDOW_CLOSED,
    EVENT_STEAM_LINK_WINDOW_OPEN,
  } from '../../constant';
  import { useTranslator, useTransportService } from '../../hooks';
  import Button from '../Button/Button.svelte';

  let steamAccountLinking = false;
  const t = useTranslator();
  const transportService = useTransportService();

  const onLinkButtonClicked = () => {
    transportService.publishEvent(EVENT_STEAM_LINK_WINDOW_OPEN);
  };

  const onCancelButtonClicked = () => {
    transportService.publishEvent(EVENT_STEAM_LINK_WINDOW_CLOSE);
  };

  onMount(() => {
    transportService.subscribeEvent(EVENT_STEAM_ACCOUNT_LINKING, () => {
      steamAccountLinking = true;
    });

    transportService.subscribeEvent(EVENT_STEAM_LINK_WINDOW_CLOSED, () => {
      steamAccountLinking = false;
    });
  });

  onDestroy(() => {
    transportService.publishEvent(EVENT_STEAM_CONNECT_PAGE_EXIT);
    transportService.unsubscribe(EVENT_STEAM_ACCOUNT_LINKING);
    transportService.unsubscribe(EVENT_STEAM_LINK_WINDOW_CLOSED);
  });
</script>

<style>
  .description {
    color: var(--social-color, var(--default-color));
    opacity: 0.6;
    font-size: 0.875rem;
    line-height: 150%;
    font-weight: 500;
    margin: 0.5rem 0 1.5rem 0;
  }
</style>

<p class="description">
  {#if steamAccountLinking}
    {$t(
      'Complete your log in through the web browser. This page will automatcially refresh'
    )}
  {:else}
    {$t('Connect your Steam account to add 2K friends who are also on Steam')}
  {/if}
</p>

{#if steamAccountLinking}
  <Button on:buttonClick="{onCancelButtonClicked}" outline>
    <span slot="label"> {$t('Cancel')} </span>
  </Button>
{:else}
  <Button on:buttonClick="{onLinkButtonClicked}" showIcon>
    <span slot="icon"><SVGSteam /></span>
    <span slot="label"> {$t('Connect')} </span>
  </Button>
{/if}
