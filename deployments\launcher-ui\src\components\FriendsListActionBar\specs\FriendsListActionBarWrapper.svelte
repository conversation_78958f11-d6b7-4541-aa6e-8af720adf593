<script lang="ts">
  import { setContext } from 'svelte';
  import {
    CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT,
    INITIAL_LANGUAGE,
  } from '../../../constant';
  import { initI18nContext, SocialServicesContext } from '../../../context';
  import FriendsListActionBar from '../FriendsListActionBar.svelte';

  export let context: SocialServicesContext;

  setContext(CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT, context);
  initI18nContext(INITIAL_LANGUAGE);
</script>

<div>
  <FriendsListActionBar />
</div>
