-ifndef(T2GP_SOCIAL_HRL).
-define(T2GP_SOCIAL_HRL, true).

-include_lib("vernemq_dev/include/vernemq_dev.hrl").

-type user() :: erlcloud_ddb2:out_item().
-type item() :: erlcloud_ddb2:out_item().
-type pk() :: binary().
-type sk() :: binary().
-type state() :: term().
-type groupid() :: binary().


-type subscription() :: {topic(), subinfo()}.
-type node_subs() :: {node(), boolean(), [subscription()]}.
-type subs() :: [node_subs()].

-define(TS_NOW, list_to_binary(erlcloud_aws:format_timestamp(erlang:universaltime()))).

% fields names & constants
-define(PK, <<"pk">>).
-define(SK, <<"sk">>).
-define(TYPE, <<"type">>).
-define(CREATED, <<"created">>).
-define(USER, <<"user">>).
-define(FRIEND, <<"friend">>).
-define(FRIENDID, <<"friendid">>).
-define(PRESENCE, <<"presence">>).
-define(TIMESTAMP, <<"timestamp">>).
-define(STATUS, <<"status">>).
-define(EXTRA, <<"extra">>).
-define(INVITER, <<"inviter">>).
-define(INVITEE, <<"invitee">>).
-define(MESSAGE, <<"message">>).
-define(PENDING, <<"pending">>).
-define(BLOCKED, <<"blocked">>).
-define(UNFRIEND, <<"unfriend">>).
-define(ONLINE, <<"online">>).
-define(OFFLINE, <<"offline">>).
-define(USERID, <<"userid">>).
-define(EMAIL, <<"email">>).
-define(GROUP, <<"group">>).
-define(GROUPID, <<"groupid">>).
-define(PAI, <<"pai">>). % JWT parent account id
-define(SUB, <<"sub">>).

-define(META_USER_SUBID, {t2gp, user_subsciption_id}).
-define(META_SUBID_USER, {t2gp, subscription_id_user}).

-define(E_JWT_SUB_USERNAME_MISMATCH, #{
    reason_code => ?BAD_USERNAME_OR_PASSWORD,
    reason_string => <<"jwt: username mismatch">>
}).
-define(E_JWT_EXPIRED, #{
    reason_code => ?BAD_USERNAME_OR_PASSWORD,
    reason_string => <<"jwt: token expired">>
}).
-define(E_JWT_VALIDATION_FAILED, #{
    reason_code => ?BAD_USERNAME_OR_PASSWORD,
    reason_string => <<"jwt: validation failed">>
}).
-define(E_INTERNAL_SERVER_ERROR, #{
    reason_code => ?BAD_USERNAME_OR_PASSWORD,
    reason_string => <<"internal server error">>
}).

%% get module, function, arity of the current function
-define(mfa(), element(2, process_info(self(), current_function))).

-define(curr_fname(), list_to_binary(io_lib:format("~p:~p/~p", tuple_to_list(?mfa())))).

-define(log_stack(), [{stack, unicode:characters_to_binary(list_to_binary(lager:pr_stacktrace(t2gp_social_apm:stack_trace())), unicode)}]).


-define(APM_NAME, <<"vmq.t2gp">>).

-endif.
