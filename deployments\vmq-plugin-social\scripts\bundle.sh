#!/bin/sh

set -x

BUNDLE=vmq-plugin-social
BUILD_DIR=_build/default
BUNDLE_DIR=${PWD}/_build
echo "Generating _build/${BUNDLE}.tar.gz"
rm -f ${BUNDLE_DIR}/${BUNDLE}.tar*
cp src/t2gp_social_ver.erl ${BUILD_DIR}/lib
cd ${BUILD_DIR}
ls -al ./
find -L ./lib -regex ".*\/\(plugins\|ebin\|priv\)\/.*" -print0 | sed 's/$/.\/lib\/t2gp_social_ver.erl/' > files.txt
tar -cf ${BUNDLE_DIR}/${BUNDLE}.tar --null -T ./files.txt
gzip ${BUNDLE_DIR}/${BUNDLE}.tar
echo "Done."
