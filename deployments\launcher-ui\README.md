# svelte app

This is a project for Social UI created with [<PERSON>velte](https://svelte.dev).

## Get started

Install the dependencies...

```bash
cd t2gp-social-frontend
npm ci
npm run lint:patch
```

we have to patch our eslint plugin to handle svelte ts. for more info, please check the eslint section

start [Rollup](https://rollupjs.org):

```bash
npm run dev
```

Navigate to [localhost:5000](http://localhost:5000). You should see your app running. Edit a component file in `src`, save it, and reload the page to see your changes.

By default, the server will only respond to requests from localhost. To allow connections from other computers, edit the `sirv` commands in package.json to include the option `--host 0.0.0.0`.

If you're using [Visual Studio Code](https://code.visualstudio.com/) we recommend installing the official extension [Svelte for VS Code](https://marketplace.visualstudio.com/items?itemName=svelte.svelte-vscode). If you are using other editors you may need to install a plugin in order to get syntax highlighting and intellisense.

## Building and running in production mode

To create an optimised version of the app:

```bash
npm run build
```

## Single-page app mode

By default, sirv will only respond to requests that match files in `public`. This is to maximise compatibility with static fileservers, allowing you to deploy your app anywhere.

If you're building a single-page app (SPA) with multiple routes, sirv needs to be able to respond to requests for _any_ path. You can make it so by editing the `"start"` command in package.json:

```js
"start": "sirv public --single"
```

## Using TypeScript

This project is setup with typescript enabled.
Two configuration files being created: `tsconfig.json` and `tsconfig.declaration.json`.
declaration configuration file created for generated typescript declaration file `.d.ts` in the dist folder.

## Unit Test

This project is using jest for test svelte component with `svelte-jester`.

To see the test results:

```bash
npm test
```

to see the test coverage

```bash
npm run test:coverage
```

## Eslint

For svelte, it is using [eslint-plugin-svelte3](https://github.com/sveltejs/eslint-plugin-svelte3) to validate svelte file. However, we added a patch to allow this plugin support typescript since svelte offically supports typescripts now.

Once the [PR 62](https://github.com/sveltejs/eslint-plugin-svelte3/pull/62) is merged, we can remove this patch.

## Smaple Integration for React

SocialPanel.tsx file:

```
...

export const SocialPanel = ({ ...props }) => {
  const socialPanel = useRef(null);
  const { t } = useTranslation();

  useEffect(() => {
    if (socialPanel.current) {
      // eslint-disable-next-line no-new
      new FriendsList({
        target: socialPanel.current,
        props: {
          theme,
          locale: { t },
          service: new SocialService('authToken', 'userId'),
        },
      });
    }
  }, [t]);

  return <div ref={socialPanel} css={cssSocialPanel} {...props} />;
};
```

## Theming the Social Compoonents

Theming is done with theme object passed in for the entry component, a explicitly way of setting the social components theme.
