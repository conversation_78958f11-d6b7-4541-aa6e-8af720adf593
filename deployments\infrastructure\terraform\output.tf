output "role_arn" {
  value = module.aws_role.iam_role_arn
}

output "redis_primary_endpoint" {
  value = aws_elasticache_replication_group.social_redis_repgrp.primary_endpoint_address
}

output "redis_configuration_endpoint" {
  value = aws_elasticache_replication_group.social_redis_repgrp.configuration_endpoint_address
}

output "redis_reader_endpoint" {
  value = aws_elasticache_replication_group.social_redis_repgrp.reader_endpoint_address
}

output "waf_acl_arn" {
  value = aws_wafv2_web_acl.social_web_acl.arn
}

output "social_profile_sync_arn" {
  value = aws_sqs_queue.social_profile_sync.arn
}

output "social_profile_sync_dlq_arn" {
  value = aws_sqs_queue.social_profile_sync_dlq.arn
}