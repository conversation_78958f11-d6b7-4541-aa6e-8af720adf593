import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

const groupMaxMembersDefault: number = 2;
const groupMaxMembersNormal: number = 5;
const groupMaxMembersMin: number = 2;
const groupMaxMembersMax: number = 100;

// timeout in ms for each test.
let testTimeout = 65000;

async function setup(memberNum: number) {
  // define user label
  let userLabels: string[] = [];
  for (let i = 1; i < memberNum; i++) {
    userLabels.push("user" + i.toString());
  }

  // all labels
  let allLabels: string[] = ["leader"].concat(userLabels);

  let usersTwok = new TwokAccounts(allLabels.length, allLabels);
  await usersTwok.loginAll({});

  // create 'auto-approve' group with maxMembers value
  const r = await socialApi.createGroupV1(
    usersTwok.acct["leader"],
    {
      maxMembers: memberNum,
      joinRequestAction: "auto-approve",
      canCrossPlay: true
    }
  );
  socialApi.testStatus(StatusCodes.CREATED, r);
  let groupId = socialApi.getGroupId(r);

  return { usersTwok, userLabels, allLabels, groupId }
}

describe(`group capacity[public v1]${_ds}happy cases${_ds}`, () => {
  let s: { usersTwok: TwokAccounts, userLabels: string[], allLabels: string[], groupId: string };

  afterEach(async () => {
    // delete group
    const r = await socialApi.deleteGroup(s.usersTwok.acct["leader"], s.groupId);

    await s.usersTwok.logoutAll({});
  });

  it.each`
    desc                                               | memberNum
    ${"can have default number of members in a group"} | ${groupMaxMembersDefault}
    ${"can have some number of members in a group"}    | ${groupMaxMembersNormal}
    ${"can have min number of members in a group"}     | ${groupMaxMembersMin}
    ${"can have max number of members in a group"}     | ${groupMaxMembersMax}
  `('$desc', async ({ memberNum }) => {
    s = await setup(memberNum);

    let testCase = {
      description: `create the group with the maxMembers = (${memberNum}), ${memberNum - 1} users join the group`,
      expected: `there are ${memberNum} members in the group`
    };

    // users request to join the 'auto-approve' group, the users will join the group as group member
    for (let label of s.userLabels) {
      const r = await socialApi.requestToJoinV1(
        s.usersTwok.acct[label],
        s.groupId,
        { canCrossPlay: true }
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    const actualGroupInfo = await socialApi.getGroupInfo(s.usersTwok.acct["leader"], s.groupId);

    // create an expected array of members object
    let memberIds = [];
    for (let userLabel of s.allLabels) {
      memberIds.push({ userid: s.usersTwok.acct[userLabel].publicId });
    }

    // verify the length of group members is just the maxMembers value
    expect(actualGroupInfo.body.members.length).toBe(memberNum);

    // verify the members list in group contains the expected list of member ids
    const expectedMember = {
      status: StatusCodes.OK,
      body: {
        members: memberIds
      },
    };

    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedMember)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the actual member list in group does not match the input user IDs"
        }
      }
    );
  }, testTimeout);
});
