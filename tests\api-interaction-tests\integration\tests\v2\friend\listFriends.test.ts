import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
const message: string = 'T2GP Social Automated Testing';

beforeEach(async () => {
  usersTwok = new TwokAccounts(2, ["inviter", "invitee"]);
  await usersTwok.loginAll({});
});

afterEach(async () => {
  await usersTwok.logoutAll({});
});

describe(`friend list[public v2]${_ds}`, () => {
  describe(`happy cases${_ds}`, () => {
    afterEach(async () => {
      await socialApi.deleteFriend(
        usersTwok.acct["inviter"],
        usersTwok.acct["invitee"].publicId
      );
    });

    it(`can show 'pending' friendship status[happy]`, async () => {
      let testCase = {
        description: "send user a friend invitation",
        expected: "'pending' friendship status is present in the friend list"
      };

      // inviter sends invitee friend invitation
      const resp: request.Response = await socialApi.makeFriends(
        usersTwok.acct["inviter"],
        usersTwok.acct["invitee"].publicId,
        { message: message }
      );
      socialApi.testStatus(StatusCodes.OK, resp);

      // get inviter's friend list
      let actualFriendInfo = await socialApi.getFriends(usersTwok.acct["inviter"], {});

      // verify friendship status is "pending"
      let expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          items: [
            {
              friendid: usersTwok.acct["invitee"].publicId,
              invitee: usersTwok.acct["invitee"].publicId,
              status: 'pending',
              userid: usersTwok.acct["inviter"].publicId,
            },
          ],
        },
      };
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
        testCase,
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "inviter's friend list does not have pending friendship status for invitee"
          }
        }
      );

      // get invitee's friend list
      actualFriendInfo = await socialApi.getFriends(usersTwok.acct["invitee"], {});

      // verify friendship status is "pending"
      expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          items: [
            {
              friendid: usersTwok.acct["inviter"].publicId,
              invitee: usersTwok.acct["invitee"].publicId,
              status: 'pending',
              userid: usersTwok.acct["invitee"].publicId,
            },
          ],
        },
      };
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
        testCase,
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "invitee's friend list does not have pending friendship status for inviter"
          }
        }
      );
    });

    it(`can show single friend in 'pending' status[happy]`, async () => {
      let testCase = {
        description: "send friend invitation to a user; user gets single friend",
        expected: "'pending' friendship status is present in the single friend"
      };

      // inviter sends invitee friend invitation
      let r = await socialApi.makeFriends(
        usersTwok.acct["inviter"],
        usersTwok.acct["invitee"].publicId,
        { message: message }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // verify the friend info of invitee
      let actualFriendInfo = await socialApi.getFriend(usersTwok.acct["invitee"], usersTwok.acct["inviter"].publicId);

      let expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          friendid: usersTwok.acct["inviter"].publicId,
          invitee: usersTwok.acct["invitee"].publicId,
          status: 'pending',
          userid: usersTwok.acct["invitee"].publicId,
        },
      };
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
        testCase,
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "expected friend did not appear in the inviter friend"
          }
        }
      );

      // verify the friend info of inviter
      actualFriendInfo = await socialApi.getFriend(usersTwok.acct["inviter"], usersTwok.acct["invitee"].publicId);

      expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          friendid: usersTwok.acct["invitee"].publicId,
          invitee: usersTwok.acct["invitee"].publicId,
          status: 'pending',
          userid: usersTwok.acct["inviter"].publicId,
        },
      };
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
        testCase,
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "expected friend did not appear in the inviter friend"
          }
        }
      );
    });

    it(`can show single friend in 'friend' status[happy]`, async () => {
      let testCase = {
        description: "send friend invitation to a user; the user accepts the invitation; user gets single friend",
        expected: "'friend' friendship status is present in the single friend"
      };

      // inviter sends invitee friend invitation
      let r = await socialApi.makeFriends(
        usersTwok.acct["inviter"],
        usersTwok.acct["invitee"].publicId,
        { message: message }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // invitee accepts friend invitation from inviter
      r = await socialApi.makeFriends(
        usersTwok.acct["invitee"],
        usersTwok.acct["inviter"].publicId,
        { message: message }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // verify the friend info of invitee
      let actualFriendInfo = await socialApi.getFriend(usersTwok.acct["invitee"], usersTwok.acct["inviter"].publicId);

      let expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          friendid: usersTwok.acct["inviter"].publicId,
          invitee: usersTwok.acct["invitee"].publicId,
          status: 'friend',
          userid: usersTwok.acct["invitee"].publicId,
        },
      };
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
        testCase,
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "expected friend did not appear in the invitee friend"
          }
        }
      );

      // verify the friend info of inviter
      actualFriendInfo = await socialApi.getFriend(usersTwok.acct["inviter"], usersTwok.acct["invitee"].publicId);

      expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          friendid: usersTwok.acct["invitee"].publicId,
          invitee: usersTwok.acct["invitee"].publicId,
          status: 'friend',
          userid: usersTwok.acct["inviter"].publicId,
        },
      };
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
        testCase,
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "expected friend did not appear in the inviter friend"
          }
        }
      );
    });
  });
});