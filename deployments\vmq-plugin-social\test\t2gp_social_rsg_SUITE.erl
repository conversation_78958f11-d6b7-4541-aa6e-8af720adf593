-module(t2gp_social_rsg_SUITE).


-compile(nowarn_export_all).
-compile(export_all).

-include_lib("eunit/include/eunit.hrl").


%% before running rebar3, run
%% export $(cat .env | grep -v '#' | xargs)
init_per_suite(Config) ->
    cover:start(),
    Config.

end_per_suite(_Config) ->
    ok.

init_per_testcase(_Case, Config) ->
    t2gp_social_test:configure(),
    ssl:start(),
    application:set_env(t2gp_social, rs_config_url, "s3://t2gp-pd-store-api/rs_auth_env/config.gz"),
    t2gp_social_rsg:start_link(),
    Config.

end_per_testcase(_Case, _Config) ->
    t2gp_social_rsg:stop(),
    ok.

all() -> [
    test_gen_server,
    test_validate_jwt,
    test_update_config,
    test_invalid_call,
    test_terminate,
    test_code_change,
    test_handle_cast,
    test_handle_info
].

test_gen_server(_) ->
    {error, {already_started, _}} = t2gp_social_rsg:start_link(),
    ok = t2gp_social_rsg:stop(),

    application:unset_env(t2gp_social, rs_config_url),
    {ok, _} = t2gp_social_rsg:start_link(),
    {state, undefined, []} = sys:get_state(t2gp_social_rsg),
    ok = t2gp_social_rsg:stop(),
    
    {ok, _} = t2gp_social_rsg:start_link(),
    ok = t2gp_social_rsg:stop(),
    application:set_env(t2gp_social, rs_config_url, "bad url"),
    
    {ok, _} = t2gp_social_rsg:start_link(),
    {state, undefined, []} = sys:get_state(t2gp_social_rsg),
    
    % check bad validation
    AlgNoneJWT = <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUyMzAyMTYyLCJydGkiOiJiYzNjNzQ3YzAxM2M0YTYxOWE4MzgxYzc4ODljMmZmYSIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiOGEzZWU4MDVmMWNkNDI3YTlmNzQ1MTQyNmMyZTkzZDEiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6MTY1MjI5ODU2MiwiaWF0IjoxNjUyMjk0OTYyLCJqdGkiOiIxNzQxNzVjY2FhNjg0MmY3ODQ0OTM5N2EzOTQ3MzY1ZCIsImxhdCI6MzkuMDQ2OX0.">>,
    {error, invalid_token} = gen_server:call(t2gp_social_rsg, {validate_jwt, AlgNoneJWT}),
    ok = t2gp_social_rsg:stop(),

    application:set_env(t2gp_social, rs_config_url, "s3://t2gp-pd-store-api/rs_auth_env/config.gz"),
    {ok, _} = t2gp_social_rsg:start_link(),
    {error, invalid_signature} = gen_server:call(t2gp_social_rsg, {validate_jwt, AlgNoneJWT}),
    ok = gen_server:stop(t2gp_social_rsg),

    {ok, _} = t2gp_social_rsg:start_link(),
    {state,"s3://t2gp-pd-store-api/rs_auth_env/config.gz",_} = sys:get_state(t2gp_social_rsg),
    ok = t2gp_social_rsg:stop(),

    %% restart 
    t2gp_social_rsg:start_link(),
    ok.

test_validate_jwt(_) ->    
    JWTExpired =  <<"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">>,
    % check w/ no jwks
    {error, expired} = gen_server:call(t2gp_social_rsg, {validate_jwt, JWTExpired}),    

    % invalid alg
    AlgNoneJWT = <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUyMzAyMTYyLCJydGkiOiJiYzNjNzQ3YzAxM2M0YTYxOWE4MzgxYzc4ODljMmZmYSIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiOGEzZWU4MDVmMWNkNDI3YTlmNzQ1MTQyNmMyZTkzZDEiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6MTY1MjI5ODU2MiwiaWF0IjoxNjUyMjk0OTYyLCJqdGkiOiIxNzQxNzVjY2FhNjg0MmY3ODQ0OTM5N2EzOTQ3MzY1ZCIsImxhdCI6MzkuMDQ2OX0.">>,
    {error, invalid_signature} = gen_server:call(t2gp_social_rsg, {validate_jwt, AlgNoneJWT}),

    % invalid jwt
    BadJWT = <<"bad jwt">>,
    {error, invalid_token} = gen_server:call(t2gp_social_rsg, {validate_jwt, BadJWT}),

    ok.

test_update_config(_) ->
    ok = gen_server:call(t2gp_social_rsg, {update_config}),
    ok = gen_server:call(t2gp_social_rsg, {update_config, ""}),
    ok = gen_server:call(t2gp_social_rsg, {update_config, "s3://t2gp-pd-store-api/rs_auth_env/config.gz"}),
    % make sure it didn't crash
    {error, {already_started, _}} = t2gp_social_rsg:start_link(),
    ok.

test_invalid_call(_) ->
    {error, invalid_msg} = gen_server:call(t2gp_social_rsg, {invalid_call}),
    ok.

test_terminate(_) ->
    ok = t2gp_social_rsg:terminate(shutdown, #{}),
    ok.

test_code_change(_) ->
    {ok, _} = t2gp_social_rsg:code_change(old_version, new_version, []),
    ok.


test_handle_cast(_) ->
    {noreply, _} = t2gp_social_rsg:handle_cast(cast, []),
    ok.

test_handle_info(_) ->
    {noreply, _} = t2gp_social_rsg:handle_info(info, []),
    ok.
