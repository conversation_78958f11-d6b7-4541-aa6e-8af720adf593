#!/bin/bash

mkdir -p /data/redis0 /data/redis1 /data/redis2
/usr/local/bin/redis-server /usr/local/etc/redis/redis0.conf &
/usr/local/bin/redis-server /usr/local/etc/redis/redis1.conf &
/usr/local/bin/redis-server /usr/local/etc/redis/redis2.conf &
sleep 1
/usr/local/bin/redis-cli --cluster create 192.168.123.4:6379 192.168.123.4:6380 192.168.123.4:6381 --cluster-replicas 0 --cluster-yes
wait $(jobs -p)
