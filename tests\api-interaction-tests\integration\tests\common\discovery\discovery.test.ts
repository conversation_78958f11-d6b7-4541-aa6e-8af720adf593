import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

describe('', () => {
  it('discovery[public happy v1 v2]', async () => {
    let testCase = {
      description: "get endpoints",
      expected: "find out endpoints to use"
    }   

    const actualResp = await socialApi.getDiscovery();

    const expectedResp = {
      status: StatusCodes.OK,
      body: expect.arrayContaining([
        expect.objectContaining({
          urls: expect.arrayContaining([
            expect.objectContaining({
              "type": "http",
              "url": config.socialService.currAccessLevel.currEnv.currVer.api
            }),
            expect.objectContaining({
              "type": "mqtt",
              "url": config.socialService.currAccessLevel.currEnv.currVer.mqtt
            }),
          ])
        })
      ]),
    };
    socialApi.expectMore(
      () => {expect(actualResp).toMatchObject(expectedResp)}, 
      testCase, 
      {
        resp: actualResp,
        additionalInfo: {
          "fail reason" : "unexpected endpoints"
        }
      }
    );   
  });
});