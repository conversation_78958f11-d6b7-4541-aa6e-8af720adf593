package errs

import "net/http"

// DO NOT MOVE THE ORDERING

type SocialError uint32

// please also add in '#/components/schemas/errorCode'
const (
	// general errors
	EUnknown SocialError = iota + 100000
	EAuthorizationFailed
	EGeneric
	EPanic
	EJsonParse
	ERequestEmpty
	EResponseEmpty
	EInvalidBearerToken
	EInvalidJWT
	EUlidParse
	EBase64Decode
	EInvalidUserID
	EInvalidLogin
	EFullAccountRequired
	EInvalidAccountType
	EInvalidAuthHeader
	EInvalidRequest
	EStringToIntParse
	ENotImplemented
	ENotYetValid
	EDiscoveryNotFound
	EInvalidTenant
	EInvalidProductID
	EOapiValidationFailed
	EDiscoveryInvalidUrl
	ENoValidParameters
	ETypeConversionFailed
)

const (
	// VMQ errors
	EVmqGeneric SocialError = iota + 101000
	EVmqAlertError
	EVmqGetSubscriptionError
	EVmqDeleteSubscriptionError
)

const (
	// profile errors
	EProfileGeneric SocialError = iota + 102000
	EProfileFetchFailed
	ERecentlyPlayedUserIdInvalid
	ESubjectNotFound
	ERecentlyPlayedCannotAddSelf
)

const (
	// friends errors
	EFriendsGeneric SocialError = iota + 103000
	EFriendsNotFullAccount
	EFriendsMaxCountReached
	EFriendsCannotFriendSelf
	EFriendsCannotFriendFromBlocklist
	EFriendsAlreadyExists
	EFriendsCannotGetCount
	EFriendsInvalidFriendID
	EFriendsNotFriend
	EFriendsReportingFailed
	EFriendsInvalidSearchQuery
	EFriendsInvalidStatus
	EFriendsSearchFailed
	EFriendsNotFound
	EFriendsClearFailed
	EFriendsCannotMakeFriend
	EFriendsCannotReportYourself
)

const (
	// groups errors
	EGroupsGeneric SocialError = iota + 104000
	EGroupsInviteExpired
	EGroupsMaxReached
	EGroupsNotFound
	EGroupsMemberNotInGroup
	EGroupsAlreadyInGroup
	EGroupsApproverIDRequired
	EGroupsMembershipNotOwned
	EGroupsNotAllowedToKick
	EGroupsInviteNotFound
	EGroupsNotAllowed
	EGroupsNotLeader
	EGroupsInvalidRole
	EGroupsMissingLeader
	EGroupsInvalidJoinRequestAction
	EGroupsInvalidMaxMembers
	EGroupsPasswordRequired
	EGroupsInvalidProductID
	EGroupsFull
	EGroupsInvalidGroupID
	EGroupsInvalidMemberID
	EGroupsOnlyLeaderCanSendInvites
	EGroupsCrossplayValidationFailed
	EGroupsDoesNotAcceptJoinRequests
	EGroupsPasswordNotSupported
	EGroupsPasswordIncorrect
	EGroupsMembershipNotFound
	EGroupsInvalidMembershipStatus
	EGroupsInvalidApproverID
	EGroupsDbPutFailedUDeprecated
	EGroupsDbGetFailedDeprecated
	EGroupsLeaveOrKickFailed
	EGroupsModifyMembershipFailed
	EGroupsGroupMemberNil
	EGroupsGroupMemberModifyFailed
	EGroupsFirstPartyInviteDoesNotExist
	EGroupsFirstPartyInviteBlocklist
	EGroupsDeleteMembershipFailed
	EGroupsControlMessageTooLong
	EGroupsInvalidIsFirstParty
	EGroupsTooManyUsersForGroup
	EGroupsMetaTooLong
	EGroupsMergedMetaTooLong
	EGroupsInvalidMeta
	EGroupsOnlyMetaUpdateForMembers
	EGroupsSetActiveGroupFailed
	EGroupsMemberUpdateForbidden
	EGroupsMemberUpdateFailed
	EGroupsMemberMetaUpdateTooOld
	EGroupsFirstPartyOnlineServiceTypeRequired
	EGroupsInvalidFirstPartyAuthCreds
	EGroupsFirstPartyRateLimitReached
)

const (
	// chat errors
	EChatGeneric SocialError = iota + 105000
	EChatMessageUnacceptable
	EChatGroupNotFound
	EChatInvalidRoomID
	EChatInvalidMessage
	EChatMessagePublishFailed
	EChatMessageBodyRequired
)

const (
	// presence error
	EPresenceGeneric SocialError = iota + 106000
	EPresenceRequired
	EPresenceInvalidPriority
	EPresenceInvalidKeepAlive
	EPresenceInvalidGameData
	EPresenceInvalidStatus
	EPresenceClientIDRequired
	EPresenceNotFound
	EPresenceGetUserPresenceFailed
	EPresenceSaveAndBroadcastFailed
	EPresenceInvalidParams
	EPresenceClearFailed
	EPresenceMetaTooLong
)

const (
	// blocklist errors
	EBlocklistGeneric SocialError = iota + 107000
	EBlocklistFetchFailed
	EBlocklistInvalidUserID
	EBlocklistNotFound
	EBlocklistExceedMaxUserBlockedCount
	EBlocklistSyncProfileFailed
	EBlocklistExcessiveIdCount
)

const (
	// identity errors
	// 2K DNA errors
	EDnaGeneric SocialError = iota + 150000
	EDnaInvalidAccountType
	EDnaGetFriendsWithStatusFailed
	EDnaSearchAccountByUserIdFailed
	EDnaSearchAccountsFailed
	EDnaSearchFriendsByEmailFailed
	EDnaGetLinkedAccountDataFailed
	EDnaRefreshTokenFailed
	EDnaLoginFailed
	EDnaLogoutFailed
	EDnaUserNotFound
	// PD errors
	EPdGeneric SocialError = iota + 151000
	EPdInvalidAccountType
	EPdGetFriendsWithStatusFailed
	EPdSearchAccountByUserIdFailed
	EPdSearchAccountsFailed
	EPdSearchFriendsByEmailFailed
	EPdGetLinkedAccountDataFailed
	EPdRefreshTokenFailed
	EPdLoginFailed
	EPdLogoutFailed
	EPdUserNotFound
)

const (
	// VoipError
	EVoipGeneric SocialError = iota + 160000
	EVoipCreateRequestFailed
	EVoipRequestFailed
)

const (
	EHttp SocialError = iota + 200000
)

const (
	// oapi-codegen errors
	EOapiUnescapedCookieParam SocialError = iota + 250000
	EOapiUnmarshalingParam
	EOapiRequireParam
	EOApiRequireHeader
	EOapiInvalidParamFormat
	EOapiTooManyValuesForParam
)

const (
	// dynamodb errors
	EDynamodbGeneric SocialError = iota + 300000
	EDynamodbConflict
	EDynamodbMarshalFailed
	EDynamodbUnmarshalFailed
	EDynamodbReadFailed
	EDynamodbPutFailed
	EDynamodbDeleteFailed
)

const (
	// redis errors
	ERedisGeneric SocialError = iota + 400000
	ERedisInvalidKey
	ERedisCacheGetFailed
	ERedisCacheSetFailed
	ERedisCacheDeleteFailed
	ERedisNilCache
	ERedisMSetFailed
	ERedisNilIndex
	ERedisExpireFailed
	ERedisZRevRangeFailed
	ERedisKeysFailed
	ERedisSyncDataFailed
	ERedisUnmarshalFailed
	ERedisZRangeByScoreFailed
	ERedisTransactionFailed
	ERedisZAddFailed
	ERedisZRemFailed
	ERedisZUnionStoreFailed
	ERedisZScoreFailed
	ERedisObjectMissing
)

const (
	// firehose errors
	EFirehoseGeneric SocialError = iota + 500000
	EFirehoseConflict
	EFirehoseDeleteFailed
	EFirehoseMarshalFailed
	EFirehoseReadFailed
	EFirehosePutFailed
	EFirehoseUnmarshalFailed
)

const (
	// kinesis errors
	EKinesisGeneric SocialError = iota + 550000
	EKinesisConflict
	EKinesisDeleteFailed
	EKinesisMarshalFailed
	EKinesisReadFailed
	EKinesisPutFailed
	EKinesisUnmarshalFailed
)

const (
	// endorse errors
	EEndorseGeneric SocialError = iota + 600000
	EEndorseCannotResetDefault
	EEndorseCannotEndorseSelf
)

const (
	// s3 errors
	ES3Generic SocialError = iota + 700000
	ES3BadRequest
	ES3NotFound
	ES3PutFailed
	ES3GetFailed
	ES3DeleteFailed
)

const (
	// abuse report
	ENoSNSTopicArnFound SocialError = iota + 800000
	EReportMessageTooLarge
)

const (
	// rate limit errors
	// NOTE: Rate limit errors are emitted from AWS WAF.
	// Codes here listed are for consistancy purposes but aren't used in code

	EWAFRateLimited SocialError = iota + 900000
)

type ErrorInfo struct {
	ID      string
	Message string
}

var ErrorMap = map[SocialError]ErrorInfo{
	// general errors
	EUnknown:              {ID: "EUnknown", Message: "unknown error"},
	EGeneric:              {ID: "EGeneric", Message: "generic error"},
	EPanic:                {ID: "EPanic", Message: "panic"},
	EJsonParse:            {ID: "EJsonParse", Message: "json parse error"},
	ERequestEmpty:         {ID: "ERequestEmpty", Message: "request empty"},
	EResponseEmpty:        {ID: "ERequestEmpty", Message: "response empty"},
	EInvalidBearerToken:   {ID: "EInvalidBearerToken", Message: "invalid bearer token"},
	EInvalidJWT:           {ID: "EInvalidJWT", Message: "invalid token"},
	EUlidParse:            {ID: "EUlidParse", Message: "ulid parse error"},
	EBase64Decode:         {ID: "EBase64Decode", Message: "base64 decode error"},
	EInvalidUserID:        {ID: "EInvalidUserID", Message: "invalid userid error"},
	EInvalidLogin:         {ID: "EInvalidLogin", Message: "invalid login"},
	EFullAccountRequired:  {ID: "EFullAccountRequired", Message: "full account required"},
	EInvalidAccountType:   {ID: "EInvalidAccountType", Message: "invalid account type"},
	EInvalidAuthHeader:    {ID: "EInvalidAuthHeaders", Message: "invalid auth header"},
	EInvalidRequest:       {ID: "EInvalidRequest", Message: "invalid request body"},
	EStringToIntParse:     {ID: "EStringToIntParse", Message: "string to int parse failed"},
	ENotImplemented:       {ID: "ENotImplemented", Message: "not implemented"},
	ENotYetValid:          {ID: "ENotYetValid", Message: "token is not yet valid"},
	EAuthorizationFailed:  {ID: "EAuthorizationFailed", Message: "authorization failed"},
	EDiscoveryNotFound:    {ID: "EDiscoveryNotFound", Message: "discovery not found"},
	EInvalidTenant:        {ID: "EInvalidTenant", Message: "tenant is invalid"},
	EInvalidProductID:     {ID: "EInvalidProductID", Message: "invalid product id"},
	EOapiValidationFailed: {ID: "EOapiValidationFailed", Message: "one or more of the fields in the request body failed to meet the constraints defined in the api docs.  please consult the docs and ensure all fields match the requirements listed. "},
	EDiscoveryInvalidUrl:  {ID: "EDiscoveryInvalidUrl", Message: "invalid discovery url"},
	ENoValidParameters:    {ID: "ENoValidParameters", Message: "no valid parameters in request body"},
	ETypeConversionFailed: {ID: "ETypeConversionFailed", Message: "type conversion failed"},

	EVmqGeneric:                 {ID: "EVmqGeneric", Message: "vmq error"},
	EVmqAlertError:              {ID: "EVmqAlertError", Message: "vmq alert error"},
	EVmqGetSubscriptionError:    {ID: "EVmqGetSubscriptionError", Message: "vmq get subscriptions failed"},
	EVmqDeleteSubscriptionError: {ID: "EVmqDeleteSubscriptionError", Message: "vmq delete subsciption failed"},

	EProfileGeneric:              {ID: "EProfileGeneric", Message: "profile error"},
	EProfileFetchFailed:          {ID: "EProfileFetchFailed", Message: "profile fetch failed"},
	ERecentlyPlayedUserIdInvalid: {ID: "RecentlyPlayedUserIdInvalid", Message: "invalid user id in recently played list"},
	ESubjectNotFound:             {ID: "ESubjectNotFound", Message: "subject not found"},
	ERecentlyPlayedCannotAddSelf: {ID: "ERecentlyPlayedCannotAddSelf", Message: "cannot add yourself to recently played list"},

	EFriendsGeneric:                   {ID: "EFriendsGeneric", Message: "friends error"},
	EFriendsNotFullAccount:            {ID: "EFriendsNotFullAccount", Message: "friend id not full account"},
	EFriendsMaxCountReached:           {ID: "EFriendsMaxCountReached", Message: "max friend count reached"},
	EFriendsCannotFriendSelf:          {ID: "EFriendsCannotFriendSelf", Message: "cannot be friends with yourself"},
	EFriendsCannotFriendFromBlocklist: {ID: "EFriendsCannotFriendFromBlocklist", Message: "cannot friend someone in your blocklist"},
	EFriendsAlreadyExists:             {ID: "EFriendsAlreadyExists", Message: "friend already exists"},
	EFriendsCannotGetCount:            {ID: "EFriendsCannotGetCount", Message: "cannot get friend count"},
	EFriendsInvalidFriendID:           {ID: "EFriendsInvalidFriendID", Message: "invalid friend id"},
	EFriendsNotFriend:                 {ID: "EFriendsNotFriend", Message: "not a friend"},
	EFriendsReportingFailed:           {ID: "EFriendReportingFailed", Message: "reporting failed"},
	EFriendsInvalidSearchQuery:        {ID: "EFriendInvalidSearchQuery", Message: "invalid search query"},
	EFriendsInvalidStatus:             {ID: "EFriendsInvalidStatus", Message: "invalid status"},
	EFriendsSearchFailed:              {ID: "EFriendsSearchFailed", Message: "friend search failed"},
	EFriendsNotFound:                  {ID: "EFriendsNotFound", Message: "friend not found"},
	EFriendsClearFailed:               {ID: "EFriendsClearFailed", Message: "friend clear failed"},
	EFriendsCannotMakeFriend:          {ID: "EFriendsCannotMakeFriend", Message: "friends cannot make friend"},
	EFriendsCannotReportYourself:      {ID: "EFriendsCannotReportYourself", Message: "you cannot report yourself"},

	EGroupsGeneric:                             {ID: "EGroupsGeneric", Message: "groups error"},
	EGroupsInviteExpired:                       {ID: "EGroupsInviteExpired", Message: "invite has expired"},
	EGroupsMaxReached:                          {ID: "EGroupsMaxReached", Message: "user cannot join more groups"},
	EGroupsNotFound:                            {ID: "EGroupsNotFound", Message: "group not found"},
	EGroupsMemberNotInGroup:                    {ID: "EGroupsMemberNotInGroup", Message: "member not in group"},
	EGroupsMemberUpdateForbidden:               {ID: "EGroupsMemberUpdateForbidden", Message: "member can only update their own meta"},
	EGroupsMemberUpdateFailed:                  {ID: "EGroupsMemberUpdateFailed", Message: "fail to update group member"},
	EGroupsMemberMetaUpdateTooOld:              {ID: "EGroupsMemberMetaUpdateTooOld", Message: "the requested update for group member's metadata is older than the existing one"},
	EGroupsAlreadyInGroup:                      {ID: "EGroupsAlreadyInGroup", Message: "member already in group"},
	EGroupsApproverIDRequired:                  {ID: "EGroupsApproverIDRequired", Message: "approver id required"},
	EGroupsMembershipNotOwned:                  {ID: "EGroupsMembershipNotOwned", Message: "membership not owned by user"},
	EGroupsNotAllowedToKick:                    {ID: "EGroupsNotAllowedToKick", Message: "user not allowed to kick"},
	EGroupsInviteNotFound:                      {ID: "EGroupsInviteNotFound", Message: "invite not found"},
	EGroupsNotAllowed:                          {ID: "EGroupsNotAllowed", Message: "not allowed"},
	EGroupsNotLeader:                           {ID: "EGroupsNotLeader", Message: "group member is not leader"},
	EGroupsInvalidRole:                         {ID: "EGroupsInvalidRole", Message: "invalid group role"},
	EGroupsMissingLeader:                       {ID: "EGroupsMissingLeader", Message: "group is missing a leader"},
	EGroupsInvalidJoinRequestAction:            {ID: "EGroupsInvalidJoinRequestAction", Message: "invalid join request action"},
	EGroupsInvalidMaxMembers:                   {ID: "EGroupsInvalidMaxMembers", Message: "invalid max members"},
	EGroupsPasswordRequired:                    {ID: "EGroupsPasswordRequired", Message: "password required"},
	EGroupsInvalidProductID:                    {ID: "EGroupsInvalidProductID", Message: "invalid product id"},
	EGroupsFull:                                {ID: "EGroupsFull", Message: "group is full"},
	EGroupsInvalidGroupID:                      {ID: "EGroupsInvalidGroupID", Message: "invalid group id"},
	EGroupsInvalidMemberID:                     {ID: "EGroupsInvalidMemberID", Message: "invalid member id"},
	EGroupsOnlyLeaderCanSendInvites:            {ID: "EGroupsOnlyLeaderCanSendInvites", Message: "only group leader can send invites"},
	EGroupsCrossplayValidationFailed:           {ID: "EGroupsCrossplayValidationFailed", Message: "crossplay validation failed"},
	EGroupsDoesNotAcceptJoinRequests:           {ID: "EGroupsDoesNotAcceptJoinRequests", Message: "group does not accept join requests"},
	EGroupsPasswordNotSupported:                {ID: "EGroupsPasswordNotSupported", Message: "password not supported"},
	EGroupsPasswordIncorrect:                   {ID: "EGroupsPasswordIncorrect", Message: "password is incorrect"},
	EGroupsMembershipNotFound:                  {ID: "EGroupsMembershipNotFound", Message: "membership not found"},
	EGroupsInvalidMembershipStatus:             {ID: "EGroupsInvalidStatus", Message: "invalid membership status"},
	EGroupsInvalidApproverID:                   {ID: "EGroupsInvalidApproverID", Message: "invalid approver id"},
	EGroupsDbPutFailedUDeprecated:              {ID: "EGroupsDbPutFailedDeprecated", Message: "DEPRECATED:failed to put group into the db"},
	EGroupsDbGetFailedDeprecated:               {ID: "EGroupsDbGetFailedDeprecated", Message: "DEPRECATED:failed to get group from db"},
	EGroupsLeaveOrKickFailed:                   {ID: "EGroupsLeaveOrKickFailed", Message: "leave or kick request failed"},
	EGroupsModifyMembershipFailed:              {ID: "EGroupsModifyMembershipFailed", Message: "modify membership request failed"},
	EGroupsGroupMemberNil:                      {ID: "EGroupsGroupMemberNil", Message: "group member is nil"},
	EGroupsGroupMemberModifyFailed:             {ID: "EGroupsGroupMemberModifyFailed", Message: "group member modify failed"},
	EGroupsFirstPartyInviteDoesNotExist:        {ID: "EGroupsFirstPartyInviteDoesNotExist", Message: "first party invite does not exist"},
	EGroupsFirstPartyInviteBlocklist:           {ID: "EGroupsFirstPartyInviteBlocklist", Message: "first party invite failed due to blocklist"},
	EGroupsDeleteMembershipFailed:              {ID: "EGroupsDeleteMembershipFailed", Message: "failed to delete membership"},
	EGroupsControlMessageTooLong:               {ID: "EGroupsControlMessageTooLong", Message: "control message must be under 5k"},
	EGroupsInvalidIsFirstParty:                 {ID: "EGroupsInvalidIsFirstParty", Message: "invalid first party flag"},
	EGroupsTooManyUsersForGroup:                {ID: "EGroupsTooManyUsersForGroup", Message: "too many users for group"},
	EGroupsMetaTooLong:                         {ID: "EGroupsMetaTooLong", Message: "group metadata too long"},
	EGroupsMergedMetaTooLong:                   {ID: "EGroupsMergedMetaTooLong", Message: "group merged metadata too long"},
	EGroupsInvalidMeta:                         {ID: "EGroupsInvalidMeta", Message: "invalid meta format"},
	EGroupsOnlyMetaUpdateForMembers:            {ID: "EGroupsOnlyMetaUpdateForMembers", Message: "only meta can be updated for member"},
	EGroupsSetActiveGroupFailed:                {ID: "EGroupsSetActiveGroupFailed", Message: "failed to set active group"},
	EGroupsFirstPartyOnlineServiceTypeRequired: {ID: "EGroupsFirstPartyOnlineServiceTypeRequired", Message: "online service type required when isFirstPartyInvite"},
	EGroupsInvalidFirstPartyAuthCreds:          {ID: "EGroupsInvalidFirstPartyAuthCreds", Message: "invalid first party auth creds.  update credentials and try again"},
	EGroupsFirstPartyRateLimitReached:          {ID: "EGroupsFirstPartyRateLimitReached", Message: "first party rate limit reached"},

	EChatGeneric:              {ID: "EChatGeneric", Message: "chat error"},
	EChatMessageUnacceptable:  {ID: "EChatMessageUnacceptable", Message: "chat message unacceptable"},
	EChatGroupNotFound:        {ID: "EChatGroupNotFound", Message: "chat group not found"},
	EChatInvalidRoomID:        {ID: "EChatInvalidRoomID", Message: "chat room id is invalid"},
	EChatInvalidMessage:       {ID: "EChatInvalidMessage", Message: "chat message is invalid"},
	EChatMessagePublishFailed: {ID: "EChatMessagePublishFailed", Message: "failed to publish chat message"},
	EChatMessageBodyRequired:  {ID: "EChatMessageBodyRequired", Message: "chat message body is required"},

	EPresenceGeneric:                {ID: "EPresenceGeneric", Message: "presence error"},
	EPresenceRequired:               {ID: "EPresenceRequired", Message: "presence required"},
	EPresenceInvalidPriority:        {ID: "EPresenceInvalidPriority", Message: "priority out of bounds"},
	EPresenceInvalidKeepAlive:       {ID: "EPresenceInvalidKeepAlive", Message: "keep alive out of bounds"},
	EPresenceInvalidGameData:        {ID: "EPresenceInvalidGameData", Message: "game data out of bounds"},
	EPresenceInvalidStatus:          {ID: "EPresenceInvalidStatus", Message: "invalid status"},
	EPresenceClientIDRequired:       {ID: "EPresenceClientIDRequired", Message: "client id required for mqtt presence"},
	EPresenceNotFound:               {ID: "EPresenceNotFound", Message: "presence not found"},
	EPresenceGetUserPresenceFailed:  {ID: "EPresenceGetPresenceFailed", Message: "failed to get user presence"},
	EPresenceSaveAndBroadcastFailed: {ID: "EPresenceSaveAndBroadcastFailed", Message: "failed to save and broadcast user presence"},
	EPresenceInvalidParams:          {ID: "EPresenceInvalidParams", Message: "invalid params for presence request"},
	EPresenceClearFailed:            {ID: "EPresenceClearFailed", Message: "presence clear failed"},
	EPresenceMetaTooLong:            {ID: "EPresenceMetaTooLong", Message: "presence metadata too long"},

	EBlocklistGeneric:                   {ID: "EBlocklistGeneric", Message: "blocklist error"},
	EBlocklistFetchFailed:               {ID: "EBlocklistFetchFailed", Message: "failed to fetch blocklist"},
	EBlocklistInvalidUserID:             {ID: "EBlocklistInvalidUserID", Message: "invalid userid in blocklist request"},
	EBlocklistNotFound:                  {ID: "EBlocklistNotFound", Message: "user(s) not found"},
	EBlocklistSyncProfileFailed:         {ID: "EBlocklistSyncProfileFailed", Message: "failed to sync user profiles"},
	EBlocklistExceedMaxUserBlockedCount: {ID: "EBlocklistExceedMaxUserBlockedCount", Message: "can't block more user as the user's block list already reached the maximum count"},
	EBlocklistExcessiveIdCount:          {ID: "EBlocklistExcessiveIdCount", Message: "the number of ids included in one request must not be greater than 20"},

	EDnaGeneric:                     {ID: "EDnaGeneric", Message: "dna generic error"},
	EDnaInvalidAccountType:          {ID: "EDnaInvalidAccountType", Message: "invalid dna account type"},
	EDnaSearchAccountByUserIdFailed: {ID: "EDnaSearchAccountByUserIdFailed", Message: "failed to search dna account by user id"},
	EDnaSearchFriendsByEmailFailed:  {ID: "EDnaSearchFriendsByEmailFailed", Message: "failed to search dna friend by email"},
	EDnaSearchAccountsFailed:        {ID: "EDnaSearchAccountsFailed", Message: "failed to search dna accounts"},
	EDnaGetFriendsWithStatusFailed:  {ID: "EDnaGetFriendsWithStatusFailed", Message: "failed to get friends with status"},
	EDnaGetLinkedAccountDataFailed:  {ID: "EDnaGetLinkedAccountDataFailed", Message: "failed to get linked dna account data"},
	EDnaRefreshTokenFailed:          {ID: "EDnaRefreshTokenFailed", Message: "failed to refresh token"},
	EDnaLoginFailed:                 {ID: "EDnaLoginFailed", Message: "failed to login"},
	EDnaLogoutFailed:                {ID: "EDnaLogoutFailed", Message: "failed to logout"},
	EDnaUserNotFound:                {ID: "EDnaUserNotFound", Message: "could not find dna user"},

	EPdGeneric:                     {ID: "EPdGeneric", Message: "pd generic error"},
	EPdInvalidAccountType:          {ID: "EPdInvalidAccountType", Message: "invalid pd account type"},
	EPdSearchAccountByUserIdFailed: {ID: "EPdSearchAccountByUserIdFailed", Message: "failed to search pd account by user id"},
	EPdSearchFriendsByEmailFailed:  {ID: "EPdSearchFriendsByEmailFailed", Message: "failed to search pd friend by email"},
	EPdSearchAccountsFailed:        {ID: "EPdSearchAccountsFailed", Message: "failed to search pd accounts"},
	EPdGetFriendsWithStatusFailed:  {ID: "EPdGetFriendsWithStatusFailed", Message: "failed to get pd friends with status"},
	EPdGetLinkedAccountDataFailed:  {ID: "EPdGetLinkedAccountDataFailed", Message: "failed to get linked pd account data"},
	EPdRefreshTokenFailed:          {ID: "EPdRefreshTokenFailed", Message: "failed to refresh pd token"},
	EPdLoginFailed:                 {ID: "EPdLoginFailed", Message: "failed to login to pd"},
	EPdLogoutFailed:                {ID: "EPdLogoutFailed", Message: "failed to logout from pd"},
	EPdUserNotFound:                {ID: "EPdUserNotFound", Message: "could not find pd user"},

	EVoipGeneric:             {ID: "EVoipGeneric", Message: "voip error"},
	EVoipCreateRequestFailed: {ID: "EVoipCreateRequestFailed", Message: "voip create request failed"},
	EVoipRequestFailed:       {ID: "EVoipRequestFailed", Message: "voip request failed"},

	// http errors
	EHttp + http.StatusBadRequest:                    {ID: "EHttp + http.StatusBadRequest", Message: "bad request"},
	EHttp + http.StatusUnauthorized:                  {ID: "EHttp + http.StatusUnauthorized", Message: "not authorized"},
	EHttp + http.StatusPaymentRequired:               {ID: "EHttp + http.StatusPaymentRequired", Message: "payment required"},
	EHttp + http.StatusForbidden:                     {ID: "EHttp + http.StatusForbidden", Message: "forbidden"},
	EHttp + http.StatusNotFound:                      {ID: "EHttp + http.StatusNotFound", Message: "not found"},
	EHttp + http.StatusMethodNotAllowed:              {ID: "EHttp + http.StatusMethodNotAllowed", Message: "method not allowed"},
	EHttp + http.StatusNotAcceptable:                 {ID: "EHttp + http.StatusNotAcceptable", Message: "not acceptable"},
	EHttp + http.StatusProxyAuthRequired:             {ID: "EHttp + http.StatusProxyAuthRequired", Message: "proxy auth required"},
	EHttp + http.StatusRequestTimeout:                {ID: "EHttp + http.StatusRequestTimeout", Message: "request timeout"},
	EHttp + http.StatusConflict:                      {ID: "EHttp + http.StatusConflict", Message: "conflict"},
	EHttp + http.StatusGone:                          {ID: "EHttp + http.StatusGone", Message: "gone"},
	EHttp + http.StatusLengthRequired:                {ID: "EHttp + http.StatusLengthRequired", Message: "length required"},
	EHttp + http.StatusPreconditionFailed:            {ID: "EHttp + http.StatusPreconditionFailed", Message: "precondition failed"},
	EHttp + http.StatusRequestEntityTooLarge:         {ID: "EHttp + http.StatusRequestEntityTooLarge", Message: "entity too large"},
	EHttp + http.StatusRequestURITooLong:             {ID: "EHttp + http.StatusRequestURITooLong", Message: "uri too long"},
	EHttp + http.StatusUnsupportedMediaType:          {ID: "EHttp + http.StatusUnsupportedMediaType", Message: "unsupported media type"},
	EHttp + http.StatusRequestedRangeNotSatisfiable:  {ID: "EHttp + http.StatusRequestedRangeNotSatisfiable", Message: "range not satisfiable"},
	EHttp + http.StatusExpectationFailed:             {ID: "EHttp + http.StatusExpectationFailed", Message: "expectation failed"},
	EHttp + http.StatusTeapot:                        {ID: "EHttp + http.StatusTeapot", Message: "teapot"},
	EHttp + http.StatusMisdirectedRequest:            {ID: "EHttp + http.StatusMisdirectedRequest", Message: "misdirected request"},
	EHttp + http.StatusUnprocessableEntity:           {ID: "EHttp + http.StatusUnprocessableEntity", Message: "unprocessable entity"},
	EHttp + http.StatusLocked:                        {ID: "EHttp + http.StatusLocked", Message: "locked"},
	EHttp + http.StatusFailedDependency:              {ID: "EHttp + http.StatusFailedDependency", Message: "failed dependency"},
	EHttp + http.StatusTooEarly:                      {ID: "EHttp + http.StatusTooEarly", Message: "too early"},
	EHttp + http.StatusUpgradeRequired:               {ID: "EHttp + http.StatusUpgradeRequired", Message: "upgrade required"},
	EHttp + http.StatusPreconditionRequired:          {ID: "EHttp + http.StatusPreconditionRequired", Message: "precondition required"},
	EHttp + http.StatusTooManyRequests:               {ID: "EHttp + http.StatusTooManyRequests", Message: "too many requests"},
	EHttp + http.StatusRequestHeaderFieldsTooLarge:   {ID: "EHttp + http.StatusRequestHeaderFieldsTooLarge", Message: "header fields too large"},
	EHttp + http.StatusUnavailableForLegalReasons:    {ID: "EHttp + http.StatusUnavailableForLegalReasons", Message: "unavailable for legal reasons"},
	EHttp + http.StatusInternalServerError:           {ID: "EHttp + http.StatusInternalServerError", Message: "internal server error"},
	EHttp + http.StatusNotImplemented:                {ID: "EHttp + http.StatusNotImplemented", Message: "not implemented"},
	EHttp + http.StatusBadGateway:                    {ID: "EHttp + http.StatusBadGateway", Message: "bad gateway"},
	EHttp + http.StatusServiceUnavailable:            {ID: "EHttp + http.StatusServiceUnavailable", Message: "service unavailable"},
	EHttp + http.StatusGatewayTimeout:                {ID: "EHttp + http.StatusGatewayTimeout", Message: "timeout"},
	EHttp + http.StatusHTTPVersionNotSupported:       {ID: "EHttp + http.StatusHTTPVersionNotSupported", Message: "http version not supported"},
	EHttp + http.StatusVariantAlsoNegotiates:         {ID: "EHttp + http.StatusVariantAlsoNegotiates", Message: "variant also negotiates"},
	EHttp + http.StatusInsufficientStorage:           {ID: "EHttp + http.StatusInsufficientStorage", Message: "insufficient storage"},
	EHttp + http.StatusLoopDetected:                  {ID: "EHttp + http.StatusLoopDetected", Message: "loop detected"},
	EHttp + http.StatusNotExtended:                   {ID: "EHttp + http.StatusNotExtended", Message: "not extended"},
	EHttp + http.StatusNetworkAuthenticationRequired: {ID: "EHttp + http.StatusNetworkAuthenticationRequired", Message: "network authentication required"},

	EOapiUnescapedCookieParam:  {ID: "EOapiUnescapedCookieParam", Message: "unescaped cookie error"},
	EOapiUnmarshalingParam:     {ID: "EOapiUnmarshalingParam", Message: "json parse error"},
	EOapiRequireParam:          {ID: "EOapiRequireParam", Message: "param required error"},
	EOApiRequireHeader:         {ID: "EOApiRequireHeader", Message: "header required error"},
	EOapiInvalidParamFormat:    {ID: "EOapiInvalidParamFormat", Message: "invalid param format error"},
	EOapiTooManyValuesForParam: {ID: "EOapiTooManyValuesForParam", Message: "too many values for param error"},

	// Dynamo Error
	EDynamodbGeneric:         {ID: "EDynamodbGeneric", Message: "db error"},
	EDynamodbConflict:        {ID: "EDynamodbConflict", Message: "db conflict error"},
	EDynamodbMarshalFailed:   {ID: "EDynamodbMarshalFailed", Message: "db marshal failed"},
	EDynamodbUnmarshalFailed: {ID: "EDynamodbUnmarshalFailed", Message: "db unmarshal failed"},
	EDynamodbReadFailed:      {ID: "EDynamodbReadFailed", Message: "db read failed"},
	EDynamodbPutFailed:       {ID: "EDynamodbPutFailed", Message: "db put failed"},
	EDynamodbDeleteFailed:    {ID: "EDynamodbDeleteFailed", Message: "db delete failed"},

	// Redis Error
	ERedisGeneric:             {ID: "ERedisGeneric", Message: "redis error"},
	ERedisNilCache:            {ID: "ERedisNilCache", Message: "redis cache is empty"},
	ERedisMSetFailed:          {ID: "EResidMSetFailed", Message: "redis failed to set requested values"},
	ERedisInvalidKey:          {ID: "ERedisInvalidKey", Message: "invalid redis key"},
	ERedisCacheGetFailed:      {ID: "ERedisCacheGetFailed", Message: "redis cache get failed"},
	ERedisCacheSetFailed:      {ID: "ERedisCacheSetFailed", Message: "redis cache set failed"},
	ERedisCacheDeleteFailed:   {ID: "ERedisCacheDeleteFailed", Message: "redis cache delete failed"},
	ERedisNilIndex:            {ID: "ERedisNilIndex", Message: "redis index is empty"},
	ERedisExpireFailed:        {ID: "ERedisExpireFailed", Message: "redis expire failed"},
	ERedisZRevRangeFailed:     {ID: "ERedisZRevRangeFailed", Message: "redis zrevrange failed"},
	ERedisKeysFailed:          {ID: "ERedisKeysFailed", Message: "redis keys command failed"},
	ERedisSyncDataFailed:      {ID: "ERedisSyncDataFailed", Message: "redis sync data failed"},
	ERedisUnmarshalFailed:     {ID: "ERedisUnmarshalFailed", Message: "redis unmarshal failed"},
	ERedisZRangeByScoreFailed: {ID: "ERedisZRangeByScoreFailed", Message: "redis range by score failed"},
	ERedisTransactionFailed:   {ID: "ERedisTransactionFailed", Message: "redis transaction failed"},
	ERedisZAddFailed:          {ID: "ERedisZAddFailed", Message: "redis zadd failed"},
	ERedisZRemFailed:          {ID: "ERedisZRemFailed", Message: "redis zrem failed"},
	ERedisZUnionStoreFailed:   {ID: "ERedisZUnionStoreFailed", Message: "redis zunionstore failed"},
	ERedisZScoreFailed:        {ID: "ERedisZScoreFailed", Message: "redis zscore failed"},
	ERedisObjectMissing:       {ID: "ERedisObjectMissing", Message: "redis object missing"},

	// Firehose Error
	EFirehoseGeneric:         {ID: "EFirehoseGeneric", Message: "firehose generic"},
	EFirehoseConflict:        {ID: "EFirehoseConflict", Message: "firehose conflict"},
	EFirehoseDeleteFailed:    {ID: "EFirehoseDeleteFailed", Message: "firehose delete failed"},
	EFirehoseMarshalFailed:   {ID: "EFirehoseMarshalFailed", Message: "firehose marshal failed"},
	EFirehosePutFailed:       {ID: "EFirehosePutFailed", Message: "firehose put failed"},
	EFirehoseReadFailed:      {ID: "EFirehoseReadFailed", Message: "firehose read failed"},
	EFirehoseUnmarshalFailed: {ID: "EFirehoseUnmarshalFailed", Message: "firehose unmarshal failed"},

	// Kinesis Error
	EKinesisGeneric:         {ID: "EKinesisGeneric", Message: "kinesis generic"},
	EKinesisConflict:        {ID: "EKinesisConflict", Message: "kinesis conflict"},
	EKinesisDeleteFailed:    {ID: "EKinesisDeleteFailed", Message: "kinesus delete failed"},
	EKinesisMarshalFailed:   {ID: "EKinesisMarshalFailed", Message: "kinesis marshal failed"},
	EKinesisPutFailed:       {ID: "EKinesisPutFailed", Message: "kinesis put failed"},
	EKinesisReadFailed:      {ID: "EKinesisReadFailed", Message: "kinesis read failed"},
	EKinesisUnmarshalFailed: {ID: "EKinesisUnmarshalFailed", Message: "kinesis unmarshal failed"},

	// endorse
	EEndorseGeneric:            {ID: "EEndorseGeneric", Message: "endorse generic"},
	EEndorseCannotResetDefault: {ID: "EEndorseCannotResetDefault", Message: "cannot reset default endorsement"},
	EEndorseCannotEndorseSelf:  {ID: "EEndorseCannotResetDefault", Message: "cannot endorse self"},

	// S3 Error
	ES3Generic:      {ID: "ES3Generic", Message: "s3 error"},
	ES3BadRequest:   {ID: "ES3BadRequest", Message: "bad request to s3"},
	ES3NotFound:     {ID: "ES3NotFound", Message: "s3 not found"},
	ES3PutFailed:    {ID: "ES3PutFailed", Message: "s3 put failed"},
	ES3GetFailed:    {ID: "ES3GetFailed", Message: "s3 get failed"},
	ES3DeleteFailed: {ID: "ES3DeleteFailed", Message: "s3 delete failed"},

	// Abuse Report Error
	ENoSNSTopicArnFound:    {ID: "ENoSNSTopicArnFound", Message: "SNS Topic Not Found"},
	EReportMessageTooLarge: {ID: "EReportPayloadTooLarge", Message: "report message too large"},

	// Abuse Report Error
	EWAFRateLimited: {ID: "EWAFRateLimited", Message: "Rate limited"},
}
