import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let tokenHost: string;
let roomId: string;
beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  roomId = await (await socialApi.createRoom(tokenHost, 2)).body.groupid;
});
afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
});
describe('', () => {
  /**
   * Checking get room information
   * - Create a room
   * - Get room information
   * - Checking if the room is matched
   */
  it('get room information', async () => {
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .get(`/chat/rooms/${roomId}`)
      .set('Authorization', 'Bearer ' + tokenHost);

    // console.log(resp.body);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.groupid).toEqual(roomId);
  });
});
