-module(t2gp_social_lager_SUITE).

-compile(nowarn_export_all).
-compile(export_all).

-include_lib("eunit/include/eunit.hrl").

init_per_suite(Config) ->
    cover:start(),
    t2gp_social_test:configure(),
    application:ensure_all_started(t2gp_social),
    Config.

end_per_suite(_Config) ->
    application:stop(t2gp_social),
    ok.

all() -> [
    test_format
].

%% {{MegaSec, Sec, MicroSec}, <<"1970-01-01T00:00:00Z">>}
timestamp_now() ->
    Now = os:timestamp(),
    {MegaSec, Sec, MicroSec} = Now,
    {ok, TimeStamp} = rfc3339:format(
        MegaSec * 1000000000000 + Sec * 1000000 + MicroSec,
        micro_seconds
    ),
    {Now, TimeStamp}.

pid() ->
    Self = self(),
    {Self, unicode:characters_to_binary(io_lib:format("~p", [Self]), unicode)}.

test_format(_) ->
    {Now, TimeStamp} = timestamp_now(),
    {Self, Pid} = pid(),
    [
        {"basic message",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{},\"severity\":\"info\",\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [], []), [])
            )},
        {"pid in metadata",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{\"pid\":\"", Pid/binary, "\"},\"severity\":\"info\",\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [{pid, io_lib:format("~p", [Self])}], []), [])
            )},
        {"bare pid in metadata",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{\"pid\":\"<0.6.0>\"},\"severity\":\"info\",\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [{pid, list_to_pid("<0.6.0>")}], []), [])
            )},
        {"file in metadata",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{\"file\":\"foo.erl\"},\"severity\":\"info\",\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [{file, "foo.erl"}], []), [])
            )},
        {"static field",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{},\"severity\":\"info\",\"static\":{\"app\":\"test\"},\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [], []), [{static, #{app => test}}])
            )},
        {"customizable separator",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{},\"severity\":\"info\",\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n----\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [], []), [{separator, <<"\n----\n">>}])
            )},
        {"non jsx term in metadata",
            ?assertEqual(
                [<<"{\"message\":\"hallo world\",\"metadata\":{\"t\":\"[{<<\\\"key\\\">>,<<\\\"value\\\">>},false]\"},\"severity\":\"info\",\"timestamp\":\"", TimeStamp/binary, "\"}">>, <<"\n">>],
                t2gp_social_lager:format(lager_msg:new("hallo world", Now, info, [{t, [{<<"key">>, <<"value">>}, false]}], []), [])
            )}
    ].
