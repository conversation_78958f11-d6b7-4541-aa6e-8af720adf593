#!/usr/bin/env python3
import re
import os.path

twok_dict = {
    'TWOK_<idx>_2K_PUBLIC_ID':  '${{ secrets.IAT_<key> }}',
    'TWOK_<idx>_EMAIL':         '${{ secrets.IAT_<key> }}',
    'TWOK_<idx>_PASSWORD':      '${{ secrets.IAT_<key> }}',
    'TWOK_<idx>_DISPLAY_NAME':  '${{ secrets.IAT_<key> }}'
}

steam_dict = {
    'STEAM_<idx>_2K_PUBLIC_ID':       '${{ secrets.IAT_<key> }}',
    'STEAM_<idx>_STEAM_ID':           '${{ secrets.IAT_<key> }}',
    'STEAM_<idx>_STEAM_ACCOUNT_NAME': '${{ secrets.IAT_<key> }}'
}


# TODO:a function to find the longest key
#
def get_workflow_env(k, v, f):
    f.write("{}: {}\n".format(k, v.rjust(47)))  # 47 is the longest key +2

#
def get_secret_template(k, v, f):
    m = re.search("(IAT_.*) ", v)
    if m != None:
        f.write("{}, ".format(m.group(1)))

#
def process(cnt, acct_dict, task, output_file):
    if os.path.exists(output_file):
        print("{} file exists, removing it".format(output_file))
        os.remove(output_file)

    f = open(output_file, 'a')

    for i in range(1, cnt+1):
        newidx = str(i).zfill(3)

        for k, v in acct_dict.items():
            k = k.replace("<idx>", newidx)
            v = v.replace("<key>", k)

            #
            task(k, v, f)

    f.close()



# 
process(10, twok_dict, get_workflow_env, 'twok_workflow_env')
process(10, steam_dict, get_workflow_env, 'steam_workflow_env')

#
#process(10, twok_dict, get_secret_template, 'twok_secret_keys')
#process(10, steam_dict, get_secret_template, 'steam_secret_keys')


