package api

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	gomock "go.uber.org/mock/gomock"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	. "github.com/onsi/gomega"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func TestListFriends(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	var friendList []*apipub.FriendResponse
	friendList = append(friendList, &apipub.FriendResponse{
		Friendid: "1",
	})

	var presences []*apipub.PresenceResponse
	presences = append(presences, &apipub.PresenceResponse{
		Clientid: aws.String("ClientId1"),
		GameData: aws.String("GameData1"),
		GameName: "GameName1",
	})
	presences = append(presences, &apipub.PresenceResponse{
		Clientid: aws.String("ClientId2"),
		GameData: aws.String("GameData2"),
		GameName: "GameName2",
	})

	const lockKey = "__bsm_redislock_unit_test__"

	redisOpts := &redis.ClusterOptions{
		Addrs: []string{"127.0.0.1:6379", "127.0.0.1:6380", "127.0.0.1:6381"},
	}
	//this is to support CI/CD tests and devcontainer
	if utils.StringContainsSubstr(cfg.DynamoDBURL, "192.168.123.") {
		redisOpts.Addrs = []string{"*************:6379", "*************:6380", "*************:6381"}

	}

	rc := redis.NewClusterClient(redisOpts)
	client := redislock.New(rc)
	ctx := context.Background()

	lock, err := client.Obtain(ctx, lockKey, time.Hour, nil)
	if err != nil {
		t.Fatal(err)
	}
	defer lock.Release(ctx)

	g.Describe("ListFriends", func() {
		profile := apipub.UserProfileResponse{Userid: "userid", DisplayName: aws.String("test user")}
		profileAr := []*apipub.UserProfileResponse{&profile}

		g.It("get friend list with exists in Cache false", func() {
			w, r := Login(User1JWT)
			params := apipub.ListFriendsParams{}

			var items []*apipub.FriendResponse
			friend := &apipub.FriendResponse{
				Userid:   utils.GenerateRandomDNAID(),
				Friendid: utils.GenerateRandomDNAID(),
				Status:   "friend",
			}
			items = append(items, friend)

			mock.rc.EXPECT().FriendlistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
			mock.ds.EXPECT().GetFullFriendList(gomock.Any(), gomock.Any()).Return(&items, nil).AnyTimes()
			mock.ds.EXPECT().GetFriends(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&items, nil, nil)
			mock.rc.EXPECT().GetSyncLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(lock)
			mock.rc.EXPECT().ClearFriendlist(gomock.Any(), gomock.Any())
			mock.rc.EXPECT().SetFriends(gomock.Any(), gomock.Any(), gomock.Any())
			mock.rc.EXPECT().GetUserCacheMeta(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SetUserCacheMeta(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetUserPresences(r.Context(), gomock.Any(), gomock.Any()).Return(&presences, nil)

			mock.api.ListFriends(w, r, params)

			var response apipub.FriendsNext
			body := w.Body.String()
			json.Unmarshal([]byte(body), &response)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(len(response.Items) > 0).IsTrue()
		})
		g.It("get friend list from Cache", func() {
			w, r := Login(User1JWT)
			params := apipub.ListFriendsParams{}

			var items []*apipub.FriendResponse
			friend := &apipub.FriendResponse{
				Userid:   utils.GenerateRandomDNAID(),
				Friendid: utils.GenerateRandomDNAID(),
				Status:   "pending",
			}
			items = append(items, friend)

			mock.rc.EXPECT().FriendlistExistsInCache(gomock.Any(), gomock.Any()).Return(true)
			mock.rc.EXPECT().GetFriends(r.Context(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&items, "", nil)
			mock.rc.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(nil, nil)

			mock.api.ListFriends(w, r, params)

			var response apipub.FriendsNext
			body := w.Body.String()
			json.Unmarshal([]byte(body), &response)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(len(response.Items) > 0).IsTrue()
		})
		g.It("get friend list succeed without priority", func() {
			w, r := Login(User1JWT)
			params := apipub.ListFriendsParams{}

			mock.rc.EXPECT().FriendlistExistsInCache(gomock.Any(), gomock.Any()).Return(true)
			mock.rc.EXPECT().GetFriends(r.Context(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&friendList, "", nil)
			mock.rc.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetUserPresences(r.Context(), gomock.Any(), gomock.Any()).Return(&presences, nil)

			mock.api.ListFriends(w, r, params)

			var response apipub.FriendsNext
			body := w.Body.String()
			json.Unmarshal([]byte(body), &response)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(len(response.Items) > 0).IsTrue()
		})
		g.It("get friend list succeed with empty product id", func() {
			w, r := Login(User1JWT)

			params := apipub.ListFriendsParams{}

			mock.rc.EXPECT().FriendlistExistsInCache(gomock.Any(), gomock.Any()).Return(true)
			mock.rc.EXPECT().GetFriends(r.Context(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&friendList, "", nil)
			mock.rc.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().GetUserProfiles(r.Context(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetUserPresences(r.Context(), "1", gomock.Any()).Return(&presences, nil)

			mock.api.ListFriends(w, r, params)

			response := apipub.FriendsNext{}
			body := w.Body.String()
			json.Unmarshal([]byte(body), &response)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(len(response.Items) > 0).IsTrue()
		})
		g.It("get friend list failed", func() {
			w, r := Login(User1JWT)
			params := apipub.ListFriendsParams{}

			// Fail to get friends
			mock.rc.EXPECT().FriendlistExistsInCache(gomock.Any(), gomock.Any()).Return(true)
			mock.rc.EXPECT().GetFriends(r.Context(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
			mock.api.ListFriends(w, r, params)

			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}

func TestSearchFriends(t *testing.T) {
	g := goblin.Goblin(t)

	// create mock data store
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	full := apipub.AccountTypeDNAFULL
	platform := apipub.AccountTypeDNAPLATFORM

	var accounts apipub.SearchAccountResponseList
	accounts = append(accounts, apipub.SearchAccountResponse{
		DisplayName: aws.String("Full"),
		Id:          aws.String("Full"),
		Type:        &full,
		Links:       nil,
	})
	accounts = append(accounts, apipub.SearchAccountResponse{
		// Platform account
		DisplayName: aws.String("Platform"),
		Id:          aws.String("Platform"),
		Type:        &platform,
		Links:       nil,
	})
	searchResponse := accounts

	g.Describe("SearchFriends", func() {

		g.It("search by display name succeed", func() {
			g.Timeout(45 * time.Second)
			w, r := Login(User1JWT)

			params := apipub.Search2KUsersParams{}
			params.DisplayName = "full"

			mock.id.EXPECT().SearchAccounts(r.Context(), gomock.Any()).Return(&searchResponse, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.tele.EXPECT().SendFriendEvent(gomock.Any(), gomock.Any()).AnyTimes()
			mock.api.Search2KUsers(w, r, params)
			g.Assert(w.Code).Equal(http.StatusOK)

			body := w.Body.String()
			body = strings.TrimSpace(body)
			expected := "{\"items\":[{\"links\":null,\"name\":\"Full\",\"simplePresence\":null,\"userid\":\"Full\"}]}"
			g.Assert(body).Equal(expected)
		})

		g.It("search failed because of bad request", func() {

			w, r := Login(User1JWT)

			params := apipub.Search2KUsersParams{}
			params.DisplayName = "full"

			mock.id.EXPECT().SearchAccounts(r.Context(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			mock.api.Search2KUsers(w, r, params)
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}

func TestMakeFriend(t *testing.T) {
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()
	g := goblin.Goblin(t)
	//special hook for gomega
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	badReqBody := apipub.SetFriendsRequestBody{
		Message: aws.String("All your base are belong to us!"),
	}

	reqBody := apipub.SetFriendsRequestBody{
		Message: aws.String("Hello there!"),
	}

	testCases := []struct {
		name               string
		prepareRequest     func(string) (*httptest.ResponseRecorder, *http.Request)
		prepareExpections  func()
		expectResponseCode int
		expectResponseJSON string
		friendid           string
	}{
		{
			name: "MakeFriend Call succeed",
			prepareRequest: func(jwt string) (*httptest.ResponseRecorder, *http.Request) {
				return AddBodyToRequest(reqBody, User1JWT)
			},
			prepareExpections: func() {
				var fewFriendsCount int64 = 10
				userprofile := apipub.UserProfileResponse{
					DisplayName: aws.String("test name"),
				}
				// var ret []*apipub.Blocklist
				mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
				mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
				mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&userprofile, nil)
				mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
				mock.rc.EXPECT().CountFriendlistMembers(gomock.Any(), gomock.Any()).Return(fewFriendsCount, nil)
				mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
				mock.rc.EXPECT().CountFriendlistMembers(gomock.Any(), gomock.Any()).Return(fewFriendsCount, nil)
				mock.rc.EXPECT().GetFriend(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
				mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
				mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
				mock.ds.EXPECT().MakeFriend(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("{\"status\":\"pending\"}", nil)
				mock.rc.EXPECT().MakeFriend(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			},
			expectResponseCode: http.StatusOK,
			expectResponseJSON: "{\"status\":\"{\\\"status\\\":\\\"pending\\\"}\"}\n",
			friendid:           "e12c3df480984141b2f385646b2024fa",
		},
		{
			name: "friend self return empty",
			prepareRequest: func(jwt string) (*httptest.ResponseRecorder, *http.Request) {
				reqBody := apipub.SetFriendsRequestBody{
					Message: aws.String("Hello there!"),
				}
				return AddBodyToRequest(reqBody, User1JWT)
			},
			prepareExpections: func() {
			},
			expectResponseCode: http.StatusOK,
			expectResponseJSON: "{}",
			friendid:           "b287e655461f4b3085c8f244e394ff7e",
		},
		{
			name: "Bad user ID in friend Request",
			prepareRequest: func(jwt string) (*httptest.ResponseRecorder, *http.Request) {
				return AddBodyToRequest(badReqBody, User1JWT)
			},
			prepareExpections: func() {
			},
			expectResponseCode: http.StatusUnprocessableEntity,
			expectResponseJSON: errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidFriendID).Error(),
			friendid:           "$$fooooobar  ",
		},
	}

	g.Describe("MakeFriend", func() {
		for i := range testCases {
			testCase := testCases[i]
			g.It(testCase.name, func() {

				w, req := testCase.prepareRequest(User1JWT)
				testCase.prepareExpections()
				mock.api.MakeFriend(w, req, testCase.friendid)

				Ω(w.Body.String()).Should(ContainSubstring(testCase.expectResponseJSON))
				g.Assert(w.Code).Equal(testCase.expectResponseCode)
			})
		}
	})
}

func TestUpdateFriendStatus(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	profile := apipub.UserProfileResponse{Userid: "Id", DisplayName: aws.String("test user")}

	defer mock.ctrl.Finish()

	g.Describe("UpdateFriendStatus", func() {
		g.It("update status succeeded", func() {
			viewed := false
			reqBody := apipub.UpdateFriendStatusJSONBody{
				Viewed: &viewed,
			}
			w, r := AddBodyToRequest(reqBody, User1JWT)
			friend := apipub.FriendResponse{
				Friendid: "Friend",
				Userid:   "Id",
			}
			mock.rc.EXPECT().GetFriend(r.Context(), gomock.Any(), "111").Return(&friend, nil)
			mock.ds.EXPECT().PutItemInProfileTable(r.Context(), &friend).Return(nil)
			mock.rc.EXPECT().SetFriend(r.Context(), &friend, gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetUserProfile(r.Context(), friend.Friendid).Return(&profile, nil)
			mock.api.UpdateFriendStatus(w, r, "111")
			g.Assert(w.Code).Equal(http.StatusOK)
		})

		g.It("Update status failed because GetFriend() returned an error", func() {
			friend := apipub.FriendResponse{
				Friendid: "Friend",
				Userid:   "Id",
			}
			mock.rc.EXPECT().GetFriend(gomock.Any(), gomock.Any(), gomock.Any()).Return(&friend, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
			viewed := false
			reqBody := apipub.UpdateFriendStatusJSONBody{
				Viewed: &viewed,
			}

			w, r := AddBodyToRequest(reqBody, User1JWT)
			mock.api.UpdateFriendStatus(w, r, "111")
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("Update status failed because PutItem returned an error", func() {
			viewed := false
			reqBody := apipub.UpdateFriendStatusJSONBody{
				Viewed: &viewed,
			}

			w, r := AddBodyToRequest(reqBody, User1JWT)
			friend := apipub.FriendResponse{
				Friendid: "Friend",
				Userid:   "Id",
			}
			mock.rc.EXPECT().GetFriend(r.Context(), gomock.Any(), "111").Return(&friend, nil)
			mock.ds.EXPECT().PutItemInProfileTable(r.Context(), &friend).Return(errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			mock.api.UpdateFriendStatus(w, r, "111")
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}

func TestDeleteFriend(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	friend := apipub.FriendResponse{
		Userid: "111",
		Status: apipub.Friend,
	}

	g.Describe("DeleteFriend", func() {
		g.It("delete friend succeeded", func() {
			w, r := Login(User1JWT)

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ds.EXPECT().MakeUnfriend(r.Context(), gomock.Any(), "111").Return(nil)
			mock.rc.EXPECT().MakeUnfriend(r.Context(), gomock.Any(), "111").Return(nil)
			mock.rc.EXPECT().GetUserProfile(r.Context(), gomock.Any()).AnyTimes()
			mock.ds.EXPECT().GetUserProfile(r.Context(), gomock.Any()).AnyTimes()
			mock.id.EXPECT().SyncUserProfile(r.Context(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: friend.Userid}, nil).AnyTimes()
			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetFriend(r.Context(), gomock.Any(), gomock.Any()).Return(&friend, nil)
			mock.tele.EXPECT().SendFriendEvent(gomock.Any(), gomock.Any()).AnyTimes()
			mock.api.DeleteFriend(w, r, "111")

			g.Assert(w.Code).Equal(http.StatusOK)
		})

		g.It("delete friend failed because MakeUnfriend returns error", func() {
			w, r := Login(User1JWT)

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ds.EXPECT().MakeUnfriend(r.Context(), gomock.Any(), "111").Return(errors.New("Error"))
			mock.rc.EXPECT().GetUserProfile(r.Context(), gomock.Any()).AnyTimes()
			mock.ds.EXPECT().GetUserProfile(r.Context(), gomock.Any()).AnyTimes()
			mock.id.EXPECT().SyncUserProfile(r.Context(), gomock.Any()).AnyTimes()
			mock.rc.EXPECT().GetFriend(r.Context(), gomock.Any(), gomock.Any()).Return(&friend, nil)
			mock.tele.EXPECT().SendFriendEvent(gomock.Any(), gomock.Any()).AnyTimes()
			mock.api.DeleteFriend(w, r, "111")

			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}

func TestImportPlatformFriends(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()
	accType := apipub.AccountTypeDNA(3)
	var accounts []apipub.SearchAccountResponse
	accounts = append(accounts, apipub.SearchAccountResponse{
		// Full account
		DisplayName:     aws.String("Full"),
		FirstPartyAlias: aws.String("Full"),
		FirstPartyId:    aws.String("Full"),
		Id:              aws.String("Full"),
		Type:            &accType,
	})
	accounts = append(accounts, apipub.SearchAccountResponse{
		// Platform account
		DisplayName:     aws.String("Platform"),
		FirstPartyAlias: aws.String("Platform"),
		FirstPartyId:    aws.String("Platform"),
		ParentAccountId: aws.String("Platform"),
	})
	searchResponse := accounts

	g.Describe("ImportPlatformFriends", func() {
		g.It("import succeeded", func() {
			w, r := Login(User1JWT)
			params := apipub.ImportPlatformFriendsParams{
				Id: "1111,2222",
			}
			mock.id.EXPECT().SearchAccounts(r.Context(), gomock.Any()).Return(&searchResponse, nil)
			mock.api.ImportPlatformFriends(w, r, params)

			g.Assert(w.Code).Equal(http.StatusOK)
		})
	})

	g.Describe("ImportPlatformFriends", func() {
		g.It("import failed because SearchAccount returned an error", func() {
			w, r := Login(User1JWT)
			params := apipub.ImportPlatformFriendsParams{
				Id: "1111,2222",
			}
			mock.id.EXPECT().SearchAccounts(r.Context(), gomock.Any()).Return(&searchResponse, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
			mock.api.ImportPlatformFriends(w, r, params)

			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}
