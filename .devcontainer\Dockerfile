FROM golang:1.24.2-alpine3.21
COPY ../t2paca.crt /usr/local/share/ca-certificates/t2paca.crt

RUN cat /usr/local/share/ca-certificates/t2paca.crt >> /etc/ssl/certs/ca-certificates.crt
RUN apk update
RUN apk add --no-cache git openssl openssh openssh-server ca-certificates make redis wget curl unzip less g++ aws-cli libxext libxrender libxtst libxi freetype procps gcompat
# RUN pip install --ignore-installed wcwidth docutils cryptography urllib3 six

RUN echo quit | openssl s_client -showcerts -servername proxy.golang.org -connect proxy.golang.org:443  2>&1 | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' > /etc/ssl/certs/ca-cert-t2.pem

ENV GOPRIVATE="github.com/take-two-t2gp,github.take2games.com"
ENV GOOS=linux
ENV GOARCH=amd64
ENV GOPATH="/usr/go"
RUN go install golang.org/x/tools/gopls@latest
RUN go install github.com/go-delve/delve/cmd/dlv@latest
WORKDIR /workdir
