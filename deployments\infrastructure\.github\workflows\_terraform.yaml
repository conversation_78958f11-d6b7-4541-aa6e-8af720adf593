name: Reusable Terraform Steps

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

on:
  workflow_call:
    inputs:
      workspace:
        required: true
        type: string
      accepted_events:
        required: true
        type: string
      env_ver_mapping_update:
        required: false
        type: boolean
jobs:
  terraform_apply:
    name: Terraform Apply
    environment: ${{inputs.workspace}}
    runs-on: [self-hosted, linux]
    defaults:
      run:
        working-directory: terraform
    steps:
      - name: Checkout
        uses: actions/checkout@main
      - name: DEBUG - Check event name
        run: |
          echo ${{ github.ref }}
          echo ${{ github.event_name }}
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@8eba69932f928f2bc1b9837d16a095e7e1cb0845
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Get Terraform Version from Repo
        run: |
          echo TERRAFORM_VERSION=$(cat .terraform-version) >> $GITHUB_ENV
          echo DEPLOY_KEY=${{ secrets.DEPLOY_KEY }} >> $GITHUB_ENV
      - name: Env Ver Mapping Version Update (Develop Workspace only)
        if: inputs.env_ver_mapping_update
        run: |
          echo TF_VAR_env_ver_mapping_update=${GITHUB_SHA::8} >> $GITHUB_ENV
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}
      - name: Terraform Format
        id: fmt
        run: terraform fmt -check
      - name: Terraform Init
        id: init
        run: terraform init -reconfigure
      - name: Terraform Workspace
        run: terraform workspace select ${{ inputs.workspace }}
      - name: Terraform Plan
        id: plan
        if: github.event_name == 'pull_request'
        run: |
          terraform plan -no-color -input=false
      - uses: robburger/terraform-pr-commenter@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TF_WORKSPACE: ${{ inputs.workspace }}
        with:
          commenter_type: plan # Choose one
          commenter_input: ${{ format('{0}{1}', steps.plan.outputs.stdout, steps.plan.outputs.stderr) }}
          commenter_exitcode: ${{ steps.plan.outputs.exitcode }}
      - name: Terraform Plan Status
        if: steps.plan.outcome == 'failure'
        run: exit 1
      - name: Terraform Apply
        if: contains(fromJson(inputs.accepted_events), github.event_name)
        run: terraform apply -auto-approve -input=false