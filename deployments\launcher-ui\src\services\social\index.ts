import type { DefaultOptions } from '@sveltestack/svelte-query';
import {
  EVENT_FRIENDS_FETCH,
  EVENT_FRIENDS_FETCH_RESULT,
  EVENT_FRIENDS_PAGE_MOUNT,
  EVENT_FRIENDS_SEARCH_REQUEST,
  EVENT_FRIENDS_SEARCH_RESULT,
  EVENT_FRIEND_DELETE_ERROR,
  EVENT_FRIEND_REQUEST_ACCEPT,
  EVENT_FRIEND_REQUEST_ACCEPTED,
  EVENT_FRIEND_REQUEST_ACCEPT_ERROR,
  EVENT_FRIEND_REQUEST_DELETE,
  EVENT_FRIEND_REQUEST_DELETED,
  EVENT_FRIEND_REQUEST_MAKE,
  EVENT_FRIEND_REQUEST_MAKE_ERROR,
  EVENT_FRIEND_REQUEST_SENT,
  EVENT_FRIEND_REQUEST_VIEWED,
  EVENT_LANGUAGE_CHANGED,
  EVENT_MQTT_FRIEND_INVITE_MESSAGE_RECEIVED,
  EVENT_MQTT_FRIEND_REMOVED_MESSAGE_RECEIVED,
  EVENT_MQTT_PRESENCE_MESSAGE_RECEIVED,
  EVENT_PENDING_FRIENDS_FETCH,
  EVENT_PENDING_FRIENDS_FETCH_RESULT,
  EVENT_PRESENCE_CHANGED,
  EVENT_STEAM_ACCOUNT_LINKED,
  EVENT_STEAM_ACCOUNT_LINKING,
  EVENT_STEAM_CONNECT_PAGE_EXIT,
  EVENT_STEAM_FRIENDS_FETCH,
  EVENT_STEAM_FRIENDS_FETCH_RESULT,
  EVENT_STEAM_LINK_WINDOW_CLOSE,
  EVENT_STEAM_LINK_WINDOW_CLOSED,
  EVENT_STEAM_LINK_WINDOW_OPEN,
  EVENT_USER_PROFILE_FETCH_REQUEST,
  EVENT_USER_PROFILE_FETCH_RESULT,
  INITIAL_LANGUAGE,
  MQTT_MESSAGE_TOPIC,
} from '../../constant';
import { getSteamIdFromLocationSearch } from '../../utils';
import type {
  FriendRequestResponse,
  IFriendsService,
  Presence,
} from '../friends';
import { ILogService, LogService } from '../log';
import type {
  IMqttService,
  MqttMessagePayload,
  MqttPresenceMessageData,
  MqttUserMessageData,
} from '../mqtt';
import type { IQueryService, QueryStoreResult } from '../query';
import { QueryService } from '../query';
import type { TransportService } from '../transport';
import type { IUserService } from '../user';

const QUERY_KEY_FRIENDS_RESULT = 'friendsQueryResult';
const QUERY_KEY_PENDING_FRIENDS_RESULT = 'pendingFriendsQueryResult';
const QUERY_KEY_USER_PROFILE_RESULT = 'userProfileQueryResult';
const QUERY_KEY_STEAM_FRIENDS_RESULT = 'steamFriendsQueryResult';

export interface SocialOptions {
  lang: string;
}

const defaultOptions: SocialOptions = {
  lang: INITIAL_LANGUAGE,
};

export class SocialServices<
  T extends TransportService,
  K extends IFriendsService,
  G extends IUserService
> {
  protected transportService: T;
  protected friendsService: K;
  protected userService: G;
  protected queryService: IQueryService;
  protected mqttService: IMqttService;
  protected logService: ILogService;

  protected options: SocialOptions;
  protected steamQueryResultMap: Map<string, QueryStoreResult>;

  constructor({
    options,
    queryOptions,
    transportService,
    friendsService,
    userService,
    mqttService,
    logService,
  }: {
    options?: SocialOptions;
    queryOptions?: DefaultOptions;
    transportService?: T;
    friendsService?: K;
    userService?: G;
    mqttService?: IMqttService;
    logService?: ILogService;
  }) {
    this.transportService = transportService;
    this.friendsService = friendsService;
    this.userService = userService;
    this.options = { ...defaultOptions, ...options };
    this.queryService = new QueryService(queryOptions);
    this.mqttService = mqttService;
    this.logService = logService || new LogService();
    this.steamQueryResultMap = new Map<string, QueryStoreResult>();
    this.init();
  }

  getTransportService(): T {
    return this.transportService;
  }

  getFriendsService(): K {
    return this.friendsService;
  }

  getUserService(): G {
    return this.userService;
  }

  getLogServie(): ILogService {
    return this.logService;
  }

  getQueryService(): IQueryService {
    return this.queryService;
  }

  init(): void {
    if (this.userService) {
      this.setUserQuery();
      this.set3rdProviderLink();
    }

    if (this.friendsService) {
      this.setFriendsQuery();
      this.setSteamFriendsQuery();
    }

    if (this.mqttService) {
      this.setMqttListeners();

      this.transportService.subscribeEvent(
        EVENT_PRESENCE_CHANGED,
        async (eventName, payload: Presence) => {
          this.logService.log(eventName, payload);
          const userId = await this.userService.getUserIdAsync();
          this.mqttService.setPresence(userId, payload);
        }
      );
    }

    this.transportService.subscribeEvent(EVENT_FRIENDS_PAGE_MOUNT, () => {
      this.transportService.publishEvent(
        EVENT_LANGUAGE_CHANGED,
        this.options.lang
      );
    });
  }

  private set3rdProviderLink() {
    const onLinkWindowClosed = () => {
      this.transportService.publishEvent(EVENT_STEAM_LINK_WINDOW_CLOSED, {
        closed: true,
      });
    };

    const onMessageReceived = ({ data }: { data: string }) => {
      const steamId = getSteamIdFromLocationSearch(data);
      this.transportService.publishEvent(EVENT_STEAM_ACCOUNT_LINKED, steamId);
    };

    this.transportService.subscribeEvent(EVENT_STEAM_LINK_WINDOW_OPEN, () => {
      this.userService.openSteamLinkWindow(onMessageReceived);

      this.userService.watchingLinkWindow(onLinkWindowClosed);

      this.transportService.publishEvent(EVENT_STEAM_ACCOUNT_LINKING, {
        linkingToSteam: true,
      });
    });

    this.transportService.subscribeEvent(EVENT_STEAM_LINK_WINDOW_CLOSE, () => {
      this.userService.closeLinkWindow();
    });

    this.transportService.subscribeEvent(EVENT_STEAM_CONNECT_PAGE_EXIT, () => {
      this.userService.clearMessageListener();
    });
  }

  private setUserQuery() {
    const userProfileQueryResult = this.queryService.setQuery(
      QUERY_KEY_USER_PROFILE_RESULT,
      () => this.userService.getUserProfileAsync()
    );

    userProfileQueryResult.subscribe(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ({ refetch, remove, ...queryResult }) => {
        this.transportService.publishEvent(
          EVENT_USER_PROFILE_FETCH_RESULT,
          queryResult
        );
      }
    );

    this.transportService.subscribeEvent(
      EVENT_USER_PROFILE_FETCH_REQUEST,
      () => {
        userProfileQueryResult.refetch();
      }
    );
  }

  private setFriendsQuery() {
    const friendsQueryResult = this.queryService.setQuery(
      QUERY_KEY_FRIENDS_RESULT,
      () => this.friendsService.getFriendsAsync()
    );

    friendsQueryResult.subscribe(({ refetch, remove, ...queryResult }) => {
      this.transportService.publishEvent(
        EVENT_FRIENDS_FETCH_RESULT,
        queryResult
      );
    });

    this.transportService.subscribeEvent(EVENT_FRIENDS_FETCH, () => {
      friendsQueryResult.refetch();
    });

    const pendingFriendsQueryResult = this.queryService.setQuery(
      QUERY_KEY_PENDING_FRIENDS_RESULT,
      () => this.friendsService.getPendingFriendsAsync()
    );

    pendingFriendsQueryResult.subscribe(
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      ({ refetch, remove, ...queryResult }) => {
        this.transportService.publishEvent(
          EVENT_PENDING_FRIENDS_FETCH_RESULT,
          queryResult
        );
      }
    );

    this.transportService.subscribeEvent(EVENT_PENDING_FRIENDS_FETCH, () => {
      pendingFriendsQueryResult.refetch();
    });

    this.transportService.subscribeEvent(
      EVENT_FRIEND_REQUEST_MAKE,
      (_, friendUserIds: string[]) => {
        Promise.all(
          friendUserIds.map(id => this.friendsService.MakeFriendAsync(id))
        )
          .then((responses: FriendRequestResponse[]) => {
            pendingFriendsQueryResult.refetch();
            this.transportService.publishEvent(
              EVENT_FRIEND_REQUEST_SENT,
              responses
            );
          })
          .catch(error => {
            this.logService.error('Failed to make friend');
            this.transportService.publishEvent(
              EVENT_FRIEND_REQUEST_MAKE_ERROR,
              error
            );
          });
      }
    );

    this.transportService.subscribeEvent(
      EVENT_FRIEND_REQUEST_DELETE,
      (_, friendId: string) => {
        this.friendsService
          .DeleteFriendAsync(friendId)
          .then(() => {
            pendingFriendsQueryResult.refetch();
            this.transportService.publishEvent(EVENT_FRIEND_REQUEST_DELETED);
          })
          .catch(error => {
            this.logService.error('Failed to delete a friend');
            this.transportService.publishEvent(
              EVENT_FRIEND_DELETE_ERROR,
              error
            );
          });
      }
    );

    this.transportService.subscribeEvent(
      EVENT_FRIEND_REQUEST_ACCEPT,
      (_, friendId: string) => {
        this.friendsService
          .MakeFriendAsync(friendId)
          .then(() => {
            friendsQueryResult.refetch();
            pendingFriendsQueryResult.refetch();
            this.transportService.publishEvent(EVENT_FRIEND_REQUEST_ACCEPTED);
          })
          .catch(error => {
            this.logService.error('Failed to accept a friend request');
            this.transportService.publishEvent(
              EVENT_FRIEND_REQUEST_ACCEPT_ERROR,
              error
            );
          });
      }
    );

    this.transportService.subscribeEvent(
      EVENT_FRIENDS_SEARCH_REQUEST,
      async (_, displayName: string) => {
        const friends = await this.friendsService.searchFriendsAsync(
          displayName
        );

        this.transportService.publishEvent(
          EVENT_FRIENDS_SEARCH_RESULT,
          friends
        );
      }
    );

    this.transportService.subscribeEvent(
      EVENT_FRIEND_REQUEST_VIEWED,
      async (_, friendId: string) => {
        await this.friendsService.markFriendRequestViewed(friendId);
        pendingFriendsQueryResult.refetch();
      }
    );
  }

  private setSteamFriendsQuery() {
    this.transportService.subscribeEvent(
      EVENT_STEAM_FRIENDS_FETCH,
      (_, steamId: string) => {
        if (this.steamQueryResultMap.has(steamId)) {
          const queryResult = this.steamQueryResultMap.get(steamId);
          queryResult.refetch();
        } else {
          const steamFriendsQueryResult = this.queryService.setQuery(
            QUERY_KEY_STEAM_FRIENDS_RESULT,
            () => this.friendsService.getSteamFriendsAsync(steamId)
          );

          steamFriendsQueryResult.subscribe(
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            ({ refetch, remove, ...queryResult }) => {
              this.transportService.publishEvent(
                EVENT_STEAM_FRIENDS_FETCH_RESULT,
                queryResult
              );
            }
          );

          this.steamQueryResultMap.set(steamId, steamFriendsQueryResult);
        }
      }
    );
  }

  private handleMqttPresenceMessage(
    receivedUserId: string,
    payload: MqttMessagePayload<MqttPresenceMessageData>
  ) {
    this.transportService.publishEvent(EVENT_MQTT_PRESENCE_MESSAGE_RECEIVED, {
      userId: receivedUserId,
      data: payload.data,
    });

    this.logService.log(
      'mqtt presence message received: ',
      receivedUserId,
      payload.data
    );
  }

  private handleMqttUserMessage(
    receivedUserId: string,
    payload: MqttMessagePayload<MqttUserMessageData>
  ) {
    switch (payload.type) {
      case 'friendInvite':
        this.transportService.publishEvent(
          EVENT_MQTT_FRIEND_INVITE_MESSAGE_RECEIVED,
          {
            userId: receivedUserId,
            data: payload.data,
          }
        );

        this.logService.log(
          'mqtt friendInvite message received: ',
          receivedUserId,
          payload.data
        );

        break;
      case 'friendRemoved':
        this.transportService.publishEvent(
          EVENT_MQTT_FRIEND_REMOVED_MESSAGE_RECEIVED,
          {
            userId: receivedUserId,
            data: payload.data,
          }
        );

        this.logService.log(
          'mqtt friendRemoved message received: ',
          receivedUserId,
          payload.data
        );

        break;
      default:
        this.logService.log(
          'mqtt user message received: ',
          receivedUserId,
          payload
        );
    }
  }

  private setMqttListeners() {
    const onMessageCallback = (topic: string, data: Buffer) => {
      const index = topic.indexOf('/');
      const type = topic.slice(0, index);
      const receivedUserId = topic.slice(index + 1, topic.length);

      switch (type) {
        case MQTT_MESSAGE_TOPIC.presence:
          this.handleMqttPresenceMessage(
            receivedUserId,
            JSON.parse(data.toString())
          );
          break;
        case MQTT_MESSAGE_TOPIC.user:
          this.handleMqttUserMessage(
            receivedUserId,
            JSON.parse(data.toString())
          );
          break;
        default:
          this.logService.log(
            'mqtt message received: ',
            topic,
            data.toString()
          );
      }
    };

    this.mqttService.onMessage(onMessageCallback);
  }
}
