-module(t2gp_social_vmq_SUITE).


-compile(nowarn_export_all).
-compile(export_all).

-include_lib("eunit/include/eunit.hrl").

init_per_suite(Config) ->
    t2gp_social_test:configure(),
    t2gp_social_db:create_table(),
    {ok, _Tables} = erlcloud_ddb2:list_tables(),
    t2gp_social_test:start_vmq(),
    cover:start(),
    Config.

end_per_suite(_Config) ->
    erlcloud_ddb2:delete_table(t2gp_social_test:table()),
    t2gp_social_test:stop_vmq(),
    ok.

all() -> [
    test_list_subscriptions,
    test_unsubscribe_all,
    test_unsubscribe,
    test_unsubscribe,
    test_subscribe,
    test_publish,
    test_reduce_results
].

test_list_subscriptions(_) ->
    Topics = [[<<"a">>,<<"b">>,<<"c">>]],
    SubscriberId = {[], <<"test-client">>},
    Username = <<"test-username">>,
    % no subs
    [] = t2gp_social_vmq:list_subscriptions(Username),

    % add subs
    ok = t2gp_social_test:add_subscriber(Username, SubscriberId, Topics, true),
    
    % list w/ subscriber id
    Subs = t2gp_social_vmq:list_subscriptions(SubscriberId),
    [{<<"test-client">>,[<<"a">>,<<"b">>,<<"c">>]}] = Subs,
    
    % list w/ username
    Subs2 = t2gp_social_vmq:list_subscriptions(Username),
    [{<<"test-client">>,[<<"a">>,<<"b">>,<<"c">>]}] = Subs2,
    ok.

test_unsubscribe(_) ->
    Topic1 = [<<"a">>,<<"b">>,<<"c">>],
    Topic2 = [<<"foo">>, <<"bar">>],
    Topics = [Topic1, Topic2],
    SubscriberId = {[], <<"test-client">>},
    Username = <<"test-username">>,
    BadUsername = <<"bad-username">>,
    BadSubscriberId = {[], <<"bad-client">>},
    ok = t2gp_social_test:add_subscriber(Username, SubscriberId, Topics, true),
    
    % test unsubscribe w/ subscriber id
    ok = t2gp_social_vmq:unsubscribe(SubscriberId, Topic1),
    Subs = t2gp_social_vmq:list_subscriptions(Username),
    [{<<"test-client">>,[<<"foo">>, <<"bar">>]}] = Subs,
    
    % test unsubscribe w/ username
    ok = t2gp_social_vmq:unsubscribe(Username, Topic2),
    Subs2 = t2gp_social_vmq:list_subscriptions(Username),
    [] = Subs2,

    ok = t2gp_social_vmq:unsubscribe(BadUsername, Topic1),
    ok = t2gp_social_vmq:unsubscribe(BadSubscriberId, Topic1),
    ok.

test_unsubscribe_all(_) ->
    Topic1 = [<<"a">>,<<"b">>,<<"c">>],
    Topic2 = [<<"foo">>, <<"bar">>],
    Topics = [Topic1, Topic2],
    SubscriberId = {[], <<"test-client">>},
    Username = <<"test-username">>,
    ok = t2gp_social_test:add_subscriber(Username, SubscriberId, Topics, true),

    t2gp_social_vmq:unsubscribe_all(SubscriberId),
    Subs = t2gp_social_vmq:list_subscriptions(Username),
    [] = Subs,
    ok.


test_subscribe(_) ->
    Topic1 = [<<"a">>,<<"b">>,<<"c">>],
    Topic2 = [<<"foo">>, <<"bar">>],
    Topic3 = [<<"foo">>, <<"baz">>],
    Topics = [Topic1],
    ClientId = <<"test-client">>,
    SubscriberId = {[], ClientId},
    Username = <<"test-username">>,
    BadUsername = <<"bad-username">>,
    BadSubscriberId = {[], <<"bad-client">>},
    % no subscriber
    ok = t2gp_social_vmq:subscribe(SubscriberId, Topic2),

    ok = t2gp_social_test:add_subscriber(Username, SubscriberId, Topics, true),

    ok = t2gp_social_vmq:subscribe(SubscriberId, Topic2),
    Subs = t2gp_social_vmq:list_subscriptions(Username),
    [{ClientId, Topic1}, {ClientId, Topic2}] = Subs,

    ok = t2gp_social_vmq:subscribe(Username, Topic3),
    Subs2 = t2gp_social_vmq:list_subscriptions(Username),
    [{ClientId, Topic1}, {ClientId, Topic2}, {ClientId, Topic3}] = Subs2,

    ok = t2gp_social_vmq:subscribe(BadUsername, Topic3),
    ok = t2gp_social_vmq:subscribe(BadSubscriberId, Topic3),
    ok.

test_publish(_) ->
    Topic = [<<"a">>,<<"b">>,<<"c">>],
    Topics = [Topic],
    SubscriberId = {[], <<"test-client">>},
    Username = <<"test-username">>,
    BadUsername = <<"bad-username">>,
    ok = t2gp_social_test:add_subscriber(Username, SubscriberId, Topics, true),

    ok = t2gp_social_vmq:publish(BadUsername, Topic, <<"msg">>, #{}),
    ok = t2gp_social_vmq:publish(Username, Topic, <<"msg">>, #{}),
    ok = t2gp_social_vmq:publish(SubscriberId, Topic, <<"msg">>, #{}),
    ok.


test_reduce_results(_) ->
    ok = t2gp_social_vmq:reduce_results([ok, {ok, status}, ok]),
    {error, not_ok} = t2gp_social_vmq:reduce_results([ok, ok, {error, not_ok}, ok, ok]),
    ok.
