package store

import (
	"context"
	"strconv"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

// SaveChatMessage save a chat message
func (ds *DataStore) SaveChatMessage(ctx context.Context, message *apipub.ChatMessage) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	item, errMarshal := attributevalue.MarshalMap(message)
	if errMarshal != nil {
		log.Error().Err(errMarshal).Msgf("failed to marshal datastore item")
		return errMarshal
	}

	sk := message.PostedTime.Unix()
	item["pk"] = &types.AttributeValueMemberS{Value: message.PK(tenant)}
	item["sk"] = &types.AttributeValueMemberN{Value: strconv.FormatInt(sk, 10)}

	input := dynamodb.PutItemInput{
		Item:      item,
		TableName: &ds.cfg.ChatMessagesTable,
	}
	_, err := ds.ddb.PutItem(ctx, &input)
	if err != nil {
		log.Error().Err(err).Msgf("PutItemInTableWithPkSk() for %s failed", message.PK(tenant))
		return err
	}

	return nil
}
