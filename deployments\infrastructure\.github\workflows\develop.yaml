name: Deploy to develop env

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop
  workflow_dispatch:
    inputs:
      deploy_key:
        description: 'Deploy Key: SECRETS.DEPLOY_KEY'

jobs:
  terraform:
    uses: ./.github/workflows/_terraform.yaml
    with:
      workspace: develop
      accepted_events: '["push","workflow_dispatch"]'
      env_ver_mapping_update: true
    secrets: inherit