-module(t2gp_social_db_SUITE).

-include_lib("erlcloud/include/erlcloud.hrl").
-include_lib("erlcloud/include/erlcloud_aws.hrl").
-include_lib("erlcloud/include/erlcloud_ddb2.hrl").
-include_lib("eunit/include/eunit.hrl").

-include_lib("t2gp_social.hrl").
-include_lib("t2gp_social_test.hrl").

-compile(nowarn_export_all).
-compile(export_all).

init_per_suite(Config) ->
    t2gp_social_test:configure(),
    t2gp_social_db:create_table(),
    {ok, _Tables} = erlcloud_ddb2:list_tables(),
    cover:start(),
    Config.

end_per_suite(_Config) ->
    erlcloud_ddb2:delete_table(t2gp_social_test:table()),
    ok.

init_per_group(vmq, Config) ->
    t2gp_social_test:start_vmq(),
    Config;
init_per_group(_GroupName, Config) ->
    Config.

end_per_group(vmq, _Config) ->
    t2gp_social_test:stop_vmq(),
    ok;
end_per_group(_GroupName, _Config) ->
    ok.

init_per_testcase(_TestCase, Config) ->
    t2gp_social_test:configure(),
    Config.

end_per_testcase(_TestCase, _Config) ->
    ok.

all() ->
    [
        {group, vmq},
        test_get_user,
        test_friends,
        % test_groups,
        test_gen_server,
        test_profile_table,
        test_key
    ].

groups() ->
    Tests = [test_subscriber_id],
    [
        {vmq, [shuffle, sequence], Tests}
    ].

test_get_user(_Config) ->
    Id = <<"test_user_id">>,
    Email = <<"<EMAIL>">>,
    Tenant = <<"unk">>,
    % make sure the user has been deleted
    PK = utils:concat([Tenant, t2gp_social_db:key(<<"#user">>, Id)], binary),
    SK = utils:concat([Tenant, t2gp_social_db:key(<<"#profile">>, Id)], binary),

    t2gp_social_db:delete_item([{?PK, PK}, {?SK, SK}]),
    {ok, User1} = t2gp_social_db:get_user(Tenant, Id),
    User1 = [],
    User2 = [
        {?PK, PK},
        {?SK, SK},
        {?USERID, Id},
        {?EMAIL, Email}
    ],
    {ok, _} = t2gp_social_db:put_item(User2),
    {ok, User3} = t2gp_social_db:get_user(Tenant, Id),
    Id = proplists:get_value(?USERID, User3),
    Email = proplists:get_value(?EMAIL, User3),
    % ?debugVal(Id),
    % ?debugVal(Email),
    % ?debugVal(User3),
    ok.

test_friends(_Config) ->
    User1 = <<"user1">>,
    User2 = <<"user2">>,
    Tenant = <<"unk">>,
    PK = utils:concat([Tenant, "#user#", User1], binary),
    SK = utils:concat([Tenant, "#friend#", User1], binary),
    Friend = [
        {?PK, PK},
        {?SK, SK},
        {?FRIENDID, User2},
        {?INVITEE, User2},
        {?USERID, User1}
    ],
    {ok, _} = t2gp_social_db:put_item(Friend),
    {ok, Friends} = t2gp_social_db:get_friends(Tenant, User1),
    1 = length(Friends),
    Friend2 = lists:nth(1, Friends),
    User2 = proplists:get_value(<<"friendid">>, Friend2),
    User1 = proplists:get_value(<<"userid">>, Friend2),
    ok.

% Todo: cluster set up for this test seems not worthwhile
% test_groups(_Config) ->
%     % Todo: TGP-12380 remove it
%     application:set_env(t2gp_social, redis_url, "redis:6379"),
%     %application:set_env(t2gp_social, redis_primary_node_url, "redis:6379"),
%     % {ok, C} = eredis_cluster:start_link("redis", 6379),
%     eredis_cluster:start(),
%     application:set_env(eredis_cluster, pool_size, 5),
%     application:set_env(eredis_cluster, pool_max_overflow, 10),
%     application:set_env(eredis_cluster, socket_options, [{send_timeout, 6000}]),
%     eredis_cluster:connect([{"redis", 6379}], []),
%     Tenant = <<"unk">>,
%     User1 = <<"user1">>,
%     Prod1 = <<"prod1">>,
%     Group1 = <<"group1">>,
%     Key =  lists:flatten(io_lib:format("~s:user:~s:groups:product:~s", [Tenant, User1, Prod1])),
%     Value = lists:flatten(io_lib:format("~s:prod:~s:group:~s", [Tenant, Prod1, Group1])),
%     eredis_cluster:q(mycluster, ["ZADD", Key, "0", Value]),
%     t2gp_social_db:start_link(),
%     {ok, Groups} = t2gp_social_db:get_groups(Tenant, User1, Prod1),
%     1 = length(Groups),
%     true = lists:member(Group1, Groups),
%     ok.

test_subscriber_id(_Config) ->
    % t2gp_social_test:start_vmq(),
    UserId = <<"b287e655461f4b3085c8f244e394ff7e">>,
    [] = t2gp_social_db:list_subscriber_id(UserId),
    SubscriberId1 = {[], "test-b287e655461f4b3085c8f244e394ff7e-1234"},
    SubscriberId2 = {[], "test-b287e655461f4b3085c8f244e394ff7e-4567"},
    ok = t2gp_social_db:add_subscriber_id(UserId, SubscriberId1),
    ok = t2gp_social_db:add_subscriber_id(UserId, SubscriberId2),
    UserId = t2gp_social_db:get_userid_from_subscriber_id(SubscriberId1),
    [SubscriberId1, SubscriberId2] = t2gp_social_db:list_subscriber_id(UserId),
    ok = t2gp_social_db:del_subscriber_id(SubscriberId1),
    [SubscriberId2] = t2gp_social_db:list_subscriber_id(UserId),
    ok = t2gp_social_db:del_all_subscriptions(UserId),
    [] = t2gp_social_db:list_subscriber_id(UserId),
    ok = t2gp_social_db:del_subscriber_id(SubscriberId1),
    ok.

test_gen_server(_Config) ->
    Pid =
        case t2gp_social_db:start_link() of
            {ok, P} -> P;
            {error, {already_started, P}} -> P;
            _ -> ?_assert(false)
        end,
    Msg = {foo, bar},
    State = {},
    {reply, ok, State} = t2gp_social_db:handle_call(Msg, {Pid, 0}, State),
    {noreply, State} = t2gp_social_db:handle_cast(Msg, State),
    {noreply, State} = t2gp_social_db:handle_info(Msg, State),
    ok = t2gp_social_db:terminate(normal, State),
    {ok, State} = t2gp_social_db:code_change(old, State, {}),
    ok = t2gp_social_db:on_reload(),
    ok.

test_profile_table(_Config) ->
    Table = t2gp_social_db:profile_table(),
    Table = <<"profile_table">>,
    ok.

test_key(_Config) ->
    PK = t2gp_social_db:key(<<"user">>, <<"foobar">>),
    PK = <<"user#foobar">>,
    ok.
