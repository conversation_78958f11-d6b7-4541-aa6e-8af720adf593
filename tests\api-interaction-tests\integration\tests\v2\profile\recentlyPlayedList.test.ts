import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccount, TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';
import { v4 as uuidv4 } from 'uuid';

let usersTwok: TwokAccounts;
const minExpiresIn = 60;
let maxLimit: number = 100;
const maxExpiresIn: number = 2628288;
const maxItemPerPage: number = 100;
let mockUser = new TwokAccount("<EMAIL>", "123", "");

beforeAll(async () => {
  usersTwok = new TwokAccounts(1, ['user']);
  await usersTwok.loginAll({});
});

afterAll(async () => {
  await usersTwok.logoutAll({});
});

afterEach(async () => {
  // user clears entire recently played list
  const r = await socialApi.deleteRecentlyPlayed(usersTwok.acct["user"]);
  socialApi.testStatus(StatusCodes.OK, r);
});

describe(`update recently played list[public v2]${_ds}`, () => {
  describe(`happy cases${_ds}`, () => {
    it.each`
      desc                                   | rplSize
      ${"can add a list of >1 users[happy]"} | ${10}
      ${"can add max-limit users"}           | ${maxLimit}
    `('$desc', async ({rplSize}) => {
      let testCase = {
        description: `add a number of users to the recently played list
  number of users ${rplSize} arbitrarily chosen`,
        expected: "all added users are present in the list; order unimportant"
      };

      // create an expected array of users
      let expectedUserArray = [];

      // create an input list of users with uuid format
      let userArray = [];
      for (let i = 1; i <= rplSize; i++) {
        let numStr = uuidv4();
        userArray.push({userid: numStr});
        expectedUserArray.push(expect.objectContaining({userid: numStr}));
      }

      // add the list of users to his/her Recently Played List
      const r: request.Response = await socialApi.updateRecentlyPlayedList(
        usersTwok.acct["user"],
        { ttl: minExpiresIn, users: userArray }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      const actualRPL = await socialApi.getRecentlyPlayedList(usersTwok.acct["user"], {});

      // verify the Recently Played List contains the input list of users
      const expectedRPL = {
        status: StatusCodes.OK,
        body: {
          items: expect.arrayContaining(expectedUserArray),
        },
      };
      socialApi.expectMore(
        () => {expect(actualRPL).toMatchObject(expectedRPL)},
        testCase,
        {
          resp: actualRPL,
          additionalInfo: {
            "fail reason": "the actual recently played list does not match the input users"
          }
        }
      );
    });

    it('can add max-limit users; adding an extra user replaces the oldest user', async () => {
      // prepare rpl size
      let rplSize = maxLimit + 1;

      let testCase = {
        description: `add max-limit number of users to the recently played list, then add one more user to the recently played list`,
        expected: `the oldest user is replaced and the newer one is added`
      };

      // create an expected array of users (the latest max-limit users)
      let latestUsersArray = [];
      // create an unexpected array of users (the oldest user)
      let oldestUserArray = [];

      // create an input list of users with uuid format
      let userArray = [];
      for (let i = 1; i <= rplSize; i++) {
        let numStr = uuidv4();
        userArray.push({userid: numStr});
        if (i == 1) {
          oldestUserArray.push(expect.objectContaining({userid: numStr}));
        } else {
          latestUsersArray.push(expect.objectContaining({userid: numStr}));
        } 
      }

      // add max-limit number of users to his/her Recently Played List
      let r: request.Response = await socialApi.updateRecentlyPlayedList(
        usersTwok.acct["user"],
        { ttl: minExpiresIn, users: userArray.slice(0, maxLimit) }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // add one more ID to his/her Recently Played List
      r = await socialApi.updateRecentlyPlayedList(
        usersTwok.acct["user"],
        { ttl: minExpiresIn, users: userArray.slice(maxLimit) }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      const actualRPL = await socialApi.getRecentlyPlayedList(usersTwok.acct["user"], {});

      // verify the Recently Played List contains the latest max-limit users
      const expectedRPL = {
        status: StatusCodes.OK,
        body: {
          items: expect.arrayContaining(latestUsersArray),
        },
      };
      socialApi.expectMore(
        () => {expect(actualRPL).toMatchObject(expectedRPL)},
        testCase,
        {
          resp: actualRPL,
          additionalInfo: {
            "fail reason": "the actual recently played list does not match the input users"
          }
        }
      );
      socialApi.expectMore(
        () => {expect(actualRPL.body.items.length).toEqual(maxLimit)},
        testCase,
        {
          resp: actualRPL,
          additionalInfo: {
            "fail reason": "the extra user might be added unexpectedly when recently played reaches the max limit"
          }
        }
      );

      // verify the Recently Played List doesn't contain the oldest user
      const unexpectedRPL = {
        body: {
          items: expect.not.arrayContaining(oldestUserArray),
        },
      };
      socialApi.expectMore(
        () => {expect(actualRPL).toMatchObject(unexpectedRPL)},
        testCase,
        {
          resp: actualRPL,
          additionalInfo: {
            "fail reason": "unexpected oldest user is in the recently played list"
          }
        }
      );
    });
  });

  describe(`corner cases${_ds}`, () => {
    it.each`
      scenario                                 | parameter      | value
      ${"cannot have empty userid string"}     | ${"userid"}    | ${""}
      ${"cannot have expiresIn = minimum - 1"} | ${"ttl"} | ${minExpiresIn - 1}
      ${"cannot have expiresIn = maximum + 1"} | ${"ttl"} | ${maxExpiresIn + 1}
    `('$scenario', async ({parameter, value}) => {
      let testCase = {
        description: `update recently played list with ${parameter} = ${value}`,
        expected: "4xx status"
      };

      let actualResp: request.Response;

      actualResp = await socialApi.updateRecentlyPlayedList(
        usersTwok.acct["user"],
        {
          ...(parameter == 'userid' && { users: [{ userid: value }] }),
          ...(parameter == 'ttl' && { ttl: value })
        }
      );

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });

    it('cannot have invalid token', async () => {
      let testCase = {
        description: 'update recently played list with invalid token',
        expected: "4xx status"
      };

      const actualResp = await socialApi.updateRecentlyPlayedList(mockUser, {});

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNAUTHORIZED)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });

    it('cannot add self', async () => {
      let testCase = {
        description: "update self to recently played list",
        expected: "4xx status"
      };

      const actualResp: request.Response = await socialApi.updateRecentlyPlayedList(
        usersTwok.acct["user"],
        { users: [{ userid: usersTwok.acct["user"].publicId }] }
      );

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });
  });
});

describe(`get recently played list[public v2]${_ds}`, () => {
  beforeAll(async () => {
    // prepare rpl size
    let rplSize = 10;

    // create an input list of users with uuid format
    let userArray = [];
    for (let i = 1; i <= rplSize; i++) {
      let numStr = uuidv4();
      userArray.push({userid: numStr});
    }

    // add the list of users to his/her Recently Played List
    let r: request.Response = await socialApi.updateRecentlyPlayedList(
      usersTwok.acct["user"],
      { users: userArray }
    );
    socialApi.testStatus(StatusCodes.OK, r);
  });

  describe(`happy cases${_ds}`, () => {
    it('can show all items with limit = 0 ', async () => {
      let testCase = {
        description: `get recently played list with limit = 0`,
        expected: "all items are in one page"
      };

      // get the unpaginated recently played list as the expected result
      let r = await socialApi.getRecentlyPlayedList(usersTwok.acct["user"], {});
      let expectedRPL = r.body.items;

      // get the recently played list with limit = 0 as the actual result
      r = await socialApi.getRecentlyPlayedList(usersTwok.acct["user"], { limit: 0 });
      let actualRPL = r.body.items;

      // verify the recently played list with limit = 0 equals the expected list
      socialApi.expectMore(
        () => {expect(actualRPL).toEqual(expectedRPL)},
        testCase,
        {
          resp: r,
          additionalInfo: {
            "fail reason": "get unexpected items because of unexpected pagination page"
          }
        }
      );
    });

    it('can result in the first page with empty next string ', async () => {
      let limitNum = 4;

      let testCase = {
        description: `get recently played list with ${limitNum} items per page and empty next string`,
        expected: "the recently played list remains in the first page"
      };

      // get the recently played list with limit = ${limitNum} as the expected result
      let r = await socialApi.getRecentlyPlayedList(usersTwok.acct["user"], { limit: limitNum });
      let expectedRPL = r.body.items;

      // get the recently played list with limit = ${limitNum} and empty next string as the actual result
      r = await socialApi.getRecentlyPlayedList(
        usersTwok.acct["user"],
        { limit: limitNum, next: "" }
      );
      let actualRPL = r.body.items;

      // verify the recently played list with empty next equals the expected list
      socialApi.expectMore(
        () => {expect(actualRPL).toEqual(expectedRPL)},
        testCase,
        {
          resp: r,
          additionalInfo: {
            "fail reason": "get unexpected items because of unexpected pagination page"
          }
        }
      );
    });
  });

  describe(`corner cases${_ds}`, () => {
    it.each`
      scenario                                    | value
      ${"cannot have limit > max items per page"} | ${maxItemPerPage + 1}
      ${"cannot have empty limit string"}         | ${""}
    `('$scenario', async ({value}) => {
      let testCase = {
        description: `get recently played list with limit = ${value}`,
        expected: "4xx status"
      };

      let actualResp: request.Response;

      actualResp = await socialApi.getRecentlyPlayedList(usersTwok.acct["user"], { limit: value });

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });

    it('cannot have invalid token', async () => {
      let testCase = {
        description: 'get recently played list with invalid token',
        expected: "4xx status"
      };

      const actualResp = await socialApi.getRecentlyPlayedList(mockUser, {});

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNAUTHORIZED)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });
  });
});