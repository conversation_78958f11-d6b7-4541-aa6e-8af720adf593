package api

import (
	"context"
	"errors"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"net/http"
)

// ReportError reports an error
func ReportError(ctx context.Context, err error) {
	// report to datadog
	span, found := tracer.SpanFromContext(ctx)
	if found {
		span.SetTag(ext.Error, err)
	}
}

// ReturnOK 200
func ReturnOK(w http.ResponseWriter, r *http.Request, result interface{}) {
	utils.WriteJsonResponse(w, r, http.StatusOK, result)
}

// ReturnEmptyOK 200 empty json body
func ReturnEmptyOK(w http.ResponseWriter, r *http.Request) {
	utils.WriteJsonResponse(w, r, http.StatusOK, map[string]string{})
}

// ReturnCreated 201
func ReturnCreated(w http.ResponseWriter, r *http.Request, result interface{}) {
	utils.WriteJsonResponse(w, r, http.StatusCreated, result)
}

// ReturnRaw returns raw
func ReturnRaw(w http.ResponseWriter, r *http.Request, statusCode int, contentType string, result []byte) {
	if statusCode != http.StatusOK {
		ReportError(r.Context(), errors.New(string(result)))
	}

	// add request Id to response header
	reqID := middleware.GetReqID(r.Context())
	if reqID != "" {
		w.Header().Add("X-Request-ID", reqID)
	}

	w.Header().Add(constants.KContentType, contentType)
	w.WriteHeader(statusCode)
	w.Write(result)
}
