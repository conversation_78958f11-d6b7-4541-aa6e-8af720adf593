# OS Crud
.DS_Store

# IDE assets
*.code-workspace

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Vim swap files
*.swp

# Python related
__pycache__

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Generated directory from go generate.  just a copy of api for embedding.
cmd/social/api
cmd/social/__debug*
pkg/middleware/oapi/api

build
__debug_bin
.env
_build

main

**/.terraform/**/*
*.log
.netrc
yulius-api.rest

*.dump

docs/api-error-codes.md
node_modules/
data/
*.txt
t2paca.crt
