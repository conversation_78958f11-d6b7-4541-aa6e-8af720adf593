package api

import (
	"net/http"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"

	"github.com/franela/goblin"
	. "github.com/onsi/gomega"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
)

func generateLongString(size int) string {
	str := ""
	for i := 0; i < size; i++ {
		str += "A"
	}

	return str
}

func TestReportUserAbuse(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	reportedUserId := "e12c3df480984141b2f385646b2024fa"
	reportingUserId := "b287e655461f4b3085c8f244e394ff7e"

	request := &apipub.AbuseReport{
		SubjectTitle:         "Subject",
		ReportedUserId:       reportedUserId,
		Os:                   aws.String("Win 11"),
		ReportingCategory:    "Hacking",
		ReportingContentType: aws.String("Use of Unauthorized 3rd Party Programs"),
		ReportingUserLocale:  "zh-CN",
		ReportMessage:        "Unit test",
	}

	report := &apipub.AbuseReport{
		ReportingUserId:          reportingUserId,
		ReportingUserDisplayName: "Reporting User",
		ReportedUserId:           reportedUserId,
		ReportedUserDisplayName:  "Reported User",
		ReportingUserIpAddress:   "",
		ReportingUserLocale:      "zh-CN",
		RequestId:                "",
		ReportingUserAgent:       "",
		ReportingUserProductId:   "",
		ReportMessage:            "Unit test",
		Os:                       aws.String("Win 11"),
		SubjectTitle:             "Subject",
	}
	//
	//reporteeProfile := &apipub.UserProfileResponse{
	//	Userid:      "e12c3df480984141b2f385646b2024fa",
	//	DisplayName: aws.String("Reported User"),
	//}
	//
	//reporterProfile := &apipub.UserProfileResponse{
	//	Userid:      "b287e655461f4b3085c8f244e394ff7e",
	//	DisplayName: aws.String("Reporting User"),
	//}

	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
	g.Describe("ReportUserAbuse", func() {
		g.It("should fail with profile fetch error reportee", func() {
			w, r := AddBodyToRequest(request, User1JWT)
			mock.rc.EXPECT().GetUserProfile(r.Context(), reportedUserId).Return(nil, errs.New(http.StatusInternalServerError, errs.EProfileFetchFailed))

			mock.api.ReportUserAbuse(w, r, "e12c3df480984141b2f385646b2024fa")

			g.Assert(w.Code).Equal(http.StatusNotFound)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusNotFound, errs.EInvalidUserID).Error()))
		})
		g.It("should fail with invalid id reportee", func() {
			w, r := AddBodyToRequest(report, User1JWT)
			mock.rc.EXPECT().GetUserProfile(r.Context(), reportedUserId).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfile(r.Context(), reportedUserId).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfile(r.Context(), reportedUserId).Return(nil, nil)

			mock.api.ReportUserAbuse(w, r, "e12c3df480984141b2f385646b2024fa")

			g.Assert(w.Code).Equal(http.StatusNotFound)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusNotFound, errs.EInvalidUserID).Error()))
		})
		g.It("should fail with bad JWT", func() {
			w, r := AddBodyToRequest(nil, BadJWT)
			mock.api.ReportUserAbuse(w, r, "e12c3df480984141b2f385646b2024fa")

			g.Assert(w.Code).Equal(http.StatusUnauthorized)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnauthorized, errs.EInvalidJWT).Error()))
		})
		g.It("should fail with 5001 ASCII characters", func() {
			large_report := report
			large_report.ReportMessage = generateLongString(5001)
			w, r := AddBodyToRequest(large_report, User1JWT)
			//mock.rc.EXPECT().GetUserProfile(r.Context(), reportedUserId).Return(reporteeProfile, nil)
			//mock.rc.EXPECT().GetUserProfile(r.Context(), reportingUserId).Return(reporterProfile, nil)
			mock.api.ReportUserAbuse(w, r, "e12c3df480984141b2f385646b2024fa")

			g.Assert(w.Code).Equal(http.StatusRequestEntityTooLarge)
		})
	})
}
