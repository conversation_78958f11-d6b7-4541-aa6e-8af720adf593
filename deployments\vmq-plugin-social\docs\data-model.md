# DataModel

```
pk/sk

user#<EMAIL>/user#<EMAIL> {
    userId: XXXXXX
}
user#userId/user#userId {
    userId: string,
    userEmail: email,
    userDisplayName: string,
}
user#userId/friend#userid {
    friendInvitor: userid, # GSKI1
    friendInvitee: userid, # GSKI2
    friendState: string(pending|friends|unfriended|blocked),
    friendInviteDate: timestamp,
    friendAcceptDate: timestamp
}
user#userId/history#userid {

}
presence#userId/presence#userId {
    status: string(online|offline|afk|away)
    extras: {
        gameId:
        level:
        ...
    }
}
group#groupId/group#groupId {
    metadata...
}
group#groupId/member#memberId {
    role: string(admin|member)
}
```
