
# variables

# JWT bearer. See login below
@bear={{login.response.body.$.accessToken}}
@groupid={{createGroup.response.body.$.groupid}}
@userid=e12c3df480984141b2f385646b2024fa
@local=http://localhost:8000
@localTrust=http://localhost:8005
@develop=https://social-service-develop.dev.d2dragon.net
@developTrust=https://social-trusted-develop.dev.d2dragon.net
@prodTrust=https://social-trusted-production.d2dragon.net
@integration=https://social-service-integration.d2dragon.net
@staging=https://social-service-staging-api.d2dragon.net
@loadtesting=https://social-service-loadtesting-api.d2dragon.net
@sso=https://sso.api.2kcoretech.online/sso/v2.0

# change base url for the different endpoints
@baseUrl={{local}}

### login
# @name login
POST {{baseUrl}}/v2/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "D2CTesting",
  "locale": "en-US"
}

# For load testing
# "email": "<EMAIL>"
# "password": "Abc1@345",  

### createTrustGroup
# @name createTrustGroup
POST {{baseUrl}}/v2/server/groups
Content-Type: application/json

###
# get block list
GET {{baseUrl}}/v2/user/blocklist
Authorization: Bearer {{bear}}

####
# get block list with profile sync on
GET {{baseUrl}}/v1/user/blocklist?syncProfile=true
Authorization: Bearer {{bear}}

####
# modify the blocklist
POST {{baseUrl}}/v1/user/blocklist
Authorization: Bearer {{bear}}

{
    "add": [	
      "93f799bc0fee48388482c25bc3eb2d83",
      "63ba55759b3b48faad24e170728f692e",
      "13b451bc00e340fda12ec36c1b7b1f1b",
      "57617037e59249cf84bc44a9cfc2cb10",
      "25b5563df4df4d7ca2898faa17e31cb8",
      "c25d04d7f6184f06aa4b1dc7289a8b5c",
      "821e37c415634d4ea257464a8e93db68",
      "cf17d1efc7a542ec8172dbfa9884bbab",
      "d4f8a50a1c3c4589863139425f05fbb0",
      "d86ac88c7f024918b2651a3eb2d0de7e",
      "dc3ccf0fc044406c8357672041ccaa97",
      "d69a86d3108647ba807e461d960016c7",
      "668a96ad33064ce6892e2c88f898fdcc",
      "ff6a2a77257445ffa84050657f0fe2e8",
      "5d8c5e3fc7d04bd19954184ab926d2f3",
      "423b6225ab294e1ebfe065e958994197",
      "c9960f70463c428aa74e6d0a3b266b5e",
      "214e6291f6b948e980e85f7ed357be80",
      "ed2d85bae0b14d968f32d9a303e0d434",
      "99f9523979b04a33a2d641fc03fd5141"
    ]
}

####
# <EMAIL>
# <EMAIL>
# 

# Get friends list
GET {{baseUrl}}/v1/friends?limit=3&status=friend
Authorization: Bearer {{bear}}

###
#Make a friend
POST {{baseUrl}}/v1/friends
Authorization: Bearer {{bear}}

{
  "userid": "ecb2b8a542e14201b83723521ecdfa39",
  "message": "Hello there!"
}

###
# get subscription
GET http://localhost:8001/dev/subscriptions/ecb2b8a542e14201b83723521ecdfa39

###
# get friend by friendid
GET {{baseUrl}}/v1/friends/02ca1d70eb694d0fb61
Authorization: Bearer {{bear}}

#####
# patch yourself
PATCH  {{baseUrl}}/v1/friends/02ca1d70eb694d0fb61
Authorization: Bearer {{bear}}

{
  "viewed":true
}

####
# @name search2KUsersFullAccountByDisplayName
GET {{baseUrl}}/v1/search/2KUsers?name=ctpshared#44632
Authorization: Bearer {{bear}}

####
GET https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/b4d4c0f7b45a4f988a7e6f245b1ea7b2
Authorization: Basic {{$dotenv APP_BASIC_AUTH}}
Content-Type: application/json

#####
POST https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/batch/first-party
Authorization: Basic {{$dotenv APP_BASIC_AUTH}}
Content-Type: application/json

["0c8136f278404bcd8ec406ca877aeb12"]

####
POST https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/search
Authorization:  Basic {{$dotenv APP_BASIC_AUTH}}
Content-Type: application/json

{
  "type": "accountsByFirstPartyAlias",               
  "criterias": [{
    "firstPartyAlias": "shark"
  }]
}

####
POST https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/search
Authorization: Basic {{$dotenv APP_BASIC_AUTH}}
Content-Type: application/json

{
  "type": "accountsByFirstPartyId",               
  "criterias": [{
    "firstPartyId": "814ab886626d4aa0a3b7641916767afe",
    "onlineServiceType": 2
  }]
}

###
GET https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/b4d4c0f7b45a4f988a7e6f245b1ea7b2/links
Authorization: Basic {{$dotenv APP_BASIC_AUTH}}
Content-Type: application/json

####
POST https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/search
Authorization: Basic {{$dotenv APP_BASIC_AUTH}}
Content-Type: application/json

{
  "type": "accountsById",               
  "criterias": [{
    "accountId": "b4d4c0f7b45a4f988a7e6f245b1ea7b2"
  }, {
    "accountId": "ecb2b8a542e14201b83723521ecdfa39"
  }, {
    "accountId": "b60595c511e04d81b5dd95ee451794d8"
  },
  {
    "accountId": "c16d83dc3b5445f39c0e0fe985dea9b6"
  }
  ]
}

####
# trigger group clean up

GET http://localhost:8001/v1/dev/groups/clean-up


### decode ulid
GET http://localhost:8001/v1/dev/ulids/01FN4APAWQ1CG9ZT0HZ69YK5BX


### get user profile
GET {{baseUrl}}/v1/user/profile
Authorization: Bearer {{bear}}


#### 2k user account me
#####
GET https://sso.api.2kcoretech.online/sso/v2.0/user/accounts/me
Authorization: Bearer {{bear}}
Content-Type: application/json

#####
### get user profile
POST {{baseUrl}}/v1/user/profile/sync
Authorization: Bearer {{bear}}

###
## @name firstPartyInvite
#send first party invite
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships/invite
Authorization: Bearer {{bear}}
Content-Type: application/json

{
    "memberid": "67111838d1ddc824",
    "isFirstPartyInvite":true
}

###
## @name invite
#send party invite
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships
Authorization: Bearer {{bear}}

{
    "groupid":"{{groupid}}",
    "memberid": "e12c3df480984141b2f385646b2024fa",
    "status": "invited"
}

####
# accept invite
PATCH {{baseUrl}}/v1/user/invites/{{groupid}}
Authorization: Bearer {{bear}}

{
    "groupid":"{{groupid}}",
    "memberid": "*****************",
    "onlineServiceType":3,
    "status": "invited"
}

####
# approve request
PATCH {{baseUrl}}/v1/groups/{{groupid}}/memberships/c16d83dc3b5445f39c0e0fe985dea9b6
Authorization: Bearer {{bear}}

{
    "groupid":"{{groupid}}",
    "memberid": "c16d83dc3b5445f39c0e0fe985dea9b6",
    "onlineServiceType":24,
    "approverid": "ecb2b8a542e14201b83723521ecdfa39",
    "status": "approved"
}

####
GET http://AECKGs2qq4hJdXMufpfCEHUeF07mmtKa@localhost:8081/api/v1/session/show

### vmq command
# vmq-admin t2gp publish topic=group/01FQJ2JK7A5V3PKM1A63DPPH27  message="{\"data\":{\"payload\":\"abc\"},\"type\":\"groupControlMessage\"}" user=russel
# vmq-admin t2gp topics user=5fd88043ed47493ab2177ad16c75acd4
# vmq-admin t2gp subscribe topic=group/01FQJ2JK7A5V3PKM1A63DPPH27 userid=5fd88043ed47493ab2177ad16c75acd4
# vmq-admin t2gp unsubscribe topic=group/01FQJ2JK7A5V3PKM1A63DPPH27 userid=5fd88043ed47493ab2177ad16c75acd4

####
# mqtt
GET http://AECKGs2qq4hJdXMufpfCEHUeF07mmtKa@localhost:6000/api/v1/t2gp/version

###
# discovery
GET https://stg.discovery.api.2kcoretech.online/discovery/v1/services
Content-Type: application/json
Authorization: Application f0f4789228e64407bb2bbb2a978fa68c

###
# DNA login
# staging
#POST https://e7bcfa5048776c67402cfb7e0cfa106f.my.2k.com/sso/v2.0/auth/tokens
# prod
POST https://sso.api.2kcoretech.online/sso/v2.0/auth/tokens
Content-Type: application/json
Authorization: Application {{$dotenv APP_ID}}

{
  "locale": "en-US",
  "accountType": "full",
  "credentials": {
    "type": "emailPassword",
    "email": "<EMAIL>",
    "password": "*******"
  }
}

#####
### Search by platform id
GET {{baseUrl}}/v1/friends/search?q=*****************,*****************&platform=steam&type=accountsByFirstPartyId
Authorization: Bearer {{bear}}

#####
### Search by DNA account id
GET {{baseUrl}}/v1/friends/search?q=********************************,********************************&platform=steam&type=accountsById
Authorization: Bearer {{bear}}

#####
### Search by platform alias
GET {{baseUrl}}/v1/friends/search?q=yulius,yulius&platform=steam
Authorization: Bearer {{bear}}

#####
### Search email
GET {{baseUrl}}/v1/friends/search?q=<EMAIL>
Authorization: Bearer {{bear}}

#####
### Report abuse
POST {{baseUrl}}/v1/user/6605503594e741769afe5fb4f51cd3a7/report
Content-Type: application/json
Authorization: Bearer {{bear}}

{
  "reason": "bad-displayname",
  "message": "vulgar or bad display name"
}

####
### Kick a team member with a reason
DELETE {{baseUrl}}/v1/groups/{{groupid}}/members/{{userid}}?reason=I don't like you
Authorization: Bearer {{bear}}

####
### Kick a team member without a reason
DELETE {{baseUrl}}/v1/groups/{{groupid}}/members/{{userid}}
Authorization: Bearer {{bear}}

###
# @name createGroup
POST {{baseUrl}}/v2/groups
Authorization: Bearer {{bear}}
Content-Type: application/json

{
    "maxMembers": 5,
    "joinRequestAction": "auto-approve"
}

###
# @name getGroup
GET {{baseUrl}}/v1/groups/{{groupid}}
Authorization: Bearer {{bear}}
Content-Type: application/json

###
# @name addGroupMemberMeta
PUT {{baseUrl}}/v1/groups/{{groupid}}/members/2017e9305ccc4e5781d076403c1b6725/meta
Authorization: Bearer {{bear}}
Content-Type: application/json

{
  "meta": {
    "test": "pending",
    "something": "nothing"
  },
  "timestamp": "2024-12-09T20:05:30Z"
}

###
# @name updateGroupMemberMeta
PUT {{baseUrl}}/v1/groups/{{groupid}}/members/2017e9305ccc4e5781d076403c1b6725/meta
Authorization: Bearer {{bear}}
Content-Type: application/json

{
  "meta": {
    "test": "done",
    "foo": "foo"
  },
  "timestamp": "2024-12-09T21:05:30Z"
}

@trustedBear={{trustedLogin.response.body.$.accessToken}}
@trustedGroupId={{trustedCreateGrouop.response.body.$.groupid}}
@trustedRandomGroupId={{trustedCreateGrouopWithRandomJWT.response.body.$.groupid}}

###
# @name trustedLogin
GET {{localTrust}}/v2/server/auth/token
Authorization: Basic ****************************************************************************************
Content-Type: application/json

###
# @name trustedCreateGrouop
POST {{developTrust}}/v2/server/groups
Authorization: Bearer {{trustedBear}}
Content-Type: application/json

{
  "joinRequestAction":"auto-approve",
  "canCrossPlay":true,
  "maxMembers":2,
  "canMembersInvite":false,
  "groupLeader":"********************************",
  "onlineServiceType":0
}

###
# @name trustedCreateGrouopWithRandomJWT
POST {{developTrust}}/v2/server/groups
Authorization: Bearer ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
Content-Type: application/json

{
  "joinRequestAction":"auto-approve",
  "canCrossPlay":true,
  "maxMembers":2,
  "canMembersInvite":false,
  "groupLeader":"********************************",
  "onlineServiceType":0
}

###
# @name trustedDeleteGrouop
DELETE {{localTrust}}/v2/server/groups/{{trustedGroupId}}
Authorization: Bearer {{trustedBear}}
Content-Type: application/json

###
# @name trustedDeleteGrouopWithRandomJWT
DELETE {{localTrust}}/v2/server/groups/{{trustedRandomGroupId}}
Authorization: Bearer {{trustedBear}}
Content-Type: application/json
