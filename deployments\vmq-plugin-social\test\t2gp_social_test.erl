-module(t2gp_social_test).

-include_lib("t2gp_social_test.hrl").
-include_lib("eunit/include/eunit.hrl").

-compile(nowarn_export_all).
-compile(export_all).

configure() ->
    application:ensure_all_started(lager),
    lager:set_loglevel(lager_console_backend, none),
    application:ensure_all_started(opencensus),
    application:ensure_all_started(hackney),
    os:putenv("DD_ENV", "local"),
    os:putenv("SOCIAL_DYNAMODB_ENDPOINT", "http://*************:8000"),
    os:putenv("SOCIAL_ELASTICACHE_URL", "192.168.123.4:6379"),
    DiscoveryURL = os:getenv("SOCIAL_DISCOVERY_URL", "https://discovery.api.2kcoretech.online"),
    AppID = os:getenv("SOCIAL_APP_ID"),
    AppBasicAuth = os:getenv("SOCIAL_APP_BASIC_AUTH"),
    APIKey = os:getenv("SOCIAL_API_KEY"),
    application:set_env(t2gp_social, discovery_url, DiscoveryURL),
    application:set_env(t2gp_social, app_id, AppID),
    application:set_env(t2gp_social, app_basic_auth, AppBasicAuth),
    application:set_env(t2gp_social, api_key, APIKey),
    application:set_env(t2gp_social, elasticache_url, "localhost:6379"),

    TableName = ?TABLE,
    application:ensure_all_started(erlcloud),
    application:set_env(t2gp_social, profile_table, TableName),
    application:set_env(t2gp_social, dynamodb_endpoint, "http://*************:8000"),
    Key = "local",
    Secret = "local",
    application:set_env(t2gp_social, aws_access_key, Key),
    application:set_env(t2gp_social, aws_secret, Secret),
    erlcloud_ddb2:configure(Key, Secret, "*************", 8000, "http://").

table() ->
    ?TABLE.

%% From vmq_test_utils.erl
%% https://github.com/vernemq/vernemq/blob/master/apps/vmq_server/test/vmq_test_utils.erl#L10
start_vmq() ->
    start_vmq(application:get_env(t2gp_social, test_vmq_started, false)).

start_vmq(false) ->
    %% we only want to start it once.  once started, leave it alone
    os:cmd(os:find_executable("epmd") ++ " -daemon"),
    NodeName = list_to_atom("vmq_server-" ++ integer_to_list(erlang:phash2(os:timestamp()))),
    ok = maybe_start_distribution(NodeName),
    Datadir = "/tmp/vernemq-test/data/" ++ atom_to_list(node()),
    os:cmd("rm -rf " ++ Datadir),
    application:load(vmq_swc),
    application:set_env(vmq_swc, db_backend, leveldb),
    application:set_env(vmq_swc, data_dir, Datadir),
    application:set_env(vmq_swc, metadata_root, Datadir),
    application:load(vmq_server),
    PrivDir = code:priv_dir(vmq_server),
    application:set_env(vmq_server, listeners, [{vmq, [{{{0, 0, 0, 0}, random_port()}, []}]}]),
    application:set_env(vmq_server, ignore_db_config, true),
    application:load(vmq_plugin),
    application:set_env(vmq_plugin, default_schema_dir, [PrivDir]),
    application:set_env(vmq_server, metadata_impl, vmq_swc),
    application:load(vmq_generic_msg_store),
    application:set_env(vmq_generic_msg_store, msg_store_engine, vmq_storage_engine_leveldb),
    LogDir = "log/log." ++ atom_to_list(node()),
    application:stop(lager),
    application:set_env(lager, handlers, [
        {lager_file_backend, [
            {file, LogDir ++ "/console.log"},
            {level, info},
            {size, 10485760},
            {date, "$D0"},
            {count, 5}
        ]},
        {lager_file_backend, [
            {file, LogDir ++ "/error.log"},
            {level, error},
            {size, 10485760},
            {date, "$D0"},
            {count, 5}
        ]}
    ]),
    application:ensure_all_started(lager),
    vmq_server:start(),
    disable_all_plugins(),
    application:set_env(t2gp_social, test_vmq_started, true),
    ok;
start_vmq(true) ->
    ok.

random_port() ->
    10000 + (erlang:phash2(node()) rem 10000).

stop_vmq() ->
    %% We never stop the VMQ server in the tests.  We just start it once and leave it alone

    % disable_all_plugins(),
    % vmq_metrics:reset_counters(),
    % vmq_server:stop(),
    % vmq_swc:stop(),
    % application:unload(vmq_swc),
    % application:unload(vmq_server),
    % Datadir = "/tmp/vernemq-test/data/" ++ atom_to_list(node()),
    % _ = [eleveldb:destroy(Datadir ++ "/meta/" ++ integer_to_list(I), [])
    %      || I <- lists:seq(0, 11)],
    % _ = [eleveldb:destroy(Datadir ++ "/msgstore/" ++ integer_to_list(I), [])
    %      || I <- lists:seq(0, 11)],
    % eleveldb:destroy(Datadir ++ "/trees", []),
    ok.

disable_all_plugins() ->
    {ok, Plugins} = vmq_plugin_mgr:get_plugins(),
    %% Disable App Pluginns

    %({application, vmq_plumtree, _}) ->
    lists:foreach(
        fun
            % don't disable metadata plugin
            %   ignore;
            ({application, vmq_swc, _}) ->
                ignore;
            ({application, vmq_generic_msg_store, _}) ->
                % don't disable message store plugin
                ignore;
            ({application, App, _Hooks}) ->
                vmq_plugin_mgr:disable_plugin(App);
            (_ModPlugins) ->
                ignore
        end,
        Plugins
    ),
    %% Disable Mod Plugins
    lists:foreach(
        fun
            ({_, vmq_lvldb_store, _, _}) ->
                ignore;
            (P) ->
                vmq_plugin_mgr:disable_plugin(P)
        end,
        vmq_plugin:info(all)
    ).

maybe_start_distribution(Name) ->
    case ets:info(sys_dist) of
        undefined ->
            %% started without -sname or -name arg
            {ok, _} = net_kernel:start([Name, shortnames]),
            ok;
        _ ->
            ok
    end.

reset_tables() ->
    _ = [reset_tab(T) || T <- [subscriber, config, retain]],
    %% it might be possible that a cached retained message
    %% isn't yet persisted in plumtree
    ets:delete_all_objects(vmq_retain_srv),
    ok.

reset_tab(Tab) ->
    vmq_metadata:fold(
        {vmq, Tab},
        fun({Key, V}, _) ->
            case V =/= '$deleted' of
                true ->
                    vmq_metadata:delete({vmq, Tab}, Key);
                _ ->
                    ok
            end
        end,
        ok
    ).

get_suite_rand_seed() ->
    %% To set the seed when running a test from the command line,
    %% create a config.cfg file containing `{seed, {x,y,z}}.` with the
    %% desired seed in there. Then run the tests like this:
    %%
    %% `./rebar3 ct --config config.cfg ...`
    Seed =
        case ct:get_config(seed, undefined) of
            undefined ->
                os:timestamp();
            X ->
                X
        end,
    io:format(user, "Suite random seed: ~p~n", [Seed]),
    {seed, Seed}.

seed_rand(Config) ->
    Seed =
        case proplists:get_value(seed, Config, undefined) of
            undefined ->
                throw("No seed found in Config");
            S ->
                S
        end,
    rand:seed(exsplus, Seed).

rand_bytes(N) ->
    L = [rand:uniform(256) - 1 || _ <- lists:seq(1, N)],
    list_to_binary(L).

get_cluster_members(Node) ->
    rpc:call(Node, vmq_plugin, only, [cluster_members, []]).

pmap(F, L) ->
    Parent = self(),
    lists:foldl(
        fun(X, N) ->
            spawn_link(fun() ->
                Parent ! {pmap, N, F(X)}
            end),
            N + 1
        end,
        0,
        L
    ),
    L2 = [
        receive
            {pmap, N, R} -> {N, R}
        end
     || _ <- L
    ],
    {_, L3} = lists:unzip(lists:keysort(1, L2)),
    L3.

wait_until(Fun, Retry, Delay) when Retry > 0 ->
    Res = Fun(),
    case Res of
        true ->
            ok;
        _ when Retry == 1 ->
            {fail, Res};
        _ ->
            timer:sleep(Delay),
            wait_until(Fun, Retry - 1, Delay)
    end.

wait_until_left(Nodes, LeavingNode) ->
    wait_until(
        fun() ->
            lists:all(
                fun(X) -> X == true end,
                pmap(
                    fun(Node) ->
                        not lists:member(
                            LeavingNode,
                            get_cluster_members(Node)
                        )
                    end,
                    Nodes
                )
            )
        end,
        60 * 2,
        500
    ).

wait_until_joined(Nodes, ExpectedCluster) ->
    wait_until(
        fun() ->
            lists:all(
                fun(X) -> X == true end,
                pmap(
                    fun(Node) ->
                        lists:sort(ExpectedCluster) ==
                            lists:sort(get_cluster_members(Node))
                    end,
                    Nodes
                )
            )
        end,
        60 * 2,
        500
    ).

wait_until_ready(Nodes) ->
    wait_until(
        fun() ->
            NodeStates = [rpc:call(N, vmq_cluster, is_ready, []) || N <- Nodes],
            lists:all(fun(Bool) -> Bool end, NodeStates)
        end,
        60 * 100,
        100
    ).

wait_until_offline(Node) ->
    wait_until(
        fun() ->
            pang == net_adm:ping(Node)
        end,
        60 * 2,
        500
    ).

wait_until_disconnected(Node1, Node2) ->
    wait_until(
        fun() ->
            pang == rpc:call(Node1, net_adm, ping, [Node2])
        end,
        60 * 2,
        500
    ).

wait_until_connected(Node1, Node2) ->
    wait_until(
        fun() ->
            pong == rpc:call(Node1, net_adm, ping, [Node2])
        end,
        60 * 2,
        500
    ).

start_node(Name, Config, Case) ->
    ct:pal("Start Node ~p for Case ~p~n", [Name, Case]),
    CodePath = lists:filter(fun filelib:is_dir/1, code:get_path()),
    %% have the slave nodes monitor the runner node, so they can't outlive it
    NodeConfig = [
        {boot_timeout, 50000000000000},
        {monitor_master, true},
        %% smp for the eleveldb god
        {erl_flags, "-smp"}
    ],
    VmqServerPrivDir = code:priv_dir(vmq_server),
    ct:log("Starting node ~p with Opts = ~p", [Name, NodeConfig]),
    case ct_slave:start(Name, NodeConfig) of
        {ok, Node} ->
            true = rpc:block_call(Node, code, set_path, [CodePath]),
            PrivDir = proplists:get_value(priv_dir, Config),
            NodeDir = filename:join([PrivDir, Node, Case]),
            ok = rpc:call(Node, application, load, [vmq_server]),
            ok = rpc:call(Node, application, load, [vmq_plugin]),
            ok = rpc:call(Node, application, load, [vmq_generic_msg_store]),
            % ok = rpc:call(Node, application, load, [plumtree]),
            ok = rpc:call(Node, application, load, [vmq_swc]),
            ok = rpc:call(Node, application, load, [lager]),
            ok = rpc:call(Node, application, set_env, [vmq_server, metadata_impl, vmq_swc]),
            ok = rpc:call(Node, application, set_env, [vmq_server, max_drain_time, 5000]),
            ok = rpc:call(Node, application, set_env, [vmq_server, max_msgs_per_drain_step, 40]),
            ok = rpc:call(Node, application, set_env, [vmq_server, mqtt_connect_timeout, 12000]),
            ok = rpc:call(Node, application, set_env, [vmq_server, coordinate_registrations, true]),
            ok = rpc:call(Node, application, set_env, [
                vmq_server, allow_register_during_netsplit, true
            ]),
            ok = rpc:call(Node, application, set_env, [lager, log_root, NodeDir]),
            ok = rpc:call(Node, application, set_env, [vmq_swc, data_dir, NodeDir]),
            ok = rpc:call(Node, application, set_env, [vmq_swc, metadata_root, NodeDir ++ "/meta/"]),
            ok = rpc:call(Node, application, set_env, [
                vmq_server,
                listeners,
                [{vmq, [{{{127, 0, 0, 1}, random_port(Node)}, []}]}]
            ]),
            ok = rpc:call(Node, application, set_env, [
                vmq_generic_msg_store,
                msg_store_opts,
                [{store_dir, NodeDir ++ "/msgstore"}]
            ]),
            ok = rpc:call(Node, application, set_env, [vmq_plugin, wait_for_proc, vmq_server_sup]),
            ok = rpc:call(Node, application, set_env, [vmq_plugin, plugin_dir, NodeDir]),
            ok = rpc:call(Node, application, set_env, [
                vmq_plugin, default_schema_dir, [VmqServerPrivDir]
            ]),
            {ok, _} = rpc:call(
                Node,
                application,
                ensure_all_started,
                [vmq_server]
            ),
            %{ok, _} = rpc:call(Node, application, ensure_all_started, [vmq_swc]),
            ok = wait_until(
                fun() ->
                    case rpc:call(Node, vmq_plugin, only, [cluster_members, []]) of
                        {error, no_matching_hook} ->
                            false;
                        Members when is_list(Members) ->
                            ct:pal("CLUSTER MEMBERS: ~p~n", [Members]),
                            case rpc:call(Node, erlang, whereis, [vmq_server_sup]) of
                                undefined ->
                                    false;
                                P when is_pid(P) ->
                                    true
                            end
                    end
                end,
                60,
                500
            ),
            Node;
        {error, already_started, Node} ->
            {ok, _NodeName} = ct_slave:stop(Name),
            wait_until_offline(Node),
            start_node(Name, Config, Case)
    end.

partition_cluster(ANodes, BNodes) ->
    pmap(
        fun({Node1, Node2}) ->
            true = rpc:call(Node1, erlang, set_cookie, [Node2, canttouchthis]),
            true = rpc:call(Node1, erlang, disconnect_node, [Node2]),
            ok = wait_until_disconnected(Node1, Node2)
        end,
        [{Node1, Node2} || Node1 <- ANodes, Node2 <- BNodes]
    ),
    ok.

heal_cluster(ANodes, BNodes) ->
    GoodCookie = erlang:get_cookie(),
    pmap(
        fun({Node1, Node2}) ->
            true = rpc:call(Node1, erlang, set_cookie, [Node2, GoodCookie]),
            ok = wait_until_connected(Node1, Node2)
        end,
        [{Node1, Node2} || Node1 <- ANodes, Node2 <- BNodes]
    ),
    ok.

ensure_cluster(Config) ->
    [{Node1, _} | OtherNodes] = Nodes = proplists:get_value(nodes, Config),
    [
        begin
            {ok, _} = rpc:call(Node, vmq_server_cmd, node_join, [Node1])
        end
     || {Node, _} <- OtherNodes
    ],
    {NodeNames, _} = lists:unzip(Nodes),
    Expected = lists:sort(NodeNames),
    ok = vmq_cluster_test_utils:wait_until_joined(NodeNames, Expected),
    [
        ?assertEqual(
            {Node, Expected}, {Node, lists:sort(vmq_cluster_test_utils:get_cluster_members(Node))}
        )
     || Node <- NodeNames
    ],
    vmq_cluster_test_utils:wait_until_ready(NodeNames),
    ok.

random_port(Node) ->
    10000 + (erlang:phash2(Node) rem 10000).

wait_until_true(_, 0) ->
    false;
wait_until_true(Fun, N) ->
    case Fun() of
        true ->
            true;
        false ->
            timer:sleep(100),
            wait_until_true(Fun, N - 1)
    end.

add_subscriber(Username, SubscriberId, Topics, Clean) ->
    t2gp_social_db:add_subscriber_id(Username, SubscriberId),
    {ok, _, _} = vmq_queue_sup_sup:start_queue(SubscriberId, Clean),
    lists:foreach(
        fun(Topic) ->
            V0Subs = [{Topic, 1, node()}],
            vmq_metadata:put({vmq, subscriber}, SubscriberId, V0Subs),
            true = t2gp_social_test:wait_until_true(
                fun() ->
                    %% this should setup a queue
                    C1 = vmq_reg:get_queue_pid(SubscriberId) =/= not_found,
                    %% return proper subscription
                    V1Subs = [{node(), false, [{Topic, 1}]}],
                    C2 = V1Subs == vmq_reg:subscriptions_for_subscriber_id(SubscriberId),
                    %% routing index must be updated
                    C3 =
                        [{SubscriberId, 1}] ==
                            vmq_reg_view:fold(
                                vmq_reg_trie,
                                SubscriberId,
                                Topic,
                                fun(V, _, Acc) -> [V | Acc] end,
                                []
                            ),
                    C1 and C2 and C3
                end,
                10
            ),
            ok
        end,
        Topics
    ),
    ok.

login_hs256(Email, Password) ->
    login(Email, Password, <<"e3c64ee90b8044d2ba35f12ea161fae4">>).

login_rs256(Email, Password) ->
    login(Email, Password, <<"0730770bdf204acc8e78851f67990847">>).

login(Email, Password, AppID) ->
    application:ensure_all_started(hackney),
    URL = <<"https://sso.api.2kcoretech.online/sso/v2.0/auth/tokens">>,
    ReqBody = [
        {<<"locale">>, <<"en-US">>},
        {<<"accountType">>, <<"full">>},
        {<<"credentials">>, [
            {<<"type">>, <<"emailPassword">>},
            {<<"email">>, Email},
            {<<"password">>, Password}
        ]}
    ],
    Headers = [
        {<<"Content-Type">>, <<"application/json">>},
        {<<"Authorization">>, <<<<"Application ">>/binary, AppID/binary>>}
    ],
    Payload = jsx:encode(ReqBody),
    {ok, 200, _RespHeaders, RespBody} = hackney:request(post, URL, Headers, Payload, [with_body]),
    Resp = jsx:decode(RespBody),
    JWT = proplists:get_value(<<"accessToken">>, Resp, <<>>),
    {ok, JWT}.

login_pd(Email, Password) ->
    application:ensure_all_started(hackney),
    URL = <<"https://pd-backoffice.d2dragon.net/oauth2/token">>,
    Headers = [
        {<<"Content-Type">>, <<"application/x-www-form-urlencoded">>},
        {<<"Authorization">>,
            <<"Basic MmU1NjlhMzgtZTdiNi00ZDlhLTk1YzEtOGI3M2FkYmYzYzgwOnNja0ZKZFhsdmh1Nm1xcTNpQ0lQNFEyQjY1TVY0WDF2aGJBUlY0aENzZE0=">>}
    ],
    ReqBody = [
        {<<"client_id">>, <<"2e569a38-e7b6-4d9a-95c1-8b73adbf3c80">>},
        {<<"grant_type">>, <<"password">>},
        {<<"loginId">>, Email},
        {<<"password">>, Password}
    ],
    Payload = uri_string:compose_query(ReqBody),
    {ok, 200, _RespHeaders, RespBody} = hackney:request(post, URL, Headers, Payload, [with_body]),
    Resp = jsx:decode(RespBody),
    JWT = proplists:get_value(<<"access_token">>, Resp, <<>>),
    {ok, JWT}.
