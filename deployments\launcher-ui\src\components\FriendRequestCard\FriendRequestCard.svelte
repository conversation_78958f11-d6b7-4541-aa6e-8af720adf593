<script lang="ts">
  import { onMount } from 'svelte';
  import { SVGAccept, SVGCancel, SVGSteam } from '../../assets/icons';
  import {
    EVENT_FRIEND_REQUEST_ACCEPT,
    EVENT_FRIEND_REQUEST_ACCEPTED,
    EVENT_FRIEND_REQUEST_ACCEPT_ERROR,
    EVENT_FRIEND_REQUEST_DELETE,
    EVENT_FRIEND_REQUEST_DELETED,
    EVENT_FRIEND_REQUEST_DELETE_ERROR,
    EVENT_FRIEND_REQUEST_VIEWED,
  } from '../../constant';
  import { useTransportService } from '../../hooks';
  import { Avatar } from '../Avatar';
  import { LoadingSpinner } from '../LoadingSpinner';
  import { Tooltip } from '../Tooltip';

  export let displayName = '';
  export let platformDisplayName = '';
  export let platform = '';
  export let friendId = '';
  export let message = '';
  export let received = false;
  export let cancelTooltip = '';
  export let acceptTooltip = '';

  let accepting = false;
  let cancelling = false;
  const transportService = useTransportService();

  const onCancelClicked = () => {
    cancelling = true;
    transportService.publishEvent(EVENT_FRIEND_REQUEST_DELETE, friendId);
  };

  const onAcceptClicked = () => {
    accepting = true;
    transportService.publishEvent(EVENT_FRIEND_REQUEST_ACCEPT, friendId);
  };

  onMount(() => {
    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_DELETED, () => {
      cancelling = false;
    });

    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_DELETE_ERROR, () => {
      cancelling = false;
    });

    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_ACCEPTED, () => {
      accepting = false;
    });

    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_ACCEPT_ERROR, () => {
      accepting = false;
    });

    transportService.publishEvent(EVENT_FRIEND_REQUEST_VIEWED, friendId);
  });

  $: fromSteam = platform === 'steam';
</script>

<style>
  .request-card {
    min-height: 3.75rem;
    background-color: rgba(49, 49, 49, 1);
    display: flex;
    align-items: center;
    padding: 0.625rem 1rem;
    width: 100%;
    box-sizing: border-box;
    border-radius: 0.24rem;
    margin-bottom: 0.5rem;
  }

  .request-card .detail {
    color: rgba(255, 255, 255, 1);
    margin-left: 1rem;
    display: flex;
    flex-direction: column;
    max-width: 12rem;
  }

  .request-card .detail .displayName {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .request-card .action {
    margin-inline-start: auto;
    display: flex;
  }

  .request-card .detail .platform {
    font-size: 0.875rem;
    opacity: 0.6;
    display: flex;
    align-items: center;
  }

  .request-card .detail .platform :global(svg) {
    width: 12px;
    height: 12px;
    margin-inline-end: 0.4rem;
  }

  .request-card .action .icon:nth-child(2) {
    margin-inline-start: 0.8125rem !important;
  }
  .request-card .action .icon:hover {
    cursor: pointer;
  }
</style>

<div class="request-card">
  <Avatar size="32px" showPresence="{false}" />
  <div class="detail">
    <span class="displayName">{displayName} {message || ''} </span>
    <span class="platform">
      {#if fromSteam}
        <SVGSteam />
      {/if}
      {platformDisplayName}
    </span>
  </div>
  <div class="action">
    {#if received}
      <span class="icon" on:click="{onAcceptClicked}">
        <Tooltip value="{acceptTooltip}">
          {#if accepting}
            <LoadingSpinner size="{24}" />
          {:else}
            <SVGAccept />
          {/if}
        </Tooltip>
      </span>
    {/if}

    <span class="icon" on:click="{onCancelClicked}">
      <Tooltip value="{cancelTooltip}">
        {#if cancelling}
          <LoadingSpinner size="{24}" />
        {:else}
          <SVGCancel />
        {/if}
      </Tooltip>
    </span>
  </div>
</div>
