package apipub

import (
	"testing"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func TestProfile(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("Profile", func() {
		g.It("should return expected values", func() {
			userid := utils.GenerateRandomDNAID()
			p := &UserProfileResponse{
				Userid: userid,
			}

			g.<PERSON>(p.PK("test")).Equal("test#user#" + userid)
			g.<PERSON>(p.SK("test")).Equal("test#profile#" + userid)
			g.<PERSON>(p.<PERSON>("test")).Equal("test:user:{" + userid + "}")
		})
	})
}
