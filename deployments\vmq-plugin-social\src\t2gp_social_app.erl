%%====================================================================
%% @doc t2gp_social_app public API
%% @end
%%====================================================================

-module(t2gp_social_app).

-behaviour(application).

-include_lib("erlcloud/include/erlcloud_aws.hrl").

-export([
    start/2,
    stop/1,
    reload/0
]).

-export([
    get_config/3,
    get_sm_secrets/1,
    get_sm_env_name/1
]).

-spec get_config(string(), list(), any()) -> any().
get_config(Key, Secrets, DefaultValue) ->
    case utils:get_env() of
        "local" ->
            os:getenv(Key, DefaultValue);
        _ ->
            %% per <PERSON>'s request, env vars are a higher priority
            case os:getenv(Key) of
                false ->
                    proplists:get_value(Key, Secrets, DefaultValue);
                EnvValue ->
                    EnvValue
            end
    end.

-spec get_sm_env_name(binary()) -> binary().
get_sm_env_name(EnvIn) ->
    Env =
        case EnvIn of
            <<"develop">> -> <<"develop">>;
            <<"integration">> -> <<"integration">>;
            <<"loadtesting">> -> <<"loadtesting">>;
            <<"staging">> -> <<"staging">>;
            <<"cert">> -> <<"cert">>;
            <<"production">> -> <<"production">>;
            %% DEFAULT_SM_TARGET is used to override which secrets manager to look at
            _ -> list_to_binary(os:getenv("DEFAULT_SM_TARGET", "develop"))
        end,
    SecretsName = <<<<"social/mqtt/">>/binary, Env/binary>>,
    SecretsName.

-spec get_sm_secrets(string()) -> list().
get_sm_secrets("local") ->
    [];
get_sm_secrets(EnvIn) ->
    SecretsName = get_sm_env_name(list_to_binary(EnvIn)),
    Config = erlcloud_aws:default_config(),
    ConfigSM = Config#aws_config{
        http_client = hackney,
        sm_scheme = "https://",
        sm_port = 443
    },
    erlcloud_aws:default_config_override(ConfigSM),
    lager:info("setting aws sm url ~p", [ConfigSM#aws_config.sm_host]),
    {ok, Response} = erlcloud_sm:get_secret_value(SecretsName, [], ConfigSM),
    SecretsBin = jsx:decode(proplists:get_value(<<"SecretString">>, Response, <<"{}">>)),
    Secrets = [{binary_to_list(K), binary_to_list(V)} || {K, V} <- SecretsBin],
    Secrets.

-spec start(application:start_type(), term()) -> {ok, pid()} | {ok, pid(), map()} | {error, term()}.
start(_StartType, _StartArgs) ->
    % setup aws access
    application:ensure_all_started(hackney),
    application:ensure_all_started(ercloud),

    % update application config based on env variable or aws secrets manager
    Secrets = get_sm_secrets(os:getenv("DD_ENV", "develop")),
    application:set_env(t2gp_social, app_id, get_config("SOCIAL_APP_ID", Secrets, undefined)),
    application:set_env(
        t2gp_social, app_secret, get_config("SOCIAL_APP_SECRET", Secrets, undefined)
    ),
    application:set_env(
        t2gp_social, app_basic_auth, get_config("SOCIAL_APP_BASIC_AUTH", Secrets, undefined)
    ),
    application:set_env(
        t2gp_social,
        discovery_url,
        get_config("SOCIAL_DISCOVERY_URL", Secrets, "https://discovery.api.2kcoretech.online")
    ),
    application:set_env(
        t2gp_social,
        jwt_allow_alg_none,
        get_config("SOCIAL_JWT_ALLOW_ALG_NONE", Secrets, "off") == "on"
    ),
    application:set_env(
        t2gp_social, profile_table, get_config("SOCIAL_PROFILE_TABLE", Secrets, undefined)
    ),
    application:set_env(
        t2gp_social, dynamodb_endpoint, get_config("SOCIAL_DYNAMODB_ENDPOINT", Secrets, undefined)
    ),
    application:set_env(
        t2gp_social,
        elasticache_url,
        get_config("SOCIAL_ELASTICACHE_URL", Secrets, "localhost:6379")
    ),
    application:set_env(
        t2gp_social,
        pd_private_url,
        get_config("SOCIAL_PD_PRIVATE_URL", Secrets, "https://pd-backoffice-dev.d2dragon.net")
    ),
    application:set_env(
        t2gp_social,
        rs_config_url,
        get_config("SOCIAL_RS_CONFIG_URL", Secrets, "s3://t2gp-pd-store-api/rs_auth_env/config.gz")
    ),
    application:set_env(
        t2gp_social, predefined_user, get_config("SOCIAL_PREDEFINED_USER", Secrets, "restuser")
    ),

    % register metrics w/ vmq
    application:set_env(t2gp_social, vmq_metrics_mfa, {t2gp_social_metrics, metrics, []}),

    % Override the vmq cowboy config to allow large GET calls w/ the VMQ REST API
    % This will prevent 414 errors when publishing large messages from Artemis
    Opts1 = ranch:get_protocol_options({{0, 0, 0, 0}, 8888}),
    Opts2 = maps:put(max_request_line_length, 16000, Opts1),
    ranch:set_protocol_options({{0, 0, 0, 0}, 8888}, Opts2),

    Opts3 = ranch:get_protocol_options({{0, 0, 0, 0}, 8100}),
    Opts4 = maps:put(max_request_line_length, 16000, Opts3),
    ranch:set_protocol_options({{0, 0, 0, 0}, 8100}, Opts4),

    case get_config("SOCIAL_API_KEY", Secrets, "") of
        "" ->
            ok;
        ApiKeyStr ->
            % only add the api key if it's not already there
            ApiKeyBin = list_to_binary(ApiKeyStr),
            case lists:member(ApiKeyBin, vmq_auth_apikey:list_api_keys()) of
                false ->
                    application:set_env(t2gp_social, api_key, ApiKeyStr),
                    vmq_auth_apikey:add_api_key(ApiKeyBin, "mgmt", "3023-01-01T00:00:00");
                true ->
                    ok
            end
    end,

    case get_config("SOCIAL_HTTPPUB_API_KEY", Secrets, "") of
        "" ->
            ok;
        HttpPubApiKeyStr ->
            % only add the httppub api key if it's not already there
            HttpPubApiKeyBin = list_to_binary(HttpPubApiKeyStr),
            case lists:member(HttpPubApiKeyBin, vmq_auth_apikey:list_api_keys()) of
                false ->
                    application:set_env(t2gp_social, api_key, HttpPubApiKeyStr),
                    vmq_auth_apikey:add_api_key(HttpPubApiKeyBin, "httppub", "3023-01-01T00:00:00");
                true ->
                    ok
            end
    end,

    % initialize lager w/ json console writer
    t2gp_social_lager:init(),
    % initial APM has to be initialized first
    t2gp_social_apm:init(),
    % reloader (maybe should be disabled in production)
    case utils:get_env() of
        <<"local">> ->
            application:set_env(
                t2gp_social, profile_table, "social-service-non-production-profile"
            ),
            application:set_env(t2gp_social, dynamodb_endpoint, "http://*************:8000"),
            application:set_env(t2gp_social, elasticache_url, "*************:6379"),
            reloader:start();
        _ ->
            ok
    end,
    % jwt parser
    application:ensure_all_started(jwt),
    % cli & HTTP API provider
    t2gp_social_cli:register_cli(),
    % supervisor restart crashed apps
    t2gp_social_sup:start_link().

-spec stop(map()) -> ok.
stop(_State) ->
    ok.

-spec reload() -> ok.
reload() ->
    application:set_env(erlcloud, ec2_meta_host_port, "***************"),
    application:stop(t2gp_social),
    reloader:reload_modules(reloader:all_changed()),
    application:ensure_all_started(t2gp_social),
    t2gp_social_cli:on_reload(),
    % force plugin reload.
    % vmq can get into a state where the plugin is loaded but is missing from the auth chain
    vmq_plugin_mgr:disable_plugin(t2gp_social),
    vmq_plugin_mgr:enable_plugin(t2gp_social, []),
    ok.

%% internal functions
