package cache

import (
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

var productid = utils.GenerateRandomDNAID()
var leaderid = utils.GenerateRandomDNAID()
var groupid1 = utils.GenerateRandomDNAID()
var groupid2 = utils.GenerateRandomDNAID()
var memberid1 = utils.GenerateRandomDNAID()
var memberid2 = utils.GenerateRandomDNAID()

var invite1Group1 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid1,
	Status:     "invited",
	Approverid: leaderid,
}

var invite2Group1 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid2,
	Groupid:    groupid1,
	Status:     "invited",
	Approverid: leaderid,
}

var invite3Group1 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid1,
	Status:     "invited",
	Approverid: memberid2,
}

var invite1Group2 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid2,
	Status:     "invited",
	Approverid: leaderid,
}

var invite2Group2 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid2,
	Groupid:    groupid2,
	Status:     "invited",
	Approverid: leaderid,
}

var invite3Group2 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid2,
	Status:     "invited",
	Approverid: memberid2,
}

var join1Group1 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid1,
	Status:     "requested",
	Approverid: leaderid,
}

var join2Group1 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid2,
	Groupid:    groupid1,
	Status:     "requested",
	Approverid: leaderid,
}

var join3Group1 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid1,
	Status:     "requested",
	Approverid: memberid2,
}

var join1Group2 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid2,
	Status:     "requested",
	Approverid: leaderid,
}

var join2Group2 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid2,
	Groupid:    groupid2,
	Status:     "requested",
	Approverid: leaderid,
}

var join3Group2 = apipub.MembershipRequest{
	Productid:  &productid,
	Memberid:   memberid1,
	Groupid:    groupid2,
	Status:     "requested",
	Approverid: memberid2,
}

var group1 = apipub.GroupResponse{
	Productid: productid,
	Groupid:   groupid1,
	MembershipRequests: &[]apipub.MembershipRequest{
		invite1Group1,
		invite2Group1,
		invite3Group1,
		join1Group1,
		join2Group1,
		join3Group1,
	},
}

var group2 = apipub.GroupResponse{
	Productid: productid,
	Groupid:   groupid2,
	MembershipRequests: &[]apipub.MembershipRequest{
		invite1Group2,
		invite2Group2,
		invite3Group2,
		join1Group2,
		join2Group2,
		join3Group2,
	},
}

func TestSetMembershipIdxAndCheckRequestExistence(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("SetMembershipIdx and InviteExistsInCache", func() {
		g.BeforeEach(func() {
			g.Timeout(45 * time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group1)
			rc.SetUserGroupIdxs(ctx, leaderid, &group2)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid2)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid2)

		})

		g.It("check a nonexisting invite request", func() {
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)).IsFalse()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid2, leaderid, nil)).IsFalse()
		})
		g.It("check a nonexisting join request", func() {
			g.Assert(rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)).IsFalse()
			g.Assert(rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid2, leaderid)).IsFalse()
		})
		g.It("set single invite request", func() {
			rc.setMembership(ctx, &invite1Group1)
			userSub := index.NewUserSubject("dna", productid, memberid1)
			userKey := userSub.InvitedToKey()
			groupKey := group1.RedisKey("dna")

			score := rc.zScore(ctx, *userKey, groupKey).Val()

			g.Assert(score == 0).IsTrue()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)).IsTrue()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, memberid2, nil)).IsFalse()
		})
		g.It("Same user invites a user to one group multiple times - one invite", func() {
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite1Group1)

			userSub := index.NewUserSubject("dna", productid, memberid1)
			userKey := userSub.InvitedToKey()

			count := rc.zCount(ctx, *userKey, "-inf", "+inf").Val()

			g.Assert(count == 1).IsTrue()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)).IsTrue()
		})
		g.It("Two users invite a user to one group - two invites", func() {
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite3Group1)

			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)).IsTrue()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, memberid2, nil)).IsTrue()
		})
		g.It("One user invites one user to two group - two invites", func() {
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite1Group2)

			userSub := index.NewUserSubject("dna", productid, memberid1)
			userKey := userSub.InvitedToKey()
			count := rc.zCount(ctx, *userKey, "-inf", "+inf").Val()

			g.Assert(count == 2).IsTrue()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)).IsTrue()
			g.Assert(rc.InviteExistsInCache(ctx, memberid1, productid, groupid2, leaderid, nil)).IsTrue()
		})
		g.It("Two users request to join two groups - 2 invites", func() {
			rc.setMembership(ctx, &join1Group1)
			rc.setMembership(ctx, &join2Group1)
			rc.setMembership(ctx, &join3Group1)
			rc.setMembership(ctx, &join1Group2)
			rc.setMembership(ctx, &join2Group2)
			rc.setMembership(ctx, &join3Group2)

			userSub := index.NewUserSubject("dna", productid, memberid1)
			userKey := userSub.RequestedToJoinKey()
			count := rc.zCount(ctx, *userKey, "-inf", "+inf").Val()
			g.Assert(count == 2).IsTrue()

			userSub = index.NewUserSubject("dna", productid, memberid2)
			userKey = userSub.RequestedToJoinKey()
			count = rc.zCount(ctx, *userKey, "-inf", "+inf").Val()
			g.Assert(count == 2).IsTrue()

			g.Assert(rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)).IsTrue()
			g.Assert(rc.JoinRequestExistsInCache(ctx, memberid2, productid, groupid1, leaderid)).IsTrue()
			g.Assert(rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid2, leaderid)).IsTrue()
			g.Assert(rc.JoinRequestExistsInCache(ctx, memberid2, productid, groupid2, leaderid)).IsTrue()
		})
	})
}

func TestGetInvitesAndJoinRequest(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GetInvitesForUser", func() {
		g.BeforeEach(func() {
			g.Timeout(45 * time.Second)
			rc.SetGroup(ctx, &group1, time.Duration(60)*time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group1)
			rc.SetGroup(ctx, &group2, time.Duration(60)*time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group2)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid2)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid2)
		})
		g.It("get invites for two users", func() {

			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite2Group1)
			rc.setMembership(ctx, &invite3Group1)

			// get member1's invites
			requests, _, _ := rc.GetInvitesForUser(ctx, memberid1, productid, aws.Int64(100), nil)
			g.Assert(len(*requests)).Equal(2)

			g.Assert((*requests)[0].Memberid).Equal(memberid1)
			g.Assert((*requests)[0].Groupid).Equal(groupid1)
			g.Assert((*requests)[0].Approverid == leaderid || (*requests)[0].Approverid == memberid2).IsTrue()
			g.Assert((*requests)[1].Approverid == leaderid || (*requests)[1].Approverid == memberid2).IsTrue()

			// get member2's invites
			requests, _, _ = rc.GetInvitesForUser(ctx, memberid2, productid, aws.Int64(100), nil)
			g.Assert(len(*requests)).Equal(1)

			g.Assert((*requests)[0].Memberid).Equal(memberid2)
			g.Assert((*requests)[0].Groupid).Equal(groupid1)
			g.Assert((*requests)[0].Approverid).Equal(leaderid)
		})
		g.It("get invites for an nonexisting user", func() {

			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			// rdb.SetUserGroupIdxs(ctx, leaderid, &group2, time.Duration(600)*time.Second)
			rc.setMembership(ctx, &invite1Group1)

			// Invite member1 to group1
			requests, _, _ := rc.GetInvitesForUser(ctx, "member1212", productid, aws.Int64(100), nil)
			g.Assert(requests).IsNil()
		})
		g.It("get invite for a user with limit of 1", func() {

			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			// rdb.SetUserGroupIdxs(ctx, leaderid, &group2, time.Duration(600)*time.Second)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite1Group2)

			// get member1's invites
			requests, next, _ := rc.GetInvitesForUser(ctx, memberid1, productid, aws.Int64(1), nil)
			g.Assert(len(*requests)).Equal(1)
			g.Assert(next == "").IsFalse()
		})
		g.It("use next iterator to get more invites", func() {

			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			// rdb.SetUserGroupIdxs(ctx, leaderid, &group2, time.Duration(600)*time.Second)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite1Group2)

			// get member1's invites
			requests, next, _ := rc.GetInvitesForUser(ctx, memberid1, productid, aws.Int64(1), nil)
			g.Assert(len(*requests)).Equal(1)
			g.Assert(next == "").IsFalse()
			g.Assert((*requests)[0].Memberid).Equal(memberid1)
			g.Assert((*requests)[0].Groupid == groupid1 || (*requests)[0].Groupid == groupid2).IsTrue()
			g.Assert((*requests)[0].Approverid).Equal(leaderid)

			requests, next, _ = rc.GetInvitesForUser(ctx, memberid1, productid, aws.Int64(1), &next)
			g.Assert(len(*requests)).Equal(1)
			g.Assert(next == "").IsTrue()
			g.Assert((*requests)[0].Memberid).Equal(memberid1)
			g.Assert((*requests)[0].Groupid == groupid1 || (*requests)[0].Groupid == groupid2).IsTrue()
			g.Assert((*requests)[0].Approverid).Equal(leaderid)
		})
		g.It("get invites for two users", func() {

			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			// rdb.SetUserGroupIdxs(ctx, leaderid, &group2, time.Duration(600)*time.Second)

			// Invite member1 and mermber 2 to group1
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite2Group1)
			rc.setMembership(ctx, &invite3Group1)

			// Invite member1 and mermber 2 to group2
			rc.setMembership(ctx, &invite1Group2)
			rc.setMembership(ctx, &invite2Group2)
			rc.setMembership(ctx, &invite3Group2)
			requests, _, _ := rc.GetInvitesForUser(ctx, memberid1, productid, aws.Int64(100), nil)
			g.Assert(len(*requests)).Equal(4)

			requests, _, _ = rc.GetInvitesForUser(ctx, memberid2, productid, aws.Int64(100), nil)
			g.Assert(len(*requests)).Equal(2)

		})
	})
}

func TestGetInvite(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GetInvitesForUser", func() {
		g.BeforeEach(func() {
			g.Timeout(45 * time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group1)
			rc.SetUserGroupIdxs(ctx, leaderid, &group2)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid2)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid2)
		})
		g.It("get a non-existing invite for a user", func() {

			group1Copy := apipub.GroupResponse{
				Productid: productid,
				Groupid:   groupid1,
				MembershipRequests: &[]apipub.MembershipRequest{
					invite1Group1,
					invite2Group1,
				},
			}

			group2Copy := apipub.GroupResponse{
				Productid: productid,
				Groupid:   groupid2,
				MembershipRequests: &[]apipub.MembershipRequest{
					invite3Group2,
				},
			}

			rc.SetUserGroupIdxs(ctx, leaderid, &group1Copy)
			rc.SetUserGroupIdxs(ctx, leaderid, &group2Copy)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite2Group1)
			rc.setMembership(ctx, &invite3Group2)

			// wrong approver id
			request, _ := rc.GetInvite(ctx, memberid1, productid, groupid1, "asdf", nil)
			g.Assert(request).IsNil()

			// wrong group id
			request, _ = rc.GetInvite(ctx, memberid1, productid, "asdf", leaderid, nil)
			g.Assert(request).IsNil()

			// wrong member id
			request, _ = rc.GetInvite(ctx, "asdf", productid, groupid2, leaderid, nil)
			g.Assert(request).IsNil()
		})
		g.It("get a invite for a user", func() {
			rc.SetUserGroupIdxs(ctx, leaderid, &group1)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite2Group1)
			rc.setMembership(ctx, &invite3Group1)

			request, _ := rc.GetInvite(ctx, memberid1, productid, groupid1, leaderid, nil)
			g.Assert(request).IsNotNil()

			g.Assert(request.Memberid).Equal(memberid1)
			g.Assert(request.Groupid).Equal(groupid1)
			g.Assert(request.Approverid).Equal(leaderid)

			request, _ = rc.GetInvite(ctx, memberid1, productid, groupid1, memberid2, nil)
			g.Assert(request).IsNotNil()

			g.Assert(request.Memberid).Equal(memberid1)
			g.Assert(request.Groupid).Equal(groupid1)
			g.Assert(request.Approverid).Equal(memberid2)

			request, _ = rc.GetInvite(ctx, memberid2, productid, groupid1, leaderid, nil)
			g.Assert(request).IsNotNil()

			g.Assert(request.Memberid).Equal(memberid2)
			g.Assert(request.Groupid).Equal(groupid1)
			g.Assert(request.Approverid).Equal(leaderid)

			request, _ = rc.GetInvite(ctx, memberid2, productid, groupid1, memberid2, nil)
			g.Assert(request).IsNil()
		})
	})
}

func TestClearAllMemberships(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("ClearAllMemberships", func() {
		g.BeforeEach(func() {
			g.Timeout(4500 * time.Second)
			// 	rdb.ecWriteClient.FlushAll(aws.BackgroundContext())
		})
		g.It("clear users' requests", func() {

			rc.SetGroup(ctx, &group1, time.Duration(60)*time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group1)
			rc.SetGroup(ctx, &group2, time.Duration(60)*time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group2)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite2Group1)
			rc.setMembership(ctx, &invite3Group1)
			rc.setMembership(ctx, &join1Group1)
			rc.setMembership(ctx, &join2Group1)
			rc.setMembership(ctx, &join3Group1)

			exists := rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsTrue()
			exists = rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, memberid2, nil)
			g.Assert(exists).IsTrue()
			exists = rc.InviteExistsInCache(ctx, memberid2, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsTrue()
			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)
			g.Assert(exists).IsTrue()
			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, memberid2)
			g.Assert(exists).IsTrue()
			exists = rc.JoinRequestExistsInCache(ctx, memberid2, productid, groupid1, leaderid)
			g.Assert(exists).IsTrue()

			rc.ClearAllMemberships(ctx, memberid1, productid, groupid1)
			exists = rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsFalse()
			exists = rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, memberid2, nil)
			g.Assert(exists).IsFalse()
			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)
			g.Assert(exists).IsFalse()
			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, memberid2)
			g.Assert(exists).IsFalse()

			rc.ClearAllMemberships(ctx, memberid2, productid, groupid1)
			exists = rc.InviteExistsInCache(ctx, memberid2, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsFalse()
			exists = rc.JoinRequestExistsInCache(ctx, memberid2, productid, groupid1, leaderid)
			g.Assert(exists).IsFalse()
		})
	})
}

func TestDeleteMembership(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("DeleteMembership", func() {
		g.BeforeEach(func() {
			g.Timeout(45 * time.Second)
			rc.SetUserGroupIdxs(ctx, leaderid, &group1)
			rc.SetUserGroupIdxs(ctx, leaderid, &group2)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid1, productid, groupid2)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid1)
			rc.ClearAllMemberships(ctx, memberid2, productid, groupid2)
		})
		g.It("clear users' invite requests", func() {
			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			rc.setMembership(ctx, &invite1Group1)
			rc.setMembership(ctx, &invite2Group1)
			rc.setMembership(ctx, &invite3Group1)

			exists := rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsTrue()
			rc.DeleteMembership(ctx, &invite1Group1)
			exists = rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsFalse()

			exists = rc.InviteExistsInCache(ctx, memberid2, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsTrue()
			rc.DeleteMembership(ctx, &invite2Group1)
			exists = rc.InviteExistsInCache(ctx, memberid2, productid, groupid1, leaderid, nil)
			g.Assert(exists).IsFalse()

			exists = rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, memberid2, nil)
			g.Assert(exists).IsTrue()
			rc.DeleteMembership(ctx, &invite3Group1)
			exists = rc.InviteExistsInCache(ctx, memberid1, productid, groupid1, memberid2, nil)
			g.Assert(exists).IsFalse()
		})
		g.It("clear users' join requests", func() {
			// rdb.SetUserGroupIdxs(ctx, leaderid, &group1, time.Duration(600)*time.Second)
			rc.setMembership(ctx, &join1Group1)
			rc.setMembership(ctx, &join2Group1)
			rc.setMembership(ctx, &join3Group1)

			exists := rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)
			g.Assert(exists).IsTrue()
			rc.DeleteMembership(ctx, &join1Group1)
			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)
			g.Assert(exists).IsFalse()

			exists = rc.JoinRequestExistsInCache(ctx, memberid2, productid, groupid1, leaderid)
			g.Assert(exists).IsTrue()
			rc.DeleteMembership(ctx, &join2Group1)
			exists = rc.JoinRequestExistsInCache(ctx, memberid2, productid, groupid1, leaderid)
			g.Assert(exists).IsFalse()

			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)
			g.Assert(exists).IsFalse()
			rc.DeleteMembership(ctx, &join3Group1)
			exists = rc.JoinRequestExistsInCache(ctx, memberid1, productid, groupid1, leaderid)
			g.Assert(exists).IsFalse()
		})
	})
}
