package api

import (
	"bytes"
	"context"
	"github.com/franela/goblin"
	. "github.com/onsi/gomega"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestDecodeBody(t *testing.T) {
	g := goblin.Goblin(t)
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	g.Describe("decodeBody", func() {
		g.It("parse decode body", func() {
			ctx := context.Background()
			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{})

			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
			w := httptest.NewRecorder()

			var obj interface{}
			result := DecodeBody(w, r, obj)
			g.<PERSON>ser<PERSON>(result).IsFalse()
			g.Assert(w.Code).Equal(http.StatusBadRequest)
			// g.Assert(w.Body.String()).Equal(ErrorJson(http.StatusBadRequest, errs.ERequestEmpty))
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.ERequestEmpty).Error()))

			w = httptest.NewRecorder()
			r.Body = io.NopCloser(bytes.NewBuffer([]byte("foobar")))
			result = DecodeBody(w, r, obj)
			g.Assert(result).IsFalse()
			g.Assert(w.Code).Equal(http.StatusBadRequest)
			// g.Assert(w.Body.String()).Equal("{\"code\":400,\"errorCode\":100004,\"message\":\"json: expected 'false' but found invalid token: foobar\"}\n")
			Ω(w.Body.String()).Should(ContainSubstring("{\"code\":400,\"errorCode\":100004,\"message\":\"json parse error"))

			w = httptest.NewRecorder()
			obj2 := struct {
				Foo string `json:"foo"`
			}{}
			r.Body = io.NopCloser(bytes.NewBuffer([]byte("{\"foo\":\"bar\"}")))
			result = DecodeBody(w, r, &obj2)
			g.Assert(result).IsTrue()
			g.Assert(obj2.Foo).Equal("bar")

			// r.Body = ioutil.NopCloser(bytes.NewBuffer([]byte("{\"foo\":\"<script>alert('foo')</script>\"}")))
			// result = decodeBody(w, r, &obj2)
			// g.Assert(result).IsTrue()
			// g.Assert(obj2.Foo).Equal("&lt;script&gt;alert(&#39;foo&#39;)&lt;/script&gt;")
		})
	})
}
