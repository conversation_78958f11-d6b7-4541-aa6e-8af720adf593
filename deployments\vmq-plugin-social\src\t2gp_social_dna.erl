-module(t2gp_social_dna).

-include_lib("t2gp_social.hrl").

%% gen_server callbacks
-export([
    start_link/0,
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

-export([
    discover_sso/2,
    get_application/4,
    get_key_from_jwks/3,
    parse_discovery/1
]).

-record(state, {
    applications,
    base_url,
    jwks
}).

-define(APM_DNA_REQUEST, <<"dna">>).

%
% gen_server
%

-spec start_link() -> {ok, pid()} | {error, {already_started, pid()}}.
start_link() ->
    lager:info("t2gp_social_dna:start_link"),
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

-spec init(term()) -> {ok, state()}.
init(_) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:info("t2gp_social_dna:init"),
        {ok, DiscoveryURL} = application:get_env(t2gp_social, discovery_url),
        {ok, AppID} = application:get_env(t2gp_social, app_id),
        case discover_sso(DiscoveryURL, AppID) of
            {ok, Props} ->
                SSOBaseURL = maps:get(<<"baseUrl">>, Props),
                lager:info("DNA SSO baseUrl ~p", [SSOBaseURL]),
                NewState = #state{applications = #{}, base_url = binary_to_list(SSOBaseURL)},
                lager:info("DNA SSO new state ~p", [NewState]),
                {ok, NewState};
            Err ->
                lager:error("DNA error ~p", [Err]),
                {ok, #state{applications = #{}}}
        end
    end).

-spec validate_hs256(binary(), binary(), state()) -> {reply, term(), state()}.
validate_hs256(Claims, JWT, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        ClaimsJSON = jsx:decode(base64url:decode(Claims)),
        AppID = binary_to_list(proplists:get_value(<<"iss">>, ClaimsJSON, <<"no-app-id">>)),
        BaseURL = State#state.base_url,
        Apps = State#state.applications,
        {ok, BasicAuth} = application:get_env(t2gp_social, app_basic_auth),
        case get_application(BaseURL, AppID, BasicAuth, Apps) of
            {ok, App, NewApps} ->
                Secret = proplists:get_value(<<"secret">>, App),
                NewState = State#state{applications = NewApps},
                {reply, jwt:decode(JWT, Secret), NewState};
            Err ->
                {reply, Err, State}
        end
    end).

-spec validate_rs256(binary(), binary(), state()) -> {reply, term(), state()}.
validate_rs256(Kid, JWT, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        Jwks = State#state.jwks,
        BaseURL = State#state.base_url,
        case get_key_from_jwks(BaseURL, Kid, Jwks) of
            {ok, Key, NewJwks} ->
                NewState = State#state{jwks = NewJwks},
                {reply, jwt:decode(JWT, Key), NewState};
            {error, Err} ->
                {reply, {error, Err}, State}
        end
    end).

-spec handle_call({atom(), term()}, {pid(), term()}, state()) -> {reply, term(), state()}.
handle_call({validate_jwt, JWT}, _From, State) when is_binary(JWT), byte_size(JWT) > 0 ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{cmd => <<"validate_jwt">>}),
        case binary:split(JWT, <<".">>, [global]) of
            [Header, Claims, _Signature] ->
                HeaderJSON = jsx:decode(base64url:decode(Header)),
                case proplists:get_value(<<"alg">>, HeaderJSON) of
                    <<"HS256">> ->
                        validate_hs256(Claims, JWT, State);
                    <<"RS256">> ->
                        Kid = proplists:get_value(<<"kid">>, HeaderJSON),
                        validate_rs256(Kid, JWT, State);
                    Alg ->
                        lager:error("Unknown alg in JWT: ~p", [Alg]),
                        {reply, {error, invalid_alg}, State}
                end;
            _Else ->
                {reply, {error, invalid_jwt}, State}
        end
    end);
handle_call({validate_jwt, _}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:error("Invalid JWT"),
        {reply, {error, invalid_jwt}, State}
    end);
handle_call({clear_jwks}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{cmd => <<"clear_jwks">>}),
        lager:info("Clearing jwks cache in t2gp_social_dna"),
        NewState = State#state{jwks = undefined},
        {reply, ok, NewState}
    end);
handle_call({clear_applications}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{cmd => <<"clear_applications">>}),
        lager:info("Clearing application cache state in t2gp_social_dna"),
        NewState = State#state{applications = #{}},
        {reply, ok, NewState}
    end);
handle_call({clear_all}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{cmd => <<"clear_all">>}),
        lager:info("Clearing all cache in t2gp_social_dna"),
        NewState = State#state{applications = #{}, jwks = undefined},
        {reply, ok, NewState}
    end);
handle_call(Msg, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:info("Unhandled message: ~p", [Msg]),
        {reply, {error, invalid_msg}, State}
    end).

-spec handle_cast(term(), state()) -> {noreply, state()}.
handle_cast(_Msg, State) ->
    {noreply, State}.

-spec handle_info(term(), state()) -> {noreply, state()}.
handle_info(_Msg, State) ->
    {noreply, State}.

-spec terminate(normal | shutdown | {shutdown, term()} | term(), term()) -> ok.
terminate(Reason, State) ->
    lager:info("t2gp_social_dna:terminate ~p, ~p", [Reason, State]),
    ok.

-spec code_change(term() | {down, term()}, state(), term()) -> {ok, state()} | {error, term()}.
code_change(OldVsn, State, Extra) ->
    lager:info("t2gp_social_dna:code_change ~p, ~p, ~p", [OldVsn, State, Extra]),
    {ok, State}.

%
% http helpers
%
-spec discover_sso(string(), string()) -> {ok, list()} | {error, term()}.
discover_sso(DiscoveryURL, AppID) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{appid => AppID, discovery_url => DiscoveryURL}),
        Method = get,
        URL = list_to_binary(DiscoveryURL ++ "/discovery/v1/services"),
        Header = [
            {<<"Content-Type">>, <<"application/json">>},
            {<<"Authorization">>, list_to_binary("Application " ++ AppID)}
        ],
        t2gp_social_apm:span_start(
            ?APM_DNA_REQUEST, list_to_binary(io_lib:format("GET ~s", [URL]))
        ),
        t2gp_social_apm:tags(#{method => atom_to_binary(Method), url => URL}),
        Result =
            case hackney:request(Method, URL, Header, <<>>, [with_body]) of
                {ok, 200, _Headers, Body} ->
                    Services = parse_discovery(Body),
                    {ok, maps:get(<<"sso">>, Services, #{})};
                {ok, Code, Headers, Body} ->
                    lager:error("DNA discovery failed: ~p ~p ~p", [URL, Headers, Body]),
                    {error, {http_error, Code, Headers, Body}};
                {error, Err} ->
                    lager:error("DNA discovery failed: result ~p", [Err]),
                    {error, Err}
            end,
        t2gp_social_apm:span_finish(),
        Result
    end).

-spec parse_discovery(binary()) -> map().
parse_discovery(Body) ->
    try
        Services = jsx:decode(Body),
        ServicesMap = lists:foldl(
            fun(S, Acc) ->
                case proplists:get_value(<<"name">>, S, <<>>) of
                    <<>> -> Acc;
                    Name -> maps:put(Name, maps:from_list(S), Acc)
                end
            end,
            #{},
            Services
        ),
        ServicesMap
    catch
        Exception:Reason:Stack ->
            lager:error("t2gp_social_dna:parse_discovery invalid response: ~p ~p ~p ~p ", [
                Body, Exception, Reason, Stack
            ]),
            #{}
    end.

-spec get_application(string(), string(), string(), map()) ->
    {ok, proplists:proplist(), map()}
    | {error, {http_error, integer(), list(), binary()}}
    | {error, term()}.
get_application(BaseURL, AppID, BasicAuth, Apps) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{base_url => list_to_binary(BaseURL), appid => list_to_binary(AppID)}),
        case maps:is_key(AppID, Apps) of
            true ->
                {ok, maps:get(AppID, Apps), Apps};
            _ ->
                Method = get,
                URL = list_to_binary(BaseURL ++ "/app/apps/" ++ AppID),
                Header = [
                    {<<"Content-Type">>, <<"application/json">>},
                    {<<"Authorization">>, list_to_binary("Basic " ++ BasicAuth)}
                ],
                lager:info("Fetching application config ~p", [URL]),
                t2gp_social_apm:span_start(
                    ?APM_DNA_REQUEST, list_to_binary(io_lib:format("GET ~s", [URL]))
                ),
                t2gp_social_apm:tags(#{method => atom_to_binary(Method), url => URL, appid => AppID}),
                Result =
                    case hackney:request(Method, URL, Header, <<>>, [with_body]) of
                        {ok, 200, _Headers, Body} ->
                            PropList = jsx:decode(Body),
                            {ok, PropList, maps:put(AppID, PropList, Apps)};
                        {ok, Code, Headers, Body} ->
                            lager:error("DNA application fetch failed: ~p ~p ~p", [
                                URL, Headers, Body
                            ]),
                            {error, {http_error, Code, Headers, Body}};
                        {error, Err} ->
                            lager:error("DNA application fetch failed: result ~p", [Err]),
                            {error, Err}
                    end,
                t2gp_social_apm:span_finish(),
                Result
        end
    end).

%% 2K key is is a uuid. e.g. 03a61152-ecd9-4936-80d2-4536c9e64556
%% BaseURL https://sso.api.2kcoretech.online/sso/v2.0
-spec get_key_from_jwks(string(), binary(), binary() | undefined) ->
    {ok, proplists:proplist(), binary()} | {error, term()}.
get_key_from_jwks(_BaseURL, Kid, Jwks) when is_binary(Jwks) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{kid => Kid, cached => <<"true">>}),
        case jwk:decode(Kid, Jwks) of
            {ok, Key} ->
                {ok, Key, Jwks};
            {error, Err} ->
                {error, Err}
        end
    end);
get_key_from_jwks(BaseURL, Kid, undefined) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{kid => Kid, cached => <<"false">>}),
        Method = get,
        URL = list_to_binary(BaseURL ++ "/jwks.json"),
        Header = [
            {<<"Content-Type">>, <<"application/json">>}
        ],
        lager:info("Fetching jwks ~p", [URL]),
        t2gp_social_apm:span_start(
            ?APM_DNA_REQUEST, list_to_binary(io_lib:format("GET ~s", [URL]))
        ),
        t2gp_social_apm:tags(#{method => atom_to_binary(Method), url => URL}),
        Result =
            case hackney:request(Method, URL, Header, <<>>, [with_body]) of
                {ok, 200, _Headers, Body} ->
                    NewJwks = Body,
                    case jwk:decode(Kid, NewJwks) of
                        {ok, Key} ->
                            {ok, Key, NewJwks};
                        {error, Err} ->
                            {error, Err}
                    end;
                {ok, Code, Headers, Body} ->
                    lager:error("DNA jwks fetch failed: ~p ~p ~p", [URL, Headers, Body]),
                    {error, {http_error, Code, Headers, Body}};
                {error, Err} ->
                    lager:error("DNA jwks fetch failed: result ~p", [Err]),
                    {error, Err}
            end,
        t2gp_social_apm:span_finish(),
        Result
    end).
