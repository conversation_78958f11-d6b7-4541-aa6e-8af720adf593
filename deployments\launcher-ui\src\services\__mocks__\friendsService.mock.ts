import type { IFriendsService } from '../friends';
import {
  MOCKED_FRIENDS,
  MOCKED_PENDING_FRIENDS,
  MOCKED_STEAM_FRIENDS,
} from './data.mock';

export const friendsServiceMock: IFriendsService = {
  getPendingFriendsAsync: async () => {
    return await Promise.resolve(MOCKED_PENDING_FRIENDS);
  },
  getFriendsAsync: async () => {
    return await Promise.resolve(MOCKED_FRIENDS);
  },
  getSteamFriendsAsync: async () => {
    return await Promise.resolve(MOCKED_STEAM_FRIENDS);
  },
  MakeFriendAsync: async () => {
    return Promise.resolve({ status: 'pending' });
  },
  DeleteFriendAsync: () => {
    return Promise.resolve({ status: 'unfriend' });
  },
  searchFriendsAsync: async displayName => {
    return await Promise.resolve(
      MOCKED_FRIENDS.filter(f => f.name === displayName)
    );
  },
  markFriendRequestViewed: async () => {
    return await Promise.resolve({
      ...MOCKED_PENDING_FRIENDS[0],
      viewed: true,
    });
  },
};
