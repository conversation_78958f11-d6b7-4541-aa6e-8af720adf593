{application, t2gp_social,
 [{description, "T2GP Social Plugin for VerneMQ"},
  {vsn, "0.3.1"},
  {registered, []},
  {mod, {t2gp_social_app, []}},
  {applications,
   [kernel,
    stdlib,
    lager,
    opencensus,
    hackney,
    erlcloud
   ]},
  {env,[
        {jwt_allow_alg_none, false},
        {dynamodb_endpoint, ""},
        {profile_table, ""},
        {vmq_plugin_hooks, 
        [
            {t2gp_social, auth_on_register, 5, []},
            {t2gp_social, auth_on_register_m5, 6, []},
            {t2gp_social, auth_on_publish, 6, []},
            {t2gp_social, auth_on_publish_m5, 7, []},            
            {t2gp_social, auth_on_subscribe_m5, 4, []},
            % for presence
            {t2gp_social, on_client_wakeup, 1, []},
            {t2gp_social, on_client_offline, 1, []},
            {t2gp_social, on_client_gone, 1, []}
        ]}
      ]},
  {modules, []},
  {links, []}
 ]}.
