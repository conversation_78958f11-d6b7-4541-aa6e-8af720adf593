name: Build sanity test docker

on:
  push:
    branches:
      - develop
    paths:
      - "tests/api-interaction-tests/**"
      - ".github/workflows/build-sanity-test-image.yml"

permissions:
  id-token: write
  contents: write

jobs:
  build-sanity-test:
    name: Build sanity test
    runs-on: [t2gp-arc-linux]
    env:
      WORK_DIR: ./
    outputs:
      image_tag: ${{ steps.build-docker.outputs.image_tag }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          fetch-depth: 0

      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.GH_AWS_ROLE }}
          role-session-name: ${{ vars.GH_AWS_SESSION }}
          aws-region: us-east-1
      - name: Dockerhub login
        uses: docker/login-action@v2.2.0
        with:
          username: ${{ secrets.D2C_DOCKERHUB_PULL_USERNAME }}
          password: ${{ secrets.D2C_DOCKERHUB_PULL_PASSWORD }}
      - name: Get commit if push to develop
        if: ${{github.event_name == 'push'}}
        id: declare-envs
        run: |
          echo VER=${GITHUB_SHA::8} >> $GITHUB_ENV
      - name: Build docker images
        id: build-docker
        run: |
          echo "image_tag=$VER" >> $GITHUB_OUTPUT
          docker build --progress plain -t t2gp-social-interaction-test -f docker/Dockerfile-k8s .
        env:
          NODE_AUTH_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        working-directory: tests/api-interaction-tests
      - name: Push api image
        uses: jwalton/gh-ecr-push@v1
        with:
          access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          region: us-east-1
          local-image: 't2gp-social-interaction-test'
          image: 't2gp-social-interaction-test:${{env.VER}}'