<script lang="ts">
  import { isAway, isOnline, isPlaying } from '../../utils';
  import { StatusIndicator } from '../StatusIndicator';

  export let style = '';
  export let name = '';
  export let initials = '';
  export let src = '';
  export let bgColor = 'lightGrey';
  export let textColor = 'white';
  export let size = '24px';
  export let borderRadius = '50%';
  export let square = false;
  export let status = '';
  export let showPresence = true;

  const background = bgColor;

  let imageFail = false;
  let imageLoading = true;

  $: abbr = initials;
  $: abbrLength = abbr.length;
  $: online = isOnline(status) || isPlaying(status) || isAway(status);
  $: playing = isPlaying(status);
  $: away = isAway(status);
  $: offline = !online && !playing && !away;
</script>

<style>
  .wrapper {
    position: relative;
    width: var(--size);
    height: var(--size);
    opacity: 0.4;
  }

  .wrapper.online {
    opacity: 1;
  }

  .innerImage,
  .innerInitials,
  .imageLoading {
    display: block;
    width: 100%;
    height: 100%;
    border-radius: var(--borderRadius);
  }

  .innerInitials {
    line-height: var(--size);
    background-color: var(--bgColor);
    color: var(--textColor);
    text-align: center;
    font-size: calc(var(--size) / (var(--abbrLength) + 0.5));
    text-transform: uppercase;
  }

  .imageLoading {
    background-color: var(--bgColor);
  }
  .imageLoading::before {
    content: '';
    display: block;
    height: 100%;
    width: 100%;
    background: linear-gradient(90deg, transparent, #ffffff38, transparent);
    position: absolute;
    top: 0;
    left: 0;
    animation: skeleton-animation 1.2s linear infinite;
  }

  @keyframes skeleton-animation {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .wrapper :global(.indicator) {
    position: absolute;
    top: 0;
    right: 0;
  }
</style>

<div
  aria-label="{name}"
  class="wrapper"
  class:online="{online || !showPresence}"
  style="{style}--borderRadius:{square
    ? 0
    : borderRadius}; --size:{size}; --bgColor:{background};
    --src:{src}; --textColor:{textColor}; --abbrLength:{abbrLength}"
>
  {#if src && !imageFail}
    <div class="{imageLoading ? 'imageLoading' : ''}">
      <img
        alt=""
        class="{`innerImage`}"
        src="{src}"
        on:error="{() => (imageFail = true)}"
        on:load="{() => (imageLoading = false)}"
      />
    </div>
  {:else}
    <div class="innerInitials">{abbr}</div>
  {/if}

  {#if showPresence}
    <StatusIndicator
      className="indicator"
      bind:online
      bind:playing
      bind:away
      bind:offline
    />
  {/if}
</div>
