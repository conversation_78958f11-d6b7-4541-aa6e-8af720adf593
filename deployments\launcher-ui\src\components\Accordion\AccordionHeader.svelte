<script lang="ts">
  import { SVGArrowDown, SVGArrowUp } from '../../assets/icons';
  import { ACCORDION_MODE } from '../../constant';

  export let title = undefined;
  export let open = undefined;
  export let className = '';
  export let mode = ACCORDION_MODE.multiple;
</script>

<style>
  .accordion-header {
    color: var(--social-color, var(--default-color));
    background-color: var(
      --social-bg-color-header,
      var(--default-bg-color-header)
    );
    margin: 0;
    padding: 0.75rem;
    cursor: pointer;
    text-transform: uppercase;
    font-size: 0.75rem;
    font-weight: 700;
    display: flex;
    justify-content: space-between;
    letter-spacing: 0.05rem;
  }

  .accordion-header span :global(svg) {
    width: 12px;
    height: 8px;
  }
</style>

<div on:click class="{`accordion-header ${className}`}">
  {title}
  <span>
    {#if mode === ACCORDION_MODE.multiple}
      {#if open}
        <span><SVGArrowUp /></span>
      {:else}
        <span><SVGArrowDown /></span>
      {/if}
    {/if}
  </span>
</div>
