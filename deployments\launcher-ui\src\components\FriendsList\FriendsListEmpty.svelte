<script lang="ts">
  import { SVGEmptyFace } from '../../assets/icons';
  import { EVENT_FRIENDS_ADD_WINDOW_OPEN } from '../../constant';
  import { useTranslator, useTransportService } from '../../hooks';
  import Button from '../Button/Button.svelte';

  const t = useTranslator();
  const transportService = useTransportService();

  const onAddFriendsClicked = () => {
    transportService.publishEvent(EVENT_FRIENDS_ADD_WINDOW_OPEN);
  };
</script>

<style>
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .container .svg {
    margin-top: 12rem;
  }

  .container p {
    font-family: Montserrat;
    font-style: normal;
    font-weight: 500;
    font-size: 1rem;
    line-height: 150%;
    text-align: center;
    color: var(--social-color, var(--default-color));
    margin-top: 1rem;
    margin-bottom: 1rem;
    opacity: 0.8;
  }
</style>

<div class="container">
  <div class="svg">
    <SVGEmptyFace />
  </div>

  <p>{$t("You don't have any friends yet")}.</p>
  <Button on:buttonClick="{onAddFriendsClicked}">
    <span slot="label"> {$t('Add Friends')} </span>
  </Button>
</div>
