import { fireEvent, render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import TaskBarWrapper from './TaskBarWrapper.svelte';

jest.mock('../../../assets/icons', () => ({
  SVGClose: SVGIconMock,
  SVGMinimize: SVGIconMock,
}));

describe('TaskBar', () => {
  it('should render task bar', () => {
    const { getByTestId } = render(TaskBarWrapper);
    const minimizeButton = getByTestId('minimize-button');
    const closeButton = getByTestId('close-button');

    expect(minimizeButton).not.toBeNull();
    expect(closeButton).not.toBeNull();
  });

  it('should fire close button event', async () => {
    const closeEventHandler = jest.fn();
    const { getByTestId } = render(TaskBarWrapper);
    const closeButton = getByTestId('close-button');
    closeButton.addEventListener('click', closeEventHandler);

    await fireEvent.click(closeButton);

    expect(closeEventHandler).toBeCalled();
  });

  it('should fire minimized button event', async () => {
    const minimizeEventHandler = jest.fn();
    const { getByTestId } = render(TaskBarWrapper);
    const closeButton = getByTestId('minimize-button');
    closeButton.addEventListener('click', minimizeEventHandler);

    await fireEvent.click(closeButton);

    expect(minimizeEventHandler).toBeCalled();
  });
});
