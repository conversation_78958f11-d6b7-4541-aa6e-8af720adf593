package index

import (
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
)

func Test_SecondaryIndex(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("newSecondaryIndex", func() {

		g.It("success set", func() {
			idx := NewSecondaryIndex("idxKey", "valKey")
			g.<PERSON>sert(idx).IsNotNil()
			g.Assert(idx.idxKey).Equal("idxKey")
			g.Assert(idx.valKey).Equal("valKey")
		})
	})

	g.Describe("secondaryIndex getters", func() {
		g.It("should return default vals if nil idx", func() {
			var idx *SecondaryIndex
			cfg := config.ConfigForTests()
			g.<PERSON>sert(idx.IdxKey() == "")
			g.<PERSON>(idx.ValKey() == "")
			g.<PERSON>(idx.Score() == aws.Float64(0))
			g.<PERSON>(*idx.Ttl(cfg) == time.Duration(cfg.TtlDefault)*time.Second)
		})

		g.It("normal gets", func() {
			idx := NewSecondaryIndex("idxKey", "valKey")
			cfg := config.ConfigForTests()
			g.Assert(idx.IdxKey() == "idxKey")
			g.Assert(idx.ValKey() == "valKey")
			g.Assert(idx.Score() == aws.Float64(0))
			g.Assert(*idx.Ttl(cfg) == time.Duration(cfg.TtlDefault)*time.Second)
		})

	})

	g.Describe("secondaryIndex setters", func() {
		g.It("should set score", func() {
			idx := NewSecondaryIndex("idxKey", "valKey")
			score := 1.0
			idx.SetScore(score)
			g.Assert(idx.Score() == aws.Float64(score))
		})

		g.It("should set ttl", func() {
			idx := NewSecondaryIndex("idxKey", "valKey")
			ttl := 1 * time.Second
			idx.SetTtl(ttl)
			g.Assert(idx.Ttl(nil) == &ttl)
		})
	})

}
