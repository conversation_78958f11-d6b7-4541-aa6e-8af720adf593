name: Run unit test

on:
  workflow_dispatch:
  push:
    branches:
      - '*'
  release:
    types:
      - published

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  test:
    name: Run unit test
    runs-on: [t2gp-arc-linux]
    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_ecr_rw
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Inject slug/short variables
        uses: rlespinasse/github-slug-action@v3.x
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Sonarqube needs full depth to allocate issues to the original author
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
      - name: Use Node.js 
        uses: actions/setup-node@v4
        with:
          node-version: 22
          registry-url: https://npm.pkg.github.com/
          scope: take-two-t2gp
      - name: Install redocly
        run: npm install @redocly/cli -g
      - name: Set up Go 1.21
        uses: actions/setup-go@v5
        with:
          go-version-file: 'go.mod'
          cache-dependency-path: "go.sum"
        id: go
      - name: generate vmq .env
        working-directory: deployments/vmq-plugin-social
        run: |
          echo AWS_DEFAULT_REGION=us-east-1 >> .env
          echo AWS_REGION=us-east-1 >> .env
          echo SOCIAL_DISCOVERY_URL=https://discovery.api.2kcoretech.online >> .env          
          echo SOCIAL_DYNAMODB_ENDPOINT="192.168.123.2:8000" >> .env
          echo SOCIAL_JWT_ALLOW_ALG_NONE=on >> .env
          echo SOCIAL_PD_PRIVATE_URL=https://pd-backoffice-dev.d2dragon.net >> .env
          echo PD_PUBLIC_URL=https://account-dev.id.privatedivision.com >> .env
          echo SOCIAL_RS_CONFIG_URL=s3://t2gp-pd-store-api/rs_auth_env/config.gz >> .env
          echo SOCIAL_PROFILE_TABLE=social-service-non-production-profile >> .env
          echo SOCIAL_CHAT_MESSAGES_TABLE=social-service-non-production-chat >> .env
          echo SOCIAL_APP_ID=${{ secrets.APP_ID }} >> .env
          echo SOCIAL_APP_SECRET=${{ secrets.APP_SECRET }} >> .env
          echo SOCIAL_APP_BASIC_AUTH=${{secrets.APP_BASIC_AUTH}} >> .env
          echo SOCIAL_JWT_ALLOW_ALG_NONE=on >> .env
          echo SOCIAL_API_KEY=${{ secrets.VMQ_API_KEY }} >> .env
          echo SOCIAL_PREDEFINED_USER=${{ secrets.SOCIAL_PREDEFINED_USER }} >> .env
          echo SOCIAL_HTTPPUB_API_KEY=${{ secrets.SOCIAL_HTTPPUB_API_KEY }} >> .env
          echo SOCIAL_ELASTICACHE_URL=${{ secrets.SOCIAL_ELASTICACHE_URL }} >> .env          
          echo DD_ENV=local >> .env >> .env

      - name: generate test-runner .env
        run: |
          echo AWS_DEFAULT_REGION=us-east-1 >> .env
          echo AWS_REGION=us-east-1 >> .env
          echo AWS_ACCESS_KEY_ID=${{ env.AWS_ACCESS_KEY_ID }} >> .env
          echo AWS_SECRET_ACCESS_KEY=${{ env.AWS_SECRET_ACCESS_KEY }} >> .env
          echo APP_ID=${{ secrets.APP_ID }} >> .env
          echo APP_SECRET=${{ secrets.APP_SECRET }} >> .env
          echo APP_BASIC_AUTH=${{ secrets.APP_BASIC_AUTH }} >> .env
          echo VMQ_API_KEY=${{ secrets.VMQ_API_KEY }} >> .env
          echo PD_API_KEY=${{ secrets.PD_API_KEY }} >> .env
          echo VMQ_HTTPPUB_API_KEY=${{ secrets.VMQ_HTTPPUB_API_KEY }} >> .env
          echo VMQ_HTTPPUB_USER=${{ secrets.VMQ_HTTPPUB_USER }} >> .env
          echo DYNAMODB_URL=http://192.168.123.2:8000 >> .env
          echo VMQ_API_URL=http://192.168.123.8:8100 >> .env
          echo VMQ_HTTPPUB_API_URL=http://192.168.123.8:8110 >> .env
          echo ELASTICACHE_URL=192.168.123.4:6379 >> .env
          echo DD_ENV=local >> .env
          echo DISCOVERY_URL=https://discovery.api.2kcoretech.online >> .env
          echo PROFILE_TABLE=social-service-non-production-profile >> .env
          echo CHAT_MESSAGES_TABLE=social-service-non-production-chat >> .env
          echo PD_PRIVATE_URL=https://pd-backoffice-dev.d2dragon.net >> .env
          echo PD_PUBLIC_URL=https://account-dev.id.privatedivision.com >> .env
          echo DISCOVERY_BUCKET=t2gp-social-discovery-test >> .env
          echo CORS_ALLOWED_ORIGINS=http://localhost* >> .env
          echo VMQ_USE_BINARY_PUB=false >> .env
      - name: Validate openapi yml files
        uses: mbowman100/swagger-validator-action@master
        with:
          files: |
            api/openapi-social-public.yml
            api/openapi-social-private.yml
      - name: Pull Images
        run: |
          docker-compose -f docker-compose-test.yml pull
      - name: Run Test
        run: |
          echo "${{secrets.T2GP_CERT}}" >> t2paca.crt
          git config --global url.https://${{secrets.SERVICE_ACCOUNT_GH_PAT}}:<EMAIL>/.insteadOf https://github.com/
          GOPRIVATE=github.com/take-two-t2gp,github.com/2kg-coretech
          make build
          make lint
          make vmq-build
          mkdir build && touch cp.out
          touch report.xml
          AWS_ACCESS_KEY_ID=${{ env.AWS_ACCESS_KEY_ID }} AWS_SECRET_ACCESS_KEY=${{ env.AWS_SECRET_ACCESS_KEY }} docker-compose -f docker-compose-test.yml up -d
          docker exec t2gp-social-service_redis2_1 redis-cli --cluster create 192.168.123.4:6379 192.168.123.5:6380 192.168.123.6:6381 --cluster-replicas 0 --cluster-yes
          docker exec t2gp-social-service_t2gp-social-service_1 git config --global --add safe.directory '*'
          docker exec t2gp-social-service_t2gp-social-service_1 git config --global url.https://${{secrets.SERVICE_ACCOUNT_GH_PAT}}:<EMAIL>/.insteadOf https://github.com/
          docker exec t2gp-social-service_t2gp-social-service_1 make cleantestwithreport OPTS='-v'
      - name: Upload test summary to DD
        if: always()
        id: dd-test-report-upload
        run: |
          curl -L --fail "https://github.com/DataDog/datadog-ci/releases/latest/download/datadog-ci_linux-x64" --output ./datadog-ci && chmod +x ./datadog-ci
          {
            echo "## Datadog link"
            ./datadog-ci junit upload --service social-service-api report.xml | grep -A1 Commit
          } >> $GITHUB_STEP_SUMMARY
        env:
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
          DD_ENV: ${{ env.GITHUB_REF_SLUG }}
