import { render } from '@testing-library/svelte';
import SVGSteamMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  transportServiceMock,
} from '../../../services/__mocks__';
import AvatarMock from '../../Avatar/__mock__/Avatar.svelte';
import LoadingSpinnerMock from '../../LoadingSpinner/__mock__/LoadingSpinner.svelte';
import TooltipMock from '../../Tooltip/__mock__/Tooltip.svelte';
import DirectImportCardWrapper from './DirectImportCardWrapper.svelte';

jest.mock('../../Avatar', () => ({
  Avatar: AvatarMock,
}));

jest.mock('../../Tooltip', () => ({
  Tooltip: TooltipMock,
}));

jest.mock('../../../assets/icons', () => ({
  SVGSteam: SVGSteamMock,
  SVGAddFriend: SVGSteamMock,
}));

jest.mock('../../LoadingSpinner', () => ({
  LoadingSpinner: LoadingSpinnerMock,
}));

const socialServicesMock = new SocialServices({
  transportService: transportServiceMock,
  friendsService: friendsServiceMock,
});

describe('DirectImportCard', () => {
  it('should render UI', () => {
    const { getByTestId } = render(DirectImportCardWrapper, {
      props: {
        context: socialServicesMock,
      },
    });
    expect(getByTestId('tooltip-mock')).not.toBeNull();
  });
});
