import * as socialApi from './social-api';
import mqtt from 'mqtt';
import { TwokAccount, config } from './config';
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

/**
 * MQTT Wrapper
 */
// Wraps some functionalities of the MQTT library
export class MqttWrapper {
  private client: mqtt.MqttClient;
  constructor(token: string, userId: string) {
    this.client = mqtt.connect(config.socialService.currAccessLevel.currEnv.currVer.mqtt, {
      keepalive: 30,
      clientId: `social-auto-${userId}-${Math.random()
        .toString(16)
        .substr(2, 8)}`,
      password: token,
      properties: { userProperties: { uid: userId } },
      protocolVersion: 5,
      reconnectPeriod: 20000,
      username: userId,
    });

    this.client.on('error', error => {
      console.log(userId, error);
    });
  }

  public getClient() {
    return this.client;
  }

  public stop = () => {
    this.client.end();
  };
}

/**
 * MQTT Client Manager
 */
// Manages clients and and performs tasks for them.
export class MqttClientManager {
  private clients: {[idx: string]: MqttWrapper} = {};

  constructor(twokAccts: {[idx: string]: TwokAccount}) {
    for (let k of Object.keys(twokAccts)) {
      this.clients[k] = new MqttWrapper(twokAccts[k].accessToken, twokAccts[k].publicId);
    }
  }

  public getClients() {
    return this.clients;
  }

  public getClientsNum() {
    return Object.keys(this.clients).length;
  }

  public async waitConnectAll() {
    for (const client of Object.values(this.clients)) {
      // hang around for a while in case connection is not established
      await socialApi.waitWhile(async () => !client.getClient().connected, 15, 1000);

      if (!client.getClient().connected) {
        throw new Error('Mqtt connection is still not established. Please verify test setup. ');
      }
    }
  }

  public stopAll() {
    for (const client of Object.values(this.clients)) {
      client.stop();
    }
  }

  public addMsgListenerAll(vmc: mqtt.OnMessageCallback) {
    for (const client of Object.values(this.clients)) {
      client.getClient().on('message', vmc);
    }
  }

  public removeAllListenersAll(event?: string) {
    for (const client of Object.values(this.clients)) {
      client.getClient().removeAllListeners(event);
    }
  }
}