// Package identity provides identity services for different labels
package identity

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"github.com/2kg-coretech/dna-common/pkg/authn"
	zlog "github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

type IdentityService string

const (
	IdentityServiceDNA             IdentityService = "dna"
	IdentityServiceRockstar        IdentityService = "rsg"
	IdentityServicePrivateDivision IdentityService = "pdi"
	IdentityServiceUnknown         IdentityService = "unk"
)

type IdentityServiceInterface interface {
	health.DependentService
	SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error)
	SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error)
	GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error)
	Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error)
	Login(ctx context.Context, username string, password string, local string, appID string) (*apipub.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string, locale string) (*apipub.LoginResponse, error)
	Logout(ctx context.Context, token string) error
	UpdateConfig(ctx context.Context) error
	SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error)
	SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error)
	SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse)
	GetProductIdFromAppId(ctx context.Context, appId string) (*string, error)
	GetIdentityServiceStr() IdentityService
}

type IdentityInterface interface {
	SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error)
	SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error)
	GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error)
	Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error)
	Login(ctx context.Context, username string, password string, local string, appID string) (*apipub.LoginResponse, error)
	RefreshToken(ctx context.Context, refreshToken string, locale string) (*apipub.LoginResponse, error)
	Logout(ctx context.Context, token string) error
	SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error)
	SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error)
	SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse)
	GetProductIdFromAppId(ctx context.Context, appId string) (*string, error)

	GetIdentityService(providerType IdentityService) IdentityServiceInterface
	GetIdentityServiceFromContext(ctx context.Context) IdentityServiceInterface
	GetIdentityServiceStr(ctx context.Context) IdentityService
}

type Identity struct {
	cfg       *config.Config
	providers map[IdentityService]IdentityServiceInterface
	ticker    atomic.Value
	quit      chan struct{}
	wg        sync.WaitGroup
	timeout   time.Duration
}

func NewIdentity(ctx context.Context, cfg *config.Config, sm health.ServiceMonitorInterface) *Identity {

	dnaProvider := NewDNAService(cfg)
	id := &Identity{
		cfg: cfg,
		providers: map[IdentityService]IdentityServiceInterface{
			IdentityServiceDNA: dnaProvider,
		},
		quit:    make(chan struct{}),
		timeout: 1 * time.Minute,
	}

	//only initialize rsg provider when not local.  this removes the need to wait for timeout when starting debugger locally.
	if !utils.IsLocal() {
		rsgProvider := NewRSService(ctx, cfg)
		id.providers = map[IdentityService]IdentityServiceInterface{
			IdentityServiceDNA:      dnaProvider,
			IdentityServiceRockstar: rsgProvider,
		}
	}

	for key, value := range id.providers {
		sm.AddDependentService("social-service-id-"+string(key), value)
	}

	return id
}

func (id *Identity) GetIdentityService(providerType IdentityService) IdentityServiceInterface {
	if provider, ok := id.providers[providerType]; ok {
		return provider
	}
	// should never get here
	zlog.Error().Msgf("unknown identity provider %s", providerType)
	return nil
}

func (id *Identity) GetIdentityServiceFromContext(ctx context.Context) IdentityServiceInterface {
	value := ctx.Value(constants.T2GPCtxTenant)
	defaultTenant := IdentityServiceDNA
	tenant := defaultTenant
	if value != nil {
		tenant = IdentityService(value.(string))
	}
	provider := id.GetIdentityService(tenant)
	if provider == nil {
		provider = id.GetIdentityService(defaultTenant)
	}
	return provider
}

func (id *Identity) SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.SearchAccounts(ctx, request)
}

func (id *Identity) SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.SearchAccountsByUserID(ctx, userid)
}

func (id *Identity) GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.GetUserProfileAccountLinks(ctx, userid)
}

func (id *Identity) Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.Authenticate(ctx, authHeader, skipRevokedTokenValidation)
}

func (id *Identity) Login(ctx context.Context, username string, password string, locale string, appID string) (*apipub.LoginResponse, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.Login(ctx, username, password, locale, appID)
}

func (id *Identity) RefreshToken(ctx context.Context, refreshToken string, locale string) (*apipub.LoginResponse, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.RefreshToken(ctx, refreshToken, locale)
}

func (id *Identity) Logout(ctx context.Context, token string) error {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.Logout(ctx, token)
}

func (id *Identity) SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.SyncUserProfile(ctx, userid)
}

func (id *Identity) SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.SyncUserProfiles(ctx, userids)
}

func (id *Identity) SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse) {
	provider := id.GetIdentityServiceFromContext(ctx)
	provider.SetAgeGroupFromDob(ctx, profile)
}

func (id *Identity) GetProductIdFromAppId(ctx context.Context, appId string) (*string, error) {
	provider := id.GetIdentityServiceFromContext(ctx)
	return provider.GetProductIdFromAppId(ctx, appId)
}

func (id *Identity) GetIdentityServiceStr(ctx context.Context) IdentityService {
	provider := id.GetIdentityServiceFromContext(ctx)
	if provider == nil {
		return IdentityServiceUnknown
	}
	return provider.GetIdentityServiceStr()
}

func HttpRequest(ctx context.Context, c utils.HTTPClientInterface, method string, URL string, headers map[string]string, reqBody interface{}, respBody interface{}) error {
	log := logger.FromContext(ctx).With().Fields(map[string]interface{}{
		"method":  method,
		"headers": headers,
		"url":     URL,
		"reqBody": fmt.Sprintf("%v", reqBody),
	}).Logger()
	var reqBodyReader io.Reader
	if reqBody != nil {
		if reqBodyStr, ok := reqBody.(string); ok {
			reqBodyReader = strings.NewReader(reqBodyStr)
		} else {
			reqBodyJson, err := json.Marshal(reqBody)
			if err != nil {
				log.Error().Err(err).Msgf("failed to marshal reqBody %v", reqBody)
				return err
			}
			reqBodyReader = strings.NewReader(string(reqBodyJson))
		}
	}
	req, err := http.NewRequest(method, URL, reqBodyReader)
	if err != nil {
		log.Error().Err(err).Msgf("failed to create request")
		return err
	}

	for key, value := range headers {
		req.Header.Add(key, value)
	}

	resp, err := c.Do(req)
	if err != nil {
		log.Error().Err(err).Msgf("http request failed")
		return err
	}

	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("bad status code %d", resp.StatusCode)
	}

	if respBody != nil {
		// Uncomment to read body
		// body, err := ioutil.ReadAll(resp.Body)
		// if err != nil {
		// 	return err
		// }
		// err = json.Unmarshal(body, respBody)
		err = json.NewDecoder(resp.Body).Decode(respBody)
		return err
	}

	return nil
}

func (id *Identity) Start(ctx context.Context) {
	id.wg.Add(1)
	go func() {
		defer id.wg.Done()
		update := func() {
			for key, value := range id.providers {
				err := value.UpdateConfig(ctx)
				if err != nil {
					zlog.Error().Err(err).Msgf("updating %s failed", key)
				}
			}
		}

		ticker := time.NewTicker(id.timeout)
		id.ticker.Store(ticker)

		for {
			select {
			case <-ticker.C:
				update()
			case <-id.quit:
				zlog.Info().Msgf("identity ticker stoppped")
				return
			}
		}
	}()
}

func (id *Identity) Stop() {
	ticker := id.ticker.Load()
	if ticker == nil {
		return
	}

	// stop ticker
	ticker.(*time.Ticker).Stop()
	// send quit message to stop goroutine
	close(id.quit)
	// for to goroutine to finish
	id.wg.Wait()

	// clear ticker value
	var nilTicker *time.Ticker = nil
	id.ticker.Store(nilTicker)
}

func GetTenantFromCtx(ctx context.Context, id IdentityInterface) string {
	value := ctx.Value(constants.T2GPCtxTenant)
	if value != nil {
		tenant := value.(string)
		return tenant
	}
	if id == nil {
		return string(IdentityServiceUnknown)
	}
	tenant := string(IdentityServiceUnknown)
	idService := id.GetIdentityServiceFromContext(ctx)
	if idService == nil {
		return string(IdentityServiceUnknown)
	}
	tenant = string(idService.GetIdentityServiceStr())
	return tenant
}

func NewDNAIdentity(cfg *config.Config, sm health.ServiceMonitorInterface) *Identity {
	dnaProvider := NewDNAService(cfg)
	id := &Identity{
		cfg: cfg,
		providers: map[IdentityService]IdentityServiceInterface{
			IdentityServiceDNA: dnaProvider,
		},
		quit:    make(chan struct{}),
		timeout: 1 * time.Minute,
	}

	for key, value := range id.providers {
		sm.AddDependentService("social-service-id-"+string(key), value)
	}

	return id
}
