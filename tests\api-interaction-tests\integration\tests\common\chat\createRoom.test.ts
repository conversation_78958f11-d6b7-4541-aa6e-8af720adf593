import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let tokenHost: string;
let groupId: string;
beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
});
afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, groupId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
});
describe('', () => {
  /**
   * Checking creating a chat coom
   * - Create a chat room
   * - Checking the room maxMembers
   */
  it('create a room', async () => {
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .post('/chat/rooms')
      .set('Authorization', 'Bearer ' + tokenHost)
      .send({
        maxMembers: 6,
        public: true,
        password: config.roomPassword,
      });
    groupId = resp.body.groupid;
    // console.log(resp.body);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 6);
  });

  /**
   * Checking creating a chat coom
   * - Create a chat room
   * - set the maxMembers -1
   */
  it('create a room with negative maxMembers', async () => {
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .post('/chat/rooms')
      .set('Authorization', 'Bearer ' + tokenHost)
      .send({
        maxMembers: -1,
        public: true,
        password: config.roomPassword,
      });

    // console.log(resp.body);
    groupId = resp.body.groupid;
    expect(resp.body).toHaveProperty('maxMembers', 20);
  });
});
