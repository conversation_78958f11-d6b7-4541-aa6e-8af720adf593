data "aws_region" "current" {}
data "aws_caller_identity" "current" {}
data "aws_eks_cluster" "t2gp" {
  name = local.envvars[terraform.workspace]["cluster_name"]
}

data "aws_vpc" "vpc" {
  id = data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id
}

data "aws_security_group" "eks_pods" {
  tags = {
    "use_case" = "eks"
    "layer"    = "compute"
    "prefix"   = "d2c-east1"
  }
}

data "aws_security_group" "d2c_lambda" {
  tags = {
    "use_case" = "lambda"
  }
}

data "aws_security_group" "eks_nodes" {
  tags = {
    "aws:eks:cluster-name" = local.envvars[terraform.workspace]["cluster_name"]
  }
}


data "aws_subnets" "private_subnets" {
  filter {
    name   = "vpc-id"
    values = [data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id]
  }
  tags = {
    "tier" = "private"
  }
}


data "aws_eip" "jumpbox" {
  tags = {
    "project"      = "d2c-networking"
    "service_type" = "networking"
    "Name"         = "jumpbox-1"
  }
}

# SNS

data "aws_sns_topic" "social_abuse_report_topic" {
  name = "Social-abuse-report"
}