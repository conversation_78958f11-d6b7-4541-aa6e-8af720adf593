openapi: '3.0.0'
info:
  title: SSO Service - Game Client
  description: SSO Service endpoints for Game Clients.  All IDs are assumed to be for DNA services unless otherwise noted.  Properties denoted with a red asterisk in the models are required to send the specified request.  Non-denoted properties are either optional or read-only.
  version: 'Current Release Version: 1.50.0'
 
servers:
- url: https://sso.api.2kcoretech.online/sso/v2.0
 
paths:
  '/user/accounts/me/parent':
    get:
      description: "Retrieves the Account ID and display name of the linked Full Account for the current user.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetParentAccountResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
    patch:
      description: "Updates the display name of the linked Full Account for the current user.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      requestBody:
        description: The Display Name update request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateParentDisplayNameRequest'
      responses:
        204:
          description: No Content
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/client/user/accounts/{accountId}':
    patch:
      description: "Updates the name of the targeted Device Account associated with the current user.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclUserAccountsUpdateMe</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      parameters:
      - $ref: '#/components/parameters/accountId'
      requestBody:
        description: The Device Account name update request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateDeviceNameRequest'
      responses:
        204:
          description: No Content
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/me/links':
    get:
      description: "Retrieves a list of Accounts that are linked to the Platform or Device Account of the current user.  The list can also be filtered by using the 'filterBy' parameter in the query for Full, Platform, or Device Accounts.  If this optional parameter is not included in the query, all linked Accounts will be returned.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclUserAccountsGetMe</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      parameters:
        - $ref: '#/components/parameters/filterBy'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLinkedAccountsListResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
    post:
      description: "Creates a link between the Platform or Device Account of the current user and the specified Parent Account. This is the new endpoint which requires the 'linkType' in the request body as opposed to the legacy version of the endpoint ('/me/links/parent') which does not require the 'linkType' field in the request. Note the following rules that apply to Platform and Device Account linking:\n\n\n
        - Platform Accounts can only be linked to one Full Account at a time.\n\n
        - Platform Accounts cannot directly link to other Platform Accounts.\n\n
        - The link between a Platform Account and a Device Account must be initiated by the Device Account.\n\n
        - The link between a Full Account and a Device Account must be initiated by the Device Account.\n\n
        - Device Accounts can only be linked to one Full Account and one Platform Account type at a time.\n\n
        - Device Accounts cannot be linked directly to other Device Accounts.\n\n
        - When linking a Platform or Device Account to a Full Account, all pre-existing links on the Full Account will be inherited by the newly-linked Platform or Device Account, and the Full Account will inherit those already linked to the Platform or Device Account. Once unlinked from the Full Account, the inherited links will be removed.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclUserAccountsLinkMe</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      requestBody:
        description: The Account link request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountLinkRequestNewEndpoint'
      responses:
        201:
          description: Created
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/me/links/{accountId}':
    delete:
      description: "Removes the link between the Platform or Device Account of the current user and the Account specified in the path.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclUserAccountsLinkMe</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      parameters:
        - $ref: '#/components/parameters/unlinkAccountId'
      responses:
        204:
          description: No Content
          headers:
            X-2k-Result-Parent-Account-Id:
              description: The new Parent Account ID if unlinking caused a change in Parent Account status.
              schema:
                type: integer
                format: uint32
            X-2k-Result-Access-Token:
              description: The new JWT access token if unlinking caused a change to the Account status of the current user.
              schema:
                type: string
                format: jwt
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/me/links/parent':
    post:
      description: "Creates a link between the Platform Account of the current user and the target Account. Note that once linked, the Account of the current user will inherit the linked Accounts of the target Account, and the target Account will inherit the links of the Account of the current user.  Also note that Device Accounts cannot link through this endpoint.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      requestBody:
        description: accountLink
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountLinkRequestLegacyEndpoint'
      responses:
        201:
          description: Created
          headers:
            X-2k-Result-Parent-Account-Id:
              description: The new Parent Account ID, which is only present when the Account of the current user is a Platform Account.
              schema:
                type: integer
                format: uint32
            X-2k-Result-Access-Token:
              description: The new JWT access token, which is only present when the Account of the current user is a Platform Account.
              schema:
                type: string
                format: jwt
            X-2k-Account-Display-Name:
              description: The display name of the Parent Account, which is only present if Parent Account have a display name.
              schema:
                type: string
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
    delete:
      description: "Removes the link between the Platform Account of the current user and its Parent Full Account.  Note that Device Accounts cannot unlink through this endpoint.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      responses:
        204:
          description: No Content
          headers:
            X-2k-Result-Parent-Account-Id:
              description: The new Parent Account ID if unlinking caused a change in Parent Account status.
              schema:
                type: integer
                format: uint32
            X-2k-Result-Access-Token:
              description: The new JWT access token if unlinking caused a change to the Account status of the current user.
              schema:
                type: string
                format: jwt
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/me/links/platform':
    post:
      description: "Creates a link between the Full Account and a Platform Account in the context of a Full Account (JWT).\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      requestBody:
        description: The Account link request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountLinkPlatformRequest'
      responses:
        201:
          description: Created
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/client/user/accounts/me/apps':
    get:
      description: "Retrieves the list of Application IDs the Platform Account of the current user has ever logged into. This function cannot be used for Full or Device Accounts.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclUserAccountsGetMeApps</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAppLoginResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/subscriptions/me/parent':
    post:
      description: "Subscribes the Parent Full Account on behalf of the current Platform Account to one or more newsletters.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Subscriptions
      security:
        - accessToken: []
      requestBody:
        description: The Parent Account subscription request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ParentSubscriptionRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ParentSubscriptionResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/ageratings':
    get:
      description: "Retrieves a paginated list of Age Ratings sorted by ascending Country.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Age Ratings
      security:
        - appId: []
      parameters:
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/limit'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetAgeRatingsResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens':
    post:
      description: "Issues unique Access and Refresh Tokens once valid credentials have been provided by the current user.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - appId: []
      parameters:
        - $ref: '#/components/parameters/psnRegion'
      requestBody:
        description: The Account authorization login request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountLoginRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountLoginResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/logout':
    post:
      description: "Logs the current user out of the session and revokes the Access and Refresh Tokens associated with that session.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - accessToken: []
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountLogoutResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/exchange':
    post:
      description: "Logs into a different linked Device Account using a Device Account JWT or a different linked Platform Account using a Platform Account JWT.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - accessToken: []
      requestBody:
        description: The Account login request.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                accountId:
                  description: The Account ID to be logged in.
                  type: string
                  format: ID
                  example: 14ff90c740fb4f0b9386af202ba48a6c
      responses:
        200:
          description: OK
          content:
            application/json:
                schema:
                  $ref: '#/components/schemas/AccountLoginResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/augment':
    post:
      description: "Augments a Platform Account access token (JWT) with the Primary Account ID from the request as a JWT Claim. The purpose is to specify which dataset to use for a cross-progression-enabled DNA Application.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - accessToken: []
      requestBody:
        description: The Account augment request.
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                primaryAccountId:
                  description: The Account ID to specify which dataset to use for a cross-progression-enabled DNA Application.
                  type: string
                  format: ID
                  example: 14ff90c740fb4f0b9386af202ba48a6c
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccountLoginResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/device_authorization':
    post:
      description: "Generates a unique Device verification code and an end-user code that are valid for a limited time, and required for Device Authorization.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      security:
      - appId: []
      tags:
        - 'Device Authorization'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NewDeviceAuthorizationResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
    get:
      description: "Validates the end-user verification code.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - 'Device Authorization'
      security:
        - appId: []
      parameters:
        - $ref: '#/components/parameters/userCode'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetValidateUserCodeResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/legal/documents/{documentId}':
   get:
      description: "Retrieves the specified Legal Document by its ID.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Legal Documents
      security:
        - accessToken: []
      parameters:
        - $ref: '#/components/parameters/documentId'
        - $ref: '#/components/parameters/documentLocale'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLegalDocumentResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/me/responses':
    post:
      description: "Records the response to one or more Legal Documents for the current user.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Legal Documents
      security:
        - accessToken: []
      requestBody:
        description: The Legal Document response list.
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/LegalResponseRequest'
      responses:
        201:
          description: Created
          headers:
            X-2k-Result-Account-Type:
              description: The new Account type if the Legal Document response resulted in the change.
              schema:
                type: integer
                format: uint32
            X-2k-Result-Access-Token:
              description: The new JWT access token if the Legal Document response resulted in the change of the Account type.
              schema:
                type: string
                format: jwt
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/presence/heartbeats':
    post:
      description: "Sends a keep-alive heartbeat for the current logged-in session.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclHeartbeatGet</i>"
      tags:
        - Presence
      security:
        - accessToken: []
      responses:
        201:
          description: Created
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/jwks.json':
    get:
      description: "A JSON object that represents a set of JWKs (JSON Web Key Set). Returns a list of SSO public keys for validating the token signature.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - JSON Web Key Sets (JWKs)
      parameters:
        - $ref: '#/components/parameters/ifModifiedSince'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetJwksResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/openid-configuration':
    get:
      description: "Returns the OpenID configuration.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - JSON Web Key Sets (JWKs)
      parameters:
        - $ref: '#/components/parameters/ifModifiedSince'
      responses:
        200:
          description: OK
 
components:
  securitySchemes:
    appId:
      type: apiKey
      name: Authorization
      in: header
    accessToken:
      type: oauth2
      flows:
        implicit:
           authorizationUrl: https://tbd.2k.com
           scopes: {}
 
  schemas:
    AccountLoginRequest:
      type: object
      required:
      - accountType
      properties:
        locale:
          description: The locale for the current user.
          type: string
          format: xx-XX
          example: 'en-US'
        accountType:
          description: The type of Account the current user is authenticating against.
          type: string
          format: platform
        credentials(appleId):
          $ref: '#/components/schemas/appleIdCredentials'
        credentials(calico):
          $ref: '#/components/schemas/calicoCredentials'
        credentials(device):
          $ref: '#/components/schemas/deviceCredentials'
        credentials(epic):
          $ref: '#/components/schemas/epicCredentials'
        credentials(facebook):
          $ref: '#/components/schemas/facebookCredentials'
        credentials(gamecenter):
          $ref: '#/components/schemas/gameCenterCredentials'
        credentials(google):
          $ref: '#/components/schemas/googleCredentials'
        credentials(googlePlay):
          $ref : '#/components/schemas/googlePlayCredentials'
        credentials(nintendo):
          $ref: '#/components/schemas/nintendoCredentials'
        credentials(oauthDevice):
          $ref: '#/components/schemas/oauthDeviceCredentials'
        credentials(psn):
          $ref: '#/components/schemas/psnCredentials'
        credentials(refresh):
          $ref: '#/components/schemas/refreshCredentials'
        credentials(server):
          $ref: '#/components/schemas/serverCredentials'
        credentials(stadia):
          $ref: '#/components/schemas/stadiaCredentials'
        credentials(steam):
          $ref: '#/components/schemas/steamCredentials'
        credentials(twitch):
          $ref: '#/components/schemas/twitchCredentials'
        credentials(twitter):
          $ref: '#/components/schemas/twitterCredentials'
        credentials(wegame):
          $ref: '#/components/schemas/weGameCredentials'
        credentials(windowsDev):
          $ref: '#/components/schemas/windowsDevCredentials'
        credentials(xbl):
          $ref: '#/components/schemas/xblCredentials'
        credentials(zendesk):
          $ref: '#/components/schemas/zendeskCredentials'
        credentials(t2gp):
          $ref: '#/components/schemas/t2gpCredentials'
    appleIdCredentials:
      description: The credentials to be used for Apple ID authentication.
      type: object
      required:
        - type
        - appleId
        - appleIdToken
      properties:
        type:
          description: The Apple ID credential type being supplied for the login request.
          type: string
          example: 'appleId'
        appleId:
          description: The Apple ID for the current user, which corresponds to the 'sub' field of the Apple identity JWT.
          type: string
        appleIdToken:
          description: The Apple ID Token issued by the Apple ID authentication server.
          type: string
    calicoCredentials:
      description: The credentials to be used for Calico authentication.
      type: object
      required:
        - type
        - calicoId
        - calicoToken
        - dob
      properties:
        type:
          description: The Calico credential type being supplied for the login request.
          type: string
          example: 'calico'
        calicoId:
          description: The Calico ID for the current user.
          type: string
        calicoToken:
          description: The Calico Token issued by the Calico authentication server.
          type: string
        dob:
          description: The date of birth for the current user.
          type: string
          format: mm/dd/yyyy
    deviceCredentials:
      description: The credentials to be used for Device authentication.
      type: object
      required:
        - type
        - deviceId
      properties:
        type:
          description: The Device credential type being supplied for the login request.
          type: string
          example: 'device'
        deviceId:
          description: The Device identifier used for Device authentication.
          type: string
        deviceName:
          description: The name of the Device.
          type: string
        dob:
          description: The date of birth for the current user.
          type: string
          format: mm/dd/yyyy
    epicCredentials:
      description: The credentials to be used for Epic authentication.
      type: object
      required:
        - type
        - epicUserId
        - epicUserName
        - epicAccessToken
      properties:
        type:
          description: The Epic credential type being supplied for the login request.
          type: string
          example: 'epic'
        epicUserId:
          description: The Epic User ID for the current user.
          type: string
        epicUserName:
          description: The Epic first party alias for the current user.
          type: string
        epicAccessToken:
          description: The Epic Access Token issued by the Epic authentication server.
          type: string
    facebookCredentials:
      description: The credentials to be used for Facebook authentication.
      type: object
      required:
        - type
        - facebookId
        - facebookName
        - facebookToken
        - facebookAgeRange
      properties:
        type:
          description: The Facebook credential type being supplied for the login request.
          type: string
          example: 'facebook'
        facebookId:
          description: The Facebook ID for the current user.
          type: string
        facebookName:
          description: The Facebook Name of the current user.
          type: string
        facebookToken:
          description: The Facebook Token issued by the Facebook authentication server.
          type: string
        facebookAgeRange:
          description: The age range of the current user.
          type: object
          properties:
            min:
              description: The lower bounds of the age range for the current user.
              type: integer
              enum: [13, 18, 21]
            max:
              description: The upper bounds of the age range for the current user.
              type: integer
              enum: [17, 20]
    gameCenterCredentials:
      description: The credentials to be used for Game Center authentication.
      type: object
      required:
        - type
        - gamecenterId
        - gamecenterAlias
        - isUnderage
      properties:
        type:
          description: The Game Center credential type being supplied for the login request.
          type: string
          example: 'gamecenter'
        gamecenterId:
          description: The Game Center ID for the current user.
          type: string
        gamecenterAlias:
          description: The Game Center Alias of the current user.
          type: string
        isUnderage:
          description: Indicates whether the user is underage. The value is 'true' if the current user is not old enough to agree to Legal Documents.
          type: boolean
          enum: [true, false]
        gamecenterPublicKeyUrl:
          description: The URL used to get a public key for signature validation.
          type: string
        gamecenterSignature:
          description: The Game Center data signature to be validated.
          type: string
        gamecenterSalt:
          description: The Salt used to create a data signature.
          type: string
        gamecenterTimestamp:
          description: The UNIX timestamp that indicates when the validation data was issued.
          type: integer
          format: uint64
    googleCredentials:
      description: The credentials to be used for Google authentication. Note that the 'googleAccessToken' and 'googleIdToken' properties cannot be used simultaneously in the same request.
      type: object
      required:
        - type
      properties:
        type:
          description: The Google credential type being supplied for the login request.
          type: string
          example: 'google'
        googleId:
          description: The Google ID for the current user, which is required when using the 'googleAccessToken' field in the request.
          type: string
        googleDisplayName:
          description: The Google Display Name for the current user, which is required when using the 'googleAccessToken' field in the request.
          type: string
        googleAccessToken:
          description: The Google Access Token issued by the Google authentication server and is required if the 'googleIdToken' field is missing in the request.
          type: string
        googleIdToken:
          description: The Google ID Token issued by the Google authentication server and is required if the 'googleAccessToken' field is missing in the request.  When using this field, no other field is required in the request.
          type: string
    googlePlayCredentials:
      description: The credentials to be used for Google Play authentication.
      type: object
      required:
        - type
        - googlePlayId
        - googlePlayDisplayName
        - googlePlayToken
      properties:
        type:
          description: The Google Play credential type being supplied for the login request.
          type: string
          example: 'googlePlay'
        googlePlayId:
          description: The Google Play ID for the current user.
          type: string
        googlePlayDisplayName:
          description: The Google Play Display Name for the current user.
          type: string
        googlePlayToken:
          description: The Google Play Token issued by the Google authentication server.
          type: string
    nintendoCredentials:
      description: The credentials to be used for Nintendo authentication.
      type: object
      required:
        - type
        - nsaId
        - nsaNickname
        - nsaToken
        - naAuthCode
      properties:
        type:
          description: The Nintendo credential type being supplied for the login request.
          type: string
          example: 'nintendo'
        nsaId:
          description: The Nintendo Service Account ID for the current user.
          type: string
        nsaNickname:
          description: The Nintendo Service Account Nickname for the current user.
          type: string
        nsaToken:
          description: The Nintendo Service Account Token issued by the Nintendo authentication server.
          type: string
        naAuthCode:
          description: The Nintendo Account Authorization Code issued by the Nintendo authentication server.
          type: string
    oauthDeviceCredentials:
      description: The credentials to be used for OAuth Device authentication.
      type: object
      required:
        - deviceCode
      properties:
        deviceCode:
          description: The device verification code.
          type: string
          example: 'NGU5OWFiNjQ5YmQwNGY3YTdmZTEyNzQ3YzQ1YSA'
    psnCredentials:
      description: The credentials to be used for PSN authentication.
      type: object
      required:
        - type
        - psnAccountId
        - psnOnlineId
        - dob
        - psnAuthCode
        - psnEnvironment
        - age
      properties:
        type:
          description: The PSN credential type being supplied for the login request.
          type: string
          example: 'psn'
        psnAccountId:
          description: The PSN Account ID for the current user.
          type: integer
          format: uint64
        psnOnlineId:
          description: The PSN Online ID for the current user.
          type: string
        dob:
          description: The date of birth for the current user (required if 'age' is null).
          type: string
          format: mm/dd/yyyy
        psnAuthCode:
          description: The PSN Authorization Code issued to the current user.  This unique code can only be used once when issued.
          type: string
        psnEnvironment:
          description: "The PSN Environment that issued the authorization code.\n\n\n
            List of environments for reference:\n\n
            1 - DEVELOPMENT\n\n
            8 - QA\n\n
            256 - PRODUCTION"
          type: integer
        psnRegion:
          description: The PSN Region the current user is logging in from.  This field is required if the 'psnRegion' is not present in the request headers.
          type: string
          example: 'scea|scee|scej'
        age:
          description: The PSN user age for the current user (required if 'dob' is null).
          type: integer
          format: uint64
    refreshCredentials:
      description: The credentials to be used for Refresh Token authentication.
      type: object
      required:
        - type
        - refreshToken
      properties:
        type:
          description: The Refresh Token credential type being supplied to refresh the access token for the current user.
          type: string
          example: 'refresh'
        refreshToken:
          description: The Refresh Token value, which was issued to the current user upon successful login.
          type: string
        psnRegion:
          description: The PSN Region the current user is logging in from.  This field is required if the 'psnRegion' is not present in the request headers.  Note that this is only for PSN-based refresh tokens requests.
          type: string
          example: 'scea|scee|scej'
    serverCredentials:
      description: The credentials to be used for Server authentication.
      type: object
      required:
        - type
        - instanceId
      properties:
        type:
          description: The Server credential type being supplied for the login request.
          type: string
          example: 'server'
        instanceId:
          description: A 32-character no-dash GUID that uniquely identifies the current server instance.
          type: string
          format: ID
    stadiaCredentials:
      description: The credentials to be used for Stadia authentication.
      type: object
      required:
        - type
        - stadiaPlayerId
        - gamerTag
        - stadiaToken
      properties:
        type:
          description: The Stadia credential type being supplied for the login request.
          type: string
          example: 'stadia'
        stadiaPlayerId:
          description: The Stadia Player ID for the current user.
          type: string
        gamerTag:
          description: The Stadia Gamertag for the current user.
          type: string
        stadiaToken:
          description: The Stadia token issued by the Stadia authentication server.
    steamCredentials:
      description: The credentials to be used for Steam authentication.
      type: object
      required:
        - steamUserId
        - steamProfileName
      properties:
        type:
          description: The Steam credential type being supplied for the login request.
          type: string
          example: 'steam'
        steamUserId:
          description: The Steam User ID for the current user.
          type: string
          format: uint64
        steamProfileName:
          description: The Steam Profile Name for the current user.
          type: string
        encryptedAppTicket:
          description: The Steam Encrypted Application Ticket, which can be used to verify the identity of the current user between the Game Client and the server.
          type: string
    twitchCredentials:
      description: The credentials to be used for Twitch authentication.
      type: object
      required:
        - type
      properties:
        type:
          description: The Twitch credential type being supplied for the login request.
          type: string
          example: 'twitch'
        twitchId:
          description: The Twitch ID for the current user, which is required when using the 'twitchAccessToken' field in the request.
          type: string
        twitchUserName:
          description: The Twitch User Name for the current user, which is required when using the 'twitchAccessToken' field in the request.
          type: string
        twitchAccessToken:
          description: The Twitch Access Token issued by the Twitch authentication server and is required if the 'twitchIdToken' field is missing in the request.
          type: string
        twitchIdToken:
          description: The Twitch ID Token (OIDC) issued by the Twitch authentication server and is required if the 'twitchAccessToken' field is missing in the request.  When using this field, no other field is required in the request.
          type: string
        twitchNonce:
          description: The Twitch Nonce that is provided when the Twitch ID issued by the Twitch authentication server and is optional when the 'twitchIdToken' field is present in the request.
          type: string
    twitterCredentials:
      description: The credentials to be used for Twitter authentication.
      type: object
      required:
        - type
        - twitterId
        - twitterUserName
        - twitterAccessToken
        - twitterAccessTokenSecret
      properties:
        type:
          description: The Twitter credential type being supplied for the login request.
          type: string
          example: 'twitter'
        twitterId:
          description: The Twitter ID for the current user.
          type: string
        twitterUserName:
          description: The Twitter User Name for the current user.
          type: string
        twitterAccessToken:
          description: The Twitter Access Token issued by the Twitter authentication server.
          type: string
        twitterAccessTokenSecret:
          description: The Twitter Access Token Secret issued by the Twitter authentication server.
          type: string
    weGameCredentials:
      description: The credentials to be used for WeGame authentication.
      type: object
      required:
        - railId
        - railPlayerName
        - isUnderage
        - railSessionTicket
      properties:
        type:
          description: The WeGame credential type being supplied for the login request.
          type: string
          example: 'wegame'
        railId:
          description: The Rail ID for the current user.
          type: string
        railPlayerName:
          description: The Rail Player Name for the current user.
          type: string
        isUnderage:
          description: Indicates whether the user is underage. The value is 'true' if the current user is not old enough to agree to Legal Documents.
          type: boolean
          enum: [true, false]
        railSessionTicket:
          description: The Rail Session Ticket issued by the WeGame authentication server.
          type: string
    windowsDevCredentials:
      description: The credentials to be used for Windows Developer authentication.
      type: object
      required:
        - type
        - userName
        - isUnderage
      properties:
        type:
          description: The Windows Developer credential type being supplied for the login request.
          type: string
          example: 'windowsDev'
        userName:
          description: The authenticated Windows username and domain name.
          type: string
          format: username@domain
        userAlias:
          description: The User Alias/Gamertag of the current user.
          type: string
        isUnderage:
          description: Indicates whether the user is underage. The value is 'true' if the current user is not old enough to agree to Legal Documents.
          type: boolean
          enum: [true, false]
    xblCredentials:
      description: The credentials to be used for XBL authentication.
      type: object
      required:
        - xblXuid
        - xblGamertag
        - ageGroup
        - xstsToken
      properties:
        type:
          description: The XBL credential type being supplied for the login request.
          type: string
          example: 'xbl'
        xblXuid:
          description: The Xbox User ID for the current user.
          type: integer
          format: uint64
        xblGamertag:
          description: The Xbox Gamertag for the current user.
          type: string
        ageGroup:
          description: "The Age Group of the current user.\n\n\n
            List of groups for reference:\n\n
            0 - UNKNOWN\n\n
            1 - UNDER 13 YEARS\n\n
            2 - 13-17 YEARS\n\n
            3 - 18-24 YEARS\n\n
            4 - 25-34 YEARS\n\n
            5 - 35 YEARS AND ABOVE\n\n
            6 - 13 YEARS AND ABOVE"
          type: integer
          format: uint32
        xstsToken:
          description: The Xbox Secure Token Server Token issued by the XBL authentication server.
          type: string
          format: jwt
    zendeskCredentials:
      description: The credentials to be used for Zendesk authentication.
      type: object
      required:
        - zendeskId
        - zendeskName
        - zendeskToken
      properties:
        type:
          description: The Zendesk credential type being supplied for the login request.
          type: string
          example: 'zendesk'
        zendeskId:
          description: The Zendesk User ID for the current user.
          type: string
          format: uint64
        zendeskName:
          description: The Zendesk Name for the current user.
          type: string
        zendeskToken:
          description: The Zendesk Token issued by the Zendesk authentication server.
          type: string
          format: jwt
    t2gpCredentials:
        description: The credentials to be used for T2GP authentication.
        type: object
        required:
          - accessToken
        properties:
          type:
            description: The T2GP credential type being supplied for the login request.
            type: string
            example: 't2gp'
          accessToken:
            description: The T2GP Token from the Full Account.
            type: string
            format: jwt
    AccountLoginResponse:
      type: object
      properties:
        accessToken:
          description: The JWT (JSON Web Token) that is issued upon successful login.
          type: string
          format: jwt
        accessTokenExpiresIn:
          description: The number of seconds (TTL) until the issued access token expires.
          type: integer
          format: uint32
        refreshToken:
          description: The JWT (JSON Web Token) that is issued upon successful login.  This token is used to refresh a current session.
          type: string
          format: jwt
        refreshTokenExpiresIn:
          description: The number of seconds (TTL) until the issued refresh token expires.
          type: integer
          format: uint32
        accountId:
          description: The Account ID that has been authenticated upon successful login.
          type: string
          format: ID
        accountType:
          description: "The Account type that has been authenticated upon successful login.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - ANONYMOUS\n\n
            2 - PLATFORM\n\n
            3 - FULL\n\n
            4 - TOOLS\n\n
            5 - NEWSLETTER\n\n
            6 - UNDISCLOSED\n\n
            7 - PRIVACY POLICY ACCEPTED ONLY"
          type: integer
          format: uint32
        parentAccountId:
          description: The Parent Account ID to which the linked Platform Account belongs.
          type: string
          format: ID
        legalManifest:
          $ref: '#/components/schemas/GetLegalManifestResponse'
        playFabTicket:
          description: A unique ID issued upon successful login if the Application the current user has logged in with has the 'Issue PlayFab Ticket' feature enabled.
          type: string
        sessionId:
          description: The unique ID associated with the current session upon successful login.
          type: string
        displayName:
          description: The display name for the current user.
          type: string
    AccountLogoutResponse:
      type: array
      items:
        type: object
        properties:
          state:
            description: The current state of the Access Token or Refresh Token.
            type: string
            example: 'revoked'
          jti:
            description: The Access Token or Refresh Token ID that was generated when the token was created.
            type: string
            format: ID
    GetParentAccountResponse:
      type: object
      properties:
        id:
          description: The Account ID of the linked Full Account.
          type: string
          format: ID
        displayName:
          description: The display name for the linked Full Account.
          type: string
          pattern: '^[a-zA-Z]{3,25}$'
    UpdateParentDisplayNameRequest:
      type: object
      required:
        - displayName
      properties:
        displayName:
          description: The display name for the linked Full Account.
          type: string
          minimum: 3
          maximum: 25
          pattern: '^[a-zA-Z]{3,25}$'
    UpdateDeviceNameRequest:
      type: object
      required:
        - deviceName
      properties:
        deviceName:
          description: The name of the specified Device Account.
          type: string
    GetAppLoginResponse:
      type: array
      items:
        type: string
        format: ID
    AccountLinkRequestLegacyEndpoint:
      type: object
      properties:
        email:
          description: The email address of the Full Account, existing or non-existing, to be linked.
          type: string
          format: email
        password:
          description: The password of the existing Full Account to be linked.
          type: string
          format: password
        newsletters:
          description: The list of newsletters to be subscribed to. The newsletters will be appended to the parent account.
          type: array
          items:
            type: string
          example: ['2k']
    AccountLinkRequestNewEndpoint:
      type: object
      properties:
        email:
          description: The email address of the Full Account, existing or non-existing, to be linked.  The field is required for linking existing or non-existing Full Accounts when the 'accessToken' field is not present in the request.
          type: string
          format: email
        password:
          description: The password of the existing Full Account to be linked. This field is required for linking existing Full Accounts when the 'accessToken' field is not present in the request.
          type: string
          format: password
        accessToken:
          description: The access token for the Full or Platform Account to be linked.  Note that only Device Accounts can link using this field in the request, and only this field is required for linking when the 'email' and 'password' fields are not present in the request.
          type: string
          format: jwt
        linkType:
          description: The Account type to be linked.  When linking a Device Account to a Full or Platform Account, or linking a Platform Account to a Full Account, the 'linkType' is always 'parent' for the request.
          type: string
          enum: ['parent']
        newsletters:
          description: The list of newsletters to be subscribed to. The newsletters will be appended to the parent account.
          type: array
          items:
            type: string
          example: ['2k']
    AccountLinkPlatformRequest:
      type: object
      properties:
        accessToken:
          description: The access token of the Platform Account to be linked.
          type: string
          format: jwt
    GetLinkedAccountsListResponse:
      type: array
      items:
        type: object
        properties:
          linkType:
            description: The linked Account platform type.  Exceptions to this are 'parent' which identifies the Full Account that has been linked, and 'device' which identifies a Device Account.
            type: string
          accountId:
            description: The Account ID of the linked Account.
            type: string
          accountType:
            description: "The linked Account type, which is usually Platform (2), or Full/Parent (3).\n\n\n
              List of types for reference:\n\n
              0 - UNKNOWN\n\n
              1 - ANONYMOUS\n\n
              2 - PLATFORM\n\n
              3 - FULL\n\n
              4 - TOOLS\n\n
              5 - NEWSLETTER\n\n
              6 - UNDISCLOSED\n\n
              7 - PRIVACY POLICY ACCEPTED ONLY"
            type: integer
            format: uint32
          onlineServiceType:
            description: "The online service platform of the linked Platform Account.\n\n\n
              List of types for reference:\n\n
              0 - UNKNOWN\n\n
              1 - XBOX LIVE\n\n
              2 - SONY ENTERTAINMENT NETWORK\n\n
              3 - STEAM\n\n
              4 - WEB\n\n
              5 - LEGACY GAME CENTER\n\n
              6 - GOOGLE PLAY\n\n
              9 - WINDOWS PHONE\n\n
              10 - CALICO\n\n
              11 - NINTENDO\n\n
              12 - GAME CENTER\n\n
              13 - WEGAME\n\n
              14 - VORTEX\n\n
              15 - EPIC\n\n
              16 - STADIA\n\n
              17 - FACEBOOK\n\n
              18 - GOOGLE\n\n
              19 - TWITTER\n\n
              20 - TWITCH\n\n
              21 - DEVICE\n\n
              22 - APPLE\n\n
              23 - ZENDESK\n\n
              24 - T2GP\n\n
              99 - WINDOWS DEVELOPER"
            type: integer
            format: uint32
          alias:
            description: The First Party alias of the linked Platform Account. For Device Accounts, the Device Name will be displayed. The Full Account will not have an alias.
            type: string
          firstPartyId:
            description: The First Party ID of the linked Platform Account. For Device Accounts, the Device ID will be displayed.
            type: string
    LegalResponseRequest:
      type: object
      required:
      - documentId
      - isAccepted
      properties:
        documentId:
          description: The Legal Document ID, which can be obtained from the 'Location' response header when the Legal Document is created.
          type: string
          format: ID
        isAccepted:
          description: Whether the current user has accepted the Legal Document.
          type: boolean
          enum: [true, false]
        type:
          description: "The Legal Document type.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - TERMS OF SERVICE\n\n
            2 - PRIVACY POLICY\n\n
            3 - END USER LICENSE AGREEMENT\n\n
            4 - DEMO\n\n
            5 - OPTIONAL\n\n
            6 - CUSTOM\n\n
            7 - RESERVED"
          type: integer
          format: uint32
        appId:
          description: The Application ID associated with the Legal Document.
          type: string
          format: ID
    GetLegalDocumentResponse:
      type: object
      properties:
        documentId:
          description: The Legal Document ID, which can be obtained from the 'Location' response header when the Legal Document is created.
          type: string
          format: ID
        title:
          description: The localized title of the specified Legal Document.
          type: string
        date:
          description: The localized date of the specified Legal Document.
          type: string
        content:
          description: The localized content contained within the specified Legal Document.
          type: string
        type:
          description: "The Legal Document type.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - TERMS OF SERVICE\n\n
            2 - PRIVACY POLICY\n\n
            3 - END USER LICENSE AGREEMENT\n\n
            4 - DEMO\n\n
            5 - OPTIONAL\n\n
            6 - CUSTOM\n\n
            7 - RESERVED"
          type: integer
          format: uint32
    GetLegalManifestResponse:
      type: array
      items:
        type: object
        properties:
          documentId:
            description: The Legal Document ID, which was generated when the Legal Document was created.
            type: string
            format: ID
          title:
            description: The localized title of the specified Legal Document.
            type: string
          date:
            description: The localized date of the specified Legal Document.
            type: string
          type:
            description: "The Legal Document type.\n\n\n
              List of types for reference:\n\n
              0 - UNKNOWN\n\n
              1 - TERMS OF SERVICE\n\n
              2 - PRIVACY POLICY\n\n
              3 - END USER LICENSE AGREEMENT\n\n
              4 - DEMO\n\n
              5 - OPTIONAL\n\n
              6 - CUSTOM\n\n
              7 - RESERVED"
            type: integer
            format: uint32
    GetJwksResponse:
      type: object
      properties:
        keys:
          description: The supported JWKS key.
          type: array
          items:
            $ref: '#/components/schemas/JwksItem'
          example:
            - kty: RSA
              e: AQAB
              use: sig
              kid: 755d9354-1359-11eb-adc1-0242ac120002
              alg: RS256
              'n': l_BSeK2DcWDrG6dqNlEsHJWk5ZEObC3k_9cCr5jjiBaNSim-XO4eY96BK_QA44eJTORwo_Z8CnhTNU_H0MWQ-_dnYaY7RKaK67v_g6JqPVY8o7gcYSOwCZ-ppYg0ZXuEl8hSDlC99Dw5eSbRbVA90Ef4OHx22kPSQsxVVWX0frINrnV3h2gbxeMa-gjzTWNUmTBXGQK5Mu4XfT67ubBY5Hm0KC291ONQBZ7sLnflMMW0FgnPj_v9-FMN-QpOkeZGmDbLKWg4w3XrCLuXIPVStodv_zwANoFNdc3hUhdxEnz_oK1pUpGeazbr-MGukJ3sbVtm1clFJ5mnjCQ7yeHBjw
            - kty: RSA
              e: AQAB
              use: sig
              kid: 03a61152-ecd9-4936-80d2-4536c9e64556
              alg: RS256
              'n': q2IUn5RQ6kP9U_JRcKxeTTE9fqP4Rx6tsHP4yFYoTQHTD07AdUI1uDzNcC5A3vvujZJ2mzerJGfC6u9ueLzN62_jQ6RBE-zlB4jzpe2XEMvB01MpdJwt_TQGCIJVwkoltoKr8A8x3s0EfkViRgKaA8VuIHwyV8XolYtS9_h-UoVm9KCNuAbJJLPG_zuHjHBTpeJlsY6vzdgjuM5pyaHUUHbzrIEV69-Slh6h68TS8iZH7MS4OMvOruNfkytM_cCa85N3gtNUgh2P_7eyqP3k6XX1Y-MHiKQF_iDbglKawKQObXXTNKK_waUraKRWUFchbjN0lF7MO0cKBaHX8qXtnQ
    JwksItem:
      type: object
      properties:
        kty:
          description: The Key Type parameter identifies the cryptographic algorithm family used with the key, such as 'RSA' or 'EC'.
          type: string
        e:
          description: The Exponent parameter contains the exponent value for the RSA public key. It is represented as a Base64urlUInt-encoded value.
          type: string
        use:
          description: The Public Key Use Parameter identifies the intended use of the public key. Values defined by this specification are 'sig' (signature) 'enc' (encryption).
          type: string
        kid:
          description: The Key ID parameter is used to match a specific key. This is used, for instance, to choose among a set of keys within a JWK Set during key rollover.
          type: string
        alg:
          description: The Algorithm parameter identifies the algorithm intended for use with the key.
          type: string
        n:
          description: The 'n', or Modulus parameter contains the modulus value for the RSA public key. It is represented as a Base64urlUInt-encoded value.
          type: string
    GetAgeRatingsResponse:
      type: object
      properties:
        ratingId:
          description: The unique ID of the Age Rating used to determine the age group of the specified user.
          type: integer
          example: 101
        description:
          description: The description of the specified Age Rating.
          type: string
        country:
          description: The country which the specified Age Rating is applied to.
          type: string
          example: 'US'
        minAge:
          description: The minimum age of the specified Age Rating.
          type: integer
          example: 10
        ratingCode:
          description: The Age Rating code which consists of a two-letter country code and the minimum age.
          type: string
          example: 'US10'
        createdOn:
          description: The UNIX timestamp that indicates when the specified Age Ratings was created.
          type: integer
          format: uint64
        modifiedOn:
          description: The UNIX timestamp that indicates when the specified Age Ratings was last modified.
          type: integer
          format: uint64
    ParentSubscriptionRequest:
      type: object
      required:
        - newsletters
      properties:
        newsletters:
          description: The list of newsletters to be subscribed to.
          type: array
          items:
            type: string
        emailBaseUrl:
          description: Supports the ability to override the base URL when creating a link for email user notification such as Account verification, reset password, etc.
          type: string
        additionalQueryParams:
          description: A map of query parameters that will be added to email URL.
          example: {'client_id' : '12345', 'state' : 'state'}
    ParentSubscriptionResponse:
      type: array
      items:
        type: object
        properties:
          newsletter:
            description: The newsletter to be subscribed to.
            type: string
          state:
            description: The current state of each newsletter subscription..
            type: string
            enum: [FAILED, NEW_SUBSCRIBED, ALREADY_SUBSCRIPTION]
            example: 'FAILED'
          message:
            description: The error message for each newsletter subscription.
            type: string
            example: The parent full account is not of the correct age to subscribe.
    NewDeviceAuthorizationResponse:
      type: object
      properties:
        userCode:
          description: The end-user verification code.
          type: string
          example: 'BDWP-HQPK'
        deviceCode:
          description: The device verification code.
          type: string
          example: 'NGU5OWFiNjQ5YmQwNGY3YTdmZTEyNzQ3YzQ1YSA'
        interval:
          description: The minimum amount of time in seconds that the client should wait between polling requests to the token endpoint.
          type: integer
          example: 5
        expiresIn:
          description: The lifetime in seconds of the deviceCode and userCode.
          type: integer
    GetValidateUserCodeResponse:
      type: object
      properties:
        clientId:
          description: The Application ID associated with the Device console login/registration.
          type: string
          format: ID
 
    ErrorResponse:
      type: object
      required:
        - code
        - message
      properties:
        code:
          description: A code representing the error that occurred.
          type: integer
          format: uint32
        message:
          description: A string describing the error that occurred.
          type: string
 
  parameters:
      offset:
        name: offset
        in: query
        description: The pagination offset to use.
        required: false
        schema:
          type: integer
          format: uint32
      limit:
        name: limit
        in: query
        description: The pagination limit to use.
        required: false
        schema:
          type: integer
          format: uint32
      accountId:
        name: accountId
        in: path
        description: The Account ID, which can be obtained from either the Account Login response body or the 'Location' response header when the Account was created.
        required: true
        schema:
          type: string
          format: ID
          example: a1acf868a9d34bcc95ded15498e5a507
      unlinkAccountId:
        name: accountId
        in: path
        description: "The Account ID to be unlinked. Note the following rules that apply to Platform and Device Account unlinking with this endpoint:\n\n\n
          - If the Account of the current user is a Platform Account, the Account ID to be unlinked can be either a Full or Device Account.\n\n
          - If the Account of the current user is a Device Account, the Account ID to be unlinked must be a Platform Account as long as it is not already linked to a Full Account."
        required: true
        schema:
          type: string
          format: ID
          example: 09df4740e88947998a0bd307c9ff80ac
      userCode:
        name: 'userCode'
        in: 'query'
        description: The verification code to be used in the query.
        required: true
        schema:
            type: string
      filterBy:
        name: filterBy
        in: query
        description: The linked Account type to be used in the query.
        required: false
        schema:
          type: string
          enum: [device, full, platform]
      psnRegion:
        name: X-2k-Psn-Region
        in: header
        description: The PSN Region is required if the Platform Account of the current user logging in is PSN.
        required: false
        schema:
          type: string
          format: scea|scee|scej
      documentId:
        name: documentId
        in: path
        description: The Legal Document ID, which can be obtained from the 'Location' response header when the Legal Document ID is created.
        required: true
        schema:
          type: string
          format: ID
          example: d0fee63990394b38adcf2dd9dc7073a2
      documentLocale:
        name: locale
        in: query
        required: false
        description: The locale of the Legal Document to be displayed. If a locale is not specified, the 'en-US' version of the manifest will be displayed by default.
        schema:
          type: string
          format: xx-XX
          example: 'en-US'
      ifModifiedSince:
        name: If-Modified-Since
        in: header
        description: Conditional request based on the last modification.
        required: false
        schema:
          type: string
          format: date-time
 
  responses:
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '409':
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '415':
      description: Unsupported Media Type
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '429':
      description: Too Many Requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
