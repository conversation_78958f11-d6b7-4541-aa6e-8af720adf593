package api

import (
	"net/http"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	. "github.com/onsi/gomega"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	gomock "go.uber.org/mock/gomock"
)

func TestGetDiscovery(t *testing.T) {
	g := goblin.Goblin(t)
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	g.Describe("GetDiscovery", func() {
		appID := "4029a6ffe9924f969955aa2e1c0782aa"

		g.It("with correct productid return default", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			w, r := AddBodyToRequest("", User1JWT)

			mock.api.GetDiscovery(w, r, apipub.GetDiscoveryParams{DiscoveryPid: aws.String(appID)})

			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`[{"canList":null,"description":"serverDefault","id":"serverDefault","urls":[{"fragment":null,"host":null,"path":null,"port":null,"query":null,"scheme":null,"type":"http","url":"http://localhost:8000"},{"fragment":null,"host":null,"path":null,"port":null,"query":null,"scheme":null,"type":"mqtt","url":"ws://localhost:8080/mqtt"}]}]`))
		})

		g.It("with bad appid should return default", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()
			w, r := AddBodyToRequest("", "foobar")

			mock.api.GetDiscovery(w, r, apipub.GetDiscoveryParams{DiscoveryPid: nil})

			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`[{"canList":null,"description":"serverDefault","id":"serverDefault","urls":[{"fragment":null,"host":null,"path":null,"port":null,"query":null,"scheme":null,"type":"http","url":"http://localhost:8000"},{"fragment":null,"host":null,"path":null,"port":null,"query":null,"scheme":null,"type":"mqtt","url":"ws://localhost:8080/mqtt"}]}]`))
		})

		g.It("with bad dynamodb query should return default", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()
			w, r := AddBodyToRequest("", User1JWT)
			query := r.URL.Query()
			query.Set("id", "foobar")
			r.URL.RawQuery = query.Encode()

			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusNotFound, errs.EDynamodbGeneric))
			mock.api.GetDiscovery(w, r, apipub.GetDiscoveryParams{DiscoveryPid: aws.String(appID), Discoveryid: aws.String("foobar")})
			Expect(w.Code).Should(Equal(http.StatusNotFound))
		})

		g.It("with appid and param id, return result", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			ret := []apipub.DiscoveryResponse{
				{
					Description: "foobar",
					Id:          "foobar",
					Urls: []apipub.DiscoveryURLResponse{
						{
							Type: "http",
							Url:  "http://foobar:8000",
						},
						{
							Type: "mqtt",
							Url:  "ws://foobar:8080/mqtt",
						},
					},
				},
			}

			w, r := AddBodyToRequest("", User1JWT)
			query := r.URL.Query()
			query.Set("id", "foobar")
			r.URL.RawQuery = query.Encode()

			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&ret, nil)

			mock.api.GetDiscovery(w, r, apipub.GetDiscoveryParams{DiscoveryPid: aws.String(appID), Discoveryid: aws.String("foobar")})

			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`[{"canList":null,"description":"foobar","id":"foobar","urls":[{"fragment":null,"host":null,"path":null,"port":null,"query":null,"scheme":null,"type":"http","url":"http://foobar:8000"},{"fragment":null,"host":null,"path":null,"port":null,"query":null,"scheme":null,"type":"mqtt","url":"ws://foobar:8080/mqtt"}]}]`))
		})
	})
}
