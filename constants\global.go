// Package constants is for GLOBAL constants only.  ONLY USE THIS FILE if the constant is used in multiple packages.
// Some leeway given if one or more of the set is used in multiple packages even if one or more of them are only used in one package.
package constants

const (
	//Auth constants
	BearerAuthJWT    = "bearerAuth.JWT"
	BearerAuthString = "bearerAuth.String"

	//Context constants
	T2GPCtxTenant      = "t2gp.tenant"
	T2GPCtxProductId   = "t2gp.pid"
	T2GPCtxTrustedId   = "t2gp.tid"
	T2GPCtxTrustedHash = "t2gp.thash"

	//Header constants
	KContentType            = "Content-Type"
	KApplicationJson        = "application/json"
	KApplicationOctetStream = "application/octet-stream"
	KT2GPLabel              = "X-T2GP-Label"

	//Trusted Server Permissions
	PermissionBans      = "bans"
	PermissionDiscovery = "discovery"
	PermissionGroups    = "groups"

	TrustedServer = "T2GP.TrustedServer"

	//DD service env var for trusted server
	DDServiceTrusted = "social-trusted-api"
)
