package store

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

func (ds *DataStore) GetTotalFriendsCount(ctx context.Context, status string) (*int32, error) {
	query := dynamodb.ScanInput{
		TableName: aws.String(ds.cfg.ProfileTable),
		Select: "COUNT",
	}

	if status == "" {
		query.FilterExpression = aws.String("begins_with(#sk, :sk)")
		query.ExpressionAttributeValues = map[string]types.AttributeValue{
			":sk": &types.AttributeValueMemberS{Value: "friend#"},
		}
		query.ExpressionAttributeNames = map[string]string{"#sk": "sk"}
	} else {
		query.FilterExpression = aws.String("begins_with(#sk, :sk) AND #status = :s")
		query.ExpressionAttributeValues = map[string]types.AttributeValue{
			":sk": &types.AttributeValueMemberS{Value: "friend#"},
			":s": &types.AttributeValueMemberS{Value: status},
		}
		query.ExpressionAttributeNames = map[string]string{
			"#sk": "sk",
			"#status": "status",
		}
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.Scan", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("GetTotalFriendsCount"))
	setPkSkFromScanExp(&span, query)
	result, err := ds.ddb.Scan(ctx, &query)
	span.Finish()
	if err != nil {
		logger.FromContext(ctx).Error().Err(err).Msgf("GetTotalFriendsCount Scan()")
		return nil, err
	}
	return &result.Count, nil
}

func (ds *DataStore) GetTotalUserCount(ctx context.Context) (*int32, error) {
	query := dynamodb.ScanInput{
		TableName: aws.String(ds.cfg.ProfileTable),
		Select: "COUNT",
		FilterExpression: aws.String("begins_with(#sk, :sk)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":sk": &types.AttributeValueMemberS{Value: "user#"},
		},
		ExpressionAttributeNames: map[string]string{"#sk": "sk"},
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.Scan", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("GetTotalUserCount"))
	setPkSkFromScanExp(&span, query)
	result, err := ds.ddb.Scan(ctx, &query)
	span.Finish()
	if err != nil {
		logger.FromContext(ctx).Error().Err(err).Msgf("GetTotalUserCount Scan()")
		return nil, err
	}
	return &result.Count, nil
}
