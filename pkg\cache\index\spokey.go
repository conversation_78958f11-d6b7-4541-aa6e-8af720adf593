package index

/// Generate idx keys for SPO

import (
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

type userSubject struct {
	tenant    string
	productid string
	userid    string
}

type groupSubject struct {
	tenant    string
	productid string
	groupid   string
}

type userRelationSubject struct {
	tenant string
	userid string
}

func NewUserSubject(tenant, productid string, userid string) *userSubject {
	sub := &userSubject{
		tenant:    tenant,
		productid: productid,
		userid:    userid,
	}
	if !sub.validate() {
		return nil
	}
	return sub
}

func NewGroupSubject(tenant, productid string, groupid string) *groupSubject {
	sub := &groupSubject{
		tenant:    tenant,
		productid: productid,
		groupid:   groupid,
	}
	if !sub.validate() {
		return nil
	}
	return sub
}

func NewUserRelationSubject(tenant, userid string) *userRelationSubject {
	sub := &userRelationSubject{
		tenant: tenant,
		userid: userid,
	}
	if !sub.validate() {
		return nil
	}
	return sub
}

// InvitedKey - S invited O (UserProfile)
func (sub userSubject) InvitedKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:invited", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// InvitedByKey - S invited by O (UserProfile)
func (sub userSubject) InvitedByKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:invitedBy", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// InvitedToKey - S invited to O (Group)
func (sub userSubject) InvitedToKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:invitedTo", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// InvitedToByKey - S invited, by user, to O (Group)
func (sub userSubject) InvitedByToKey(byUser string, isFirstParty *bool) *string {
	if !sub.validate() {
		return nil
	}
	firstParty := ""
	if isFirstParty != nil && *isFirstParty {
		firstParty = ":firstParty"
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:by:%s:invitedTo%s", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid, byUser, firstParty))
}

// Invites S, to group, has O (MembershipRequest)
func (sub userSubject) InvitesKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:invites", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// GroupInvitesKey S has O (MembershipRequest)
func (sub groupSubject) GroupInvitesKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:group:{%s}:invites", sub.tenant, sub.productid, utils.GetEnvironment(), sub.groupid))
}

// JoinRequestedOfKey - S join requested O (UserProfile)
func (sub userSubject) JoinRequestedOfKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:joinRequested", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// JoinRequestedByKey - S join requested by O (UserProfile)
func (sub userSubject) JoinRequestedByKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:joinRequestedBy", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// RequestedToJoinKey - S join requested to O (Group)
func (sub userSubject) RequestedToJoinKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:requestedToJoin", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// RequestedViaToJoinKey -  S join requested, Via user, to O (Group)
func (sub userSubject) RequestedViaToJoinKey(viaUser string) *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:via:%s:requestedToJoin", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid, viaUser))
}

// GroupJoinRequestsKey - S has O (MembershipRequests)
func (sub groupSubject) GroupJoinRequestsKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:group:{%s}:joinRequests", sub.tenant, sub.productid, utils.GetEnvironment(), sub.groupid))
}

// MemberOfKey - S in O (Group)
func (sub userSubject) MemberOfKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:memberOf", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// GroupMembersKey - S has O (UserProfile)
func (sub groupSubject) GroupMembersKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:group:{%s}:members", sub.tenant, sub.productid, utils.GetEnvironment(), sub.groupid))
}

// FriendsKey - S with O (Friend)
func (sub userRelationSubject) FriendsListKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:user:{%s}:friends", sub.tenant, sub.userid))
}

// PresenceByPriorityKey - S sets O (UserProfile)
func (sub userSubject) PresencesSetForKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:presences", sub.tenant, sub.productid, strings.TrimSuffix(utils.GetEnvironment(), "-v2"), sub.userid))
}

// FriendsInGameKey - for S, friends in O (UserProfile)
func (sub userRelationSubject) FriendsInGameKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:user:{%s}:friends:prod", sub.tenant, sub.userid))
}

// FriendsStatusKey - for S, friend with O (UserProfile)
func (sub userRelationSubject) FriendsStatusKey(status string) *string {
	if status != "friend" && status != "pending" {
		return nil
	}
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:user:{%s}:friends:%s", sub.tenant, sub.userid, status))
}

// BlockedKey - S blocked O (UserProfile)
func (sub userRelationSubject) BlockedKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:user:{%s}:blocked", sub.tenant, sub.userid))
}

// BlockedByKey - S blocked by O (UserProfile)
func (sub userRelationSubject) BlockedByKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:user:{%s}:blockedBy", sub.tenant, sub.userid))
}

// PlayedWithKey - S played, in prod, with O (UserProfile)
func (sub userSubject) PlayedWithKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:user:{%s}:played", sub.tenant, sub.userid))
}

// EndorsementsList - S has been endorsed for O
func (sub userSubject) EndorsementListKey() *string {
	if !sub.validate() {
		return nil
	}
	return aws.String(fmt.Sprintf("%s:prod:%s:%s:user:{%s}:endorsedFor", sub.tenant, sub.productid, utils.GetEnvironment(), sub.userid))
}

// validate - validate helper
func (sub userSubject) validate() bool {

	if sub.userid == "" || (sub.tenant == "dna" && validation.ValidateIdOrFirstPartyId(sub.userid) != nil) {
		return false
	}
	if sub.productid == "" {
		return false
	}
	if sub.tenant == "" || sub.tenant == "unk" {
		return false
	}
	return true
}

// validate - validate helper
func (sub *groupSubject) validate() bool {
	if sub.groupid == "" {
		return false
	}
	if sub.productid == "" {
		return false
	}
	if sub.tenant == "" || sub.tenant == "unk" {
		return false
	}
	return true
}

// validateGroupSub - validate helper
func (sub *userRelationSubject) validate() bool {
	if sub.userid == "" || (sub.tenant == "dna" && validation.ValidateIdOrFirstPartyId(sub.userid) != nil) {
		return false
	}
	if sub.tenant == "" || sub.tenant == "unk" {
		return false
	}
	return true
}
