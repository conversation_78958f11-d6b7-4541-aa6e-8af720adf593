locals {
  envvars = {
    develop = {
      cluster_name                           = "t2gp-non-production"
      dynamo_billing_mode                    = "PAY_PER_REQUEST"
      redis_node_type                        = "cache.t3.medium"
      redis_repgrp_num                       = 1
      redis_repgrp_replica_count             = 0
      redis_repgrp_param_group_name          = "t2gp-social-redis-parameters"
      t2gp_backend_infra_branch_pointer      = "develop"
      redis_repgrp_changes_apply_immediately = true
      create_clustered_redis                 = false
    }
    integration = {
      cluster_name                           = "t2gp-non-production"
      dynamo_billing_mode                    = "PAY_PER_REQUEST"
      redis_node_type                        = "cache.t3.medium"
      redis_repgrp_num                       = 1
      redis_repgrp_replica_count             = 0
      redis_repgrp_param_group_name          = "t2gp-social-redis-parameters"
      t2gp_backend_infra_branch_pointer      = "develop"
      redis_repgrp_changes_apply_immediately = true
      create_clustered_redis                 = false
    }
    staging = {
      cluster_name                           = "t2gp-production"
      dynamo_billing_mode                    = "PAY_PER_REQUEST"
      redis_node_type                        = "cache.t3.medium"
      redis_repgrp_num                       = 1
      redis_repgrp_replica_count             = 0
      redis_repgrp_param_group_name          = "t2gp-social-redis-parameters"
      t2gp_backend_infra_branch_pointer      = "production"
      redis_repgrp_changes_apply_immediately = false
      create_clustered_redis                 = false
    }
    cert = {
      cluster_name                           = "t2gp-production"
      dynamo_billing_mode                    = "PAY_PER_REQUEST"
      redis_node_type                        = "cache.t3.medium"
      redis_repgrp_num                       = 1
      redis_repgrp_replica_count             = 0
      redis_repgrp_param_group_name          = "t2gp-social-redis-parameters"
      t2gp_backend_infra_branch_pointer      = "production"
      redis_repgrp_changes_apply_immediately = false
      create_clustered_redis                 = false
    }
    production = {
      cluster_name                           = "t2gp-production"
      dynamo_billing_mode                    = "PAY_PER_REQUEST"
      redis_node_type                        = "cache.t3.medium"
      redis_repgrp_num                       = 1
      redis_repgrp_replica_count             = 0
      redis_repgrp_param_group_name          = "t2gp-social-redis-parameters"
      t2gp_backend_infra_branch_pointer      = "production"
      redis_repgrp_changes_apply_immediately = false
      create_clustered_redis                 = false
    }
    loadtesting = {
      cluster_name                           = "t2gp-testing"
      dynamo_billing_mode                    = "PAY_PER_REQUEST"
      redis_node_type                        = "cache.r6g.large"
      redis_repgrp_num                       = 1
      redis_repgrp_replica_count             = 0
      redis_repgrp_param_group_name          = "t2gp-social-redis-parameters"
      t2gp_backend_infra_branch_pointer      = "testing"
      redis_repgrp_changes_apply_immediately = true
      create_clustered_redis                 = false
    }
  }
}