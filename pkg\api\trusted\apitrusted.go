// Package api implementation for interfaces from oapi-codegen
package api

import (
	"context"
	"net/http"
	"net/url"
	"regexp"

	"github.com/lestrrat-go/jwx/jwk"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// SocialTrustedAPI returns social trusted api struct
type SocialTrustedAPI struct {
	SocialApi *api.SocialPublicAPI
	cfg       *config.Config
	jwk       jwk.Set
}

var _ apitrusted.ServerInterface = &SocialTrustedAPI{}

func fetchJWKS(url string) (jwk.Set, error) {
	set, err := jwk.Fetch(context.Background(), url)
	if err != nil {
		return nil, err
	}

	return set, nil
}

// NewSocialTrustedAPI create social api object
func NewSocialTrustedAPI(api *api.SocialPublicAPI, ctx context.Context, cfg *config.Config) *SocialTrustedAPI {
	log := logger.FromContext(ctx)
	jwksPath := cfg.SsoURL + "/jwks.json"
	jwk, err := fetchJWKS(jwksPath)
	if err != nil {
		log.Err(err).Msg("failed to get JWKS from SSO")
		return nil
	}

	tsapi := &SocialTrustedAPI{
		SocialApi: api,
		cfg:       cfg,
		jwk:       jwk,
	}
	return tsapi
}

// ServerGetHealth get the server health
func (tApi *SocialTrustedAPI) ServerGetHealth(w http.ResponseWriter, r *http.Request, params apitrusted.ServerGetHealthParams) {
	publicParams, err := utils.TypeConverter[apipub.GetHealthParams](params)
	if err == nil {
		tApi.SocialApi.GetHealth(w, r, *publicParams)
	} else {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EInvalidTenant))
	}
}

// ServerGetVersion get the version of the server
func (tApi *SocialTrustedAPI) ServerGetVersion(w http.ResponseWriter, r *http.Request) {
	tApi.SocialApi.GetVersion(w, r)
}

func ValidateUrls(ctx context.Context, urls []apitrusted.DiscoveryURLResponse) bool {
	log := logger.FromContext(ctx)
	httpRegex := regexp.MustCompile("^https?://.*/v[0-9]+$")
	doubleV1Regex := regexp.MustCompile("/v[0-9]+/v[0-9]+$")
	mqttRegex := regexp.MustCompile("^wss://.*/mqtt")

	for _, discUrl := range urls {
		bRet := false

		_, err := url.ParseRequestURI(discUrl.Url)
		if err != nil {
			log.Error().Err(err).Str("url", discUrl.Url).Interface("discUrl", discUrl).Msg("Error parsing discovery url")
			return false
		}

		switch discUrl.Type {
		case "http":
			fallthrough
		case "trusted":
			bRet = httpRegex.MatchString(discUrl.Url)
			//don't end in double /v1/v1
			bDoubleV1 := doubleV1Regex.MatchString(discUrl.Url)
			if bDoubleV1 {
				bRet = false
			}
		case "mqtt":
			bRet = mqttRegex.MatchString(discUrl.Url)
		default:
			bRet = true
		}

		if !bRet {
			log.Info().Str("url", discUrl.Url).Msg("Invalid discovery url")
			return bRet
		}
	}
	return true
}
