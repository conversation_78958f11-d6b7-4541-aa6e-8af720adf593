package utils

import (
	"context"
	"errors"
	"net/http"
	"sync/atomic"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/aws/retry"
	"github.com/rs/zerolog/log"

	v2Config "github.com/aws/aws-sdk-go-v2/config"
)

// This file is from this blog post
// https://www.rokt.com/engineering-blog/improving-app-latency-eks/

// CredentialGenerator is a helper type to abstract fetching a **fresh** AWS credential
type CredentialGenerator func() (*aws.Credentials, error)

// CredentialProvider is a helper interface to allow refreshing credentials
type CredentialProvider interface {
	RefreshCredential(t *time.Ticker, stopCh <-chan interface{})
	GetCredential() (*aws.Credentials, error)
}

// ReplaceAwsClientV4Signer replaces the default v4 signer with a new v4 signer
// that reads credentials from an instance of CredentialProvider
// func ReplaceAwsClientV4Signer(credProvider CredentialProvider, awsClientIn *session.Session) *session.Session {
// 	awsClient := awsClientIn.Copy()
// 	signerOpt := func(signer *v4.Signer) {
// 		// only replace cred if it can be acquired successfully
// 		cred, err := credProvider.GetCredential()
// 		if err != nil {
// 			log.Error().Err(err).Msgf("failed to read credential from provider. falling back to provider from chain")
// 			return
// 		}
// 		// replace credential with the one we provide
// 		signer.Credentials = cred
// 	}
// 	v4SignerName := v4.SignRequestHandler.Name

// 	// create a new signer
// 	patchedSignHandler := v4.BuildNamedHandler(v4SignerName, signerOpt)

// 	// replace the existing v4 signer in the SDK client
// 	awsClient.Handlers.Sign.Swap(v4SignerName, patchedSignHandler)

// 	return awsClient
// }

type CachedAwsCredProvider struct {
	// caches the cred
	v atomic.Value

	// used to acquire a *fresh* cred
	credGenFn CredentialGenerator
}

func NewCachedAwsCredProvider(credGenFn CredentialGenerator) *CachedAwsCredProvider {
	return &CachedAwsCredProvider{
		v:         atomic.Value{},
		credGenFn: credGenFn,
	}
}

func (c *CachedAwsCredProvider) fetchAndCacheCred() (*aws.Credentials, error) {
	cred, err := c.credGenFn()
	if err != nil {
		return nil, err
	}

	c.v.Store(cred)
	return cred, nil
}

func (c *CachedAwsCredProvider) RefreshCredential(t *time.Ticker, stopCh <-chan interface{}) {
	for {
		select {
		case <-stopCh:
			return
		case <-t.C:
			if _, err := c.fetchAndCacheCred(); err != nil {
				log.Error().Err(err).Msgf("failed to refresh credential")
			}
		}
	}
}

func (c *CachedAwsCredProvider) GetCredential() (*aws.Credentials, error) {
	v := c.v.Load()

	// v is not initialized.
	// fall back to fetch cred in sync
	if v == nil {
		return c.fetchAndCacheCred()
	}

	vCasted, ok := v.(*aws.Credentials)
	if !ok {
		return nil, errors.New("should not happen. failed to cast cred")
	}
	return vCasted, nil
}

// CredentialGeneratorWithDefaultChain uses aws default credential chain to create credentials.
// STS regional endpoint is used to improve latency
//
// NOTE: a fresh session must be created each time
// to avoid its cache for *credentials.Credentials
// var CredentialGeneratorWithDefaultChain = func() (*credentials.Credentials, error) {
// 	s, err := session.NewSessionWithOptions(session.Options{
// 		Config: aws.Config{
// 			STSRegionalEndpoint: endpoints.RegionalSTSEndpoint,
// 		},
// 		SharedConfigState: session.SharedConfigEnable,
// 	})
// 	if err != nil {
// 		return nil, fmt.Errorf("failed to new aws session")
// 	}
// 	return s.Config.Credentials, nil
// }

// func GetCredentialProvider() CredentialProvider {
// 	credProvider := NewCachedAwsCredProvider(CredentialGeneratorWithDefaultChain)

// 	// force getting cred on initialization
// 	_, err := credProvider.GetCredential()
// 	if err != nil && !IsLocal() {
// 		panic(err)
// 	}

// 	// refresh cred async
// 	// hard coded to fresh credentials every 10 min/non-stop
// 	go credProvider.RefreshCredential(time.NewTicker(10*time.Minute), make(chan interface{}))

// 	return credProvider
// }

func GetV2CredentialProvider(ctx context.Context) aws.CredentialsProvider {
	credentialProvider := aws.NewCredentialsCache(aws.CredentialsProviderFunc(
		func(ctx context.Context) (aws.Credentials, error) {
			cfg, err := v2Config.LoadDefaultConfig(ctx)
			if err != nil {
					return aws.Credentials{}, err
			}
			return cfg.Credentials.Retrieve(ctx)
		},
	))
 
	_, err := credentialProvider.Retrieve(ctx)
	if err != nil {
			if !IsLocal() {
					panic(err)
			}
	}

	ctx, cancel := context.WithCancel(ctx)
	go func() {
			defer cancel()
			ticker := time.NewTicker(10 * time.Minute)

			for {
					select {
					case <-ticker.C:
							// Trigger credential refresh
							_, err := credentialProvider.Retrieve(ctx)
							if err != nil {
									log.Info().Msgf("Failed to refresh credentials: %v", err)
							}
					case <-ctx.Done():
							return
					}
			}
	}()

	return credentialProvider
}

type CustomRetryableError struct{}

func (CustomRetryableError) IsErrorRetryable(err error) aws.Ternary {
	if requestErr, ok := err.(interface{ HTTPStatusCode() int }); ok {
		statusCode := requestErr.HTTPStatusCode()
		if statusCode == http.StatusTooManyRequests || statusCode == http.StatusServiceUnavailable {
			return aws.TrueTernary
		}
	}
	return aws.FalseTernary
}

func GetDefaultRetryStandard(attempt int) *retry.Standard {
	return retry.NewStandard(func(o *retry.StandardOptions) {
		o.MaxAttempts = attempt
		o.Backoff = retry.NewExponentialJitterBackoff(2 * time.Second /* base backoff time */)
		o.Retryables = []retry.IsErrorRetryable{
			CustomRetryableError{},
		}
	})
}
