package api

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// GetBlocklist get blocklist for a given user
func (api *SocialPublicAPI) GetBlocklist(w http.ResponseWriter, r *http.Request, params apipub.GetBlocklistParams) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	productid := token.Claims.ProductID

	limit := api.Cfg.MaxBlocks
	if params.Limit != nil && *params.Limit > 0 && *params.Limit <= api.Cfg.MaxBlocks {
		limit = *params.Limit
	}

	blocklist, next, err := api.GetBlockListInternal(r.Context(), userid, productid, ost, limit, params.Next)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EBlocklistFetchFailed))
		return
	}

	response := apipub.BlocklistsNext{
		Items: []apipub.BlocklistResponse{},
	}

	if blocklist != nil {
		var blocks []string
		for _, blocked := range *blocklist {
			blocks = append(blocks, blocked.Blockedid)
		}

		baseBlockObj := &apipub.BlocklistResponse{
			Userid:            userid,
			OnlineServiceType: ost,
			Productid:         productid,
		}

		blWithUserInfo := api.createBlocklist(r.Context(), blocks, baseBlockObj)

		for _, blocked := range blWithUserInfo {
			blocks = append(blocks, blocked.Blockedid)

			blockedProfile, _ := api.getUserProfile(r.Context(), blocked.Blockedid, true)
			//links filtering needs to be done all the time regardless of name existing or not.
			if blockedProfile != nil {
				if blockedProfile.Links != nil {
					blocked.Links = blockedProfile.Links
					identity.FilterLinksByOST(blocked.Links, ost)
				}
				if blockedProfile.DisplayName != nil {
					blocked.Name = blockedProfile.DisplayName
				}
			}
			response.Items = append(response.Items, *blocked)
		}

		if next != "" {
			nextPieces := strings.Split(next, ":")
			if len(nextPieces) == 5 && nextPieces[3] == "blocks" {
				next = nextPieces[4]
			}
			response.Nextid = &next
		}
	}

	ReturnOK(w, r, response)
}

// DelBlocklist clear the blocklist for a given user
func (api *SocialPublicAPI) DelBlocklist(w http.ResponseWriter, r *http.Request) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	productid := token.Claims.ProductID

	//get blocklist
	blocklist, _, err := api.GetBlockListInternal(r.Context(), userid, productid, ost, api.Cfg.MaxBlocks, nil)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EBlocklistFetchFailed))
		return
	}
	if blocklist == nil {
		ReturnEmptyOK(w, r)
		return
	}

	api.saveBlocklistThroughCache(r.Context(), productid, userid, nil, blocklist, time.Duration(api.Cfg.TtlBlocklist)*time.Second)

	ReturnEmptyOK(w, r)
}

// GetBlockListInternal helper function to get blocklist from redis and dynamo
func (api *SocialPublicAPI) GetBlockListInternal(ctx context.Context, userid, productid string, ost apipub.OnlineServiceType, limit int, next *string) (*[]*apipub.BlocklistResponse, string, error) {
	log := logger.FromContext(ctx)
	limit64 := int64(limit)
	blocklist, it, err := api.Cache.GetUserBlocklist(ctx, userid, &limit64, next)
	if err != nil {
		log.Error().Err(err).Msg("failed to get block list from Cache")
	}
	if blocklist == nil || (err != nil && errs.IsEqual(err, errs.ERedisObjectMissing)) {
		var nextIt *string
		blocklist, nextIt, err = api.Ds.GetBlocklistWithLimit(ctx, userid, productid, ost, limit, next)
		if err != nil {
			log.Error().Err(err).Msg("failed to query blocklist from persistence")
			return nil, "", err
		}
		if blocklist == nil || len(*blocklist) == 0 {
			log.Info().Str("userid", userid).Msg("no blocked user found")

			//update userCacheMetadata
			userMeta, metaErr := api.Cache.GetUserCacheMeta(ctx, userid)
			if metaErr != nil {
				log.Error().Err(metaErr).Str("userid", userid).Msg("get userMeta err")
			}
			if userMeta == nil {
				userMeta = &apipub.UserCacheMeta{
					Friends: true,
					Pending: true,
					Blocks:  true,
				}
			}
			//only save if change
			if userMeta.Blocks {
				userMeta.Blocks = false
				metaErr = api.Cache.SetUserCacheMeta(ctx, userid, userMeta, time.Duration(api.Cfg.TtlProfile)*time.Second)
				if metaErr != nil {
					log.Error().Err(metaErr).Str("userid", userid).Msg("save userMeta err")
				}
			}

			return nil, "", nil
		}
		if nextIt != nil {
			it = *nextIt
		}
		syncFullBlockList(api, ctx, userid, productid, ost, limit, next, blocklist)
	}
	return blocklist, it, err
}

func syncFullBlockList(api *SocialPublicAPI, ctx context.Context, userid, productid string, ost apipub.OnlineServiceType, limit int, next *string, blocklist *[]*apipub.BlocklistResponse) {
	log := logger.FromContext(ctx)

	// We've got a full list, so just Cache it
	if limit == api.Cfg.MaxBlocks && next == nil && blocklist != nil {
		api.setBlockListCache(ctx, userid, blocklist)
		return
	}

	blocklist, _, err := api.Ds.GetBlocklistWithLimit(ctx, userid, productid, ost, api.Cfg.MaxBlocks, nil)
	if err != nil {
		log.Error().Err(err).Str("userid", userid).Msg("failed to query blocklist")
		return
	}
	if blocklist != nil {
		err = api.setBlockListCache(ctx, userid, blocklist)
	}
	if err != nil {
		log.Error().Err(err).Str("userid", userid).Msg("failed to set Cache blocklist")
	}
}

func buildFirstPartyBlocklistSearchRequest(ctx context.Context, searchRequestType apipub.SearchAccountRequestType, queries []string, ost apipub.OnlineServiceType) (*apipub.SearchAccountRequest, *errs.Error) {
	log := logger.FromContext(ctx)

	if queries == nil {
		return nil, errs.New(http.StatusInternalServerError, errs.EBlocklistGeneric)
	}

	var criteria []apipub.SearchAccountCriteria
	var searchRequest *apipub.SearchAccountRequest

	switch searchRequestType {
	case apipub.AccountsByFirstPartyAlias:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				FirstPartyAlias:   aws.String(q),
				OnlineServiceType: aws.Int(int(ost)),
			})
		}
	case apipub.AccountsByFirstPartyId:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				FirstPartyId:      aws.String(q),
				OnlineServiceType: aws.Int(int(ost)),
			})
		}
	case apipub.AccountsById:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				AccountId: aws.String(q),
			})
		}
	case apipub.FullAccountByDisplayName:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				DisplayName: aws.String(q),
			})
		}
	default:
		log.Error().Msgf("Unsupported search type %s", searchRequestType)
		return nil, errs.New(http.StatusBadRequest, errs.EFriendsInvalidSearchQuery)
	}

	searchRequest = &apipub.SearchAccountRequest{
		Type:      &searchRequestType,
		Criterias: &criteria,
	}
	return searchRequest, nil
}

func (api *SocialPublicAPI) AddBlocklist(w http.ResponseWriter, r *http.Request, pUserid apipub.PUserid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	if !validation.IsDnaId(r.Context(), pUserid) {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistInvalidUserID))
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer

	blockedid := pUserid

	if blockedid == userid || blockedid == "" {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistInvalidUserID))
		return
	}

	doesExceed, err := api.Ds.DoesExceedMaxBlockCount(r.Context(), userid, 1)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
		return
	}
	if doesExceed {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistExceedMaxUserBlockedCount))
		return
	}

	var addBlocklistRequest apipub.AddBlocklistRequestBody
	if !DecodeBody(w, r, &addBlocklistRequest) {
		return
	}

	baseBlockObj := &apipub.BlocklistResponse{
		Userid:            userid,
		OnlineServiceType: ost,
		Productid:         productid,
	}

	blockedUser := api.createBlocklist(r.Context(), []string{blockedid}, baseBlockObj)
	//telemetry for each add

	additionalInfo := make(map[string]string)
	if addBlocklistRequest.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*addBlocklistRequest.TeleMeta, &additionalInfo)
	}
	api.Tele.SendBlocklistEvent(r.Context(), telemetry.BuildBlocklistTeleMeta(telemetry.KBlocklistAdd, productid, userid, ost, []string{blockedid}, &appid, &additionalInfo))

	api.saveBlocklistThroughCache(r.Context(), productid, userid, &blockedUser, nil, time.Duration(api.Cfg.TtlBlocklist)*time.Second)

	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) RemoveBlocklist(w http.ResponseWriter, r *http.Request, pUserid apipub.PUserid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	if !validation.IsDnaId(r.Context(), pUserid) {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistInvalidUserID))
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	blockedid := pUserid

	baseBlockObj := &apipub.BlocklistResponse{
		Userid:            userid,
		OnlineServiceType: ost,
		Productid:         productid,
	}
	blockedUser := api.createBlocklist(r.Context(), []string{blockedid}, baseBlockObj)

	api.saveBlocklistThroughCache(r.Context(), productid, userid, nil, &blockedUser, time.Duration(api.Cfg.TtlBlocklist)*time.Second)

	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) ImportBlocklist(w http.ResponseWriter, r *http.Request) {
	log := logger.Get(r)
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer

	var importBlocklistRequest apipub.ImportBlocklistRequestBody
	if !DecodeBody(w, r, &importBlocklistRequest) {
		return
	}

	var blockedUsers []*apipub.BlocklistResponse
	var searchResponse *[]apipub.SearchAccountResponse
	if importBlocklistRequest.Userids != nil {

		count := len(importBlocklistRequest.Userids)

		if count == 1 {
			blockedid := (importBlocklistRequest.Userids)[0]
			if blockedid == userid || blockedid == "" {
				errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistInvalidUserID))
				return
			}
		}

		doesExceed, err := api.Ds.DoesExceedMaxBlockCount(r.Context(), userid, len(importBlocklistRequest.Userids))
		if err != nil {
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
			return
		}

		if len(importBlocklistRequest.Userids) > 20 {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistExcessiveIdCount))
			return
		}

		if doesExceed {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EBlocklistExceedMaxUserBlockedCount))
			return
		}

		if importBlocklistRequest.IsFirstParty != nil && *importBlocklistRequest.IsFirstParty {
			searchRequest, searchRequestErr := buildFirstPartyBlocklistSearchRequest(r.Context(), apipub.AccountsByFirstPartyId, importBlocklistRequest.Userids, ost)
			if searchRequestErr != nil {
				errs.Return(w, r, searchRequestErr)
				return
			}

			searchResponse, err = api.Id.SearchAccounts(r.Context(), searchRequest)
			if err != nil {
				log.Error().Err(err).Msgf("failed to search accounts dna")
				errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDnaSearchAccountsFailed))
				return
			}

			if searchResponse != nil {
				importBlocklistRequest.Userids = nil
				for _, i := range *searchResponse {
					if i.ParentAccountId != nil {
						importBlocklistRequest.Userids = append(importBlocklistRequest.Userids, *(i.ParentAccountId))
					}
				}
			}
		}

		baseBlockObj := &apipub.BlocklistResponse{
			Userid:            userid,
			OnlineServiceType: ost,
			Productid:         productid,
		}

		blockedUsers = api.createBlocklist(r.Context(), importBlocklistRequest.Userids, baseBlockObj)
		//telemetry for each add
		for i := 0; i < len(blockedUsers); i++ {
			additionalInfo := make(map[string]string)
			if importBlocklistRequest.TeleMeta != nil {
				utils.ConvertMapInterfaceToMapString(*importBlocklistRequest.TeleMeta, &additionalInfo)
			}
			api.Tele.SendBlocklistEvent(r.Context(), telemetry.BuildBlocklistTeleMeta(telemetry.KBlocklistAdd, productid, userid, ost, importBlocklistRequest.Userids, &appid, &additionalInfo))
		}
	}

	api.saveBlocklistThroughCache(r.Context(), productid, userid, &blockedUsers, nil, time.Duration(api.Cfg.TtlBlocklist)*time.Second)
	var newBlockedUsersRet []string
	for _, user := range blockedUsers {
		//if we want to return first party Id:
		if importBlocklistRequest.IsFirstParty != nil && *importBlocklistRequest.IsFirstParty {
			if searchResponse != nil {
				for _, i := range *searchResponse {
					if i.ParentAccountId != nil && *i.ParentAccountId == user.Blockedid {
						if i.FirstPartyId != nil {
							newBlockedUsersRet = append(newBlockedUsersRet, *i.FirstPartyId)
						} else {
							newBlockedUsersRet = append(newBlockedUsersRet, *i.ParentAccountId)
						}
					}
				}
			}
		} else { //we want to return the full Id:
			newBlockedUsersRet = append(newBlockedUsersRet, user.Blockedid)
			//return service type as T2GP if not first party
			ost = 24
		}
	}
	response := apipub.ImportBlocklistResponse{
		OnlineServiceType: ost,
		Blockedids:        newBlockedUsersRet,
	}

	ReturnOK(w, r, response)
}

func (api *SocialPublicAPI) saveBlocklistThroughCache(ctx context.Context, productid string, userId string, add *[]*apipub.BlocklistResponse, remove *[]*apipub.BlocklistResponse, ttl time.Duration) {
	//just log errors since it's all multi part shit.  we don't want to return midway.
	log := logger.FromContext(ctx)

	tenant := identity.GetTenantFromCtx(ctx, api.Id)

	// save the blocklist to the store
	err := api.Ds.ModifyBlocklist(ctx, userId, add, remove)
	if err != nil {
		log.Error().Err(err).Msgf("failed to save block list %s", err)
	}

	if add != nil {
		// save the blocklist to the Cache
		err = api.Cache.AddToUserBlockList(ctx, add, ttl)
		if err != nil {
			log.Error().Err(err).Msgf("failed to save block list to Cache %s", err)
		}

		// unsubscribe from presence topics after friendship removed
		for _, block := range *add {
			userPresence := apipub.PresenceResponse{
				Userid:    block.Userid,
				Productid: productid,
			}
			blockPresence := apipub.PresenceResponse{
				Userid:    block.Blockedid,
				Productid: productid,
			}

			messenger.Unsubscribe(ctx, api.Cfg, block.Userid, blockPresence.Topic(tenant))
			messenger.Unsubscribe(ctx, api.Cfg, block.Blockedid, userPresence.Topic(tenant))
		}
	}

	if remove != nil {
		// remove the blocklist from the Cache
		err = api.Cache.RemoveFromUserBlockList(ctx, remove)
		if err != nil {
			log.Error().Err(err).Msgf("failed to remove block list from Cache %s", err)
		}
	}
}

func (api *SocialPublicAPI) setBlockListCache(ctx context.Context, userId string, blocklist *[]*apipub.BlocklistResponse) error {
	log := logger.FromContext(ctx)

	lockKey := "blocklist:" + userId + ":lock"
	lockTTL := time.Duration(api.Cfg.RedisLockDuration) * time.Second
	lock := api.Cache.GetSyncLock(ctx, lockKey, lockTTL)
	if lock == nil {
		log.Warn().Str("userid", userId).Msg("block list sync already started for user")
		return nil
	}
	defer lock.Release(ctx)

	//clear blocklist in Cache
	err := api.Cache.ClearUserBlocklist(ctx, userId)
	if err != nil {
		log.Error().Str("userid", userId).Msgf("failed to clear block list in Cache")
		return err
	}

	return api.Cache.AddToUserBlockList(ctx, blocklist, time.Duration(api.Cfg.TtlBlocklist)*time.Second)
}

func (api *SocialPublicAPI) createBlocklist(ctx context.Context, addList []string, blockObj *apipub.BlocklistResponse) []*apipub.BlocklistResponse {
	var blockedUsers []*apipub.BlocklistResponse
	if len(addList) <= 0 || blockObj == nil {
		return blockedUsers
	}

	profiles, profileErr := api.GetUserProfiles(ctx, addList, true)

	if profileErr != nil || profiles == nil || len(*profiles) <= 0 {
		return blockedUsers
	}

	for _, blockedid := range addList {
		blockedUser := &apipub.BlocklistResponse{
			Userid:            blockObj.Userid,
			Blockedid:         blockedid,
			OnlineServiceType: blockObj.OnlineServiceType,
			Productid:         blockObj.Productid,
			Created:           time.Now().UTC(),
		}

		for _, profile := range *profiles {
			if profile.Userid == blockedid {
				if profile.DisplayName != nil {
					blockedUser.Name = profile.DisplayName
				}
				if profile.Links != nil {
					blockedUser.Links = profile.Links
				}

				blockedUsers = append(blockedUsers, blockedUser)
				break
			}
		}
	}

	return blockedUsers
}

func (api *SocialPublicAPI) doesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error) {
	if api.Cache.UserBlocklistExistsInCache(ctx, blockerid) {
		return api.Cache.DoesBlockerBlockBlockee(ctx, blockerid, blockeeid)
	}
	return api.Ds.DoesBlockerBlockBlockee(ctx, blockerid, blockeeid)
}
