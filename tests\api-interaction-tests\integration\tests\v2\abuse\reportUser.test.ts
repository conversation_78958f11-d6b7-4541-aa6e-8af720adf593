import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccount, TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';
import { SQSClient, ReceiveMessageCommand, DeleteMessageCommand } from "@aws-sdk/client-sqs";

const sqsClient = new SQSClient({});
const queueURL = "https://sqs.us-east-1.amazonaws.com/************/t2gp-social-abuse-report-v2";
// max amount of time the test waits for the message to arrive at the queue, in ms
const msgEnqueueTimeout = 10000;

let usersTwok: TwokAccounts;

beforeAll(async () => {
  usersTwok = new TwokAccounts(2);
  await usersTwok.loginAll({});
});

afterAll(async () => {
  await usersTwok.logoutAll({});
});

describe(`report user[public v2]${_ds}`, () => {
  it('can report[happy]', async () => {
    let testCase = {
      description: `report an user, and long poll SQS up to ${msgEnqueueTimeout} ms`,
      expected: "report is present in SQS and message content matches input"
    };

    let reportInfo = {
      reportingUserId: usersTwok.acct["001"].publicId,
      reportingUserLocale: "US",
      reportingCategory: "Minor_Abuse_or_Child_Sexual_Content",
      reportMessage: "test case report user",
      platform: "PlayStation 5",
      subjectTitle: "API tests"
    };

    const expectedReport = (({ platform, ...rest }) => ({
      ...rest,
      reportingUserPlatformIdentifier: platform
    }))(reportInfo);

    let actualReport = {};

    // report user
    let reportUserResp = await socialApi.reportUser(
      usersTwok.acct["001"],
      usersTwok.acct["002"].publicId,
      reportInfo
    );
    socialApi.testStatus(StatusCodes.OK, reportUserResp);

    let found = false;
    let expectedMsgId = reportUserResp.body.MessageId;
    const startTime = Date.now();
    let elapsedTime = 0;

    // within a certain amount of time, receive messages from the queue.
    // process each message and see if it matches with the expected id.
    // each processed message is deleted whether it matches or not, because
    // the receive message command doesn't follow a particular order.
    // deleting processed messages could help get to the matched id sooner.
    while ((found != true) && (elapsedTime < msgEnqueueTimeout)) {
      // receive messages from the queue
      const data = await sqsClient.send(new ReceiveMessageCommand(
        {
          MaxNumberOfMessages: 10,
          QueueUrl: queueURL,
          WaitTimeSeconds: 2,
        }
      ));

      // process messages if they exist
      if (data.Messages != undefined) {
        for (let i = 0; i < data.Messages.length; i++) {
          // delete message from the queue
          await sqsClient.send(new DeleteMessageCommand(
            {
              QueueUrl: queueURL,
              ReceiptHandle: data.Messages[i].ReceiptHandle,
            }
          ));

          if (data.Messages[i].Body != undefined) {
            const msgBodyJson = JSON.parse(data.Messages[i].Body as string);
            let actualMsgId = msgBodyJson['MessageId']

            if (expectedMsgId == actualMsgId) {
              found = true;
              // get actual report
              actualReport = JSON.parse(msgBodyJson.Message);
              break;
            }
          }
        }
      }

      elapsedTime = Date.now() - startTime;
    }

    // verify the message is found in the queue
    socialApi.expectMore(
      () => {expect(found).toBe(true)},
      testCase,
      {
        resp: reportUserResp,
        additionalInfo: {
          "SQS queue URL": queueURL,
          "fail reason": "message not found in the queue"
        }
      }
    );

    // verify message content is correct
    // note that this verification wouldn't carry out if message is not found
    socialApi.expectMore(
      () => {expect(actualReport).toMatchObject(expectedReport)},
      testCase,
      {
        resp: reportUserResp,
        additionalInfo: {
          "SQS queue URL": queueURL,
          "fail reason": "message content not matching input"
        }
      }
    );
  });

  it('cannot report self', async () => {
    let expectedReport = {
      report: 'bad user behavior',
      detail: 'test case report self',
      note: 'API tests'
    };

    // report self
    let reportUserResp = await socialApi.reportUser(
      usersTwok.acct["001"],
      usersTwok.acct["001"].publicId,
      expectedReport
    );

    // return bad request; cannot report self
    socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, reportUserResp);
  });

  it('cannot report invalid ID', async () => {
    const invalidUserId = "5b35a1dab1c39e24626dbf5a49d272cc";

    let expectedReport = {
      report: 'bad user behavior',
      detail: 'test case report invalid ID',
      note: 'API tests'
    };

    // report self
    let reportUserResp = await socialApi.reportUser(
      usersTwok.acct["001"],
      invalidUserId,
      expectedReport
    );
    // return bad request; profile fetch failed
    socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, reportUserResp);
  });
});

describe(`corner cases[public v2]${_ds}`, () => {
  const invalidToken: string = 'invalidToken';

  it('cannot have invalid token', async () => {
    let testCase = {
      description: "report user with invalid token",
      expected: "4xx status"
    }

    let mockUser = new TwokAccount("<EMAIL>", "123", "");

    let expectedReport = {
      report: 'bad user behavior',
      detail: 'test case report invalid token',
      note: 'API tests'
    };

    // report user
    let reportUserResp = await socialApi.reportUser(
      mockUser,
      usersTwok.acct["002"].publicId,
      expectedReport
    );

    socialApi.expectMore(
      () => {expect(reportUserResp.status).toEqual(StatusCodes.UNAUTHORIZED)},
      testCase,
      {
        resp: reportUserResp,
        additionalInfo: {
          "fail reason": "unexpected status code"
        }
      }
    );
  });
});
