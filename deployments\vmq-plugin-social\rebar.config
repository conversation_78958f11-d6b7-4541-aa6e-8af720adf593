{erl_opts, [
    {parse_transform, lager_transform},
    {lager_function_transforms, [
        %% https://github.com/erlang-lager/lager#setting-dynamic-metadata-at-compile-time
        %% Placeholder   Resolve type  Callback tuple
        {dd, on_log, {t2gp_social_lager, dd_meta}}
    ]},
    warning_as_errors,
    debug_info
]}.
{plugins, [
    rebar3_path_deps
]}.
{deps, [
    {vernemq_dev, {git, "https://github.com/vernemq/vernemq_dev.git", {branch, "master"}}},
    {cuttlefish, "3.2.0"},
    {clique, {git, "https://github.com/vernemq/clique.git", {tag, "v0.3.11-verne"}}},
    {erlcloud, {git, "https://github.com/erlcloud/erlcloud.git", {tag, "3.7.6"}}},
    {eredis_cluster, {git, "https://github.com/Nordix/eredis_cluster", {branch, "master"}}},
    {lager, "3.9.2"},
    {jwt, {path, "deps/jwt"}},
    {hackney, "v1.18.1"},
    {opencensus, {git, "https://github.com/census-instrumentation/opencensus-erlang.git", {tag, "v0.9.3"}}}
]}.
{shell, [
    % {config, "config/sys.config"},
    {apps, [t2gp_social]}
]}.
