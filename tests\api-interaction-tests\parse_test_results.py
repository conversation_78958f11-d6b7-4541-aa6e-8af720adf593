#!/usr/bin/env python3
import json
import sys
sys.path.append("../reporting")
import post_to_slack
from datetime import datetime
import pytz
import os
from collections import defaultdict
import re
import argparse

# TODO: handle AND and OR relationships of the run tags.
# AND relationship excludes tests without all of the run tags present from the report
# OR relationship excludes tests without any of the run tags present from the report

# Parse arguments
ap = argparse.ArgumentParser(description="Parse Jest json output and post "\
    "test results to Slack. "\
    "Valid run tags are: [happy]. "\
    "Valid ignore tags are: [broken lowprio]. "\
    "Specify the run tags used during test execution."\
    "Specify the run suite used during test execution."\
    "Specify the run test used during test execution."\
    "Ignore tags are assumed to be used.")
ap.add_argument("path", help="path to the Jest json output")
ap.add_argument("--runMandatorytags", nargs="*", default=[], help="run mandatory tags")
ap.add_argument("--runOptionaltags", nargs="*", default=[], help="run optional tags")
ap.add_argument("--specificSuite", nargs="?", default="", help="run specific suite")
ap.add_argument("--specificTest", nargs="?", default="", help="run specific test")
args = ap.parse_args()

file_path = args.path
args_mandatory_tags = args.runMandatorytags
args_optional_tags = args.runOptionaltags
args_specificSuite = args.specificSuite
args_specificTest = args.specificTest

try:
    with open(file_path, "r") as f:
        test_report = json.loads(f.read())
except FileNotFoundError as e:
    print(e)
    test_report = None

# Create test_results template to be passed to post_to_slack
test_results = {
    "tr_name" : "Social API Interaction Test Result",
    "total_duration" : 0,
    "ts_list" : []
}

# Process test results from the Jest json output, and put them in ts_list
if test_report is not None:
    # Group the test suites according to the name of the containing directory of the
    # test suite files. When executing the tests, the test suite files of the same
    # directory might not necessarily be run together. Therefore this processing
    # is needed, otherwise the the test suites would seem to be not grouped in the
    # report.
    d = defaultdict(list)
    for ts in test_report["testResults"]:
        # get the name of the dir containing the test suite file.
        k = os.path.dirname(ts["name"]).split("/")[-1]
        d[k].append(ts)

    # concatenate the lists of test suites
    ts_grouped = []
    for v in d.values():
        ts_grouped += v

    #
    for ts in ts_grouped:
        # exclude test suites without the specific suite from the argument.
        if args_specificSuite and not (args_specificSuite in ts["name"]):
            continue
        ts_result = {
            # Use the file name plus the top-level dir name as the test suite name,
            # since each file is a test suite.
            "ts_name" : "[{}] {}".format(os.path.dirname(ts["name"]).split("/")[-1].upper(),
                                        os.path.basename(ts["name"]).split(".")[0]),
            "tc_list" : []
        }
        test_results["total_duration"] += (ts["endTime"] - ts["startTime"]) / 1000

        # process a test case result, then add it to the test case list of a test suite.
        for tc in ts["assertionResults"]:
            # gather all tags from the test case.
            # covers both styles of [tag1][tag2] and [tag1 tag2]
            tc_tags = []
            m = re.findall("\[.*?\]", tc["fullName"])
            for i in m:
                tc_tags += i.strip("[]").split(" ")

            # determine which results to exclude from the report:
            # exclude tests with lowprio tags from the report.
            if "lowprio" in tc_tags:
                continue

            # exclude tests without ALL of the run tags from the argument.
            if args_mandatory_tags and not all([t in tc_tags for t in args_mandatory_tags]):
                continue

            # exclude tests without ANY of the run tags from the argument
            if args_optional_tags and not any([t in tc_tags for t in args_optional_tags]):
                continue

            # exclude tests without the specific test from the argument
            if args_specificTest and not (args_specificTest in tc["fullName"]):
                continue

            # translate test case status from Jest output to what post_to_slack expects.
            if tc["status"] == "passed":
                tc["status"] = "pass"
            elif tc["status"] == "failed":
                tc["status"] = "fail"
            elif tc["status"] == "pending":
                tc["status"] = "skip"

            # remove tags for filtering during test execution from the test case name.
            tc["fullName"] = re.sub("\[.*?\]", "", tc["fullName"])

            # compose test case result
            tc_result = {
                # fullName includes the descriptions of all nesting describe blocks
                # plus the test block.
                "tc_name" : tc["fullName"],
                "result" : tc["status"]
            }

            #
            ts_result["tc_list"].append(tc_result)

        # add a test suite result to the test suite list.
        test_results["ts_list"].append(ts_result)

# Create timestamp for test completion
now = datetime.now(pytz.timezone("Canada/Pacific"))
test_results["completion_time"] = now.strftime("%Y/%m/%d %H:%M:%S %Z")

post_to_slack.post_results(test_results)