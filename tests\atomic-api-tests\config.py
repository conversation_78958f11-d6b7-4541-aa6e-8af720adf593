import os

# pass in a flag to switch environment
server = os.environ.get('ATOMIC_API_TEST_SOCIAL_API_URL')
if server == None:
    server = "https://social-service-develop.d2dragon.net/v1"

# TODO: credentials in as a environment variable
dna_account_info = {
    "email"      : os.environ.get("ATOMIC_API_TEST_2K_EMAIL"),
    "password"   : os.environ.get("ATOMIC_API_TEST_2K_PASSWORD"),
    "account_id" : ""
}

#
aws_access_key_id = os.environ.get("AWS_ACCESS_KEY_ID")
aws_secret_access_key = os.environ.get("AWS_SECRET_ACCESS_KEY")

status_list = ["friend", "pending", # defined
               "invalid", "", " ", "!@#%"]

friend_attr = {
    "friendid" : "f_dummy_friendid",
    "invitee"  : "f_dummy_invitee",
    "message"  : "f_dummy_msg",
    "name"     : "f_dummy_name", #
    "presence" : { #
        "status"    : "f_dummy_presence_status",
        "timestamp" : "2012-12-12T12:12:12Z"
    },
    "status"   : "f_dummy_status",
    "userid"   : "f_dummy_userid", #
    "viewed"   : False
}

user_profile_attr = {
    'email'     : '<EMAIL>',
    'lastLogin' : '2022-02-22T22:22:22Z',
    'locale'    : 'up_dummy_locale',
    'created'   : '2011-11-11T11:11:11Z',
    'name'      : 'up_dummy_name', #
    'presence'  : { #
        "status"    : "up_dummy_presence_status",
        "timestamp" : "2010-10-10T10:10:10Z"
    },
    'type'      : 'up_dummy_type',
    'userid'    : 'up_dummy_userid' #
}

user_email_attr = {
    "email"     : "<EMAIL>",
    "timestamp" : "2008-08-08T08:08:08Z",
    "type"      : "ue_dummy_type",
    "userid"    : "ue_dummy_userid"
}

block_list_attr = {
    "blockedUserIds" : {
        "bl_dummy_id1" : True,
        "bl_dummy_id2" : True,
        "bl_dummy_id3" : True
    },
    "userid" : "bl_dummy_userid"
}


# The common attributes that are pulled from a user profile to update a friend
# profile
common_attr = ["avatar", "name", "presence"]



#
## Get account ID.
## TODO: access DNA server directly instead of using the login API
#def getAccountId(email, password):
#    import api
#    resp = api.post_auth_login(email, password)
#    api.post_auth_logout(resp.json()['accessToken'])
#    return resp.json()['accountId']
#
#
## Init
#dna_account_info["account_id"] = getAccountId(dna_account_info["email"],
#                                              dna_account_info["password"])
#
