import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

// eslint-disable-next-line max-lines-per-function
describe('[public v1]', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
  });

  it('Member can get the member list of the group[happy]', async () => {
    let testCase = {
      description: "member views group members information",
      expected: "get a detailed list of member information"
    };

    // Member1 requests to join the group.
    let r: request.Response = await socialApi.requestToJoinV1(
      usersTwok.acct["member1"],
      groupId,
      {
        memberid: usersTwok.acct["member1"].publicId,
        status: 'requested',
        canCrossPlay: true
      },
    );
    socialApi.testStatus(StatusCodes.CREATED, r);

    // The leader agrees the member1 to join the group.
    r = await socialApi.approveRequestV1(usersTwok.acct["leader"], groupId, usersTwok.acct["member1"].publicId, {});
    socialApi.testStatus(StatusCodes.OK, r);

    // The leader sends an invitation to member2.
    r = await socialApi.inviteV1(usersTwok.acct["leader"], groupId, { memberid: usersTwok.acct["member2"].publicId, status: 'invited' });
    socialApi.testStatus(StatusCodes.CREATED, r);

    // Member2 accepts the invitation.
    r = await socialApi.acceptInviteV1(
      usersTwok.acct["member2"],
      groupId,
      {
        approverid: usersTwok.acct["leader"].publicId,
        memberid: usersTwok.acct["member2"].publicId
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 gets the member list of the group.
    const actualListInfo: request.Response = await socialApi.getMembers(
      usersTwok.acct["member1"],
      groupId
    );

    // Expect the member list of the group.
    // - contains the leader
    // - contains member1 and member2
    const expectedListInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.arrayContaining([
          expect.objectContaining({
            groupid: groupId,
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            groupid: groupId,
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
          expect.objectContaining({
            groupid: groupId,
            role: 'member',
            userid: usersTwok.acct["member2"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualListInfo).toMatchObject(expectedListInfo)},
      testCase,
      {
        resp: actualListInfo,
        additionalInfo: {
          "fail reason": "the member list does not contain all of group members"
        }
      }
    );
  });

  it('Get info of the specified member[happy]', async () => {
    let testCase = {
      description: "view the specified member information",
      expected: "get the detail info of the member"
    };

    // Member1 requests to join the group.
    let r: request.Response = await socialApi.requestToJoinV1(
      usersTwok.acct["member1"],
      groupId,
      {
        memberid: usersTwok.acct["member1"].publicId,
        status: 'requested',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);

    // The leader agrees the member1 to join the group.
    r = await socialApi.approveRequestV1(usersTwok.acct["leader"], groupId, usersTwok.acct["member1"].publicId, {});
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 gets the info of the member1.
    const actualMemberInfo = await socialApi.getGroupMember(
      usersTwok.acct["member1"],
      usersTwok.acct["member1"].publicId,
      groupId
    );

    // Expect the info of the member1 is returned.
    const expectedMemberInfo = {
      status: StatusCodes.OK,
      body: {
        role: 'member',
        userid: usersTwok.acct["member1"].publicId,
      },
    };
    socialApi.expectMore(
      () => {expect(actualMemberInfo).toMatchObject(expectedMemberInfo)},
      testCase,
      {
        resp: actualMemberInfo,
        additionalInfo: {
          "fail reason": "unexpected role or userid"
        }
      }
    );
  });
});
