// Package errs implements error handling utilities
package errs

import (
	"errors"
	"net/http"

	"github.com/2kg-coretech/dna-common/pkg/errs"
	"github.com/aws/smithy-go"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// SocialErrorInterface error interface for social services
type SocialErrorInterface interface {
	Error() string
	GetHttpErrorCode() int
	GetSocialErrorCode() SocialError
	GetStack() *string
}

// Error should match apipub.Error (oapi-codegen has no good way to use an external package)
type Error struct {
	// HTTP error code
	Code int `json:"code"`

	// error code
	ErrorCode SocialError `json:"errorCode"`

	// A string describing the error that occurred
	Message string `json:"message"`

	// Stack trace of the error (will be only returned in dev environment)
	Stack *string `json:"stack,omitempty"`

	// Request transaction id
	RequestID *string `json:"requestId,omitempty"`
}

// New create a new http error
func New(httpStatus int, errorCode SocialError) *Error {
	msg := ErrorString(errorCode)
	return NewMessage(httpStatus, msg, errorCode)
}

func ToError(err error) *Error {
	target := &Error{}
	if errors.As(err, &target) {
		return NewMessage(target.GetHttpErrorCode(), target.Message, target.ErrorCode)
	}

	return NewMessage(http.StatusInternalServerError, "failed to parse error", errs.InternalServerError)
}

func NewMessage(httpStatus int, msg string, errorCode SocialError) *Error {
	return &Error{
		Code:      httpStatus,
		ErrorCode: errorCode,
		Message:   msg,
		Stack:     utils.GetStack(),
	}
}

func Panic() *Error {
	return &Error{
		Code:      http.StatusInternalServerError,
		ErrorCode: EPanic,
		Message:   "panic",
		Stack:     utils.GetStack(),
	}
}

// Return returns http response
func Return(w http.ResponseWriter, r *http.Request, err *Error) {
	// report to datadog
	span, found := tracer.SpanFromContext(r.Context())
	if found {
		span.SetTag(ext.Error, err)
	}

	httpError := err.GetHttpErrorCode()
	if httpError == 0 {
		httpError = http.StatusInternalServerError
	}
	message := err.Error()
	var stack *string = nil
	if utils.IsNonProdCluster() {
		stack = err.GetStack()
		if stack == nil {
			stack = utils.GetStack()
		}
	}

	logger.Get(r).Error().Err(err).Msgf("HTTP %d: %s", httpError, message)
	e := &Error{
		Code:      err.GetHttpErrorCode(),
		ErrorCode: err.GetSocialErrorCode(),
		Message:   message,
		Stack:     stack,
	}

	utils.WriteJsonResponse(w, r, httpError, e)
}

// Error create error
func (e *Error) Error() string {
	return e.Message
}

func (e *Error) GetHttpErrorCode() int {
	return e.Code
}

func (e *Error) GetSocialErrorCode() SocialError {
	return e.ErrorCode
}

func (e *Error) GetStack() *string {
	return e.Stack
}

// ErrorString returns error string
func ErrorString(id SocialError) string {
	if e, ok := ErrorMap[id]; ok {
		return e.Message
	}
	return ErrorMap[EUnknown].Message
}

func IsEqual(err error, code SocialError) bool {
	var sErr SocialErrorInterface
	if errors.As(err, &sErr) {
		return sErr.GetSocialErrorCode() == code
	}
	return false
}

func SanitizeDynamoDBException(err error) error {
	var aerr smithy.APIError
	if errors.As(err, &aerr) {
		switch aerr.ErrorCode() {
		case "ValidationException":
			return errors.New("validation failed. Please check your request")
		case "ConditionalCheckFailedException":
			return errors.New("write operation failed because the condition specified in the request was not met")
		case "ProvisionedThroughputExceededException":
		case "LimitExceededException":
		case "RequestLimitExceeded":
			return errors.New("too many requests")
		case "InternalServerError":
			return errors.New("internal server error occurred")
		case "TransactionConflictException":
			return errors.New("transaction failef because of a conflict with another transaction")
		case "ResourceInUseException":
			return errors.New("attempt to create a resource that already exists")
		case "ResourceNotFoundException":
			return errors.New("the resource cannot be found")
		default:
			return errors.New("unknown DynamoDB error occurred")
		}
	}

	return err
}
