package apipub

import (
	"fmt"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// ChatMessageEventType defines model for chatMessageEventType.
type ChatMessageEventType string

// Defines values for ChatMessageEventType.
const (
	ChatMessageEventTypeJoined  ChatMessageEventType = "joined"
	ChatMessageEventTypeKicked  ChatMessageEventType = "kicked"
	ChatMessageEventTypeLeft    ChatMessageEventType = "left"
	ChatMessageEventTypeUnknown ChatMessageEventType = "unknown"
)

// Defines values for ChatMessageGroupType.
const (
	ChatMessageGroupTypeEvent        ChatMessageGroupType = "event"
	ChatMessageGroupTypeGroup        ChatMessageGroupType = "group"
	ChatMessageGroupTypeGroupPrivate ChatMessageGroupType = "group-private"
)

// ChatMessageGroup defines model for chatMessageGroup.
type ChatMessageGroup struct {
	EventData  *ChatEventData          `json:"eventData,omitempty"`
	FromUserid string                  `json:"fromUserid"`
	Message    string                  `json:"message"`
	Meta       *map[string]interface{} `json:"meta"`
	Roomid     string                  `json:"roomid"`
	Timestamp  time.Time               `json:"timestamp"`
	ToUserid   *string                 `json:"toUserid,omitempty"`

	// Ttl Unix timestamp when message will expired and be deleted
	Ttl     int64                `json:"ttl"`
	Type    ChatMessageGroupType `json:"type"`
	Version int                  `json:"version"`
}

// GroupChatsNext defines model for blocklistsNext.
type GroupChatsNext struct {
	Items []ChatMessageGroup `json:"items"`

	// Nextid next value to be used for requesting next page
	Nextid *Nextid `json:"nextid,omitempty"`
}

// ChatEventData defines model for chatEventData.
type ChatEventData struct {
	EventType ChatMessageEventType `json:"eventType"`
	Userid    string               `json:"userid"`
}

// ChatMessageGroupType defines model for chatMessageGroupType.
type ChatMessageGroupType string

// Roomid defines model for roomid.
type Roomid = string

// RedisKey redis key
func (msg *SendChatMessageRequestBody) RedisKey(tenant string) string {
	return ""
}

// RedisKey redis key
func (msg *ChatMessageGroup) RedisKey(tenant string) string {
	pk := msg.Roomid
	sk := utils.GenerateULID(msg.Timestamp)
	return fmt.Sprintf("%s:chatMessageGroup:{%s}:%s", tenant, pk, sk)
}

// PK generate partition key
func (msg *ChatMessageGroup) PK(tenant string) string {
	return tenant + "#chat-group#" + msg.Roomid
}

// SK generate sort key
func (msg *ChatMessageGroup) SK(tenant string) string {
	return tenant + "#message#" + utils.GenerateULID(msg.Timestamp)
}

// Ver ChatMessageGroup schema version
func (msg *ChatMessageGroup) Ver() int {
	return msg.Version
}

// func (*DataStore) SaveChatMessage(ctx context.Context, message *apipub.ChatMessage) error {
// 	if message == nil {
// 		return errs.New(http.StatusBadRequest, errs.EChatInvalidMessage)
// 	}
// 	return nil
// }

// PK generate partition key
func (msg *ChatMessage) PK(tenant string) string {
	return fmt.Sprintf("%s:User:%s:Chat", tenant, msg.SubjectId)
}

// PK generate partition key
func (msg *ChatMessage) RedisKey(tenant string, userid string) string {
	return fmt.Sprintf("%s:user:%s:chatMessages", tenant, userid)
}
