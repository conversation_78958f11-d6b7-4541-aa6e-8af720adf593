data "aws_iam_policy_document" "social_s3" {
  statement {
    actions = [
      "s3:GetObject",
      "s3:ListBucket",
      "s3:PutObject",
      "s3:DeleteObject",
    ]
    resources = [
      "arn:aws:s3:::t2gp-social",
      "arn:aws:s3:::t2gp-social/*",
      "arn:aws:s3:::t2gp-social-api",
      "arn:aws:s3:::t2gp-social-api/*",
      "arn:aws:s3:::t2gp-${local.resource_prefix}",
      "arn:aws:s3:::t2gp-${local.resource_prefix}/*",
      "arn:aws:s3:::t2gp-social-vernemq-plugin",
      "arn:aws:s3:::t2gp-social-vernemq-plugin/*",
      # Social's access to store-api
      "arn:aws:s3:::t2gp-pd-store-api",
      "arn:aws:s3:::t2gp-pd-store-api/*",
      # Social's access to abuse-reports bucket
      "arn:aws:s3:::t2gp-social-abuse-report",
      "arn:aws:s3:::t2gp-social-abuse-report/*"
    ]
  }
}
resource "aws_iam_policy" "social_s3" {
  name        = "t2gp-${local.resource_prefix}-s3"
  description = "EKS nodes access to API S3 resources"
  policy      = data.aws_iam_policy_document.social_s3.json
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-s3"
  }, local.tags)
}

data "aws_iam_policy_document" "social_dynamo" {
  statement {
    actions = [
      "dynamodb:List*",
      "dynamodb:DescribeReservedCapacity*",
      "dynamodb:DescribeLimits",
      "dynamodb:DescribeTimeToLive",
      "dynamodb:DescribeTable",
      "dynamodb:Scan",
      "dynamodb:UpdateItem",
      "dynamodb:UpdateTable",
      "dynamodb:UpdateTimeToLive",
      "dynamodb:Query",
      "dynamodb:PutItem",
      "dynamodb:GetItem",
      "dynamodb:GetRecords",
      "dynamodb:BatchGetItem",
      "dynamodb:BatchWriteItem",
      "dynamodb:DeleteItem",
    ]
    resources = [
      "arn:aws:dynamodb:us-east-1:354767525209:table/t2gp-${local.resource_prefix}-social",
      "arn:aws:dynamodb:us-east-1:354767525209:table/t2gp-${local.resource_prefix}-chat-messages",
      "arn:aws:dynamodb:us-east-1:354767525209:table/t2gp-${local.resource_prefix}-social/*",
      "arn:aws:dynamodb:us-east-1:354767525209:table/t2gp-${local.resource_prefix}-chat-messages/*",
      "arn:aws:dynamodb:us-east-1:354767525209:table/t2gp-${local.resource_prefix}-social-*/*",
      "arn:aws:dynamodb:us-east-1:354767525209:table/t2gp-${local.resource_prefix}-chat-messages-*/*"
    ]
  }
  statement {
    actions = [
      "dynamodb:List*",
    ]
    resources = [
      "arn:aws:dynamodb:us-east-1:354767525209:table/*"
    ]
  }
}

resource "aws_iam_policy" "social_dynamo" {
  name        = "t2gp-${local.resource_prefix}-dynamo"
  description = "Social DynamoDB Access"
  policy      = data.aws_iam_policy_document.social_dynamo.json
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-dynamo"
  }, local.tags)
}

data "aws_iam_policy_document" "social_ssm" {
  statement {
    actions = [
      "ssm:DescribeParameters"
    ]
    resources = [
      "*"
    ]
  }
  statement {
    actions = [
      "ssm:GetParameters",
      "ssm:GetParameter",
      "ssm:GetParametersByPath",
      "ssm:PutParameter",
    ]
    resources = [
      "arn:aws:ssm:us-east-1:354767525209:parameter/${var.project_code}/mqtt/*"
    ]
  }
}

resource "aws_iam_policy" "social_ssm" {
  name        = "t2gp-${local.resource_prefix}-ssm"
  description = "Social SSM Access"
  policy      = data.aws_iam_policy_document.social_ssm.json
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-ssm"
  }, local.tags)
}

data "aws_iam_policy_document" "social_sns" {
  statement {
    actions = [
      "SNS:Publish"
    ]
    resources = [
      data.aws_sns_topic.social_abuse_report_topic.arn
    ]
  }
}

resource "aws_iam_policy" "social_sns" {
  name        = "t2gp-${local.resource_prefix}-sns"
  description = "Social SNS Access"
  policy      = data.aws_iam_policy_document.social_sns.json
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-ssm"
  }, local.tags)
}

data "aws_iam_policy_document" "social_kinesis" {
  statement {
    actions = [
      "kinesis:DescribeStream",
      "kinesis:PutRecord",
      "kinesis:PutRecords"
    ]
    resources = [
      "arn:aws:kinesis:us-east-1:354767525209:stream/t2gp-social-api-traffic"
      # aws_kinesis_stream.social[0].arn
    ]
  }
}

resource "aws_iam_policy" "social_kinesis" {
  name        = "t2gp-${local.resource_prefix}-kinesis"
  description = "Social Kinesis Access"
  policy      = data.aws_iam_policy_document.social_kinesis.json
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-kinesis"
  }, local.tags)
}

module "aws_role" { # connects with aws_policy
  source       = "terraform-aws-modules/iam/aws//modules/iam-assumable-role-with-oidc"
  version      = "4.1.0"
  create_role  = true
  role_name    = "t2gp-${local.resource_prefix}"
  provider_url = data.aws_eks_cluster.t2gp.identity[0].oidc[0].issuer
  oidc_subjects_with_wildcards = [
    "system:serviceaccount:${var.project_code}-service:${var.project_code}-*",
  ]
  role_policy_arns = [
    aws_iam_policy.social_s3.arn,
    aws_iam_policy.social_dynamo.arn,
    aws_iam_policy.social_ssm.arn,
    aws_iam_policy.social_sns.arn,
    aws_iam_policy.social_kinesis.arn
  ]
  tags = local.tags
}
