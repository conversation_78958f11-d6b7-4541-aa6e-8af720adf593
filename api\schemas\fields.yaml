# This document is for reusable fields.  Basically anything that's not an object will be kept here and the objects in the other files will reference this file for common schemas for individual fields.
# Do not set required/readonly/writeonly/default etc status here and instead use them in the objects that use these schemas.  Always include at least type, description, and example.  Try to include a format if possible.
accountTypeDNA:
  type: integer
  description: The type of Account according to DNA.  Notable is 3 for full.
  format: uint32
  enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 7
  x-enumNames:
    - UNKNOWN
    - ANONYMOUS
    - PLATFORM
    - FULL
    - TOOLS
    - NEWSLETTER
    - UNDISCLOSED
    - PRIVACY POLICY ACCEPTED ONLY
  example: 2
errorCode:
  type: integer
  description: error code.  list of errors on docsite.
  format: uint32
  # defined in apipub/errors.go
  example: 100120
onlineServiceType:
  type: integer
  description: basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
  enum:
    - 0
    - 1
    - 2
    - 3
    - 4
    - 5
    - 6
    - 9
    - 10
    - 11
    - 12
    - 13
    - 14
    - 15
    - 16
    - 17
    - 18
    - 19
    - 20
    - 21
    - 22
    - 23
    - 24
    - 99
  x-enumNames:
    - UNKNOWN
    - XBOX LIVE
    - SONY ENTERTAINMENT NETWORK
    - STEAM
    - WEB
    - LEGACY GAME CENTER
    - GOOGLE PLAY
    - WINDOWS PHONE
    - CALICO
    - NINTENDO
    - GAME CENTER
    - WEGAME
    - VORTEX
    - EPIC
    - STADIA
    - FACEBOOK
    - GOOGLE
    - TWITTER
    - TWITCH
    - DEVICE
    - APPLE
    - ZENDESK
    - T2GP
    - WINDOWS DEVELOPER
  example: 3
groupMemberRole:
  type: string
  description: the role of a group member
  enum: [leader, member, nonmember]
  example: leader
groupid:
  allOf:
    - $ref: "#/ulid"
  description: the id of the group.  validates ULID pattern.
productid:
  allOf:
    - $ref: "#/dnaid"
  description: the id of the product. validates as a dnaid.
isFirstPartyInvite:
  type: boolean
  description: a flag to indicate whether this invite should be processed as a first party invite
  example: false
firstPartyid:
  type: string
  description: The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
  example: *****************
firstPartySessionid:
  type: string
  description: The First Party Session Id of the specified.
  example: *****************
canCrossPlay:
  type: boolean
  description: Does the user sending this have cross play enabled on their local system
  example: false
password:
  type: string
  description: password for the join request
  format: password
email:
  type: string
  description: email of user.  since this is PII, it is rarely returned.  do not depend on this field's value.
  example: "<EMAIL>"
dnaid:
  type: string
  description: an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
  example: effe28b27efc6594e43bfc0879b40085
  pattern: '^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$'
userid:
  type: string
  description: The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
  example: effe28b27efc6594e43bfc0879b40085
approverid:
  type: string
  description: The approverid of the request.  "leader" can be used to use the group leader as default.
  example: effe28b27efc6594e43bfc0879b40085
  pattern: '^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$|leader'
locale:
  type: string
  description: the locale of the user.
  example: en-US
  x-oapi-codegen-extra-tags:
    dynamodbav: "locale"
created:
  type: string
  description: timestamp that this record was created.
  format: date-time
  example: "2020-09-14T20:30:35.129Z"
dob:
  type: string
  description: The date of birth for the specified user. mm/dd/yyyy format. since this is PII, it is never returned.  do not depend on this field's value.
  pattern: '^([0-2][0-9]|(3)[0-1])(\/)(((0)[0-9])|((1)[0-2]))(\/)\d{4}$'
  example: "02/30/1999"
name:
  type: string
  description: The name associated with this user. If friend name is empty string, it is possible that it is not a full 2K account.
  example: discopotato
timestamp:
  type: string
  description: timstamp of the event
  format: date-time
  example: "2020-09-14T20:30:35.129Z"
expiresAt:
  type: integer
  description: Unix timestamp when message will expired and be deleted
  format: int64
  example: **********
serverReturnMembershipErrors:
  type: boolean
  description: Should this trusted API call return errors that happened when adding users
  example: true
maxMembers:
  type: integer
  description: default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
  example: 6
  minimum: 2
  maximum: 100
canMembersInvite:
  type: boolean
  description: Should all members be allowed to invite other users, not just the leader?
  example: true
nextid:
  type: string
  description: next value to be used for requesting next page
  example: 2f141278ccbf465ca054ad3c997722a7
friendStatus:
  type: string
  enum: [pending, friend]
  example: friend
membershipStatus:
  type: string
  description: The membership status of the invite or request to join state.
    * requested - a request to join flow initiated
    * approved - group join request has been approved
    * rejected - group join request has been rejected
    * invited - the user has been invited to the group
    * accepted - the user has accepted the invite to the group
    * declined - the invite has been declined
    * revoked - the invite has been revoked
  enum:
    [
      "requested",
      "approved",
      "rejected",
      "invited",
      "accepted",
      "declined",
      "revoked",
    ]
  example: "rejected"
joinRequestAction:
  type: string
  description: The group join request type.
    * manual - A member of the group is prompted to accept the user (usually the leader).
    * auto-approve - Any authenticated user within the product can join if they know the group id.
    * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
  enum: ["manual", "auto-approve", "auto-reject"]
  example: "manual"
presenceStatus:
  type: string
  description: the status of the presence record for the user
  enum:
    [
      "online",
      "offline",
      "playing",
      "custom",
      "away",
      "dnd",
      "chat",
      "authenticating",
    ]
  example: online
ttl:
  type: integer
  format: int64
  description: time in seconds until the object expires
  example: 3600
customStatus:
  type: string
  example: I like waffles
richPresence:
  type: string
  description: string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
gameData:
  type: string
  description: free form field for games to send additional presence information for their internal use.
  maxLength: 1024
gameName:
  type: string
  description: pre-localized game name.
  example: Sample Game
clientid:
  type: string
  example: random-mqtt-client-id-string
  description: client id of mqtt connection for mqtt presence
priority:
  type: integer
  description:
    Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).|
    40000 = mqtt server(connected/disconnected).  offline will remove from list.
dnaDisplayName:
  type: string
  description: 2k display name with optional 5 digit discrimnating hash
  pattern: '^[0-9a-zA-Z]{3,16}($|\#\d{5}$)'
  example: "discopotato#12345"
ulid:
  type: string
  description: lexigraphicaly sorted unique identifier
  example: 01EYRSXN4DCFF1AV128Y5A211J
  pattern: "^[0123456789ABCDEFGHJKMNPQRSTVWXYZ]{26}$"
reportingCategory:
  type: string
  description: Reporting category
  example: Player_Boosting_Cheating
  enum:
    [
      Player_Boosting_Cheating,
      Hacks_Mods,
      Privacy_Doxing,
      Fraud,
      Spam,
      Realistic_Gore_or_Violence,
      Suicide_or_Self_Harm,
      Adult_Sexual_Harassment,
      Adult_Sexually_Explicit_Content,
      Minor_Abuse_or_Child_Sexual_Content,
      Bullying_Threats_Harassment,
      Hate_Speech_Discrimination,
      Terrorism_or_Violent_Extremism,
      Other_Illegal_Content,
    ]
authCode:
  type: string
  description: authCode to use for OAuth with First Party for session syncing
endorsementName:
  type: string
  description: the name of the endorsement to be acted upon
isPositive:
  type: boolean
  default: true
  description: is this custom endorsement considered to be positive (or is it something like a downvote).  this is a permanent value for the endorsement and is not meant to reduce the increment.  the value of the first instance of this endorsement is preserved.  these "negative" values only increase and are meant to be used by game teams if they want to formulate a behavior score or something similar.
isPrivate:
  type: boolean
  default: false
  description: do not allow this endorsement be queried by users other than self.  this can be used to only allow other users to query positive endorsements and not see "downvotes"  but still store them to use for behavior score computations.  this is a permanent value of the endorsement and cannot be changed after the initial value is set.
incrementValue:
  type: integer
  minimum: 1
  description: how many endorsements
currentEndorsementCount:
  type: integer
  description: how many endorsements have been received since the last time the endorsement was reset.
totalEndorsementCount:
  type: integer
  description: how many endorsements have been received since the first time this endorsement was received.  does not get reset when the endorsement is reset.
