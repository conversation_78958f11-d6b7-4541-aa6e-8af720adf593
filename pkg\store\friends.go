package store

import (
	"context"
	"net/http"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// GetFriend retrive a single friend object
func (ds *DataStore) GetFriend(ctx context.Context, userid string, friendid string) (*apipub.FriendResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	// fetch the friend object
	pk := tenant + "#user#" + userid
	sk := tenant + "#friend#" + friendid
	var friend apipub.FriendResponse
	found, err := ds.GetItem(ctx, pk, sk, &friend)
	if err != nil {
		return nil, err
	}
	if !found {
		return nil, errs.New(http.StatusNotFound, errs.EFriendsNotFriend)
	}
	// fill in the missing friend object pieces w/ the profile object
	profileSk := tenant + "#profile#" + userid
	_, err = ds.GetItem(ctx, pk, profileSk, &friend)
	if err != nil {
		return nil, err
	}

	// we don't send presence information
	if friend.Status != apipub.Friend {
		friend.Presence = nil
	}

	return &friend, nil
}

func patchDynamoUserItem(user map[string]types.AttributeValue) map[string]types.AttributeValue {
	// delete presence from map
	if presence, ok := user["presence"]; ok {
		if _, isMap := presence.(*types.AttributeValueMemberM); isMap {
			delete(user, "presence")
		}
	}
	return user
}

// GetFullFriendList retrieves a full friend list
func (ds *DataStore) GetFullFriendList(ctx context.Context, userid string) (*[]*apipub.FriendResponse, error) {
	log := logger.FromContext(ctx)
	query := ds.buildGetFriendDynamoQuery(ctx, userid, nil, int32(ds.cfg.ListFriendsLimit), nil)

	friends, _, err := ds.getFriends(ctx, query)
	if err != nil {
		log.Error().Err(err).Str("user", userid).Msg("failed to get a full friend list from persistence")
		return nil, err
	}
	return friends, err
}

func (ds *DataStore) GetFriends(ctx context.Context, userid string, status *apipub.FriendStatus, limit int, next *string) (*[]*apipub.FriendResponse, *string, error) {
	log := logger.FromContext(ctx)
	var err error
	query := ds.buildGetFriendDynamoQuery(ctx, userid, status, int32(limit), next)

	friends, next, err := ds.getFriends(ctx, query)
	if err != nil {
		log.Error().Err(err).Str("user", userid).Msg("failed to get friends from persistence")
		return nil, nil, err
	}

	return friends, next, err
}

func (ds *DataStore) buildGetFriendDynamoQuery(ctx context.Context, userId string, status *apipub.FriendStatus, limit int32, next *string) dynamodb.QueryInput {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	pk := tenant + "#user#" + userId
	sk := tenant + "#friend#"
	toKey := sk + utils.GetMaxUTF8()
	query := dynamodb.QueryInput{
		TableName:              &ds.cfg.ProfileTable,
		KeyConditionExpression: aws.String("pk = :pk AND sk BETWEEN :sk AND :toKey"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk":    &types.AttributeValueMemberS{Value: pk},
			":sk":    &types.AttributeValueMemberS{Value: sk},
			":toKey": &types.AttributeValueMemberS{Value: toKey},
		},
		ScanIndexForward: aws.Bool(true),
		Limit:            aws.Int32(limit),
	}

	if status != nil {
		query.FilterExpression = aws.String("attribute_exists(friendid) AND #status = :status")
		query.ExpressionAttributeValues[":status"] = &types.AttributeValueMemberS{Value: string(*status)}
		query.ExpressionAttributeNames = map[string]string{"#status": "status"}
	}

	if next != nil {
		query.ExclusiveStartKey = map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: tenant + "#user#" + userId},
			"sk": &types.AttributeValueMemberS{Value: tenant + "#friend#" + *next},
		}
	}

	return query
}

func (ds *DataStore) getFriends(ctx context.Context, query dynamodb.QueryInput) (*[]*apipub.FriendResponse, *string, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	log := logger.FromContext(ctx)
	friendResults, returnNext, errFriend := ds.QueryByPkSkWithLimitAndNext(ctx, query)
	if errFriend != nil {
		return nil, returnNext, errFriend
	}
	if returnNext != nil {
		parts := strings.Split(*returnNext, "#")
		*returnNext = parts[len(parts)-1]
	}
	friendsMap := map[string]*apipub.FriendResponse{}

	// process query for friends
	var userids []string
	for _, item := range friendResults {
		friendUserID := strings.TrimPrefix(item["sk"].(*types.AttributeValueMemberS).Value, tenant+"#friend#")
		var friend apipub.FriendResponse
		err := attributevalue.UnmarshalMap(item, &friend)
		if err != nil {
			logger.FromContext(ctx).Error().Err(err).Interface("item", item).Msg("UnmarshalMap failed to parse friend")
			continue
		}

		userids = append(userids, friendUserID)
		friendsMap[friendUserID] = &friend
	}

	// if there are no users, we don't pull the profiles
	if len(userids) == 0 {
		return nil, returnNext, nil
	}

	// fetch user objects for friends to get presence and name information
	users, errUsers := ds.BatchGetItems(ctx, userids, tenant+"#user", tenant+"#profile")
	if errUsers != nil {
		log.Error().Err(errUsers).Strs("userids", userids).Msgf("BatchGetItems failed to get user profile")
		return nil, returnNext, errUsers
	}

	for _, user := range users {
		userid := strings.TrimPrefix(user["pk"].(*types.AttributeValueMemberS).Value, tenant+"#user#")
		if friend, ok := friendsMap[userid]; ok {
			var newFriend apipub.FriendResponse
			// patch old persence
			user = patchDynamoUserItem(user)
			err := attributevalue.UnmarshalMap(user, &newFriend)
			if err != nil {
				logger.FromContext(ctx).Error().Err(err).Interface("user", user).Msg("UnmarshalMap failed to parse friend")
				continue
			}
			// only copy the important items from profile
			friend.Name = newFriend.Name
			friend.Links = newFriend.Links
		}
	}

	var friends []*apipub.FriendResponse
	for _, friend := range friendsMap {
		friends = append(friends, friend)
	}
	return &friends, returnNext, nil
}

// MakeFriend establish friendship
func (ds *DataStore) MakeFriend(ctx context.Context, userid string, friendid string, message string, isUserBlocked bool, userOST apipub.OnlineServiceType) (string, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	if userid == "" || friendid == "" {
		return "", errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidFriendID)
	}

	userFriendPK := tenant + "#user#" + userid
	userFriendSK := tenant + "#friend#" + friendid
	var userFriend, revFriend apipub.FriendResponse
	found, err := ds.GetItem(ctx, userFriendPK, userFriendSK, &userFriend)
	if err != nil {
		return "", err
	}

	if !found {
		userFriend := apipub.FriendResponse{
			Invitee: friendid,
			Status:  apipub.FriendStatus("pending"),
		}
		if message != "" {
			userFriend.Message = &message
		}
		userFriend.Userid = userid
		userFriend.Friendid = friendid
		var items []DataStoreItem
		items = append(items, &userFriend)
		if !isUserBlocked {

			revFriend = userFriend
			revFriend.Userid = friendid
			revFriend.Friendid = userid
			revFriend.InviterOST = &userOST
			items = append(items, &revFriend)
		}
		ds.PutItems(ctx, items)
		return string(apipub.Pending), nil
	}

	if userFriend.Status == apipub.Pending && userid == userFriend.Invitee {
		// establish a new friendship
		userFriend.Status = apipub.Friend
		revFriend = userFriend
		revFriend.Userid = friendid
		revFriend.Friendid = userid
		revFriend.InviterOST = &userOST
		revFriend.Status = apipub.Friend
		ds.PutItems(ctx, []DataStoreItem{&userFriend, &revFriend})
		return string(apipub.Friend), nil
	}

	// make sure the appropriate status is set when the friend invite
	// is sent for not the first time
	return string(userFriend.Status), nil
}

func (ds *DataStore) UpdateFriends(ctx context.Context, friends *[]*apipub.FriendResponse) error {
	var items []DataStoreItem
	for _, friend := range *friends {
		items = append(items, friend)
	}

	err := ds.PutItems(ctx, items)
	return err
}

// MakeUnfriend break a friendship
func (ds *DataStore) MakeUnfriend(ctx context.Context, userid string, friendid string) error {
	if userid == "" || friendid == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidFriendID)
	}

	f := apipub.FriendResponse{
		Userid:   userid,
		Friendid: friendid,
	}
	err1 := ds.DeleteItem(ctx, &f)
	f.Userid = friendid
	f.Friendid = userid
	err2 := ds.DeleteItem(ctx, &f)
	if err1 != nil {
		return err1
	}
	if err2 != nil {
		return err2
	}

	return nil
}

// GetFriendsCount query a user's friend count
func (ds *DataStore) GetFriendsCount(ctx context.Context, userid string) (*int32, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	query := dynamodb.QueryInput{
		TableName:              aws.String(ds.cfg.ProfileTable),
		KeyConditionExpression: aws.String("#pk = :pk AND begins_with(#sk, :sk)"),
		FilterExpression:       aws.String("#status <> :s"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk": &types.AttributeValueMemberS{Value: tenant + "#user#" + userid},
			":sk": &types.AttributeValueMemberS{Value: tenant + "#friend#"},
			":s":  &types.AttributeValueMemberS{Value: "pending"},
		},
		ExpressionAttributeNames: map[string]string{
			"#pk":     "pk",
			"#sk":     "sk",
			"#status": "status",
		},
		Select: "COUNT",
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.QueryWithContext", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("GetFriendsCount"))
	setPkSkFromQueryExp(&span, query)
	result, err := ds.ddb.Query(ctx, &query)
	span.Finish()
	if err != nil {
		logger.FromContext(ctx).Error().Err(err).Str("pk", tenant+"#user#"+userid).Str("sk", tenant+"#friend#").Msgf("QueryWithContext() friends count for %s ", userid)
		return nil, err
	}
	return &result.Count, nil
}
