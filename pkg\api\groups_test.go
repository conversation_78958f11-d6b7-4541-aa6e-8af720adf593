package api

import (
	"context"
	"errors"
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	. "github.com/onsi/gomega"
	"github.com/segmentio/encoding/json"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func TestCreateGroup(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
	productID := "c71f50c3533c462785a2fc22f24c9fad"
	userID := "b287e655461f4b3085c8f244e394ff7e"
	jwt := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MzgyMDYxMjEsImlhdCI6MTYzODIwMjUyMSwianRpIjoiZjk4OWQxZGM5ZjFkNDgxM2JkYTc5YzUxY2YwNGMwZGIiLCJ0dHkiOjAsInBpZCI6ImM3MWY1MGMzNTMzYzQ2Mjc4NWEyZmMyMmYyNGM5ZmFkIiwiZ2lkIjoiNDg1OThhMzkzYmZhNGMxOTlkYmI1NzFiYThiNWM3MTMiLCJsb2MiOiJlbi1VUyIsImN0eSI6IkFzaGJ1cm4iLCJjdHIiOiJVUyIsImxhdCI6MzkuMDQ2OSwibG9uIjotNzcuNDkwMywicnRpIjoiM2QwNTlkZGI5N2JmNDdlN2I1YjA1ZWUyMjAwY2YyYWMiLCJyZXgiOjE2MzgyMDk3MjEsImlzcyI6ImI5MGUzMjgzZjIyZWFlOThhNTI5MjQxMjU1ZThkYzYyIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJhdHkiOjMsImFncCI6NSwic2lkIjoiY2E1MTRhNzg0YzYzNGEzODk3NGJhYjVjZGM5ZDVjZmYiLCJ2ZXIiOnRydWUsImFnciI6MTA3MCwiZG9iIjoiUUUvV1FCV25wdXZrOWNrRDlDbVkzQT09In0.3oqJxtJ0MeNiA_MBcdDgOsTc8-hfmyDsOett6dlJuo4"

	reqBody := apipub.CreateGroupRequestBody{
		MaxMembers:        aws.Int(30),
		JoinRequestAction: apipub.AutoApprove,
		CanCrossPlay:      aws.Bool(true),
		CanMembersInvite:  aws.Bool(false),
		Password:          aws.String(""),
	}
	canXplay := true
	var groupMember apipub.GroupMemberResponse
	groupMember.Userid = userID
	var groupMembers []apipub.GroupMemberResponse
	groupMembers = append(groupMembers, groupMember)
	expectedBody := &apipub.GroupResponse{
		Groupid:      groupID,
		Productid:    productID,
		MaxMembers:   30,
		Created:      nil,
		CanCrossPlay: &canXplay,
	}
	expectedBody.Members = &groupMembers

	g.Describe("CreateGroup", func() {
		g.It("should successfully create a group", func() {
			g.Timeout(mock.testTimeout)
			var count int64 = 0
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
			mock.rc.EXPECT().SetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any())
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil)
			w, req := AddBodyToRequest(reqBody, jwt)

			mock.api.CreateGroup(w, req)

			g.Assert(w.Code).Equal(http.StatusCreated)
			var group apipub.GroupResponse
			body := w.Body.String()
			json.Unmarshal([]byte(body), &group)
			g.Assert(group.MaxMembers).Equal(30)
			g.Assert(group.Productid).Equal(productID)
		})

		g.It("failed to create a group because Count User Groups returned an error", func() {
			var count int64 = 0
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
			w, req := AddBodyToRequest(reqBody, jwt)

			mock.api.CreateGroup(w, req)
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}

func TestGetGroup(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"

	reqBody := apipub.CreateGroupRequestBody{
		MaxMembers:        aws.Int(30),
		JoinRequestAction: apipub.AutoApprove,
		CanCrossPlay:      aws.Bool(true),
		CanMembersInvite:  aws.Bool(false),
		Password:          aws.String(""),
	}
	now := time.Now().UTC()
	var groupMember apipub.GroupMemberResponse
	userID := "b287e655461f4b3085c8f244e394ff7e"
	groupMember.Userid = userID
	groupMember.Name = aws.String("Test User")

	var groupMembers []apipub.GroupMemberResponse
	groupMembers = append(groupMembers, groupMember)

	expectedBody := &apipub.GroupResponse{
		Groupid:    groupID,
		Productid:  productID,
		MaxMembers: 30,
		Created:    &now,
		Members:    &groupMembers,
	}

	g.Describe("GetGroup", func() {
		g.It("should return correct groupid", func() {
			profile := apipub.UserProfileResponse{Userid: userID}
			profileAr := []*apipub.UserProfileResponse{&profile}

			// Mock the GetGroup call
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedBody, nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().PutUserProfiles(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

			w, req := AddBodyToRequest(reqBody, User1JWT)

			// get the group
			mock.api.GetGroup(w, req, apipub.Groupid(expectedBody.Groupid))

			var newGroup apipub.GroupResponse
			body := w.Body.String()
			json.Unmarshal([]byte(body), &newGroup)
			if newGroup.Groupid != expectedBody.Groupid {
				t.Errorf("group ids don't match %s != %s", newGroup.Groupid, expectedBody.Groupid)
			}
			if newGroup.MaxMembers != expectedBody.MaxMembers {
				t.Errorf("group max members don't match %d != %d", newGroup.MaxMembers, expectedBody.MaxMembers)
			}
		})

		g.It("GetGroup returned an error", func() {
			// user is not apart of the group
			groupMember.Userid = "not user1"
			var newGroupMembers []apipub.GroupMemberResponse
			newGroupMembers = append(newGroupMembers, groupMember)
			expectedBody.Members = &newGroupMembers
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedBody, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			w, req := AddBodyToRequest(reqBody, User1JWT)
			mock.api.GetGroup(w, req, apipub.Groupid(expectedBody.Groupid))

			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("should return 403 for member not in group", func() {
			// user is not apart of the group
			groupMember.Userid = "not user1"
			var newGroupMembers []apipub.GroupMemberResponse
			newGroupMembers = append(newGroupMembers, groupMember)
			expectedBody.Members = &newGroupMembers
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedBody, nil)
			w, req := AddBodyToRequest(reqBody, User1JWT)
			mock.api.GetGroup(w, req, apipub.Groupid(expectedBody.Groupid))
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})

		g.It("should return 404 for non existing group", func() {
			// Group doesn't exist
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			w, req := AddBodyToRequest(reqBody, User1JWT)
			mock.api.GetGroup(w, req, apipub.Groupid(expectedBody.Groupid))
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})
	})

}

func TestGetGroups(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	reqBody := apipub.CreateGroupRequestBody{
		MaxMembers:        aws.Int(30),
		JoinRequestAction: apipub.AutoApprove,
		CanCrossPlay:      aws.Bool(true),
		CanMembersInvite:  aws.Bool(false),
		Password:          aws.String(""),
	}

	params := apipub.GetGroupsParams{}

	var groupMember apipub.GroupMemberResponse
	userID := "b287e655461f4b3085c8f244e394ff7e"
	groupMember.Userid = userID

	g.Describe("GetGroups", func() {
		g.It("should return correct groupid", func() {
			var group apipub.GroupResponse
			var testGroups []*apipub.GroupResponse
			testGroups = append(testGroups, &group)
			// Mock the GetGroups call
			mock.rc.EXPECT().GetUserGroups(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&testGroups, "", nil)

			w, req := AddBodyToRequest(reqBody, User1JWT)
			mock.api.GetGroups(w, req, params)

			var groups apipub.GroupsNext
			body := w.Body.String()
			json.Unmarshal([]byte(body), &groups)

			count := len(groups.Items)
			if count != 1 {
				t.Fatalf("Incorrect number of groups %d", count)
			}
		})

		g.It("GetGroups returned an error", func() {
			mock.rc.EXPECT().GetUserGroups(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			w, req := AddBodyToRequest(reqBody, User1JWT)
			mock.api.GetGroups(w, req, params)
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})
	})
}

func TestDeleteGroup(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"

	var group apipub.GroupResponse

	// Test User is not group leader and tries to delete group
	// Make user1 a member of the group but not leader
	member := apipub.GroupMemberResponse{
		Role:   apipub.Member,
		Userid: "b287e655461f4b3085c8f244e394ff7e",
	}
	members := []apipub.GroupMemberResponse{
		{
			Role:   apipub.Leader,
			Userid: "leader",
		},
		member,
	}

	now := time.Now().UTC()
	group = apipub.GroupResponse{
		Groupid:    groupID,
		Productid:  productID,
		Created:    &now,
		Members:    &members,
		MaxMembers: 20,
	}

	g.Describe("DeleteGroup", func() {
		g.It("should get not found with nil group", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			w, req := Login(User1JWT)
			mock.api.DeleteGroup(w, req, "coolGroup")
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("should get bad request with error from get group", func() {
			// Test GetGroup errors
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
			w, req := Login(User1JWT)

			mock.api.DeleteGroup(w, req, "fail")
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("should get bad request when leader deletes a group with get group returning  error", func() {
			g.Timeout(mock.testTimeout)
			// Test user is group leader and tries to delete group but fails
			member.Role = apipub.Leader
			newMembers := []apipub.GroupMemberResponse{}
			newMembers = append(newMembers, member)

			group.Members = &newMembers

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().DeleteGroup(gomock.Any(), gomock.Any()).Return(errors.New("errored while deleting group"))

			w, req := Login(User1JWT)
			mock.api.DeleteGroup(w, req, "fails to delete")
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("should succeed", func() {
			g.Timeout(mock.testTimeout)
			// Test user is group leader and tries to delete group and succeeds
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().DeleteGroup(gomock.Any(), gomock.Any()).Return(nil)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil)
			w, req := Login(User1JWT)
			mock.api.DeleteGroup(w, req, "successful delete")
			g.Assert(w.Code).Equal(http.StatusOK)
		})
	})
}
func TestGetGroupMembers(t *testing.T) {
	g := goblin.Goblin(t)

	// create mock data store
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"

	g.Describe("GetGroupMembers", func() {
		g.It("should return bad request for db error", func() {
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			w, r := Login(User1JWT)
			// db.GetGroup(par)(nil, error) fails
			mock.api.GetGroupMembers(w, r, groupID)
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("should return group not found", func() {
			// db.GetGroup(par)(nil, nil) returns none
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

			w, r := Login(User1JWT)
			mock.api.GetGroupMembers(w, r, groupID)
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("should return forbidden when user is not in group", func() {
			// db.GetGroup(par)(group, nil) user is not in group
			group := apipub.GroupResponse{
				Groupid:           groupID,
				Productid:         productID,
				MaxMembers:        10,
				JoinRequestAction: apipub.AutoApprove,
			}

			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			w, r := Login(User1JWT)
			mock.api.GetGroupMembers(w, r, groupID)
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})

		g.It("should get correct group members", func() {
			g.Timeout(mock.testTimeout)
			group := apipub.GroupResponse{
				Groupid:           groupID,
				Productid:         productID,
				MaxMembers:        10,
				JoinRequestAction: apipub.AutoApprove,
			}

			// db.GetGroup(par)(group, nil) user is in group,(mock GetUserProfile for every user in group) success
			groupLeader := apipub.GroupMemberResponse{
				Userid:    "b287e655461f4b3085c8f244e394ff7e",
				Role:      apipub.Leader,
				Productid: productID,
			}
			group.AddMemberIfNotExist(&groupLeader)

			groupMember := apipub.GroupMemberResponse{
				Userid:    "e12c3df480984141b2f385646b2024fa",
				Role:      apipub.Member,
				Productid: productID,
			}
			group.AddMemberIfNotExist(&groupMember)

			profile := apipub.UserProfileResponse{Userid: groupLeader.Userid}
			profileAr := []*apipub.UserProfileResponse{&profile}

			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil).AnyTimes()
			mock.ds.EXPECT().PutUserProfiles(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()

			w, r := Login(User1JWT)
			mock.api.GetGroupMembers(w, r, groupID)

			var members apipub.MembersNext
			body := w.Body.String()
			json.Unmarshal([]byte(body), &members)

			g.Assert(members.Items).Equal(*group.Members)
		})
	})
}

func TestGetGroupMember(t *testing.T) {
	g := goblin.Goblin(t)
	// create mock data store
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"

	// db.GetGroup(par)(group, nil) requesting user is not in group
	group := apipub.GroupResponse{
		Groupid:           groupID,
		Productid:         productID,
		MaxMembers:        10,
		JoinRequestAction: apipub.AutoApprove,
	}

	g.Describe("GetGroupMember", func() {
		g.It("should get bad request with db error", func() {
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			w, r := Login(User1JWT)
			// db.GetGroup(par)(nil, error) fails
			mock.api.GetGroupMember(w, r, groupID, "e12c3df480984141b2f385646b2024fa")
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("should get not found", func() {
			// db.GetGroup(par)(nil, nil) returns none
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			w, r := Login(User1JWT)
			mock.api.GetGroupMember(w, r, groupID, "e12c3df480984141b2f385646b2024fa")
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("should get forbidden when called by non-member", func() {
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			w, r := Login(User1JWT)
			mock.api.GetGroupMember(w, r, "group1", "e12c3df480984141b2f385646b2024fa")
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})

		g.It("should get not found when requested user is not in group", func() {

			// db.GetGroup(par)(group, nil) requested user is not in group
			groupLeader := apipub.GroupMemberResponse{
				Userid: "b287e655461f4b3085c8f244e394ff7e",
				Role:   apipub.Leader,
				// Status:    apipub.GroupMemberStatusJoined,
				Productid: productID,
				Name:      aws.String("User1"),
			}

			group.Members = &[]apipub.GroupMemberResponse{groupLeader}
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

			w, r := Login(User1JWT)
			mock.api.GetGroupMember(w, r, groupID, "e12c3df480984141b2f385646b2024fa")
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})
	})
}

func TestUpdateGroup(t *testing.T) {
	g := goblin.Goblin(t)
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	// create mock data store
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"

	// db.GetGroup(par)(group, nil) requesting user is not in group
	var members []apipub.GroupMemberResponse
	members = append(members, apipub.GroupMemberResponse{
		Userid: "1",
		Role:   apipub.Nonmember,
	})
	now := time.Now().UTC()
	group := apipub.GroupResponse{
		Groupid:    groupID,
		Productid:  productID,
		MaxMembers: 10,
		// Public:     aws.Bool(true),
		JoinRequestAction: apipub.AutoApprove,
		Members:           &members,
		Created:           &now,
	}

	reqBody := apipub.UpdateGroupRequest{
		MaxMembers:       aws.Int(30),
		CanMembersInvite: aws.Bool(false),
		Password:         aws.String(""),
	}

	profile := apipub.UserProfileResponse{Userid: "1"}
	g.Describe("UpdateGroup", func() {
		g.It("should get bad request with db error", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))

			w, r := Login(User1JWT)
			// db.GetGroup(par)(nil, error) fails
			mock.api.UpdateGroup(w, r, groupID)
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("should get not found", func() {
			// db.GetGroup(par)(nil, nil) returns none
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			w, r := Login(User1JWT)
			mock.api.UpdateGroup(w, r, "group1")
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("should get forbidden when requesting user is not in group", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			w, r := AddBodyToRequest(reqBody, User1JWT)
			mock.api.UpdateGroup(w, r, groupID)
			g.Assert(w.Code).Equal(http.StatusForbidden)

			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsMemberNotInGroup).Error()))
		})

		g.It("should get forbidden when user is not leader", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			w, r := AddBodyToRequest(reqBody, User1JWT)

			groupMember := apipub.GroupMemberResponse{
				Userid: "b287e655461f4b3085c8f244e394ff7e",
				Role:   apipub.Member,
				// Status:    apipub.GroupMemberStatusJoined,
				Productid: productID,
			}
			group.AddMemberIfNotExist(&groupMember)
			mock.api.UpdateGroup(w, r, groupID)
			g.Assert(w.Code).Equal(http.StatusForbidden)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotLeader).Error()))
			group.RemoveMember(groupMember.Userid)
		})

		g.It("should succeed editing group", func() {
			g.Timeout(mock.testTimeout)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().SetGroupMeta(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().SetGroupMaxMembers(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			//mock.rc.EXPECT().SetGroupPassword(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&[]*apipub.UserProfileResponse{&profile}, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&[]*apipub.UserProfileResponse{&profile}, nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil)
			groupLeader := apipub.GroupMemberResponse{
				Userid: "b287e655461f4b3085c8f244e394ff7e",
				Role:   apipub.Leader,
				// Status:    apipub.GroupMemberStatusJoined,
				Productid: productID,
			}
			group.AddMemberIfNotExist(&groupLeader)

			meta := map[string]interface{}{
				"name": "TestGroup",
			}
			editGroupRequest := apipub.UpdateGroupRequest{
				MaxMembers: aws.Int(100),
				Meta:       &meta,
			}

			rr, req := AddBodyToRequest(editGroupRequest, User1JWT)

			mock.api.UpdateGroup(rr, req, groupID)

			g.Assert(rr.Code).Equal(http.StatusOK)
		})
	})
}

func TestKickOrLeaveGroup(t *testing.T) {
	g := goblin.Goblin(t)

	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupId := utils.GenerateNewULID()
	userid := "e12c3df480984141b2f385646b2024fa" //user2
	productid := "4029a6ffe9924f969955aa2e1c0782aa"
	requestorid := "b287e655461f4b3085c8f244e394ff7e"
	appid := "955e2f6c7ce345a6ba0d4d4f77d9257d"

	g.Describe("KickOrLeaveGroup", func() {
		g.It("1. FAIL - failed to get group", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EGroupsNotFound))
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			w, r := Login(User1JWT)

			mock.api.KickOrLeaveGroup(w, r, groupId, requestorid, userid, nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("2. FAIL - kick helper return error", func() {
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "member",
				},
			}

			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(apipub.ChatMessageEventTypeUnknown, false, errs.New(http.StatusUnprocessableEntity, errs.EGroupsLeaveOrKickFailed))
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			w, r := Login(User1JWT)

			mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
		})

		g.It("3. OK - user1 kick user2 and user1 is leader", func() {
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "member",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(apipub.ChatMessageEventTypeKicked, false, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid)
			mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			w, r := Login(User1JWT)
			mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(w.Body.String()).Equal("{}\n")
		})

		g.It("4. OK - user2 leader left the group", func() {
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "member",
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "leader",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(apipub.ChatMessageEventTypeLeft, false, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid)
			mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			w, r := Login(User1JWT)
			mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(w.Body.String()).Equal("{}\n")
		})

		g.It("5. OK - user2 as member left the group", func() {
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "member",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(apipub.ChatMessageEventTypeLeft, false, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			w, r := Login(User1JWT)
			mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(w.Body.String()).Equal("{}\n")
		})

		g.It("6. OK - kick a member with a customized reason", func() {
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "member",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apipub.ChatMessageEventTypeLeft, false, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid)
			mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SavePresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			w, r := Login(User1JWT)
			mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(w.Body.String()).Equal("{}\n")
		})

		g.It("7. OK - user2 as member left the group with this as the active group in presence", func() {
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "member",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			activeGroup := apipub.ActiveGroupResponse{
				CanCrossPlay:       false,
				CanRequestJoin:     false,
				CurrentMemberCount: 2,
				Groupid:            groupId,
				MaxMembers:         2,
			}
			var presences []*apipub.PresenceResponse
			presence := apipub.PresenceResponse{
				ActiveGroup: &activeGroup,
				Userid:      userid,
				Productid:   productid,
			}
			presences = append(presences, &presence)

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(apipub.ChatMessageEventTypeLeft, false, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid).Return(&presences, nil)
			mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			w, r := Login(User1JWT)
			mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			g.Assert(w.Code).Equal(http.StatusOK)
			g.Assert(w.Body.String()).Equal("{}\n")
		})

		g.It("8. OK - kick a member with this as the active group in presence", func() {
			// members := []apipub.GroupMember{
			// 	{
			// 		Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
			// 		Role:   "leader",
			// 	},
			// 	{
			// 		Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
			// 		Role:   "member",
			// 	},
			// }
			// group := apipub.Group{
			// 	Groupid: groupId,
			// 	Members: &members,
			// }
			// activeGroup := apipub.ActiveGroup{
			// 	CanCrossPlay:       false,
			// 	CanRequestJoin:     false,
			// 	CurrentMemberCount: 2,
			// 	Groupid:            groupId,
			// 	MaxMembers:         2,
			// }
			// var presences []*apipub.Presence
			// presence := apipub.Presence{
			// 	ActiveGroup: &activeGroup,
			// 	Userid:      userid,
			// 	Productid:   productid,
			// }
			// presences = append(presences, &presence)

			// reason := "Time out"
			// mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			// mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), &reason).Return(apipub.ChatMessageEventTypeLeft, nil)
			// mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid).Return(&presences, nil)
			// mock.rc.EXPECT().SetPresence(gomock.Any(), &presence, nil, gomock.Any(), gomock.Any(), userid, gomock.Any(), gomock.Any(), gomock.Any())
			// mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			// mock.rc.EXPECT().GetUserGroups(gomock.Any(), userid, productid, nil, nil).Return(nil, "", nil)
			// mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any())
			// mock.rc.EXPECT().SetActiveGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			// w, r := Login(User1JWT)
			// mock.api.KickOrLeaveGroup(w, r, apipub.Groupid(groupId), requestorid, apipub.Userid(userid), nil, productid, appid, "", 0, 0, 0)

			// g.Assert(w.Code).Equal(http.StatusOK)
			// g.Assert(w.Body.String()).Equal("{}\n")
		})
	})
}

func TestUpdateGroupMember(t *testing.T) {
	g := goblin.Goblin(t)

	// create mock data store
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupId := "01FN83SSZZ26H316YA4HQ6KX88"
	userid := "e12c3df480984141b2f385646b2024fa" //user2

	g.Describe("UpdateGroupMember", func() {

		g.It("1. FAIL - failed to get group, group is nil", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			w, r := Login(User1JWT)
			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("2. FAIL - failed to get group, got the error", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
			w, r := Login(User1JWT)
			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("3. FAIL - no patch body is provided", func() {
			group := apipub.GroupResponse{}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			w, r := Login(User1JWT)
			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusBadRequest)
		})

		g.It("4. FAIL - invalid role in body", func() {
			updateRoleRequest := apipub.UpdateGroupMemberRequest{
				Role: "admin",
			}
			w, r := AddBodyToRequest(updateRoleRequest, User1JWT)

			//request for friends fails
			group := apipub.GroupResponse{}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
		})

		g.It("5. FAIL - logged in user is not in group", func() {
			updateRoleRequest := apipub.UpdateGroupMemberRequest{
				Role: "leader",
			}
			w, r := AddBodyToRequest(updateRoleRequest, User2JWT)

			//request for friends fails
			group := apipub.GroupResponse{}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})

		g.It("6. FAIL - logged in user not leader", func() {
			updateRoleRequest := apipub.UpdateGroupMemberRequest{
				Role: "leader",
			}
			w, r := AddBodyToRequest(updateRoleRequest, User2JWT)

			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "member",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})
		g.It("7. FAIL - member is not in group", func() {
			updateRoleRequest := apipub.UpdateGroupMemberRequest{
				Role: "leader",
			}
			w, r := AddBodyToRequest(updateRoleRequest, User1JWT)

			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusNotFound)
		})

		g.It("8. FAIL - group requires at least 1 leader", func() {
			updateRoleRequest := apipub.UpdateGroupMemberRequest{
				Role: "member",
			}
			w, r := AddBodyToRequest(updateRoleRequest, User2JWT)

			members := []apipub.GroupMemberResponse{
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "leader",
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
		})

		g.It("9. OK - user1 update to leader", func() {
			updateRoleRequest := apipub.UpdateGroupMemberRequest{
				Role: "leader",
			}
			w, r := AddBodyToRequest(updateRoleRequest, User1JWT)

			user1Name := "User1"
			user2Name := "User2"
			profile := apipub.UserProfileResponse{Userid: "b287e655461f4b3085c8f244e394ff7e"}
			members := []apipub.GroupMemberResponse{
				{
					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
					Role:   "leader",
					Name:   &user1Name,
					Presence: &apipub.PresenceResponse{
						Status:    "online",
						Timestamp: time.Now().UTC(),
					},
				},
				{
					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
					Role:   "member",
					Name:   &user2Name,
					Presence: &apipub.PresenceResponse{
						Status:    "online",
						Timestamp: time.Now().UTC(),
					},
				},
			}
			group := apipub.GroupResponse{
				Groupid: groupId,
				Members: &members,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.rc.EXPECT().SetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile, nil)
			mock.api.UpdateGroupMember(w, r, apipub.Groupid(groupId), apipub.Userid(userid))
			g.Assert(w.Code).Equal(http.StatusOK)
		})
	})
}

func TestCanDeleteGroup(t *testing.T) {
	g := goblin.Goblin(t)

	productid := utils.GenerateRandomDNAID()
	leaderid := utils.GenerateRandomDNAID()
	memberid := utils.GenerateRandomDNAID()
	groupid1 := utils.GenerateRandomDNAID()
	groupid2 := utils.GenerateRandomDNAID()
	groupNoLeaderId := utils.GenerateRandomDNAID()

	members := &[]apipub.GroupMemberResponse{
		{
			Userid:    leaderid,
			Productid: productid,
			Role:      "leader",
		},
		{
			Userid:    memberid,
			Productid: productid,
			Role:      "member",
		},
	}
	group1 := &apipub.GroupResponse{
		Productid: productid,
		Members:   members,
		Groupid:   groupid1,
	}
	group2 := &apipub.GroupResponse{
		Productid: productid,
		Members:   members,
		Groupid:   groupid2,
	}
	groupNoLeader := &apipub.GroupResponse{
		Productid: productid,
		Groupid:   groupNoLeaderId,
		Members: &[]apipub.GroupMemberResponse{
			{
				Userid:    memberid,
				Productid: productid,
				Role:      "member",
			},
		},
	}

	g.Describe("TestCanDeleteGroup", func() {
		g.Before(func() {
			err := pubApi.Cache.SetGroup(ctx, group1, time.Duration(pubApi.Cfg.TtlDefault)*time.Second)
			print(err)
			pubApi.Cache.SetGroup(ctx, group2, time.Duration(pubApi.Cfg.TtlDefault)*time.Second)
			pubApi.Cache.SetGroup(ctx, groupNoLeader, time.Duration(pubApi.Cfg.TtlDefault)*time.Second)
			pubApi.Cache.SetGroup(ctx, group1, time.Duration(pubApi.Cfg.TtlDefault)*time.Second)
			pubApi.Cache.SetUserGroupIdxs(ctx, leaderid, group1)
			pubApi.Cache.SetUserGroupIdxs(ctx, leaderid, group2)
			pubApi.Cache.SetUserGroupIdxs(ctx, memberid, group1)
			pubApi.Cache.SetUserGroupIdxs(ctx, memberid, group2)

		})
		g.It("leader can delete an owned group", func() {
			err := pubApi.CanDelete(ctx, group1, leaderid, productid)
			g.Assert(err).IsNil()
		})
		g.It("member cannot delete an owned group", func() {

			err := pubApi.CanDelete(ctx, group1, memberid, productid)
			g.Assert(err).IsNotNil()
		})
		g.It("anyone can delete a no-leader group", func() {
			err := pubApi.CanDelete(ctx, groupNoLeader, memberid, productid)
			g.Assert(err).IsNil()

			err = pubApi.CanDelete(ctx, groupNoLeader, leaderid, productid)
			g.Assert(err).IsNil()

		})

	})
}

func TestSendControlMessage(t *testing.T) {
	g := goblin.Goblin(t)
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	// create mock data store
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	groupId := "01FN83SSZZ26H316YA4HQ6KX88"
	productID := "4029a6ffe9924f969955aa2e1c0782aa"

	user1Name := "User1"
	user2Name := "User2"
	members := []apipub.GroupMemberResponse{
		{
			Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
			Role:   "leader",
			Name:   &user1Name,
			Presence: &apipub.PresenceResponse{
				Status:    "online",
				Timestamp: time.Now().UTC(),
			},
		},
		{
			Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
			Role:   "member",
			Name:   &user2Name,
			Presence: &apipub.PresenceResponse{
				Status:    "online",
				Timestamp: time.Now().UTC(),
			},
		},
	}
	group := apipub.GroupResponse{
		Groupid:   groupId,
		Productid: productID,
		Members:   &members,
	}

	controlMessageOk := apipub.SendControlMessageRequestBody{
		Payload: "payload",

		Event: aws.String("event"),
	}

	//6000 chars
	longPayload := "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890"

	controlMessageLong := apipub.SendControlMessageRequestBody{
		Payload: longPayload,

		Event: aws.String("event"),
	}

	g.Describe("TestSendControlMessage", func() {

		g.It("1. FAIL - failed to get group, group is nil", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			w, r := AddBodyToRequest(controlMessageOk, User1JWT)
			mock.api.SendControlMessage(w, r, apipub.Groupid(groupId))
			g.Assert(w.Code).Equal(http.StatusNotFound)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusNotFound, errs.EGroupsNotFound).Error()))
		})

		g.It("2. FAIL - failed to get group, got the error", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
			w, r := AddBodyToRequest(controlMessageOk, User1JWT)
			mock.api.SendControlMessage(w, r, apipub.Groupid(groupId))
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed).Error()))
		})

		g.It("3. FAIL - no body is provided", func() {
			w, r := Login(User1JWT)
			mock.api.SendControlMessage(w, r, apipub.Groupid(groupId))
			g.Assert(w.Code).Equal(http.StatusBadRequest)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.ERequestEmpty).Error()))
		})

		g.It("4. FAIL - message too long", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			w, r := AddBodyToRequest(controlMessageLong, User1JWT)
			mock.api.SendControlMessage(w, r, apipub.Groupid(groupId))
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsControlMessageTooLong).Error()))
		})

		g.It("5. FAIL - user not in group", func() {
			group2 := apipub.GroupResponse{
				Groupid:   groupId,
				Productid: productID,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group2, nil)
			w, r := AddBodyToRequest(controlMessageLong, User1JWT)
			mock.api.SendControlMessage(w, r, apipub.Groupid(groupId))
			g.Assert(w.Code).Equal(http.StatusForbidden)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsMemberNotInGroup).Error()))
		})

		g.It("6. OK - sends message", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
			mock.tele.EXPECT().SendGroupEvent(gomock.Any(), gomock.Any()).Return(nil)
			w, r := AddBodyToRequest(controlMessageOk, User1JWT)
			mock.api.SendControlMessage(w, r, apipub.Groupid(groupId))
			g.Assert(w.Code).Equal(http.StatusOK)
		})
	})
}

func TestUpdateGroupMemberMeta(t *testing.T) {
	g := goblin.Goblin(t)
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	// create mock data store
	mock := NewMockAPI(t)
	rc := cache.NewRedisCache(ctx, cfg, nil, id)
	defer mock.ctrl.Finish()

	groupId := "01FN83SSZZ26H316YA4HQ6KX88"
	user1Id := "b287e655461f4b3085c8f244e394ff7e"
	user2Id := "e12c3df480984141b2f385646b2024fa"
	member1 := apipub.GroupMemberResponse{
		Userid: user1Id,
		Role:   "leader",
		Name:   aws.String("UserName1"),
		Presence: &apipub.PresenceResponse{
			Status:    "online",
			Timestamp: time.Now().UTC(),
		},
	}

	member2 := apipub.GroupMemberResponse{
		Userid: user2Id,
		Role:   "member",
		Name:   aws.String("UserName2"),
		Presence: &apipub.PresenceResponse{
			Status:    "online",
			Timestamp: time.Now().UTC(),
		},
	}

	group := apipub.GroupResponse{
		Groupid:    groupId,
		Productid:  "4029a6ffe9924f969955aa2e1c0782aa",
		MaxMembers: 5,
		Members:    &[]apipub.GroupMemberResponse{member1, member2},
	}

	validMetadata := map[string]interface{}{
		"test":      "pending",
		"something": "nothing",
	}

	// emptyMetadata := map[string]interface{}{}

	updateValidMetadata := map[string]interface{}{
		"test": "done",
		"foo":  "foo",
	}

	g.Describe("TestUpdateGroupMemberMeta", func() {
		g.BeforeEach(func() {
			member1 = apipub.GroupMemberResponse{
				Userid: user1Id,
				Role:   "leader",
				Name:   aws.String("UserName1"),
				Presence: &apipub.PresenceResponse{
					Status:    "online",
					Timestamp: time.Now().UTC(),
				},
			}

			member2 = apipub.GroupMemberResponse{
				Userid: user2Id,
				Role:   "member",
				Name:   aws.String("UserName2"),
				Presence: &apipub.PresenceResponse{
					Status:    "online",
					Timestamp: time.Now().UTC(),
				},
			}
		})

		g.It("1. OK - add a member's meta", func() {
			now := time.Now().UnixMilli()
			member1.Meta = &validMetadata
			member1.MetaLastUpdated = aws.Uint64(uint64(now))

			rc.SetGroup(context.Background(), &group, time.Duration(1)*time.Minute)
			err := rc.UpdateGroupMember(context.Background(), &group, &member1)
			g.Assert(err).IsNil()

			group, err := rc.GetGroup(context.Background(), group.Productid, groupId)
			g.Assert(err).IsNil()
			g.Assert(group).IsNotNil()
			g.Assert(len(*group.Members)).Equal(2)

			meta := (*(*group.Members)[0].Meta)
			g.Assert(meta["test"]).Equal("pending")
			g.Assert(meta["something"]).Equal("nothing")
		})
		g.It("2. OK - update a member's meta", func() {
			now := time.Now().UnixMilli()
			member1.Meta = &validMetadata
			member1.MetaLastUpdated = aws.Uint64(uint64(now))

			rc.SetGroup(context.Background(), &group, time.Duration(1)*time.Minute)
			err := rc.UpdateGroupMember(context.Background(), &group, &member1)
			g.Assert(err).IsNil()

			now = time.Now().UnixMilli()
			member1.Meta = &updateValidMetadata
			member1.MetaLastUpdated = aws.Uint64(uint64(now))
			err = rc.UpdateGroupMember(context.Background(), &group, &member1)
			g.Assert(err).IsNil()

			group, err := rc.GetGroup(context.Background(), group.Productid, groupId)
			g.Assert(err).IsNil()
			g.Assert(group).IsNotNil()
			g.Assert(len(*group.Members)).Equal(2)

			meta := (*(*group.Members)[0].Meta)
			g.Assert(meta["test"]).Equal("done")
			g.Assert(meta["foo"]).Equal("foo")
		})
	})
}
