name: Create Release Tag and Develop Mergeback

# This action runs when release candidate branch is merged to master
# https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#running-your-workflow-when-a-pull-request-merges-1
on:
  pull_request_target:
    types:
      - closed
    branches:
      - master

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  push-release-tag:
    if: github.event.pull_request.merged == true
    name: Create Release Tag
    runs-on: [t2gp-arc-linux]
    steps:
      - name: Get Release Tag from Merged Branch
        run: |
          echo RC_TAG=${GITHUB_HEAD_REF##*/} >> $GITHUB_ENV
      - name: Create Release Tag
        uses: mathieudutour/github-tag-action@v6.1
        with:
          github_token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          custom_tag: ${{env.RC_TAG}}
          tag_prefix: '' # prevent double 'v'
  merge-back-develop:
    if: github.event.pull_request.merged == true
    needs: [push-release-tag]
    name: Merge back to develop
    runs-on: [t2gp-arc-linux]
    steps:
      - uses: actions/checkout@v3
      - name: Open PR to merge-back to develop
        uses: peter-evans/create-pull-request@v4
        with:
          token: '${{ secrets.SERVICE_ACCOUNT_GH_PAT }}'
          base: develop
          commit-message: Mergeback PR
          branch: m-to-d
          delete-branch: true
