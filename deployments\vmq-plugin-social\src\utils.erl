-module(utils).

-export([
  concat/2,
  get_env/0,
  get_cluster_env/0,
  is_jwt/1,
  log_info_lowload/1
]).

%% EXTERNAL

concat(Words, string) ->
    internal_concat(Words);
concat(Words, binary) ->
    list_to_binary(internal_concat(Words)).

-spec get_env() -> binary().
get_env() -> 
    list_to_binary(os:getenv("DD_ENV", "local")).

-spec get_cluster_env() -> binary().
get_cluster_env() ->
	Str = get_env(),
  Prod = "production",
	if
		Str == <<"production">> ->
			Prod;
		Str == <<"staging">> ->
			Prod;
		Str == <<"cert">> ->
			Prod;
		true ->
			"develop"
	end.

-spec is_jwt(_Str) -> atom().
is_jwt(Str) ->
	M = re:run(Str, "^ey"),
	if 
		M == {match,[{0,2}]} ->
			true;
		M == nomatch ->
			false
	end.

-spec log_info_lowload(_Str) -> none().
log_info_lowload(Str) ->
  Env = get_env(),
  if
    Env == <<"production">> ->
      ok;
    Env == <<"loadtesting">> ->
      ok;
    true ->
      lager:info(Str)
  end.

%% INTERNAL

internal_concat(Elements) ->
    NonBinaryElements = [case Element of _ when is_binary(Element) -> binary_to_list(Element); _ -> Element end || Element <- Elements],
    lists:concat(NonBinaryElements).
