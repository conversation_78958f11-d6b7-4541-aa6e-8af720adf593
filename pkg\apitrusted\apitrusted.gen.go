// Package apitrusted provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.3.0 DO NOT EDIT.
package apitrusted

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for AccountLinkDNALinkType.
const (
	Epic     AccountLinkDNALinkType = "epic"
	Nintendo AccountLinkDNALinkType = "nintendo"
	Parent   AccountLinkDNALinkType = "parent"
	Psn      AccountLinkDNALinkType = "psn"
	Steam    AccountLinkDNALinkType = "steam"
	Xbl      AccountLinkDNALinkType = "xbl"
)

// Defines values for AccountTypeDNA.
const (
	AccountTypeDNAANONYMOUS                 AccountTypeDNA = 1
	AccountTypeDNAFULL                      AccountTypeDNA = 3
	AccountTypeDNANEWSLETTER                AccountTypeDNA = 5
	AccountTypeDNAPLATFORM                  AccountTypeDNA = 2
	AccountTypeDNAPRIVACYPOLICYACCEPTEDONLY AccountTypeDNA = 7
	AccountTypeDNATOOLS                     AccountTypeDNA = 4
	AccountTypeDNAUNDISCLOSED               AccountTypeDNA = 6
	AccountTypeDNAUNKNOWN                   AccountTypeDNA = 0
)

// Defines values for DiscoveryURLResponseType.
const (
	DiscoveryURLResponseTypeHttp    DiscoveryURLResponseType = "http"
	DiscoveryURLResponseTypeMqtt    DiscoveryURLResponseType = "mqtt"
	DiscoveryURLResponseTypeTrusted DiscoveryURLResponseType = "trusted"
)

// Defines values for GroupMemberRole.
const (
	Leader    GroupMemberRole = "leader"
	Member    GroupMemberRole = "member"
	Nonmember GroupMemberRole = "nonmember"
)

// Defines values for JoinRequestAction.
const (
	AutoApprove JoinRequestAction = "auto-approve"
	AutoReject  JoinRequestAction = "auto-reject"
	Manual      JoinRequestAction = "manual"
)

// Defines values for MembershipStatus.
const (
	Accepted  MembershipStatus = "accepted"
	Approved  MembershipStatus = "approved"
	Declined  MembershipStatus = "declined"
	Invited   MembershipStatus = "invited"
	Rejected  MembershipStatus = "rejected"
	Requested MembershipStatus = "requested"
	Revoked   MembershipStatus = "revoked"
)

// Defines values for OnlineServiceType.
const (
	OnlineServiceTypeAPPLE                    OnlineServiceType = 22
	OnlineServiceTypeCALICO                   OnlineServiceType = 10
	OnlineServiceTypeDEVICE                   OnlineServiceType = 21
	OnlineServiceTypeEPIC                     OnlineServiceType = 15
	OnlineServiceTypeFACEBOOK                 OnlineServiceType = 17
	OnlineServiceTypeGAMECENTER               OnlineServiceType = 12
	OnlineServiceTypeGOOGLE                   OnlineServiceType = 18
	OnlineServiceTypeGOOGLEPLAY               OnlineServiceType = 6
	OnlineServiceTypeLEGACYGAMECENTER         OnlineServiceType = 5
	OnlineServiceTypeNINTENDO                 OnlineServiceType = 11
	OnlineServiceTypeSONYENTERTAINMENTNETWORK OnlineServiceType = 2
	OnlineServiceTypeSTADIA                   OnlineServiceType = 16
	OnlineServiceTypeSTEAM                    OnlineServiceType = 3
	OnlineServiceTypeT2GP                     OnlineServiceType = 24
	OnlineServiceTypeTWITCH                   OnlineServiceType = 20
	OnlineServiceTypeTWITTER                  OnlineServiceType = 19
	OnlineServiceTypeUNKNOWN                  OnlineServiceType = 0
	OnlineServiceTypeVORTEX                   OnlineServiceType = 14
	OnlineServiceTypeWEB                      OnlineServiceType = 4
	OnlineServiceTypeWEGAME                   OnlineServiceType = 13
	OnlineServiceTypeWINDOWSDEVELOPER         OnlineServiceType = 99
	OnlineServiceTypeWINDOWSPHONE             OnlineServiceType = 9
	OnlineServiceTypeXBOXLIVE                 OnlineServiceType = 1
	OnlineServiceTypeZENDESK                  OnlineServiceType = 23
)

// Defines values for PresenceStatus.
const (
	Authenticating PresenceStatus = "authenticating"
	Away           PresenceStatus = "away"
	Chat           PresenceStatus = "chat"
	Custom         PresenceStatus = "custom"
	Dnd            PresenceStatus = "dnd"
	Offline        PresenceStatus = "offline"
	Online         PresenceStatus = "online"
	Playing        PresenceStatus = "playing"
)

// Defines values for ServerDiscoveryURLRequestType.
const (
	ServerDiscoveryURLRequestTypeHttp    ServerDiscoveryURLRequestType = "http"
	ServerDiscoveryURLRequestTypeMqtt    ServerDiscoveryURLRequestType = "mqtt"
	ServerDiscoveryURLRequestTypeTrusted ServerDiscoveryURLRequestType = "trusted"
)

// AccountLinkDNA First party DNA account links.  This will be filtered by the current user's OST.
type AccountLinkDNA struct {
	// AccountId an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	AccountId *Dnaid `json:"accountId,omitempty"`

	// AccountType The type of Account according to DNA.  Notable is 3 for full.
	AccountType *AccountTypeDNA `json:"accountType,omitempty"`

	// FirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
	FirstPartyid *FirstPartyid           `json:"firstPartyid,omitempty"`
	LinkType     *AccountLinkDNALinkType `json:"linkType,omitempty"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType,omitempty"`
}

// AccountLinkDNALinkType defines model for AccountLinkDNA.LinkType.
type AccountLinkDNALinkType string

// AccountTypeDNA The type of Account according to DNA.  Notable is 3 for full.
type AccountTypeDNA uint32

// ActiveGroupResponse defines model for activeGroupResponse.
type ActiveGroupResponse struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// CanRequestJoin in v2, this boolean is basically just a check on if the group is full.  it used to check group join request action but now any group can be joined using a password even as long as it's not full.
	CanRequestJoin bool `json:"canRequestJoin"`

	// CurrentMemberCount count of current members of the group
	CurrentMemberCount int `json:"currentMemberCount"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers MaxMembers `json:"maxMembers"`
}

// CanCrossPlay Does the user sending this have cross play enabled on their local system
type CanCrossPlay = bool

// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
type CanMembersInvite = bool

// Clientid client id of mqtt connection for mqtt presence
type Clientid = string

// Created timestamp that this record was created.
type Created = time.Time

// CustomStatus defines model for customStatus.
type CustomStatus = string

// DiscoveryListResponse defines model for discoveryListResponse.
type DiscoveryListResponse = []DiscoveryResponse

// DiscoveryResponse defines model for discoveryResponse.
type DiscoveryResponse struct {
	// CanList if true, this discovery will be returned in the discovery list response
	CanList     *bool  `json:"canList"`
	Description string `json:"description"`

	// Id must be unique.  can be any string identifier. env/guid/etc.
	Id   string                 `json:"id"`
	Urls []DiscoveryURLResponse `json:"urls"`
}

// DiscoveryURLResponse defines model for discoveryURLResponse.
type DiscoveryURLResponse struct {
	// Fragment optional piece of uri
	Fragment *string `json:"fragment"`

	// Host optional piece of uri
	Host *string `json:"host"`

	// Path optional piece of uri
	Path *string `json:"path"`

	// Port optional piece of uri
	Port *string `json:"port"`

	// Query optional piece of uri
	Query *string `json:"query"`

	// Scheme optional piece of uri
	Scheme *string                  `json:"scheme"`
	Type   DiscoveryURLResponseType `json:"type"`

	// Url string url with port included with domain if needed
	Url string `json:"url"`
}

// DiscoveryURLResponseType defines model for DiscoveryURLResponse.Type.
type DiscoveryURLResponseType string

// DnaDisplayName 2k display name with optional 5 digit discrimnating hash
type DnaDisplayName = string

// Dnaid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type Dnaid = string

// EmptyObject Empty object
type EmptyObject = map[string]interface{}

// EndorsementName the name of the endorsement to be acted upon
type EndorsementName = string

// Error defines model for error.
type Error struct {
	// Code HTTP error code
	Code uint32 `json:"code"`

	// ErrorCode error code.  list of errors on docsite.
	ErrorCode ErrorCode `json:"errorCode"`

	// Message error message
	Message string `json:"message"`

	// Stack Stack trace of the error (will be only returned in dev environment)
	Stack *string `json:"stack,omitempty"`
}

// ErrorCode error code.  list of errors on docsite.
type ErrorCode = uint32

// FirstPartySessionid The First Party Session Id of the specified.
type FirstPartySessionid = string

// FirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
type FirstPartyid = string

// GameData free form field for games to send additional presence information for their internal use.
type GameData = string

// GameName pre-localized game name.
type GameName = string

// GroupMemberResponse defines model for groupMemberResponse.
type GroupMemberResponse struct {
	Links *Links `json:"links"`
	Meta  *Meta  `json:"meta"`

	// MetaLastUpdated Represents the time when an update request is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
	MetaLastUpdated *uint64           `json:"metaLastUpdated"`
	Name            *Name             `json:"name"`
	Presence        *PresenceResponse `json:"presence"`

	// Productid the id of the product. validates as a dnaid.
	Productid Productid `json:"productid"`

	// Role the role of a group member
	Role GroupMemberRole `json:"role"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`
}

// GroupMemberRole the role of a group member
type GroupMemberRole string

// GroupMembershipErrorResponse defines model for groupMembershipErrorResponse.
type GroupMembershipErrorResponse struct {
	Error Error `json:"error"`

	// Memberid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
	Memberid Userid `json:"memberid"`
}

// GroupResponse Productid only set as required due to codegen issues.
type GroupResponse struct {
	CanCrossPlay     *CanCrossPlay     `json:"canCrossPlay"`
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite"`
	Created          *Created          `json:"created"`

	// FirstPartySessionid The First Party Session Id of the specified.
	FirstPartySessionid FirstPartySessionid `json:"firstPartySessionid"`

	// GroupCompositionId incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
	GroupCompositionId *int64 `json:"groupCompositionId"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers         MaxMembers             `json:"maxMembers"`
	Members            *[]GroupMemberResponse `json:"members,omitempty"`
	MembershipRequests *[]MembershipRequest   `json:"membershipRequests"`

	// Meta free form map (json format) to store metadata for this object.
	Meta              *Meta              `json:"meta"`
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType"`
	Password          *Password          `json:"password"`

	// Productid the id of the product. validates as a dnaid.
	Productid Productid `json:"productid"`
}

// GroupWithErrorsResponse ProductID only set as required due to codegen issues.
type GroupWithErrorsResponse struct {
	CanCrossPlay     *CanCrossPlay                   `json:"canCrossPlay"`
	CanMembersInvite *CanMembersInvite               `json:"canMembersInvite"`
	Created          *Created                        `json:"created"`
	Errors           *[]GroupMembershipErrorResponse `json:"errors"`

	// GroupCompositionId incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
	GroupCompositionId *int64 `json:"groupCompositionId"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers         MaxMembers             `json:"maxMembers"`
	Members            *[]GroupMemberResponse `json:"members"`
	MembershipRequests *[]MembershipRequest   `json:"membershipRequests"`
	Meta               *Meta                  `json:"meta"`

	// OnlineServiceType the group creator's online service type. used when xplay membership requests are validated.
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType"`
	Password          *Password          `json:"password"`

	// Productid the id of the product. validates as a dnaid.
	Productid Productid `json:"productid"`
	Ttl       *Ttl      `json:"ttl"`
}

// Groupid lexigraphicaly sorted unique identifier
type Groupid = Ulid

// HealthResponse defines model for healthResponse.
type HealthResponse struct {
	Generated     *string                 `json:"generated"`
	Name          *string                 `json:"name"`
	OverallStatus *string                 `json:"overall-status"`
	Services      *map[string]interface{} `json:"services"`
	Version       *string                 `json:"version"`
}

// InviteGroupMember defines model for inviteGroupMember.
type InviteGroupMember struct {
	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// Memberid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
	Memberid Userid `json:"memberid"`
}

// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
type IsFirstPartyInvite = bool

// JoinContext Context used to join a game session
type JoinContext struct {
	LaunchGameArgs string `json:"launchGameArgs"`
	Sessionid      string `json:"sessionid"`
}

// JoinGroupMember defines model for joinGroupMember.
type JoinGroupMember struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// Memberid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Memberid Dnaid `json:"memberid"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`
}

// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
type JoinRequestAction string

// Links Linked accounts. Filtered to current OST.
type Links = []AccountLinkDNA

// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
type MaxMembers = int

// MembershipRequest this schema defines a membership request.  which can be either a join request or an invite using the status field as a determiner.
type MembershipRequest struct {
	// Approverid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Approverid Dnaid `json:"approverid"`

	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay *CanCrossPlay `json:"canCrossPlay,omitempty"`

	// FirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
	FirstPartyid *FirstPartyid `json:"firstPartyid,omitempty"`

	// FromDisplayName the display name of the user that this request is from.  provided to display in the UI of the invite.
	FromDisplayName *DnaDisplayName `json:"fromDisplayName,omitempty"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// Memberid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Memberid Dnaid `json:"memberid"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType,omitempty"`

	// Productid the id of the product. validates as a dnaid.
	Productid *Productid `json:"productid,omitempty"`

	// Status The membership status of the invite or request to join state. * requested - a request to join flow initiated * approved - group join request has been approved * rejected - group join request has been rejected * invited - the user has been invited to the group * accepted - the user has accepted the invite to the group * declined - the invite has been declined * revoked - the invite has been revoked
	Status MembershipStatus `json:"status"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`
}

// MembershipStatus The membership status of the invite or request to join state. * requested - a request to join flow initiated * approved - group join request has been approved * rejected - group join request has been rejected * invited - the user has been invited to the group * accepted - the user has accepted the invite to the group * declined - the invite has been declined * revoked - the invite has been revoked
type MembershipStatus string

// Meta free form map (json format) to store metadata for this object.
type Meta = map[string]interface{}

// Name The name associated with this user. If friend name is empty string, it is possible that it is not a full 2K account.
type Name = string

// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
type OnlineServiceType int

// Password password for the join request
type Password = string

// PresenceResponse a presence record
type PresenceResponse struct {
	ActiveGroup *ActiveGroupResponse `json:"activeGroup"`

	// ActiveSessionid id of active login session.
	ActiveSessionid Dnaid         `json:"activeSessionid"`
	Clientid        *Clientid     `json:"clientid"`
	CustomStatus    *CustomStatus `json:"customStatus"`
	GameData        *GameData     `json:"gameData"`

	// GameName pre-localized game name.
	GameName    GameName     `json:"gameName"`
	JoinContext *JoinContext `json:"joinContext"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// Platformid DNA only. at the time the presence is set, if the token has a pai claim.  this will be the sub claim, which should be the platformid.  we do not guarantee this value will be returned since full account tokens will not have it.
	Platformid *Dnaid `json:"platformid"`

	// Priority Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
	Priority Priority `json:"priority"`

	// Productid the id of the product. validates as a dnaid.
	Productid    Productid     `json:"productid"`
	RichPresence *RichPresence `json:"richPresence"`

	// Status the status of the presence record for the user
	Status PresenceStatus `json:"status"`

	// Timestamp timstamp of the event
	Timestamp Timestamp `json:"timestamp"`

	// Ttl How long in seconds before this presence will be considered offline if no presence Heartbeat is made.   | This is an optional value for those who are not using our MQTT.  People using our MQTT will have this functionality via our mqtt plugin.  Timeout set to 5 minutes for auto drop from group.
	Ttl *Ttl `json:"ttl"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`
}

// PresenceStatus the status of the presence record for the user
type PresenceStatus string

// Priority Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
type Priority = int

// Productid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type Productid = Dnaid

// RichPresence string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
type RichPresence = string

// ServerDiscoveryURLRequest url for trusted api discovery request schema
type ServerDiscoveryURLRequest struct {
	// Fragment optional piece of uri
	Fragment *string `json:"fragment,omitempty"`

	// Host optional piece of uri
	Host *string `json:"host,omitempty"`

	// Path optional piece of uri
	Path *string `json:"path,omitempty"`

	// Port optional piece of uri
	Port *string `json:"port,omitempty"`

	// Query optional piece of uri
	Query *string `json:"query,omitempty"`

	// Scheme optional piece of uri
	Scheme *string                       `json:"scheme,omitempty"`
	Type   ServerDiscoveryURLRequestType `json:"type"`

	// Url string url with port included with domain if needed
	Url string `json:"url"`
}

// ServerDiscoveryURLRequestType defines model for ServerDiscoveryURLRequest.Type.
type ServerDiscoveryURLRequestType string

// ServerLoginResponse Successful server authentication response
type ServerLoginResponse struct {
	// AccessToken The access token
	AccessToken string `json:"accessToken"`

	// AccessTokenExpiresIn The number of seconds until the access token expires
	AccessTokenExpiresIn uint32 `json:"accessTokenExpiresIn"`

	// InstanceId The instance id used to authenticate
	InstanceId string `json:"instanceId"`

	// RefreshToken The refresh token
	RefreshToken string `json:"refreshToken"`

	// RefreshTokenExpiresIn The number of seconds until the refresh token expires
	RefreshTokenExpiresIn uint32 `json:"refreshTokenExpiresIn"`
}

// ServerReturnMembershipErrors Should this trusted API call return errors that happened when adding users
type ServerReturnMembershipErrors = bool

// TelemetryMetaData Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
type TelemetryMetaData = map[string]interface{}

// Timestamp timstamp of the event
type Timestamp = time.Time

// Ttl time in seconds until the object expires
type Ttl = int64

// Ulid lexigraphicaly sorted unique identifier
type Ulid = string

// Userid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
type Userid = string

// VersionResponse defines model for versionResponse.
type VersionResponse struct {
	BuildDate string `json:"buildDate"`
	GitHash   string `json:"gitHash"`
	Version   string `json:"version"`
}

// DiscoveryPid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type DiscoveryPid = Dnaid

// Discoveryid The id of the discovery set desired.  '?id=' will return all sets for given product.
type Discoveryid = string

// Healthid The id of the identity provider desired. No parameter will return health check without identity info.
type Healthid = string

// PEndorsementName the name of the endorsement to be acted upon
type PEndorsementName = EndorsementName

// PGroupid the id of the group.  validates ULID pattern.
type PGroupid = Groupid

// PMemberid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
type PMemberid = Userid

// PUserid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
type PUserid = Userid

// N200empty Empty object
type N200empty = EmptyObject

// N400 defines model for 400.
type N400 = Error

// N401 defines model for 401.
type N401 = Error

// N403 defines model for 403.
type N403 = Error

// N404 defines model for 404.
type N404 = Error

// N429 defines model for 429.
type N429 = Error

// N500 defines model for 500.
type N500 = Error

// ServerCreateGroupRequestBody defines model for serverCreateGroupRequestBody.
type ServerCreateGroupRequestBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay *CanCrossPlay `json:"canCrossPlay,omitempty"`

	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// GroupLeader an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	GroupLeader  Dnaid              `json:"groupLeader"`
	GroupMembers *[]JoinGroupMember `json:"groupMembers,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// Password password for the join request
	Password               *Password `json:"password,omitempty"`
	ReturnMembershipErrors *bool     `json:"returnMembershipErrors,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerJoinRequestRequestBody defines model for serverJoinRequestRequestBody.
type ServerJoinRequestRequestBody struct {
	Members []JoinGroupMember `json:"members"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// ReturnMembershipErrors Should this trusted API call return errors that happened when adding users
	ReturnMembershipErrors *ServerReturnMembershipErrors `json:"returnMembershipErrors,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerRefreshTokenRequestBody defines model for serverRefreshTokenRequestBody.
type ServerRefreshTokenRequestBody struct {
	// InstanceId The instance id used to authenticate
	InstanceId string `json:"instanceId"`

	// RefreshToken Refresh token
	RefreshToken string `json:"refreshToken"`
}

// ServerSendControlMessageRequestBody defines model for serverSendControlMessageRequestBody.
type ServerSendControlMessageRequestBody struct {
	// Event a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
	Event   *string `json:"event,omitempty"`
	Payload string  `json:"payload"`

	// Senderid sender should be a dna userid or a dna server instance id.
	Senderid *Dnaid `json:"senderid,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Timestamp timstamp of the event
	Timestamp *Timestamp `json:"timestamp,omitempty"`
}

// ServerSendInviteRequestBody defines model for serverSendInviteRequestBody.
type ServerSendInviteRequestBody struct {
	// Inviterid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Inviterid Dnaid               `json:"inviterid"`
	Members   []InviteGroupMember `json:"members"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// ReturnMembershipErrors Should this trusted API call return errors that happened when adding users
	ReturnMembershipErrors *ServerReturnMembershipErrors `json:"returnMembershipErrors,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`
}

// ServerUpdateGroupMemberRequestBody defines model for serverUpdateGroupMemberRequestBody.
type ServerUpdateGroupMemberRequestBody struct {
	// Role the role of a group member
	Role *GroupMemberRole `json:"role,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerUpdateGroupRequestBody defines model for serverUpdateGroupRequestBody.
type ServerUpdateGroupRequestBody struct {
	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction *JoinRequestAction `json:"joinRequestAction,omitempty"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerUpsertDiscoveryRequestBody defines model for serverUpsertDiscoveryRequestBody.
type ServerUpsertDiscoveryRequestBody = []struct {
	// CanList if true, this discovery will be returned in the discovery list response
	CanList     *bool  `json:"canList,omitempty"`
	Description string `json:"description"`

	// Id must be unique.  can be any string identifier. env/guid/etc.
	Id   string                      `json:"id"`
	Urls []ServerDiscoveryURLRequest `json:"urls"`
}

// ServerRefreshTokenJSONBody defines parameters for ServerRefreshToken.
type ServerRefreshTokenJSONBody struct {
	// InstanceId The instance id used to authenticate
	InstanceId string `json:"instanceId"`

	// RefreshToken Refresh token
	RefreshToken string `json:"refreshToken"`
}

// ServerDeleteDiscoveryParams defines parameters for ServerDeleteDiscovery.
type ServerDeleteDiscoveryParams struct {
	// Discoveryid Get specific discovery endpoint based on id.  Must also send Authorization header
	Discoveryid *Discoveryid `form:"discoveryid,omitempty" json:"discoveryid,omitempty"`

	// DiscoveryPid productid to filter by.
	DiscoveryPid *DiscoveryPid `form:"discoveryPid,omitempty" json:"discoveryPid,omitempty"`
}

// ServerGetDiscoveryParams defines parameters for ServerGetDiscovery.
type ServerGetDiscoveryParams struct {
	// Discoveryid Get specific discovery endpoint based on id.  Must also send Authorization header
	Discoveryid *Discoveryid `form:"discoveryid,omitempty" json:"discoveryid,omitempty"`

	// DiscoveryPid productid to filter by.
	DiscoveryPid *DiscoveryPid `form:"discoveryPid,omitempty" json:"discoveryPid,omitempty"`
}

// ServerUpsertDiscoveryJSONBody defines parameters for ServerUpsertDiscovery.
type ServerUpsertDiscoveryJSONBody = []struct {
	// CanList if true, this discovery will be returned in the discovery list response
	CanList     *bool  `json:"canList,omitempty"`
	Description string `json:"description"`

	// Id must be unique.  can be any string identifier. env/guid/etc.
	Id   string                      `json:"id"`
	Urls []ServerDiscoveryURLRequest `json:"urls"`
}

// ServerCreateGroupJSONBody defines parameters for ServerCreateGroup.
type ServerCreateGroupJSONBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay *CanCrossPlay `json:"canCrossPlay,omitempty"`

	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// GroupLeader an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	GroupLeader  Dnaid              `json:"groupLeader"`
	GroupMembers *[]JoinGroupMember `json:"groupMembers,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// Password password for the join request
	Password               *Password `json:"password,omitempty"`
	ReturnMembershipErrors *bool     `json:"returnMembershipErrors,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerUpdateGroupJSONBody defines parameters for ServerUpdateGroup.
type ServerUpdateGroupJSONBody struct {
	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction *JoinRequestAction `json:"joinRequestAction,omitempty"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerSendControlMessageJSONBody defines parameters for ServerSendControlMessage.
type ServerSendControlMessageJSONBody struct {
	// Event a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
	Event   *string `json:"event,omitempty"`
	Payload string  `json:"payload"`

	// Senderid sender should be a dna userid or a dna server instance id.
	Senderid *Dnaid `json:"senderid,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Timestamp timstamp of the event
	Timestamp *Timestamp `json:"timestamp,omitempty"`
}

// ServerKickMemberFromGroupParams defines parameters for ServerKickMemberFromGroup.
type ServerKickMemberFromGroupParams struct {
	// Reason reason for removing a group member
	Reason *string `form:"reason,omitempty" json:"reason,omitempty"`
}

// ServerUpdateGroupMemberJSONBody defines parameters for ServerUpdateGroupMember.
type ServerUpdateGroupMemberJSONBody struct {
	// Role the role of a group member
	Role *GroupMemberRole `json:"role,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerGetHealthParams defines parameters for ServerGetHealth.
type ServerGetHealthParams struct {
	// Id Get specific identity provider for health check based on id.
	Id *Healthid `form:"id,omitempty" json:"id,omitempty"`
}

// ServerSendInvitesForGroupJSONBody defines parameters for ServerSendInvitesForGroup.
type ServerSendInvitesForGroupJSONBody struct {
	// Inviterid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Inviterid Dnaid               `json:"inviterid"`
	Members   []InviteGroupMember `json:"members"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// ReturnMembershipErrors Should this trusted API call return errors that happened when adding users
	ReturnMembershipErrors *ServerReturnMembershipErrors `json:"returnMembershipErrors,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`
}

// ServerSendJoinRequestsForGroupJSONBody defines parameters for ServerSendJoinRequestsForGroup.
type ServerSendJoinRequestsForGroupJSONBody struct {
	Members []JoinGroupMember `json:"members"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// ReturnMembershipErrors Should this trusted API call return errors that happened when adding users
	ReturnMembershipErrors *ServerReturnMembershipErrors `json:"returnMembershipErrors,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ServerRefreshTokenJSONRequestBody defines body for ServerRefreshToken for application/json ContentType.
type ServerRefreshTokenJSONRequestBody ServerRefreshTokenJSONBody

// ServerUpsertDiscoveryJSONRequestBody defines body for ServerUpsertDiscovery for application/json ContentType.
type ServerUpsertDiscoveryJSONRequestBody = ServerUpsertDiscoveryJSONBody

// ServerCreateGroupJSONRequestBody defines body for ServerCreateGroup for application/json ContentType.
type ServerCreateGroupJSONRequestBody ServerCreateGroupJSONBody

// ServerUpdateGroupJSONRequestBody defines body for ServerUpdateGroup for application/json ContentType.
type ServerUpdateGroupJSONRequestBody ServerUpdateGroupJSONBody

// ServerSendControlMessageJSONRequestBody defines body for ServerSendControlMessage for application/json ContentType.
type ServerSendControlMessageJSONRequestBody ServerSendControlMessageJSONBody

// ServerUpdateGroupMemberJSONRequestBody defines body for ServerUpdateGroupMember for application/json ContentType.
type ServerUpdateGroupMemberJSONRequestBody ServerUpdateGroupMemberJSONBody

// ServerSendInvitesForGroupJSONRequestBody defines body for ServerSendInvitesForGroup for application/json ContentType.
type ServerSendInvitesForGroupJSONRequestBody ServerSendInvitesForGroupJSONBody

// ServerSendJoinRequestsForGroupJSONRequestBody defines body for ServerSendJoinRequestsForGroup for application/json ContentType.
type ServerSendJoinRequestsForGroupJSONRequestBody ServerSendJoinRequestsForGroupJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (POST /server/auth/logout)
	ServerLogout(w http.ResponseWriter, r *http.Request)

	// (POST /server/auth/refresh)
	ServerRefreshToken(w http.ResponseWriter, r *http.Request)

	// (GET /server/auth/token)
	ServerGetToken(w http.ResponseWriter, r *http.Request)
	// Delete discovery entry. Nil or Empty '?id=' will delete all discovery info.
	// (DELETE /server/discovery)
	ServerDeleteDiscovery(w http.ResponseWriter, r *http.Request, params ServerDeleteDiscoveryParams)
	// Find endpoints to use.  Nil discoveryid or Empty '?discoveryid=' will return all discovery. The trusted API Ignores the canList=true flag in the discovery list and will return all values.
	// (GET /server/discovery)
	ServerGetDiscovery(w http.ResponseWriter, r *http.Request, params ServerGetDiscoveryParams)
	// Upsert discovery entries.  It will add any new ids and update existing ids.
	// (PATCH /server/discovery)
	ServerUpsertDiscovery(w http.ResponseWriter, r *http.Request)
	// completely removes an endorsement for user.  as opposed to just reseting the resetable counter.
	// (DELETE /server/endorsements/{pEndorsementName}/users/{pUserid}/remove)
	RemoveEndorsementForUser(w http.ResponseWriter, r *http.Request, pEndorsementName PEndorsementName, pUserid PUserid)
	// reset endorsement for user.  this sets the resetable counter to 0.
	// (DELETE /server/endorsements/{pEndorsementName}/users/{pUserid}/reset)
	ResetEndorsementForUser(w http.ResponseWriter, r *http.Request, pEndorsementName PEndorsementName, pUserid PUserid)
	// Create a Group
	// (POST /server/groups)
	ServerCreateGroup(w http.ResponseWriter, r *http.Request)
	// Disband/Delete a Group
	// (DELETE /server/groups/{pGroupid})
	ServerDeleteGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Get a Group
	// (GET /server/groups/{pGroupid})
	ServerGetGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Update a Group's size or metadata
	// (PATCH /server/groups/{pGroupid})
	ServerUpdateGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Send a control message (json or binary)
	// (POST /server/groups/{pGroupid}/control)
	ServerSendControlMessage(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Kick a member from group
	// (DELETE /server/groups/{pGroupid}/members/{pMemberid})
	ServerKickMemberFromGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid, params ServerKickMemberFromGroupParams)
	// Update group member
	// (PATCH /server/groups/{pGroupid}/members/{pMemberid})
	ServerUpdateGroupMember(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Get the server health status
	// (GET /server/health)
	ServerGetHealth(w http.ResponseWriter, r *http.Request, params ServerGetHealthParams)
	// Send invitation for group impersonating a user
	// (POST /server/memberships/invites/groups/{pGroupid})
	ServerSendInvitesForGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Joins a user to group
	// (POST /server/memberships/requests/groups/{pGroupid})
	ServerSendJoinRequestsForGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Get the server version
	// (GET /server/version)
	ServerGetVersion(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// (POST /server/auth/logout)
func (_ Unimplemented) ServerLogout(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (POST /server/auth/refresh)
func (_ Unimplemented) ServerRefreshToken(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /server/auth/token)
func (_ Unimplemented) ServerGetToken(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Delete discovery entry. Nil or Empty '?id=' will delete all discovery info.
// (DELETE /server/discovery)
func (_ Unimplemented) ServerDeleteDiscovery(w http.ResponseWriter, r *http.Request, params ServerDeleteDiscoveryParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Find endpoints to use.  Nil discoveryid or Empty '?discoveryid=' will return all discovery. The trusted API Ignores the canList=true flag in the discovery list and will return all values.
// (GET /server/discovery)
func (_ Unimplemented) ServerGetDiscovery(w http.ResponseWriter, r *http.Request, params ServerGetDiscoveryParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Upsert discovery entries.  It will add any new ids and update existing ids.
// (PATCH /server/discovery)
func (_ Unimplemented) ServerUpsertDiscovery(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// completely removes an endorsement for user.  as opposed to just reseting the resetable counter.
// (DELETE /server/endorsements/{pEndorsementName}/users/{pUserid}/remove)
func (_ Unimplemented) RemoveEndorsementForUser(w http.ResponseWriter, r *http.Request, pEndorsementName PEndorsementName, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// reset endorsement for user.  this sets the resetable counter to 0.
// (DELETE /server/endorsements/{pEndorsementName}/users/{pUserid}/reset)
func (_ Unimplemented) ResetEndorsementForUser(w http.ResponseWriter, r *http.Request, pEndorsementName PEndorsementName, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Create a Group
// (POST /server/groups)
func (_ Unimplemented) ServerCreateGroup(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Disband/Delete a Group
// (DELETE /server/groups/{pGroupid})
func (_ Unimplemented) ServerDeleteGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get a Group
// (GET /server/groups/{pGroupid})
func (_ Unimplemented) ServerGetGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update a Group's size or metadata
// (PATCH /server/groups/{pGroupid})
func (_ Unimplemented) ServerUpdateGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Send a control message (json or binary)
// (POST /server/groups/{pGroupid}/control)
func (_ Unimplemented) ServerSendControlMessage(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Kick a member from group
// (DELETE /server/groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) ServerKickMemberFromGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid, params ServerKickMemberFromGroupParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update group member
// (PATCH /server/groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) ServerUpdateGroupMember(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get the server health status
// (GET /server/health)
func (_ Unimplemented) ServerGetHealth(w http.ResponseWriter, r *http.Request, params ServerGetHealthParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Send invitation for group impersonating a user
// (POST /server/memberships/invites/groups/{pGroupid})
func (_ Unimplemented) ServerSendInvitesForGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Joins a user to group
// (POST /server/memberships/requests/groups/{pGroupid})
func (_ Unimplemented) ServerSendJoinRequestsForGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get the server version
// (GET /server/version)
func (_ Unimplemented) ServerGetVersion(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// ServerLogout operation middleware
func (siw *ServerInterfaceWrapper) ServerLogout(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerLogout(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerRefreshToken operation middleware
func (siw *ServerInterfaceWrapper) ServerRefreshToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerRefreshToken(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerGetToken operation middleware
func (siw *ServerInterfaceWrapper) ServerGetToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerGetToken(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerDeleteDiscovery operation middleware
func (siw *ServerInterfaceWrapper) ServerDeleteDiscovery(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params ServerDeleteDiscoveryParams

	// ------------- Optional query parameter "discoveryid" -------------

	err = runtime.BindQueryParameter("form", true, false, "discoveryid", r.URL.Query(), &params.Discoveryid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "discoveryid", Err: err})
		return
	}

	// ------------- Optional query parameter "discoveryPid" -------------

	err = runtime.BindQueryParameter("form", true, false, "discoveryPid", r.URL.Query(), &params.DiscoveryPid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "discoveryPid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerDeleteDiscovery(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerGetDiscovery operation middleware
func (siw *ServerInterfaceWrapper) ServerGetDiscovery(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params ServerGetDiscoveryParams

	// ------------- Optional query parameter "discoveryid" -------------

	err = runtime.BindQueryParameter("form", true, false, "discoveryid", r.URL.Query(), &params.Discoveryid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "discoveryid", Err: err})
		return
	}

	// ------------- Optional query parameter "discoveryPid" -------------

	err = runtime.BindQueryParameter("form", true, false, "discoveryPid", r.URL.Query(), &params.DiscoveryPid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "discoveryPid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerGetDiscovery(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerUpsertDiscovery operation middleware
func (siw *ServerInterfaceWrapper) ServerUpsertDiscovery(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerUpsertDiscovery(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// RemoveEndorsementForUser operation middleware
func (siw *ServerInterfaceWrapper) RemoveEndorsementForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pEndorsementName" -------------
	var pEndorsementName PEndorsementName

	err = runtime.BindStyledParameterWithOptions("simple", "pEndorsementName", chi.URLParam(r, "pEndorsementName"), &pEndorsementName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pEndorsementName", Err: err})
		return
	}

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RemoveEndorsementForUser(w, r, pEndorsementName, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ResetEndorsementForUser operation middleware
func (siw *ServerInterfaceWrapper) ResetEndorsementForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pEndorsementName" -------------
	var pEndorsementName PEndorsementName

	err = runtime.BindStyledParameterWithOptions("simple", "pEndorsementName", chi.URLParam(r, "pEndorsementName"), &pEndorsementName, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pEndorsementName", Err: err})
		return
	}

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ResetEndorsementForUser(w, r, pEndorsementName, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerCreateGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerCreateGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerCreateGroup(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerDeleteGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerDeleteGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerDeleteGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerGetGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerGetGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerGetGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerUpdateGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerUpdateGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerUpdateGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerSendControlMessage operation middleware
func (siw *ServerInterfaceWrapper) ServerSendControlMessage(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerSendControlMessage(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerKickMemberFromGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerKickMemberFromGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params ServerKickMemberFromGroupParams

	// ------------- Optional query parameter "reason" -------------

	err = runtime.BindQueryParameter("form", true, false, "reason", r.URL.Query(), &params.Reason)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "reason", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerKickMemberFromGroup(w, r, pGroupid, pMemberid, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerUpdateGroupMember operation middleware
func (siw *ServerInterfaceWrapper) ServerUpdateGroupMember(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerUpdateGroupMember(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerGetHealth operation middleware
func (siw *ServerInterfaceWrapper) ServerGetHealth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params ServerGetHealthParams

	// ------------- Optional query parameter "id" -------------

	err = runtime.BindQueryParameter("form", true, false, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerGetHealth(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerSendInvitesForGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerSendInvitesForGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerSendInvitesForGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerSendJoinRequestsForGroup operation middleware
func (siw *ServerInterfaceWrapper) ServerSendJoinRequestsForGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerSendJoinRequestsForGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ServerGetVersion operation middleware
func (siw *ServerInterfaceWrapper) ServerGetVersion(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ServerGetVersion(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/server/auth/logout", wrapper.ServerLogout)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/server/auth/refresh", wrapper.ServerRefreshToken)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/server/auth/token", wrapper.ServerGetToken)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/server/discovery", wrapper.ServerDeleteDiscovery)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/server/discovery", wrapper.ServerGetDiscovery)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/server/discovery", wrapper.ServerUpsertDiscovery)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/server/endorsements/{pEndorsementName}/users/{pUserid}/remove", wrapper.RemoveEndorsementForUser)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/server/endorsements/{pEndorsementName}/users/{pUserid}/reset", wrapper.ResetEndorsementForUser)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/server/groups", wrapper.ServerCreateGroup)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/server/groups/{pGroupid}", wrapper.ServerDeleteGroup)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/server/groups/{pGroupid}", wrapper.ServerGetGroup)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/server/groups/{pGroupid}", wrapper.ServerUpdateGroup)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/server/groups/{pGroupid}/control", wrapper.ServerSendControlMessage)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/server/groups/{pGroupid}/members/{pMemberid}", wrapper.ServerKickMemberFromGroup)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/server/groups/{pGroupid}/members/{pMemberid}", wrapper.ServerUpdateGroupMember)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/server/health", wrapper.ServerGetHealth)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/server/memberships/invites/groups/{pGroupid}", wrapper.ServerSendInvitesForGroup)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/server/memberships/requests/groups/{pGroupid}", wrapper.ServerSendJoinRequestsForGroup)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/server/version", wrapper.ServerGetVersion)
	})

	return r
}
