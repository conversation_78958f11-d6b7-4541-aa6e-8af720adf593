import { setContext } from 'svelte';
import { CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT } from '../constant';
import type {
  FriendsService,
  SocialServices,
  TransportService,
} from '../services';
import type { UserService } from '../services/user';

export type SocialServicesContext = SocialServices<
  TransportService,
  FriendsService,
  UserService
>;

export const initSocialServicesContext: (
  context: SocialServicesContext
) => void = (context: SocialServicesContext) => {
  setSocialServicesContext(context);
};

export const setSocialServicesContext: (
  context: SocialServicesContext
) => void = (context: SocialServicesContext) => {
  return setContext<SocialServicesContext>(
    CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT,
    context
  );
};
