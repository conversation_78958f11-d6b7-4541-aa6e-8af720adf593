checks = [
    "all", 
    "-ST1000", # golang mock depreciated.  go.uber mockgen does generate package comments correctly
    "-SA1019", # go.uber mockgen uses depreciated AccountsMeResponse.  can possibly remove later if they update
    "-SA1029", # Inappropriate key in call to context.WithValue (disabled because oapi-codegen doesn't support this)
    "-ST1003", # Poorly chosen identifier (e.g. userId -> userID)
]
