import { render } from '@testing-library/svelte';
import { MOCKED_FRIENDS } from '../../../services/__mocks__';
import AvatarMock from '../../Avatar/__mock__/Avatar.svelte';
import FriendCard from '../FriendCard.svelte';

jest.mock('../../Avatar', () => ({
  Avatar: AvatarMock,
}));

describe('FriendCard', () => {
  it('should render UI', () => {
    const friend = MOCKED_FRIENDS[0];
    const { getByText } = render(FriendCard, {
      props: {
        friend,
      },
    });

    expect(getByText(friend.name)).not.toBeNull();
  });
});
