name: Release Branch

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      tag:
        description: Tag to create a release on
        required: true

jobs:
  tagged-release:
    name: "Tagged Release"
    runs-on: "ubuntu-latest"

    steps:
      - name: Set Title
        run: |
          echo TITLE=${GITHUB_REF#refs/*/}-${GITHUB_SHA::8} >> $GITHUB_ENV
      - uses: "marvinpinto/action-automatic-releases@latest"
        with:
          title: ${{ env.TITLE }}
          repo_token:  ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          prerelease: false
          automatic_release_tag: ${{ github.event.inputs.mode }}
