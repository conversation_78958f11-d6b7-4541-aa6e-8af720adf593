-module(t2gp_social_pd_SUITE).

-compile(nowarn_export_all).
-compile(export_all).

-include_lib("eunit/include/eunit.hrl").

%% before running rebar3, run
%% export $(cat .env | grep -v '#' | xargs)
init_per_suite(Config) ->
    cover:start(),
    Config.

end_per_suite(_Config) ->
    ok.

init_per_testcase(_Case, Config) ->
    t2gp_social_test:configure(),
    ssl:start(),
    application:set_env(t2gp_social, pd_private_url, "https://pd-backoffice.d2dragon.net"),
    t2gp_social_pd:start_link(),
    Config.

end_per_testcase(_Case, _Config) ->
    ok.

all() ->
    [
        test_gen_server,
        test_validate_jwt,
        test_get_key_from_jwks,
        test_clear_jwks,
        test_invalid_call,
        test_terminate,
        test_code_change,
        test_handle_cast,
        test_handle_info
    ].

test_gen_server(_) ->
    {error, {already_started, PID1}} = t2gp_social_pd:start_link(),
    ok = gen_server:stop(PID1),
    {ok, PID2} = t2gp_social_pd:start_link(),
    ok = gen_server:stop(PID2),
    application:set_env(t2gp_social, pd_private_url, "bad url"),
    {ok, PID3} = t2gp_social_pd:start_link(),
    {state, "bad url", undefined} = sys:get_state(t2gp_social_pd),
    % check bad validation
    AlgNoneJWT =
        <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.">>,
    {error, _} = gen_server:call(t2gp_social_pd, {validate_jwt, AlgNoneJWT}),
    ok = gen_server:stop(PID3),
    application:unset_env(t2gp_social, pd_private_url),
    {ok, PID4} = t2gp_social_pd:start_link(),
    {state, "https://pd-backoffice.d2dragon.net", undefined} = sys:get_state(t2gp_social_pd),
    ok = gen_server:stop(PID4),
    ok.

test_validate_jwt(_) ->
    User = <<"<EMAIL>">>,
    Pass = <<"gKDET*PB6eLT$C$C4ZyGV$f9q">>,
    {ok, JWT} = t2gp_social_test:login_pd(User, Pass),

    % check w/ no jwks
    {ok, Claims} = gen_server:call(t2gp_social_pd, {validate_jwt, JWT}),
    <<"aa7393be-778c-4404-a0ad-6c06e9a9ac2c">> = maps:get(<<"sub">>, Claims, <<>>),

    % run again to test jwks cache
    {ok, Claims} = gen_server:call(t2gp_social_pd, {validate_jwt, JWT}),
    <<"aa7393be-778c-4404-a0ad-6c06e9a9ac2c">> = maps:get(<<"sub">>, Claims, <<>>),

    % invalid alg
    AlgNoneJWT =
        <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.">>,
    {error, invalid_alg} = gen_server:call(t2gp_social_pd, {validate_jwt, AlgNoneJWT}),

    % invalid jwt
    BadJWT = <<"bad jwt">>,
    {error, invalid_token} = gen_server:call(t2gp_social_pd, {validate_jwt, BadJWT}),

    ok.

test_get_key_from_jwks(_) ->
    GoodURL = "https://https://account.id.privatedivision.com/.well-known/jwks.json",
    GoodKid = <<"w8IBNKZmc4C9qcq3r6b6u47Prv0">>,
    BadKid = <<"bad key id">>,
    {ok, _Key, Jwks} = t2gp_social_pd:get_key_from_jwks(GoodURL, GoodKid, undefined),
    {error, _} = t2gp_social_pd:get_key_from_jwks(GoodURL, BadKid, undefined),
    {ok, _, _} = t2gp_social_pd:get_key_from_jwks(GoodURL, GoodKid, Jwks),
    {error, _} = t2gp_social_pd:get_key_from_jwks(GoodURL, BadKid, Jwks),
    NotFoundURL = "https://httpbin.org/status/404",
    {error, {http_error, 404, _, _}} = t2gp_social_pd:get_key_from_jwks(
        NotFoundURL, BadKid, undefined
    ),
    BadURL = "bad url",
    {error, _Err} = t2gp_social_pd:get_key_from_jwks(BadURL, BadKid, undefined),
    ok.

test_clear_jwks(_) ->
    ok = gen_server:call(t2gp_social_pd, {clear_jwks}),
    {state, "https://account.id.privatedivision.com", undefined} = sys:get_state(
        t2gp_social_pd
    ),
    ok.

test_invalid_call(_) ->
    {error, invalid_msg} = gen_server:call(t2gp_social_pd, {invalid_call}),
    ok.

test_terminate(_) ->
    ok = t2gp_social_pd:terminate(shutdown, #{}),
    ok.

test_code_change(_) ->
    {ok, _} = t2gp_social_pd:code_change(old_version, new_version, []),
    ok.

test_handle_cast(_) ->
    {noreply, _} = t2gp_social_pd:handle_cast(cast, []),
    ok.

test_handle_info(_) ->
    {noreply, _} = t2gp_social_pd:handle_info(info, []),
    ok.
