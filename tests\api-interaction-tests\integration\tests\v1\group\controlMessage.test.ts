import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
let groupId: string;

beforeAll(async () => {
  usersTwok = new TwokAccounts(1, ["leader"]);
  await usersTwok.loginAll({});

  let r = await socialApi.createGroupV1(
    usersTwok.acct["leader"],
    {
      maxMembers: 6,
      joinRequestAction: 'manual',
      canCrossPlay: true,
    }
  );
  socialApi.testStatus(StatusCodes.CREATED, r);
  groupId = socialApi.getGroupId(r);
});

afterAll(async () => {
  await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);

  // logout all 2k accounts
  await usersTwok.logoutAll({});
});

describe('[public v1]', () => {
  let characters: string[] = ['a', ';', '<', ' '];
  // support 5120 ascii characters
  const maxCharacterSize: number = 5120;

  it.each(characters)(`send max-limit '%s' character; request only[happy trusted]`, async (character) => {
    let testCase = {
      description: `user sends max allowed number of control messages; check the response status only`,
      expected: `2xx status`
    };

    // create input characters
    let message = character.repeat(maxCharacterSize);

    // send message
    const actualControlMessage = await socialApi.sendControlMessage(usersTwok.acct["leader"], groupId, { payload: message }
    );

    // the message is sent out
    socialApi.expectMore(
      () => {expect(actualControlMessage.status).toEqual(StatusCodes.OK)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected status code`
        }
      }
    );
  });

  it.each(characters)(`send max-limit + 1 '%s' characters; request only[trusted]`, async (character) => {
    let testCase = {
      description: `user sends max allowed +1 number of control messages; check the response status only`,
      expected: `4xx status`
    };

    // create input characters
    let message = character.repeat(maxCharacterSize + 1);

    // send message
    const actualControlMessage = await socialApi.sendControlMessage(usersTwok.acct["leader"], groupId, { payload: message });

    // the message is not sent out
    socialApi.expectMore(
      () => {expect(actualControlMessage.status).toEqual(StatusCodes.BAD_REQUEST)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected status code`
        }
      }
    );
  });
});