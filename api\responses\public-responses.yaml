loginResponseBody:
  200:
    description: Successful authentication
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/loginResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
logoutResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
refreshResponseBody:
  200:
    description: Successful authentication
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/loginResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
importBlocklistResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/importBlocklistResponse"
        examples:
          "Add users to the blocklist":
            value:
              blockedids:
                [
                  "b287e655461f4b3085c8f244e394ff7e",
                  "effe28b27efc6594e43bfc0879b40085",
                ]
              onlineServiceType: 24
          "Add first party users to the blocklist":
            value:
              blockedids: ["76543210123456789"]
              onlineServiceType: 3
          "Remove users from the blocklist":
            value:
              blockedids: null
              onlineServiceType: 24
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
addBlocklistResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
removeBlocklistResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
clearBlocklistResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getProfileResponseBody:
  200:
    description: A object representing the logged in user profile
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/userProfileResponse"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
syncProfileResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getPlayedResponseBody:
  200:
    description: A paged array of recently played users. There is a maximum of 100 players.
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/playedPlayersNext"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
setPlayedResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
clearPlayedResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getPresenceResponseBody:
  200:
    description: A paged array of presence objects.  In most cases, this will be a list of 1 record.  But support exists for multiple presence records.
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/presenceNext"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
setPresenceResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
setActiveGroupResponseBody:
  200:
    description: a presence record
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/presenceResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
clearPresenceResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
clearActiveGroupResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
heartbeatPresenceResponseBody:
  200:
    description: a presence record.  if presence for user is not found, it will still return 200, but with a presence status of `offline`.
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/presenceResponse"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
getBlocklistResponseBody:
  200:
    description: A paged array of blocked user's info.
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/blocklistsNext"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getFriendsResponseBody:
  200:
    description: A paged array of friends
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/friendsNext"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
setFriendsResponseBody:
  200:
    description: Success
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/setFriendResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
importFriendsResponseBody:
  200:
    description: Expected response to a valid request
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/accountsNext"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getFriendResponseBody:
  200:
    description: The friend requested
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/friendResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
setFriendViewedResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
delFriendResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
setReportResponseBody:
  200:
    description: Expected response to a valid request
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/abuseReturn"
        examples:
          "SNS message id":
            value:
              messageId: 4e8f419e-4ac3-5222-b71d-1da980edf33a
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getGroupsResponseBody:
  200:
    description: A paginated array of groups
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/groupsNext"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
createGroupResponseBody:
  201:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/groupResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
delGroupResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getGroupResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/groupResponse"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
updateGroupResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getGroupMembersResponseBody:
  200:
    description: List of detailed members
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/membersNext"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getGroupMemberResponseBody:
  200:
    description: Group member information
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/groupMemberResponse"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
updateGroupMemberResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
kickMemberResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
leaveGroupResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
updateGroupMemberMetaResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  409:
    $ref: "../responses/responses.yaml#/responses/409"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
sendControlMessageResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getInvitesResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/invitesNext"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
sendInviteResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
acceptInviteResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
revokeInviteResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
declineInviteResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
requestJoinResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/joinRequestResponse"
        examples:
          "Empty Return request already exists":
            value: {}
          "Empty Return user blocked by group leader":
            value: {}
          "Membership Return":
            value:
              {
                "memberid": "b287e655461f4b3085c8f244e394ff7e",
                "approverid": "b287e655461f4b3085c8f244e394ff7e",
                "groupid": "01EYRSXN4DCFF1AV128Y5A211J",
                "status": "requested",
                "version": 0,
                "displayName": "string",
              }
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
approveJoinResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
rejectJoinResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
sendDirectMessageResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getDirectMessagesResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/dmsNext"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  406:
    $ref: "../responses/responses.yaml#/responses/406"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getVersionResponseBody:
  200:
    description: Version
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/versionResponse"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
getHealthResponseBody:
  200:
    description: Server is healthy
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/healthResponse"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    description: Server is unhealthy
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/healthResponse"
getDiscoveryResponseBody:
  200:
    description: Discovery response
    content:
      application/json:
        schema:
          type: array
          items:
            $ref: "../schemas/responseSchemas.yaml#/discoveryResponse"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
search2KResponseBody:
  200:
    description: An array of users
    content:
      application/json:
        schema:
          properties:
            items:
              type: array
              items:
                $ref: "../schemas/responseSchemas.yaml#/search2kUserResponse"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
syncSessionToGroupResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
upsertSessionAuthResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
acceptInviteBySessionResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
getEndorsementResponseBody:
  200:
    description: an array of endorsements
    content:
      application/json:
        schema:
          $ref: "../schemas/responseSchemas.yaml#/endorsementListResponse"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
resetEndorsementResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
incrementEndorsementResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
incrementCustomEndorsementResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
sendChatMessageResponseBody:
  200:
    $ref: "../responses/responses.yaml#/responses/200empty"
  400:
    $ref: "../responses/responses.yaml#/responses/400"
  401:
    $ref: "../responses/responses.yaml#/responses/401"
  403:
    $ref: "../responses/responses.yaml#/responses/403"
  404:
    $ref: "../responses/responses.yaml#/responses/404"
  429:
    $ref: "../responses/responses.yaml#/responses/429"
  500:
    $ref: "../responses/responses.yaml#/responses/500"
