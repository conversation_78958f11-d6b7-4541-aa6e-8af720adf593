// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/docker-existing-docker-compose
{
  "name": "Existing Docker Compose (Extend)",

  // Update the 'dockerComposeFile' list if you have more compose files or use different names.
  // The .devcontainer/docker-compose.yml file contains any overrides you need/want to make.
  "dockerComposeFile": ["../docker-compose.yml", "docker-compose.yml"],

  // The 'service' property is the name of the service for the container that VS Code should
  // use. Update this value and .devcontainer/docker-compose.yml to the real service name.
  "service": "t2gp-social-service",

  // The optional 'workspaceFolder' property is the path VS Code should open by default when
  // connected. This is typically a file mount in .devcontainer/docker-compose.yml
  "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}",
  "features": {},
  "customizations": {
    "vscode": {
      "extensions": [
        "golang.go",
        "donjayamanne.githistory",
        "GitHub.vscode-pull-request-github",
        "eamodio.gitlens",
        "VisualStudioExptTeam.vscodeintellicode",
        "esbenp.prettier-vscode",
        "42Crunch.vscode-openapi",
        "humao.rest-client",
        "redhat.vscode-yaml"
      ]
    }
  },

  // IGNORE ERROR FROM CLUSTER CREATE COMMAND ON REPEATS.
  "postCreateCommand": "go mod tidy && redis-cli --cluster create 192.168.123.4:6379 192.168.123.5:6380 192.168.123.6:6381 --cluster-replicas 0 --cluster-yes"
//  "postCreateCommand": "go mod tidy && redis-cli --cluster create 127.0.0.1:6379 127.0.0.1:6380 127.0.0.1:6381 --cluster-replicas 0 --cluster-yes"

  // Features to add to the dev container. More info: https://containers.dev/features.
  // "features": {},

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // Uncomment the next line if you want start specific services in your Docker Compose config.
  // "runServices": [],

  // Uncomment the next line if you want to keep your containers running after VS Code shuts down.
  // "shutdownAction": "none",

  // Configure tool-specific properties.
  // "customizations": {},

  // Uncomment to connect as an existing user other than the container default. More info: https://aka.ms/dev-containers-non-root.
  // "remoteUser": "devcontainer"
}
