import mqtt from 'mqtt/dist/mqtt.min';
import { MQTT_BROKER_URL } from '../../constant';
import type { Presence } from '../friends';
import { ILogService, LogService } from '../log';

export interface MqttMessagePayload<T> {
  type: string;
  data: T;
}

export interface MqttPresenceMessageData extends Presence {}

export interface MqttUserMessageData {
  inviter: string;
  message: string;
  name: string;
  status: string;
}

export interface IMqttService {
  onMessage(callback: (topic: string, data: Buffer) => void): void;
  onConnect(): void;
  onError(): void;
  publish<T>(
    topic: string,
    payload: MqttMessagePayload<T>,
    options: any,
    callback?: Function
  ): void;

  setPresence(useId: string, payload: Presence, callback?: Function): void;
}

export class MqttService implements IMqttService {
  private mqttClient: mqtt.MqttClient;
  private logService: ILogService;

  constructor(accessToken: string, userId: string, logService?: ILogService) {
    this.mqttClient = mqtt.connect(MQTT_BROKER_URL, {
      keepalive: 0,
      clientId: `socialweb-${userId}-${Math.random()
        .toString(16)
        .substr(2, 8)}`,
      password: accessToken,
      properties: { userProperties: { uid: userId } },
      protocolVersion: 5,
      reconnectPeriod: 20000,
      username: userId,
    });

    this.logService = logService || new LogService();
  }

  onMessage(callback: (topic: string, data: Buffer) => void) {
    this.mqttClient.on('message', (topic: string, data: Buffer) => {
      callback(topic, data);
    });
  }

  onConnect() {
    this.mqttClient.on('connect', () => {
      this.logService.log('mqtt connected to', MQTT_BROKER_URL);
    });
  }

  onError() {
    this.mqttClient.on('error', () => {
      this.logService.log('mqtt error');
    });
  }

  publish<T>(
    topic: string,
    payload: MqttMessagePayload<T>,
    options: any,
    callback: Function
  ) {
    const message =
      typeof payload === 'string' ? payload : JSON.stringify(payload);
    this.mqttClient.publish(topic, message, options, callback);
  }

  setPresence(useId: string, payload: Presence, callback?: Function) {
    const presenceOpts = {
      messageExpiryInterval: 86400,
      retain: true,
    };

    const finalPayload = {
      ...payload,
      timestamp: new Date().toISOString(),
    };

    this.publish<Presence>(
      `presence/${useId}`,
      {
        type: 'presence',
        data: finalPayload,
      },
      presenceOpts,
      callback
    );
  }
}
