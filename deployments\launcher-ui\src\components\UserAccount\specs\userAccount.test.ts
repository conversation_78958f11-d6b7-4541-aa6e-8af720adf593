import { render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { SocialServices } from '../../../services';
import { transportServiceMock } from '../../../services/__mocks__';
import AvatarMock from '../../Avatar/__mock__/Avatar.svelte';
import StatusIndicatorMock from '../../StatusIndicator/__mock__/StatusIndicator.svelte';
import UserAccountTestWrapper from './UserAccountTestWrapper.svelte';

jest.mock('../../../assets/icons', () => ({
  SVGArrowDown: SVGIconMock,
}));

jest.mock('../../Avatar', () => ({
  Avatar: AvatarMock,
}));

jest.mock('../../StatusIndicator', () => ({
  StatusIndicator: StatusIndicatorMock,
}));

describe('UserAccount', () => {
  it('should render UI', () => {
    const socialServicesMock = new SocialServices({
      transportService: transportServiceMock,
    });
    expect(() =>
      render(UserAccountTestWrapper, {
        props: {
          context: socialServicesMock,
        },
      })
    ).not.toThrow();
  });
});
