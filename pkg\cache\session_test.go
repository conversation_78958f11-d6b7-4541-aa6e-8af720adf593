package cache

//
//func Test_DnaSession(t *testing.T) {
//	g := goblin.Goblin(t)
//	g.Describe("test DnaSession", func() {
//		userId := "userId"
//		key1 := "dna:user:userId:dnaSession:sessionId1"
//		key2 := "dna:user:userId:dnaSession:sessionId2"
//		claim1 := jwt.Claims{
//			Subject:     userId,
//			SessionID:   "sessionId1",
//			ProductID:   "productId1",
//			AppGroupID:  "appGroupId1",
//			CreatedTime: time.Now().Unix(),
//			ExpiresTime: time.Now().Add(1 * time.Second).Unix(),
//		}
//
//		claim2 := jwt.Claims{
//			Subject:     userId,
//			SessionID:   "sessionId2",
//			ProductID:   "productId2",
//			AppGroupID:  "appGroupId2",
//			CreatedTime: time.Now().Unix(),
//			ExpiresTime: time.Now().Add(1 * time.Minute).Unix(),
//		}
//
//		g.BeforeEach(func() {
//			unix := time.Now()
//			claim1.CreatedTime = unix.Unix()
//			claim2.CreatedTime = unix.Unix()
//			claim1.ExpiresTime = unix.Add(1 * time.Second).Unix()
//			claim2.ExpiresTime = unix.Add(1 * time.Minute).Unix()
//		})
//		g.AfterEach(func() {
//			rc.del(ctx, key1)
//			rc.del(ctx, key2)
//		})
//
//		g.It("set two DnaSessions and get them", func() {
//			rc.SetSession(ctx, &claim1, "dna")
//			rc.SetSession(ctx, &claim2, "dna")
//
//			session, _ := rc.GetSession(ctx, userId, "dna", "sessionId1")
//			g.Assert(session).IsNotNil()
//
//			dnsSession, ok := session.Session.(apipub.DnaSession)
//			g.Assert(ok).IsTrue()
//			g.Assert(dnsSession.SessionId).Equal("sessionId1")
//			g.Assert(dnsSession.AppId).Equal("appGroupId1")
//
//			session, _ = rc.GetSession(ctx, userId, "dna", "sessionId2")
//			dnsSession, ok = session.Session.(apipub.DnaSession)
//			g.Assert(ok).IsTrue()
//			g.Assert(dnsSession.SessionId).Equal("sessionId2")
//			g.Assert(dnsSession.AppId).Equal("appGroupId2")
//		})
//
//		g.It("Two sessions expire when get them", func() {
//			rc.SetSession(ctx, &claim1, "dna")
//			rc.SetSession(ctx, &claim2, "dna")
//			session, _ := rc.GetSession(ctx, userId, "dna", "sessionId1")
//			dnsSession, ok := session.Session.(apipub.DnaSession)
//			g.Assert(ok).IsTrue()
//			g.Assert(dnsSession).IsNotNil()
//			g.Assert(dnsSession.SessionId).Equal("sessionId1")
//
//			session, _ = rc.GetSession(ctx, userId, "dna", "sessionId2")
//			dnsSession, ok = session.Session.(apipub.DnaSession)
//			g.Assert(ok).IsTrue()
//			g.Assert(dnsSession).IsNotNil()
//			g.Assert(dnsSession.SessionId).Equal("sessionId2")
//
//			time.Sleep(2 * time.Second)
//			session, _ = rc.GetSession(ctx, userId, "dna", "sessionId1")
//			g.Assert(session).IsNil()
//		})
//
//		g.It("get an invalid session", func() {
//			rc.SetSession(ctx, &claim1, "dna")
//			rc.SetSession(ctx, &claim2, "dna")
//
//			session, _ := rc.GetSession(ctx, userId, "dna", "sessionId3")
//			g.Assert(session).IsNil()
//		})
//	})
//}
