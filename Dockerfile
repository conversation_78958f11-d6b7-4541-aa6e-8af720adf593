############################
# STEP 1 build executable binary
############################
FROM golang:1.24.2-alpine3.21 AS builder

#SHELL ["/bin/bash", "-c"]

# Install git.
# Git is required for fetching the dependencies.
RUN apk update && apk add --no-cache git openssl openssh ca-certificates

RUN echo quit | openssl s_client -showcerts -servername proxy.golang.org -connect proxy.golang.org:443  2>&1 | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' > /etc/ssl/certs/ca-cert-t2.pem

WORKDIR /build

# Add T2 ca-cert to system to allow build on VPN
#RUN echo quit | openssl s_client -showcerts -servername proxy.golang.org -connect proxy.golang.org:443  2>&1 > /etc/ssl/certs/ca-cert-t2.pem

# disable known hosts check
RUN mkdir -p /root/.ssh && echo "StrictHostKeyChecking=no" > /root/.ssh/config

ARG app_version="dev"
ARG git_token

ENV GOPRIVATE="github.com/take-two-t2gp,github.com/2kg-coretech"
ENV GOOS=linux
ENV GOARCH=amd64
ENV CGO_ENABLED=0
ENV NODE_AUTH_TOKEN="${NODE_AUTH_TOKEN}"

RUN git config --global url.https://${git_token}:<EMAIL>/.insteadOf https://github.com/

RUN env

# Download deps first
COPY go.mod .
RUN go mod download

# Copy project files
COPY . .

# Build
RUN go generate ./...
RUN go build -v -o bin/application -ldflags="-s -w -X 'main.VER=$app_version'" cmd/social/main.go

############################
# STEP 2 build a small image
############################
FROM alpine:3.15
RUN apk update && apk add ca-certificates && rm -rf /var/cache/apk/*
RUN update-ca-certificates
# Copy our static executable.
WORKDIR /app
COPY --from=builder /build/bin/application /app/bin/application

ARG VERSION
ARG GIT_HASH
ARG BUILD_DATE

ENV VERSION "${VERSION}"
ENV GIT_HASH "${GIT_HASH}"
ENV BUILD_DATE "${BUILD_DATE}"

EXPOSE 8000/tcp
EXPOSE 8001/tcp

# Run the binary.
ENTRYPOINT ["/app/bin/application"]
