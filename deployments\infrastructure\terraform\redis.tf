resource "aws_elasticache_subnet_group" "social_redis_repgrp_subnet_group" {
  name       = "t2gp-${local.resource_prefix}-social-subnet-grp"
  subnet_ids = data.aws_subnets.private_subnets.ids
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social-subnet-grp"
  }, local.tags)
}

resource "aws_elasticache_replication_group" "social_redis_repgrp" {
  apply_immediately             = local.envvars[terraform.workspace]["redis_repgrp_changes_apply_immediately"]
  replication_group_id          = "t2gp-${local.resource_prefix}-social"
  replication_group_description = "Cache 2K user info"
  node_type                     = local.envvars[terraform.workspace]["redis_node_type"]
  parameter_group_name          = "t2gp-social-redis"
  engine_version                = "6.x"
  port                          = local.REDIS_PORT
  replicas_per_node_group       = local.envvars[terraform.workspace]["redis_repgrp_replica_count"]
  subnet_group_name             = aws_elasticache_subnet_group.social_redis_repgrp_subnet_group.name
  security_group_ids            = [aws_security_group.redis_sg.id]
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social"
  }, local.tags)
}

resource "aws_elasticache_replication_group" "social_redis_repgrp_clustered" {
  count                         = local.envvars[terraform.workspace]["create_clustered_redis"] == true ? 1 : 0
  apply_immediately             = local.envvars[terraform.workspace]["redis_repgrp_changes_apply_immediately"]
  replication_group_id          = "t2gp-${local.resource_prefix}-social-cluster"
  replication_group_description = "Cache 2K user info"
  node_type                     = local.envvars[terraform.workspace]["redis_node_type"]
  cluster_mode {
    num_node_groups         = local.envvars[terraform.workspace]["redis_repgrp_num"]
    replicas_per_node_group = local.envvars[terraform.workspace]["redis_repgrp_replica_count"]
  }
  parameter_group_name       = "default.redis6.x.cluster.on"
  engine_version             = "6.x"
  port                       = local.REDIS_PORT
  automatic_failover_enabled = true
  subnet_group_name          = aws_elasticache_subnet_group.social_redis_repgrp_subnet_group.name
  security_group_ids         = [aws_security_group.redis_sg.id]
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social-cluster"
  }, local.tags)
}

# resource "aws_elasticache_cluster" "social_redis" {
#   cluster_id           = "t2gp-${local.resource_prefix}-social"
#   replication_group_id = aws_elasticache_replication_group.example.id
# }