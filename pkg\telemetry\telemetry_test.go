package telemetry

import (
	"context"
	"net/http"
	reflect "reflect"
	"testing"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
)

func TestNewTelemetry(t *testing.T) {
	g := goblin.Goblin(t)
	cfg := config.ConfigForTests()

	g.Describe("NewTelemetry", func() {
		g.It("DDAPM False should return valid telemetry interface", func() {
			cfg.DatadogAPMEnabled = false
			tele := NewTelemetry(cfg, nil)
			g.Assert(tele).IsNotNil()
			g.Assert(tele.client).IsNotNil()
			name := reflect.TypeOf(tele.client).Elem().Name()
			g.<PERSON><PERSON><PERSON>(name).Equal("NullStatsdClient")
		})

		g.It("DDAPM True should return valid telemetry interface", func() {
			cfg.DatadogAPMEnabled = true
			tele := NewTelemetry(cfg, nil)
			g.Assert(tele).IsNotNil()
			g.Assert(tele.client).IsNotNil()
			name := reflect.TypeOf(tele.client).Elem().Name()
			g.Assert(name).Equal("Client")
		})
	})
}

//func TestEvent(t *testing.T) {
//	// TODO:  Had to use the deprecated golang Mock package here instead of the go.uber.com package
//	// because the Datadog statsd project uses golang mock and it was acting screwy when trying to mockgen.
//	// Maybe eventually we are able to switch this to use go.uber.com mocks to be consistent with the rest of the project.
//	mockCtrl := gomock.NewController(t)
//	defer mockCtrl.Finish()
//
//	g := goblin.Goblin(t)
//	cfg := config.ConfigForTests()
//	tele := NewTelemetry(cfg, nil)
//	ctx := context.Background()
//	client := mock_statsd.NewMockClientInterface(mockCtrl)
//	tele.SetClient(client)
//
//	g.Describe("Events", func() {
//		g.Assert(tele).IsNotNil()
//		r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
//
//		g.It("should send event", func() {
//			event := tele.NewEvent(KAuthLoginSuccess, r.Context())
//			client.EXPECT().Event(event)
//			tele.Event(event)
//		})
//
//		g.It("should create invalid event w/ an invalid type", func() {
//			t := EventType("foo")
//			e := tele.NewEvent(t, r.Context())
//			g.Assert(e.Title).Equal("foo")
//			g.Assert(e.Text).Equal("unknown")
//		})
//
//		g.It("should send simple event", func() {
//			client.EXPECT().SimpleEvent("foo", "foobar")
//			tele.SimpleEvent("foo", "foobar")
//		})
//
//		g.It("should send float number event", func() {
//			client.EXPECT().Set("foo", "123.000000", []string{}, 1.0)
//			tele.SetFloat("foo", 123.0, []string{})
//		})
//
//		g.It("should send timing event", func() {
//			client.EXPECT().Timing("foo", time.Hour, []string{}, 1.0)
//			tele.Timing("foo", time.Hour, []string{})
//		})
//	})
//}

func TestJWTInfo(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("JWTInfo", func() {
		g.It("should return empty appid and userid", func() {
			ctx := context.Background()
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
			appid, userid := JWTInfo(r.Context())
			g.Assert(appid).Equal("")
			g.Assert(userid).Equal("")
		})

		g.It("should return valid appid and userid from", func() {
			ctx := context.Background()
			JWT := "eyJraWQiOiI3NTVkOTM1NC0xMzU5LTExZWItYWRjMS0wMjQyYWMxMjAwMDIiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUzNjg0NzMzLCJydGkiOiI4MmYzOGJmODRlY2U0OTRmOWZmYmU1NjQ3YzQ3YmFiZSIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiNmIxZTEyMmJmODA0NGI4MTlkMzkyYTNkZGRjYmVhOGIiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6MTY1MzY4MTEzMywiaWF0IjoxNjUzNjc3NTMzLCJqdGkiOiIzNzZiYTQ0M2VkNzc0YTE1YTVmNjI5Mzc4ZTA4YzgzZiIsImxhdCI6MzkuMDQ2OX0."
			jwt, _ := jwt.ParseJWTTokenWithoutValidation(JWT)
			ctx = context.WithValue(ctx, constants.BearerAuthJWT, jwt)
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
			appid, userid := JWTInfo(r.Context())
			g.Assert(appid).Equal("3bb92115af724e509f639113b0d521f8")
			g.Assert(userid).Equal("b287e655461f4b3085c8f244e394ff7e")
		})
	})
}
