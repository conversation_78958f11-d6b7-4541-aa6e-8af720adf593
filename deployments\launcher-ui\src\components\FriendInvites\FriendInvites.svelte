<script lang="ts">
  import { SVGArrowLeft, SVGPersonAdd } from '../../assets/icons';
  import { ACCORDION_MODE, ROUTE_FRIENDS_LIST } from '../../constant';
  import { useTranslator } from '../../hooks';
  import { Accordion, AccordionSection } from '../Accordion';
  import { NavLink } from '../NavLink';
  import { SteamImport } from '../SteamImport';

  const t = useTranslator();
</script>

<style>
  .container {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .action-bar {
    box-sizing: border-box;
    height: 3.75rem;
    background-color: var(
      --social-bg-color-action-bar,
      var(--default-bg-color-action-bar)
    );
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.75rem 0.875rem;
  }

  .action-bar .nav-icon {
    display: flex;
    margin-inline-end: 1.25rem;
  }

  .action-bar .icon {
    display: flex;
    margin-inline-end: 0.625rem;
  }

  .action-bar .title {
    color: var(--default-color, var(--social-color));
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.25rem;
  }

  .content {
    flex: 1;
    background-color: rgba(64, 64, 64, 1);
  }

  .content :global(.accordion) {
    display: flex;
    flex-direction: column;
  }
  .content :global(.accordion-section-container) {
    display: flex;
    flex-direction: column;
    margin: 0;
  }

  .content :global(.accordion-section-content) {
    height: 100%;
    width: 100%;
  }
</style>

<div class="friends-invites container">
  <div class="action-bar">
    <NavLink to="{ROUTE_FRIENDS_LIST}">
      <span class="nav-icon">
        <SVGArrowLeft />
      </span>
    </NavLink>

    <span class="icon">
      <SVGPersonAdd />
    </span>
    <span class="title"> {$t('Add Friend')} </span>
  </div>
  <div class="content">
    <Accordion
      value="{['import-friends']}"
      className="accordion"
      mode="{ACCORDION_MODE.single}"
    >
      <AccordionSection
        title="{$t('import friends')}"
        key="import-friends"
        mode="{ACCORDION_MODE.single}"
        containerClassName="accordion-section-container"
        contentClassName="accordion-section-content"
      >
        <SteamImport />
      </AccordionSection>
    </Accordion>
  </div>
</div>
