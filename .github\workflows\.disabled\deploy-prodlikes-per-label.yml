name: Deploy STG/CRT/PRD Environment for Game
run-name: Deploy ${{ github.event.inputs.version }} to ${{ github.event.inputs.env_label }} ${{ github.event.inputs.env_type }}

on:
  workflow_dispatch:
    inputs:
      version:
        description: 'Release Version'
      env_type:
        # type: choice
        description: 'Environment type to deploy to. Please give these sensible names like staging, cert, or production'
      template_helm_vals_to_use:
        type: choice
        description: 'Helm values to source from'
        options:
          - staging
          - cert
          - production
      env_label:
        description: "Unique label to append to environment name. This should be the game's name and unique."
      isolate_datastore:
        description: 'Check this field if this environment needs its dynamodb/redis isolated. (Do NOT check this box if deploying to staging-artemis or cert-artemis)'
        type: boolean
      deploy_key:
        description: 'Deploy Key: SECRETS.DEPLOY_KEY. Check 1Password'

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  run-helm:
    name: 'Run Helm Deploy'
    runs-on: [t2gp-arc-linux]
    env:
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
      CLUSTER: t2gp-production
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
      environment_name: ${{ steps.output_info.outputs.environment_name }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables (Manual)
        if: github.event.inputs.version != '' && github.event.inputs.env_label != ''
        run: |
          ver=${{github.event.inputs.version}}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
          echo "IMAGE_TAG=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo "ENV_LABEL=${{ github.event.inputs.env_label }}" >> $GITHUB_ENV
          echo DEPLOY_KEY=${{ secrets.DEPLOY_KEY }} >> $GITHUB_ENV
          echo "ENV_TYPE=${{ github.event.inputs.env_type }}" >> $GITHUB_ENV
          echo "HELM_VALUES_TEMPLATE=${{ github.event.inputs.template_helm_vals_to_use }}" >> $GITHUB_ENV
          echo "ISOLATED_DATASTORE=${{ github.event.inputs.isolate_datastore}}" >> $GITHUB_ENV
          echo "DEPLOY_TIME=$(date +'%Y-%m-%d %H:%M:%S %Z')" >> $GITHUB_ENV
          echo "CLUSTER_OIDC_ID=$(aws eks describe-cluster --name ${{ env.CLUSTER }}  --query "cluster.identity.oidc.issuer" --output text | cut -d '/' -f 5)" >> $GITHUB_ENV
      - name: Check Deploy Key
        if: ${{ env.DEPLOY_KEY != github.event.inputs.deploy_key }}
        run: |
          exit 1
      - name: Helm Deploy social-service
        id: helm_deploy
        if: github.event.inputs.version != '' && github.event.inputs.env_label != ''
        uses: eul721/helm-deploy@v1.3.0
        with:
          cluster: ${{ env.CLUSTER }}
          dry-run: false
          namespace: social-service
          release-name: ${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}
          chart-location: helm/helm_chart
          value-files: helm/helm_values/${{ env.HELM_VALUES_TEMPLATE }}.yaml
          values: 'social-api.groupsApi.image.tag=${{ env.IMAGE_TAG }},social-api.groupsApi.commitSha=${{env.GIT_SHA}},global.socialMqttEnabled=true,social-mqtt.pluginLoader.defaultPluginVersion=${{env.SUBMODULE_HASH}},social-api.mqttEnv=${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }},social-api.ingress.publicHostname=,social-api.ingress.privateHostname=,global.configToUse=${{ env.HELM_VALUES_TEMPLATE }},social-mqtt.ingress.hostname=,global.socialDatastoreEnabled=${{env.ISOLATED_DATASTORE}},social-datastore.eksOidcId=${{env.CLUSTER_OIDC_ID}},global.ddEnv=${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }},social-mqtt.vernemq.defaultSMTarget=${{ env.HELM_VALUES_TEMPLATE }}'
      - name: Update plugin with script
        working-directory: deployments
        env:
          TARGET_ENV: ${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        if: github.event.inputs.version != '' && github.event.inputs.env_label != ''
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}", "version": "${{env.IMAGE_TAG}}", "api_url":"https://social-service-${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}.d2dragon.net/v1", "api_private_url":"https://social-service-${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}-private.d2dragon.net", "mqtt_url":"wss://social-service-${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}.d2dragon.net/mqtt" }'
      # - name: Generate Config for Discovery Endpoint
      #   if: github.event.inputs.version != '' && github.event.inputs.env_label != '' && github.event.inputs.client_app_id != ''
      #   run: |
      #     JSON_STRING=$( jq -n \
      #       --arg http_url "https://social-service-${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}.d2dragon.net/" \
      #       --arg mqtt_url "wss://social-service-${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}.d2dragon.net/mqtt" \
      #       '{httpUrl: $http_url , mqttUrl: $mqtt_url}')
      #     echo $JSON_STRING > config.json
      #     aws s3 cp config.json s3://t2gp-social/discovery/${{ env.ENV_TYPE }}/${{ github.event.inputs.client_app_id }}/config.json
      - name: Job Summary
        run: |
          echo "# ${{ env.ENV_LABEL }}#${{ env.ENV_TYPE }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "deployed_version:\"${{ env.IMAGE_TAG }}\"" >> $GITHUB_STEP_SUMMARY
          echo "deployed_url:\"https://social-service-${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}.d2dragon.net/\"" >> $GITHUB_STEP_SUMMARY
      - name: Output Info
        id: output_info
        run: |
          echo "environment_name=${{ env.ENV_TYPE }}-${{ env.ENV_LABEL }}" >> $GITHUB_OUTPUT
          echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: ${{ needs.run-helm.outputs.environment_name }}
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      skip_notification: false
      api_test_note: 'deploy ${{ needs.run-helm.outputs.environment_name }}'
    secrets: inherit
