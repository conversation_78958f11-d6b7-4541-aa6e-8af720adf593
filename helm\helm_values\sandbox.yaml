social-mqtt:
  enabled: false
  # in case mqtt is requested, use the following values
  replicaCount: 1
  nodeLabel: "social-mqtt-nodes-non-production"
  nodeTaintEnv: non-production
  ingress:
    targetType: ip
    securityGroups:
      - d2c-east1-eks-alb
      - d2c-east1-corp
      - social-develop-ext-sg
    albEnvTag: sandbox
  resources:
    limits:
      cpu: "1"
      memory: "1Gi"
    requests:
      cpu: "0.5"
      memory: "512Mi"

social-api:
  mqttEnv: develop
  hpa:
    minReplicas: 1
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
        - matchExpressions:
          - key: eks.amazonaws.com/capacityType
            operator: In
            values:
            - ON_DEMAND
  # multitenant temp env
  ingress:
    targetType: ip
    albEnvTag: sandbox
    securityGroups:
      - d2c-east1-eks-alb
      - d2c-east1-corp
      - social-develop-ext-sg
    privateSecurityGroups:
      - d2c-east1-eks-alb
      - d2c-east1-corp
      - social-develop-private-ext-sg

global:
  awsRoleArn: arn:aws:iam::354767525209:role/t2gp-social-non-production
  albGroupNameSuffix: "sandbox" # all sandboxes should share an ALB
  configToUse: "develop"
  datadogEnabled: true
  loadtestEnabled: false
  ingress:
    waf:
      enabled: true
      arn: "arn:aws:wafv2:us-east-1:354767525209:regional/webacl/t2gp-social-non-production-social/ecd3f999-602d-4935-8e80-243fba96af13"

