package messenger

import (
	"bytes"
	"context"
	"fmt"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/segmentio/encoding/json"

	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// VMQApiCall call t2gp api to VMQ
func VMQApiCall(ctx context.Context, cfg *config.Config, command string, params Params) (*[]Row, error) {
	return VMQApiCallNamed(ctx, cfg, "t2gp", command, params)
}

// VMQApiCallNamed call generic api to VMQ
func VMQApiCallNamed(ctx context.Context, cfg *config.Config, apiName string, command string, params Params) (*[]Row, error) {
	log := logger.FromContext(ctx)
	VMQApiURL := cfg.VMQApiURL //os.Getenv("VMQ_API_URL")
	VMQApiKey := cfg.VMQApiKey //os.Getenv("VMQ_API_KEY")
	commandURL := fmt.Sprintf("%s/api/v1/%s/%s?", VMQApiURL, apiName, command)

	paramDict := zerolog.Dict()

	// NOTE: params need to be in the same order as the API call spec
	for _, param := range params {
		commandURL += fmt.Sprintf("%s=%s&", url.QueryEscape(param.Key), url.QueryEscape(param.Value))
		paramDict = paramDict.Str(param.Key, param.Value)
	}
	commandURL = commandURL[:len(commandURL)-1]

	log.Info().Dict("params", paramDict).Str("event", "VMQ API call").Msgf("VMQ API call = %s", commandURL)

	req, err := http.NewRequest("GET", commandURL, nil)
	if err != nil {
		log.Error().Err(err).Msgf("http create request error")
		return nil, err
	}

	req.Header.Add(constants.KContentType, constants.KApplicationJson)

	// Documentation here: https://docs.vernemq.com/administration/http-administration
	req.SetBasicAuth(VMQApiKey, "")

	return VMQApiCallProcess(ctx, req, false)
}

func VMQApiCallProcess(ctx context.Context, req *http.Request, binReq bool) (*[]Row, error) {
	span, _ := tracer.StartSpanFromContext(ctx, "vmq.webApi", tracer.ResourceName(req.URL.Path))
	var resp *http.Response
	resp, err := http.DefaultClient.Do(req)
	if err != nil {
		span.Finish()
		log.Error().Msgf("http client error %v", err)
		return nil, err
	}
	span.Finish()

	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)

	// check response is 200
	if resp.StatusCode != http.StatusOK {
		log.Error().Msgf("http client code=%d error %s", resp.StatusCode, body)
		return nil, errs.New(resp.StatusCode, errs.EVmqGeneric)
	}

	// parse response
	var envelope Envelope
	if len(body) > 0 {
		err = json.Unmarshal(body, &envelope)
		if err != nil {
			//binary message return is not json, handle that here
			//return the error if not a binary request
			if binReq && strings.HasPrefix(err.Error(), "json: invalid character") {
				return &[]Row{{
					"text": string(body),
				}}, nil
			}
			log.Error().Err(err).Msgf("json unmarshal error")
			return nil, errs.New(http.StatusInternalServerError, errs.EJsonParse)
		}
	} else {
		log.Error().Msgf("body length is zero resp=%d body=%v", resp.StatusCode, body)
		return nil, errs.New(http.StatusInternalServerError, errs.EResponseEmpty)
	}

	switch envelope.Type {
	case "table":
		var table Table
		err = json.Unmarshal(body, &table)
		if err != nil {
			log.Error().Err(err).Msgf("json unmarshal error")
			return nil, errs.New(http.StatusInternalServerError, errs.EJsonParse)
		}
		return &table.Table, nil
	case "text":
		var text Text
		err = json.Unmarshal(body, &text)
		if err != nil {
			log.Error().Err(err).Msgf("Failed to unmarshall text message")
			return nil, errs.New(http.StatusInternalServerError, errs.EJsonParse)
		}
		return &[]Row{{
			"text": text.Text,
		}}, nil
	case "alert":
		var alert Alert
		err = json.Unmarshal(body, &alert)
		if err != nil {
			log.Error().Err(err).Msgf("json unmarshal error")
			return nil, errs.New(http.StatusInternalServerError, errs.EJsonParse)
		}
		log.Error().Msgf("VMQ alert error %s", alert.Alert[0].Text)
		return nil, errs.New(http.StatusInternalServerError, errs.EVmqAlertError)
	}

	log.Error().Msgf("unknown message %s", body)
	return nil, fmt.Errorf("unknown message")
}

// VMQBinApiCall call generic api to VMQ
func VMQBinApiCall(ctx context.Context, cfg *config.Config, command string, data *[]byte, topic string, userid string, jwt string) (*[]Row, error) {
	log := logger.FromContext(ctx)
	if data == nil {
		log.Error().Msgf("http create request error message data is nil")
		return nil, errs.New(http.StatusBadRequest, errs.EInvalidRequest)
	}

	VMQApiURL := cfg.VMQHttpPubApiURL // os.Getenv("VMQ_HTTPPUB_API_URL")
	VMQApiKey := cfg.VMQHttpPubApiKey //os.Getenv("VMQ_HTTPPUB_API_KEY")

	commandURL := fmt.Sprintf("%s/restmqtt/api/v1/%s", VMQApiURL, command)

	// //can use params or header for some values
	// commandURL := fmt.Sprintf("%s/restmqtt/api/v1/%s?", VMQApiURL, command) //needs trailing ? if using params
	// for _, param := range params {
	// 	commandURL += fmt.Sprintf("%s=%s&", url.QueryEscape(param.Key), url.QueryEscape(param.Value))
	// }
	// commandURL = commandURL[:len(commandURL)-1]

	log.Info().Str("topic", topic).Str("userid", userid).Msgf("VMQ API call = %s", commandURL)

	req, err := http.NewRequest("POST", commandURL, io.NopCloser(bytes.NewBuffer(*data)))
	if err != nil {
		log.Error().Err(err).Msgf("http create request error")
		return nil, err
	}
	/**/
	req.Header.Set(constants.KContentType, constants.KApplicationOctetStream)
	req.Header.Set("retain", "false")
	req.Header.Set("QoS", "1")
	req.Header.Set("topic", topic)
	req.Header.Set("user", userid)
	req.Header.Set("password", jwt)
	req.Header.Set("client_id", userid)
	//req.Header.Set("user_properties", `[{"isBinary": "true"}]`)
	//--header 'user_properties: [{"a":"b2"}]'
	//user properties is only sendable in header. Also seems like it doesn't work if set

	//https://docs.vernemq.com/v/master/configuring-vernemq/http-pub
	req.SetBasicAuth(VMQApiKey, "")

	return VMQApiCallProcess(ctx, req, true)
}

// Publish a message to VMQ
func Publish(ctx context.Context, cfg *config.Config, fromUserid string, topic string, message string, jwt string) error {
	log := logger.FromContext(ctx)
	bUseBinAPI := false
	if cfg.VMQUseBinaryPub {
		bUseBinAPI = true
	}

	var err error

	if bUseBinAPI {
		messageBin := []byte(message)
		err = PublishBin(ctx, cfg, fromUserid, topic, &messageBin, jwt)
	} else {
		_, err = VMQApiCall(ctx, cfg, "publish", Params{
			// param order matters
			// for more details run "vmq-admin t2gp publish" in the vmq container
			Param{Key: "topic", Value: topic},
			Param{Key: "message", Value: message},
			Param{Key: "userid", Value: fromUserid},
		})
	}
	if err != nil {
		log.Error().Err(err).Msgf("vmq-admin publish %s %s failed", topic, message)
	}
	return err
}

// PublishBin Publish a binary message to VMQ
func PublishBin(ctx context.Context, cfg *config.Config, fromUserid string, topic string, data *[]byte, jwt string) error {
	log := logger.FromContext(ctx)

	vmqResult, err := VMQBinApiCall(ctx, cfg, "publish", data, topic, fromUserid, jwt)
	if vmqResult != nil {
		log.Debug().Interface("vmqresult", (*vmqResult)[0]["text"]).Msg("vmq binary result")
	}
	if err != nil {
		log.Error().Err(err).Msgf("vmq-admin publish bin %s %s failed", topic, fromUserid)
	}
	return err
}

// Subscribe a message to VMQ
func Subscribe(ctx context.Context, cfg *config.Config, userid string, topic string) error {
	log := logger.FromContext(ctx)
	_, err := VMQApiCall(ctx, cfg, "subscribe", Params{
		// param order matters
		// for more details run "vmq-admin t2gp subscribe" in the vmq container
		Param{Key: "topic", Value: topic},
		Param{Key: "userid", Value: userid},
	})
	if err != nil {
		log.Error().Err(err).Msgf("vmq-admin subscribe %s %s", userid, topic)
	}
	return err
}

// Unsubscribe a message to VMQ
func Unsubscribe(ctx context.Context, cfg *config.Config, userid string, topic string) error {
	log := logger.FromContext(ctx)
	_, err := VMQApiCall(ctx, cfg, "unsubscribe", Params{
		// param order matters
		// for more details run "vmq-admin t2gp unsubscribe" in the vmq container
		Param{Key: "topic", Value: topic},
		Param{Key: "userid", Value: userid},
	})
	if err != nil {
		log.Error().Err(err).Msgf("vmq-admin unsubscribe %s %s failed", userid, topic)
	}
	return err
}

func GetSubscriptions(ctx context.Context, cfg *config.Config, userid string) (*[]Row, error) {
	log := logger.FromContext(ctx)
	result, err := VMQApiCall(ctx, cfg, "topics", Params{
		Param{Key: "userid", Value: userid},
	})
	if err != nil {
		log.Error().Err(err).Msgf("vmq-admin subscriptions %s failed", userid)
	}
	return result, err
}

func DeleteSubscriptions(ctx context.Context, cfg *config.Config, userid string) error {
	log := logger.FromContext(ctx)
	_, err := VMQApiCall(ctx, cfg, "clear-topics", Params{
		Param{Key: "userid", Value: userid},
	})
	if err != nil {
		log.Error().Err(err).Msgf("vmq-admin subscriptions %s failed", userid)
	}
	return err
}

// VMQStatus return status json structure
func VMQStatus(ctx context.Context, cfg *config.Config) (*string, error) {
	log := logger.FromContext(ctx)
	VMQApiURL := cfg.VMQApiURL //os.Getenv("VMQ_API_URL")
	// Documentation here: https://docs.vernemq.com/v/1.8.0/monitoring/status
	URL := fmt.Sprintf("%s/status.json", VMQApiURL)

	req, err := http.NewRequest("GET", URL, nil)
	if err != nil {
		log.Error().Msgf("http create request error %v", err)
		return nil, err
	}

	var resp *http.Response
	resp, err = http.DefaultClient.Do(req)
	if err != nil {
		log.Error().Msgf("http client error %v", err)
		return nil, err
	}

	// parse response
	defer resp.Body.Close()
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		return nil, readErr
	}
	result := string(body)
	return &result, nil
}

// VMQSessions return connected clients
func VMQSessions(ctx context.Context, cfg *config.Config) (*[]Row, error) {
	result, err := VMQApiCallNamed(ctx, cfg, "session", "show", Params{})
	return result, err
}
