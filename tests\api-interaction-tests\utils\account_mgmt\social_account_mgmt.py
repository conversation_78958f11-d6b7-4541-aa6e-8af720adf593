from abc import ABC, abstractmethod
import os
import subprocess
from datetime import datetime
import account_mgmt

#
class PrintAccountInfoMixin(ABC):
    # path of the account health check tool relative to this module
    _account_health_check_rel_path = "../account_health_check"

    # path of repo root relative to this module
    _repo_root_rel_path = "../.."

    @property
    @abstractmethod
    def ci_test_acct_file_name(self):
        pass

    @abstractmethod
    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        pass

    #
    def _print_account_info(self, owner, account_creator_id, output_mode):
        try:
            super()._print_account_info(owner, account_creator_id, output_mode)
        except ValueError:
            if output_mode in {
                                # Output account info in the format to be ingested by dotenv.
                                # Output to the terminal for inspection.
                                "ci",
                                # Output account info in the format to be ingested by dotenv.
                                # Output to a file located at the test framework repo root.
                                "ci_file",
                                # A special mode for account health check. Output file to where the account health check tool is.
                                # This option is not meant to be exposed externally like a CLI tool.
                                "health_check_file"
                              }:

                account_info = self._accounts[owner][account_creator_id]
                # zero-padded counter
                zpnum = str(self._print_account_info_cnt).zfill(3)

                account_info_str = self.get_account_info_str(account_creator_id, account_info, zpnum)

                if output_mode == "ci":
                    print(account_info_str, end="")
                elif output_mode in {"ci_file", "health_check_file"}:
                    if self._print_account_info_cnt == 1:
                        mode = "w"
                    else:
                        mode = "a"

                    if output_mode == "ci_file":
                        acct_file_path = f"{self._repo_root_rel_path}/{self.ci_test_acct_file_name}"
                    elif output_mode == "health_check_file":
                        acct_file_path = f"{self._account_health_check_rel_path}/{self.ci_test_acct_file_name}"

                    with open(acct_file_path, mode) as f:
                        # add comments
                        if self._print_account_info_cnt == 1:
                            f.write(f"# test accounts owned by [{owner}]\n")
                            f.write(f"# file generated on [{datetime.now()}]\n\n")

                        f.write(account_info_str)
            else:
                raise ValueError("invalid output mode [{}]".format(output_mode))

#
class SocialTwokAccountManager(PrintAccountInfoMixin, account_mgmt.TwokAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.twok_accounts"

    #
    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
TWOK_{}_EMAIL={}
TWOK_{}_2K_PUBLIC_ID={}
TWOK_{}_DISPLAY_NAME={}
TWOK_{}_PASSWORD={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["display name"],
           zpnum, self._gen_acct_password)  # TODO: change this to account_info["password"] when all accounts have their password field filled in.

    # TODO: health check for platform accounts?
    #
    def health_check(self, search_by, q, replace_bad_accounts):
        # generate test account file containing accounts to perform health check on
        self.search_account(search_by, q, "health_check_file")

        health_check_replace = "no_replace"
        if replace_bad_accounts == "replace":
            status = self._lock.get_lock_status()
            if search_by == "owner" and q != "None" and status["exist"] == False:
                health_check_replace = "replace"
            else:
                print("Replacing bad accounts is available only when search_by is \"owner\", owner is not \"None\", and there's no existing lock\n")

        healthCheckArgs = (["npx", "ts-node", "account_health_check_cli.ts", health_check_replace, q],)
        healthCheckKwargs = {"cwd": self._account_health_check_rel_path}

        if os.name == "nt":
            healthCheckKwargs["shell"] = True

        subprocess.run(*healthCheckArgs, **healthCheckKwargs)

#
class SocialPdAccountManager(PrintAccountInfoMixin, account_mgmt.PdAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.pd_accounts"

    #
    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
PD_{}_EMAIL={}
PD_{}_ID={}
PD_{}_DISPLAY_NAME={}
PD_{}_PASSWORD={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["ID"],
           zpnum, account_info["display name"],
           zpnum, account_info["password"])

#
class SocialSteamPAccountManager(PrintAccountInfoMixin, account_mgmt.SteamPAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.steam_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
STEAM_{}_PLATFORM_ID={}
STEAM_{}_2K_PUBLIC_ID={}
STEAM_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])

#
class SocialEpicPAccountManager(PrintAccountInfoMixin, account_mgmt.EpicPAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.epic_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
EPIC_{}_PLATFORM_ID={}
EPIC_{}_2K_PUBLIC_ID={}
EPIC_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])

#
class SocialSwitchPAccountManager(PrintAccountInfoMixin, account_mgmt.SwitchPAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.switch_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
SWITCH_{}_PLATFORM_ID={}
SWITCH_{}_2K_PUBLIC_ID={}
SWITCH_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])

#
class SocialXbxPAccountManager(PrintAccountInfoMixin, account_mgmt.XbxPAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.xbx_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
XBX_{}_PLATFORM_ID={}
XBX_{}_2K_PUBLIC_ID={}
XBX_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])

#
class SocialXb1PAccountManager(PrintAccountInfoMixin, account_mgmt.Xb1PAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.xb1_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
XB1_{}_PLATFORM_ID={}
XB1_{}_2K_PUBLIC_ID={}
XB1_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])

#
class SocialPs4PAccountManager(PrintAccountInfoMixin, account_mgmt.Ps4PAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.ps4_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
PS4_{}_PLATFORM_ID={}
PS4_{}_2K_PUBLIC_ID={}
PS4_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])

#
class SocialPs5PAccountManager(PrintAccountInfoMixin, account_mgmt.Ps5PAccountManager):
    @property
    def ci_test_acct_file_name(self):
        return ".env.ps5_accounts"

    def get_account_info_str(self, account_creator_id, account_info, zpnum):
        return """\
PS5_{}_PLATFORM_ID={}
PS5_{}_2K_PUBLIC_ID={}
PS5_{}_ALIAS={}

""".format(zpnum, account_creator_id,
           zpnum, account_info["public ID"],
           zpnum, account_info["alias"])