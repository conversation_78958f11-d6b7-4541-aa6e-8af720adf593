package messenger

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"testing"

	"github.com/franela/goblin"
	"github.com/jarcoal/httpmock"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func MockVMQApiRequest(plugin string, command string, params Params, httpResponseCode int, httpResponseBody string) string {
	VMQApiURL := cfg.VMQApiURL //os.Getenv("VMQ_API_URL")

	queryString := ""
	for _, param := range params {
		queryString += fmt.Sprintf("%s=%s&", url.QueryEscape(param.Key), url.QueryEscape(param.Value))
	}
	if queryString != "" {
		queryString = queryString[:len(queryString)-1]
	}
	method := "GET"

	// GET /v1/api/[plugin]/[command]?key1=value1
	retval := ""

	url := fmt.Sprintf("%s/api/v1/%s/%s", VMQApiURL, plugin, command)
	//commandURL := fmt.Sprintf("%s/restmqtt/api/v1/%s", VMQApiURL, command)
	if cfg.VMQUseBinaryPub {
		method = "POST"
		url = fmt.Sprintf("%s/restmqtt/api/v1/%s", cfg.VMQHttpPubApiURL, command)
	} else if queryString != "" {
		url = fmt.Sprintf("%s?%s", url, queryString)
	}

	retval = fmt.Sprintf("%s %s", method, url)
	httpmock.RegisterResponder(method, url,
		httpmock.NewStringResponder(httpResponseCode, httpResponseBody))
	return retval
}

func TestVMQApiCall(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("VMQApiCall", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			body := `{"table":[{"version":"0.2.85-c03ef72","git_hash":"c03ef724fa504b7c7921b7fad2021bf4e8b37112","build_date":"2021-11-01T23:29:49:-0700"}],"type":"table"}`
			key := MockVMQApiRequest("t2gp", "version", Params{}, http.StatusOK, body)

			result, err := VMQApiCall(context.Background(), cfg, "version", Params{})
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			row := (*result)[0]
			g.Assert(row["version"]).Equal("0.2.85-c03ef72")
			g.Assert(row["git_hash"]).Equal("c03ef724fa504b7c7921b7fad2021bf4e8b37112")
			g.Assert(row["build_date"]).Equal("2021-11-01T23:29:49:-0700")

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})

		g.It("should fail with bad json unmarshal error", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			body := "bad json"
			key := MockVMQApiRequest("t2gp", "version", Params{}, http.StatusOK, body)

			result, err := VMQApiCall(context.Background(), cfg, "version", Params{})
			g.Assert(err).IsNotNil()
			g.Assert(err.Error())
			g.Assert(result).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})

		g.It("should fail with empty body w/ a parse error", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			body := ""
			key := MockVMQApiRequest("t2gp", "version", Params{}, http.StatusOK, body)

			result, err := VMQApiCall(context.Background(), cfg, "version", Params{})
			g.Assert(err).IsNotNil()
			g.Assert(err.Error())
			g.Assert(result).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})

	})
}

func TestPublish(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("Publish", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			topic := "dna/user/foobar"
			message := `{"foo":"bar"}`
			key := MockVMQApiRequest("t2gp", "publish", Params{
				Param{Key: "topic", Value: topic},
				Param{Key: "message", Value: message},
				Param{Key: "userid", Value: "system"},
			}, http.StatusOK, `{"text":"Done","type":"text"}`)

			err := Publish(context.Background(), cfg, "system", topic, message, "jwt")
			g.Assert(err).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})

		g.It("should fail", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			topic := "dna/user/foobar"
			message := `{"foo":"bar"}`
			key := MockVMQApiRequest("t2gp", "publish", Params{
				Param{Key: "topic", Value: topic},
				Param{Key: "message", Value: message},
				Param{Key: "userid", Value: "system"},
			}, http.StatusInternalServerError, `{"text":"Done","type":"text"}`)

			err := Publish(context.Background(), cfg, "system", "dna/user/foobar", `{"foo":"bar"}`, "jwt")
			g.Assert(err).IsNotNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}

func TestSubscribe(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("Subscribe", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			userid := "b287e655461f4b3085c8f244e394ff7e"
			topic := "group/01FWHCZR2NVTKEXGHT5F964TPE"
			key := MockVMQApiRequest("t2gp", "subscribe", Params{
				Param{Key: "topic", Value: topic},
				Param{Key: "userid", Value: userid},
			}, http.StatusOK, `{"text":"Done","type":"text"}`)

			err := Subscribe(context.Background(), cfg, userid, topic)
			g.Assert(err).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}

func TestUnsubscribe(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("Unsubscribe", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			userid := "b287e655461f4b3085c8f244e394ff7e"
			topic := "group/01FWHCZR2NVTKEXGHT5F964TPE"
			key := MockVMQApiRequest("t2gp", "unsubscribe", Params{
				Param{Key: "topic", Value: topic},
				Param{Key: "userid", Value: userid},
			}, http.StatusOK, `{"text":"Done","type":"text"}`)

			err := Unsubscribe(context.Background(), cfg, userid, topic)
			g.Assert(err).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})

	g.Describe("DeleteSubscriptions", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			userid := "b287e655461f4b3085c8f244e394ff7e"
			key := MockVMQApiRequest("t2gp", "clear-topics", Params{
				Param{Key: "userid", Value: userid},
			}, http.StatusOK, `{"text":"Done","type":"text"}`)

			err := DeleteSubscriptions(context.Background(), cfg, userid)
			g.Assert(err).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}

func TestVMQApiCallNamed(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("VMQApiCallNamed", func() {
		g.It("should get alert error", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			message := "foobar"
			topic := "group/01FWHCZR2NVTKEXGHT5F964TPE"
			key := MockVMQApiRequest("t2gp", "publish", Params{
				Param{Key: "topic", Value: topic},
				Param{Key: "message", Value: message},
				Param{Key: "userid", Value: "system"},
			}, http.StatusOK, `{"alert":[{"text":"bad api call"}],"type":"alert"}`)

			err := Publish(context.Background(), cfg, "system", topic, message, "jwt")
			g.Assert(err).IsNotNil()
			g.Assert(err.Error()).Equal("vmq alert error")

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}

func TestVMQStatus(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("VMQSessions", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			response := `vmq status`

			VMQApiURL := cfg.VMQApiURL //os.Getenv("VMQ_API_URL")

			method := "GET"

			// GET /status.json
			url := fmt.Sprintf("%s/status.json", VMQApiURL)
			key := fmt.Sprintf("%s %s", method, url)
			httpmock.RegisterResponder(method, url,
				httpmock.NewStringResponder(http.StatusOK, response))

			text, err := VMQStatus(context.Background(), cfg)
			g.Assert(err).IsNil()
			g.Assert(text).IsNotNil()
			g.Assert(*text).Equal(response)

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}

func TestVMQSessions(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("VMQSessions", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			response := `{"table":[{"client_id":"socialweb-b287e655461f4b3085c8f244e394ff7e-122182","is_online":true,"mountpoint":"","peer_host":"**********","peer_port":48774,"user":"b287e655461f4b3085c8f244e394ff7e"}],"type":"table"}`
			key := MockVMQApiRequest("session", "show", Params{}, http.StatusOK, response)

			rows, err := VMQSessions(context.Background(), cfg)
			g.Assert(err).IsNil()
			g.Assert(rows).IsNotNil()
			g.Assert(len(*rows)).Equal(1)

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}

func TestGetSubscriptions(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GetSubscriptions", func() {
		g.It("should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			// mock request
			userid := utils.GenerateRandomDNAID()
			response := `{"table":[{"client_id":"socialweb-b287e655461f4b3085c8f244e394ff7e-21036b","topic":"user/b287e655461f4b3085c8f244e394ff7e"}],"type":"table"}`
			key := MockVMQApiRequest("t2gp", "topics", Params{
				Param{Key: "userid", Value: userid},
			}, http.StatusOK, response)

			rows, err := GetSubscriptions(context.Background(), cfg, userid)
			g.Assert(err).IsNil()
			g.Assert(rows).IsNotNil()
			g.Assert(len(*rows)).Equal(1)

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}
