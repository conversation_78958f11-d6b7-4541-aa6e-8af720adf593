responses:
  200:
    description: OK
  200empty:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/emptyObject'
  201:
    description: Created
  204:
    description: No Content
  400:
    description: Bad Request
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 400
          message: Bad request
  401:
    description: Unauthorized - The request did not include the required authorization information
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 401
          message: Invalid bearer token
  403:
    description: Forbidden - The requestor is not authorized to make the request
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 403
          message: Forbidden
  404:
    description: Not Found - The requested entity could not be found
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 404
          message: Not found
  406:
    description: Unacceptable - The message is unacceptable
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 406
          message: Message is unacceptable
  409:
    description: Conflict -
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 409
          message: The requested metadata conflicts the group member's existing metadata.
  429:
    description: Too Many Requests - The client has sent too many requests
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 429
          message: Too Many Requests
  500:
    description: Internal Server Error
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/error'
        example:
          code: 500
          message: Internal server error