resource "aws_wafv2_web_acl" "social_web_acl" {
  name        = "t2gp-${local.resource_prefix}-social"
  description = "prevent various attacks on social svc"
  scope       = "REGIONAL"

  default_action {
    allow {}
  }

  rule {
    name     = "common-rules"
    priority = 1

    override_action {
      count {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"

        excluded_rule {
          name = "SizeRestrictions_QUERYSTRING"
        }
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = false
      metric_name                = "t2gp-${local.resource_prefix}-social-common-rules"
      sampled_requests_enabled   = true
    }

  }

  rule {
    name     = "ip-rep-list"
    priority = 2

    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAmazonIpReputationList"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = false
      metric_name                = "t2gp-${local.resource_prefix}-ip-rep-list"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "anon-ip-block"
    priority = 3
    override_action {
      count {}
    }
    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesAnonymousIpList"
        vendor_name = "AWS"
      }
    }
    visibility_config {
      cloudwatch_metrics_enabled = false
      metric_name                = "t2gp-${local.resource_prefix}-social-anon-ip-block"
      sampled_requests_enabled   = true
    }
  }

  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social"
  }, local.tags)

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "t2gp-${local.resource_prefix}-social"
    sampled_requests_enabled   = true
  }
}