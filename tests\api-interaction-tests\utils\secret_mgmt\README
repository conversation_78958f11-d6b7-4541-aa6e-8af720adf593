Use the scripts process.py and secrety.py to make environment variables and secret management easier.

1. Generate the mapping between environment variables and secrets for the workflow yml file.

e.g.:
      TWOK_001_2K_PUBLIC_ID:        ${{ secrets.IAT_TWOK_001_2K_PUBLIC_ID }}
      TWOK_001_EMAIL:               ${{ secrets.IAT_TWOK_001_EMAIL }}
      TWOK_001_PASSWORD:            ${{ secrets.IAT_TWOK_001_PASSWORD }}
      TWOK_001_DISPLAY_NAME:
      TWOK_002_2K_PUBLIC_ID:        ${{ secrets.IAT_TWOK_002_2K_PUBLIC_ID }}
      TWOK_002_EMAIL:               ${{ secrets.IAT_TWOK_002_EMAIL }}
      TWOK_002_PASSWORD:            ${{ secrets.IAT_TWOK_002_PASSWORD }}
      TWOK_002_DISPLAY_NAME:
... and so on.


Run the following (for example) in process.py: 
process(10, twok_dict, get_workflow_env, 'twok_workflow_env')

Copy the contents of the output files and paste them to the workflow yml file.


2. Create/update secrets.

In secret.py:
Ensure 'repo_secrets_url' points to the right repo.
Ensure 'a' contains valid Github user name and personal access token.

Note that there's a limit of 100 secrets per repo. If the limit is exceeded, create/update
secrets will fail.

2.1
Generate a list of secrt names by running (for example) in process.py:

process(10, twok_dict, get_secret_template, 'twok_secret_keys')

2.2
Prepare a file with account info.  For example, 'twok_account_info'. Account info should be
separated by '/' and in the same order as the dictionaries in process.py.  Account info contents can
be found in:

https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/001/vaj3w3pvqxbjy52dfo55ocwgwy

2.3
With 'xxx_secret_keys' and 'xxx_account_info' in the same directory as secret.py, run secret.py. 
