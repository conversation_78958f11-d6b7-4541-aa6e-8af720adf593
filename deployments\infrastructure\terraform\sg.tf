resource "aws_security_group" "grant_public_access_to_mqtt_and_api" {
  name   = "${local.resource_prefix}-ext-sg"
  vpc_id = data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id
  # TODO:
  # Considering restricting to a smaller set
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge({
    "Name" = "${local.resource_prefix}-ext-sg"
  }, local.tags)
}

resource "aws_security_group" "grant_private_access_to_mqtt_and_api" {
  name   = "${local.resource_prefix}-private-ext-sg"
  vpc_id = data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id
  # TODO:
  # Considering restricting to a smaller set
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = local.private_api_whitelist
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge({
    "Name" = "${local.resource_prefix}-private-ext-sg"
  }, local.tags)
}

resource "aws_security_group" "redis_sg" {
  name   = "${local.resource_prefix}-redis"
  vpc_id = data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id
  ingress {
    from_port       = local.REDIS_PORT
    to_port         = local.REDIS_PORT
    protocol        = "tcp"
    security_groups = local.envvars[terraform.workspace]["cluster_name"] == "t2gp-production" ? [data.aws_security_group.eks_pods.id, data.aws_security_group.eks_nodes.id, data.aws_security_group.d2c_lambda.id] : [data.aws_security_group.eks_pods.id, data.aws_security_group.d2c_lambda.id]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = merge({
    "Name" = "${local.resource_prefix}-redis"
  }, local.tags)
}

resource "aws_security_group" "webrtc_sg_private" {
  name   = "webrtc-${terraform.workspace}-api-manager-private-sg"
  vpc_id = data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["**********/16", "${data.aws_eip.jumpbox.public_ip}/32"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["**********/16", "${data.aws_eip.jumpbox.public_ip}/32"]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = merge({
    "Name" = "webrtc-${terraform.workspace}-api-manager-private-sg"
  }, local.tags)
}

resource "aws_security_group" "webrtc_sg_public" {
  name   = "webrtc-${terraform.workspace}-api-manager-public-sg"
  vpc_id = data.aws_eks_cluster.t2gp.vpc_config.0.vpc_id
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = merge({
    "Name" = "webrtc-${terraform.workspace}-api-manager-public-sg"
  }, local.tags)
}