<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { SVGChecked, SVGCheckedDisabled, SVGDash } from '../../assets/icons';
  import type { CustomEventMap } from './customInput.type';

  export let value = '';
  export let checked = false;
  export let type = 'checkbox';
  export let showPartialCheck = false;
  export let disabled = false;
  export let name = 'checkbox';

  const dispatch = createEventDispatcher<CustomEventMap>();

  const onClick = () => {
    dispatch('checkboxClick', {
      checked: !checked,
      value,
    });
  };
</script>

<style>
  .checkbox {
    display: flex;
    align-items: center;
    font-size: 1rem;
    box-sizing: border-box;
  }

  input:hover,
  .label:hover {
    cursor: pointer;
  }

  .checkbox.disabled {
    color: grey;
  }

  .checkbox__input {
    display: grid;
    grid-template-areas: 'checkbox';
    padding: 0.5rem;
  }

  .checkbox__input:focus-within {
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
    outline: none;
  }

  .checkbox__input * {
    grid-area: checkbox;
  }

  .checkbox input {
    opacity: 0;
    width: 1.125rem;
    height: 1.125rem;
    margin: 0;
    z-index: 10;
  }

  .checkbox__control.checked :global(svg) {
    transform: scale(1);
  }

  input:disabled + .checkbox__control {
    color: rgba(255, 255, 255, 0.4);
  }

  .checkbox__control {
    display: inline-grid;
    width: 1.125rem;
    height: 1.125rem;
    border-radius: 0.25em;
    border: 0.125rem solid rgba(255, 255, 255, 0.8);
    box-sizing: border-box;
  }

  .checkbox__control.disabled {
    border-color: rgba(129, 129, 129, 1);
  }
  .checkbox__control :global(svg) {
    transition: transform 0.1s ease-in 25ms;
    transform: scale(0);
    transform-origin: bottom left;
    border-radius: 0.25em;
    width: 1.125rem;
    height: 1.125rem;
    margin-top: -0.1rem;
    margin-left: -0.1rem;
  }

  .label {
    margin-inline-start: 0.5rem;
    opacity: 0.8;
    font-size: 0.875rem;
  }
</style>

{#if type === 'checkbox'}
  <span class="checkbox" class:disabled on:click="{onClick}">
    <span class="checkbox__input">
      <input
        type="checkbox"
        name="{name}"
        value="{value}"
        bind:checked
        disabled="{disabled}"
      />
      <span class="checkbox__control" class:checked class:disabled>
        {#if showPartialCheck}
          <SVGDash />
        {:else if disabled}
          <SVGCheckedDisabled />
        {:else}
          <SVGChecked />
        {/if}
      </span>
    </span>
    <label class="label" for="{name}">
      <slot />
    </label>
  </span>
{/if}
