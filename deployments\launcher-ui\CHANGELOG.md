## [1.19.14](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.13...v1.19.14) (2022-06-10)


### Bug Fixes

* copy dist to public before copying to s3 ([2920c21](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/2920c21f3170eae890d97f505d07d00e64a565f5))

## [1.19.13](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.12...v1.19.13) (2022-06-10)


### Bug Fixes

* aws credentials ([a5e62d4](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/a5e62d46fd34980562b107be8d31b1c1fb616c07))

## [1.19.12](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.11...v1.19.12) (2022-06-10)


### Bug Fixes

* friend presence ([d77eae9](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/d77eae92bab6c4cc9352ca5d22d3ee65ab7ac2f4))
* lint error ([6062e31](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/6062e316446a54e41aaf1a8300c8f0acf9222c9e))

## [1.19.11](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.10...v1.19.11) (2022-05-26)


### Bug Fixes

* update social endpoints ([#81](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/81)) ([73a6372](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/73a63723ea16453f66237945c05bb46393e0d8ad))

## [1.19.10](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.9...v1.19.10) (2021-03-01)


### Bug Fixes

* updated ui-library package ([#68](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/68)) ([15bcf56](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/15bcf5643a9946e3844bb5da676d7fb747be16f6))

## [1.19.9](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.8...v1.19.9) (2021-02-26)


### Bug Fixes

* added new unit tests (TGP-3945) ([#67](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/67)) ([776ce4c](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/776ce4cd12fe94a6774120b242f4c6ac682ec0aa))

## [1.19.8](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.7...v1.19.8) (2021-02-23)


### Bug Fixes

* fixed friends request notification logic ([#66](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/66)) ([2c8068a](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/2c8068afa44326a64507cee3aefc03412e4d3d2a))

## [1.19.7](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.6...v1.19.7) (2021-02-22)


### Bug Fixes

* fixed the request badge logic ([#65](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/65)) ([8445074](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/8445074820502a8b90ce724852e217b29f283d2f))

## [1.19.6](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.5...v1.19.6) (2021-02-22)


### Bug Fixes

* fixed the friends list stack ranking order ([#64](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/64)) ([e8aebac](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/e8aebac6a403941bbdfe031ac42de7aae131564e))

## [1.19.5](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.4...v1.19.5) (2021-02-19)


### Bug Fixes

* fixed the button icon ([#63](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/63)) ([f04ab49](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/f04ab4961a90f7cf1c05939141bc50c9d0034929))

## [1.19.4](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.3...v1.19.4) (2021-02-19)


### Bug Fixes

* updated button style ([#62](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/62)) ([818c734](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/818c7342ab2582b40b436623a965f2063494eb1f))

## [1.19.3](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.2...v1.19.3) (2021-02-18)


### Bug Fixes

* updated import friend tile (TGP-3811) ([#61](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/61)) ([cbc4fa9](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/cbc4fa90901ca0691f3148d52b289cf33a3bbf82))

## [1.19.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.1...v1.19.2) (2021-02-17)


### Bug Fixes

* fixed the user menu style ([#60](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/60)) ([e25f82e](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/e25f82efc9dafdbf8faf9890bfeea2302c8285f3))

## [1.19.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.19.0...v1.19.1) (2021-02-13)


### Bug Fixes

* fixed the style ([#58](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/58)) ([ef159e4](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/ef159e4cac8af7fab126a21f9d7d24d282a3644a))

# [1.19.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.18.2...v1.19.0) (2021-02-12)


### Features

* updated steam import UI ([#57](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/57)) ([8fb7304](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/8fb73041c0a1914c90ebacd6f8b542a0b4dc725f))

## [1.18.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.18.1...v1.18.2) (2021-02-11)


### Bug Fixes

* fixed the data query (TGP-3832) ([#56](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/56)) ([69f0193](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/69f0193783c78d080fd935a2fafd0070d5a32739))

## [1.18.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.18.0...v1.18.1) (2021-02-10)


### Bug Fixes

* fixed the style ([#55](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/55)) ([6b19aca](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/6b19aca818b9cc2fc840a33e8d96841ad2ab24d0))

# [1.18.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.17.1...v1.18.0) (2021-02-10)


### Features

* updated add friend workflow (TGP-3830) ([#54](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/54)) ([0442162](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/04421629c4204705f2b0df9679c3ede5ca3bf4aa))

## [1.17.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.17.0...v1.17.1) (2021-02-05)


### Bug Fixes

* updated the presence menu item ([#53](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/53)) ([d666eaf](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/d666eaff5e210e7a63e7b635267ee2f7ca3f1f09))

# [1.17.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.16.3...v1.17.0) (2021-02-05)


### Features

* added user presence menu (TGP-3788) ([#52](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/52)) ([6c975f5](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/6c975f510b7e9492b1ea2484f9290c34c8a575ed))

## [1.16.3](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.16.2...v1.16.3) (2021-02-02)


### Bug Fixes

* fixed the routing ([#51](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/51)) ([2dfd267](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/2dfd2671ee12d09cfb045c7b03543979946ee5e0))

## [1.16.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.16.1...v1.16.2) (2021-02-02)


### Bug Fixes

* routing fix ([#50](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/50)) ([9a132ad](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/9a132ad1c11f0ae332b0ef4ce1b1d1648058d57a))

## [1.16.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.16.0...v1.16.1) (2021-02-02)


### Bug Fixes

* fixed the routing ([#49](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/49)) ([29faef7](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/29faef7e2cea490d14bdebf3a1079b2f7a15ec71))

# [1.16.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.15.0...v1.16.0) (2021-02-02)


### Features

* added tooltip ([#48](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/48)) ([8c9d2db](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/8c9d2db76de15fa2dd2f6e95482fa15a1ae1b3bf))

# [1.15.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.14.0...v1.15.0) (2021-02-01)


### Features

* updated unit tests (TGP-3648) ([#47](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/47)) ([08c7a54](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/08c7a54a901dea9e59c8c442a570807cdf0d4f5d))

# [1.14.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.13.1...v1.14.0) (2021-01-27)


### Features

* added api service (TGP-727) ([#46](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/46)) ([4a3d7e9](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/4a3d7e9642195d737796358d54eee0d673ec02a9))

## [1.13.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.13.0...v1.13.1) (2021-01-26)


### Bug Fixes

* refactor fix ([#45](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/45)) ([44e0f7f](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/44e0f7f25d472a02abeb81abaa7ea414b446abe1))

# [1.13.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.12.4...v1.13.0) (2021-01-26)


### Features

* added log service (TGP-3019) ([#44](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/44)) ([f3b7711](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/f3b7711b7784e2f6dc4aa6f23fdea9e3fbbee1dd))

## [1.12.4](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.12.3...v1.12.4) (2021-01-25)


### Bug Fixes

* fixed the request page ([#43](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/43)) ([01a1958](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/01a1958c8237968d8d0fb71804df93a319e8e2ca))

## [1.12.3](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.12.2...v1.12.3) (2021-01-25)


### Bug Fixes

* updated request page (TGP-2944) ([#42](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/42)) ([05dc2c9](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/05dc2c969a050b5e33f5b8cac24014ba67375152))

## [1.12.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.12.1...v1.12.2) (2021-01-23)


### Bug Fixes

* router fix ([#41](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/41)) ([d090080](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/d090080cfcbf9090faa878a4f92b0117ea0b8118))

## [1.12.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.12.0...v1.12.1) (2021-01-23)


### Bug Fixes

* fixed minor issue ([#40](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/40)) ([42a01de](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/42a01de700085847928510c40230ef6c8215f50e))

# [1.12.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.11.0...v1.12.0) (2021-01-22)


### Features

* refactor work ([#39](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/39)) ([59ecb47](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/59ecb472dea830fdb6ce896352e7ea13f5996637))

# [1.11.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.10.0...v1.11.0) (2021-01-21)


### Features

* added friend request page (TGP-2927) ([#38](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/38)) ([ec304ec](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/ec304ec09d9d0864c12905150814f6bcba493dba))

# [1.10.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.9.1...v1.10.0) (2021-01-19)


### Features

* added mqtt service (TGP-2925) ([#37](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/37)) ([75baec0](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/75baec0bd2f3ed5f0aa480a7a8614b012ee8122f))

## [1.9.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.9.0...v1.9.1) (2021-01-18)


### Bug Fixes

* patch fix for select all checkbox ([#36](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/36)) ([540d09d](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/540d09d22dd2c0e4e4003a9d86c4954300309d8a))

# [1.9.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.8.1...v1.9.0) (2021-01-18)


### Features

* added retrieving steam friends ui (TGP-2852) ([#35](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/35)) ([3219b69](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/3219b69080e732b0d10b284b01a4ffc9251021b4))

## [1.8.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.8.0...v1.8.1) (2021-01-13)


### Bug Fixes

* fixed the user presence status ([#34](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/34)) ([bd8d3dd](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/bd8d3dd8e07814c8b3d58284e8e926cdf99e89b3))

# [1.8.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.7.0...v1.8.0) (2021-01-13)


### Features

* added friends list page (TGP-2905) ([#33](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/33)) ([4dac1df](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/4dac1dfec6546c2d38853641e8a6fd447a6e253d))

# [1.7.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.6.0...v1.7.0) (2021-01-11)


### Features

* added connect to steam page (TGP-2908) ([#30](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/30)) ([ddab9e9](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/ddab9e9a43e45603d2c4c80ff9d0137c82c418d7))

# [1.6.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.5.2...v1.6.0) (2020-12-09)


### Features

* added data query logic ([a339574](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/a339574a956cef555eddeb5aa024a7a1ddf2a9c3))

## [1.5.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.5.1...v1.5.2) (2020-12-03)


### Bug Fixes

* updated components ([#27](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/27)) ([9100cc7](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/9100cc7c5592ba3fdcaf14486997b9078c29d6e8))

## [1.5.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.5.0...v1.5.1) (2020-12-02)


### Bug Fixes

* updated background png (TGP-2942) ([#26](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/26)) ([94e5238](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/94e5238b27ec583ca02c0d0650e1429a017aa8f3))

# [1.5.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.4.0...v1.5.0) (2020-12-01)


### Features

* added friends list page with empty and error state (TGP-2942) ([#25](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/25)) ([9a93868](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/9a93868adea2892133d323006200d8a239780583))

# [1.4.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.3.2...v1.4.0) (2020-11-20)


### Features

* added localization logic ([#24](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/24)) ([85c190c](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/85c190ceb706ec7e9b112bc0b418f91871dd7694))

## [1.3.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.3.1...v1.3.2) (2020-11-18)


### Bug Fixes

* fixed the provider typing ([#23](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/23)) ([f0478b2](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/f0478b24fd22a5be5d56d205fbbc0417b8872882))

## [1.3.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.3.0...v1.3.1) (2020-11-18)


### Bug Fixes

* updated typing ([#22](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/22)) ([b678104](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/b6781040a78f4caf2b5f74194243e45b2e4f3124))

# [1.3.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.2.3...v1.3.0) (2020-11-18)


### Features

* added transport service ([#21](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/21)) ([422183a](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/422183af9c2cefb14bc11fccd29b98584ad5a86d))

## [1.2.3](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.2.2...v1.2.3) (2020-11-10)


### Bug Fixes

* updated readme ([#20](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/20)) ([31202c0](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/31202c09450c4cb9bf070223de76cf6833db520f))

## [1.2.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.2.1...v1.2.2) (2020-11-09)


### Bug Fixes

* fixed the mock ([#19](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/19)) ([cc41dbd](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/cc41dbd4326360d51139b17c2a5a79f5d4a68396))

## [1.2.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.2.0...v1.2.1) (2020-11-09)


### Bug Fixes

* updated tests structure ([#18](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/18)) ([a88b025](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/a88b025dfbec3bc51c2a283c800a53a536da51f0))

# [1.2.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.1.0...v1.2.0) (2020-11-06)


### Features

* added locale ([#17](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/17)) ([2cd61c8](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/2cd61c87fecb81626756d88d5813d2821baacc87))

# [1.1.0](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.7...v1.1.0) (2020-11-05)


### Features

* added sample components ([#16](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/16)) ([f3d418a](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/f3d418a013d819b23156acf642cba014ee5aded2))

## [1.0.7](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.6...v1.0.7) (2020-11-03)


### Bug Fixes

* updated file structure for integration demo ( TGP-2585 ) ([#15](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/15)) ([9fae5b5](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/9fae5b5bc150d2b5854c92f9ff6d811e7d156c8e))

## [1.0.6](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.5...v1.0.6) (2020-10-30)


### Bug Fixes

* added declaration generation ([#13](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/13)) ([9987b11](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/9987b11cb4f8e39595dea8951058d39e5ec3474d))

## [1.0.5](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.4...v1.0.5) (2020-10-29)


### Bug Fixes

* removed postintall script ([#12](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/12)) ([7ee40a1](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/7ee40a1883fa0cdad4036616bc9c8d7068d7d22c))

## [1.0.4](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.3...v1.0.4) (2020-10-29)


### Bug Fixes

* added danger ([#11](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/11)) ([452c073](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/452c073eac8e9ebfff37b3565b1c61de48555b29))

## [1.0.3](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.2...v1.0.3) (2020-10-28)


### Bug Fixes

* updated package json ([391c194](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/391c19436039e6acfe516faf8ec90e1159691811))

## [1.0.2](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.1...v1.0.2) (2020-10-28)


### Bug Fixes

* added size snapshot ([#10](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/10)) ([0b4e194](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/0b4e1946e9bc7afdacde63429a35cebcc1165d4d))

## [1.0.1](https://github.com/take-two-t2gp/t2gp-social-frontend/compare/v1.0.0...v1.0.1) (2020-10-28)


### Bug Fixes

* updated release format ([#9](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/9)) ([9f080d8](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/9f080d8c5d47bc41c1c5c7c9aa9d7058ca077883))

# 1.0.0 (2020-10-28)


### Bug Fixes

* added npm token ([#7](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/7)) ([f41c672](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/f41c6722aac7b6104ba20864f3a6c5576895efea))
* added release in the package json ([#3](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/3)) ([1361220](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/136122032f3d55d65dc4b5c36219054fd60eb8cc))
* fixed the build ([#5](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/5)) ([11792c8](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/11792c86609ab8da6f85c0d738abb62db6feac3b))
* removed npm token ([#4](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/4)) ([096c8f8](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/096c8f88666e0ffa1e34f1a994d02a9175b6cad9))
* updated scope ([#8](https://github.com/take-two-t2gp/t2gp-social-frontend/issues/8)) ([5bb753b](https://github.com/take-two-t2gp/t2gp-social-frontend/commit/5bb753b683c1c830f2d694647d29c9d063f2b52f))
