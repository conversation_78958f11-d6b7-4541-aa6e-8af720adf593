name: Create Sandbox Env

on:
  pull_request:
    branches:
      - develop
    types:
      - labeled
      - synchronize
      - edited
      - opened

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

concurrency: pr-${{ github.head_ref }}
jobs:
  build-social-service:
    name: Build social service docker images
    runs-on: [t2gp-arc-linux]
    env:
      WORK_DIR: ./
    outputs:
      image_tag: ${{ steps.build-docker.outputs.image_tag }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_ecr_rw
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Get commit if push to develop
        id: declare-envs
        run: |
          echo VER=${GITHUB_SHA::8} >> $GITHUB_ENV
      - name: Build docker images
        id: build-docker
        run: |
          REF=$(echo "${{ github.event.pull_request.head.sha }}" | awk -F'/' '{print $3}')
          if [[ "$REF" == release* ]]; then
            RC=$(echo "${{ github.event.pull_request.head.sha }}" | awk -F'/' '{print $4}')
            ENV=release
            echo "image_tag=${RC}-${VER}" >> $GITHUB_OUTPUT
          else
            echo "image_tag=sandbox-$VER" >> $GITHUB_OUTPUT
          fi
          DATE=$(date "+%Y-%m-%d-%H-%M-%S")
          docker build --progress plain -t t2gp-social-api . \
            --build-arg VERSION="${GITHUB_SHA::8}" \
            --build-arg GIT_HASH="${GITHUB_SHA}" \
            --build-arg BUILD_DATE="$DATE" \
            --build-arg git_token=${{secrets.SERVICE_ACCOUNT_GH_PAT}}
      - name: Push api image
        uses: jwalton/gh-ecr-push@v1
        with:
          access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          region: us-east-1
          local-image: 't2gp-social-api'
          image: 't2gp-social-api:${{ steps.build-docker.outputs.image_tag }}'
      - name: Upload git metadata to datadog
        run: |
          curl -L --fail "https://github.com/DataDog/datadog-ci/releases/latest/download/datadog-ci_linux-x64" --output "datadog-ci"
          chmod +x ./datadog-ci
          ./datadog-ci git-metadata upload
        env:
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}
  run-helm:
    needs: [build-social-service]
    name: 'Run Helm'
    runs-on: [t2gp-arc-linux]
    env:
      CLUSTER: t2gp-non-production
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
    outputs:
      release_name: ${{ steps.output_info.outputs.release_name }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          ref: ${{github.sha}}
          submodules: recursive
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          always-auth: false
          node-version: '22.x'
          registry-url: https://npm.pkg.github.com
          scope: '@take-two-t2gp'
      - name: set branch name env if pull pull_request
        run: |
          echo MQTT_ENV=false>> $GITHUB_ENV
          echo DATASTORE_ENV=false>> $GITHUB_ENV
          REF=$(echo "${{ github.ref }}" | awk -F'/' '{print $3}')
          if [[ "$REF" == release* ]]; then
            echo RELEASE_NAME="release" >> $GITHUB_ENV
          else
            echo RELEASE_NAME="pr-${{github.event.pull_request.number}}" >> $GITHUB_ENV
          fi
          echo MQTT_URL=wss://social-service-develop.d2dragon.net/mqtt >> $GITHUB_ENV
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1

      - name: set mqtt enabler
        if: contains(github.event.pull_request.labels.*.name, 'create-mqtt')
        run: |
          echo "SUBMODULE_HASH=$(git ls-tree ${{github.sha}} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
          echo MQTT_ENV=true>> $GITHUB_ENV
          echo MQTT_URL=wss://social-service-${{env.RELEASE_NAME}}.d2dragon.net/mqtt >> $GITHUB_ENV
      - name: set datastore
        if: contains(github.event.pull_request.labels.*.name, 'create-datastore')
        run: |
          echo DATASTORE_ENV=true>> $GITHUB_ENV
          echo "CLUSTER_OIDC_ID=$(aws eks describe-cluster --name ${{ env.CLUSTER }}  --query "cluster.identity.oidc.issuer" --output text | cut -d '/' -f 5)" >> $GITHUB_ENV

      - name: Helm Deploy social-service
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.7
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          raise-pr: false
          environment: ${{ env.RELEASE_NAME }}
          helm-values: 'social-api.groupsApi.image.tag=${{ needs.build-social-service.outputs.image_tag }},social-api.groupsApi.commitSha=${{github.sha}},global.ddEnv=${{ env.RELEASE_NAME }}'
          sandbox: true
      - name: Update plugin with script
        if: env.MQTT_ENV == 'true'
        working-directory: deployments
        env:
          TARGET_ENV: ${{ env.RELEASE_NAME }}
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Delete SSM Param, if used
        if: env.MQTT_ENV == 'false'
        run: |
          set -x
          if aws ssm get-parameter --name /social/mqtt/${{ env.RELEASE_NAME }}/t2gp-plugin-version; then
            aws ssm delete-parameter --name /social/mqtt/${{ env.RELEASE_NAME }}/t2gp-plugin-version
          fi
          exit 0
      - name: Comment PR
        uses: thollander/actions-comment-pull-request@v1
        continue-on-error: true
        with:
          message: |
            ${{ steps.helm_deploy.outputs.helm-output  }}
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "${{env.RELEASE_NAME}}", "version": "${{needs.build-social-service.outputs.image_tag}}", "api_url":"https://social-service-${{env.RELEASE_NAME}}.d2dragon.net/v1", "api_private_url":"https://social-service-${{env.RELEASE_NAME}}-private.d2dragon.net", "mqtt_url":"${{env.MQTT_URL}}" }'
      - name: Output Info
        id: output_info
        run: |
          echo "release_name=${{ env.RELEASE_NAME }}" >> $GITHUB_OUTPUT

  post-deploy-update:
    needs: [build-social-service, run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: ${{ needs.run-helm.outputs.release_name }}
      version: ${{ needs.build-social-service.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      api_test_note: 'deploy ${{ needs.run-helm.outputs.release_name }}'
    secrets: inherit
