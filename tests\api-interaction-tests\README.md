# API Interaction Tests
These are API tests based on Jest for the Social Service.

## Setup
The following is for running the tests in WSL, and with No<PERSON> in Docker. They can be adjusted for other environments.
### 1. Install [WSL](https://learn.microsoft.com/en-us/windows/wsl/)

### 2. Docker
Docker is the easiest way to make WSL work with VPN. Othewise, it's not needed.
#### 2.1 Install [Docker Desktop](https://www.docker.com/products/docker-desktop/)
#### 2.2 Build Docker Image
Build image with:
```
cd docker
docker build . --tag social_interaction_test
```

List images:
```
docker images
```

With this image, commands requiring Node can be prefixed with the following:
```
docker run --rm -v <repo_root>:/code/social-auto -it social_interaction_test:latest
```

For example:
```
docker run --rm -v <repo_root>:/code/social-auto -it social_interaction_test:latest npm <param1> <param2> ...
```


### 3. Install Packages
```
npm install
```

### 4. Generate Test Accounts
See [Test Account Management Tool](https://github.com/t2gp-harry/social-auto/blob/main/utils/account_mgmt/README.md) regarding how to allocate test accounts and generate test account files such as `.env.twok_accounts`.


## Run Tests
As an example, the following command runs the test case `send friend invitation` of the `createFriendship` test suite.
```
npm run test -- --testPathPattern='integration/friend/createFriendship.test.ts' -t='send friend invitation'
```

If running inside the Docker image, note the extra `--` required.
```
docker run --rm -v <repo_root>:/code -it social_interaction_test:latest npm run test -- --testPathPattern='integration/friend/createFriendship.test.ts' -t='send friend invitation'
```

## Test Run Control
### 1. Test Suites, Test Cases, and Test Organization
In Jest, a .test.ts file is a test suite. `Describe` blocks group similar `it` blocks, and an `it` block is a test case.

The name of a test suite is the name of a .test.ts file. The name of a test case is the combination of the name of the outter `describe` block(s) and the name of the `it` block.


### 2. Tags and Tag Filters for Test Run Control
Tags and tag filters work together to provide finer control of which test cases are run during execution.

#### Tags
Tags can be inserted in `describe` or `it` names.

Tag Insertion Rules:
- Tags are surrounded by square brackets.
- Multiple tags can be inserted in either of these styles: `[tag1 tag2 tag3]` or `[tag1]` `[tag2]` `[tag3]`.
- Tags can appear anywhere in the name. Note that tags declared in a `describe` name apply to all test cases under it.
- Tags can appear in any order.

#### Tag Filters
In a tag filter, a tag can be either a `run tag` or an `ignore tag`.  Ignore tags have priority over run tags.

Tag filters are passed to Jest via the `-t` option during test execution.  The following are some common usage scenarios:

Run all test cases, except for those with ignore tags `broken` or `lowprio`.
```
-t='^(?!.*\[.*\b(broken|lowprio)\b.*\]).*'
```

Run only tests with the run tag `happy`, except for those with ignore tags `broken` or `lowprio`.
```
-t='^(?!.*\[.*\b(broken|lowprio)\b.*\])(?=.*\[.*\bhappy\b.*\]).*'
```

Run only tests with the run tags of either `happy` **OR** `highprio`, except for those with ignore tags `broken` or `lowprio`.
```
-t='^(?!.*\[.*\b(broken|lowprio)\b.*\])(?=.*\[.*\b(happy|highprio)\b.*\]).*'
```

Run only tests with the run tags of both `happy` **AND** `friend`, except for those with ignore tags `broken` or `lowprio`.
```
-t='^(?!.*\[.*\b(broken|lowprio)\b.*\])(?=.*\[.*\bhappy\b.*\])(?=.*\[.*\bfriend\b.*\]).*'
```

The following tags are currently used in the test scripts:
- ignore tags:  **lowprio, broken**
- run tags:     **happy**


### 3. Test Reporting Rules with Tags
Tests with `lowprio` tags are not shown in the report.

Tests with `broken` tags are shown as `skipped`.

Since tags are removed from the report, when inserting tags, leave no space between a square bracket and the test case name for better readability.


## Test Account Management Classes
`integration/lib/config.ts` provides classes for managing test accounts.  They provide a convenient way to access information of multiple test accounts, provide aliases to test accounts, perform batch operations to test accounts (login, logout, link, unlink, etc.), and more.

### 1. Account Aliases
Usage patterns should be apparent from the existing scripts.  However, there's a subtlety about account management object instantiation that's best illustrated with some examples.

An account management object is instantiated by specifying the number of accounts and an array of aliases.  For example:
```
let usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
```

If aliases aren't specified, default aliases are used.  Default aliases are determined from the environment variables (see `.env`).  For example, the environment variable `TWOK_001_2K_PUBLIC_ID` means the default alias is `001`.
```
let usersTwok = new TwokAccounts(2);
```

If the specified number of accounts exceeds what's defined in the environment variables, the unexpected will happen, obviously.

If the specified number of accounts is less than the total defined number of test accounts, and if the aliases aren't specified, then the corresponding number of default aliases are used from the beginning of a sorted default alias array.  For example, if there were 10 2k accounts and their default aliases are `001`, `002`, ..., `010`, with the above statement, aliases `001` and `002` are used.

### 2. Simultaneous Login with Multiple App IDs for 2K Accounts
2K account objects can manage login tokens from multiple app IDs.  The following describes the usage:

Suppose we have a 2K account management object.
```
usersTwok = new TwokAccounts(2);
```

Passing in an array of app IDs to the login function would cause login with each of the app IDs.
```
await usersTwok.acct["001"].login({appIdArray: [config.apps.alphaRace.appId, config.apps.socialServiceProduct.appId]});
```

Tokens can be looked up with the app IDs.
```
console.log(usersTwok.acct["001"].accessTokenDict[config.apps.alphaRace.appId]);
console.log(usersTwok.acct["001"].accessTokenDict[config.apps.socialServiceProduct.appId]);
```

If app IDs aren't specified, the default app ID would be used.  The default app is specified in the `.env` file.
```
await usersTwok.acct["001"].login({});
```

The token can be accessed by either of these two ways.
```
console.log(usersTwok.acct["001"].accessToken);
console.log(usersTwok.acct["001"].accessTokenDict[config.apps.default.appId]);
```

Passing in an array of app IDs to the logout function would logout the corresponding tokens and clear `accessTokenDict`.  Passing in a subset of the app IDs used for login is also allowed.
```
await usersTwok.acct["001"].logout({appIdArray: [config.apps.alphaRace.appId, config.apps.socialServiceProduct.appId]});
```

If app IDs aren't specified, the default app ID would be used.  Both `accessToken` and `accessTokenDict` are cleared.
```
await usersTwok.acct["001"].logout({});
```

The above applies to `loginAll` and `logoutAll` as well.  The same is done on all accounts.


## Consideration When Using "Expect"
Please bear in mind that when an `expect` fails, the remaining test cases are not run. As a result, having multiple `expect` back-to-back could cause critical runtime info to be missing, and thus make debugging difficult.

The recommended way is to use `toMatchObject` to write a single `expect` statement. See [example](https://github.com/t2gp-harry/social-auto/commit/e3d8a5eb7e73e85657daf78b8336e854f655eb8a) (in this example, "body" is used incorrectly. See below for explanation).

Note that the response object's property representing body, is actually "\_body" instead of "body". This is the [correction](https://github.com/t2gp-harry/social-auto/commit/dd1b503cb3f6b44ea2e4b91a4ce2d4b58642cccc) to the above example. Also note that this body -> \_body change seems to be a side effect of upgrading node-fetch to 2.6.7. You might need to remove node\_modules and run "npm install" again.

If the expected object contains an array, the response shouldn't be hardcoded, since the order of array items isn't guaranteed. See a [recommended approach](https://github.com/t2gp-harry/social-auto/commit/dcd53be78f1a7eb26ea79d026447209296bdb4f6).


## A Note About Concurrency
Note that tests are [configured](https://github.com/t2gp-harry/social-auto/blob/e4a6ef666e1f27a3b92619fefc5a82596c3e21ab/package.json#L37-L39) to run serially.  Test accounts and Social-related resources (e.g. friends and groups) are subject to resource contention.  If we are to consider running tests in parallel (perhaps to save time), we need a resource booking system in place.

Since tests are run serially, test verification can also be written in a simpler manner.  For example, if a test user is expected to have no groups at the end of a test, we can simply check if the group list is empty, for tests running serially.  If tests are run in parallel, we have to check if the group list does not contain the group that was created for this test, since the group list could contain groups created from other tests running in parallel.


## Guidelines for Scripting a Test Case
### 1. Comments
Make comments in-line to describe the scenario. Also state the expected results at the verification step.

### 2. Verification with "Expect"
Typically a test case performs some API calls to create a test scenario, and call a "GET" (such as `getGroupInfo`) as verification. For the former, verification is required for each API call, but checking status is sufficient. Compose the "expected object" for `toMatchObject` only for the verification calls for the "GET". Use _expectedXXX_ and _actualXXX_ variable naming.

### 3. Guidelines for "Describe" and "It"
Refer to [this](https://testthat.r-lib.org/reference/describe.html) for guidelines on how to describe test scenarios with `describe` and `it`.

### 4. "Describe", Test Case Grouping, and Resource Management
Use `describe` to group test cases of the same nature or the same setup/teardown.

Put all clean-up related code in an `afterEach` function. If it's placed in the test case after an `expect`, when `expect` fails, clean-up will not be run.

Global setup/teardown generally decreases the clarity of a test case. Local setup/teardown is preferred.

### 5. Avoid Magic Numbers
Instead of using a number or string literal, use variables to give them some meaning.

### 6. Naming Scheme for Test Users
Use generic users and name them according to the scenario, and gradually phase out "invite/invited/restricted".

### 7. Variable Naming Style
Camel case, and adjective-noun. For example, group leader's token would be _leaderToken_ rather than _tokenLeader_. The ID of a group would be _groupId_ rather than _idGroup_.

### 8. Parameter Formatting Style
Either in the same line, or in multiple lines and the same column, for better readability.
