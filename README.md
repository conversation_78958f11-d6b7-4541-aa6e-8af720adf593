# T2GP Social Service

[![Quality Gate Status](https://sonarqube.take2games.com/api/project_badges/measure?project=t2gp-social-service&metric=alert_status&token=5ab4d8baccd10e249ceae0c22394791b52ff0e0a)](https://sonarqube.take2games.com/dashboard?id=t2gp-social-service)

## Deployed Environments

https://tk2-d2c.slack.com/archives/C03CTP9JY5V/p1677531090605559

## Discovery Endpoint

Discovery endpoint will be used by integrators to receive the corresponding environment endpoint for their product.
For this to work, the corresponding config needs to be added to the **t2gp-social** bucket, under `s3://t2gp-social/discovery/<environment>`

For example, if a product with App ID `WHDABEXXGO4X0VWN5ICW` needs to fetch its **staging** environment, then the config needs to be added to `s3://t2gp-social/discovery/staging/WHDABEXXGO4X0VWN5ICW/config.json`.

Each config.json file is a json consists of

```json
{
  "httpUrl": "<PRODUCT_ENV_SPECIFIC_ENVIRONMENT_ENDPOINT>",
  "mqttUrl": "<GENERAL_ENV_MQTT_ENDPOINT>"
}
```

## Deployed Game Specific Environments

Visit https://social-env-ver-mapping.d2dragon.net/ to get a list of deployed game specific environments.
This is WIP and will be reformatted to table soon.

### Redis Commander 

You can view redis data using the redis commander web ui.

- Non Production (develop, loadtesting, integration): https://redis-ui-non-production.d2dragon.net/
- Production (staging, cert, production): https://redis-ui-production.d2dragon.net/

### Deploy notes

For every environment, API will be updated when a new commit is pushed (develop) or deployed (staging/production).

MQTT tags will be updated but the pods won't be restarted; only social plugin will be hotswapped on the fly. Normally this will be enough

If there is a need to restart the MQTT pods, all mqtt pods need to be killed first. The StatefulSet in k8s will bring the pods backup by order.

1. Do a deploy first. This will deploy API and update the tag on MQTT.
2. Scale MQTT down to 0 pod `kubectl scale --replicas=0 sts -n social-service social-service-mqtt-<env>`
3. Wait until all pods are gone.
4. Re-scale back to the desired count. `kubectl scale --replicas=2 sts -n social-service social-service-mqtt-<env>`

> In the case where a new mqtt container is spawned, it will need to fetch the plugin version from AWS Parameter Store. This value is updated after the Helm deploy & plugin swap (if it happens)
> The deployed plugin VER are found here
> https://us-east-1.console.aws.amazon.com/systems-manager/parameters/?region=us-east-1&tab=Table#list_parameter_filters=Name:Contains:%2Fsocial%2Fmqtt

#### Staging Deployment

1. Get [Release](https://github.com/take-two-t2gp/t2gp-social-service/releases) you want to get from a prior integration build or from [AWS ECR](https://us-east-1.console.aws.amazon.com/ecr/repositories/private/354767525209/t2gp-social-api?region=us-east-1).
2. Go to [Staging Deploy](https://github.com/take-two-t2gp/t2gp-social-service/actions/workflows/deploy-staging.yml) page and enter the tagged version you want to deploy. (e.g. `v0.1.0-f1e56a`)

#### Production Deployment

1. Use [Release](https://github.com/take-two-t2gp/t2gp-social-service/releases) created by in the staging deployment
2. Go to [Production Deployment](https://github.com/take-two-t2gp/t2gp-social-service/actions/workflows/deploy-production.yml) page and enter the tagged version you want to deploy. (e.g. `v0.1.0-f1e56a`)
3. Find [deploy key t2gp-social-service](https://enterprise-console.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/aolt526kenhoxonfxpmzotwmtu) in 1Password and enter the deployment key.
4. Wait for the deployment to finish and ask QA in `#t2gp-scrum-social` channel to check

#### Cert Deployment

1. Use [Release](https://github.com/take-two-t2gp/t2gp-social-service/releases) created by in the staging deployment
2. Go to [Cert Deployment](https://github.com/take-two-t2gp/t2gp-social-service/actions/workflows/deploy-cert.yml) page and enter the tagged version you want to deploy. (e.g. `v0.1.0`)
3. Find [deploy key t2gp-social-service](https://enterprise-console.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/aolt526kenhoxonfxpmzotwmtu) in 1Password and enter the deployment key.
4. Wait for the deployment to finish and ask QA in `#t2gp-scrum-social` channel to check

#### Loadtesting Deployment

1. Go to [Cert Deployment](https://github.com/take-two-t2gp/t2gp-social-service/actions/workflows/deploy-loadtesting.yml) page and enter the image version you want to deploy. (8 character length git sha OR git tag)
2. Find [deploy key t2gp-social-service](https://enterprise-console.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/aolt526kenhoxonfxpmzotwmtu) in 1Password and enter the deployment key.
3. Wait for the deployment to finish.

Note that loadtesting deployment will be tore down every midnight to save on costs.

## Monitoring

Monitoring is done through datadog.

- [Social Service Dashboard](https://app.datadoghq.com/dashboard/uwg-mur-nfv/social-service?from_ts=1636052020274&to_ts=1636055620274&live=true)

## Restart Pods Safely

Must use YVR global select vpn gateway

https://rollouts-production.d2dragon.net/rollouts/social-service

https://rollouts-non-production.d2dragon.net/rollouts/social-service

To use without YVR gateway using kubectl.  (might require aws SRE creds)

```
kubectl config use-context t2gp-production
kubectl port-forward service/argo-rollouts-dashboard -n argo-rollouts 3100:3100
http://localhost:3100
```

## Troublehshooting

1. Install [Kubenetes Lens](https://k8slens.dev/)
2. Create kubectl configs for `production` and `non-production` clusters

```
aws eks --region us-east-1 update-kubeconfig --name t2gp-non-production
aws eks --region us-east-1 update-kubeconfig --name t2gp-production --kubeconfig ~/.kube/config.prod
```

3. Use Lens to look at the logs or create a shell in the `social-service` namespace pods.
4. Also use datadog to view logs or traces to help troubleshoot further.

## How To Build

## Prerequisites

### Create `.env` files

You can get the development .env files here:
https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/5herwn7efaj3l6z3oyzn2fqbqu

You need to create two `.env files.

- `t2gp-social-service/.env`
- `t2gp-social-service/deployments/vmq-plugin-social/.env`

### Windows

- Install [docker desktop](https://www.docker.com/products/docker-desktop)
- Install [scoop](https://scoop.sh)
- run `scoop install go`
- run `scoop install make`
- run `scoop install busybox`

### Mac

- Install [docker desktop](https://www.docker.com/products/docker-desktop)
- Install [homebrew](https://brew.sh)
- run `brew install go`
- run `xcode-select --install`

## Build

- run `git submodule update --init --recursive` from the root directory

  - if you use git over ssh, and get challenged with username/password for github.com, please append to your `.gitconfig`

  ```
  [url "**************:"]
        insteadOf = https://github.com/
  ```

  - note: if you are unable to install the `dna-common` module, and are using powershell, make sure you able to run `ssh -T *************************`from the **Git Bash** terminal as well. Ensure the ssh config is setup in the git bash terminal as well as powershell
  - note2: alternatively you can create a PAT (https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/creating-a-personal-access-token) in the github.take2games.com account (different github than github.com). then create a ~/.netrc file. in it, include a line:
    ```
    machine github.take2games.com login <USERNAME> password <GITHUB_PAT>
    ```

- run `make vmq-build` to build the VMQ plugin
- run `make vmq-start` to start VerneMQ MQTT Server
- run `make build` to build the social-api server

  - Make sure your installed version of go is `>= 1.21`
  - Build may fail because it doesn't accept user credentials. As an option, you can generate and set up Github access token. On Github, go to **Settings** -> **Developer settings** -> **Personal access tokens**. After generating a token, run
    ```
    git remote set-url origin https://<USER_NAME>:<TOKEN>@github.com/take-two-t2gp/t2gp-social-service.git
    git pull
    ```
  - If you run into the following error:

    ```
    go: github.take2games.com/2kg-coretech/dna-common@v0.1.36: reading github.take2games.com/2kg-coretech/dna-common/go.mod at revision v0.1.36: git ls-remote -q origin in C:\Users\<USER>\go\pkg\mod\cache\vcs\43ee0db1e546d6e1efce71db8587447de2507afc7c03b85ec05c1cc2358a14f3: exit status 128:
            fatal: Cannot prompt because user interactivity has been disabled.
            fatal: could not read Username for 'https://github.take2games.com': terminal prompts disabled
    Confirm the import path was entered correctly.
    If this is a private repository, see https://golang.org/doc/faq#git_https for additional information.
    ```

    Go to [T2 Github](https://github.take2games.com), and generate a personal access token as mentioned above. Note your username here, it should be `[FIRSTNAME]-[LASTNAME]`

    Open `~\.gitconfig` and add the following lines:

    ```
    [url "https://[USERNAME]:[TOKEN]@github.take2games.com/"]
            insteadOf = https://github.take2games.com/
    ```

- Before starting the server, please refer to [AWS SSO](https://paper.dropbox.com/doc/How-to-New-Okta-Tile-for-AWS-SSO-Login--Bf63HGHBOS1Dm5KSZUF0Lm8MAg-hRoeiMoX37acpAeVQC3NG).
  Paste all three AWS credentials in **.env**. You need to refresh these credentials if DynamoDB throws errors related to authentication
- run `make run` to start the social-api server
- open http://localhost:8001/ and use **APP_ID** and
  **APP_SECRET** defined in `t2gp-social-service/.env` to login

## How to fix `certificate verify failed`
Go to **1Password** and download [t2paca.crt](https://start.1password.com/open/i?a=6QTLI3ML5RA7JN4CV3X6OT5CDE&v=kclr6zwvafdjettpjl3p7gwyfm&i=i2l5vxlmh2zpvvr4lsgl4tfioe&h=enterprise-console.1password.com). Place the file in the root folder.

## How to debug multi-user scenarios from local Social service

1. Open a new incognito window and log in with a test account.
2. Open a new window and log in with a different account.

## How to connect local Social service to Dev environment

Because dev Redis nodes are behind VPC, to connect them from local instance, you need to acquire permissions
by following these steps:

1. Pull https://github.com/take-two-t2gp/d2c-jumpbox-auth and add your ssh public key to the list.
2. Push your commit and then ask an SRE to approve your PR.
3. Use the [template](https://console.service-now.com/t2?id=ticket&table=incident&sys_id=eac86f3fdbb5ddd07b93819b139619b1) to cut an IT ticket.
4. Once your request is approved, run
   ```
   ssh -f -N -L 6379:t2gp-social-develop-social.app4go.ng.0001.use1.cache.amazonaws.com:6379 ec2-user@54.82.201.238
   ```
5. Stop your local Redis container before running the web tool.

# Steps to update vernemq version

1. Update vmq-plugin-social with version changes. make sure you leave the -alpine on the erlang dockerfile
   ```
   vernemq/vernemq:<version>
   vernemq/vernemq:<version>-alpine
   ```
2. create and publish the docker images to eks in vmq-plugin-social project.
   ```
   make docker
   make publish
   ```
3. update applicable helm files in the api project
   ```
   develop
   https://github.com/take-two-t2gp/t2gp-social-service/blob/develop/helm/helm_chart/charts/social-mqtt/values.yaml#L16
   ```
   ```
   other envs - replace <env> in the path below
   https://github.com/take-two-t2gp/t2gp-social-service/blob/develop/helm/helm_values/<env>.yaml#L4
   ```
4. restart the mqtt pods by deleting them in order using Lens

# MQTT messages

## group info

all received on the `group/{groupid}` topic

### control message

```
{
    "type": "control"
    "data": {
        "message": "anything"
    }
}
```

### Group deleted

```
{
    "type": "groupModified",
    "data": {
        "action": "disband"
    }
}
```

### Users Changed

#### User Left Group

```
{
    "type": "usersModified",
    "data": {
        "action": "left",
        "userid": "larrydavid",
        "reason": "left"
    }
}
```

#### User Kicked Group

```
{
    "type": "usersModified",
    "data: {
        "action": "left",
        "userid": "larrydavid",
        "reason": "kicked"
    }
}
```

#### User Joined Group

```
{
    "type": "usersModified",
    "data": {
        "action": "joined",
        "userid": "larrydavid"
    }
}
```

## user info

all received on the `user/{userid}` topic

### Invite Received (auto-join flow)

```
{
    "type": "inviteReceived",
    "data": {
        "groupID": "202d64e7-fd80-411c-8165-0b7f1f7d9669",
        "nonce": "24839237286918125", //optional
        "senderID": "teddanson"
    }
}
```

### Invite Received

```
{
    "type": "inviteReceived",
    "data": {
        "groupID": "202d64e7-fd80-411c-8165-0b7f1f7d9669",
        "senderID": "teddanson"
    }
}
```

### User Requested to join

```
{
    "type": "joinRequest",
    "data": {
        "groupID": "202d64e7-fd80-411c-8165-0b7f1f7d9669",
        "nonce": "24839237286918125",
        "userID": "teddanson"
    }
}
```

### User Presence Changed

```
{
    "type": "presenceChanged",
    "data": {
        "status": "playing",
        "timestamp": "2020-09-17T23:16:49.275Z",
        "extra": {
            "gameID": "Kerbal Space Program",
            "orbitting": "Duna"
        },
    }
}
```

## About Abuse Report API

### Overview

Please refer to [Miro](https://miro.com/app/board/uXjVP5mN4yY=/?share_link_id=116173721361) for the detailed design of abuse reporting system.

### Legacy implementation

[Git](https://github.com/take-two-t2gp/t2gp-social-abuse-report-lambda) sends reports to Zendesk or Slack, which is the legacy implementation of abuse report api.

### Onboard new customer

We have to onboard customers who will use this API. A new customer need the following AWS resources:

- An SNS topic
- An SQS queue
- An IAM user
- Add the product ids of their game along with the SNS topic created for the game to [abuse-report-resource-mapping](s3://t2gp-social/abuse-report//abuse-report-resource-mapping.json)

This setup is not environment specific and it allows customers to access their team's reports only. The IAM role should be given permissions to subscribe, receiving notification and polling SQS messages.

### Debugging

.devcontainer is set up to debug if you want to go that route.  In VSCode make sure you have the devcontainer extension and docker desktop.
use ctrl-shift-p Devcontainers: Rebuild and Reopen in Container.

if you want to just use the make vmq-<command> to set up the dockers, you need to enable host network support in Docker Desktop.  At the time
of this readme, this is a beta feature.  then you can use make vmq-start to create the docker images and debug in IDE.


## T2GP-D2C vs T2GP-Dev AWS users and environment differences

Use T2GP-D2C to view production services and T2GP-Dev account to view non-production services. 
T2GP-D2C services are on us-east-1.  T2GP-Dev services are on us-west-2

### These 3 services do not have DEV account counterparts
* Kinesis
* S3
* SNS

### These 2 have corresponding resources in DEV account
* Elasticache
* DynamoDB
