import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccount, TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

/**
 * TODO:
 *  - change "user1"/"user2" to "inviter"/"invitee"
 *  - consider factoring the scenarios
 */

/**
 * NOTE:
 *  - currently checking all fields on both inviter/invitee sides for major happy-path cases like send invite and accept invite.
 */

let usersTwok: TwokAccounts;
const message: string = 'T2GP Social Automated Testing';

beforeAll(async () => {
  usersTwok = new TwokAccounts(2, ["user1", "user2"]);
  await usersTwok.loginAll({});
});

afterAll(async () => {
  await usersTwok.logoutAll({});
});

describe('[public v2]', () => {
  afterEach(async () => {
    await socialApi.deleteFriend(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId
    );
  });

  it('send friend invitation[happy]', async () => {
    let testCase = {
      description: "send user a friend invitation",
      expected: "'pending' friendship status is present in the friend lists of both sides"
    };

    // member1 sends member2 friend invitation
    const resp: request.Response = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    // verify the friend list info of user1 the inviter
    let actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    let expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user2"].publicId,
            invitee: usersTwok.acct["user2"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "inviter's friend list does not have pending friendship status for invitee"
        }
      }
    );

    // verify the friend list info of user2 the invitee
    actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            invitee: usersTwok.acct["user2"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "invitee's friend list does not have pending friendship status for inviter"
        }
      }
    );
  });

  it('send friend invitation to the same person more than once', async () => {
    let testCase = {
      description: "send repeated friend invitations",
      expected: "only one instance of the 'pending' friendship is present in the friend list"
    };

    // member1 sends member2 friend invitation
    let resp: request.Response = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    // member1 sends member2 friend invitation again
    resp = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    // inviter's friend list
    const actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    // verify friendship status is "pending"
    const expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user2"].publicId,
            invitee: usersTwok.acct["user2"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "inviter's friend list does not contain only 1 instance of pending friendship status for the invitee"
        }
      }
    );
  });

  it('send friend invitation to an existing friend returns error', async () => {
    let testCase = {
      description: "make friend with an existing friend",
      expected: "existing friendship remains with no invitation"
    };

    // member2 sends member1 friend invitation
    let r: request.Response = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // member1 accepts friend invitation from member2
    r = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // member2 sends member1 friend invitation again
    r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // inviter's friend list
    let actualFriendInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // member1 and member2 friendship status remains "friends"; there should be no other friendship statuses like pending
    const expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            invitee: usersTwok.acct["user1"].publicId,
            status: 'friend',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship in the inviter's friend list"
        }
      }
    );
  });

  it('send friend invitation to self returns error', async () => {
    let testCase = {
      description: "make friend with self",
      expected: "the invitation is not sent out"
    };

    // member1 sends self friend invitation
    const resp: request.Response = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    const actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    // verify member1 doesn't have a "pending" friendship status with self
    const expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: []
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the friend list"
        }
      }
    );
  });

  it('send friend invitation with an invalid token', async () => {
    // define a mock user without valid token
    const mockUser: TwokAccount = new TwokAccount("<EMAIL>", "pass", "test");

    const resp: request.Response = await socialApi.makeFriends(
      mockUser,
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.UNAUTHORIZED, resp);
  });
});

describe('[public v2]', () => {
  afterEach(async () => {
    await socialApi.deleteFriend(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId
    );
  });

  it('accept friend invitation[happy]', async () => {
    let testCase = {
      description: "send friend invitation to a user; the user accepts the invitation",
      expected: "the 'friend' friendship is established"
    };

    // user2 sends user1 friend invitation
    let r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // verify the friend list info of user2 the inviter
    let actualFriendInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    let expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            invitee: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "inviter has unexpected friendship in the friend list"
        }
      }
    );

    // verify the friend list info of user1 the invitee
    actualFriendInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user2"].publicId,
            invitee: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "invitee has unexpected friendship in the friend list"
        }
      }
    );

    // user1 accepts friend invitation from user2
    r = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // verify the friend list info of user2 the inviter
    actualFriendInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            invitee: usersTwok.acct["user1"].publicId,
            status: 'friend',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "inviter has unexpected friendship in the friend list"
        }
      }
    );

    // verify the friend list info of user1 the invitee
    actualFriendInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user2"].publicId,
            invitee: usersTwok.acct["user1"].publicId,
            status: 'friend',
            userid: usersTwok.acct["user1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "invitee has unexpected friendship in the friend list"
        }
      }
    );
  });
});

describe('[public v2]', () => {
  beforeEach(async () => {
    // member1 adds member2 on his/her block list
    const r = await socialApi.updateBlockList(usersTwok.acct["user1"], { userids: [usersTwok.acct["user2"].publicId] });
    socialApi.testStatus(StatusCodes.OK, r);
  });

  afterEach(async () => {
    // member1 removes member2 on his/her block list
    const r = await socialApi.delBlockList(usersTwok.acct["user1"]);
    socialApi.testStatus(StatusCodes.OK, r);
  });

  it('Blocker sends friend invitation to blockee[happy]', async () => {
    let testCase = {
      description: "add a user to the block list; then blocker sends a friend invitation to the user",
      expected: "the blockee does not receive the friend invitation from the blocker"
    };

    // member1 sends member2 friend invitation
    const r = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, r);

    const actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    // verify member1 doesn't have a "pending" friendship status with member2
    const expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: []
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "the blocker has unexpected friendship"
        }
      }
    );
  });

  it('Blockee sends friend invitation to blocker[happy]', async () => {
    let testCase = {
      description: "adds a user to the block list; the user sends a friend invitation to the blocker",
      expected: "the 'pending' friendship is present in the blockee's friend list"
    };

    // member2 sends member1 friend invitation
    const r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    const actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // verify friendship status is 'pending'
    const expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the blockee's friend list"
        }
      }
    );
  });
});

describe('[public v2]', () => {
  beforeEach(async () => {
    // member1 adds member2 on his/her block list
    const r = await socialApi.updateBlockList(usersTwok.acct["user1"], { userids: [usersTwok.acct["user2"].publicId] });
    socialApi.testStatus(StatusCodes.OK, r);
  });

  afterEach(async () => {
    await socialApi.deleteFriend(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId
    );
  });

  it(`Blocker unblocks blockee, and blockee's invitation while blocked should still remain`, async () => {
    let testCase = {
      description: "blockee sends blocker a friend invitation; blocker unblocks blockee",
      expected: "the 'pending' friendship still remains in the blockee's friend list"
    };

    // member2 sends member1 friend invitation
    const r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    let actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // verify there is a pending invite
    let expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the blockee's friend list"
        }
      }
    );

    // member1 unblocks member2
    await socialApi.delBlockList(usersTwok.acct["user1"]);
    socialApi.testStatus(StatusCodes.OK, r);

    actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // verify the pending invite still remain
    expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "the previous invitation while blocked does not remain"
        }
      }
    );
  });

  it(`Blocker unblocks blockee, with blockee's invitation while blocked, both still need to invite each other to become friends`, async () => {
    let testCase = {
      description: `blockee sends blocker a friend invitation; blocker unblocks blockee; blocker sends friend invitation to blockee;
blockee sends blocker friend invitation again`,
      expected: "the 'friend' friendship is established"
    };

    // member2 sends member1 friend invitation
    let r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    let actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // verify there is a pending invite
    let expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the friend list"
        }
      }
    );

    // member1 unblocks member2
    await socialApi.delBlockList(usersTwok.acct["user1"]);
    socialApi.testStatus(StatusCodes.OK, r);

    actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // verify the pending invite still remain
    expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "the previous invitation while blocked does not remain"
        }
      }
    );

    // member1 sends member2 friend invitation
    r = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    )
    socialApi.testStatus(StatusCodes.OK, r);

    actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    // verify there is another pending invite from member1 to member2
    // currently there are two pending invite but member1 and member2 are not friend yet.
    expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user2"].publicId,
            status: 'pending',
            userid: usersTwok.acct["user1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the friend list"
        }
      }
    );

    // member2 sends member1 friend invitation
    r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    )
    socialApi.testStatus(StatusCodes.OK, r);

    actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user2"], {});

    // verify member1 and member2 are friend now.
    expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user1"].publicId,
            status: 'friend',
            userid: usersTwok.acct["user2"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the friend list"
        }
      }
    );
  });
});

describe('[public v2]', () => {
  afterEach(async () => {
    // member1 removes member2 on his/her block list
    const r = await socialApi.delBlockList(usersTwok.acct["user1"]);
    socialApi.testStatus(StatusCodes.OK, r);
  });

  it('Blocking removes existing friendship', async () => {
    let testCase = {
      description: "add an existing friend to the block list",
      expected: "the existing friendship is removed"
    };

    //member1 sends member2 friend invitation
    let r = await socialApi.makeFriends(
      usersTwok.acct["user1"],
      usersTwok.acct["user2"].publicId,
      { message: message }
    )
    socialApi.testStatus(StatusCodes.OK, r);

    // member2 accepts the friend invitation
    r = await socialApi.makeFriends(
      usersTwok.acct["user2"],
      usersTwok.acct["user1"].publicId,
      { message: message }
    )
    socialApi.testStatus(StatusCodes.OK, r);

    // inviter's friend list
    let actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    // verify member1 has a "friend" friendship status with member2
    const expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user2"].publicId,
            status: 'friend',
            userid: usersTwok.acct["user1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "unexpected friendship is present in the inviter's friend list"
        }
      }
    );

    // member1 adds member2 on his/her block list
    await socialApi.updateBlockList(usersTwok.acct["user1"], {userids: [usersTwok.acct["user2"].publicId]});
    socialApi.testStatus(StatusCodes.OK, r);

    actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["user1"], {});

    // verify member1 doesn't have a "friend" friendship status with member2
    const expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: []
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendsInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendsInfo,
        additionalInfo: {
          "fail reason": "blocking does not unfriend friends"
        }
      }
    );
  });
});