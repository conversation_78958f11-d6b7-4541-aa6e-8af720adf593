package cache

// this wrapper exists because we used to write to both elasticache and redis enterprise after migration with feature flags.  we can remove in v2 if desired.

import (
	"context"
	"errors"
	"time"

	"github.com/redis/go-redis/v9"
)

// del wrapped function for redis del
func (rc *RedisCache) del(ctx context.Context, key string) *redis.IntCmd {
	return rc.ecWriteClient.Del(ctx, key)
}

// exists wrapped function for redis exists
func (rc *RedisCache) exists(ctx context.Context, keys ...string) *redis.IntCmd {
	return rc.ecReadClient.Exists(ctx, keys...)
}

// existsPrimary wrapped function for redis exists that uses the primary shard
func (rc *RedisCache) existsPrimary(ctx context.Context, keys ...string) *redis.IntCmd {
	return rc.ecWriteClient.Exists(ctx, keys...)
}

// existsInSortedSet not technically wrapped function.  use redis function Do to get a nil back if member does not exist in key.  Using ZScore function won't return nil
func (rc *RedisCache) existsInSortedSet(ctx context.Context, key string, member string) bool {
	result := rc.ecReadClient.Do(ctx, "ZSCORE", key, member)
	if result != nil && result.Val() != nil && (errors.Is(result.Err(), redis.Nil) || result.Err() == nil) {
		return true
	}
	return false
}

// expire wrapped function for redis expire
func (rc *RedisCache) expire(ctx context.Context, key string, ttl time.Duration) *redis.BoolCmd {
	return rc.ecWriteClient.Expire(ctx, key, ttl)
}

// expireAt wrapped function for redis expireAt
func (rc *RedisCache) expireAt(ctx context.Context, key string, time time.Time) *redis.BoolCmd {
	return rc.ecWriteClient.ExpireAt(ctx, key, time)
}

// get wrapped function for redis get
func (rc *RedisCache) get(ctx context.Context, key string) *redis.StringCmd {
	return rc.ecReadClient.Get(ctx, key)
}

// incr wrapped function for redis incr
//func (rc *RedisCache) incr(ctx context.Context, key string) *redis.IntCmd {
//	return rc.ecReadClient.Incr(ctx, key)
//}

// mget wrapped function for redis mget
//func (rc *RedisCache) mGet(ctx context.Context, keys ...string) *redis.SliceCmd {
//	var ret *redis.SliceCmd
//
//	if rc.cfg.UseElasticacheRead {
//		ret = rc.ecReadClient.MGet(ctx, keys...)
//	} else {
//		ret = rc.rdb.MGet(ctx, keys...)
//	}
//	return ret
//}

// jSONGet wrapped function for redis JSONGet
func (rc *RedisCache) jSONGet(ctx context.Context, key string, paths ...string) *redis.JSONCmd {
	return rc.ecReadClient.JSONGet(ctx, key, paths...)
}

// jSONMGet wrapped function for redis JSONMGet
func (rc *RedisCache) jSONMGet(ctx context.Context, path string, keys ...string) *redis.JSONSliceCmd {
	return rc.ecReadClient.JSONMGet(ctx, path, keys...)
}

// jSONSet wrapped function for redis JSONSet
func (rc *RedisCache) jSONSet(ctx context.Context, key string, path string, value interface{}) *redis.StatusCmd {
	return rc.ecWriteClient.JSONSet(ctx, key, path, value)
}

// jSONDel wrapped function for redis JSONDel
func (rc *RedisCache) jSONDel(ctx context.Context, key string, path string) *redis.IntCmd {
	return rc.ecWriteClient.JSONDel(ctx, key, path)
}

// ping wrapped function for redis ping
func (rc *RedisCache) ping(ctx context.Context) *redis.StatusCmd {
	return rc.ecReadClient.Ping(ctx)
}

// pipeline wrapped function for redis pipeline
func (rc *RedisCache) pipeline() redis.Pipeliner {
	return rc.ecWriteClient.Pipeline()
}

// pipelined wrapped function for redis pipelined
func (rc *RedisCache) pipelined(ctx context.Context, fn func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	return rc.ecWriteClient.Pipelined(ctx, fn)
}

// watch wrapped function for redis watch
//func (rc *RedisCache) watch(ctx context.Context, fn func(*redis.Tx) error, keys string) error {
//	return rc.ecWriteClient.Watch(ctx, fn, keys)
//}

// set wrapped function for redis set
func (rc *RedisCache) set(ctx context.Context, key string, name interface{}, ttl time.Duration) *redis.StatusCmd {
	return rc.ecWriteClient.Set(ctx, key, name, ttl)
}

// tTL wrapped function for redis TTL
func (rc *RedisCache) tTL(ctx context.Context, key string) *redis.DurationCmd {
	return rc.ecReadClient.TTL(ctx, key)
}

// zAdd wrapped function for redis ZAdd
func (rc *RedisCache) zAdd(ctx context.Context, key string, members ...redis.Z) *redis.IntCmd {
	return rc.ecWriteClient.ZAdd(ctx, key, members...)
}

// zCard wrapped function for redis ZCard
func (rc *RedisCache) zCard(ctx context.Context, key string) *redis.IntCmd {
	return rc.ecReadClient.ZCard(ctx, key)
}

// zRangeByLex wrapped function for redis zRangeByLex
func (rc *RedisCache) zRangeByLex(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.StringSliceCmd {
	return rc.ecReadClient.ZRangeByLex(ctx, key, opt)
}

// zRangeByScore wrapped function for redis ZRangebyScore
func (rc *RedisCache) zRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.StringSliceCmd {
	return rc.ecReadClient.ZRangeByScore(ctx, key, opt)
}

// zRem wrapped function for redis ZRem
func (rc *RedisCache) zRem(ctx context.Context, key string, members ...interface{}) *redis.IntCmd {
	return rc.ecWriteClient.ZRem(ctx, key, members)
}

// zRangeByScore wrapped function for redis ZRangebyScore
//func (rc *RedisCache) zRemRangeByScore(ctx context.Context, key, min, max string) *redis.IntCmd {
//	var ret *redis.IntCmd
//
//	if rc.cfg.UseElasticacheRead {
//		ret = rc.ecReadClient.ZRemRangeByScore(ctx, key, min, max)
//	} else {
//		ret = rc.rdb.ZRemRangeByScore(ctx, key, min, max)
//	}
//	return ret
//}

// zRevRangeByScore wrapped function for redis ZRevRangeByScore
func (rc *RedisCache) zRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.ZSliceCmd {
	return rc.ecReadClient.ZRevRangeByScoreWithScores(ctx, key, opt)
}

// zRangeByScore wrapped function for redis ZRangeByScore
func (rc *RedisCache) zRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.ZSliceCmd {
	return rc.ecReadClient.ZRangeByScoreWithScores(ctx, key, opt)
}

// zScore wrapped function for redis ZScore
func (rc *RedisCache) zScore(ctx context.Context, key string, member string) *redis.FloatCmd {
	return rc.ecReadClient.ZScore(ctx, key, member)
}

// zCount wrapped function for redis ZCount
func (rc *RedisCache) zCount(ctx context.Context, key string, min string, max string) *redis.IntCmd {
	return rc.ecReadClient.ZCount(ctx, key, min, max)
}
