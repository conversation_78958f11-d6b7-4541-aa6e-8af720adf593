#!/bin/bash

set -ex

KUBETOKEN=$(aws eks get-token --cluster-name $CLUSTER  | jq -c -r '.status.token')
CLUSTER_ENDPOINT=$(aws eks describe-cluster --name $CLUSTER  | jq -c -r '.cluster.endpoint')
kubectl config set-cluster cluster \
    --server=$CLUSTER_ENDPOINT \
    --insecure-skip-tls-verify
kubectl config set-context cluster \
    --cluster=cluster
kubectl config use-context cluster

kubectl --token=$KUBETOKEN -n social-service patch svc social-service-mqtt-$TARGET_ENV-ext --patch "{\"spec\": {\"selector\": {\"bluegreen\": \"$TARGET_SET\"}}}"
kubectl --token=$KUBETOKEN -n social-service patch svc social-service-mqtt-$TARGET_ENV-active-sd --patch "{\"spec\": {\"selector\": {\"bluegreen\": \"$TARGET_SET\"}}}"