import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { MqttClientManager } from '../../../../integration/lib/mqttWrapper';
import { config, TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
let mqttClientMgr: MqttClientManager;

// Define group member labels for Group related tests: 1 leader, 1 "test subject member", and 0-n existing members.
// The "test subject member" is the one who performs some action that triggers MQTT messages.
// Depending on the scenario, the "test subject member" may or may not be an existing member.
let existingMemberCnt: number = 3;
let memberLabels: string[] = ["leader", "memberTs"];
for (let i = 1; i <= existingMemberCnt; i++) {
  memberLabels.push("member" + i.toString());
}

// mainly used to add a member to a group
async function inviteAndAccept(gid: string, invitedLabel: string) {
  let r = await socialApi.inviteV1(usersTwok.acct["leader"], gid, { memberid: usersTwok.acct[invitedLabel].publicId, status: 'invited' });
  socialApi.testStatus(StatusCodes.CREATED, r);
  r = await socialApi.acceptInviteV1(
    usersTwok.acct[invitedLabel],
    gid,
    {
      approverid: usersTwok.acct["leader"].publicId,
      memberid: usersTwok.acct[invitedLabel].publicId
    }
  );
  socialApi.testStatus(StatusCodes.OK, r);
}

beforeAll(async () => {
  // Name 2k accounts and login
  usersTwok = new TwokAccounts(memberLabels.length, memberLabels);
  await usersTwok.loginAll({});
  // Create mqtt clients with the above 2k accounts
  mqttClientMgr = new MqttClientManager(usersTwok.acct);
  await mqttClientMgr.waitConnectAll();
});

afterAll(async () => {
  // Stop all mqtt clients
  mqttClientMgr.stopAll();
  // Logout all 2k accounts
  await usersTwok.logoutAll({});
});

afterEach(async () => {
  // Remove listeners for all mqtt clients
  mqttClientMgr.removeAllListenersAll('message');
});

/**
 * Invitee receives a notification for a group invite.
 */
describe(`groupInviteReceived${_ds}`, () => {
  let expectedMqttMsgType = "groupInviteReceived";
  let mGroupId: string;

  beforeEach(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions */
  async function sendGroupInvite() {
    let r: request.Response = await socialApi.inviteV1(
      usersTwok.acct["leader"],
      mGroupId,
      {
        memberid: usersTwok.acct["memberTs"].publicId,
        status: 'invited'
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
  }

  it.each`
    testSubjectFunc    | scenario
    ${sendGroupInvite} | ${"send group invite[public v1]"}
  `('[happy][trusted]$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `send group invite to non-member; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the non-member receives the ${expectedMqttMsgType} message; the content of the message is correct;
the number of the message is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          approverid: usersTwok.acct["leader"].publicId,
          groupid: mGroupId,
          memberid: usersTwok.acct["memberTs"].publicId
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserTopic(usersTwok.acct["memberTs"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["memberTs"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Leader receives a notification for a group join request.
 */
describe(`groupJoinRequest${_ds}`, () => {
  let expectedMqttMsgType = "groupJoinRequest";
  let mGroupId: string;

  beforeEach(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions */
  async function requestJoinManualGroup() {
    let r = await socialApi.requestToJoinV1(
      usersTwok.acct["memberTs"],
      mGroupId,
      {
        canCrossPlay: true, status: 'requested'
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
  }

  it.each`
    testSubjectFunc           | scenario
    ${requestJoinManualGroup} | ${"send join request to a manual group[public v1]"}
  `('[happy]$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `non-member sends join request to a manual group; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group leader receives the ${expectedMqttMsgType} message; the content of the message is correct;
the number of the message is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          groupid: mGroupId,
          status: 'requested',
          userid: usersTwok.acct["memberTs"].publicId
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserTopic(usersTwok.acct["leader"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["leader"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Group members all receive notifications when a non-member joins a group and becomes a member.
 */
describe(`groupMembersModified[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "groupMembersModified";
  let mGroupId: string;
  let authGroupId: string;
  const authGroupPwd: string = 'somepassword';
  let autoApproveGroupId: string;
  let mGroupInviteId: string;

  beforeAll(async () => {
    // create "manual" group
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);

    for (let i = 1; i <= existingMemberCnt; i++) {
      await inviteAndAccept(mGroupId, "member" + i.toString());
    }

    // create "authenticate" group
    r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'authenticate',
        password: authGroupPwd,
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    authGroupId = socialApi.getGroupId(r);

    for (let i = 1; i <= existingMemberCnt; i++) {
      await inviteAndAccept(authGroupId, "member" + i.toString());
    }

    // create "auto-approve" group
    r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'auto-approve',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    autoApproveGroupId = socialApi.getGroupId(r);

    for (let i = 1; i <= existingMemberCnt; i++) {
      await inviteAndAccept(autoApproveGroupId, "member" + i.toString());
    }

    // create "manual" group for invite
    // join request action type doesn't matter for invite, and manual was arbitrarily chosen.
    r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupInviteId = socialApi.getGroupId(r);

    for (let i = 1; i <= existingMemberCnt; i++) {
      await inviteAndAccept(mGroupInviteId, "member" + i.toString());
    }
  });

  afterAll(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await socialApi.deleteGroup(usersTwok.acct["leader"], authGroupId);
    await socialApi.deleteGroup(usersTwok.acct["leader"], autoApproveGroupId);
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupInviteId);
  });

  /* test subject functions for groupMembersModified / joined */
  async function joinReqApproved() {
    let r = await socialApi.requestToJoinV1(usersTwok.acct["memberTs"], mGroupId, { canCrossPlay: true });
    socialApi.testStatus(StatusCodes.CREATED, r);
    r = await socialApi.approveRequestV1(usersTwok.acct["leader"], mGroupId, usersTwok.acct["memberTs"].publicId, {});
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function joinReqAutoApproved() {
    const resp = await socialApi.requestToJoinV1(usersTwok.acct["memberTs"], autoApproveGroupId, { canCrossPlay: true });
    socialApi.testStatus(StatusCodes.OK, resp);
  }

  async function joinReqAuth() {
    const resp = await socialApi.requestToJoinV1(usersTwok.acct["memberTs"], authGroupId, { canCrossPlay: true, password: authGroupPwd });
    socialApi.testStatus(StatusCodes.OK, resp);
  }

  async function inviteAccepted() {
    await inviteAndAccept(mGroupInviteId, "memberTs");
  }

  // message type "groupMembersModified"
  // reason "joined"
  it.each`
    testSubjectFunc        | scenario                                | groupIdName             | reason
    ${joinReqApproved}     | ${"join request manual group approved"} | ${"mGroupId"}           | ${"joined"}
    ${joinReqAutoApproved} | ${"join request auto-approve group"}    | ${"autoApproveGroupId"} | ${"joined"}
    ${joinReqAuth}         | ${"join request authenticate group"}    | ${"authGroupId"}        | ${"joined"}
    ${inviteAccepted}      | ${"invite accepted"}                    | ${"mGroupInviteId"}     | ${"joined"}
  `('[happy]joined / $scenario', async ({testSubjectFunc, groupIdName, reason}) => {
    let testCase = {
      description: `non-member joins the group; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the numbers of the message are correct`
    };

    let groupId: string = "init";

    switch (groupIdName) {
      case "mGroupId":
        groupId = mGroupId;
        break;
      case "autoApproveGroupId":
        groupId = autoApproveGroupId;
        break;
      case "authGroupId":
        groupId = authGroupId;
        break;
      case "mGroupInviteId":
        groupId = mGroupInviteId;
        break;
    }

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"reason":"${reason}"`),
      {
        data: {
          action: "joined",
          postRole: "member",
          preRole: "nonmember",
          reason: reason,
          userid: usersTwok.acct["memberTs"].publicId
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(groupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Group members all receive notifications when a non-member creates a group and becomes a leader.
 * Note in this case leader is the only group member involved.
 */
describe(`groupMembersModified${_ds}`, () => {
  let expectedMqttMsgType = "groupMembersModified";
  let mGroupId: string;

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions for groupMembersModified / created */
  async function createManualGroup() {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  }

  // message type "groupMembersModified"
  // reason "created"
  it.each`
    testSubjectFunc      | scenario                         | reason
    ${createManualGroup} | ${"create manual group[public v1]"} | ${"created"}
  `('[happy][trusted]$reason / $scenario', async ({testSubjectFunc, reason}) => {
    let testCase = {
      description: `non-member creates a group; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the number of the message is correct`
    };

    // verify message contents
    let msg = {topic: "init", payload: Buffer.alloc(0)};
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"reason":"${reason}"`),
      {
        data: {
          action: "create",
          postRole: "leader",
          preRole: "nonmember",
          reason: reason,
          userid: usersTwok.acct["leader"].publicId
        },
        type: expectedMqttMsgType
      },
      null,
      occurCount,
      msg
    );

    // add listener
    mqttClientMgr.getClients()["leader"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );

    // verify topic
    socialApi.expectMore(
      () => {expect(msg.topic).toBe(socialApi.getGroupTopic(mGroupId))},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected topic of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Group members all receive notifications when a member is promoted to become a leader.
 */
describe(`groupMembersModified[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "groupMembersModified";
  let mGroupId: string;

  beforeEach(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);

    await inviteAndAccept(mGroupId, "memberTs");

    for (let i = 1; i <= existingMemberCnt; i++) {
      let curr = "member" + i.toString();
      await inviteAndAccept(mGroupId, curr);
    }
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await socialApi.deleteGroup(usersTwok.acct["memberTs"], mGroupId);
  });

  /* test subject functions for groupMembersModified / leaderLeft or modified */
  async function leaderKicksSelf() {
    // The leader kicks him/herself
    let r = await socialApi.kickGroupMember(usersTwok.acct["leader"], mGroupId, usersTwok.acct["leader"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function leaderLeaves() {
    // The leader leaves the group.
    let r = await socialApi.leaveGroup(usersTwok.acct["leader"], mGroupId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function promotedToLeader() {
    // The leader promotes memberTs to be the leader
    let r: request.Response = await socialApi.updateGroupMember(
      usersTwok.acct["leader"],
      usersTwok.acct["memberTs"].publicId,
      mGroupId,
      { role: 'leader' }
    );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  // message type "groupMembersModified"
  // reason "leaderLeft" or "modified"
  it.each`
    testSubjectFunc     | scenario                       | reason
    ${leaderKicksSelf}  | ${"Leader kicks himself"}      | ${"leaderLeft"}
    ${leaderLeaves}     | ${"Leader leaves group"}       | ${"leaderLeft"}
    ${promotedToLeader} | ${"member promoted to leader"} | ${"modified"}
  `('[happy]$reason / $scenario', async ({testSubjectFunc, reason}) => {
    let testCase = {
      description: `group member is promoted to be the group leader because of the reason '${reason}';
check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the numbers of the message are correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"reason":"${reason}"`),
      {
        data: {
          action: "roleChanged",
          postRole: "leader",
          preRole: "member",
          reason: reason,
          // Note: in the case of leader left, the member promoted is the "next" member in the group.
          // During beforeEach, memberTs joins the group after the leader.
          userid: usersTwok.acct["memberTs"].publicId
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    let expectedOccurCnt = 0;
    for (let label of memberLabels) {
      // the leaving leader wouldn't get the message
      if (label != "leader") {
        mqttClientMgr.getClients()[label].getClient().on('message', vmc);
        expectedOccurCnt += 1;
      }
    }

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < expectedOccurCnt, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(expectedOccurCnt)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Group members all receive notifications when a leader or member leaves and becomes a non-member.
 */
describe(`groupMembersModified[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "groupMembersModified";
  let mGroupId: string;
  // user updates keepalive time to be(35 is the minimum)
  let keepalive = 35;
  // extra time in seconds allowed for presence to become expired
  let maxSlack = 15;
  // Timeout in ms for each test. Adjust according to keepalive
  let testTimeout = 100000;

  beforeEach(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);

    await inviteAndAccept(mGroupId, "memberTs");

    for (let i = 1; i <= existingMemberCnt; i++) {
      let curr = "member" + i.toString();
      await inviteAndAccept(mGroupId, curr);
    }
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    await socialApi.deleteGroup(usersTwok.acct["memberTs"], mGroupId);
  });

  /* test subject functions for groupMembersModified / left or kicked */
  async function leaderKicksSelf() {
    // The leader kicks him/herself
    let r = await socialApi.kickGroupMember(usersTwok.acct["leader"], mGroupId, usersTwok.acct["leader"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function leaderKicksMember() {
    // The leader kicks memberTs.
    let r = await socialApi.kickGroupMember(usersTwok.acct["leader"], mGroupId, usersTwok.acct["memberTs"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function leaderLeaves() {
    // The leader leaves the group.
    let r = await socialApi.leaveGroup(usersTwok.acct["leader"], mGroupId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function memberLeaves() {
    // memberTs leaves the group.
    let r = await socialApi.leaveGroup(usersTwok.acct["memberTs"], mGroupId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function leaderAutoKicked() {
    // leader sets presence
    let r = await socialApi.setPresenceV1(
      usersTwok.acct["leader"],
      {
        keepAliveFor: keepalive,
        status: "chat",
        gameName: "Automated API Tests Game Name"
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // let presence keepalive expire
    await new Promise(r => setTimeout(r, keepalive * 1000));

    // some extra amount of time is allowed before group member is autokicked when presence expires
    await socialApi.waitWhile(async () => {
      let g = await socialApi.getGroupInfo(
        usersTwok.acct["memberTs"],
        mGroupId
      );
      return (g.body.members.length == memberLabels.length)
    }, maxSlack, 1000);
  }

  async function memberAutoKicked() {
    // update leader presence
    let r = await socialApi.setPresenceV1(
      usersTwok.acct["memberTs"],
      {
        keepAliveFor: keepalive,
        status: "chat",
        gameName: "Automated API Tests Game Name"
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // let presence keepalive expire
    await new Promise(r => setTimeout(r, keepalive * 1000));

    // some extra amount of time is allowed before group member is autokicked when presence expires
    await socialApi.waitWhile(async () => {
      let g = await socialApi.getGroupInfo(
        usersTwok.acct["leader"],
        mGroupId
      );
      return (g.body.members.length == memberLabels.length)
    }, maxSlack, 1000);
  }

  // message type "groupMembersModified"
  // reason "left" or "kicked"
  it.each`
    testSubjectFunc      | scenario                         | leaverRole  | reason
    ${leaderKicksSelf}   | ${"Leader kicks himself[happy]"} | ${"leader"} | ${"left"}
    ${leaderKicksMember} | ${"Leader kicks member[happy]"}  | ${"member"} | ${"kicked"}
    ${leaderLeaves}      | ${"Leader leaves group[happy]"}  | ${"leader"} | ${"left"}
    ${memberLeaves}      | ${"Member leaves group[happy]"}  | ${"member"} | ${"left"}
    ${leaderAutoKicked}  | ${"Leader autokicked"}           | ${"leader"} | ${"Kicking from Group for presence expiry"}
    ${memberAutoKicked}  | ${"Member autokicked"}           | ${"member"} | ${"Kicking from Group for presence expiry"}
  `('$reason / $scenario', async ({testSubjectFunc, leaverRole, reason}) => {
    let testCase = {
      description: `the ${leaverRole} leaves the group because of the reason '${reason}'; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the numbers of the message are correct`
    };

    let preRole = leaverRole;
    let userid: string = "init";
    if (preRole == "leader") {
      userid = usersTwok.acct["leader"].publicId
    } else if (preRole == "member") {
      userid = usersTwok.acct["memberTs"].publicId
    }

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"reason":"${reason}"`),
      {
        data: {
          action: "left",
          postRole: "nonmember",
          preRole: preRole,
          reason: reason,
          userid: userid
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of the ${expectedMqttMsgType} message`
        }
      }
    );
  }, testTimeout);
});

/**
 * Group members all receive notifications when a group is created.
 * Note in this case leader is the only group member involved.
 */
describe(`groupModified${_ds}`, () => {
  let expectedMqttMsgType = "groupModified";
  let mGroupId: string;

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions for groupModified */
  async function createManualGroup() {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);
  }

  // message type "groupModified"
  it.each`
    testSubjectFunc      | scenario                          | action
    ${createManualGroup} | ${"create manual group[public v1]"}  | ${"create"}
  `('[happy][trusted]$scenario', async ({testSubjectFunc, action}) => {
    let testCase = {
      description: `create a manual group; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the number of the message is correct`
    };

    // verify message contents
    let msg = {topic: "init", payload: Buffer.alloc(0)};
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"action":"${action}".*"type":"${expectedMqttMsgType}"`),
      {
        data: {
          action: "create"
        },
        type: expectedMqttMsgType
      },
      null,
      occurCount,
      msg
    );

    // add listener
    mqttClientMgr.getClients()["leader"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );

    // verify topic
    socialApi.expectMore(
      () => {expect(msg.topic).toBe(socialApi.getGroupTopic(mGroupId))},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected topic of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Group members all receive notifications when a group is modified or disbanded.
 */
describe(`groupModified[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "groupModified";
  let mGroupId: string;

  beforeEach(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);

    await inviteAndAccept(mGroupId, "memberTs");

    for (let i = 1; i <= existingMemberCnt; i++) {
      let curr = "member" + i.toString();
      await inviteAndAccept(mGroupId, curr);
    }
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions for groupMembersModified */
  async function deleteGroup() {
    let r = await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function updateGroup() {
    const r = await socialApi.updateGroup(usersTwok.acct["leader"], mGroupId, { maxMembers: 10 });
    socialApi.testStatus(StatusCodes.OK, r);
  }

  // message type "groupModified"
  it.each`
    testSubjectFunc | scenario           | action
    ${updateGroup}  | ${"update group"}  | ${"modify"}
    ${deleteGroup}  | ${"disband group"} | ${"disband"}
  `('[happy]$scenario', async ({testSubjectFunc, action}) => {
    let testCase = {
      description: `${action} the group; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the numbers of the message are correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"action":"${action}".*"type":"${expectedMqttMsgType}"`),
      {
        data: {
          action: action
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

/**
 * Group members all receive notifications when a group member sets presence
 */
describe(`presence[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "presence";
  let mGroupId: string;

  beforeAll(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);

    await inviteAndAccept(mGroupId, "memberTs");

    for (let i = 1; i <= existingMemberCnt; i++) {
      let curr = "member" + i.toString();
      await inviteAndAccept(mGroupId, curr);
    }
  });

  afterAll(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions for presence */
  async function memberSetPresence(status: string) {
    let r = await socialApi.setPresenceV1(
      usersTwok.acct["memberTs"],
      { status: status, gameName: "Automated API Tests Game Name" }
    );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function memberClearPresence() {
    let r = await socialApi.clearPresence(usersTwok.acct["memberTs"]);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function memberSetActiveGroup() {
    let r = await socialApi.setActiveGroupV1( usersTwok.acct["memberTs"], { activeGroupid: mGroupId } );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each`
    testSubjectFunc      | scenario                                       | status
    ${memberSetPresence} | ${"group member sets presence online"}         | ${"online"}
    ${memberSetPresence} | ${"group member sets presence offline"}        | ${"offline"}
    ${memberSetPresence} | ${"group member sets presence playing"}        | ${"playing"}
    ${memberSetPresence} | ${"group member sets presence custom"}         | ${"custom"}
    ${memberSetPresence} | ${"group member sets presence away"}           | ${"away"}
    ${memberSetPresence} | ${"group member sets presence dnd"}            | ${"dnd"}
    ${memberSetPresence} | ${"group member sets presence chat"}           | ${"chat"}
    ${memberSetPresence} | ${"group member sets presence authenticating"} | ${"authenticating"}
  `('[happy]$scenario', async({testSubjectFunc, status}) => {
    let testCase = {
      description: `group member sets presence to be ${status}; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the messages is correct;
the number of the messages is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}`),
      {
        data: {
          groupid: mGroupId,
          userid: usersTwok.acct["memberTs"].publicId,
          status: status
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await testSubjectFunc(status);

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of ${expectedMqttMsgType} message`
        },
      }
    );
  });

  it.each`
    testSubjectFunc        | scenario
    ${memberClearPresence} | ${"group member clears presence"}
  `('[happy]$scenario', async({testSubjectFunc}) => {
    let testCase = {
      description: `group member clears presence; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the messages is correct;
the number of the messages is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}`),
      {
        data: {
          userid: "",
          status: ""
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of ${expectedMqttMsgType} message`
        },
      }
    );
  });

  it.each`
    testSubjectFunc         | scenario
    ${memberSetActiveGroup} | ${"group member sets active group"}
  `('[happy]$scenario', async({testSubjectFunc}) => {
    let testCase = {
      description: `group member sets active group; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the messages is correct;
the number of the messages is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}`),
      {
        data: {
          activeGroup: {
            groupid: mGroupId
          },
          userid: usersTwok.acct["memberTs"].publicId
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of ${expectedMqttMsgType} message`
        },
      }
    );
  });
});

/**
 * Group members all receive control message.
 */
describe(`groupControlMessage[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "groupControlMessage";
  // in the below array: the first char is tag, and second char is the character under test
  let tagAndCharacters: string[][] = [['[happy]', 'a'], ['[happy]', ';'], ['[happy]', '<'], ['[happy]', ' ']];
  // support 5120 ascii characters
  const maxCharacterSize: number = 5120;
  let mGroupId: string;

  beforeAll(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true,
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mGroupId = socialApi.getGroupId(r);

    await inviteAndAccept(mGroupId, "memberTs");

    for (let i = 1; i <= existingMemberCnt; i++) {
      let curr = "member" + i.toString();
      await inviteAndAccept(mGroupId, curr);
    }
  });

  afterAll(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mGroupId);
  });

  /* test subject functions */
  async function sendControlMessage(message: string) {
    let r = await socialApi.sendControlMessage(usersTwok.acct["leader"], mGroupId, { payload: message });
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each(tagAndCharacters)(`%ssend max-limit '%s' characters and check receipt`, async (tag, character) => {
    let testCase = {
      description: `user sends control message; check the ${expectedMqttMsgType} mqtt message`,
      expected: `the group members all receive the ${expectedMqttMsgType} message; the content of the message is correct;
the numbers of the message are correct`
    };

    // create input characters
    let message = character.repeat(maxCharacterSize);

    // verify message contents
    let occurCount = { cnt: 0 };
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          groupid: mGroupId,
          payload: message,
          senderid: usersTwok.acct["leader"].publicId
        },
        type: expectedMqttMsgType
      },
      socialApi.getGroupTopic(mGroupId),
      occurCount
    );

    // add listener
    mqttClientMgr.addMsgListenerAll(vmc);

    // main test subject
    await sendControlMessage(message);

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < mqttClientMgr.getClientsNum(), 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(mqttClientMgr.getClientsNum())},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});