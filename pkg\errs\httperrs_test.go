package errs

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/franela/goblin"
	"github.com/go-chi/chi/v5"
)

func TestHttpErrorHandler(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("HttpErrorHandler", func() {
		g.It("should process requests properly", func() {
			ctx := context.Background()
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com", nil)
			w := httptest.NewRecorder()
			HttpErrorHandler(w, r)
			g.<PERSON>sert(w.Code).Equal(200)

			ctx = context.WithValue(ctx, chi.RouteCtxKey, &chi.Context{
				URLParams: chi.RouteParams{
					Keys:   []string{"errorID"},
					Values: []string{"500"},
				},
			})
			r, _ = http.NewRequestWithContext(ctx, "GET", "http://example.com", nil)
			w = httptest.NewRecorder()
			HttpErrorHandler(w, r)
			g.<PERSON>sert(w.Code).Equal(500)
		})
	})
}
