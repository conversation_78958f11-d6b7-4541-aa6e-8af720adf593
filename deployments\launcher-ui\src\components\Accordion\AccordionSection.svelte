<script lang="ts">
  import { slide } from 'svelte/transition';
  import { ACCORDION_MODE } from '../../constant';
  import { useAccordionContext } from '../../hooks';
  import Header from './AccordionHeader.svelte';

  export let title = undefined;
  export let open = false;
  export let containerClassName = '';
  export let contentClassName = '';
  export let key = '';
  export let mode = ACCORDION_MODE.single;
  $: key = key || title;
  const { onHeaderClicked, selected } = useAccordionContext();
  //get selected value from context
  $: open = $selected.includes(key);
</script>

<style>
  .open {
    flex: 1;
  }

  .accordion-section-content {
    color: var(--social-color, var(--default-color));
  }
</style>

<li class="{`accordion-section ${containerClassName}`}" class:open>
  <Header
    on:click="{onHeaderClicked(key)}"
    title="{title}"
    open="{open}"
    mode="{mode}"
  />
  {#if open}
    <div
      class="{`accordion-section-content ${contentClassName}`}"
      transition:slide
    >
      <slot />
    </div>
  {/if}
</li>
