<script lang="ts">
  import { SocialServiceProvider } from './components/SocialServiceProvider';
  import { INITIAL_LANGUAGE } from './constant';
  import { Friends } from './pages/Friends';
  import type {
    FriendsService,
    SocialServices,
    TransportService,
  } from './services';
  import { UserService } from './services/user';
  import type { socialTheme } from './utils';

  export let url = '/';
  export let lang = INITIAL_LANGUAGE;
  export let theme: socialTheme = {
    color: '',
    bgColorTopBar: '',
    bgColorActionBar: '',
    bgColorButton: '',
  };
  export let services: SocialServices<
    TransportService,
    FriendsService,
    UserService
  >;
</script>

<SocialServiceProvider lang="{lang}" services="{services}">
  <Friends url="{url}" theme="{theme}" />
</SocialServiceProvider>
