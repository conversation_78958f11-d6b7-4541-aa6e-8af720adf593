import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

describe('[public v2]', () => {
  let usersTwok: TwokAccounts;
  // game name
  let gameName = "Automated API Tests Game Name";

  beforeAll(async () => {
    usersTwok = new TwokAccounts(1, ["user"]);
    await usersTwok.loginAll({});
  });

  afterAll(async () => {
    await usersTwok.logoutAll({});
  });

  beforeEach(async () => {
    await socialApi.clearPresence(
      usersTwok.acct['user']
    );
  });

  afterEach(async () => {
    await socialApi.clearPresence(
      usersTwok.acct['user']
    );
  });

  it.each`
    status
    ${"online"}
    ${"offline"}
    ${"playing"}
    ${"custom"}
    ${"away"}
    ${"dnd"}
    ${"chat"}
    ${"authenticating"}
  `("set presence $status[happy]", async ({ status }) => {
    let testCase = {
      description: `set '${status}' presence status for user; get user presence`,
      expected: `presence status is '${status}'`
    };

    // set presence
    let resp = await socialApi.setPresence(
      usersTwok.acct["user"],
      { status: status, gameName: gameName }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    let actualPresence = await socialApi.getPresence(
      usersTwok.acct["user"],
      {}
    );

    // verify presence status is set successfully
    const expectedGetPresence = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            status: status,
            userid: usersTwok.acct["user"].publicId
          }
        ]
      },
    };
    socialApi.expectMore(
      () => {expect(actualPresence).toMatchObject(expectedGetPresence)},
      testCase,
      {
        resp: actualPresence,
        additionalInfo: {
          "fail reason": `unexpected presence status`
        }
      }
    );
  })

  it('clear presence[happy]', async () => {
    let testCase = {
      description: "set user presence; clear user presence",
      expected: "user presence is cleared"
    };

    // set presence
    let r = await socialApi.setPresence(
      usersTwok.acct['user'],
      { status: 'chat', gameName: gameName }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // get presence
    let actualPresence = await socialApi.getPresence(
      usersTwok.acct['user'],
      {}
    );

    // verify the user presence exists
    let expectedPresence = {
      status: StatusCodes.OK,
      body: {
        items: [{ status: 'chat', userid: usersTwok.acct['user'].publicId }]
      },
    };
    socialApi.expectMore(
      () => {expect(actualPresence).toMatchObject(expectedPresence)},
      testCase,
      {
        resp: actualPresence,
        additionalInfo: {
          "fail reason": "user presence is not updated"
        }
      }
    );

    // clear presence
    r = await socialApi.clearPresence(
      usersTwok.acct['user']
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // get presence
    actualPresence = await socialApi.getPresence(
      usersTwok.acct['user'],
      {}
    );

    // verify presence is cleared
    expectedPresence = {
      status: StatusCodes.OK,
      body: {
        items: []
      },
    };
    socialApi.expectMore(
      () => {expect(actualPresence).toMatchObject(expectedPresence)},
      testCase,
      {
        resp: actualPresence,
        additionalInfo: {
          "fail reason": "user presence not cleared, or cleared presence not matching the expected empty array"
        }
      }
    );
  });
});

// TODO: change the following test cases to be "users do not get presence update from other apps"

/*
describe('', () => {
  let usersTwok: TwokAccounts;
  let appArray = [
    { 'app': config.apps.alphaRace, 'status': "playing" },
    { 'app': config.apps.socialServiceProduct, 'status': "chat" }
  ];
  let appIdArray: string[] = [];

  beforeAll(async () => {
    for (let i = 0; i < appArray.length; i++) {
      appIdArray.push(appArray[i].app.appId);
    }

    usersTwok = new TwokAccounts(1, ["user"]);
    await usersTwok.acct["user"].login({ appIdArray: appIdArray })
  });

  afterAll(async () => {
    await usersTwok.acct["user"].logout({ appIdArray: appIdArray })
  });

  beforeEach(async () => {
    for (let i = 0; i < appArray.length; i++) {
      await socialApi.clearPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId]
      );
    }
  });

  afterEach(async () => {
    for (let i = 0; i < appArray.length; i++) {
      await socialApi.clearPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId]
      );
    }
  });

  it('[happy]set presence with multiple product IDs', async () => {
    let testCase = {
      description: "",
      expected: ""
    };

    // set presence status in multiple apps
    for (let i = 0; i < appArray.length; i++) {
      let r = await socialApi.setPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId],
        { status: appArray[i].status }
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    // create expected presence array
    let expectedPresenceArray = [];
    for (let i = 0; i < appArray.length; i++) {
      expectedPresenceArray.push(expect.objectContaining(
        {
          productid: appArray[i].app.productId,
          status: appArray[i].status,
          userid: usersTwok.acct["user"].publicId
        }
      ));
    }

    // verify that the user's presences in all apps are reported in each app
    for (let i = 0; i < appArray.length; i++) {
      // get presence in this app
      const actualPresence = await socialApi.getPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId],
        {}
      );

      const expectedPresence = {
        status: StatusCodes.OK,
        body: {
          items: expect.arrayContaining(expectedPresenceArray),
        },
      };
      socialApi.expectMore(
        () => {expect(actualPresence).toMatchObject(expectedPresence)},
        testCase,
        {
          resp: actualPresence,
          additionalInfo: {
            "fail reason": ""
          }
        }
      );

      // ensure each app reports exactly the expected presence objects and no more.
      socialApi.expectMore(
        () => {expect(actualPresence.body.items.length).toEqual(expectedPresenceArray.length)},
        testCase,
        {
          resp: actualPresence,
          additionalInfo: {
            "fail reason": ""
          }
        }
      );
    }
  })

  it('[happy]clear presence with multiple product IDs', async () => {
    let testCase = {
      description: "",
      expected: ""
    };

    // set presence status in multiple apps
    for (let i = 0; i < appArray.length; i++) {
      let r = await socialApi.setPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId],
        { status: appArray[i].status }
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    // clear presence in multiple apps
    for (let i = 0; i < appArray.length; i++) {
      let r = await socialApi.clearPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId]
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }

    // verify the user presences are cleared
    for (let i = 0; i < appArray.length; i++) {
      let actualPresence = await socialApi.getPresence(
        usersTwok.acct["user"].accessTokenDict[appArray[i].app.appId],
        {}
      );

      let expectedPresence = {
        status: StatusCodes.OK,
        body: {},
      };
      socialApi.expectMore(
        () => {expect(actualPresence).toMatchObject(expectedPresence)},
        testCase,
        {
          resp: actualPresence,
          additionalInfo: {
            "fail reason": ""
          }
        }
      );
    }
  });
});
*/