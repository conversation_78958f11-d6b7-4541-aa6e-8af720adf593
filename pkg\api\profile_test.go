package api

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/go-redis/redismock/v9"
	. "github.com/onsi/gomega"
	"github.com/segmentio/encoding/json"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func TestGetUserProfile(t *testing.T) {
	g := goblin.Goblin(t)
	userid := "e12c3df480984141b2f385646b2024fa"
	email := "<EMAIL>"
	displayName := "displayName"
	assertUserProfile := func(t *testing.T, w *httptest.ResponseRecorder, mockRedis redismock.ClusterClientMock) {
		profile := apipub.UserProfileResponse{}
		err := json.Unmarshal(w.Body.Bytes(), &profile)
		g.<PERSON>sert(err).IsNil()
		g.Assert(profile.Userid).Equal(userid)
		//g.Assert(profile.Email).IsNotNil()
		//g.Assert(*profile.Email).Equal(types.Email(email))
		g.Assert(profile.DisplayName).IsNotNil()
		g.Assert(mockRedis.ExpectationsWereMet()).IsNil()
	}

	testCases := []struct {
		name               string
		prepareRequest     func(string) (*httptest.ResponseRecorder, *http.Request)
		prepareExpections  func(mock *MockAPI)
		expectResponseCode int
		assert             func(t *testing.T, w *httptest.ResponseRecorder, mockRedis redismock.ClusterClientMock)
	}{
		// {
		// 	name: "1. OK - get 2k user profile not in redis and dynamodb",
		// 	prepareRequest: func(jwt string) (*httptest.ResponseRecorder, *http.Request, redisCacheDNA.ICache, redismock.ClientMock, *redis.Client) {
		// 		client, mockRedis := redismock.NewClientMock()
		// 		Cache := redisCacheDNA.NewCache(client)

		// 		w, r := Login(User2JWT)

		// 		return w, r, Cache, mockRedis, client
		// 	},
		// 	prepareExpections: func(Ds *store.MockDataStoreInterface, mockRedis redismock.ClientMock) {
		// 		// not in dynamodb
		// 		Ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
		// 		searchResponse := apipub.SearchAccountResponse{
		// 			{
		// 				Id:          &userid, //user2JWT subject
		// 				DisplayName: &displayName,
		// 				Email:       &email,
		// 			},
		// 		}
		// 		Ds.EXPECT().SearchAccountsDNA(gomock.Any(), gomock.Any()).Return(&searchResponse, nil)
		// 		// get profile link
		// 		Ds.EXPECT().GetUserProfileAccountLinksDNA(gomock.Any(), gomock.Any())
		// 		Ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
		// 		//mockRedis.ExpectGet("user#" + userid).RedisNil()
		// 		//mockRedis.Regexp().ExpectSet("user#"+userid, `.*<EMAIL>.*`, 15*time.Minute)
		// 	},
		// 	expectResponseCode: http.StatusOK,
		// 	assert:             assertUserProfile,
		// },
		// {
		// 	name: "2. OK - get 2k user profile in redis Cache",
		// 	prepareRequest: func(jwt string) (*httptest.ResponseRecorder, *http.Request, redisCacheDNA.ICache, redismock.ClientMock, *redis.Client) {
		// 		client, mockRedis := redismock.NewClientMock()
		// 		Cache := redisCacheDNA.NewCache(client)

		// 		w, r := Login(User2JWT)

		// 		return w, r, Cache, mockRedis, client
		// 	},
		// 	prepareExpections: func(Ds *store.MockDataStoreInterface, mockRedis redismock.ClientMock) {
		// 		// in redis Cache
		// 		profile := apipub.UserProfileResponse{
		// 			Userid: userid,
		// 			Email:  (*types.Email)(&email),
		// 			Name:   &displayName,
		// 		}
		// 		blob, _ := json.Marshal(profile)
		// 		mockRedis.ExpectGet("user#" + userid).SetVal(string(blob))
		// 	},
		// 	expectResponseCode: http.StatusOK,
		// 	assert:             assertUserProfile,
		// },
		{
			name: "3. OK - get 2k user profile in dynamodb",
			prepareRequest: func(jwt string) (*httptest.ResponseRecorder, *http.Request) {
				w, r := Login(User2JWT)

				return w, r
			},
			prepareExpections: func(mock *MockAPI) {
				// in dynamodb
				// mockRedis.ExpectGet("user#" + userid).RedisNil()
				profile := apipub.UserProfileResponse{
					Userid:      userid,
					Email:       &email,
					DisplayName: &displayName,
				}
				mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
				mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile, nil)
				mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).AnyTimes()
				mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			},
			expectResponseCode: http.StatusOK,
			assert:             assertUserProfile,
		},
	}

	g.Describe("GetUserProfile", func() {
		mock := NewMockAPI(t)
		defer mock.ctrl.Finish()
		for i := range testCases {
			testCase := testCases[i]
			g.It(testCase.name, func() {

				mock := NewMockAPI(t)
				defer mock.ctrl.Finish()

				w, req := testCase.prepareRequest(User1JWT)

				testCase.prepareExpections(mock)

				mock.api.GetUserProfile(w, req)

				g.Assert(w.Code).Equal(testCase.expectResponseCode)
				testCase.assert(t, w, mock.redisMock)
			})
		}
	})
}

func TestSyncUserProfile(t *testing.T) {
	userid := "e12c3df480984141b2f385646b2024fa"
	// email := types.Email("<EMAIL>")
	// displayName := "displayName"

	g := goblin.Goblin(t)
	g.Describe("SyncUserProfile", func() {
		g.It("get 2k user profile not in redis and dynamodb", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			w, r := Login(User2JWT)

			// searchResponse := apipub.SearchAccountResponse{
			// 	{
			// 		Id:          &userid, //user2JWT subject
			// 		DisplayName: &displayName,
			// 		Email:       &email,
			// 	},
			// }
			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userid}, nil)
			mock.ds.EXPECT().PutUserProfile(r.Context(), gomock.Any())
			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.api.SyncUserProfile(w, r)

			g.Assert(w.Code).Equal(http.StatusOK)
		})
	})
}

func TestUpdateRecentlyPlayed(t *testing.T) {
	g := goblin.Goblin(t)

	//special hook for gomega
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	g.Describe("UpdateRecentlyPlayed", func() {
		body := apipub.SetPlayedRequest{
			Ttl: nil,
			Users: []apipub.RecentlyPlayedUsers{
				{Userid: "b287e655461f4b3025c8w244e394ff7b"},
			},
		}

		profile := apipub.UserProfileResponse{
			Userid: "b287e655461f4b3025c8w244e394ff7b",
		}
		profileAr := []*apipub.UserProfileResponse{&profile}
		// token, _ := jwt.ParseJWTTokenWithoutValidation(User1JWT)

		// mock.redisGoMock.ExpectationsWereMet()

		g.It("with invalid token should get unauthorized", func() {
			w, r := AddBodyToRequest(body, BadJWT)
			mock.api.UpdateRecentlyPlayed(w, r)
			g.Assert(w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("with bad json should get bad request", func() {
			w, r := AddBodyToRequest("bad json", User1JWT)
			mock.api.UpdateRecentlyPlayed(w, r)
			g.Assert(w.Code).Equal(http.StatusBadRequest)
		})
		g.It("fails with bad user id", func() {
			w, r := AddBodyToRequest(apipub.SetPlayedRequest{
				Ttl: nil,
				Users: []apipub.RecentlyPlayedUsers{
					{Userid: "b287e655461f4b3025c8w244e394ff7b"},
					{Userid: "foobar"},
				},
			}, User1JWT)

			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SetRecentlyPlayedUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			mock.rc.EXPECT().GetRecentlyPlayed(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", nil)
			mock.api.UpdateRecentlyPlayed(w, r)
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
		})

		g.It("with empty redis result should be ok", func() {
			w, r := AddBodyToRequest(body, User1JWT)

			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil)
			mock.rc.EXPECT().SetRecentlyPlayedUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			mock.rc.EXPECT().GetRecentlyPlayed(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", nil)

			mock.api.UpdateRecentlyPlayed(w, r)
			Expect(w.Code).Should(Equal(http.StatusOK))
		})

		recentUsers := []*apipub.RecentlyPlayedUserResponse{}

		g.It("with POST items greater than 20 should be ok", func() {
			g.Timeout(45 * time.Second)
			body30 := apipub.SetPlayedRequest{
				Ttl:   aws.Int64(24 * 90 * 3600),
				Users: []apipub.RecentlyPlayedUsers{},
			}

			for i := 0; i < 30; i++ {
				userReq := apipub.RecentlyPlayedUsers{
					Userid: fmt.Sprintf("%v", utils.GenerateRandomDNAID()),
				}
				user := apipub.RecentlyPlayedUserResponse{
					Userid: fmt.Sprintf("%v", utils.GenerateRandomDNAID()),
				}
				body30.Users = append(body30.Users, userReq)
				if i < 20 {
					recentUsers = append(recentUsers, &user)
				}
			}

			w, r := AddBodyToRequest(body30, User1JWT)

			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil)
			mock.rc.EXPECT().SetRecentlyPlayedUsers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
			mock.rc.EXPECT().GetRecentlyPlayed(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&recentUsers, "", nil)

			mock.api.UpdateRecentlyPlayed(w, r)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`{}`))
		})

		g.It("with redis result of current user should be ok", func() {
			g.Timeout(mock.testTimeout)
			w, r := AddBodyToRequest(body, User1JWT)

			rec1 := &apipub.RecentlyPlayedUserResponse{
				Userid: "b287e655461f4b3025c8w244e394ff7b",
				Name:   aws.String("tacocat"),
			}

			recentUsers = []*apipub.RecentlyPlayedUserResponse{rec1}

			mock.api.UpdateRecentlyPlayed(w, r)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`{}`))
		})
	})
}

func TestGetRecentlyPlayed(t *testing.T) {
	g := goblin.Goblin(t)
	// mockCtrl := gomock.NewController(t)

	//special hook for gomega
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	// token, _ := jwt.ParseJWTTokenWithoutValidation(User1JWT)

	g.Describe("GetRecentlyPlayed", func() {
		g.It("with invalid token should get unauthorized", func() {
			w, r := AddBodyToRequest("", BadJWT)
			mock.api.GetRecentlyPlayed(w, r, apipub.GetRecentlyPlayedParams{Limit: (*apipub.Limit)(aws.Int(10)), Next: (*apipub.Next)(aws.String(""))})
			g.Assert(w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("with empty redis result should be ok", func() {
			w, r := AddBodyToRequest("", User1JWT)

			// reset pubApi with rc because previous test set it to nil
			mock.api.Cache = mock.rc

			mock.rc.EXPECT().GetRecentlyPlayed(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", nil)
			mock.api.GetRecentlyPlayed(w, r, apipub.GetRecentlyPlayedParams{Limit: (*apipub.Limit)(aws.Int(10)), Next: (*apipub.Next)(aws.String(""))})
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`{"items":[]`))
		})

		g.It("with weight should return ok", func() {
			w, r := AddBodyToRequest("", User1JWT)

			user1 := apipub.RecentlyPlayedUserResponse{
				Userid: "foobar",
				Name:   aws.String("tacocat"),
				Weight: aws.Int(100),
			}

			user2 := apipub.RecentlyPlayedUserResponse{
				Userid: "383b1f46b9db4740b7ec80a417caef32",
				Weight: aws.Int(10),
			}

			user3 := apipub.RecentlyPlayedUserResponse{
				Userid: "aaa",
				Weight: aws.Int(1),
			}

			users := &[]*apipub.RecentlyPlayedUserResponse{&user1, &user2, &user3}
			profile := apipub.UserProfileResponse{Userid: "foobar", DisplayName: aws.String("tacocat")}
			profileAr := []*apipub.UserProfileResponse{&profile}

			mock.rc.EXPECT().GetRecentlyPlayed(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(users, "", nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().GetUserPresences(gomock.Any(), gomock.Any(), gomock.Any()).Times(3)

			mock.api.GetRecentlyPlayed(w, r, apipub.GetRecentlyPlayedParams{Limit: (*apipub.Limit)(aws.Int(10)), Next: (*apipub.Next)(aws.String(""))})
			response := w.Body.String()

			list := apipub.PlayedPlayersNext{}
			items := []apipub.RecentlyPlayedUserResponse{}

			json.Unmarshal([]byte(response), &list)
			itemsJson, _ := json.Marshal(&list.Items)
			json.Unmarshal(itemsJson, &items)

			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(items[0].Userid).Should(Equal("foobar"))
			Ω(items[1].Userid).Should(Equal("383b1f46b9db4740b7ec80a417caef32"))
			Ω(items[2].Userid).Should(Equal("aaa"))
		})
	})
}

func TestReplacePlatformIds(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	var platformid, fullid, groupid, productid, approverid string
	var pinvited, pinvited2, prequested, prequested2 apipub.MembershipRequest
	var w *httptest.ResponseRecorder
	var r *http.Request

	g.Describe("Sync Profile with Replace Platform IDs", func() {

		g.Before(func() {
			platformJWT := "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
			platformid = "cd80c899b25c4749903e8d6af6c74d85"
			fullid = "e7483ccd4b3744eebe23c3329f4cccd0"
			groupid = utils.GenerateNewULID()
			productid = utils.GenerateNewULID()
			approverid = utils.GenerateNewULID()

			pinvited = apipub.MembershipRequest{
				Memberid:   platformid,
				Status:     apipub.Invited,
				Approverid: approverid,
				Groupid:    groupid,
				Productid:  &productid,
			}
			pinvited2 = pinvited
			pinvited2.Memberid = fullid
			prequested = apipub.MembershipRequest{
				Memberid:   platformid,
				Status:     apipub.Requested,
				Approverid: approverid,
				Groupid:    groupid,
				Productid:  &productid,
			}
			prequested2 = prequested
			prequested2.Memberid = fullid

			w, r = Login(platformJWT)
		})

		g.It("happy path", func() {

			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: platformid}, nil)
			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.api.SyncUserProfile(w, r)
		})
	})
}

func TestGetUserProfileInternals(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	userid1 := "e12c3df480984141b2f385646b2024fa"
	userid2 := "2017e9305ccc4e5781d076403c1b6725"
	userids := []string{userid1, userid2}
	name := "user"
	name2 := "user1"

	var missing []string
	var profiles []*apipub.UserProfileResponse
	var found map[string]struct{}
	var profile1 apipub.UserProfileResponse
	var profile2 apipub.UserProfileResponse
	var profilesResult []*apipub.UserProfileResponse
	var profilesPtr *[]*apipub.UserProfileResponse

	g.Describe("GetUserProfiles internals", func() {
		g.Before(func() {
			profiles = make([]*apipub.UserProfileResponse, 0, len(userids))
			missing = make([]string, 0, len(userids))
			found = make(map[string]struct{}, len(userids))

			profile1 = apipub.UserProfileResponse{
				Userid:      userid1,
				DisplayName: &name,
			}
			profile2 = apipub.UserProfileResponse{
				Userid:      userid2,
				DisplayName: &name2,
			}
			profilesResult = []*apipub.UserProfileResponse{&profile1, &profile2}
			profilesPtr = &profilesResult
		})

		g.It("foundIds nil found map", func() {
			g.Assert(foundIds(nil, profiles, profilesPtr)).Eql(profiles)
		})
		g.It("foundIds nil newProfiles array", func() {
			g.Assert(foundIds(found, profiles, nil)).Eql(profiles)
		})
		g.It("foundIds nil profile in element of newProfilesArray", func() {
			(*profilesPtr)[0] = nil
			nilProfileResult := profilesResult[1:]
			g.Assert(foundIds(found, profiles, profilesPtr)).Eql(nilProfileResult)
			g.Assert(found[userid1]).Eql(struct{}{})
		})
		g.It("foundIds found no ids", func() {
			emptyProfiles := make([]*apipub.UserProfileResponse, 0, len(userids))
			emptyProfilesPtr := &emptyProfiles
			found = make(map[string]struct{}, len(userids))
			g.Assert(foundIds(found, profiles, emptyProfilesPtr)).Eql(emptyProfiles)
			g.Assert(len(found)).Eql(0)
		})
		g.It("foundIds found all ids", func() {
			//fix arrays from previous test
			profilesResult = []*apipub.UserProfileResponse{&profile1, &profile2}
			profilesPtr = &profilesResult
			g.Assert(foundIds(found, profiles, profilesPtr)).Eql(profilesResult)
			g.Assert(found[userid1]).Eql(struct{}{})
			g.Assert(found[userid2]).Eql(struct{}{})
		})

		g.It("missingIds nil found map", func() {
			g.Assert(missingIds(nil, userids)).Eql(userids)
		})
		g.It("missingIds nil userids", func() {
			g.Assert(missingIds(found, nil)).Eql(missing)
		})
		g.It("missingIds found all ids", func() {
			missingRet := missingIds(found, userids)
			g.Assert(missingRet).Eql(missing)
			g.Assert(len(missingRet)).Eql(0)
		})
		g.It("missingIds all ids missing", func() {
			missing = userids
			found = make(map[string]struct{}, len(userids))
			missingRet := missingIds(found, userids)
			g.Assert(missingRet).Eql(missing)
			g.Assert(len(missing)).Eql(2)
			g.Assert(missingRet[0]).Eql(userid1)
			g.Assert(missingRet[1]).Eql(userid2)
		})
		g.It("missingIds one missing", func() {
			missing = []string{userid1}
			found[userid2] = struct{}{}
			missingRet := missingIds(found, userids)
			g.Assert(missingRet).Eql(missing)
			g.Assert(len(missingRet)).Eql(1)
			g.Assert(missingRet[0]).Eql(userid1)
		})

		g.It("reorderAndSanitizeProfiles", func() {
			useridsBw := []string{userid2, userid1}
			profilesResultBw := []*apipub.UserProfileResponse{&profile2, &profile1}
			reordered := reorderAndSanitizeProfiles(profilesResult, useridsBw)
			g.Assert(reordered).Eql(profilesResultBw)
			g.Assert(len(reordered)).Eql(2)
			g.Assert(reordered[0].Userid).Eql(useridsBw[0])
			g.Assert(reordered[0].Dob).IsNil()
			g.Assert(reordered[0].Email).IsNil()
			g.Assert(reordered[1].Userid).Eql(useridsBw[1])
			g.Assert(reordered[1].Dob).IsNil()
			g.Assert(reordered[1].Email).IsNil()
		})
	})
}

func TestGetUserProfiles(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	userid1 := "e12c3df480984141b2f385646b2024fa"
	userid2 := "2017e9305ccc4e5781d076403c1b6725"
	userids := []string{userid1, userid2}
	name := "user"
	name2 := "user1"

	var profile1 apipub.UserProfileResponse
	var profile2 apipub.UserProfileResponse
	var profilesResult []*apipub.UserProfileResponse
	var profilesPtr *[]*apipub.UserProfileResponse
	var profileArr1 []*apipub.UserProfileResponse
	var profileArr2 []*apipub.UserProfileResponse
	var r *http.Request

	g.Describe("GetUserProfiles", func() {
		g.Before(func() {
			profile1 = apipub.UserProfileResponse{
				Userid:      userid1,
				DisplayName: &name,
			}
			profile2 = apipub.UserProfileResponse{
				Userid:      userid2,
				DisplayName: &name2,
			}
			profilesResult = []*apipub.UserProfileResponse{&profile1, &profile2}
			profilesPtr = &profilesResult
			_, r = Login(User2JWT)

			profileArr1 = []*apipub.UserProfileResponse{&profile1}
			profileArr2 = []*apipub.UserProfileResponse{&profile2}
		})

		g.It("get users from Cache", func() {
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(profilesPtr, nil)
			retProfiles, err := mock.api.GetUserProfiles(r.Context(), userids, false)
			g.Assert(err).IsNil()
			g.Assert(retProfiles).Eql(profilesPtr)
		})

		g.It("get users from dyanamo", func() {
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(profilesPtr, nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			retProfiles, err := mock.api.GetUserProfiles(r.Context(), userids, false)
			g.Assert(err).IsNil()
			g.Assert(retProfiles).Eql(profilesPtr)
		})

		g.It("get user from Cache and dyanamo", func() {
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(&profileArr1, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), userids[1:]).Return(&profileArr2, nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			retProfiles, err := mock.api.GetUserProfiles(r.Context(), userids, false)
			g.Assert(err).IsNil()
			g.Assert(retProfiles).Eql(profilesPtr)
		})

		g.It("get user from Cache and dna", func() {
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(&profileArr1, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), userids[1:]).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), userids[1:]).Return(&profileArr2, nil)
			mock.ds.EXPECT().PutUserProfiles(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			retProfiles, err := mock.api.GetUserProfiles(r.Context(), userids, true)
			g.Assert(err).IsNil()
			g.Assert(retProfiles).Eql(profilesPtr)
		})

		g.It("get user from dynamo and dna", func() {
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), userids).Return(&profileArr1, nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), userids[1:]).Return(&profileArr2, nil)
			mock.ds.EXPECT().PutUserProfiles(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			retProfiles, err := mock.api.GetUserProfiles(r.Context(), userids, true)
			g.Assert(err).IsNil()
			g.Assert(retProfiles).Eql(profilesPtr)
		})
	})
}
