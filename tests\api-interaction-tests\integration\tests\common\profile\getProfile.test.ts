import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

describe('', () => {
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(1, ["user"]);
    await usersTwok.loginAll({});
  });

  afterEach(async () => {
    await usersTwok.logoutAll({});
  });

  /**
   * Checking get profile
   * - Get profile
   * - Check if userid is matched
   */
  it('Get profile[public v1 v2 happy]', async () => {
    let testCase = {
      description: "get user profile for the current logged-in user",
      expected: "correct user profile is retrieved"
    }
    const actualResp: request.Response = await socialApi.getProfile(usersTwok.acct["user"]);

    const expectedResp = {
      status: StatusCodes.OK,
      body: {
        displayName: usersTwok.acct["user"].displayName,
        userid: usersTwok.acct["user"].publicId,
      },
    };
    socialApi.expectMore(
      () => {expect(actualResp).toMatchObject(expectedResp)}, 
      testCase, 
      {
        resp: actualResp,
        additionalInfo: {
          "fail reason": "unexpected displayName or userid"
        }
      }
    );
  });
});