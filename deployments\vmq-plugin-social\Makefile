.PHONY: build help reload start stop shell test docker
default: help

ERL_IMAGE=t2gp/erlang:25
VMQ0=vmq0
VMQ1=vmq1

ifeq ($(shell uname), "Windows_NT")
BUILD_DATE=$(shell busybox date +%Y-%m-%dT%H:%M:%S:%z)
else
BUILD_DATE=$(shell date +%Y-%m-%dT%H:%M:%S:%z)
endif

help: ## Show help
	@awk 'BEGIN {FS = ":.*?## "} /^([a-zA-Z_-][^:]+):.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

compile build: version ## Build the plugin
	@if [ "$(shell docker images -q ${ERL_IMAGE})" = "" ]; then ${MAKE} erlang-image; fi
	docker run --rm -v $(shell pwd):/build --workdir /build $(ERL_IMAGE) ./rebar3 compile

deps: ## Download or update dependencies
	docker run --rm -v $(shell pwd):/build --workdir /build $(ERL_IMAGE) ./rebar3 deps

build-shell: ## Get a shell to run rebar3
	docker run --rm -it -v $(shell pwd):/build --workdir /build $(ERL_IMAGE) bash

release:
	@if [ "$(shell docker images -q ${ERL_IMAGE})" = "" ]; then ${MAKE} erlang-image; fi
	docker run --rm -v $(shell pwd):/build --workdir /build $(ERL_IMAGE) ./scripts/bundle.sh

test: ## Run unit tests
	${MAKE} erlang-image
	docker-compose -f docker/docker-compose-shell.yml up -d
	docker-compose -f docker/docker-compose-shell.yml exec -T erlang ./rebar3 do ct --cover, cover --verbose
	cp _build/test/cover/ct.coverdata ct.coverdata

test-down: ## Cleanup test docker containers
	docker-compose -f docker/docker-compose-shell.yml down --remove-orphans

clean:
	rm -rf _build

reload: ## Reload plugin into VMQ
	-docker-compose exec vmq0 vmq-admin plugin disable --name t2gp_social
	docker-compose exec vmq0 vmq-admin plugin enable --name t2gp_social --path /devel/_build/default/lib/t2gp_social
	docker-compose exec vmq0 vmq-admin plugin show

plugins: ## Show plugins
	docker-compose exec vmq0 vmq-admin plugin show

start: ## Start VerneMQ for development
	docker-compose up -d

stop: ## Stop VMQ docker container
	docker-compose down --remove-orphans

rebuild: ## Rebuild VMQ container
	make stop && make build && make start

restart: ## Restart VMQ container
	docker-compose down
	docker-compose up -d

shell: ## Create a shell in the VMQ docker container
	docker-compose exec $(VMQ0) bash

shell0:
	docker-compose exec $(VMQ0) bash

shell1:
	docker-compose exec $(VMQ1) bash

rebar-shell: ## Start a rebar shell
	docker run --rm -it -v $(shell pwd):/build --workdir /build $(ERL_IMAGE) ./rebar3 shell

test-shell: ## Start a test shell
	docker-compose -f docker/docker-compose-shell.yml up -d
	docker-compose -f docker/docker-compose-shell.yml exec erlang bash

deploy-develop: build release ## deploy to develop
	@for p in social-service-mqtt-develop-0 social-service-mqtt-develop-1; do \
		echo "Deploying to $$p..."; \
		echo "  - Copying vmq-plugin-social.tar.gz to $$p:/tmp/vmq-plugin-social.tar.tgz"; \
		kubectl cp -c social-mqtt -n social-service ./_build/vmq-plugin-social.tar.gz $$p:/tmp/vmq-plugin-social.tar.tgz; \
		echo "  - Untar to /vernemq/plugins/t2gp-social"; \
		kubectl exec -it -c social-mqtt -n social-service $$p -- bash -c "cd /vernemq/plugins/t2gp-social && tar -xf /tmp/vmq-plugin-social.tar.tgz"; \
		echo -n "  - Reloading t2gp_social plugin..."; \
		kubectl exec -it -c social-mqtt -n social-service $$p -- bash -c "vmq-admin t2gp reload"; \
		# echo -n "  - Unloading previous t2gp_social plugin..."; \
		# kubectl exec -it -c social-mqtt -n social-service $$p -- bash -c "vmq-admin plugin disable --name t2gp_social"; \
		# echo -n "  - Loading new t2gp_social plugin..."; \
		# kubectl exec -it -c social-mqtt -n social-service $$p -- bash -c "vmq-admin plugin enable --name t2gp_social --path /vernemq/plugins/t2gp-social"; \
		echo "  - Deploy complete"; \
		kubectl exec -it -c social-mqtt -n social-service $$p -- bash -c "vmq-admin t2gp version"; \
	done

eks-login: ## Login to EKS non-production
	aws eks --region us-east-1 update-kubeconfig --name t2gp-non-production

upgrade: ## Upgrade packages after changing rebar.config module version
	docker run --rm -it -v $(shell pwd):/build --workdir /build $(ERL_IMAGE) ./rebar3 upgrade

logs: ## Show VerneMQ logs
	docker-compose logs -f --tail 10

version: # Update version file
	@rm -f src/t2gp_social_ver.erl
	@${MAKE} src/t2gp_social_ver.erl

erlang-image:
	docker build -f docker/Dockerfile-erlang -t $(ERL_IMAGE) .

src/t2gp_social_ver.erl:
	@echo Generating $@
	@echo "-module(t2gp_social_ver)." > $@
	@echo "-export([version/0])." >> $@
	@echo "version() -> [" >> $@
	@export VERSION="$(shell grep vsn src/t2gp_social.app.src | awk -F '"' '{print $$2}' | sed -E 's/(.*)\..*/\1/').$(shell git rev-list --all --count)-$(shell git rev-parse --short HEAD)" && \
		echo "    { version, \"$${VERSION}\" }," >> $@
	@export GIT_HASH="$(shell git rev-list -1 HEAD)" && \
		echo "    { git_hash, \"$${GIT_HASH}\" }," >> $@
	@echo "    { build_date, \"${BUILD_DATE}\"" } >> $@
	@echo "]." >> $@

docker: version ## Build docker image
	docker build -f docker/Dockerfile -t 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-vernemq .

publish: ## Publish docker image to ECR
	docker tag 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-vernemq:latest 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-vernemq:$(shell git rev-parse --short HEAD)
	aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 354767525209.dkr.ecr.us-east-1.amazonaws.com
	docker push 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-vernemq:$(shell git rev-parse --short HEAD)

dialyzer: ## Run dialyzer static analysis tool
	mkdir -p _build/test/cover
	docker run --rm -v $(shell pwd):/app --workdir /app $(ERL_IMAGE) ./rebar3 dialyzer -- -o _build/test/cover/dialyzer.log

sq:
	sonar-scanner \
		-Dsonar.login=$(SONARQUBE_TOKEN) \
	-Dsonar.host.url=https://sonarqube.take2games.com \
	-Dsonar.projectKey=t2gp-vmq-plugin-social \
	-Dsonar.branch.name=$(shell git rev-parse --abbrev-ref HEAD) \
	-Dsonar.erlang.ct.coverdata.filename=ct.coverdata \
	-Dsonar.exclusions=_build,src/reloader.erl,src/t2gp_social_ver.erl \
	-Dsonar.sources=src \
