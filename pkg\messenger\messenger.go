// Package messenger implements functions to call VMQ HTTP API
package messenger

// Envelope to read type
type Envelope struct {
	Type string `json:"type"`
}

// Text struct to for text return type
type Text struct {
	Text string `json:"text"`
	Type string `json:"type"`
}

// Alert struct for alert type
type Alert struct {
	Type  string `json:"type"`
	Alert []Text `json:"alert"`
}

// Table struct for table type
type Table struct {
	Type  string `json:"type"`
	Table []Row  `json:"table"`
}

// Row type for string:string map
type Row map[string]interface{}

// Param struct used for defining the API Query
type Param struct {
	Key   string
	Value string
}

// Params array of Param
type Params []Param

// MqttMessageType defines model for mqttMessageType.
type MqttMessageType string

// Defines values for MqttMessageType.
const (
	MqttMessageTypeChatMessageDM MqttMessageType = "chatMessageDM"

	MqttMessageTypeChatMessageGroup MqttMessageType = "chatMessageGroup"

	MqttMessageTypeFriendInvite MqttMessageType = "friendInvite"

	MqttMessageTypeFriendRemoved MqttMessageType = "friendRemoved"

	MqttMessageTypeGroupControlMessage MqttMessageType = "groupControlMessage"

	MqttMessageTypeGroupInviteReceived MqttMessageType = "groupInviteReceived"

	MqttMessageTypeGroupJoinRequest MqttMessageType = "groupJoinRequest"

	MqttMessageTypeGroupMembersModified MqttMessageType = "groupMembersModified"

	MqttMessageTypeGroupModified MqttMessageType = "groupModified"

	MqttMessageTypePresence MqttMessageType = "presence"

	MqttMessageTypeEndorseIncrement MqttMessageType = "endorseIncrement"

	MqttMessageTypeEndorseReset MqttMessageType = "endorseReset"

	MqttMessageTypeEndorseRemove MqttMessageType = "endorseRemove"
)

// MqttMessage defines model for mqttMessage.
type MqttMessage struct {
	Data interface{}     `json:"data"`
	Type MqttMessageType `json:"type"`
}

//type MessagingInterface interface {
//	//vernemq.go
//	VMQApiCall(command string, params Params, context aws.Context) (*[]Row, error)
//	VMQApiCallNamed(apiName string, command string, params Params, context aws.Context) (*[]Row, error)
//	VMQStatus() (*string, error)
//	VMQSessions() (*[]Row, error)
//	Publish(topic string, message string) error
//	Subscribe(userid string, topic string) error
//	Unsubscribe(userid string, topic string) error
//	GetSubscriptions(userid string) (*[]Row, error)
//	DeleteSubscriptions(userid string) error
//	UnsubscribePresence(userid, blockedUserId string)
//
//	//mqtt.go
//	NewMqttMessage(msgType MqttMessageType, data interface{}) MqttMessage
//	SendMqttMessage(topic string, msgType MqttMessageType, data interface{})
//}
