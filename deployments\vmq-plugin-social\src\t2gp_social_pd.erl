-module(t2gp_social_pd).

-include_lib("t2gp_social.hrl").

%% gen_server callbacks
-export([
    start_link/0,
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

-export([
    get_key_from_jwks/3
]).

-record(state, {
    base_url,
    jwks
}).

-define(APM_REQUEST, <<"pd">>).

%
% gen_server
%

-spec start_link() -> {ok, pid()} | {error, {already_started, pid()}}.
start_link() ->
    lager:info("t2gp_social_pd:start_link"),
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

-spec init(term()) -> {ok, state()}.
init(_) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:info("t2gp_social_pd:init"),
        case application:get_env(t2gp_social, pd_private_url) of
            {ok, ServiceURL} ->
                NewState = #state{
                    base_url = ServiceURL,
                    jwks = undefined
                },
                {ok, NewState};
            _ ->
                {ok, #state{
                    base_url = "https://account.id.privatedivision.com",
                    jwks = undefined
                }}
        end
    end).

-spec validate_rs256(binary(), binary(), state()) -> {reply, term(), state()}.
validate_rs256(Kid, JWT, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        Jwks = State#state.jwks,
        BaseURL = State#state.base_url,
        URL = BaseURL ++ "/.well-known/jwks.json",
        case get_key_from_jwks(URL, Kid, Jwks) of
            {ok, Key, NewJwks} ->
                NewState = State#state{jwks = NewJwks},
                {reply, jwt:decode(JWT, Key), NewState};
            {error, Err} ->
                {reply, {error, Err}, State}
        end
    end).

-spec handle_call({atom(), term()}, {pid(), term()}, state()) -> {reply, term(), state()}.
handle_call({validate_jwt, JWT}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{cmd => <<"validate_jwt">>}),
        case binary:split(JWT, <<".">>, [global]) of
            [Header, _Claims, _Signature] ->
                HeaderJSON = jsx:decode(base64url:decode(Header)),
                case proplists:get_value(<<"alg">>, HeaderJSON) of
                    <<"RS256">> ->
                        Kid = proplists:get_value(<<"kid">>, HeaderJSON),
                        validate_rs256(Kid, JWT, State);
                    Alg ->
                        lager:error("Unknown alg in JWT: ~p", [Alg]),
                        {reply, {error, invalid_alg}, State}
                end;
            _ ->
                {reply, {error, invalid_token}, State}
        end
    end);
handle_call({clear_jwks}, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{cmd => <<"clear_jwks">>}),
        lager:info("Clearing jwks cache in t2gp_social_pd"),
        NewState = State#state{jwks = undefined},
        {reply, ok, NewState}
    end);
handle_call(Msg, _From, State) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        lager:info("Unhandled message: ~p", [Msg]),
        {reply, {error, invalid_msg}, State}
    end).

-spec handle_cast(term(), state()) -> {noreply, state()}.
handle_cast(_Msg, State) ->
    {noreply, State}.

-spec handle_info(term(), state()) -> {noreply, state()}.
handle_info(_Msg, State) ->
    {noreply, State}.

-spec terminate(normal | shutdown | {shutdown, term()} | term(), term()) -> ok.
terminate(Reason, State) ->
    lager:info("t2gp_social_pd:terminate ~p, ~p", [Reason, State]),
    ok.

-spec code_change(term() | {down, term()}, state(), term()) -> {ok, state()} | {error, term()}.
code_change(OldVsn, State, Extra) ->
    lager:info("t2gp_social_pd:code_change ~p, ~p, ~p", [OldVsn, State, Extra]),
    {ok, State}.

%
% http helpers
%

%% 2K key is is a uuid. e.g. 03a61152-ecd9-4936-80d2-4536c9e64556
%% BaseURL https://sso.api.2kcoretech.online/sso/v2.0
-spec get_key_from_jwks(string(), binary(), binary() | undefined) ->
    {ok, proplists:proplist(), binary()} | {error, term()}.
get_key_from_jwks(_URL, Kid, Jwks) when is_binary(Jwks) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{kid => Kid, cached => <<"true">>}),
        case jwk:decode(Kid, Jwks) of
            {ok, Key} ->
                {ok, Key, Jwks};
            {error, Err} ->
                {error, Err}
        end
    end);
get_key_from_jwks(URL, Kid, undefined) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{kid => Kid, cached => <<"false">>}),
        Method = get,
        Header = [
            {<<"Content-Type">>, <<"application/json">>}
        ],
        lager:info("Fetching jwks ~p", [URL]),
        t2gp_social_apm:span_start(?APM_REQUEST, list_to_binary(io_lib:format("GET ~s", [URL]))),
        t2gp_social_apm:tags(#{method => atom_to_binary(Method), url => URL}),
        Result =
            case hackney:request(Method, URL, Header, <<>>, [with_body]) of
                {ok, 200, _Headers, Body} ->
                    NewJwks = Body,
                    case jwk:decode(Kid, NewJwks) of
                        {ok, Key} ->
                            {ok, Key, NewJwks};
                        {error, Err} ->
                            {error, Err}
                    end;
                {ok, Code, Headers, Body} ->
                    lager:error("PD jwks fetch failed: ~p ~p ~p", [URL, Headers, Body]),
                    {error, {http_error, Code, Headers, Body}};
                {error, Err} ->
                    lager:error("PD jwks fetch failed: result ~p", [Err]),
                    {error, Err}
            end,
        t2gp_social_apm:span_finish(),
        Result
    end).
