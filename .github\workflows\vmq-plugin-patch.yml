name: <PERSON> VernemQ Plugin
run-name: Patching ${{ github.event.inputs.env }} VMQ Plugin - ${{ github.event.inputs.vmq_plugin_sha }}

on:
  workflow_dispatch:
    inputs:
      env:
        required: true
        type: choice
        options:
          - "develop"
          - "integration"
          - "staging"
          - "cert"
          - "cert-sumo"
          # - "production"
        description: "Environment to make the swap"
      vmq_plugin_sha:
        type: string
        required: true
        description: "8 character SHA of the VMQ plugin version"

permissions:
  id-token: write
  contents: write

jobs:
  swap-vmq-plugin:
    name: Swap VMQ Plugin
    runs-on: [t2gp-arc-linux]
    env:
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables
        run: |
          echo SUBMODULE_HASH=${{ github.event.inputs.vmq_plugin_sha }} >> $GITHUB_ENV
          echo TARGET_ENV=${{ github.event.inputs.env }} >> $GITHUB_ENV
      - name: Target Production Cluster
        if: contains(fromJson('["cert","staging","cert-sumo","production"]'), env.TARGET_ENV)
        run: echo CLUSTER=t2gp-production >> $GITHUB_ENV
      - name: Target Non-production cluster
        if: contains(fromJson('["develop","loadtesting","sre-sandbox"]'), env.TARGET_ENV)
        run: echo CLUSTER=t2gp-non-production >> $GITHUB_ENV
      - name: Install kubectl
        uses: azure/setup-kubectl@v4.0.0
      - name: Swap VernemQ Plugin
        working-directory: deployments
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Update plugin version to AWS Parameter Store
        run: |
          aws ssm put-parameter --overwrite --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --value ${{ env.SUBMODULE_HASH }}


