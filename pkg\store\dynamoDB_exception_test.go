package store

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
)

func TestSanitizeDynamoDBException(t *testing.T) {
	g := goblin.Goblin(t)
	query := "invalidQuery"
	ddb := NewDynamoDB(context.TODO(), cfg)

	invalidInput := dynamodb.QueryInput{
		TableName:              aws.String(cfg.ProfileTable),
		KeyConditionExpression: aws.String(query),
	}
	resourceNotFoundInput := dynamodb.QueryInput{
		TableName:              aws.String("SomeTable"),
		KeyConditionExpression: aws.String(fmt.Sprintf("%s = :keyValue", "key")),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":keyValue": &types.AttributeValueMemberS{Value: "value"},
		},
	}

	g.Describe("Verify sanitized exceptions", func() {
		g.Before(func() {
			createRequest := dynamodb.CreateTableInput{
				TableName: &cfg.ProfileTable,
				AttributeDefinitions: []types.AttributeDefinition{
					{AttributeName: aws.String("pk"), AttributeType: "S"},
					{AttributeName: aws.String("sk"), AttributeType: "S"},
				},
				KeySchema: []types.KeySchemaElement{
					{AttributeName: aws.String("pk"), KeyType: "HASH"},
					{AttributeName: aws.String("sk"), KeyType: "RANGE"},
				},
				ProvisionedThroughput: &types.ProvisionedThroughput{
					ReadCapacityUnits: aws.Int64(10), 
					WriteCapacityUnits: aws.Int64(10),
				},
			}
			deleteRequest := dynamodb.DeleteTableInput{
				TableName: &cfg.ProfileTable,
			}

			(*ddb).DeleteTable(context.TODO(), &deleteRequest)
			(*ddb).CreateTable(context.TODO(), &createRequest)
		})
		g.It("verify ValidationException when calling QueryByPkSkWithLimitAndNext", func() {
			g.Timeout(45 * time.Second)
			_, _, err := ds.QueryByPkSkWithLimitAndNext(ctx, invalidInput)
			g.Assert(err.Error()).Equal("validation failed. Please check your request")
		})
		g.It("verify ResourceNotFound when calling QueryByPkSkWithLimitAndNext", func() {
			_, _, err := ds.QueryByPkSkWithLimitAndNext(ctx, resourceNotFoundInput)
			g.Assert(err.Error()).Equal("the resource cannot be found")
		})
		g.It("Should return ConditionalCheckFailedException error", func() {
			// Create a PutItemInput with the table name, item, and condition expression
			input := dynamodb.PutItemInput{
				TableName: &cfg.ProfileTable,
				Item: map[string]types.AttributeValue{
					"pk": &types.AttributeValueMemberS{Value: "partition-key-value"},
					"sk": &types.AttributeValueMemberS{Value: "sort-key-value"},
					"someKey": &types.AttributeValueMemberS{Value:"attribute-value"},
				},
				ConditionExpression: aws.String("attribute_exists(someKey)"),
			}

			_, err := (*ddb).PutItem(ctx, &input)
			conditionalException := errs.SanitizeDynamoDBException(err)
			g.Assert(conditionalException.Error()).Equal("write operation failed because the condition specified in the request was not met")
		})
	})
}
