package identity

// implements calls to the DNA REST API (https://dev.take2games.com/docs/social/dna/api-trusted-server)
import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"time"

	"github.com/oapi-codegen/runtime/types"
	"github.com/segmentio/encoding/json"

	"github.com/2kg-coretech/dna-common/pkg/authn"
	"github.com/2kg-coretech/dna-common/pkg/discovery"
	"github.com/2kg-coretech/dna-common/pkg/identity"
	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/aws/aws-sdk-go-v2/aws"
	zlog "github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

var _ DNAServiceInterface = &DNAService{}
var _ health.DependentService = &DNAService{}

type DNAService struct {
	cfg            config.Config
	identityClient identity.Identity
	auth           authn.Authenticator
	serviceStatus  *health.ServiceStatus
	httpClient     utils.HTTPClientInterface
}

type DNAServiceInterface interface {
	health.DependentService
	SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error)
	SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error)
	GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error)
	Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error)
	GetIdentityServiceStr() IdentityService
}

func NewDNAService(cfg *config.Config) *DNAService {
	discoveryConfig := &discovery.ClientConfig{
		DiscoveryURL: cfg.DiscoveryURL,
		AppIDAuth:    cfg.AppID,
		BasicAuth:    cfg.AppBasicAuth,
	}
	discoveryClient := discovery.NewDiscoveryClient(discoveryConfig)

	ctx := context.Background()

	ssoConfig, discoveryErr := discoveryClient.GetConfigByName(ctx, "sso")
	for discoveryErr != nil {
		r := rand.Intn(1000) + 1000
		zlog.Error().Err(discoveryErr).Msgf("Discovery failed. Retrying in %dms", r)
		time.Sleep(time.Duration(r) * time.Millisecond)
		ssoConfig, discoveryErr = discoveryClient.GetConfigByName(ctx, "sso")
	}
	//set values in config for future requests
	cfg.SsoURL = ssoConfig.BaseURL
	cfg.SsoHost = ssoConfig.Host
	identityConfig := &identity.ClientConfig{
		ApplicationID: cfg.AppID,
		AppBasicAuth:  cfg.AppBasicAuth,
		IdentityURL:   ssoConfig.BaseURL,
		IdentityHost:  ssoConfig.Host,
	}

	identityClient := identity.NewIdentityClient(identityConfig)
	status := &health.ServiceStatus{
		Instances: []*health.InstanceInfo{
			{
				Id:     identityConfig.IdentityURL,
				Status: health.UNKNOWN,
			},
		},
		Status: health.UNKNOWN,
	}
	auth := authn.NewAuthentication(identityClient)
	return &DNAService{
		cfg:            *cfg,
		identityClient: identityClient,
		auth:           auth,
		serviceStatus:  status,
		httpClient:     http.DefaultClient,
	}
}

// Identity returns the DNA identity client
func (dnas *DNAService) Identity() identity.Identity {
	return dnas.identityClient
}

// Authenticate authenticate request header
func (dnas *DNAService) Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error) {
	userid := "unknown"
	if len(authHeader) == 0 {
		return nil, fmt.Errorf("%w", errs.New(http.StatusBadRequest, errs.EInvalidAuthHeader))
	}
	if len(authHeader) == 2 {
		token, err := jwt.ParseJWTTokenWithoutValidation(authHeader[1])
		if err == nil {
			userid = token.Claims.Subject
		}
	}
	span, _ := tracer.StartSpanFromContext(ctx, "dna.authJWT", tracer.ResourceName(userid))
	defer span.Finish()

	opts := authn.AuthOpts{
		SkipRevokedTokenValidation: skipRevokedTokenValidation,
	}

	return dnas.auth.AuthenticateWithContext(ctx, authHeader, opts)
}

func (dnas *DNAService) GetIdentityServiceStr() IdentityService {
	return IdentityServiceDNA
}

// SearchAccounts search DNA user database given first party userids (ex. steam, xbl, psn)
func (dnas *DNAService) SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error) {
	log := logger.FromContext(ctx)

	// create payload
	reqBody, err := json.Marshal(request)
	if err != nil {
		log.Error().Err(err).Msgf("failed to marshall request")
		return nil, err
	}
	url := dnas.cfg.SsoURL + "/user/accounts/search"
	log.Info().
		Str("reqBody", string(reqBody)).
		Msgf("Searching users POST %s", url)

	// http request to 2k server
	var req *http.Request
	req, err = http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	if err != nil {
		log.Error().Msgf("create failed failed url='%s' err='%v'", url, err)
		return nil, err
	}

	req.Header.Add(constants.KContentType, constants.KApplicationJson)
	req.Header.Add("Authorization", "Basic "+dnas.cfg.AppBasicAuth)

	span, _ := tracer.StartSpanFromContext(ctx, "dna.userSearch", tracer.ResourceName(req.URL.Path))
	var resp *http.Response
	resp, err = dnas.httpClient.Do(req)
	if err != nil {
		span.Finish()
		log.Error().Err(err).Msgf("POST failed %s %v", url, err)
		return nil, err
	}
	span.Finish()

	// forward body to client
	defer resp.Body.Close()
	var body []byte
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		dnaError := errs.New(http.StatusInternalServerError, errs.EDnaGeneric)
		err = json.Unmarshal(body, &dnaError)
		if err != nil {
			return nil, err
		}
		return nil, dnaError
	}

	type SearchAccountDNA struct {
		apipub.SearchAccountResponse
		LinkedAccounts *[]apipub.AccountLinkDNA `json:"linkedAccounts,omitempty"`
	}

	var responseDNA []SearchAccountDNA
	err = json.Unmarshal(body, &responseDNA)
	if err != nil {
		log.Error().Err(err).Str("body", string(body)).Msgf("unmarshal failed")
		return nil, err
	}
	response := apipub.SearchAccountResponseList{}
	for _, r := range responseDNA {
		r.Links = r.LinkedAccounts
		r.LinkedAccounts = nil
		response = append(response, r.SearchAccountResponse)
	}

	return &response, nil
}

func (dnas *DNAService) SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error) {
	searchType := apipub.AccountsById
	searchRequest := &apipub.SearchAccountRequest{
		Type: &searchType,
		Criterias: &[]apipub.SearchAccountCriteria{
			{
				AccountId: aws.String(userid),
			},
		},
	}
	searchResponse, err := dnas.SearchAccounts(ctx, searchRequest)
	if err != nil {
		return nil, err
	}

	return searchResponse, nil
}

func (dnas *DNAService) GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error) {
	log := logger.FromContext(ctx)
	url := dnas.cfg.SsoURL + "/user/accounts/" + userid + "/links"
	body, err := dnas.HttpGetFromDna(ctx, url)

	if err != nil {
		return nil, err
	}

	var response []apipub.AccountLinkDNA
	err = json.Unmarshal(body, &response)
	if err != nil {
		log.Error().Err(err).Str("body", string(body)).Msgf("unmarshal failed")
		return nil, err
	}

	return &response, nil
}

func (dnas *DNAService) IsCritical() bool {
	return false
}

func (dnas *DNAService) CheckHealth() bool {
	svc := dnas.serviceStatus.Instances[0]
	status := health.FAIL
	start := time.Now()

	err := dnas.identityClient.GetHealth()
	elapsed := time.Since(start).Milliseconds()
	if err != nil {
		health.SetInstance(svc, status, start, elapsed, err.Error())
		zlog.Err(err).Str("ctx", "health-check").Msg("DNA Identity Unhealthy")
	} else {
		status = health.OK
		health.SetInstance(svc, status, start, elapsed, "")
	}

	dnas.serviceStatus.Status = status
	return status == health.OK
}

func (dnas *DNAService) LastStatus() *health.ServiceStatus {
	return dnas.serviceStatus
}

func (dnas *DNAService) Login(ctx context.Context, username string, password string, locale string, appID string) (*apipub.LoginResponse, error) {
	log := logger.FromContext(ctx)

	loginRequest := apipub.LoginRequestDNA{
		Locale:      locale,
		AccountType: "full",
		Credentials: struct {
			Email    types.Email "json:\"email\""
			Password string      "json:\"password\""
			Type     string      "json:\"type\""
		}{
			Type:     "emailPassword",
			Email:    types.Email(username),
			Password: password,
		},
	}

	// fetch new tokens for the user
	path := "/auth/tokens"
	url := dnas.cfg.SsoURL + path

	span, _ := tracer.StartSpanFromContext(ctx, "dna.login", tracer.ResourceName(path))
	defer span.Finish()

	headers := map[string]string{
		constants.KContentType: constants.KApplicationJson,
		"Authorization":        "Application " + appID,
	}

	loginResponse := apipub.LoginResponse{}
	err := HttpRequest(ctx, dnas.httpClient, "POST", url, headers, &loginRequest, &loginResponse)
	if err != nil {
		log.Error().Err(err).Msgf("failed to login")
		return nil, errs.New(http.StatusUnprocessableEntity, errs.EDnaLoginFailed)
	}

	return &loginResponse, nil
}

func (dnas *DNAService) RefreshToken(ctx context.Context, refreshToken string, locale string) (*apipub.LoginResponse, error) {
	log := logger.FromContext(ctx)

	// create 2k payload
	refreshTokenRequestDNA := apipub.RefreshTokenRequestDNA{
		Locale:      locale,
		AccountType: "full",
		Credentials: struct {
			RefreshToken string "json:\"refreshToken\""
			Type         string "json:\"type\""
		}{
			Type:         "refresh",
			RefreshToken: refreshToken,
		},
	}

	// http request to 2k server
	path := "/auth/tokens"
	url := dnas.cfg.SsoURL + path

	span, _ := tracer.StartSpanFromContext(ctx, "dna.refreshToken", tracer.ResourceName(path))
	defer span.Finish()

	// We have to use the issuer from the refresh JWT token in the Authorization header
	token, err := jwt.ParseJWTTokenWithoutValidation(refreshToken)
	if err != nil {
		log.Error().Err(err).Msgf("JWT parse error %s", refreshToken)
		return nil, errs.New(http.StatusBadRequest, errs.EInvalidJWT)
	}

	headers := map[string]string{
		constants.KContentType: constants.KApplicationJson,
		"Authorization":        "Application " + token.Claims.Issuer,
	}

	refreshResponse := apipub.LoginResponse{}

	err = HttpRequest(ctx, dnas.httpClient, "POST", url, headers, &refreshTokenRequestDNA, &refreshResponse)
	if err != nil {
		log.Error().Err(err).Msgf("failed to refresh token")
		return nil, errs.New(http.StatusUnauthorized, errs.EDnaRefreshTokenFailed)
	}

	return &refreshResponse, nil
}

func (dnas *DNAService) Logout(ctx context.Context, token string) error {
	var err error
	log := logger.FromContext(ctx)
	path := "/auth/tokens/logout"
	url := dnas.cfg.SsoURL + path
	headers := map[string]string{
		constants.KContentType: constants.KApplicationJson,
		"Authorization":        "Bearer " + token,
	}

	span, _ := tracer.StartSpanFromContext(ctx, "dna.logout", tracer.ResourceName(path))
	defer span.Finish()

	err = HttpRequest(ctx, dnas.httpClient, "POST", url, headers, nil, nil)
	if err != nil {
		log.Error().Err(err).Msgf("failed to logout")
		return errs.New(http.StatusUnprocessableEntity, errs.EDnaLogoutFailed)
	}

	return nil
}

func (dnas *DNAService) UpdateConfig(ctx context.Context) error {
	return nil
}

func (dnas *DNAService) SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	log := logger.FromContext(ctx)
	var profile apipub.UserProfileResponse
	//Sync profile gets all links to store to dynamo.  We filter when retrieving it.
	searchResponse, err := dnas.SearchAccountsByUserID(ctx, userid)
	if err != nil {
		log.Error().Err(err).Str("userid", userid).Msg("dna user search failed")
	} else if searchResponse != nil {
		if len(*searchResponse) <= 0 {
			err = errors.New("dna user not found")
		}
		for _, account := range *searchResponse {
			now := time.Now().UTC()

			if userid == *account.Id {
				profile.Userid = userid
				profile.Created = &now
				profile.Email = nil // (*types.Email)(account.Email)
				profile.DisplayName = account.DisplayName
				profile.Links = account.Links
				profile.Locale = account.Locale
				profile.Dob = account.Dob
				dnas.SetAgeGroupFromDob(ctx, &profile)
				profile.Dob = nil
			}
		}
	} else {
		err = errors.New("searchResponse is nil")
	}

	if err != nil {
		log.Error().Err(err).Str("userid", userid).Msg("SyncUserProfile failed")
	}

	return &profile, err
}

func (dnas *DNAService) SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	log := logger.FromContext(ctx)

	var criteria []apipub.SearchAccountCriteria
	var searchRequest *apipub.SearchAccountRequest
	searchRequestType := apipub.AccountsById
	var profiles []*apipub.UserProfileResponse

	for _, q := range userids {
		criteria = append(criteria, apipub.SearchAccountCriteria{
			AccountId: aws.String(q),
		})
	}
	searchRequest = &apipub.SearchAccountRequest{
		Type:      &searchRequestType,
		Criterias: &criteria,
	}

	searchResponse, err := dnas.SearchAccounts(ctx, searchRequest)
	if err != nil {
		log.Error().Err(err).Strs("userids", userids).Msgf("failed to search accounts dna")
	} else if searchResponse != nil {
		if len(*searchResponse) <= 0 {
			err = errors.New("dna users not found")
		}
		for _, account := range *searchResponse {
			now := time.Now().UTC()

			var newProfile apipub.UserProfileResponse
			newProfile.Userid = *account.Id
			newProfile.Created = &now
			newProfile.Email = nil // (*types.Email)(account.Email)
			newProfile.DisplayName = account.DisplayName
			newProfile.Links = account.Links
			newProfile.Locale = account.Locale
			newProfile.Dob = account.Dob
			dnas.SetAgeGroupFromDob(ctx, &newProfile)
			newProfile.Dob = nil
			profiles = append(profiles, &newProfile)
		}
	} else {
		err = errors.New("searchResponse is nil")
	}

	if profiles == nil {
		return nil, err
	}

	return &profiles, err
}

func (dnas *DNAService) SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse) {
	if profile == nil || profile.Dob == nil || (profile.AgeGroup != nil && *profile.AgeGroup > 0) {
		return
	}

	log := logger.FromContext(ctx)
	dobFormat := "01/02/2006"
	unixYear := int64(365 * 24 * 60 * 60)
	ageGroup := 0 //Age Unknown

	dobTime, err := time.Parse(dobFormat, *profile.Dob)

	if err != nil {
		log.Error().Err(err).Interface("dob", *profile.Dob).Interface("locale", *profile.Locale).Msgf("failed to parse dob in SetAgeGroupFromDob")
		profile.AgeGroup = &ageGroup
		return
	}

	dobUnix := dobTime.UTC().Unix()
	unixNow := time.Now().UTC().Unix()

	diff := unixNow - dobUnix

	//DNA checks for EU first
	if isEurope(profile.Locale) {
		// Europe:
		if diff < 16*unixYear {
			ageGroup = 7 // Teen
		} else if diff < 24*unixYear {
			ageGroup = 8 // Adult
		} else { //if diff >= 24*unixYear {
			ageGroup = 9 // Adult
		}
	} else {
		// USA:
		if diff < 13*unixYear {
			ageGroup = 1 // Child
		} else if diff < 18*unixYear {
			ageGroup = 2 // Teen
		} else if diff < 25*unixYear {
			ageGroup = 3 // Adult
		} else if diff < 35*unixYear {
			ageGroup = 4 // Adult
		} else { // if diff >= 35*unixYear {
			ageGroup = 5 // Adult
		}
	}

	profile.AgeGroup = &ageGroup
}

func isEurope(locale *string) bool {
	if locale == nil || len(*locale) < 2 {
		return false
	}
	country := (*locale)[len(*locale)-2 : len(*locale)]

	//This list is from DNA SSO: GeoIpData.java
	euCodes := map[string]bool{
		"AT": true, // "Austria"
		"BE": true, // "Belgium"
		"BG": true, // "Bulgaria"
		"CY": true, // "Cyprus"
		"CZ": true, // "Czechia / Czech Republic"
		"DE": true, // "Germany"
		"DK": true, // "Denmark"
		"EE": true, // "Estonia"
		"ES": true, // "Spain"
		"FI": true, // "Finland"
		"FR": true, // "France"
		"GB": true, // "United Kingdom"
		"GR": true, // "Greece"
		"HR": true, // "Croatia"
		"HU": true, // "Hungary"
		"IE": true, // "Ireland"
		"IT": true, // "Italy"
		"LT": true, // "Lithuania"
		"LU": true, // "Luxembourg"
		"LV": true, // "Latvia"
		"MT": true, // "Malta"
		"NL": true, // "Netherlands"
		"PL": true, // "Poland"
		"PT": true, // "Portugal"
		"RO": true, // "Romania"
		"SE": true, // "Sweden"
		"SI": true, // "Slovenia"
		"SK": true, // "Slovakia"
		"EU": true, // "Europe"
		"FX": true} // "France, Metropolitan"

	if _, ok := euCodes[country]; ok {
		return true
	}

	return false
}

func (dnas *DNAService) GetProductIdFromAppId(ctx context.Context, appId string) (*string, error) {
	log := logger.FromContext(ctx)
	url := dnas.cfg.SsoURL + "/app/apps/" + appId
	body, err := dnas.HttpGetFromDna(ctx, url)

	if err != nil {
		return nil, err
	}

	var response map[string]interface{}
	err = json.Unmarshal(body, &response)
	if err != nil {
		log.Error().Err(err).Str("body", string(body)).Msgf("unmarshal failed")
		return nil, err
	}

	var productId string
	if response != nil && response["productId"] != nil {
		productId = response["productId"].(string)
	}
	return &productId, nil
}

func (dnas *DNAService) HttpGetFromDna(ctx context.Context, url string) ([]byte, error) {
	log := logger.FromContext(ctx)

	var err error
	var req *http.Request
	req, err = http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error().Err(err).Msgf("create failed failed url='%s'", url)
		return nil, err
	}

	req.Header.Add(constants.KContentType, constants.KApplicationJson)
	req.Header.Add("Authorization", "Basic "+dnas.cfg.AppBasicAuth)

	span, _ := tracer.StartSpanFromContext(ctx, "dna.getLinks", tracer.ResourceName(req.URL.Path))
	var resp *http.Response
	resp, err = dnas.httpClient.Do(req)
	if err != nil {
		span.Finish()
		log.Error().Err(err).Msgf("GET failed %s %v", url, err)
		return nil, err
	}
	span.Finish()

	// forward body to client
	defer resp.Body.Close()
	var body []byte
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		dnaError := errs.New(http.StatusInternalServerError, errs.EDnaGeneric)
		err = json.Unmarshal(body, &dnaError)
		if err != nil {
			return nil, err
		}
		return nil, dnaError
	}

	return body, nil
}

func FilterLinksByOST(links *[]apipub.AccountLinkDNA, onlineServiceType apipub.OnlineServiceType) {
	if links == nil {
		return
	}
	var ret []apipub.AccountLinkDNA
	ret = []apipub.AccountLinkDNA{}
	for _, link := range *links {
		if (link.OnlineServiceType != nil && *link.OnlineServiceType != onlineServiceType) || (link.LinkType != nil && *link.LinkType == "parent") {
			continue
		}
		ret = append(ret, link)
	}
	if len(ret) == 0 {
		*links = nil
		return
	}
	*links = ret
}
