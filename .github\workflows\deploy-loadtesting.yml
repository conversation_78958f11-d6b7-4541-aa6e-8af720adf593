name: Deploy loadtesting

on:
  repository_dispatch:
    types: ['Deploy to loadtesting']
  workflow_dispatch:
    inputs:
      version:
        description: 'Deploy Version'
      plugin_version:
        description: 'Deploy corresponding plugin version. This should exist in S3'
        default: ''
      skip_plugin_swap:
        type: boolean
        default: false
        description: 'Check this to skip plugin swap on this release'
      run_loadtest:
        type: boolean
        default: false
        description: 'Run loadtest if this is enabled.'
  workflow_call:
    inputs:
      version:
        description: 'Deploy Version'
        type: string
      plugin_version:
        description: 'Deploy corresponding plugin version. This should exist in S3'
        type: string
        default: ''
      skip_plugin_swap:
        type: boolean
        default: false
        description: 'Check this to skip plugin swap on this release'
      run_loadtest:
        type: boolean
        default: false
        description: 'Run loadtest if this is enabled.'
permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  run-helm:
    name: 'Run Helm Deploy'
    runs-on: [t2gp-arc-linux]
    env:
      AWS_DEFAULT_REGION: us-east-1
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
      TARGET_ENV: loadtesting
      CLUSTER: t2gp-testing
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
      git_sha: ${{ steps.output_info.outputs.git_sha }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
          ref: ${{ github.event.client_payload.ref }}
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables
        run: |
          ver=${{ github.event.client_payload.image_tag }}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${ver}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
      - name: Environment Variables (Manual)
        if: github.event.inputs.version != ''
        run: |
          ver=${{github.event.inputs.version}}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
      - name: Get Plugin Version (Override)
        run: |
          if [ "${{github.event.inputs.plugin_version}}" != "" ]; then
            echo "SUBMODULE_HASH=${{github.event.inputs.plugin_version}}" >> $GITHUB_ENV
          else
            echo "SUBMODULE_HASH=$(git ls-tree ${{env.GIT_SHA}} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
          fi
          echo "plugin version: $SUBMODULE_HASH"
      - name: Helm Deploy (loadtesting)
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.6
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          environment: ${{ env.TARGET_ENV}}-v2
          helm-values: 'social-api.groupsApi.image.tag="${{ env.IMAGE_TAG }}",social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Helm Trusted Deploy (loadtesting)
        id: helm_trusted_deploy
        uses: take-two-t2gp/app-charts-commit@v0.6
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-trusted-api
          environment: ${{ env.TARGET_ENV}}-v2
          helm-values: 'social-api.groupsApi.image.tag="${{ env.IMAGE_TAG }}",social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Get plugin version on AWS Parameter Store
        run: |
          set -ex
          CURRENTLY_STORED=$(aws ssm get-parameter --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --output text --query 'Parameter.Value')
          if [ "$CURRENTLY_STORED" == "${{ env.SUBMODULE_HASH }}" ]; then
            echo "UPDATE_PLUGIN=false" >> $GITHUB_ENV
          else
            echo "UPDATE_PLUGIN=true" >> $GITHUB_ENV
          fi
      - name: Install kubectl
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        uses: azure/setup-kubectl@v1
      - name: Swap VernemQ Plugin
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        working-directory: deployments
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Update plugin version to AWS Parameter Store
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        run: |
          aws ssm put-parameter --overwrite --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --value ${{ env.SUBMODULE_HASH }}
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "loadtesting", "version": "${{env.IMAGE_TAG}}", "api_url":"https://social-service-loadtesting.d2dragon.net/v2", "api_private_url":"https://social-service-loadtesting-private.d2dragon.net", "mqtt_url":"wss://social-service-loadtesting.d2dragon.net/mqtt" }'
      - name: Output Info
        id: output_info
        run: |
          echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
          echo "git_sha=${{ env.GIT_SHA }}" >> $GITHUB_OUTPUT
  # run-loadtest:
  #   name: Run Loadtest
  #   needs: [run-helm]
  #   uses: ./.github/workflows/run-loadtest.yml
  #   with:
  #     commit_sha: ${{needs.run-helm.outputs.git_sha}}
  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: loadtesting
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      api_test_note: 'deploy loadtesting'
    secrets: inherit
