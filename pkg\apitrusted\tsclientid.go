package apitrusted

import (
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

type TsClientIdInfo struct {
	ClientId  string `json:"clientid"`
	ProductId string `json:"productid"`
	TenantId  string `json:"tenantid"`
	Hash      string `json:"hash"`
}

// PK partition key - tenant param not used
func (tsCid *TsClientIdInfo) PK(tenant string) string {
	return BuildTsClientIdPK(tsCid.ClientId)
}

// SK sort key = PK - tenant param not used
func (tsCid *TsClientIdInfo) SK(tenant string) string {
	return BuildTsClientIdPK(tsCid.ClientId)
}

func BuildTsClientIdPK(clientid string) string {
	return "ts#" + GetTsEnv() + "#clientid#" + clientid
}

func GetTsEnv() string {
	env := "production"
	if utils.IsNonProdCluster() {
		env = "develop"
	}
	return env
}
