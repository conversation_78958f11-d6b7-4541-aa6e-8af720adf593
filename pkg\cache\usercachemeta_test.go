package cache

import (
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func Test_UserCacheMeta(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	var userid, fpid, key string
	var userMeta *apipub.UserCacheMeta
	var err error
	var bOk bool
	ttl := time.Duration(60) * time.Second
	ost := apipub.OnlineServiceTypeSTEAM

	g.Describe("Test UserCacheMeta", func() {

		g.Before(func() {
			userid = utils.GenerateRandomDNAID()
			fpid = utils.GenerateRandomDNAID()

			userMeta = &apipub.UserCacheMeta{
				Blocks:  true,
				Friends: true,
				Pending: true,
			}
		})

		g.It("set UserCacheMeta", func() {
			err = rc.SetUserCacheMeta(ctx, userid, userMeta, ttl)
			g.Assert(err).IsNil()
		})

		g.It("get UserCacheMeta", func() {
			userMeta, err = rc.GetUserCacheMeta(ctx, userid)
			g.Assert(err).IsNil()
			g.Assert(userMeta).IsNotNil()
			g.Assert(userMeta.Blocks).Equal(true)
			g.Assert(userMeta.Friends).Equal(true)
			g.Assert(userMeta.Pending).Equal(true)
			g.Assert(len(userMeta.FirstParty)).Equal(0)
		})

		g.It("set FirstPartyLookup", func() {
			key, err = rc.SetFirstPartyLookup(ctx, fpid, ost, userid, ttl)
			key2 := BuildFirstPartyRedisKey("dna", fpid, ost)
			g.Assert(err).IsNil()
			g.Assert(key).Equal(key2)
		})

		g.It("BuildFirstPartyRedisKey Nintendo", func() {
			fpidNin := "lp1-1234567890abcdef-1234567890ABCDEFGHIJKLMNOPQR"
			keyNin := "dna:user:firstparty:lp1-1234567890abcdef:ost:11"
			key2 := BuildFirstPartyRedisKey("dna", fpidNin, 11)
			g.Assert(key2).Equal(keyNin)
		})

		g.It("Update UserCacheMeta FirstParty", func() {
			userMeta.FirstParty = append(userMeta.FirstParty, key)
			err = rc.SetUserCacheMeta(ctx, userid, userMeta, ttl)
			g.Assert(err).IsNil()
		})

		g.It("get UserCacheMeta FirstParty", func() {
			userMeta, err = rc.GetUserCacheMeta(ctx, userid)
			g.Assert(err).IsNil()
			g.Assert(userMeta).IsNotNil()
			g.Assert(userMeta.Blocks).Equal(true)
			g.Assert(userMeta.Friends).Equal(true)
			g.Assert(userMeta.Pending).Equal(true)
			g.Assert(len(userMeta.FirstParty)).Equal(1)
			g.Assert(userMeta.FirstParty[0]).Equal(key)
		})

		g.It("get FirstPartyLookup", func() {
			var parentid *string
			parentid, err = rc.GetFirstPartyLookup(ctx, fpid, ost)
			g.Assert(err).IsNil()
			g.Assert(parentid).IsNotNil()
			g.Assert(*parentid).Equal(userid)
		})

		g.It("Update FirstPartyLookup ttl", func() {
			bOk, err = rc.UpdateFirstPartyLookupTtl(ctx, key, time.Duration(5)*time.Second)
			g.Assert(err).IsNil()
			g.Assert(bOk).Equal(true)
		})

		g.It("Update UserCacheMeta ttl", func() {
			bOk, err = rc.UpdateUserCacheMetaTtl(ctx, userid, time.Duration(5)*time.Second)
			g.Assert(err).IsNil()
			g.Assert(bOk).Equal(true)
		})
	})
}
