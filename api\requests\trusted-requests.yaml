serverCreateGroupRequestBody:
  description: Create group with members
  required: true
  content:
    application/json:
      schema:
        type: object
        required:
          - groupLeader
          - onlineServiceType
          - joinRequestAction
        properties:
          maxMembers:
            $ref: '../schemas/fields.yaml#/maxMembers'
          joinRequestAction:
            $ref: '../schemas/fields.yaml#/joinRequestAction'
          password:
            $ref: '../schemas/fields.yaml#/password'
          canMembersInvite:
            $ref: '../schemas/fields.yaml#/canMembersInvite'
          canCrossPlay:
            $ref: '../schemas/fields.yaml#/canCrossPlay'
          meta:
            $ref: '../schemas/schemas.yaml#/meta'
          groupLeader:
            $ref: '../schemas/fields.yaml#/dnaid'
          onlineServiceType:
            $ref: '../schemas/fields.yaml#/onlineServiceType'
          groupMembers:
            type: array
            items:
              $ref: '../schemas/schemas.yaml#/joinGroupMember'
            maxItems: 100
          teleMeta:
            $ref: '../schemas/schemas.yaml#/telemetryMetaData'
          returnMembershipErrors:
            type: boolean
            default: false
            example: true
      examples:
        'Create public group that anyone can join':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            maxMembers: 5
            joinRequestAction: auto-approve
        'Create manual group with a password':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            joinRequestAction: manual
            password: '********'
        'Create invite only group':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            joinRequestAction: auto-reject
            canMembersInvite: true
        'Create a group where inviter has to accept invite':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            joinRequestAction: manual
            canMembersInvite: true
        'Create crossplay group':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            joinRequestAction: manual
            canCrossPlay: true
        'Create a group with meta data':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            joinRequestAction: manual
            meta:
              key1: value1
              key2: value2
        'Create a non-crossplay group with additional group members':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            canCrossPlay: false
            maxMembers: 3
            joinRequestAction: auto-reject
            groupMembers:
              - memberid: abcdef1234567890abcdef1234567890
                canCrossPlay: false
                onlineServiceType: 3
              - memberid: 1234567890abcdef1234567890abcdef
                canCrossPlay: true
                onlineServiceType: 3
        'Create a crossplay group with group additional members that returns membership errors':
          value:
            groupLeader: ********************************
            onlineServiceType: 3
            canCrossPlay: true
            maxMembers: 3
            joinRequestAction: auto-reject
            groupMembers:
              - memberid: abcdef1234567890abcdef1234567890
                canCrossPlay: true
                onlineServiceType: 2
              - memberid: 1234567890abcdef1234567890abcdef
                canCrossPlay: true
                onlineServiceType: 1
            returnMembershipErrors: true
serverUpdateGroupRequestBody:
  description: Update group request body
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          maxMembers:
            $ref: '../schemas/fields.yaml#/maxMembers'
          meta:
            $ref: '../schemas/schemas.yaml#/meta'
          joinRequestAction:
            $ref: '../schemas/fields.yaml#/joinRequestAction'
          password:
            $ref: '../schemas/fields.yaml#/password'
          canMembersInvite:
            $ref: '../schemas/fields.yaml#/canMembersInvite'
          teleMeta:
            $ref: '../schemas/schemas.yaml#/telemetryMetaData'
serverUpdateGroupMemberRequestBody:
  description: Update group member role body
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          role:
            $ref: '../schemas/fields.yaml#/groupMemberRole'
          teleMeta:
            $ref: '../schemas/schemas.yaml#/telemetryMetaData'
serverSendControlMessageRequestBody:
  description: Control message request body. Max size for binary is 5120 bytes
  required: true
  content:
    application/json:
      schema:
        required:
          - payload
        properties:
          payload:
            type: string
            example: 'ZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFHRUFBQUJ4Q0FZQUFBREYwTTA0QUFBQUFYTlNSMElBcnM0YzZRQUFBQVJuUVUxQkFBQ3hqd3Y4WVFVQUFBQUpjRWhaY3dBQURzUUFBQTdFQVpVckRoc0FBQUt0U1VSQlZIaGU3WnRiVGdOQkRBU3puQ3hIejgwQ1NIelRJejg2SGFoSWZIbkg5bFF4bTNnRDErMTJlMzc5OEhvaGdZOFgxcWIwRHdFa0JQd3FJQUVKQVFRQ1d1QWtJQ0dBUUVBTG5BUWtCQkFJYUlHVGdJUUFBZ0V0Y0JJQ0pGeU9aMGZQNTNzL25ycXViMHg3TDA3Q0h0dmp6RWc0UnJWM0lSTDIyQjVuUnNJeHFyMExrYkRIOWpnekVvNVI3VjJJaEQyMng1bmJjOExKRExEOU9mdDR0OFVMMVI2NysrTWtGTVZNTGtQQ0pNMWlMaVFVd1UwdVE4SWt6V0l1SkJUQlRTNUR3aVROWWk0a0ZNRk5Ma1BDSk0xaUxpUVV3VTB1UThJa3pXSXVKQlRCVFM1RHdpVE5ZaTRrRk1GTkxrUENKTTFpTGlRVXdVMHVpL2crWWZ0NWZUZC9kNzBTeGtsUWhBeHhKQmdncXhKSVVJUU1jU1FZSUtzU1NGQ0VESEVrR0NDckVraFFoQXp4OXB6d2VEeGttL2Y3WFY2VGZJSGFZM2QvbklRQSswaEFRZ0NCZ0JZNENVZ0lJQkRRQWljQkNRRUVBbHF3ekFrQisxeHRnVGxoRmE4bk9lOEpIczYvVmtFQ0VnSUlCTFRBU1VCQ0FJR0FGamdKQVJJaTVnVDFPVnY5M1kvaXFQN1BXSDFmb1BLci90VjZUb0lpWklnandRQlpsVUNDSW1TSUk4RUFXWlZBZ2lKa2lDUEJBRm1WUUlJaVpJaEh6QW1HZmE2V1lFNVl4ZXRKenUzSXc1bnZFd0k0SXdFSjZRUUMrdU05QVFrQkJBSmFhTThKSjN2b1BxOC9xYkY1VFhjT1VMMXhPMUtFREhFa0dDQ3JFa2hRaEF4eEpCZ2dxeEpJVUlRTWNTUVlJS3NTU0ZDRURISExuTkRkeC9hY3NUMEhxUDF6RWhRaFF4d0pCc2lxQkJJVUlVTWNDUWJJcWdRU0ZDRkRIQWtHeUtvRUVoUWhRL3d0NWdURm9UdEhNQ2Nvd3Y4Z3p1MG9RRElTa0JCQUlLQUZUZ0lTQWdnRXRNQkpRRUlBZ1lBVy9zU3dGc0N4MVFLM294YSttY1ZJbU9IWXlvS0VGcjZaeFVpWTRkaktnb1FXdnBuRlNKamgyTXFDaEJhK21jVkltT0hZeW9LRUZyNlp4VWlZNGRqS2dvUVd2cG5GbjBlVFA4dGMwOTkvQUFBQUFFbEZUa1N1UW1DQw'
          senderid:
            allOf:
              - $ref: '../schemas/fields.yaml#/dnaid'
            description: sender should be a dna userid or a dna server instance id.
          event:
            type: string
            example: pingLocation
            description: a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
          timestamp:
            $ref: '../schemas/fields.yaml#/timestamp'
          teleMeta:
            $ref: '../schemas/schemas.yaml#/telemetryMetaData'
    application/octet-stream:
      schema:
        type: string
        format: binary
serverSendInviteRequestBody:
  description: Request Body for Trusted API invite users to group.  All users must have same OST and isFirstParty values.
  required: true
  content:
    application/json:
      schema:
        type: object
        required:
          - members
          - inviterid
          - onlineServiceType
        properties:
          members:
            type: array
            items:
              $ref: '../schemas/schemas.yaml#/inviteGroupMember'
          inviterid:
            $ref: '../schemas/fields.yaml#/dnaid'
          onlineServiceType:
            $ref: '../schemas/fields.yaml#/onlineServiceType'
          returnMembershipErrors:
            $ref: '../schemas/fields.yaml#/serverReturnMembershipErrors'
          ttl:
            allOf:
              - $ref: '../schemas/fields.yaml#/ttl'
            default: 3600
            minimum: 1
            maximum: 2628288
          teleMeta:
            $ref: '../schemas/schemas.yaml#/telemetryMetaData'
      examples:
        'Invite a user':
          value:
            inviterid: '575190c5933c4fa6a8086477e4d33e23'
            onlineServiceType: 3
            members:
              - memberid: 'abcdef1234567890abcdef1234567890'
        'Invite a user via first party platform id':
          value:
            inviterid: '575190c5933c4fa6a8086477e4d33e23'
            onlineServiceType: 3
            members:
              - memberid: '12345684343'
                isFirstPartyId: true
        'Invite a user with non-default expiration':
          value:
            inviterid: '575190c5933c4fa6a8086477e4d33e23'
            onlineServiceType: 3
            members:
              - memberid: 'abcdef1234567890abcdef1234567890'
            ttl: 300
        'Invite multiple 2k users one of which is on a different platform':
          value:
            inviterid: '575190c5933c4fa6a8086477e4d33e23'
            onlineServiceType: 3
            members:
              - memberid: 'abcdef1234567890abcdef1234567890'
              - memberid: '1234567890abcdef1234567890abcdef'
serverJoinRequestRequestBody:
  description: membership request body
  required: true
  content:
    application/json:
      schema:
        type: object
        required:
          - members
        properties:
          members:
            type: array
            items:
              $ref: '../schemas/schemas.yaml#/joinGroupMember'
          password:
            $ref: '../schemas/fields.yaml#/password'
          returnMembershipErrors:
            $ref: '../schemas/fields.yaml#/serverReturnMembershipErrors'
          teleMeta:
            $ref: '../schemas/schemas.yaml#/telemetryMetaData'
      examples:
        'Join a user to group':
          value:
            members:
              - memberid: 'abcdef1234567890abcdef1234567890'
                canCrossPlay: true
                onlineServiceType: 3
        'Join 2 users to group':
          value:
            members:
              - memberid: 'abcdef1234567890abcdef1234567890'
                canCrossPlay: true
                onlineServiceType: 3
              - memberid: 1234567890abcdef1234567890abcdef
                canCrossPlay: true
                onlineServiceType: 1
        'Join a group with a password':
          value:
            password: somepass
            members:
              - memberid: 'abcdef1234567890abcdef1234567890'
                canCrossPlay: true
                onlineServiceType: 2
serverRefreshTokenRequestBody:
  description: Refresh token request body
  required: true
  content:
    application/json:
      schema:
        type: object
        required:
          - refreshToken
          - instanceId
        properties:
          refreshToken:
            description: Refresh token
            type: string
            example: eyJhbGci...xUpOD5qlY\
          instanceId:
            description: The instance id used to authenticate
            type: string
serverUpsertDiscoveryRequestBody:
  description: Discovery entries
  required: true
  content:
    application/json:
      schema:
        type: array
        items:
          type: object
          required:
            - id
            - description
            - urls
          properties:
            id:
              type: string
              example: precert
              description: must be unique.  can be any string identifier. env/guid/etc.
            description:
              type: string
              example: Production Environment
            urls:
              type: array
              items:
                $ref: '#/serverDiscoveryURLRequest'
            canList:
              type: boolean
              example: true
              description: if true, this discovery will be returned in the discovery list response
serverDiscoveryURLRequest:
  description: url for trusted api discovery request schema
  type: object
  required:
    - type
    - url
  properties:
    type:
      type: string
      enum: [ 'http', 'mqtt', 'trusted' ]
      example: http
    url:
      type: string
      description: string url with port included with domain if needed
      example: wss://social-service-staging-additionalname.d2dragon.net/mqtt
    scheme:
      type: string
      description: optional piece of uri
      example: wss
    host:
      type: string
      description: optional piece of uri
      example: social-service-staging-additionalname.d2dragon.net
    port:
      type: string
      description: optional piece of uri
      example: '443'
    path:
      type: string
      description: optional piece of uri
      example: '/mqtt'
    query:
      type: string
      description: optional piece of uri
      example: '?isQuery=true'
    fragment:
      type: string
      description: optional piece of uri
      example: '#'