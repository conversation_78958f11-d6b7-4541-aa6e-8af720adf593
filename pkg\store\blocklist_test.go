package store

import (
	"context"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

var user1 = "b287e655461f4b3085c8f244e394ff70"
var user2 = "e12c3df480984141b2f385646b2024fa"
var user3 = "2017e9305ccc4e5781d076403c1b6725"
var user4 = "d7056450997c488da69d997d2a8e9446"

var allUsersBlocklist = []*apipub.BlocklistResponse{
	{
		Userid:    user1,
		Blockedid: user2,
	},
	{
		Userid:    user1,
		Blockedid: user3,
	},
	{
		Userid:    user1,
		Blockedid: user4,
	},
}

func cleanupBlocklist(db *DataStore) {
	db.ModifyBlocklist(ctx, user1, nil, &allUsersBlocklist)
}

func TestBlockListMethods(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("BlockListMethods", func() {
		blocklist := apipub.BlocklistResponse{
			Userid:    "user1",
			Blockedid: "user2",
		}

		g.It("should get proper PK & SK", func() {
			expectPK := "test#user#user1"
			g.Assert(blocklist.PK("test")).Equal(expectPK)
			expectSK := "test#blocklist#user2"
			g.Assert(blocklist.SK("test")).Equal(expectSK)
		})
	})
}

func TestGetBlocklist(t *testing.T) {
	g := goblin.Goblin(t)
	ddb := NewDynamoDB(context.TODO(), cfg)
	userId := "11"
	productid := "productid"
	ost := apipub.OnlineServiceType(24)
	blockedUser1 := apipub.BlocklistResponse{
		Blockedid: "1",
		Name:      aws.String("1"),
		Userid:    userId,
	}
	blockedUser2 := apipub.BlocklistResponse{
		Blockedid: "2",
		Name:      aws.String("2"),
		Userid:    userId,
	}
	blockedUser3 := apipub.BlocklistResponse{
		Blockedid: "3",
		Name:      aws.String("3"),
		Userid:    userId,
	}
	g.Describe("GetBlockList", func() {
		createRequest := &dynamodb.CreateTableInput{
			TableName: &cfg.ProfileTable,
			AttributeDefinitions: []types.AttributeDefinition{
				{
					AttributeName: aws.String("pk"),
					AttributeType: "S",
				},
				{
					AttributeName: aws.String("sk"),
					AttributeType: "S",
				},
			},
			KeySchema: []types.KeySchemaElement{
				{
					AttributeName: aws.String("pk"),
					KeyType:       "HASH",
				},
				{
					AttributeName: aws.String("sk"),
					KeyType:       "RANGE",
				},
			},
			ProvisionedThroughput: &types.ProvisionedThroughput{
				ReadCapacityUnits:  aws.Int64(10),
				WriteCapacityUnits: aws.Int64(10),
			},
		}
		deleteRequest := &dynamodb.DeleteTableInput{
			TableName: &cfg.ProfileTable,
		}
		g.Before(func() {
			(*ddb).DeleteTable(ctx, deleteRequest)
			(*ddb).CreateTable(ctx, createRequest)
			ds.PutItemInProfileTable(ctx, &blockedUser1)
			ds.PutItemInProfileTable(ctx, &blockedUser2)
			ds.PutItemInProfileTable(ctx, &blockedUser3)

			time.Sleep(time.Duration(time.Second) * 2)
		})
		g.After(func() {
			(*ddb).DeleteTable(ctx, deleteRequest)
		})
		g.It("GetBlockList - get a non-existing blocked user", func() {
			result, _, err := ds.GetBlocklistWithLimit(ctx, "22", productid, ost, 100, nil)
			g.Assert(err).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetBlockList - get a full block list", func() {
			result, _, err := ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 100, nil)

			g.Assert(err).IsNil()
			g.Assert(len(*result)).Equal(3)
		})
		g.It("GetBlockList - get two blocked users with limit of 2", func() {
			result, next, _ := ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 2, nil)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(2)

			blocked1 := (*result)[0]
			blocked2 := (*result)[1]
			g.Assert(blocked1.Blockedid).Equal("1")
			g.Assert(blocked2.Blockedid).Equal("2")
		})
		g.It("GetBlockList - get all blocked users with limit of 1", func() {
			result, next, _ := ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 1, nil)
			g.Assert(*next).Equal("1")
			g.Assert(len(*result)).Equal(1)

			blocked1 := (*result)[0]
			g.Assert(blocked1.Blockedid).Equal("1")

			result, next, _ = ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 1, next)
			g.Assert(*next).Equal("2")
			g.Assert(len(*result)).Equal(1)

			blocked1 = (*result)[0]
			g.Assert(blocked1.Blockedid).Equal("2")

			result, next, _ = ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 1, next)
			g.Assert(*next).Equal("3")
			g.Assert(len(*result)).Equal(1)

			blocked1 = (*result)[0]
			g.Assert(blocked1.Blockedid).Equal("3")

			result, next, _ = ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 1, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetBlockList - get the second blocked user in the first call", func() {
			result, next, _ := ds.GetBlocklistWithLimit(ctx, userId, productid, ost, 1, aws.String("1"))
			g.Assert(*next).Equal("2")
			g.Assert(len(*result)).Equal(1)

			blocked1 := (*result)[0]
			g.Assert(blocked1.Blockedid).Equal("2")
		})
	})
}

func TestBlockListFriendShipRemoval(t *testing.T) {
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()
	g := goblin.Goblin(t)
	defer cleanupBlocklist(mock.db)

	g.Describe("BlockListFriendShipRemoval", func() {
		g.It("should remove friend", func() {
			g.Timeout(mock.testTimeout)
			userPresence := apipub.PresenceResponse{
				Userid:    user1,
				Productid: utils.GenerateNewULID(),
			}
			friendPresence := apipub.PresenceResponse{
				Userid:    user2,
				Productid: utils.GenerateNewULID(),
			}
			messenger.Subscribe(ctx, cfg, user1, friendPresence.Topic("dna"))
			messenger.Subscribe(ctx, cfg, user2, userPresence.Topic("dna"))

			getItem := dynamodb.GetItemOutput{
				Item: map[string]types.AttributeValue{
					"userid":   &types.AttributeValueMemberS{Value: user1},
					"friendid": &types.AttributeValueMemberS{Value: user2},
					"version":  &types.AttributeValueMemberN{Value: "0"},
					"status":   &types.AttributeValueMemberS{Value: "friend"},
					"sk":       &types.AttributeValueMemberS{Value: "friend#" + user2},
					"pk":       &types.AttributeValueMemberS{Value: "user#" + user1},
				},
			}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().BatchWriteItem(gomock.Any(), gomock.Any()).Return(nil, nil).Times(2)
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(&getItem, nil).Times(2)

			friend, err := mock.db.GetFriend(context.Background(), user1, user2)
			g.Assert(err).IsNil()
			g.Assert(friend).IsNotNil()
			g.Assert(friend.Friendid).Equal(user2)
			g.Assert(friend.Userid).Equal(user1)
			g.Assert(friend.Status).Equal(apipub.FriendStatus("friend"))

			list1 := []*apipub.BlocklistResponse{
				{
					Userid:    user1,
					Blockedid: user2,
				},
			}

			// block friend relationshop
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Times(3)
			mock.ddb.EXPECT().BatchWriteItem(gomock.Any(), gomock.Any()).Return(nil, nil).Times(1)
			blErr := mock.db.ModifyBlocklist(ctx, friend.Userid, &list1, nil)
			g.Assert(blErr).IsNil()
		})
	})
}
