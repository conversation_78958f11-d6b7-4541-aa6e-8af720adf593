package apipub

import (
	"testing"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

type SubStruct struct {
	StringField string
}

type TestStruct struct {
	IntField    int
	StringField string
	StringPtr   *string
	StructField SubStruct
	StructPtr   *SubStruct
}

func TestMembershipRequest(t *testing.T) {
	g := goblin.Goblin(t)
	g.Describe("MembershipRequest", func() {
		g.It("should return expected values", func() {
			groupid := utils.GenerateNewULID()
			memberid := utils.GenerateRandomDNAID()
			approverid := utils.GenerateRandomDNAID()
			mr := &MembershipRequest{
				Groupid:    groupid,
				Memberid:   memberid,
				Approverid: approverid,
				Status:     Invited,
			}

			mr.Status = Approved
		})
	})
}
