# Social Scripts

Add migration or utility scripts here for dynamodb management or other back office services.

## truncate.js

Delete all items in a table

```
node truncate.js --table-name table-to-truncate --region us-east-1
```

### Options

```
Options:
  --version     show version number                                    [boolean]
  --table-name  table name                                   [string] [required]
  --region      AWS region                       [string] [default: "us-east-1"]
  --force       force production truncate             [boolean] [default: false]
  --help        show help                                              [boolean]
```

## backfill-social-table.js
Reformat sort keys to all friend items and backfill all items in another table
```
node backfill-social-table.js --source-table-name t2gp-social-develop-social
                              --destination-table-name t2gp-social-develop-social-v1 --region us-east-1
```
### Options
```
Options:
  --source-table-name      source table name                  [string] [required]
  --destination-table-name destination table name             [string] [required]
  --region                 AWS region             [string] [default: "us-east-1"]
  --force                  force production truncate   [boolean] [default: false]
  --help                   show help                                    [boolean]
```