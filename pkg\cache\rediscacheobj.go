package cache

import (
	"context"
	"encoding/json"
	"errors"
	"net/http"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"

	"github.com/redis/go-redis/v9"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

// RedisCacheObj Type constraints for redis cache using generics
type RedisCacheObj interface {
	apipub.GroupResponse | apipub.GroupMemberResponse | apipub.UserProfileResponse | apipub.RecentlyPlayedUserResponse | apipub.PresenceResponse | apipub.MembershipRequest | apipub.FriendResponse | apipub.BlocklistResponse | apipub.UserCacheMeta | apitrusted.TsClientIdInfo | apipub.JoinRequestAction | apipub.EndorsementResponse | map[string]interface{} | string | int | bool | int64
}

// getCachedObject get item from redis cache.
func getCachedObject[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, key string) (*cachedObj, error) {
	var item cachedObj
	// var items interface{}
	log := logger.FromContext(ctx)

	if key == "" {
		log.Error().Msg("key is empty")
		return nil, errs.New(http.StatusInternalServerError, errs.ERedisInvalidKey)
	}

	result, err := rc.jSONGet(ctx, key, ".").Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		log.Error().Err(err).Str("key", key).Msg("failed to get cached object")
		return nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed)
	}
	if result == "" {
		return nil, nil
	}

	err = json.Unmarshal([]byte(result), &item)
	if err != nil {
		log.Error().Err(err).Str("key", key).Msg("failed to unmarshal json object")
		return nil, errs.New(http.StatusInternalServerError, errs.ERedisUnmarshalFailed)
	}
	return &item, nil

}

// getCachedObjects get items from redis cache.  No Next provided because this function should be called after using the Next to get keys.
func getCachedObjects[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, keys *[]string) (*[]*cachedObj, error) {

	// var items interface{}
	log := logger.FromContext(ctx)

	if keys == nil || len(*keys) == 0 {
		return nil, errs.New(http.StatusInternalServerError, errs.ERedisInvalidKey)
	}

	var bSameHash = true

	//if we have > 1 key and we are on a cluster, check to see if they hash to the same slot
	if len(*keys) > 1 {
		var hashTag, tempHashTag string
		for i, key := range *keys {
			tempHashTag = GetHashTag(key)

			if tempHashTag == "" {
				tempHashTag = key
			}

			if hashTag == "" {
				hashTag = tempHashTag
			}

			//log.Info().Strs("keys", *keys).Msgf("key[%v] hash = %v slot = %v", i, tempHashTag, GetHashSlot(tempHashTag))

			if tempHashTag != hashTag {
				log.Info().Strs("keys", *keys).Msgf("key[%v] different hashtag %v <> %v", i, tempHashTag, hashTag)
				bSameHash = false
				//break on first different tag
				break
			}
		}
	}

	var objs []interface{}
	var err2 error
	err2 = nil
	if bSameHash {
		results, err := rc.jSONMGet(ctx, ".", *keys...).Result()
		if err != nil {
			log.Error().Err(err).Strs("keys", *keys).Msgf("failed to get cached objects")
			return nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed)
		}
		if results == nil || errors.Is(err, redis.Nil) {
			return nil, nil
		}
		objs = results
	} else {
		//get each individually
		for i := 0; i < len(*keys); i++ {
			results, err := rc.jSONMGet(ctx, ".", (*keys)[i]).Result()
			if err != nil {
				log.Error().Err(err).Strs("keys", *keys).Int("errindex", i).Msgf("failed to get cached objects")
				return nil, errs.New(http.StatusServiceUnavailable, errs.ERedisCacheGetFailed)
			}
			if results == nil || errors.Is(err, redis.Nil) {
				//return nil, nil
				continue
			}
			if len(results) >= 1 {
				if results[0] == nil {
					log.Warn().Str("missing", (*keys)[i]).Strs("keys", *keys).Int("index", i).Msgf("redis object missing for this key")
					err2 = errs.New(http.StatusInternalServerError, errs.ERedisObjectMissing)
				}
				if results[0] != nil {
					objs = append(objs, results[0])
				}
			}
		}
	}

	var items []*cachedObj

	for _, obj := range objs {
		if obj == nil {
			continue
		}
		var item cachedObj
		err := json.Unmarshal([]byte(obj.(string)), &item)
		if err != nil {
			log.Error().Err(err).Msg("failed to unmarshal json object")
			return nil, errs.New(http.StatusInternalServerError, errs.ERedisUnmarshalFailed)
		}
		items = append(items, &item)
	}

	return &items, err2
}

// setCachedObject update cached object to its root JSON Path
func setCachedObject[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, item *cachedObj, key string, ttl time.Duration) error {
	return setCachedObjectAtPath(ctx, rc, item, key, ".", ttl)
}

// setCachedObjectAtPath update cached object property at its JSON Path
func setCachedObjectAtPath[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, item *cachedObj, key, path string, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	if item == nil {
		log.Error().Str("key", key).Msg("item is nil trying to set key")
	}
	rc.pipelined(ctx, func(p redis.Pipeliner) error {
		_, err := p.JSONSet(ctx, key, path, item).Result()
		if err != nil {
			log.Error().Err(err).Str("key", key).Str("path", path).Msg("failed to set cached object")
			return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
		}
		if ttl > -1 {
			err = p.Expire(ctx, key, ttl).Err()
			if err != nil {
				log.Error().Err(err).Str("key", key).Msg("failed to set expire for key")
			}
		}
		return nil
	})
	return nil
}

// setCachedObjectAtPaths updates cached object property at given JSON paths
//func setCachedObjectAtPaths(ctx context.Context, rc *RedisCache, items map[string]interface{}, key string, ttl time.Duration) error {
//	log := logger.FromContext(ctx)
//	if len(items) == 0 {
//		log.Error().Str("key", key).Msg("no items provided to set")
//		return errs.New(http.StatusUnprocessableEntity, errs.ERedisNilCache)
//	}
//
//	// Use MULTI to start a transaction
//	err := rc.watch(ctx, func(tx *redis.Tx) error {
//		// Begin a transaction with MULTI
//		_, err := tx.Pipelined(ctx, func(p redis.Pipeliner) error {
//			for path, item := range items {
//				if item == nil {
//					log.Error().Str("key", key).Str("path", path).Msg("item is nil trying to set path")
//					continue
//				}
//
//				_, err := p.JSONSet(ctx, key, path, item).Result()
//				if err != nil {
//					log.Error().Err(err).Str("key", key).Str("path", path).Msg("failed to set cached object at path")
//				}
//			}
//
//			if ttl > -1 {
//				err := p.Expire(ctx, key, ttl).Err()
//				if err != nil {
//					log.Error().Err(err).Str("key", key).Msg("failed to set expire for key")
//				}
//			}
//			return nil
//		})
//		return err
//	}, key)
//
//	if err != nil {
//		log.Error().Err(err).Str("key", key).Msg("transaction execution failed")
//		return err
//	}
//
//	return nil
//}

// setCachedObjectArrayAtPath update cached object array at its JSON Path
func setCachedObjectArrayAtPath[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, item *[]cachedObj, key, path string, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	if item == nil {
		log.Error().Str("key", key).Msg("item is nil trying to set key")
	}
	rc.pipelined(ctx, func(p redis.Pipeliner) error {
		_, err := p.JSONSet(ctx, key, path, item).Result()
		if err != nil {
			log.Error().Err(err).Str("key", key).Str("path", path).Msg("failed to set cached object")
			return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
		}
		if ttl > -1 {
			err = p.Expire(ctx, key, ttl).Err()
			if err != nil {
				log.Error().Err(err).Str("key", key).Msg("failed to set expire for key")
			}
		}
		return nil
	})
	return nil
}

// setCachedObject update a list of objects to their root JSON Path
func setCachedObjects[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, items *[]*cachedObj, keys []string, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	var err error
	if items == nil {
		return errors.New("items are nil trying to set keys")
	}
	if len(*items) != len(keys) {
		err = errors.New("items and keys are different lengths")
	}
	if keys == nil {
		err = errors.New("keys are nil trying to set keys")
	}
	if err != nil {
		log.Error().Err(err).Strs("keys", keys).Msg("failed to set cached objects")
		return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
	}

	pipeline := rc.pipeline()
	pipe := &pipeline

	for i, item := range *items {
		if item != nil {
			jsonItem, err := json.Marshal(item)
			if err != nil {
				log.Error().Err(err).Str("key", keys[i]).Interface("item", item).Msg("failed to marshal item.")
				//don't return so we can save others...
				//return err
			}

			err = (*pipe).Do(ctx, "JSON.SET", keys[i], ".", jsonItem).Err()
			if err != nil {
				log.Error().Err(err).Str("key", keys[i]).Interface("item", item).Msg("failed to JSON.set key for item.")
				//return err
			}
			if ttl > -1 {
				err = (*pipe).Expire(ctx, keys[i], ttl).Err()
				if err != nil {
					log.Error().Err(err).Str("key", keys[i]).Interface("item", item).Msg("failed to set expire for item.")
					//return err
				}
			}

		}
	}

	res, err2 := pipeline.Exec(ctx)
	if err2 != nil {
		log.Error().Err(err2).Msgf("error executing pipeline %v", res)
		return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
	}

	return nil
}

// DeleteCachedObj delete cached object
func (rc *RedisCache) DeleteCachedObj(ctx context.Context, key string) error {
	err := rc.del(ctx, key).Err()
	if err != nil {
		log := logger.FromContext(ctx)
		log.Error().Err(err).Str("key", key).Msg("failed to delete cached object")
		return errs.New(http.StatusInternalServerError, errs.ERedisCacheDeleteFailed)
	}
	return nil
}

// deleteArrayMemberAtPath delete cached object at JSON path
//func (rc *RedisCache) deleteArrayMemberAtPath(ctx context.Context, key string, path string) error {
//	err := rc.jSONDel(ctx, key, path).Err()
//	if err != nil {
//		log := logger.FromContext(ctx)
//		log.Error().Err(err).Str("key", key).Msg("failed to delete cached object")
//		return errs.New(http.StatusInternalServerError, errs.ERedisCacheDeleteFailed)
//	}
//	return nil
//}

func (rc *RedisCache) CachedObjExists(ctx context.Context, key string) bool {
	return rc.exists(ctx, key).Val() == 1
}
