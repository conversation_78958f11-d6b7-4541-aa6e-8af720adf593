import { getContext, setContext } from 'svelte';
import type { Writable } from 'svelte/store';
import { CONTEXT_KEY_STEAM_IMPORT } from '../constant';

export interface SteamImportContext {
  linkedSteamId: Writable<string>;
}

export const setSteamImportContext: (context: SteamImportContext) => void = (
  context: SteamImportContext
) => {
  return setContext<SteamImportContext>(CONTEXT_KEY_STEAM_IMPORT, context);
};

export const getSteamImportContext = () => {
  return getContext<SteamImportContext>(CONTEXT_KEY_STEAM_IMPORT);
};
