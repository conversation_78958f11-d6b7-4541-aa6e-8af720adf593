<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import {
    EVENT_FRIENDS_SEARCH_REQUEST,
    EVENT_FRIENDS_SEARCH_RESULT,
  } from '../../constant';
  import { useTranslator, useTransportService } from '../../hooks';
  import { useSearchTerm } from '../../hooks/useSearchTerm';
  import type { SocialFriend } from '../../services';
  import type { CustomEventMap } from '../CustomInput';
  import { DirectImportCard } from '../FriendImportCard';
  import { SearchBar } from '../SearchBar';

  const t = useTranslator();
  const transportService = useTransportService();
  const searchTerm = useSearchTerm();
  const friends = writable<SocialFriend[]>([]);

  const onSearchTextChange = ({
    detail,
  }: CustomEvent<CustomEventMap['inputChange']>) => {
    const displayName = detail.text;
    if (displayName) {
      transportService.publishEvent(EVENT_FRIENDS_SEARCH_REQUEST, displayName);
      searchTerm.set(displayName);
    }
  };

  onMount(() => {
    transportService.subscribeEvent(
      EVENT_FRIENDS_SEARCH_RESULT,
      (_, data: SocialFriend[]) => {
        friends.set(data);
      }
    );

    if ($searchTerm) {
      transportService.publishEvent(EVENT_FRIENDS_SEARCH_REQUEST, $searchTerm);
    }
  });

  onDestroy(() => {
    transportService.unsubscribe(EVENT_FRIENDS_SEARCH_RESULT);
  });

  $: friendsCount = $friends.length;
</script>

<style>
  .container {
    width: 100%;
    height: 100%;
    padding: 0 0 0 2rem;
    display: flex;
    flex-direction: column;
  }

  .content {
    margin-top: 1.5rem;
  }

  .content .list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  h2 {
    margin: 0;
    margin-bottom: 1rem;
    color: var(--social-color, var(--default-color));
    font-weight: bold;
    font-size: 1rem;
    line-height: 125%;
    text-transform: capitalize;
  }

  h4 {
    font-weight: normal;
    font-size: 0.875rem;
    line-height: 150%;
    color: var(--social-color, var(--default-color));
    opacity: 0.6;
    font-style: italic;
    margin-bottom: 0;
  }
</style>

<div class="container">
  <h2>{$t('search 2K friend')}</h2>
  <SearchBar
    placeholder="{$t('search for 2k display name')}"
    value="{$searchTerm}"
    on:inputChange="{onSearchTextChange}"
  />
  <div class="content">
    {#if friendsCount > 0}
      <h4>{friendsCount} {$t('player(s) found')}</h4>
      <div class="list">
        {#each $friends as friend}
          <DirectImportCard
            avatar="{friend.avatar}"
            displayName="{friend.name}"
            initials="{friend.name && friend.name[0]}"
            userId="{friend.userid}"
          />
        {/each}
      </div>
    {/if}
  </div>
</div>
