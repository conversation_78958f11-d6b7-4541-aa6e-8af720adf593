import { getContext, setContext } from 'svelte';
import type { Writable } from 'svelte/store';
import { CONTEXT_KEY_ACCORDION } from '../constant';

export interface AccordionContext {
  onHeaderClicked: (key: string) => any;
  selected: Writable<string[]>;
}

export const setAccordionContext: (context: AccordionContext) => void = (
  context: AccordionContext
) => {
  return setContext<AccordionContext>(CONTEXT_KEY_ACCORDION, context);
};

export const getAccordionContext = () => {
  return getContext<AccordionContext>(CONTEXT_KEY_ACCORDION);
};
