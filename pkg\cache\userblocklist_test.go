package cache

import (
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func Test_Blocklist(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	var productid, userid, blockuser string
	var blocklists *[]*apipub.BlocklistResponse
	var err error

	g.Describe("set get delete blocklist", func() {

		g.Before(func() {
			productid = utils.GenerateRandomDNAID()
			userid = utils.GenerateRandomDNAID()
			blockuser = utils.GenerateRandomDNAID()

			blocklists = &[]*apipub.BlocklistResponse{
				{
					Userid:    userid,
					Productid: productid,
					Blockedid: blockuser,
				},
			}

			err = rc.AddToUserBlockList(ctx, blocklists, time.Duration(60)*time.Second)
			g.Assert(err).IsNil()

		})

		g.It("get blocklist ", func() {
			limit := int64(10)
			next := ""
			blocklists, next, err = rc.GetUserBlocklist(ctx, userid, &limit, &next)
			g.Assert(err).IsNil()
			g.Assert(blocklists).IsNotNil()
			g.Assert(len(*blocklists)).Equal(1)
		})

		g.After(func() {
			err = rc.RemoveFromUserBlockList(ctx, blocklists)
			g.Assert(err).IsNil()
		})
	})
}
