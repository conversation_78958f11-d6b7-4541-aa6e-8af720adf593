name: Deploy integration

on:
  repository_dispatch:
    types: ['Deploy to integration']
  workflow_dispatch:
    inputs:
      version:
        required: true
        description: 'Release image tag (https://go.aws/37MtoXh) with githash ex: 1.0.0-0a1b2c3d'
      skip_plugin_swap:
        type: boolean
        default: false
        description: 'Check this to skip plugin swap on this release'

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  run-helm:
    name: 'Run Helm'
    runs-on: [t2gp-arc-linux]
    environment: integration
    env:
      AWS_DEFAULT_REGION: us-east-1
      TARGET_ENV: integration
      CLUSTER: t2gp-non-production
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: true
          persist-credentials: false
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v1-node16
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables (Manual)
        if: github.event.inputs.version != ''
        run: |
          ver=${{github.event.inputs.version}}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
          echo "VERSION=$(echo '${{ github.event.inputs.version }}' | sed 's/-.*//')" >> $GITHUB_ENV
      - name: Helm Deploy (integration)
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.6
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          environment: ${{ env.TARGET_ENV }},portal-staging
          helm-values: 'social-api.groupsApi.image.tag=${{ env.IMAGE_TAG }},social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Get plugin version on AWS Parameter Store
        run: |
          set -ex
          CURRENTLY_STORED=$(aws ssm get-parameter --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --output text --query 'Parameter.Value')
          if [ "$CURRENTLY_STORED" == "${{ env.SUBMODULE_HASH }}" ]; then
            echo "UPDATE_PLUGIN=false" >> $GITHUB_ENV
          else
            echo "UPDATE_PLUGIN=true" >> $GITHUB_ENV
          fi
      - name: Install kubectl
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        uses: azure/setup-kubectl@v1
      - name: Swap VernemQ Plugin
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        working-directory: deployments
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Update plugin version to AWS Parameter Store
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        run: |
          aws ssm put-parameter --overwrite --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --value ${{ env.SUBMODULE_HASH }}
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "integration", "version": "${{env.IMAGE_TAG}}", "api_url":"https://social-service-integration.d2dragon.net/v1", "api_private_url":"https://social-service-integration-private.d2dragon.net", "mqtt_url":"wss://social-service-integration.d2dragon.net/mqtt" }'
      - name: Report to Slack Success
        if: success() && github.event.inputs.version != ''
        uses: tokorom/action-slack-incoming-webhook@main
        env:
          INCOMING_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_RELEASE }}
        with:
          attachments: |
            [
              {
                "color": "#2c6e49",
                "blocks": [
                  {
                    "type": "header",
                    "text": {
                      "type": "plain_text",
                      "text": "🎉Social API Release - ${{ env.VERSION }}🎉",
                      "emoji": true
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Environment*\n<https://social-service-${{ env.TARGET_ENV }}-private.d2dragon.net/|${{ env.TARGET_ENV }}>"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Source Code*\n<https://github.com/${{ github.repository }}/releases/tag/${{ env.VERSION }}|Release Notes>\n<https://github.com/${{ github.repository }}/tree/${{ env.VERSION }}|Browse>"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Github Action*\n<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|${{ github.run_id }}>"
                    }
                  }
                ]
              }
            ]
      - name: Publish docs to ${{ env.TARGET_ENV }}
        run: |
          ver=${{ github.event.inputs.version }}
          aws s3 cp --recursive s3://t2gp-docs/social-service/release-${ver:0:-9}/ s3://t2gp-docs/social-service/${{ env.TARGET_ENV }}/
          aws s3 cp --recursive s3://t2gp-docs/openapi/social-service/release-${ver:0:-9}/ s3://t2gp-docs/openapi/social-service/${{ env.TARGET_ENV }}/
      - name: Output Info
        id: output_info
        run: |
          echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: integration
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      skip_notification: false
      api_test_note: 'deploy integration'
    secrets: inherit
