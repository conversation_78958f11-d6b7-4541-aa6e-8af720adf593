openapi: 3.0.3
info:
  title: T2GP Social Service
  version: REPLACE_ME
servers:
  - url: https://social-service-staging.d2dragon.net/v2
  - url: https://social-service-cert.d2dragon.net/v2
  - url: https://social-service-integration.d2dragon.net/v2
  - url: https://social-service-develop.dev.d2dragon.net/v2
  - url: https://social-service-release-1-6-3.d2dragon.net/v2
  - url: /v2
security:
  - bearerAuth: []
tags:
  - name: Authentication
    description: DNA full account authentication - Not available in production environment. Recommended that game teams use DNA identity systems directly.
  - name: Profile
    description: Fetch user profile
  - name: Search
    description: Search 2K users by display name
  - name: Friends
    description: Invite, search, import friends
  - name: Groups
    description: Create and manage groups of users
  - name: Invite
    description: Manage group invites
  - name: JoinRequest
    description: Request to Join a group
  - name: Presence
    description: Manage user presence
  - name: Chat
    description: Chat with friends
  - name: Abuse
    description: Manage user block list & report users
  - name: Status
    description: Retrieve server stauts information
  - name: Discovery
    description: Get endpoints that game teams can override
paths:
  /auth/login:
    summary: Login user to DNA with social platform using email and password
    post:
      security: []
      tags:
        - Authentication
      summary: Login to DNA account
      description: T2GP API for Authentication to DNA.  Recommended that game teams use DCL rather than our API to obtain token.  We do some automatic presence mamagement for our web app and it uses unknown DNA OST etc.  This is disabled on production.  It's meant to just facilitate getting a token during early testing.
      operationId: login
      requestBody:
        $ref: '#/components/requestBodies/loginRequestBody'
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/loginResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /auth/logout:
    summary: Logout user
    post:
      tags:
        - Authentication
      summary: Logout user
      description: Logging out the user will invalidate the JWT.  Subsequent refreshes of the `refreshToken` will fail. The JWT will still be valid until the expiration time. Recommended that game teams use DCL rather than our API to logout.
      operationId: logout
      requestBody:
        $ref: '#/components/requestBodies/logoutRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '429':
          $ref: '#/components/responses/429'
  /auth/refresh:
    summary: Refresh JWT token
    post:
      security: []
      tags:
        - Authentication
      summary: Refresh token
      description: Refresh the JWT token. You will need to send the `refreshToken` from the login response. After logout, the `refreshToken` will not longer work. Recommended that game teams use DCL rather than our API to refresh token.
      operationId: refreshToken
      requestBody:
        $ref: '#/components/requestBodies/refreshRequestBody'
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/loginResponse'
        '400':
          $ref: '#/components/responses/400'
        '429':
          $ref: '#/components/responses/429'
  /user/profile:
    get:
      summary: Get current logged in user profile
      operationId: getUserProfile
      tags:
        - Profile
      responses:
        '200':
          description: A object representing the logged in user profile
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/userProfileResponse'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /user/profile/sync:
    post:
      summary: Force a sync profile w/ DNA values. The SSO sync service makes this API unnecessary unless access to profile data is needed *immediately* after an profile update.  SSO sync usually happens within seconds. We recommend NOT using this endpoint and letting it sync on its own as it can be slow to return and our back end sync is real time.
      operationId: syncUserProfile
      tags:
        - Profile
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /user/played:
    get:
      summary: Get list of recently played with users.
      operationId: getRecentlyPlayed
      tags:
        - Profile
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/next'
      responses:
        '200':
          description: A paged array of recently played users. There is a maximum of 100 players.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/playedPlayersNext'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    post:
      summary: Update recently played list of users
      operationId: updateRecentlyPlayed
      requestBody:
        $ref: '#/components/requestBodies/setPlayedRequestBody'
      tags:
        - Profile
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Profile
      summary: clear user's entire recently played list
      description: Mainly useful for automated tests.
      operationId: deleteRecentlyPlayed
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /user/presence:
    get:
      summary: Get all user presence objects for productid in the user's JWT. Parameters are deprecated.
      operationId: getUserPresence
      tags:
        - Presence
      responses:
        '200':
          description: A paged array of presence objects.  In most cases, this will be a list of 1 record.  But support exists for multiple presence records.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/presenceNext'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
    post:
      summary: Set presence for user
      operationId: setUserPresence
      requestBody:
        $ref: '#/components/requestBodies/setPresenceRequestBody'
      tags:
        - Presence
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      summary: Clear user presence for the productid provided in the user's JWT
      operationId: clearPresence
      tags:
        - Presence
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
  /user/presence/active:
    post:
      summary: Set the active group for the user
      operationId: setActiveGroup
      tags:
        - Presence
      requestBody:
        $ref: '#/components/requestBodies/setActiveGroupRequestBody'
      responses:
        '200':
          description: a presence record
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/presenceResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      summary: Clear the active group for the user
      description: Clear the active group for the user and productid provided in the user's JWT.
      operationId: clearActiveGroup
      tags:
        - Presence
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /user/presence/heartbeat:
    post:
      summary: Heartbeat presence for user
      operationId: presenceHeartBeat
      tags:
        - Presence
      responses:
        '200':
          description: a presence record.  if presence for user is not found, it will still return 200, but with a presence status of `offline`.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/presenceResponse'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /user/blocklist:
    get:
      tags:
        - Abuse
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/next'
      summary: Get user's block list.
      operationId: getBlocklist
      responses:
        '200':
          description: A paged array of blocked user's info.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/blocklistsNext'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    post:
      tags:
        - Abuse
      summary: Batch import users to blocklist.  Must submit no more than 20 userids per request.
      operationId: importBlocklist
      requestBody:
        $ref: '#/components/requestBodies/importBlocklistRequestBody'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/importBlocklistResponse'
              examples:
                Add users to the blocklist:
                  value:
                    blockedids:
                      - b287e655461f4b3085c8f244e394ff7e
                      - effe28b27efc6594e43bfc0879b40085
                    onlineServiceType: 24
                Add first party users to the blocklist:
                  value:
                    blockedids:
                      - '76543210123456789'
                    onlineServiceType: 3
                Remove users from the blocklist:
                  value:
                    blockedids: null
                    onlineServiceType: 24
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Abuse
      summary: clear user's entire block list.
      description: Mainly useful for automated tests. Returns the blocklist after clear.
      operationId: delBlocklist
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /user/blocklist/{pUserid}:
    post:
      tags:
        - Abuse
      summary: Add a user to blocklist
      parameters:
        - $ref: '#/components/parameters/pUserid'
      operationId: addBlocklist
      requestBody:
        $ref: '#/components/requestBodies/addBlocklistRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Abuse
      summary: remove user from blocklist
      parameters:
        - $ref: '#/components/parameters/pUserid'
      operationId: removeBlocklist
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /search/2KUsers:
    get:
      summary: This search API is to search for full 2K accounts by full account name.
      description: This search API is to search for full 2K accounts by full account name.
      operationId: search2KUsers
      tags:
        - Search
      parameters:
        - $ref: '#/components/parameters/displayName'
      responses:
        '200':
          description: An array of users
          content:
            application/json:
              schema:
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/search2kUserResponse'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /friends:
    get:
      summary: List friends for user
      operationId: listFriends
      tags:
        - Friends
      parameters:
        - $ref: '#/components/parameters/status'
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/next'
      responses:
        '200':
          description: A paged array of friends
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/friendsNext'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /friends/{pFriendid}:
    get:
      summary: Get a single friend by friendid
      parameters:
        - $ref: '#/components/parameters/pFriendid'
      operationId: getFriend
      tags:
        - Friends
      responses:
        '200':
          description: The friend requested
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/friendResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    post:
      summary: Invite a user to become friends or accept invite
      description: When a user invites a friend, an invitation message will be set out through MQTT. The receiving user will call this same API to establish a friendship if the users accepts.
      operationId: makeFriend
      parameters:
        - $ref: '#/components/parameters/pFriendid'
      requestBody:
        $ref: '#/components/requestBodies/setFriendsRequestBody'
      tags:
        - Friends
      responses:
        '200':
          description: Success
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/setFriendResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    patch:
      summary: Update friend `viewed` flag
      operationId: updateFriendStatus
      tags:
        - Friends
      parameters:
        - $ref: '#/components/parameters/pFriendid'
      requestBody:
        $ref: '#/components/requestBodies/setFriendViewedRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      summary: Delete a friend, reject invite, or revoke invite
      description: |
        Depending on the state of the friendship, this will do the following based on the status:
          * friend: breaks the friendship relationship
          * pending from invitor: revokes invitation
          * pending from invitee: rejects invitation
      parameters:
        - $ref: '#/components/parameters/pFriendid'
      operationId: deleteFriend
      tags:
        - Friends
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /friends/accounts/search:
    get:
      summary: Find common platforms users that have DNA accounts
      tags:
        - Friends
      operationId: importPlatformFriends
      parameters:
        - name: id
          in: query
          description: Comma separated platform id to be queried (limit 100)
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/accountsNext'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /user/{pUserid}/report:
    post:
      summary: Report a user for abuse
      operationId: reportUserAbuse
      tags:
        - Abuse
      parameters:
        - $ref: '#/components/parameters/pUserid'
      requestBody:
        $ref: '#/components/requestBodies/setReportRequestBody'
      responses:
        '200':
          description: Expected response to a valid request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/abuseReturn'
              examples:
                SNS message id:
                  value:
                    messageId: 4e8f419e-4ac3-5222-b71d-1da980edf33a
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups:
    get:
      summary: Get groups that the user belongs to
      operationId: getGroups
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/next'
      tags:
        - Groups
      responses:
        '200':
          description: A paginated array of groups
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/groupsNext'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    post:
      summary: Create a Group
      operationId: createGroup
      tags:
        - Groups
      requestBody:
        $ref: '#/components/requestBodies/createGroupRequestBody'
      responses:
        '201':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/groupResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups/{pGroupid}:
    delete:
      tags:
        - Groups
      summary: Disband/Delete a Group
      operationId: deleteGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    get:
      tags:
        - Groups
      summary: Get a Group
      operationId: getGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/groupResponse'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    patch:
      tags:
        - Groups
      summary: 'Update a group''s properties.  Valid properties are: canMembersInvite, joinRequestAction, maxMembers, meta, password. It is recommended that you only submit fields that should be modified.'
      operationId: updateGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      requestBody:
        $ref: '#/components/requestBodies/updateGroupRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups/{pGroupid}/members:
    get:
      tags:
        - Groups
      summary: Get a detailed list of member information for a specific group
      operationId: getGroupMembers
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          description: List of detailed members
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/membersNext'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups/{pGroupid}/members/{pMemberid}:
    get:
      tags:
        - Groups
      summary: Get member in group
      operationId: getGroupMember
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      responses:
        '200':
          description: Group member information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/groupMemberResponse'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    patch:
      tags:
        - Groups
      summary: Update member of group
      operationId: updateGroupMember
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      requestBody:
        $ref: '#/components/requestBodies/updateGroupMemberRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Groups
      summary: Kick a member from group
      operationId: kickMemberFromGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
        - name: reason
          in: query
          description: reason for removing a group member
          required: false
          schema:
            type: string
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups/{pGroupid}/members/me:
    delete:
      tags:
        - Groups
      summary: Leave from group
      operationId: leaveGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups/{pGroupid}/members/{pMemberid}/meta:
    put:
      tags:
        - Groups
      summary: Update the metadata of a specified group member. The API accepts an update request only if the submitted time is later than the member's existing metadata. Values in the request body will be added to the member's metadata if their keys do not already exist. Otherwise, the values will overwrite the existing ones.
      operationId: updateGroupMemberMeta
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      requestBody:
        $ref: '#/components/requestBodies/updateGroupMemberMetaRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '409':
          $ref: '#/components/responses/409'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /groups/{pGroupid}/control:
    post:
      tags:
        - Groups
      summary: Send a control message (json or binary)
      operationId: sendControlMessage
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      requestBody:
        $ref: '#/components/requestBodies/sendControlMessageRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /memberships/invites/me:
    get:
      tags:
        - Invite
      summary: Get user's invitations
      operationId: getInvites
      parameters:
        - $ref: '#/components/parameters/limit'
        - $ref: '#/components/parameters/next'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/invitesNext'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /memberships/invites/groups/{pGroupid}/members/{pMemberid}:
    post:
      summary: Invite a user to the group
      description: Creates and send an invite to a user for the group.
      tags:
        - Invite
      operationId: sendInvite
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      requestBody:
        $ref: '#/components/requestBodies/sendInviteRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      summary: Revoke an invite
      description: |
        Revoke all group invites from the sender to the memberid.
      tags:
        - Invite
      operationId: revokeInvite
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /memberships/invites/groups/{pGroupid}/approvers/{pApproverid}:
    patch:
      tags:
        - Invite
      summary: Accept invite to a group.
      description: Accepts an invite to a user for the group.
      operationId: acceptInvite
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pApproverid'
      requestBody:
        $ref: '#/components/requestBodies/acceptInviteRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Invite
      summary: Decline user's invite to group from inviter
      operationId: declineInvites
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pApproverid'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
  /memberships/requests/groups/{pGroupid}/approvers/{pApproverid}:
    post:
      summary: Request to join group
      description: |
        Creates and sends a join request for a group.
      tags:
        - JoinRequest
      operationId: sendJoinRequest
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pApproverid'
      requestBody:
        $ref: '#/components/requestBodies/requestJoinRequestBody'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/joinRequestResponse'
              examples:
                Empty Return request already exists:
                  value: {}
                Empty Return user blocked by group leader:
                  value: {}
                Membership Return:
                  value:
                    memberid: b287e655461f4b3085c8f244e394ff7e
                    approverid: b287e655461f4b3085c8f244e394ff7e
                    groupid: 01EYRSXN4DCFF1AV128Y5A211J
                    status: requested
                    version: 0
                    displayName: string
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /memberships/requests/groups/{pGroupid}/members/{pMemberid}:
    patch:
      summary: Approve a group join request
      description: |
        Approve a group join request for the memberid.
      tags:
        - JoinRequest
      operationId: approveJoinRequest
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      requestBody:
        $ref: '#/components/requestBodies/approveJoinRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      summary: Reject a group join request
      description: |
        Reject a group join request for the memberid.
      tags:
        - JoinRequest
      operationId: rejectJoinRequest
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /chatRoom/{pRoomid}/message:
    post:
      tags:
        - Chat
      summary: Send a message to a chat room
      operationId: sendChatMessage
      parameters:
        - $ref: '#/components/parameters/pRoomid'
      requestBody:
        $ref: '#/components/requestBodies/sendChatMessageRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /endorsements/users/me:
    get:
      tags:
        - Endorsements
      summary: get endorsements for self
      operationId: getEndorsementsForSelf
      responses:
        '200':
          description: an array of endorsements
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/endorsementListResponse'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /endorsements/users/{pUserid}:
    get:
      tags:
        - Endorsements
      summary: get endorsement for other user
      parameters:
        - $ref: '#/components/parameters/pUserid'
      operationId: getEndorsementsForUser
      responses:
        '200':
          description: an array of endorsements
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/endorsementListResponse'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    post:
      tags:
        - Endorsements
      summary: increment endorsement for user
      parameters:
        - $ref: '#/components/parameters/pUserid'
      operationId: incrementEndorsement
      requestBody:
        $ref: '#/components/requestBodies/incrementEndorsementRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /platform/{pOnlineServiceType}/user/{pFirstPartyid}/auth:
    put:
      tags:
        - SessionSynchronize
      summary: send platform auth code to server for user to use in Session Syncing
      parameters:
        - $ref: '#/components/parameters/pOnlineServiceType'
        - $ref: '#/components/parameters/pFirstPartyid'
      operationId: upsertSessionAuth
      requestBody:
        $ref: '#/components/requestBodies/upsertSessionAuthRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /platform/{pOnlineServiceType}/groups/{pGroupid}/sync:
    get:
      tags:
        - SessionSynchronize
      summary: request a sync from 1P session data to T2GP group
      operationId: syncSessionToGroup
      parameters:
        - $ref: '#/components/parameters/pOnlineServiceType'
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /platform/{pOnlineServiceType}/memberships/invites/session/{pSessionid}/approvers/{pFirstPartyApproverid}:
    patch:
      tags:
        - SessionSynchronize
      summary: Accept invite to a first party session.
      description: Accepts an invite to a user for the group.
      operationId: acceptInviteByFirstPartySessionIds
      parameters:
        - $ref: '#/components/parameters/pOnlineServiceType'
        - $ref: '#/components/parameters/pSessionid'
        - $ref: '#/components/parameters/pFirstPartyApproverid'
      requestBody:
        $ref: '#/components/requestBodies/acceptInviteBySessionRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /version:
    get:
      security: []
      tags:
        - Status
      summary: Get the server version
      operationId: getVersion
      responses:
        '200':
          description: Version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/versionResponse'
        '429':
          $ref: '#/components/responses/429'
  /health:
    get:
      tags:
        - Status
      security: []
      summary: Get the server health status
      operationId: GetHealth
      parameters:
        - $ref: '#/components/parameters/healthid'
      responses:
        '200':
          description: Server is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/healthResponse'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          description: Server is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/healthResponse'
  /discovery:
    get:
      tags:
        - Discovery
      security: []
      summary: Find endpoints to use.  Nil productid or id will return default discovery. Empty '?id=' parameter will return all for given productid with canList=true.
      operationId: getDiscovery
      parameters:
        - $ref: '#/components/parameters/discoveryPid'
        - $ref: '#/components/parameters/discoveryid'
      responses:
        '200':
          description: Discovery response
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/discoveryResponse'
        '400':
          $ref: '#/components/responses/400'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
components:
  schemas:
    email:
      type: string
      description: email of user.  since this is PII, it is rarely returned.  do not depend on this field's value.
      example: <EMAIL>
    password:
      type: string
      description: password for the join request
      format: password
    locale:
      type: string
      description: the locale of the user.
      example: en-US
      x-oapi-codegen-extra-tags:
        dynamodbav: locale
    dnaid:
      type: string
      description: an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
      example: effe28b27efc6594e43bfc0879b40085
      pattern: ^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$
    loginResponse:
      description: Successful authentication response
      type: object
      required:
        - accessToken
        - accessTokenExpiresIn
        - refreshToken
        - refreshTokenExpiresIn
        - accountId
        - accountType
      properties:
        accessToken:
          description: The access token
          type: string
          format: Id
          example: eyJhbGci...0jOtn_Bo
        accessTokenExpiresIn:
          description: The number of seconds until the access token expires
          type: integer
          format: uint32
          example: 3600
        refreshToken:
          description: The refresh token
          type: string
          format: Id
          example: eyJhbGci...xUpOD5qlY
        refreshTokenExpiresIn:
          description: The number of seconds until the refresh token expires
          type: integer
          format: uint32
          example: 7200
        accountId:
          $ref: '#/components/schemas/dnaid'
        accountType:
          description: The type of the account that was authenticated
          type: integer
          format: uint32
          example: 3
    errorCode:
      type: integer
      description: error code.  list of errors on docsite.
      format: uint32
      example: 100120
    error:
      type: object
      required:
        - code
        - errorCode
        - message
      properties:
        code:
          description: HTTP error code
          type: integer
          format: uint32
          example: 500
        errorCode:
          $ref: '#/components/schemas/errorCode'
        message:
          type: string
          description: error message
          example: Exception occured
        stack:
          type: string
          description: Stack trace of the error (will be only returned in dev environment)
    emptyObject:
      type: object
      description: Empty object
      example: '{}'
    created:
      type: string
      description: timestamp that this record was created.
      format: date-time
      example: '2020-09-14T20:30:35.129Z'
    timestamp:
      type: string
      description: timstamp of the event
      format: date-time
      example: '2020-09-14T20:30:35.129Z'
    presenceStatus:
      type: string
      description: the status of the presence record for the user
      enum:
        - online
        - offline
        - playing
        - custom
        - away
        - dnd
        - chat
        - authenticating
      example: online
    customStatus:
      type: string
      example: I like waffles
    richPresence:
      type: string
      description: string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
    gameName:
      type: string
      description: pre-localized game name.
      example: Sample Game
    gameData:
      type: string
      description: free form field for games to send additional presence information for their internal use.
      maxLength: 1024
    ulid:
      type: string
      description: lexigraphicaly sorted unique identifier
      example: 01EYRSXN4DCFF1AV128Y5A211J
      pattern: ^[0123456789ABCDEFGHJKMNPQRSTVWXYZ]{26}$
    groupid:
      allOf:
        - $ref: '#/components/schemas/ulid'
      description: the id of the group.  validates ULID pattern.
    canCrossPlay:
      type: boolean
      description: Does the user sending this have cross play enabled on their local system
      example: false
    maxMembers:
      type: integer
      description: default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
      example: 6
      minimum: 2
      maximum: 100
    activeGroupResponse:
      type: object
      required:
        - groupid
        - maxMembers
        - currentMemberCount
        - canRequestJoin
        - canCrossPlay
      properties:
        groupid:
          $ref: '#/components/schemas/groupid'
        canRequestJoin:
          type: boolean
          description: in v2, this boolean is basically just a check on if the group is full.  it used to check group join request action but now any group can be joined using a password even as long as it's not full.
        canCrossPlay:
          $ref: '#/components/schemas/canCrossPlay'
        maxMembers:
          $ref: '#/components/schemas/maxMembers'
        currentMemberCount:
          type: integer
          description: count of current members of the group
    productid:
      allOf:
        - $ref: '#/components/schemas/dnaid'
      description: the id of the product. validates as a dnaid.
    clientid:
      type: string
      example: random-mqtt-client-id-string
      description: client id of mqtt connection for mqtt presence
    ttl:
      type: integer
      format: int64
      description: time in seconds until the object expires
      example: 3600
    priority:
      type: integer
      description: Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
    onlineServiceType:
      type: integer
      description: basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 9
        - 10
        - 11
        - 12
        - 13
        - 14
        - 15
        - 16
        - 17
        - 18
        - 19
        - 20
        - 21
        - 22
        - 23
        - 24
        - 99
      x-enumNames:
        - UNKNOWN
        - XBOX LIVE
        - SONY ENTERTAINMENT NETWORK
        - STEAM
        - WEB
        - LEGACY GAME CENTER
        - GOOGLE PLAY
        - WINDOWS PHONE
        - CALICO
        - NINTENDO
        - GAME CENTER
        - WEGAME
        - VORTEX
        - EPIC
        - STADIA
        - FACEBOOK
        - GOOGLE
        - TWITTER
        - TWITCH
        - DEVICE
        - APPLE
        - ZENDESK
        - T2GP
        - WINDOWS DEVELOPER
      example: 3
    meta:
      type: object
      description: free form map (json format) to store metadata for this object.
      nullable: true
      example:
        key1: value1
        key2: value2
    joinContext:
      type: object
      description: Context used to join a game session
      required:
        - sessionid
        - launchGameArgs
      properties:
        sessionid:
          type: string
        launchGameArgs:
          type: string
    presenceResponse:
      type: object
      description: a presence record
      x-oapi-codegen-extra-tags:
        dynamodbav: presence
      required:
        - userid
        - status
        - timestamp
        - productid
        - gameName
        - priority
        - customStatus
        - onlineServiceType
        - activeSessionid
        - richPresence
        - gameData
        - activeGroup
        - clientid
        - ttl
        - platformid
        - joinContext
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        status:
          $ref: '#/components/schemas/presenceStatus'
        customStatus:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/customStatus'
        richPresence:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/richPresence'
        gameName:
          $ref: '#/components/schemas/gameName'
        gameData:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/gameData'
        activeGroup:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/activeGroupResponse'
        productid:
          $ref: '#/components/schemas/productid'
        clientid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/clientid'
        ttl:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ttl'
          description: How long in seconds before this presence will be considered offline if no presence Heartbeat is made.   | This is an optional value for those who are not using our MQTT.  People using our MQTT will have this functionality via our mqtt plugin.  Timeout set to 5 minutes for auto drop from group.
          minimum: 35
          maximum: 1800
        priority:
          $ref: '#/components/schemas/priority'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        meta:
          $ref: '#/components/schemas/meta'
        timestamp:
          $ref: '#/components/schemas/timestamp'
        joinContext:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/joinContext'
        platformid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaid'
          description: DNA only. at the time the presence is set, if the token has a pai claim.  this will be the sub claim, which should be the platformid.  we do not guarantee this value will be returned since full account tokens will not have it.
        activeSessionid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          description: id of active login session.
    accountTypeDNA:
      type: integer
      description: The type of Account according to DNA.  Notable is 3 for full.
      format: uint32
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
      x-enumNames:
        - UNKNOWN
        - ANONYMOUS
        - PLATFORM
        - FULL
        - TOOLS
        - NEWSLETTER
        - UNDISCLOSED
        - PRIVACY POLICY ACCEPTED ONLY
      example: 2
    firstPartyid:
      type: string
      description: The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
      example: *****************
    accountLinkDNA:
      type: object
      description: First party DNA account links.  This will be filtered by the current user's OST.
      properties:
        linkType:
          type: string
          enum:
            - xbl
            - steam
            - psn
            - epic
            - nintendo
            - parent
        accountId:
          $ref: '#/components/schemas/dnaid'
        accountType:
          $ref: '#/components/schemas/accountTypeDNA'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        firstPartyid:
          $ref: '#/components/schemas/firstPartyid'
    links:
      description: Linked accounts. Filtered to current OST.
      type: array
      items:
        $ref: '#/components/schemas/accountLinkDNA'
    dnaDisplayName:
      type: string
      description: 2k display name with optional 5 digit discrimnating hash
      pattern: ^[0-9a-zA-Z]{3,16}($|\#\d{5}$)
      example: discopotato#12345
    dob:
      type: string
      description: The date of birth for the specified user. mm/dd/yyyy format. since this is PII, it is never returned.  do not depend on this field's value.
      pattern: ^([0-2][0-9]|(3)[0-1])(\/)(((0)[0-9])|((1)[0-2]))(\/)\d{4}$
      example: 02/30/1999
    userProfileResponse:
      type: object
      description: a user's profile record.
      required:
        - userid
        - ageGroup
        - locale
        - created
        - lastLogin
        - presence
        - links
        - parentAccountId
        - displayName
        - onlineServiceType
        - dob
        - email
      properties:
        userid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: userid
        ageGroup:
          nullable: true
          type: integer
          description: |-
            This is currently, only available for DNA accounts. The age group of the specified Platform Account.
            List of types for reference:
            For United States: * 0: Unknown * 1: 0 ↔ 12 (Child) * 2: 13 ↔ 17 (Teen) * 3: 18 ↔ 24 (Adult) * 4: 25 ↔ 34 (Adult) * 5: 35 ↔ N (Adult) * 6: 13 ↔ N (Adult)
            For Europe: * 7: 0 ↔ 16 (Teen) * 8: 16 ↔ 24 (Adult) * 9: 24 ↔ N (Adult)
          example: 3
          x-oapi-codegen-extra-tags:
            dynamodbav: ageGroup
        locale:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/locale'
          x-oapi-codegen-extra-tags:
            dynamodbav: locale
        created:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/created'
          x-oapi-codegen-extra-tags:
            dynamodbav: created
        lastLogin:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/timestamp'
          x-oapi-codegen-extra-tags:
            dynamodbav: lastLogin
          description: Placeholder. Not currently used.
        presence:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/presenceResponse'
          x-oapi-codegen-extra-tags:
            dynamodbav: presence
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
          x-oapi-codegen-extra-tags:
            dynamodbav: links
        parentAccountId:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: parentAccountId
        displayName:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaDisplayName'
          x-oapi-codegen-extra-tags:
            dynamodbav: displayName
        onlineServiceType:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
          x-oapi-codegen-extra-tags:
            dynamodbav: onlineServiceType
        dob:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dob'
          x-oapi-codegen-extra-tags:
            dynamodbav: dob
        email:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/email'
          x-oapi-codegen-extra-tags:
            dynamodbav: email
    name:
      type: string
      description: The name associated with this user. If friend name is empty string, it is possible that it is not a full 2K account.
      example: discopotato
    simplePresenceResponse:
      type: object
      description: very basic presence information for possibly showing some limited presence information in certain lists like search where full presence details would definitely not be shared. game teams can use their discretion on whether they would like to display any of this information in their UI in places that it's offered.  currently only user search results and recently played.
      required:
        - status
        - userid
        - gameName
        - onlineServiceType
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        status:
          $ref: '#/components/schemas/presenceStatus'
        gameName:
          type: string
          example: Sample Game
          description: Possibly pull this from somewhere for localization in the future. Empty string is possible.
          nullable: true
        onlineServiceType:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
    recentlyPlayedUserResponse:
      type: object
      description: a recently played user record
      required:
        - userid
        - productid
        - weight
        - context
        - lastPlayed
        - name
        - links
        - forUserid
        - simplePresence
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        productid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/productid'
        weight:
          description: Sorting is done by last played unix time stamp in milliseconds + weight descending.
          nullable: true
          type: integer
          example: 100
        context:
          nullable: true
          description: Used by the game to store any context for the recenly played user.
          type: object
        lastPlayed:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/timestamp'
        name:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/name'
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
        forUserid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaid'
        simplePresence:
          type: array
          nullable: true
          items:
            allOf:
              - $ref: '#/components/schemas/simplePresenceResponse'
    nextid:
      type: string
      description: next value to be used for requesting next page
      example: 2f141278ccbf465ca054ad3c997722a7
    playedPlayersNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/recentlyPlayedUserResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    recentlyPlayedUsers:
      type: object
      description: schema for sending recently played users in requests
      required:
        - userid
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        weight:
          description: Sorting is done by last played unix time stamp in milliseconds + weight descending.  Weight can be used to make sorting not strictly by datetime.
          type: integer
          example: 100
        context:
          description: Used by the game to store any context for the recenly played user.
          type: object
    telemetryMetaData:
      type: object
      description: Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
      nullable: true
      example:
        some_boolean_field: 'true'
        some_integer_field: '0'
        some_string_field: asdf1234
    setPlayedRequest:
      type: object
      description: request schema for set recetnly played users
      required:
        - users
      properties:
        ttl:
          allOf:
            - $ref: '#/components/schemas/ttl'
          default: 3600
          minimum: 1
          maximum: 2628288
        users:
          description: Array of recently played users
          type: array
          items:
            $ref: '#/components/schemas/recentlyPlayedUsers'
        teleMeta:
          $ref: '#/components/schemas/telemetryMetaData'
    presenceNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/presenceResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    blocklistResponse:
      type: object
      required:
        - userid
        - blockedid
        - onlineServiceType
        - productid
        - created
        - name
        - links
      properties:
        userid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: userid
        blockedid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: blockedid
        name:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/name'
          x-oapi-codegen-extra-tags:
            dynamodbav: name
        onlineServiceType:
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
          x-oapi-codegen-extra-tags:
            dynamodbav: onlineServiceType
        productid:
          allOf:
            - $ref: '#/components/schemas/productid'
          x-oapi-codegen-extra-tags:
            dynamodbav: productid
        created:
          allOf:
            - $ref: '#/components/schemas/created'
          x-oapi-codegen-extra-tags:
            dynamodbav: created
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
          x-oapi-codegen-extra-tags:
            dynamodbav: links
    blocklistsNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/blocklistResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    userid:
      type: string
      description: The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
      example: effe28b27efc6594e43bfc0879b40085
    importBlocklistResponse:
      type: object
      required:
        - blockedids
        - onlineServiceType
      properties:
        blockedids:
          type: array
          items:
            type: string
          example:
            - b287e655461f4b3085c8f244e394ff7e
            - effe28b27efc6594e43bfc0879b40085
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
    twoKSearch:
      type: object
      required:
        - userid
        - name
        - links
        - simplePresence
      properties:
        userid:
          type: string
          example: effe28b27efc6594e43bfc0879b40085
          pattern: ^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$
        name:
          type: string
          example: discopotato
          nullable: true
        links:
          description: Linked accounts. Filtered to current OST.
          type: array
          nullable: true
          items:
            $ref: '#/components/schemas/accountLinkDNA'
        simplePresence:
          nullable: true
          type: array
          items:
            $ref: '#/components/schemas/simplePresenceResponse'
    search2kUserResponse:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/twoKSearch'
    friendStatus:
      type: string
      enum:
        - pending
        - friend
      example: friend
    friendResponse:
      type: object
      required:
        - userid
        - friendid
        - invitee
        - status
        - version
        - inviterOST
        - message
        - name
        - viewed
        - presence
        - links
      properties:
        userid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: userid
        friendid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: friendid
        invitee:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          x-oapi-codegen-extra-tags:
            dynamodbav: invitee
        inviterOST:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
          x-oapi-codegen-extra-tags:
            dynamodbav: inviterOST
        message:
          nullable: true
          type: string
          description: message to include with friend request
          example: Want to be my friend?
          x-oapi-codegen-extra-tags:
            dynamodbav: message
        name:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaDisplayName'
          x-oapi-codegen-extra-tags:
            dynamodbav: name
        viewed:
          nullable: true
          type: boolean
          x-oapi-codegen-extra-tags:
            dynamodbav: viewed
        presence:
          nullable: true
          type: array
          x-oapi-codegen-extra-tags:
            dynamodbav: presence
          items:
            $ref: '#/components/schemas/presenceResponse'
        status:
          allOf:
            - $ref: '#/components/schemas/friendStatus'
          x-oapi-codegen-extra-tags:
            dynamodbav: status
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
          x-oapi-codegen-extra-tags:
            dynamodbav: links
    friendsNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/friendResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    setFriendResponse:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/friendStatus'
    searchAccountResponse:
      type: object
      description: schema for DNA search results
      required:
        - id
        - type
        - firstPartyId
        - firstPartyAlias
        - parentAccountId
        - onlineServiceType
        - email
        - dob
        - displayName
        - links
        - locale
        - simplePresence
      properties:
        id:
          nullable: true
          description: The account Id
          type: string
          format: Id
        type:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/accountTypeDNA'
        firstPartyId:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/firstPartyid'
        firstPartyAlias:
          nullable: true
          description: The account first Party Alias
          type: string
          format: Id
        parentAccountId:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaid'
        onlineServiceType:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
        email:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/email'
        dob:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dob'
        displayName:
          nullable: true
          type: string
          description: The display name
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
        locale:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/locale'
    accountsNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/searchAccountResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    reportingCategory:
      type: string
      description: Reporting category
      example: Player_Boosting_Cheating
      enum:
        - Player_Boosting_Cheating
        - Hacks_Mods
        - Privacy_Doxing
        - Fraud
        - Spam
        - Realistic_Gore_or_Violence
        - Suicide_or_Self_Harm
        - Adult_Sexual_Harassment
        - Adult_Sexually_Explicit_Content
        - Minor_Abuse_or_Child_Sexual_Content
        - Bullying_Threats_Harassment
        - Hate_Speech_Discrimination
        - Terrorism_or_Violent_Extremism
        - Other_Illegal_Content
    abuseReturn:
      type: object
      required:
        - messageId
      description: Abuse report SNS message id
      properties:
        messageId:
          type: string
          example: 4e8f419e-4ac3-5222-b71d-1da980edf33a
    membershipStatus:
      type: string
      description: The membership status of the invite or request to join state. * requested - a request to join flow initiated * approved - group join request has been approved * rejected - group join request has been rejected * invited - the user has been invited to the group * accepted - the user has accepted the invite to the group * declined - the invite has been declined * revoked - the invite has been revoked
      enum:
        - requested
        - approved
        - rejected
        - invited
        - accepted
        - declined
        - revoked
      example: rejected
    isFirstPartyInvite:
      type: boolean
      description: a flag to indicate whether this invite should be processed as a first party invite
      example: false
    membershipRequest:
      type: object
      description: this schema defines a membership request.  which can be either a join request or an invite using the status field as a determiner.
      required:
        - groupid
        - memberid
        - approverid
        - status
      properties:
        ttl:
          allOf:
            - $ref: '#/components/schemas/ttl'
          default: 3600
          minimum: 1
          maximum: 2628288
        memberid:
          $ref: '#/components/schemas/dnaid'
        approverid:
          $ref: '#/components/schemas/dnaid'
        groupid:
          $ref: '#/components/schemas/groupid'
        productid:
          $ref: '#/components/schemas/productid'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        status:
          $ref: '#/components/schemas/membershipStatus'
        canCrossPlay:
          $ref: '#/components/schemas/canCrossPlay'
        firstPartyid:
          $ref: '#/components/schemas/firstPartyid'
        isFirstPartyInvite:
          $ref: '#/components/schemas/isFirstPartyInvite'
        fromDisplayName:
          allOf:
            - $ref: '#/components/schemas/dnaDisplayName'
          description: the display name of the user that this request is from.  provided to display in the UI of the invite.
        teleMeta:
          $ref: '#/components/schemas/telemetryMetaData'
    groupMemberRole:
      type: string
      description: the role of a group member
      enum:
        - leader
        - member
        - nonmember
      example: leader
    groupMemberResponse:
      type: object
      required:
        - userid
        - productid
        - role
        - name
        - presence
        - links
        - meta
        - metaLastUpdated
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        name:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/name'
        productid:
          $ref: '#/components/schemas/productid'
        role:
          $ref: '#/components/schemas/groupMemberRole'
        presence:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/presenceResponse'
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
        meta:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/meta'
        metaLastUpdated:
          description: Represents the time when an update request is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
          nullable: true
          type: integer
          format: uint64
          example: 1737484653930
    joinRequestAction:
      type: string
      description: The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
      enum:
        - manual
        - auto-approve
        - auto-reject
      example: manual
    canMembersInvite:
      type: boolean
      description: Should all members be allowed to invite other users, not just the leader?
      example: true
    firstPartySessionid:
      type: string
      description: The First Party Session Id of the specified.
      example: *****************
    groupResponse:
      type: object
      required:
        - groupid
        - maxMembers
        - productid
        - joinRequestAction
        - created
        - membershipRequests
        - password
        - canMembersInvite
        - canCrossPlay
        - onlineServiceType
        - groupCompositionId
        - firstPartySessionid
      description: Productid only set as required due to codegen issues.
      properties:
        created:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/created'
        groupid:
          $ref: '#/components/schemas/groupid'
        productid:
          $ref: '#/components/schemas/productid'
        membershipRequests:
          type: array
          nullable: true
          items:
            allOf:
              - $ref: '#/components/schemas/membershipRequest'
        meta:
          $ref: '#/components/schemas/meta'
        members:
          type: array
          items:
            $ref: '#/components/schemas/groupMemberResponse'
        maxMembers:
          $ref: '#/components/schemas/maxMembers'
        joinRequestAction:
          $ref: '#/components/schemas/joinRequestAction'
        password:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/password'
        canMembersInvite:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/canMembersInvite'
        canCrossPlay:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/canCrossPlay'
        onlineServiceType:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
        groupCompositionId:
          type: integer
          format: int64
          description: incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
          nullable: true
        firstPartySessionid:
          $ref: '#/components/schemas/firstPartySessionid'
    groupsNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/groupResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    updateGroupRequest:
      type: object
      description: request schema for update group
      properties:
        maxMembers:
          $ref: '#/components/schemas/maxMembers'
        meta:
          $ref: '#/components/schemas/meta'
        joinRequestAction:
          $ref: '#/components/schemas/joinRequestAction'
        password:
          $ref: '#/components/schemas/password'
        canMembersInvite:
          $ref: '#/components/schemas/canMembersInvite'
        teleMeta:
          $ref: '#/components/schemas/telemetryMetaData'
    membersNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/groupMemberResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    updateGroupMemberRequest:
      type: object
      description: request schema for update group member
      required:
        - role
      properties:
        role:
          $ref: '#/components/schemas/groupMemberRole'
        teleMeta:
          $ref: '#/components/schemas/telemetryMetaData'
    getInviteResponse:
      type: object
      description: schema for response for a user's invites
      required:
        - memberid
        - approverid
        - groupid
        - onlineServiceType
        - displayName
        - firstPartyid
        - isFirstPartyInvite
      properties:
        memberid:
          $ref: '#/components/schemas/dnaid'
        approverid:
          $ref: '#/components/schemas/dnaid'
        groupid:
          $ref: '#/components/schemas/groupid'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        displayName:
          allOf:
            - $ref: '#/components/schemas/dnaDisplayName'
        firstPartyid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/firstPartyid'
        isFirstPartyInvite:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/isFirstPartyInvite'
        canCrossPlay:
          nullable: true
          type: boolean
    invitesNext:
      type: object
      required:
        - items
        - nextid
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/getInviteResponse'
        nextid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/nextid'
    approverid:
      type: string
      description: The approverid of the request.  "leader" can be used to use the group leader as default.
      example: effe28b27efc6594e43bfc0879b40085
      pattern: ^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$|leader
    joinRequestResponse:
      type: object
      description: schema for a join request response
      required:
        - approverid
        - memberid
        - groupid
        - displayName
      properties:
        approverid:
          $ref: '#/components/schemas/dnaid'
        memberid:
          $ref: '#/components/schemas/dnaid'
        groupid:
          $ref: '#/components/schemas/groupid'
        displayName:
          allOf:
            - $ref: '#/components/schemas/dnaDisplayName'
    messageBodySegement:
      type: object
      description: Message body segement
      required:
        - type
        - text
      properties:
        type:
          type: string
          enum:
            - text
            - hyperLink
            - deepLink
        text:
          description: The text to be displayed in the message.
          type: string
        objectId:
          description: The id of the object being linked to. It's required for deepLink type.
          type: string
        objectType:
          description: The type of the object being linked to. It's required for deepLink type.
          type: string
        objectDescription:
          description: The description of the object being linked to. It's required for deepLink type.
          type: string
    chatMessage:
      type: object
      description: Chat message objet
      required:
        - subjectId
        - targetId
        - postedTime
        - body
        - productId
        - type
      properties:
        subjectId:
          description: The user id of the user sending the message
          type: string
        targetId:
          description: The id of message receipent. For 1on1 this is the userid. For group this is the groupid.
          type: string
        postedTime:
          $ref: '#/components/schemas/timestamp'
        body:
          description: The body of the message supports rich text. The sender's client should tokenize the raw text into a messageBodySegment array. The recipient's client can then render a rich text message from the messageBodySegment array.
          type: array
          items:
            $ref: '#/components/schemas/messageBodySegement'
        productId:
          $ref: '#/components/schemas/productid'
        type:
          description: The type of chat message. 1on1 is a direct message between two users. Group is a message sent to a group.
          type: string
          enum:
            - group
            - 1on1
        shardId:
          description: The shard id of a chat room.
          type: string
    endorsementName:
      type: string
      description: the name of the endorsement to be acted upon
    isPositive:
      type: boolean
      default: true
      description: is this custom endorsement considered to be positive (or is it something like a downvote).  this is a permanent value for the endorsement and is not meant to reduce the increment.  the value of the first instance of this endorsement is preserved.  these "negative" values only increase and are meant to be used by game teams if they want to formulate a behavior score or something similar.
    isPrivate:
      type: boolean
      default: false
      description: do not allow this endorsement be queried by users other than self.  this can be used to only allow other users to query positive endorsements and not see "downvotes"  but still store them to use for behavior score computations.  this is a permanent value of the endorsement and cannot be changed after the initial value is set.
    currentEndorsementCount:
      type: integer
      description: how many endorsements have been received since the last time the endorsement was reset.
    totalEndorsementCount:
      type: integer
      description: how many endorsements have been received since the first time this endorsement was received.  does not get reset when the endorsement is reset.
    endorsementResponse:
      type: object
      required:
        - endorsementName
        - isPositive
        - currentEndorsementCount
        - isPrivate
        - totalEndorsementCount
      properties:
        endorsementName:
          allOf:
            - $ref: '#/components/schemas/endorsementName'
          x-oapi-codegen-extra-tags:
            dynamodbav: endorsementName
        isPositive:
          allOf:
            - $ref: '#/components/schemas/isPositive'
          x-oapi-codegen-extra-tags:
            dynamodbav: isPositive
        isPrivate:
          allOf:
            - $ref: '#/components/schemas/isPrivate'
          x-oapi-codegen-extra-tags:
            dynamodbav: isPrivate
        currentEndorsementCount:
          allOf:
            - $ref: '#/components/schemas/currentEndorsementCount'
          x-oapi-codegen-extra-tags:
            dynamodbav: currentEndorsementCount
        totalEndorsementCount:
          allOf:
            - $ref: '#/components/schemas/totalEndorsementCount'
          x-oapi-codegen-extra-tags:
            dynamodbav: totalEndorsementCount
    endorsementListResponse:
      type: object
      required:
        - items
      properties:
        items:
          type: array
          items:
            $ref: '#/components/schemas/endorsementResponse'
    incrementValue:
      type: integer
      minimum: 1
      description: how many endorsements
    authCode:
      type: string
      description: authCode to use for OAuth with First Party for session syncing
    versionResponse:
      type: object
      required:
        - version
        - gitHash
        - buildDate
      properties:
        version:
          type: string
          example: 1.0.2-6623bad
        gitHash:
          type: string
          example: 6623bad133a5bbf8d6123e445d1c8d9ceeb45548
        buildDate:
          type: string
          example: 2023-10-27-19-19-32
    healthResponse:
      type: object
      required:
        - name
        - version
        - overall-status
        - generated
        - services
      properties:
        name:
          type: string
          example: t2gp-social-service
          nullable: true
        version:
          type: string
          example: v0.1.0-deadbeef
          nullable: true
        overall-status:
          type: string
          example: OK
          nullable: true
        generated:
          type: string
          example: '2023-10-30T20:01:11.199405036Z'
          nullable: true
        services:
          type: object
          nullable: true
          example:
            social-service-dynamodb: FAIL
            social-service-id-dna: OK
            social-service-redis: OK
            social-service-s3: OK
    discoveryURLResponse:
      type: object
      required:
        - type
        - url
        - schema
        - host
        - port
        - path
        - query
        - fragment
      properties:
        type:
          type: string
          enum:
            - http
            - mqtt
            - trusted
          example: http
        url:
          type: string
          description: string url with port included with domain if needed
          example: wss://social-service-staging-additionalname.d2dragon.net/mqtt
        scheme:
          type: string
          description: optional piece of uri
          nullable: true
          example: wss
        host:
          type: string
          description: optional piece of uri
          nullable: true
          example: social-service-staging-additionalname.d2dragon.net
        port:
          type: string
          description: optional piece of uri
          nullable: true
          example: '443'
        path:
          type: string
          description: optional piece of uri
          nullable: true
          example: /mqtt
        query:
          type: string
          description: optional piece of uri
          nullable: true
          example: '?isQuery=true'
        fragment:
          type: string
          description: optional piece of uri
          nullable: true
          example: '#'
    discoveryResponse:
      type: object
      required:
        - id
        - description
        - urls
        - canList
      properties:
        id:
          type: string
          example: precert
          description: must be unique.  can be any string identifier. env/guid/etc.
        description:
          type: string
          example: Production Environment
        urls:
          type: array
          items:
            $ref: '#/components/schemas/discoveryURLResponse'
        canList:
          type: boolean
          example: true
          description: if true, this discovery will be returned in the discovery list response
          nullable: true
  requestBodies:
    loginRequestBody:
      description: Login request body
      required: true
      content:
        application/json:
          schema:
            required:
              - locale
              - email
              - password
            properties:
              email:
                $ref: '#/components/schemas/email'
              password:
                $ref: '#/components/schemas/password'
              locale:
                $ref: '#/components/schemas/locale'
              appId:
                $ref: '#/components/schemas/dnaid'
    logoutRequestBody:
      description: Logout token request body
      required: false
      content:
        application/json:
          schema:
            type: object
            properties:
              refreshToken:
                description: Refresh token
                type: string
                example: eyJhbGci...xUpOD5qlY\
    refreshRequestBody:
      description: Refresh token request body
      required: true
      content:
        application/json:
          schema:
            required:
              - locale
              - refreshToken
            properties:
              locale:
                $ref: '#/components/schemas/locale'
              refreshToken:
                description: Refresh token
                type: string
                example: eyJhbGci...xUpOD5qlY\
    setPlayedRequestBody:
      description: Update recently played list
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/setPlayedRequest'
    setPresenceRequestBody:
      description: Sets Users Presence
      required: true
      content:
        application/json:
          schema:
            required:
              - status
              - gameName
            properties:
              status:
                $ref: '#/components/schemas/presenceStatus'
              customStatus:
                $ref: '#/components/schemas/customStatus'
              richPresence:
                $ref: '#/components/schemas/richPresence'
              gameData:
                $ref: '#/components/schemas/gameData'
              gameName:
                $ref: '#/components/schemas/gameName'
              ttl:
                allOf:
                  - $ref: '#/components/schemas/ttl'
                minimum: 35
                maximum: 1800
                description: How long in seconds before this connection will be considered offline if no futher presence update is made.   | NOTE - this value MUST be at MINIMUM several seconds longer than the rate of your heartbeat timer, or your presence will always expire. | We recommend having your keep alive allow for at least 3 heartbeats to occur in the window (espepecially on mobile), | but it's entirely up to you how much of a grace period you want.  And for how a long droppped connection can be recovered. | The minimum is set to 35s because our plugin heartbeat is 30s.  We do NOT recommend using 35s.  Default is 5m.
              meta:
                allOf:
                  - $ref: '#/components/schemas/meta'
                description: Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
              joinContext:
                $ref: '#/components/schemas/joinContext'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    setActiveGroupRequestBody:
      description: Sets Active Group for a user.  This determines which group should be used for automated actions. Also subscribes to the group presence object
      required: true
      content:
        application/json:
          schema:
            required:
              - activeGroupid
            properties:
              activeGroupid:
                $ref: '#/components/schemas/groupid'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    importBlocklistRequestBody:
      description: Batch import blocklist request body. Must submit 20 or fewer records per request.
      required: true
      content:
        application/json:
          schema:
            required:
              - userids
            properties:
              isFirstParty:
                type: boolean
                example: false
                description: optional flag to add the platform blocklist users to the main blocklist.
              userids:
                type: array
                description: an array of user ids to add to blocklist.  set isFirstParty = true if adding first party ids.  must all be same ost as requestor.
                items:
                  allOf:
                    - $ref: '#/components/schemas/userid'
                example:
                  - b287e655461f4b3085c8f244e394ff7e
                  - effe28b27efc6594e43bfc0879b40085
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Add users to the blocklist:
              value:
                userids:
                  - b287e655461f4b3085c8f244e394ff7e
                  - effe28b27efc6594e43bfc0879b40085
            Add first party users to the blocklist:
              value:
                isFirstParty: true
                userids:
                  - '76543210123456789'
                  - '76543210234567890'
    addBlocklistRequestBody:
      description: Add user to blocklist
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    setFriendsRequestBody:
      description: Make friend request body
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                example: Hello there!
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    setFriendViewedRequestBody:
      description: Update friend status
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              viewed:
                type: boolean
                example: true
                default: true
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Mark pending friendship as viewed:
              value:
                viewed: true
    setReportRequestBody:
      required: true
      description: Required and optional fields for a report.
      content:
        application/json:
          schema:
            required:
              - reportingUserId
              - reportingCategory
              - reportingUserLocale
              - reportMessage
              - platform
              - subjectTitle
            properties:
              reportingUserId:
                type: string
                description: The reporting user's full account id.
              reportingUserLocale:
                type: string
                description: The reporting user's locale.
                example: zh-CN
              reportingCategory:
                $ref: '#/components/schemas/reportingCategory'
                example: Player_Boosting_Cheating
              reportMessage:
                type: string
                description: The user's specified report message. Limit the size to 5 KB.
                example: Player has said some inflammatory remarks.
              platform:
                type: string
                example: PlayStation 5
              subjectTitle:
                type: string
                example: Notice of Update on your Reported Content
                description: The subject title of a report.
              versionNumber:
                type: string
                description: The version number of a game.
              reportingContentType:
                type: string
                description: The type of reporting content.
                example: Abuse of Gameplay Mechanics
              os:
                type: string
                example: Windows 11
              gameSessionInfo:
                type: object
                description: The additional information for a report in a key-value pair format.
                example:
                  key1: onlineSessionId
                  key2: session id
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    createGroupRequestBody:
      required: true
      content:
        application/json:
          schema:
            required:
              - joinRequestAction
            properties:
              maxMembers:
                $ref: '#/components/schemas/maxMembers'
              joinRequestAction:
                $ref: '#/components/schemas/joinRequestAction'
              password:
                $ref: '#/components/schemas/password'
              canMembersInvite:
                $ref: '#/components/schemas/canMembersInvite'
              canCrossPlay:
                $ref: '#/components/schemas/canCrossPlay'
              meta:
                $ref: '#/components/schemas/meta'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Create public group that anyone can join:
              value:
                maxMembers: 5
                joinRequestAction: auto-approve
            Create manual group with a password:
              value:
                joinRequestAction: manual
                password: '********'
            Create invite only group:
              value:
                joinRequestAction: auto-reject
                canMembersInvite: true
            Create a group where inviter has to accept invite:
              value:
                joinRequestAction: manual
                canMembersInvite: true
            Create no crossplay group:
              value:
                joinRequestAction: manual
                canCrossPlay: false
            Create a group with meta data:
              value:
                joinRequestAction: manual
                meta:
                  key1: value1
                  key2: value2
    updateGroupRequestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateGroupRequest'
    updateGroupMemberRequestBody:
      description: Update group member role body
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/updateGroupMemberRequest'
    updateGroupMemberMetaRequestBody:
      description: Update group member's metadata.  Keep in mind.  All data from a metadata write is taken as a full record each time.  Any fields missing will be removed, any fields added or updated will be added or updated.  We do not keep any history of previous meta data records.  Data will always be clobbered by most recent data.
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - meta
              - timestamp
            properties:
              meta:
                type: object
                description: A group member's latest metadata.
                example:
                  key1: value1
                  key2: value2
              timestamp:
                description: Represents the client time when the meta update is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
                type: integer
                format: uint64
                example: 1737484653930
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    sendControlMessageRequestBody:
      description: Control message request body. Max size for binary is 5120 bytes
      required: true
      content:
        application/json:
          schema:
            required:
              - payload
            properties:
              payload:
                type: string
                example: ZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFHRUFBQUJ4Q0FZQUFBREYwTTA0QUFBQUFYTlNSMElBcnM0YzZRQUFBQVJuUVUxQkFBQ3hqd3Y4WVFVQUFBQUpjRWhaY3dBQURzUUFBQTdFQVpVckRoc0FBQUt0U1VSQlZIaGU3WnRiVGdOQkRBU3puQ3hIejgwQ1NIelRJejg2SGFoSWZIbkg5bFF4bTNnRDErMTJlMzc5OEhvaGdZOFgxcWIwRHdFa0JQd3FJQUVKQVFRQ1d1QWtJQ0dBUUVBTG5BUWtCQkFJYUlHVGdJUUFBZ0V0Y0JJQ0pGeU9aMGZQNTNzL25ycXViMHg3TDA3Q0h0dmp6RWc0UnJWM0lSTDIyQjVuUnNJeHFyMExrYkRIOWpnekVvNVI3VjJJaEQyMng1bmJjOExKRExEOU9mdDR0OFVMMVI2NysrTWtGTVZNTGtQQ0pNMWlMaVFVd1UwdVE4SWt6V0l1SkJUQlRTNUR3aVROWWk0a0ZNRk5Ma1BDSk0xaUxpUVV3VTB1UThJa3pXSXVKQlRCVFM1RHdpVE5ZaTRrRk1GTkxrUENKTTFpTGlRVXdVMHVpL2crWWZ0NWZUZC9kNzBTeGtsUWhBeHhKQmdncXhKSVVJUU1jU1FZSUtzU1NGQ0VESEVrR0NDckVraFFoQXp4OXB6d2VEeGttL2Y3WFY2VGZJSGFZM2QvbklRQSswaEFRZ0NCZ0JZNENVZ0lJQkRRQWljQkNRRUVBbHF3ekFrQisxeHRnVGxoRmE4bk9lOEpIczYvVmtFQ0VnSUlCTFRBU1VCQ0FJR0FGamdKQVJJaTVnVDFPVnY5M1kvaXFQN1BXSDFmb1BLci90VjZUb0lpWklnandRQlpsVUNDSW1TSUk4RUFXWlZBZ2lKa2lDUEJBRm1WUUlJaVpJaEh6QW1HZmE2V1lFNVl4ZXRKenUzSXc1bnZFd0k0SXdFSjZRUUMrdU05QVFrQkJBSmFhTThKSjN2b1BxOC9xYkY1VFhjT1VMMXhPMUtFREhFa0dDQ3JFa2hRaEF4eEpCZ2dxeEpJVUlRTWNTUVlJS3NTU0ZDRURISExuTkRkeC9hY3NUMEhxUDF6RWhRaFF4d0pCc2lxQkJJVUlVTWNDUWJJcWdRU0ZDRkRIQWtHeUtvRUVoUWhRL3d0NWdURm9UdEhNQ2Nvd3Y4Z3p1MG9RRElTa0JCQUlLQUZUZ0lTQWdnRXRNQkpRRUlBZ1lBVy9zU3dGc0N4MVFLM294YSttY1ZJbU9IWXlvS0VGcjZaeFVpWTRkaktnb1FXdnBuRlNKamgyTXFDaEJhK21jVkltT0hZeW9LRUZyNlp4VWlZNGRqS2dvUVd2cG5GbjBlVFA4dGMwOTkvQUFBQUFFbEZUa1N1UW1DQw
              senderid:
                allOf:
                  - $ref: '#/components/schemas/dnaid'
                description: sender should be a dna userid or a dna server instance id.
              event:
                type: string
                example: pingLocation
                description: a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
              timestamp:
                $ref: '#/components/schemas/timestamp'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
        application/octet-stream:
          schema:
            type: string
            format: binary
    sendInviteRequestBody:
      description: membership request body
      required: true
      content:
        application/json:
          schema:
            type: object
            description: schema for sending an invite
            properties:
              ttl:
                allOf:
                  - $ref: '#/components/schemas/ttl'
                default: 3600
                minimum: 1
                maximum: 2628288
              isFirstPartyInvite:
                $ref: '#/components/schemas/isFirstPartyInvite'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Invite a user via first party platform id:
              value:
                isFirstPartyInvite: true
            Invite a user with non-default expiration:
              value:
                ttl: 300
    acceptInviteRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: schema for accepting invites
            required:
              - canCrossPlay
            properties:
              canCrossPlay:
                $ref: '#/components/schemas/canCrossPlay'
              isFirstPartyInvite:
                $ref: '#/components/schemas/isFirstPartyInvite'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Accepting an invite:
              value:
                canCrossPlay: true
            Accept first party account invitation:
              value:
                isFirstPartyInvite: true
                canCrossPlay: true
            Accept with crossplay off:
              value:
                canCrossPlay: false
                isFirstPartyInvite: true
    requestJoinRequestBody:
      description: join request body
      required: true
      content:
        application/json:
          schema:
            type: object
            description: schema for sending join requests
            required:
              - canCrossPlay
            properties:
              canCrossPlay:
                $ref: '#/components/schemas/canCrossPlay'
              password:
                $ref: '#/components/schemas/password'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Request to join group with crossplay on:
              value:
                canCrossPlay: true
            Request to join group with crossplay off:
              value:
                canCrossPlay: false
            Request to join group with a password:
              value:
                canCrossPlay: true
                password: p@ssw0rd
    approveJoinRequestBody:
      description: join request body
      required: true
      content:
        application/json:
          schema:
            type: object
            description: schema for approving join requests
            properties:
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Approve join request:
              value: {}
    sendChatMessageRequestBody:
      description: chat message
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - message
            properties:
              message:
                $ref: '#/components/schemas/chatMessage'
    incrementEndorsementRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: schema for accepting invites by session information
            required:
              - incrementValue
            properties:
              endorsementName:
                allOf:
                  - $ref: '#/components/schemas/endorsementName'
                description: only include a name if you would like to increment a custom endorsement.  if no endorsement name is included, the count will be incremented to the default endorsement/commend bucket
              incrementValue:
                $ref: '#/components/schemas/incrementValue'
              isPositive:
                $ref: '#/components/schemas/isPositive'
              isPrivate:
                $ref: '#/components/schemas/isPrivate'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    upsertSessionAuthRequestBody:
      content:
        application/json:
          schema:
            type: object
            description: send auth code for user/ost combo for session syncing auth that requires user first party token
            required:
              - authCode
            properties:
              authCode:
                $ref: '#/components/schemas/authCode'
    acceptInviteBySessionRequestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            description: schema for accepting invites by session information
            properties:
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
  responses:
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 400
            message: Bad request
    '401':
      description: Unauthorized - The request did not include the required authorization information
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 401
            message: Invalid bearer token
    '403':
      description: Forbidden - The requestor is not authorized to make the request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 403
            message: Forbidden
    '404':
      description: Not Found - The requested entity could not be found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 404
            message: Not found
    '409':
      description: Conflict -
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 409
            message: The requested metadata conflicts the group member's existing metadata.
    '429':
      description: Too Many Requests - The client has sent too many requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 429
            message: Too Many Requests
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 500
            message: Internal server error
    200empty:
      description: Success-Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/emptyObject'
  parameters:
    limit:
      name: limit
      in: query
      description: How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
      schema:
        type: integer
        maximum: 100
        example: 10
    next:
      name: next
      in: query
      allowEmptyValue: true
      description: Next value used for pagination.  Empty value accepted for first page.
      schema:
        type: string
    pUserid:
      name: pUserid
      in: path
      required: true
      description: The id of the user
      schema:
        $ref: '#/components/schemas/userid'
    displayName:
      name: displayName
      in: query
      description: The full account DNA displayname to be searched for.
      required: true
      schema:
        $ref: '#/components/schemas/dnaDisplayName'
    status:
      name: status
      in: query
      allowEmptyValue: true
      schema:
        $ref: '#/components/schemas/friendStatus'
    pFriendid:
      name: pFriendid
      in: path
      required: true
      description: The id of the friend
      schema:
        $ref: '#/components/schemas/dnaid'
    pGroupid:
      name: pGroupid
      in: path
      required: true
      description: The id of the group
      schema:
        $ref: '#/components/schemas/groupid'
    pMemberid:
      name: pMemberid
      in: path
      required: true
      description: The id of the member
      schema:
        $ref: '#/components/schemas/userid'
    pApproverid:
      name: pApproverid
      in: path
      required: true
      description: The id of the approver. FOR JOIN REQUEST ONLY, you may use 'leader' without quotes for the group leader without knowing the id.  Invites require the explicit id as invites can be made by all party members at times.
      schema:
        $ref: '#/components/schemas/approverid'
    pRoomid:
      name: pRoomid
      in: path
      required: true
      description: The id of the chat room
      schema:
        $ref: '#/components/schemas/groupid'
    pOnlineServiceType:
      name: pOnlineServiceType
      in: path
      required: true
      description: The ost of the platform
      schema:
        $ref: '#/components/schemas/onlineServiceType'
    pFirstPartyid:
      name: pFirstPartyid
      in: path
      required: true
      description: The first party user id
      schema:
        $ref: '#/components/schemas/firstPartyid'
    pSessionid:
      name: pSessionid
      in: path
      required: true
      description: The first party session id
      schema:
        $ref: '#/components/schemas/firstPartySessionid'
    pFirstPartyApproverid:
      name: pFirstPartyApproverid
      in: path
      required: true
      description: The first party approver's user id
      schema:
        $ref: '#/components/schemas/firstPartyid'
    healthid:
      name: id
      in: query
      description: Get specific identity provider for health check based on id.
      allowEmptyValue: true
      schema:
        type: string
        example:
          - dna
          - pdi
          - rsg
        description: The id of the identity provider desired. No parameter will return health check without identity info.
    discoveryPid:
      name: discoveryPid
      in: query
      allowEmptyValue: true
      description: productid to filter by.
      schema:
        $ref: '#/components/schemas/dnaid'
    discoveryid:
      name: discoveryid
      in: query
      allowEmptyValue: true
      description: Get specific discovery endpoint based on id.  Must also send Authorization header
      schema:
        type: string
        example: precert
        description: The id of the discovery set desired.  '?id=' will return all sets for given product.
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
