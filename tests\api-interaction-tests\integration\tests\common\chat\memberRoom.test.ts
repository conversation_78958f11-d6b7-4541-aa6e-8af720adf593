import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let tokenHost: string;
let roomId: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
});

afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
});
describe('', () => {
  /**
   * Checking member room
   * - A Create room
   * - Get member length=1
   */
  it('member room', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.getRoomMembers(tokenHost, resp.body.groupid);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(1);
  });
});
