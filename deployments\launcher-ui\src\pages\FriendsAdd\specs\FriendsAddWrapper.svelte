<script lang="ts">
  import { setContext } from 'svelte';
  import {
    CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT,
    INITIAL_LANGUAGE,
  } from '../../../constant';
  import { initI18nContext, SocialServicesContext } from '../../../context';
  import FriendsAdd from '../FriendsAdd.svelte';

  export let context: SocialServicesContext;
  initI18nContext(INITIAL_LANGUAGE);
  setContext(CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT, context);
</script>

<FriendsAdd />
