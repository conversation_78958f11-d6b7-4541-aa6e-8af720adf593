name: t2gp-social-frontend

on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
jobs:
  build:
    name: 'build t2gp-social-frontend'
    runs-on: [self-hosted, linux]

    strategy:
      matrix:
        node-version: [12.x]

    steps:
      - name: cleanup
        run: rm -rf *
      - uses: actions/checkout@v1
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v1
        with:
          node-version: ${{ matrix.node-version }}
          registry-url: https://npm.pkg.github.com/
          scope: take-two-t2gp
      - run: npm ci
        env:
          NODE_AUTH_TOKEN: ${{secrets.T2GP_BUILD_TOKEN}}
      - run: npm run lint:patch
      - run: npm run lint:all
      - run: npm run validate
      - run: npm run test
      - run: npm run build
      - name: Danger Checks
        if: github.event_name == 'pull_request'
        run: |
          npx danger ci -f
        env:
          GITHUB_TOKEN: ${{ secrets.T2GP_BUILD_TOKEN }}
      - uses: cycjimmy/semantic-release-action@v2
        if: github.event_name == 'push'
        env:
          NODE_AUTH_TOKEN: ${{secrets.T2GP_BUILD_TOKEN}}
          GITHUB_TOKEN: ${{ secrets.T2GP_BUILD_TOKEN }}
          NPM_TOKEN: ${{ secrets.T2GP_BUILD_TOKEN}}
        with:
          semantic_version: 17.0.8
          extra_plugins: |
            @semantic-release/changelog@5.0.1
            @semantic-release/git@9.0.0
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Copy to S3
        run: |
          rm -rf public/dist
          cp -r dist public
          aws s3 sync public/ s3://t2gp-social/t2gp-social-frontend/${{ github.sha }}/
      - name: Update bundlesize on S3
        if: github.event_name == 'push'
        run: |
          aws s3 cp .size-snapshot.json s3://t2gp-web-bundlesize/t2gp-social-frontend/.size-snapshot.json
