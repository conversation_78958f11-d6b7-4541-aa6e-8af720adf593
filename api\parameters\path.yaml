pGroupid:
  name: pGroupid
  in: path
  required: true
  description: The id of the group
  schema:
    $ref: "../schemas/fields.yaml#/groupid"
pRoomid:
  name: pRoomid
  in: path
  required: true
  description: The id of the chat room
  schema:
    $ref: "../schemas/fields.yaml#/groupid"
pUserid:
  name: pUserid
  in: path
  required: true
  description: The id of the user
  schema:
    $ref: "../schemas/fields.yaml#/userid"
pMemberid:
  name: pMemberid
  in: path
  required: true
  description: The id of the member
  schema:
    $ref: "../schemas/fields.yaml#/userid"
pApproverid:
  name: pApproverid
  in: path
  required: true
  description: The id of the approver. FOR JOIN REQUEST ONLY, you may use 'leader' without quotes for the group leader without knowing the id.  Invites require the explicit id as invites can be made by all party members at times.
  schema:
    $ref: "../schemas/fields.yaml#/approverid"
pFriendid:
  name: pFriendid
  in: path
  required: true
  description: The id of the friend
  schema:
    $ref: "../schemas/fields.yaml#/dnaid"
pOnlineServiceType:
  name: pOnlineServiceType
  in: path
  required: true
  description: The ost of the platform
  schema:
    $ref: "../schemas/fields.yaml#/onlineServiceType"
pFirstPartyid:
  name: pFirstPartyid
  in: path
  required: true
  description: The first party user id
  schema:
    $ref: "../schemas/fields.yaml#/firstPartyid"
pSessionid:
  name: pSessionid
  in: path
  required: true
  description: The first party session id
  schema:
    $ref: "../schemas/fields.yaml#/firstPartySessionid"
pFirstPartyApproverid:
  name: pFirstPartyApproverid
  in: path
  required: true
  description: The first party approver's user id
  schema:
    $ref: "../schemas/fields.yaml#/firstPartyid"
pEndorsementName:
  name: pEndorsementName
  in: path
  required: true
  description: The name of the endorsement to be acted upon
  schema:
    $ref: "../schemas/fields.yaml#/endorsementName"
