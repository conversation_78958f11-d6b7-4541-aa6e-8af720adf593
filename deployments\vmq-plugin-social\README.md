# t2gp_social_plugin

T2GP Social Plugin for VerneMQ

## Getting started

You need to have the following installed
* Docker
* make (For windows, use [scoop](https://scoop.sh/) to install make)

    $ make start build reload
* Create `.env` file with the contents from [1password](https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/5herwn7efaj3l6z3oyzn2fqbqu)
* You also need to set `AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, and AWS_SESSION_TOKEN` from AWS

### Build

    $ make build

### Start/Stop VMQ

    $ make start
    $ make stop

### Reload plugin into VMQ

Minor changes should autoreload. You would need to do this if the supervisor process crashes.

    $ make reload

### Get a docker shell into VMQ container

    $ make shell

### Inspecting local dynamodb

The local dynamodb can be inspected through http://localhost:8002/

## Implementation Details

### Allowed subscibed topics

* user/`[userid]` - Used by the system to send/receive messages for a specific user
* chat/`[userid]` - 1:1 chat messages
* chat/room/`[roomid]` - group chat room messages
* presence/`[userid]` - Friend presence information

Invites to friends, groups, etc, are received from the user/`[userid]` topic.
Messages to query, are sent also sent to user/`[userid]` topic.

## Deployment

Commits here are **NOT** deployed automatically.

To deploy the corresponding commit to social environment, you will need to update the submodule's pointer in that repo.

# VMQ Debug commands

Some helpful VMQ debug commands given to us by the folks at VMQ.

### Web UI for monitoring 
https://social-service-<env>-mqtt.d2dragon.net/status

### attaches to vernemq
    vernemq attach
### start observer text display
    observer_cli:start().

### memory usage vs allocated
    [recon_alloc:memory(used), recon_alloc:memory(allocated), recon_alloc:memory(used)/recon_alloc:memory(allocated)].
### force garbage collect
    [erlang:garbage_collect(Pid) || Pid <- processes()].
### system memory allocation histogram
    instrument:allocations().

### Trace Functions in vmq shell
    recon_trace:calls({module_name, function_name, fun(_) -> return_trace() end}, 100, [{scope, local}]).
options: https://ferd.github.io/recon/recon_trace.html    

## ETS commands for finding apikey

### list tables
    ets:i()
### get value
    ets:lookup(table,key)
### lookup apikey in ets
    ets:lookup(vmq_config_cache, {vmq_server,http_mgmt_api_keys}).

### get secrets for env.  at bash prompt, not verne attach
    t2gp_social_app:get_sm_secrets("<environment>").
    
    
### kubectl command to copy a file to server if you need to replace manually
    kubectl --kubeconfig=config.prod -n social-service -c crash-logs cp 3a7daf6.tar.gz social-service-mqtt-cert-sumo-0:/tmp/

# Possible Startup Errors

> Note that Supervisor errors can be found under `/vernemq/log/log/crash.log` on the mqtt pod, or they can be revealed by calling `t2gp_social_dna:start_link().`


```
{noproc,{gen_server,call, ...}}
```
Means missing gen_server service. This indicates that one of our modules has possibly failed.
The modules used by our plugin is defined [here](https://github.com/take-two-t2gp/vmq-plugin-social/blob/develop/src/t2gp_social_sup.erl). Whatever is causing the error will also have its name in the noproc error.
Note that without gen_server, our plugin cannot function properly.

To ensure all modules are started correctly, attach a vernemq shell and run `t2gp_social_sup:start_link().`
You can also test individual modules by starting them in isolation. For example, **t2gp_social_dna** you can restart with `t2gp_social_dna:start_link().`.

```
{error, nxdomain}
```
Means can't do dns lookup.

# Ping command to check node sync health
`net_adm:ping('<EMAIL>').`

# Find node for session then trace by client id
```
vmq-admin session show --client_id --node | grep 
vmq-admin trace client client-id=
```
