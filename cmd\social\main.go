package main

import (
	"context"
	"errors"
	"net/http"
	"os"
	"strconv"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	pApi "github.com/take-two-t2gp/t2gp-social-service/pkg/api/private"
	tApi "github.com/take-two-t2gp/t2gp-social-service/pkg/api/trusted"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/rs/zerolog/log"
	chitrace "gopkg.in/DataDog/dd-trace-go.v1/contrib/go-chi/chi.v5"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"gopkg.in/DataDog/dd-trace-go.v1/profiler"

	"github.com/take-two-t2gp/t2gp-social-service/constants"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipriv"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	socialmiddleware "github.com/take-two-t2gp/t2gp-social-service/pkg/middleware"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/middleware/oapi"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/net"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/notification"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/store"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func main() {
	cfg := config.NewConfig()
	config.ParseConfigFlags(cfg)
	if utils.IsNonProdCluster() {
		logger.SetupLogger(true)
	} else {
		logger.SetupLogger(cfg.DebugMode)
	}
	if utils.IsLocal() {
		cfg.SetLocalConfigOptions()
	}
	ctx := context.Background()

	if cfg.DatadogAPMEnabled {
		tracer.Start(tracer.WithDebugMode(cfg.DebugMode))
		defer tracer.Stop()
	}
	if cfg.DatadogProfilerEnabled {
		profile := profiler.WithProfileTypes(profiler.CPUProfile, profiler.HeapProfile)

		if !utils.IsProdCluster() {
			profile =
				profiler.WithProfileTypes(
					profiler.CPUProfile,
					profiler.HeapProfile,
					profiler.BlockProfile,
					profiler.MutexProfile,
					profiler.GoroutineProfile,
					profiler.MetricsProfile)
		}
		if !utils.IsLocal() {
			profiler.Start(profile, profiler.WithService(cfg.ServiceName), profiler.WithEnv(os.Getenv("DD_ENV")), profiler.WithVersion(cfg.Version.Version))
			defer profiler.Stop()
		}
	}
	if cfg.DnsCaching {
		net.Dns.DatadogEnabled(cfg.DatadogAPMEnabled)
		net.Dns.Start()
	}
	switch cfg.ServerMode {
	case "social":
		startServerModeSocial(ctx, cfg)
	case "discovery":
		startServerModeDiscovery(ctx, cfg)
	default:
		log.Error().Msgf("Unknown server mode %s", cfg.ServerMode)
	}
}

func startServerModeSocial(ctx context.Context, cfg *config.Config) {
	var sm *health.ServiceMonitor

	ddSvc, _ := os.LookupEnv("DD_SERVICE")
	log := logger.FromContext(ctx)
	// if trusted server pass to health service setup
	log.Info().Str("DD_SERVICE", ddSvc).Msgf("DD_SERVICE env = >%s<", ddSvc)
	if utils.IsTrustedServer() {
		sm = health.NewServiceMonitor("t2gp-social-trusted-server", cfg.Version.Version)
	} else {
		sm = health.NewServiceMonitor("t2gp-social-service", cfg.Version.Version)
	}

	// create aws clients
	ddb := store.NewDynamoDB(ctx, cfg)
	sm.AddDependentService("social-service-dynamodb", ddb)

	s3 := store.NewS3(ctx, cfg)
	sm.AddDependentService("social-service-s3", s3)

	//Kinesis data stream
	ki := telemetry.NewKinesis(ctx, cfg)
	sm.AddDependentService("social-service-kinesis", ki)

	// telemetry
	t := telemetry.NewTelemetry(cfg, ki)

	//identity
	id := identity.NewIdentity(ctx, cfg, sm)
	id.Start(ctx)

	// create new redis cache.  Start background ticker to refresh lock.  subscribe to keyspace notifications for expired keys.
	rc := cache.NewRedisCache(ctx, cfg, t, id)
	// do not start listeners if trusted server
	if !utils.IsTrustedServer() {
		go rc.StartExpirationChecker(ctx)
	}
	sm.AddDependentService("social-service-redis", rc)

	// uncomment to clear local dynamo table
	// if utils.IsLocal() {
	// 	deleteRequest := &dynamodb.DeleteTableInput{
	// 		TableName: &cfg.ProfileTable,
	// 	}
	// 	ddb.DeleteTable(deleteRequest)
	// }

	sm.Start()
	if utils.IsLocal() {
		go startPrivateServer(ctx, cfg, sm, ddb, s3, rc, t, id)
		go startTrustedServer(ctx, cfg, sm, ddb, s3, rc, t, id)
		startPublicServer(ctx, cfg, sm, ddb, s3, rc, t, id)
	} else if utils.IsTrustedServer() {
		startTrustedServer(ctx, cfg, sm, ddb, s3, rc, t, id)
	} else {
		go startPrivateServer(ctx, cfg, sm, ddb, s3, rc, t, id)
		startPublicServer(ctx, cfg, sm, ddb, s3, rc, t, id)
	}
}

func startPrivateServer(ctx context.Context, cfg *config.Config, sm *health.ServiceMonitor, ddb store.DynamoDBInterface, s3 store.S3Interface, redisCache *cache.RedisCache, t telemetry.TelemetryInterface, id identity.IdentityInterface) {
	ds := store.NewDataStore(ctx, cfg, ddb, s3, id)
	privateApi := pApi.NewSocialPrivateAPI(cfg, ds, redisCache, sm, t, id)

	r := chi.NewRouter()
	if cfg.DatadogAPMEnabled {
		opt := chitrace.WithIgnoreRequest(utils.IgnoreRequest)
		r.Use(chitrace.Middleware(opt))
	}
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(logger.NewLogger())
	r.Use(socialmiddleware.Recoverer)
	if utils.IsStaging() || utils.IsProduction() {
		r.Use(middleware.Timeout(3 * time.Second))
	} else {
		r.Use(middleware.Timeout(1 * time.Hour))
	}

	// authenticate private api w/ app id and secret
	dnas := identity.NewDNAService(cfg)
	r.Use(socialmiddleware.BasicAuth(dnas.Identity()))

	// Register SocialPublicAPI
	apipriv.HandlerWithOptions(privateApi, apipriv.ChiServerOptions{
		BaseURL:          "/v2",
		BaseRouter:       r,
		ErrorHandlerFunc: ErrorHandler,
	})
	r.Mount("/debug", middleware.Profiler())

	r.NotFound(func(w http.ResponseWriter, r *http.Request) {
		e := errs.New(http.StatusNotFound, errs.EHttp+http.StatusNotFound)
		errs.Return(w, r, e)
	})

	// static route for admin website
	api.StaticRoutes(r)

	log.Info().Msgf("listen private port [%d]", cfg.PrivatePort)
	http.ListenAndServe("0.0.0.0:"+strconv.Itoa(cfg.PrivatePort), r)
}

func startPublicServer(ctx context.Context, cfg *config.Config, sm *health.ServiceMonitor, ddb store.DynamoDBInterface, s3 store.S3Interface, redisCache *cache.RedisCache, t telemetry.TelemetryInterface, id identity.IdentityInterface) {
	sns := notification.NewSNS(ctx, cfg)
	ds := store.NewDataStore(ctx, cfg, ddb, s3, id)
	socialAPI := api.NewSocialPublicAPI(cfg, ds, redisCache, sm, t, id, sns)

	r := chi.NewRouter()
	_ = ds.ReadSessionPolicyConfigs(context.Background())

	corsVar := cors.New(cors.Options{
		AllowedOrigins: cfg.CorsAllowedOrigins,
		AllowedMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"Accept", "Authorization", constants.KContentType, constants.KT2GPLabel},
		MaxAge:         300,
		Debug:          cfg.DebugMode,
	})

	if utils.IsNonProdCluster() {
		corsVar = cors.New(cors.Options{
			AllowedOrigins: []string{"https://*", "http://*"},
			AllowedMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
			AllowedHeaders: []string{"Accept", "Authorization", constants.KContentType, constants.KT2GPLabel},
			MaxAge:         300,
			Debug:          cfg.DebugMode,
		})
	}

	// socialAuth := socialMiddleware.NewMiddleware(cfg)
	// r.Use(socialAuth.AuthenticateRequest)
	if cfg.DatadogAPMEnabled {
		opt := chitrace.WithIgnoreRequest(utils.IgnoreRequest)
		r.Use(chitrace.Middleware(opt))
	}
	r.Use(socialmiddleware.AddCustomHeadersToRootSpan())
	r.Use(socialmiddleware.BearerJWT(cfg, redisCache))
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(logger.NewLogger())
	r.Use(socialmiddleware.Recoverer)
	r.Use(corsVar.Handler)
	if utils.IsNonProdCluster() {
		r.Use(middleware.Timeout(1 * time.Hour))
	} else {
		r.Use(middleware.Timeout(3 * time.Second))
	}
	r.Use(socialmiddleware.LimitRequestBody(cfg.MaxRequestBodyBytes))

	if cfg.ShouldValidateYaml {
		oapi.AddValidateMiddleware(r, cfg, id)
	}
	// errors test triggers
	r.Get("/v2/dev/error/{errorID}", errs.HttpErrorHandler)

	// Register SocialPublicAPI
	apipub.HandlerWithOptions(socialAPI, apipub.ChiServerOptions{
		BaseURL:          "/v2",
		BaseRouter:       r,
		ErrorHandlerFunc: ErrorHandler,
	})
	r.NotFound(func(w http.ResponseWriter, r *http.Request) {
		e := errs.New(http.StatusNotFound, errs.EHttp+http.StatusNotFound)
		errs.Return(w, r, e)
	})

	log.Info().Msgf("listen public port [%d]", cfg.PublicPort)
	http.ListenAndServe("0.0.0.0:"+strconv.Itoa(cfg.PublicPort), r)
}

func startTrustedServer(ctx context.Context, cfg *config.Config, sm *health.ServiceMonitor, ddb store.DynamoDBInterface, s3 store.S3Interface, redisCache *cache.RedisCache, t telemetry.TelemetryInterface, id identity.IdentityInterface) {
	sns := notification.NewSNS(ctx, cfg)
	ds := store.NewDataStore(ctx, cfg, ddb, s3, id)
	socialAPI := api.NewSocialPublicAPI(cfg, ds, redisCache, sm, t, id, sns)
	trustedAPI := tApi.NewSocialTrustedAPI(socialAPI, ctx, cfg)
	if trustedAPI == nil {
		return
	}

	r := chi.NewRouter()
	_ = ds.ReadSessionPolicyConfigs(context.Background())

	cors := cors.New(cors.Options{
		AllowedOrigins: cfg.CorsAllowedOrigins,
		AllowedMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"Accept", "Authorization", constants.KContentType, constants.KT2GPLabel},
		MaxAge:         300,
		Debug:          cfg.DebugMode,
	})

	if cfg.DatadogAPMEnabled {
		opt := chitrace.WithIgnoreRequest(utils.IgnoreRequest)
		r.Use(chitrace.Middleware(opt))
	}
	r.Use(socialmiddleware.BearerJWT(cfg, redisCache))
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(logger.NewLogger())
	r.Use(socialmiddleware.Recoverer)
	r.Use(cors.Handler)
	if utils.IsNonProdCluster() {
		r.Use(middleware.Timeout(1 * time.Hour))
	} else {
		r.Use(middleware.Timeout(3 * time.Second))
	}
	r.Use(socialmiddleware.LimitRequestBody(cfg.MaxRequestBodyBytes))

	if cfg.ShouldValidateYaml {
		oapi.AddValidateMiddlewareTrusted(r, cfg, id)
	}
	// errors test triggers
	r.Get("/v2/dev/error/{errorID}", errs.HttpErrorHandler)

	apitrusted.HandlerWithOptions(trustedAPI, apitrusted.ChiServerOptions{
		BaseURL:          "/v2",
		BaseRouter:       r,
		ErrorHandlerFunc: ErrorHandler,
	})
	r.NotFound(func(w http.ResponseWriter, r *http.Request) {
		e := errs.New(http.StatusNotFound, errs.EHttp+http.StatusNotFound)
		errs.Return(w, r, e)
	})

	log.Info().Msgf("listen ts port [%d]", cfg.TSPort)
	http.ListenAndServe("0.0.0.0:"+strconv.Itoa(cfg.TSPort), r)
}

func startServerModeDiscovery(ctx context.Context, cfg *config.Config) {
	r := chi.NewRouter()

	cors := cors.New(cors.Options{
		AllowedOrigins: cfg.CorsAllowedOrigins,
		AllowedMethods: []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"Accept", "Authorization", constants.KContentType, constants.KT2GPLabel},
		MaxAge:         300,
		Debug:          cfg.DebugMode,
	})

	if cfg.DatadogAPMEnabled {
		opt := chitrace.WithIgnoreRequest(utils.IgnoreRequest)
		r.Use(chitrace.Middleware(opt))
	}
	r.Use(middleware.RequestID)
	r.Use(middleware.RealIP)
	r.Use(logger.NewLogger())
	r.Use(socialmiddleware.Recoverer)
	r.Use(cors.Handler)
	if utils.IsNonProdCluster() {
		r.Use(middleware.Timeout(1 * time.Hour))
	} else {
		r.Use(middleware.Timeout(3 * time.Second))
	}
	r.Use(socialmiddleware.LimitRequestBody(cfg.MaxRequestBodyBytes))

	// create aws clients
	sm := health.NewServiceMonitor("t2gp-social-service", cfg.Version.Version)
	ddb := store.NewDynamoDB(ctx, cfg)
	s3 := store.NewS3(ctx, cfg)
	id := identity.NewIdentity(ctx, cfg, sm)
	id.Start(ctx)
	ds := store.NewDataStore(ctx, cfg, ddb, s3, id)
	sns := notification.NewSNS(ctx, cfg)
	socialAPI := api.NewSocialPublicAPI(cfg, ds, nil, nil, nil, nil, sns)

	// errors test triggers
	r.Get("/v2/discovery", func(w http.ResponseWriter, r *http.Request) {
		params := apipub.GetDiscoveryParams{}
		socialAPI.GetDiscovery(w, r, params)
	})

	log.Info().Msgf("listen public port [%d]", cfg.PublicPort)
	http.ListenAndServe("0.0.0.0:"+strconv.Itoa(cfg.PublicPort), r)
}

func ErrorHandler(w http.ResponseWriter, r *http.Request, err error) {
	var socialError *errs.Error
	if errors.As(err, &socialError) {
		errs.Return(w, r, socialError)
		return
	}
}
