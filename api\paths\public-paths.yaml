/auth/login:
  summary: <PERSON>gin user to DNA with social platform using email and password
  post:
    security: []
    tags:
      - Authentication
    summary: Login to DNA account
    description: T2GP API for Authentication to DNA.  Recommended that game teams use DCL rather than our API to obtain token.  We do some automatic presence mamagement for our web app and it uses unknown DNA OST etc.  This is disabled on production.  It's meant to just facilitate getting a token during early testing.
    operationId: login
    requestBody:
      $ref: "../requests/public-requests.yaml#/loginRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/loginResponseBody"
/auth/logout:
  summary: Logout user
  post:
    tags:
      - Authentication
    summary: Logout user
    description: Logging out the user will invalidate the JWT.  Subsequent refreshes of the `refreshToken` will fail. The JWT will still be valid until the expiration time. Recommended that game teams use DCL rather than our API to logout.
    operationId: logout
    requestBody:
      $ref: "../requests/public-requests.yaml#/logoutRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/logoutResponseBody"
/auth/refresh:
  summary: Refresh JWT token
  post:
    security: []
    tags:
      - Authentication
    summary: Refresh token
    description: Refresh the JWT token. You will need to send the `refreshToken` from the login response. After logout, the `refreshToken` will not longer work. Recommended that game teams use DCL rather than our API to refresh token.
    operationId: refreshToken
    requestBody:
      $ref: "../requests/public-requests.yaml#/refreshRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/refreshResponseBody"
/user/profile:
  get:
    summary: Get current logged in user profile
    operationId: getUserProfile
    tags:
      - Profile
    responses:
      $ref: "../responses/public-responses.yaml#/getProfileResponseBody"
/user/profile/sync:
  post:
    summary:
      Force a sync profile w/ DNA values. The SSO sync service makes this API unnecessary unless access to profile data is needed *immediately* after an profile update.  SSO sync usually happens within seconds.
      We recommend NOT using this endpoint and letting it sync on its own as it can be slow to return and our back end sync is real time.
    operationId: syncUserProfile
    tags:
      - Profile
    responses:
      $ref: "../responses/public-responses.yaml#/syncProfileResponseBody"
/user/played:
  get:
    summary: Get list of recently played with users.
    operationId: getRecentlyPlayed
    tags:
      - Profile
    parameters:
      - $ref: "../parameters/query.yaml#/limit"
      - $ref: "../parameters/query.yaml#/next"
    responses:
      $ref: "../responses/public-responses.yaml#/getPlayedResponseBody"
  post:
    summary: Update recently played list of users
    operationId: updateRecentlyPlayed
    requestBody:
      $ref: "../requests/public-requests.yaml#/setPlayedRequestBody"
    tags:
      - Profile
    responses:
      $ref: "../responses/public-responses.yaml#/setPlayedResponseBody"
  delete:
    tags:
      - Profile
    summary: clear user's entire recently played list
    description: Mainly useful for automated tests.
    operationId: deleteRecentlyPlayed
    responses:
      $ref: "../responses/public-responses.yaml#/clearPlayedResponseBody"
/user/presence:
  get:
    summary: Get all user presence objects for productid in the user's JWT. Parameters are deprecated.
    operationId: getUserPresence
    tags:
      - Presence
    responses:
      $ref: "../responses/public-responses.yaml#/getPresenceResponseBody"
  post:
    summary: Set presence for user
    operationId: setUserPresence
    requestBody:
      $ref: "../requests/public-requests.yaml#/setPresenceRequestBody"
    tags:
      - Presence
    responses:
      $ref: "../responses/public-responses.yaml#/setPresenceResponseBody"
  delete:
    summary: Clear user presence for the productid provided in the user's JWT
    operationId: clearPresence
    tags:
      - Presence
    responses:
      $ref: "../responses/public-responses.yaml#/clearPresenceResponseBody"
/user/presence/active:
  post:
    summary: Set the active group for the user
    operationId: setActiveGroup
    tags:
      - Presence
    requestBody:
      $ref: "../requests/public-requests.yaml#/setActiveGroupRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/setActiveGroupResponseBody"
  delete:
    summary: Clear the active group for the user
    description: Clear the active group for the user and productid provided in the user's JWT.
    operationId: clearActiveGroup
    tags:
      - Presence
    responses:
      $ref: "../responses/public-responses.yaml#/clearActiveGroupResponseBody"
/user/presence/heartbeat:
  post:
    summary: Heartbeat presence for user
    operationId: presenceHeartBeat
    tags:
      - Presence
    responses:
      $ref: "../responses/public-responses.yaml#/heartbeatPresenceResponseBody"
/user/blocklist:
  get:
    tags:
      - Abuse
    parameters:
      - $ref: "../parameters/query.yaml#/limit"
      - $ref: "../parameters/query.yaml#/next"
    summary: Get user's block list.
    operationId: getBlocklist
    responses:
      $ref: "../responses/public-responses.yaml#/getBlocklistResponseBody"
  post:
    tags:
      - Abuse
    summary: Batch import users to blocklist.  Must submit no more than 20 userids per request.
    operationId: importBlocklist
    requestBody:
      $ref: "../requests/public-requests.yaml#/importBlocklistRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/importBlocklistResponseBody"
  delete:
    tags:
      - Abuse
    summary: clear user's entire block list.
    description: Mainly useful for automated tests. Returns the blocklist after clear.
    operationId: delBlocklist
    responses:
      $ref: "../responses/public-responses.yaml#/clearBlocklistResponseBody"
/user/blocklist/{pUserid}:
  post:
    tags:
      - Abuse
    summary: Add a user to blocklist
    parameters:
      - $ref: "../parameters/path.yaml#/pUserid"
    operationId: addBlocklist
    requestBody:
      $ref: "../requests/public-requests.yaml#/addBlocklistRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/addBlocklistResponseBody"
  delete:
    tags:
      - Abuse
    summary: remove user from blocklist
    parameters:
      - $ref: "../parameters/path.yaml#/pUserid"
    operationId: removeBlocklist
    responses:
      $ref: "../responses/public-responses.yaml#/removeBlocklistResponseBody"
/search/2KUsers:
  get:
    summary: This search API is to search for full 2K accounts by full account name.
    description: This search API is to search for full 2K accounts by full account name.
    operationId: search2KUsers
    tags:
      - Search
    parameters:
      - $ref: "../parameters/query.yaml#/displayName"
    responses:
      $ref: "../responses/public-responses.yaml#/search2KResponseBody"
/friends:
  get:
    summary: List friends for user
    operationId: listFriends
    tags:
      - Friends
    parameters:
      - $ref: "../parameters/query.yaml#/status"
      - $ref: "../parameters/query.yaml#/limit"
      - $ref: "../parameters/query.yaml#/next"
    responses:
      $ref: "../responses/public-responses.yaml#/getFriendsResponseBody"
/friends/{pFriendid}:
  get:
    summary: Get a single friend by friendid
    parameters:
      - $ref: "../parameters/path.yaml#/pFriendid"
    operationId: getFriend
    tags:
      - Friends
    responses:
      $ref: "../responses/public-responses.yaml#/getFriendResponseBody"
  post:
    summary: Invite a user to become friends or accept invite
    description:
      When a user invites a friend, an invitation message will be set out through MQTT.
      The receiving user will call this same API to establish a friendship if the users accepts.
    operationId: makeFriend
    parameters:
      - $ref: "../parameters/path.yaml#/pFriendid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/setFriendsRequestBody"
    tags:
      - Friends
    responses:
      $ref: "../responses/public-responses.yaml#/setFriendsResponseBody"
  patch:
    summary: Update friend `viewed` flag
    operationId: updateFriendStatus
    tags:
      - Friends
    parameters:
      - $ref: "../parameters/path.yaml#/pFriendid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/setFriendViewedRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/setFriendViewedResponseBody"
  delete:
    summary: Delete a friend, reject invite, or revoke invite
    description: |
      Depending on the state of the friendship, this will do the following based on the status:
        * friend: breaks the friendship relationship
        * pending from invitor: revokes invitation
        * pending from invitee: rejects invitation
    parameters:
      - $ref: "../parameters/path.yaml#/pFriendid"
    operationId: deleteFriend
    tags:
      - Friends
    responses:
      $ref: "../responses/public-responses.yaml#/delFriendResponseBody"
/friends/accounts/search:
  get:
    summary: Find common platforms users that have DNA accounts
    tags:
      - Friends
    operationId: importPlatformFriends
    parameters:
      - name: id
        in: query
        description: Comma separated platform id to be queried (limit 100)
        required: true
        schema:
          type: string
    responses:
      $ref: "../responses/public-responses.yaml#/importFriendsResponseBody"
/user/{pUserid}/report:
  post:
    summary: Report a user for abuse
    operationId: reportUserAbuse
    tags:
      - Abuse
    parameters:
      - $ref: "../parameters/path.yaml#/pUserid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/setReportRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/setReportResponseBody"
/groups:
  get:
    summary: Get groups that the user belongs to
    operationId: getGroups
    parameters:
      - $ref: "../parameters/query.yaml#/limit"
      - $ref: "../parameters/query.yaml#/next"
    tags:
      - Groups
    responses:
      $ref: "../responses/public-responses.yaml#/getGroupsResponseBody"
  post:
    summary: Create a Group
    operationId: createGroup
    tags:
      - Groups
    requestBody:
      $ref: "../requests/public-requests.yaml#/createGroupRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/createGroupResponseBody"
/groups/{pGroupid}:
  delete:
    tags:
      - Groups
    summary: Disband/Delete a Group
    operationId: deleteGroup
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
    responses:
      $ref: "../responses/public-responses.yaml#/delGroupResponseBody"
  get:
    tags:
      - Groups
    summary: Get a Group
    operationId: getGroup
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
    responses:
      $ref: "../responses/public-responses.yaml#/getGroupResponseBody"
  patch:
    tags:
      - Groups
    summary:
      "Update a group's properties.  Valid properties are: canMembersInvite, joinRequestAction, maxMembers, meta, password.
      It is recommended that you only submit fields that should be modified."
    operationId: updateGroup
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/updateGroupRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/updateGroupResponseBody"
/groups/{pGroupid}/members:
  get:
    tags:
      - Groups
    summary: Get a detailed list of member information for a specific group
    operationId: getGroupMembers
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
    responses:
      $ref: "../responses/public-responses.yaml#/getGroupMembersResponseBody"
/groups/{pGroupid}/members/{pMemberid}:
  get:
    tags:
      - Groups
    summary: Get member in group
    operationId: getGroupMember
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    responses:
      $ref: "../responses/public-responses.yaml#/getGroupMemberResponseBody"
  patch:
    tags:
      - Groups
    summary: Update member of group
    operationId: updateGroupMember
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/updateGroupMemberRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/updateGroupMemberResponseBody"
  delete:
    tags:
      - Groups
    summary: Kick a member from group
    operationId: kickMemberFromGroup
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
      - name: reason
        in: query
        description: reason for removing a group member
        required: false
        schema:
          type: string
    responses:
      $ref: "../responses/public-responses.yaml#/kickMemberResponseBody"
/groups/{pGroupid}/members/me:
  delete:
    tags:
      - Groups
    summary: Leave from group
    operationId: leaveGroup
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
    responses:
      $ref: "../responses/public-responses.yaml#/leaveGroupResponseBody"
/groups/{pGroupid}/members/{pMemberid}/meta:
  put:
    tags:
      - Groups
    summary: Update the metadata of a specified group member. The API accepts an update request only if the submitted time is later than the member's existing metadata. Values in the request body will be added to the member's metadata if their keys do not already exist. Otherwise, the values will overwrite the existing ones.
    operationId: updateGroupMemberMeta
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/updateGroupMemberMetaRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/updateGroupMemberMetaResponseBody"
/groups/{pGroupid}/control:
  post:
    tags:
      - Groups
    summary: Send a control message (json or binary)
    operationId: sendControlMessage
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/sendControlMessageRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/sendControlMessageResponseBody"
/memberships/invites/me:
  get:
    tags:
      - Invite
    summary: Get user's invitations
    operationId: getInvites
    parameters:
      - $ref: "../parameters/query.yaml#/limit"
      - $ref: "../parameters/query.yaml#/next"
    responses:
      $ref: "../responses/public-responses.yaml#/getInvitesResponseBody"
/memberships/invites/groups/{pGroupid}/members/{pMemberid}:
  post:
    summary: Invite a user to the group
    description: Creates and send an invite to a user for the group.
    tags:
      - Invite
    operationId: sendInvite
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/sendInviteRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/sendInviteResponseBody"
  delete:
    summary: Revoke an invite
    description: |
      Revoke all group invites from the sender to the memberid.
    tags:
      - Invite
    operationId: revokeInvite
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    responses:
      $ref: "../responses/public-responses.yaml#/revokeInviteResponseBody"
/memberships/invites/groups/{pGroupid}/approvers/{pApproverid}:
  patch:
    tags:
      - Invite
    summary: Accept invite to a group.
    description: Accepts an invite to a user for the group.
    operationId: acceptInvite
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pApproverid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/acceptInviteRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/acceptInviteResponseBody"
  delete:
    tags:
      - Invite
    summary: Decline user's invite to group from inviter
    operationId: declineInvites
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pApproverid"
    responses:
      $ref: "../responses/public-responses.yaml#/declineInviteResponseBody"
/memberships/requests/groups/{pGroupid}/approvers/{pApproverid}:
  post:
    summary: Request to join group
    description: |
      Creates and sends a join request for a group.
    tags:
      - JoinRequest
    operationId: sendJoinRequest
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pApproverid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/requestJoinRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/requestJoinResponseBody"
/memberships/requests/groups/{pGroupid}/members/{pMemberid}:
  patch:
    summary: Approve a group join request
    description: |
      Approve a group join request for the memberid.
    tags:
      - JoinRequest
    operationId: approveJoinRequest
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/approveJoinRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/approveJoinResponseBody"
  delete:
    summary: Reject a group join request
    description: |
      Reject a group join request for the memberid.
    tags:
      - JoinRequest
    operationId: rejectJoinRequest
    parameters:
      - $ref: "../parameters/path.yaml#/pGroupid"
      - $ref: "../parameters/path.yaml#/pMemberid"
    responses:
      $ref: "../responses/public-responses.yaml#/rejectJoinResponseBody"
/chatRoom/{pRoomid}/message:
  post:
    tags:
      - Chat
    summary: Send a message to a chat room
    operationId: sendChatMessage
    parameters:
      - $ref: "../parameters/path.yaml#/pRoomid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/sendChatMessageRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/sendChatMessageResponseBody"
/endorsements/users/me:
  get:
    tags:
      - Endorsements
    summary: get endorsements for self
    operationId: getEndorsementsForSelf
    responses:
      $ref: "../responses/public-responses.yaml#/getEndorsementResponseBody"
/endorsements/users/{pUserid}:
  get:
    tags:
      - Endorsements
    summary: get endorsement for other user
    parameters:
      - $ref: "../parameters/path.yaml#/pUserid"
    operationId: getEndorsementsForUser
    responses:
      $ref: "../responses/public-responses.yaml#/getEndorsementResponseBody"
  post:
    tags:
      - Endorsements
    summary: increment endorsement for user
    parameters:
      - $ref: "../parameters/path.yaml#/pUserid"
    operationId: incrementEndorsement
    requestBody:
      $ref: "../requests/public-requests.yaml#/incrementEndorsementRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/incrementEndorsementResponseBody"
/platform/{pOnlineServiceType}/user/{pFirstPartyid}/auth:
  put:
    tags:
      - SessionSynchronize
    summary: send platform auth code to server for user to use in Session Syncing
    parameters:
      - $ref: "../parameters/path.yaml#/pOnlineServiceType"
      - $ref: "../parameters/path.yaml#/pFirstPartyid"
    operationId: upsertSessionAuth
    requestBody:
      $ref: "../requests/public-requests.yaml#/upsertSessionAuthRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/upsertSessionAuthResponseBody"
/platform/{pOnlineServiceType}/groups/{pGroupid}/sync:
  get:
    tags:
      - SessionSynchronize
    summary: request a sync from 1P session data to T2GP group
    operationId: syncSessionToGroup
    parameters:
      - $ref: "../parameters/path.yaml#/pOnlineServiceType"
      - $ref: "../parameters/path.yaml#/pGroupid"
    responses:
      $ref: "../responses/public-responses.yaml#/syncSessionToGroupResponseBody"
/platform/{pOnlineServiceType}/memberships/invites/session/{pSessionid}/approvers/{pFirstPartyApproverid}:
  patch:
    tags:
      - SessionSynchronize
    summary: Accept invite to a first party session.
    description: Accepts an invite to a user for the group.
    operationId: acceptInviteByFirstPartySessionIds
    parameters:
      - $ref: "../parameters/path.yaml#/pOnlineServiceType"
      - $ref: "../parameters/path.yaml#/pSessionid"
      - $ref: "../parameters/path.yaml#/pFirstPartyApproverid"
    requestBody:
      $ref: "../requests/public-requests.yaml#/acceptInviteBySessionRequestBody"
    responses:
      $ref: "../responses/public-responses.yaml#/acceptInviteBySessionResponseBody"
/version:
  get:
    security: []
    tags:
      - Status
    summary: Get the server version
    operationId: getVersion
    responses:
      $ref: "../responses/public-responses.yaml#/getVersionResponseBody"
/health:
  get:
    tags:
      - Status
    security: []
    summary: Get the server health status
    operationId: GetHealth
    parameters:
      - $ref: "../parameters/query.yaml#/healthid"
    responses:
      $ref: "../responses/public-responses.yaml#/getHealthResponseBody"
/discovery:
  get:
    tags:
      - Discovery
    security: []
    summary: Find endpoints to use.  Nil productid or id will return default discovery. Empty '?id=' parameter will return all for given productid with canList=true.
    operationId: getDiscovery
    parameters:
      - $ref: "../parameters/query.yaml#/discoveryPid"
      - $ref: "../parameters/query.yaml#/discoveryid"
    responses:
      $ref: "../responses/public-responses.yaml#/getDiscoveryResponseBody"
