package api

import (
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

func (tApi *SocialTrustedAPI) ServerGetDiscovery(w http.ResponseWriter, r *http.Request, params apitrusted.ServerGetDiscoveryParams) {
	_, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := utils.GetValueFromContext(r.Context(), constants.T2GPCtxProductId)
	if params.Discoveryid == nil {
		empty := ""
		params.Discoveryid = &empty
	}
	gdp := apipub.GetDiscoveryParams{
		DiscoveryPid: &productid,
		Discoveryid:  params.Discoveryid,
	}

	tApi.SocialApi.GetDiscoveryHelper(w, r, gdp, true)
}

func (tApi *SocialTrustedAPI) ServerUpsertDiscovery(w http.ResponseWriter, r *http.Request) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := jwt.ProductID
	log := logger.Get(r)

	var discoveryList apitrusted.DiscoveryListResponse
	if !api.DecodeBody(w, r, &discoveryList) {
		log.Error().Msg("Error decoding patch Server Discovery List")
		return
	}

	//get existing items
	discoveryItems, _ := tApi.SocialApi.Ds.GetDiscovery(r.Context(), productid)

	//add existing items to incoming list before set
	if discoveryItems != nil {
		for _, item := range *discoveryItems {
			bFound := false
			for _, newItem := range discoveryList {
				if item.Id == newItem.Id {
					bFound = true
					break
				}
			}
			if !bFound {
				//convert item and add to list
				tItem, err := utils.TypeConverter[apitrusted.DiscoveryResponse](item)
				if err != nil {
					log.Error().Interface("discovery item", item).Msg("Error converting apipub.Discovery to apitrusted.Discovery")
					continue
				}
				discoveryList = append(discoveryList, *tItem)
			}
		}
	}

	for _, item := range discoveryList {
		if !ValidateUrls(r.Context(), item.Urls) {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EDiscoveryInvalidUrl))
			return
		}
	}

	err := tApi.SocialApi.Ds.SetDiscovery(r.Context(), productid, &discoveryList)
	if err != nil {
		errs.Return(w, r, err.(*errs.Error))
		return
	}

	//if created
	if discoveryItems == nil {
		api.ReturnCreated(w, r, discoveryList)
	} else {
		//if Updated
		api.ReturnEmptyOK(w, r)
	}
}

func (tApi *SocialTrustedAPI) ServerDeleteDiscovery(w http.ResponseWriter, r *http.Request, params apitrusted.ServerDeleteDiscoveryParams) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := jwt.ProductID
	log := logger.Get(r)

	//see if found first && throw 404 if not found?
	discoveryItems, err := tApi.SocialApi.Ds.GetDiscovery(r.Context(), productid)
	if err != nil || (discoveryItems == nil || len(*discoveryItems) <= 0) {
		log.Error().Str("productid", productid).Str("event", "valid productid required for discovery").Msg("valid productid required for discovery")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EDiscoveryNotFound))
		return
	}

	//no params delete it all
	if params.Discoveryid == nil || *params.Discoveryid == "" {
		err = tApi.SocialApi.Ds.DeleteDiscovery(r.Context(), productid)
		if err != nil {
			errs.Return(w, r, err.(*errs.Error))
			return
		}
	} else {
		var discoveryList apitrusted.DiscoveryListResponse
		for _, item := range *discoveryItems {
			//convert item
			tItem, err := utils.TypeConverter[apitrusted.DiscoveryResponse](item)
			if err != nil {
				log.Error().Interface("discovery item", item).Msg("Error converting apipub.Discovery to apitrusted.Discovery")
				continue
			}

			if tItem.Id != *params.Discoveryid { //else if not the specified item to delete, append
				discoveryList = append(discoveryList, *tItem)
			}
		}

		//update discovery to just the ones we don't want to remove.
		err = tApi.SocialApi.Ds.SetDiscovery(r.Context(), productid, &discoveryList)
		if err != nil {
			errs.Return(w, r, err.(*errs.Error))
			return
		}
	}

	api.ReturnOK(w, r, apitrusted.EmptyObject{})
}
