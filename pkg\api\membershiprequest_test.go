package api

//
//
//import (
//	"bytes"
//	"errors"
//	"io"
//	"net/http"
//	"net/http/httptest"
//	"testing"
//	"time"
//
//	"github.com/segmentio/encoding/json"
//	gomock "go.uber.org/mock/gomock"
//
//	"github.com/aws/aws-sdk-go-v2/aws"
//	"github.com/franela/goblin"
//	. "github.com/onsi/gomega"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
//)
//
////func TestSendMembershipRequestForGroup(t *testing.T) {
////	g := goblin.Goblin(t)
////
////	mock := NewMockAPI(t)
////	defer mock.ctrl.Finish()
////
////	groupID := utils.GenerateNewULID()
////	productID := utils.GenerateRandomDNAID()
////	userID := utils.GenerateRandomDNAID()
////	userID2 := utils.GenerateRandomDNAID()
////	userID3 := utils.GenerateRandomDNAID()
////	leaderUserID := utils.GenerateRandomDNAID()
////
////	joinRequest := apipub.MembershipRequest{
////		Groupid:      groupID,
////		Productid:    &productID,
////		Status:       apipub.Requested,
////		Approverid:   userID2,
////		CanCrossPlay: aws.Bool(true),
////	}
////
////	invitation := apipub.MembershipRequest{
////		Groupid:      groupID,
////		Productid:    &productID,
////		Status:       apipub.Invited,
////		CanCrossPlay: aws.Bool(true),
////		Memberid:     userID3,
////	}
////
////	var requests = []apipub.MembershipRequest{}
////	requests = append(requests, invitation)
////	requests = append(requests, joinRequest)
////
////	member := apipub.GroupMemberResponse{
////		Userid: userID,
////		Role:   apipub.Member,
////	}
////
////	leader := apipub.GroupMemberResponse{
////		Userid: leaderUserID,
////		Role:   apipub.Leader,
////	}
////
////	var members = []apipub.GroupMemberResponse{}
////	members = append(members, member, leader)
////
////	//Member is in group
////	group := apipub.GroupResponse{
////		Groupid:            groupID,
////		Productid:          productID,
////		Members:            &members,
////		MaxMembers:         100,
////		MembershipRequests: &requests,
////		CanCrossPlay:       aws.Bool(true),
////	}
////
////	g.Describe("SendMembershipRequestForGroup", func() {
////		g.It("should return bad request with db error", func() {
////
////			//get group returns error.  Server returns 500 server error..
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
////			w, r := Login(User1JWT)
////			jsonbytes, _ := json.Marshal(invitation)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusInternalServerError)
////		})
////
////		g.It("should return not found", func() {
////
////			//get group returns nil, nil.  Server returns 404 not found.
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
////			w, r := Login(User1JWT)
////			jsonbytes, _ := json.Marshal(invitation)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusNotFound)
////		})
////
////		g.It("empty memberid on invitation error", func() {
////
////			//For Invitation, same userID was added to both the group member and the invitation.
////			//This will return err 400.
////
////			member3 := apipub.GroupMemberResponse{
////				Userid: "b287e655461f4b3085c8f244e394ff7e",
////				Role:   apipub.Member,
////			}
////			var newMembers = []apipub.GroupMemberResponse{}
////			newMembers = append(newMembers, leader, member3)
////			group.Members = &newMembers
////			w, r := Login(User1JWT)
////			jsonbytes, _ := json.Marshal(invitation)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
////
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusBadRequest)
////		})
////
////		g.It("should get bad request for invitation, logged in user cannot invite", func() {
////
////			//For Invitation, same userID was added to both the group member and the invitation.
////			//This will return err 400.
////
////			member3 := apipub.GroupMemberResponse{
////				Userid: "b287e655461f4b3085c8f244e394ff7e",
////				Role:   apipub.Member,
////			}
////			invitation2 := apipub.MembershipRequest{
////				Groupid:      groupID,
////				Productid:    &productID,
////				Status:       apipub.Invited,
////				CanCrossPlay: aws.Bool(true),
////				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
////			}
////			var newMembers = []apipub.GroupMemberResponse{}
////			newMembers = append(newMembers, leader, member3)
////			group.Members = &newMembers
////			w, r := Login(User1JWT)
////			jsonbytes, _ := json.Marshal(invitation2)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
////
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusBadRequest)
////		})
////
////		g.It("invite exists", func() {
////
////			w, r := Login(User1JWT)
////			token, tErr := authheader.ParseJWTFromRequest(r)
////			if tErr != nil {
////				errs.Return(w, r, tErr)
////				return
////			}
////			loggedInUser := token.Claims.Subject
////			leader.Userid = loggedInUser
////
////			members2 := []apipub.GroupMemberResponse{}
////			members2 = append(members2, leader)
////			requests = []apipub.MembershipRequest{}
////
////			//Member is in group
////			group = apipub.GroupResponse{
////				Groupid:            groupID,
////				Productid:          productID,
////				Members:            &members2,
////				MaxMembers:         100,
////				MembershipRequests: &requests,
////				CanCrossPlay:       aws.Bool(true),
////			}
////
////			invitation2 := apipub.MembershipRequest{
////				Groupid:      groupID,
////				Productid:    &productID,
////				Approverid:   leader.Userid,
////				Status:       apipub.Invited,
////				CanCrossPlay: aws.Bool(true),
////				Memberid:     userID3,
////			}
////
////			jsonbytes, _ := json.Marshal(invitation2)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(true)
////
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusOK)
////		})
////
////		g.It("should get bad request for final send request call", func() {
////
////			w, r := Login(User1JWT)
////			token, tErr := authheader.ParseJWTFromRequest(r)
////			if tErr != nil {
////				errs.Return(w, r, tErr)
////				return
////			}
////			loggedInUser := token.Claims.Subject
////			leader.Userid = loggedInUser
////
////			members2 := []apipub.GroupMemberResponse{}
////			members2 = append(members2, leader)
////			requests = []apipub.MembershipRequest{}
////
////			//Member is in group
////			group = apipub.GroupResponse{
////				Groupid:            groupID,
////				Productid:          productID,
////				Members:            &members2,
////				MaxMembers:         100,
////				MembershipRequests: &requests,
////				CanCrossPlay:       aws.Bool(true),
////			}
////
////			invitation2 := apipub.MembershipRequest{
////				Groupid:      groupID,
////				Productid:    &productID,
////				Approverid:   leader.Userid,
////				Status:       apipub.Invited,
////				CanCrossPlay: aws.Bool(true),
////				Memberid:     userID3,
////			}
////
////			jsonbytes, _ := json.Marshal(invitation2)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
////			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(false)
////			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
////			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
////			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
////			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
////			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
////			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
////			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
////			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
////
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusInternalServerError)
////		})
////
////		g.It("should successfully create invitation", func() {
////
////			w, r := Login(User1JWT)
////			token, tErr := authheader.ParseJWTFromRequest(r)
////			if tErr != nil {
////				errs.Return(w, r, tErr)
////				return
////			}
////			loggedInUser := token.Claims.Subject
////			leader.Userid = loggedInUser
////
////			members2 := []apipub.GroupMemberResponse{}
////			members2 = append(members2, leader)
////			requests = []apipub.MembershipRequest{}
////
////			//Member is in group
////			group = apipub.GroupResponse{
////				Groupid:            groupID,
////				Productid:          productID,
////				Members:            &members2,
////				MaxMembers:         100,
////				MembershipRequests: &requests,
////				CanCrossPlay:       aws.Bool(true),
////			}
////
////			invitation3 := apipub.MembershipRequest{
////				Groupid:      groupID,
////				Productid:    &productID,
////				Approverid:   leader.Userid,
////				Memberid:     userID3,
////				Status:       apipub.Invited,
////				CanCrossPlay: aws.Bool(true),
////			}
////
////			//Send an invitation - happy path
////			jsonbytes, _ := json.Marshal(invitation3)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
////			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(false)
////			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
////			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
////			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
////			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
////			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
////			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
////			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
////			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
////
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusCreated)
////		})
////
////		g.It("should successfully create joinrequest", func() {
////			//Join requet uses a different user ID so it should pass that point.
////			//Send a join request - happy path
////			joinRequest3 := apipub.MembershipRequest{
////				Groupid:      groupID,
////				Productid:    &productID,
////				Approverid:   &leaderUserID,
////				Status:       apipub.Requested,
////				CanCrossPlay: aws.Bool(true),
////			}
////
////			w, r := Login(User4JWT)
////			jsonbytes, _ := json.Marshal(joinRequest3)
////			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
////			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
////			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
////			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
////			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
////			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
////			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
////			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
////			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
////			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
////			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
////			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
////
////			mock.api.SendMembershipRequestForGroup(w, r, groupID)
////			g.Assert(w.Code).Equal(http.StatusCreated)
////		})
////	})
////}
//
//func TestFirstPartyInviteJoinRequest(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateNewULID()
//	userID := utils.GenerateRandomDNAID()
//	approverID := utils.GenerateRandomDNAID()
//
//	invitation := apipub.MembershipRequest{
//		Groupid:            groupID,
//		Productid:          &productID,
//		Approverid:         approverID,
//		Memberid:           userID,
//		Status:             apipub.Invited,
//		CanCrossPlay:       aws.Bool(true),
//		IsFirstPartyInvite: aws.Bool(true),
//	}
//
//	var requests = []apipub.MembershipRequest{}
//	requests = append(requests, invitation)
//
//	leader := apipub.GroupMemberResponse{
//		Userid: approverID,
//		Role:   apipub.Leader,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, leader)
//
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	joinRequest := apipub.MembershipRequest{
//		Groupid:            groupID,
//		Productid:          &productID,
//		Approverid:         approverID,
//		Memberid:           userID,
//		Status:             apipub.Requested,
//		CanCrossPlay:       aws.Bool(true),
//		IsFirstPartyInvite: aws.Bool(true),
//	}
//
//	g.Describe("FirstPartyInviteJoinRequest", func() {
//		RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//		g.It("should successfully join group", func() {
//			g.Timeout(10 * time.Second)
//			jsonbytes, _ := json.Marshal(joinRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), aws.Bool(true)).Return(&invitation, nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			w := httptest.NewRecorder()
//
//			mock.api.sendinv(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestFirstPartyInviteLookupUser(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateNewULID()
//	userID := "b287e655461f4b3085c8f244e394ff7e"
//	memberID := utils.GenerateRandomDNAID()
//
//	invitation := apipub.MembershipRequest{
//		Groupid:            groupID,
//		Productid:          &productID,
//		Approverid:         userID,
//		Memberid:           memberID,
//		Status:             apipub.Invited,
//		CanCrossPlay:       aws.Bool(true),
//		IsFirstPartyInvite: aws.Bool(true),
//	}
//
//	var requests = []apipub.MembershipRequest{}
//
//	leader := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Leader,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, leader)
//
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	g.Describe("FirstPartyInvite", func() {
//		RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//		g.It("should successfully send invite", func() {
//			g.Timeout(1000 * time.Second)
//			jsonbytes, _ := json.Marshal(invitation)
//
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetFirstPartyLookup(gomock.Any(), memberID, 24).Return(aws.String(memberID), nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//
//			mock.api.SendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//	})
//}
//
//func TestModifyMembershipForGroupAndMember(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := "4029a6ffe9924f969955aa2e1c0782aa"
//	tokenUserID := "b287e655461f4b3085c8f244e394ff7e"
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//	approverID := utils.GenerateRandomDNAID()
//
//	invitation := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID,
//		Approverid: approverID,
//		Status:     apipub.Revoked,
//	}
//
//	joinRequest := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID2,
//		Approverid: tokenUserID,
//		Status:     apipub.Approved,
//	}
//
//	invited := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID,
//		Approverid: approverID,
//		Status:     apipub.Invited,
//	}
//
//	requested := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID2,
//		Approverid: approverID,
//		Status:     apipub.Requested,
//	}
//
//	var requests = []apipub.MembershipRequest{}
//	requests = append(requests, invited)
//	requests = append(requests, requested)
//
//	member := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Member,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, member)
//
//	//Member is in group
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//	}
//
//	invitation2 := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID,
//		Approverid: approverID,
//		Status:     apipub.Accepted,
//	}
//
//	joinRequest2 := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID2,
//		Approverid: approverID,
//		Status:     apipub.Approved,
//	}
//
//	member2 := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Member,
//	}
//	members2 := append(members, member2)
//
//	var requests2 = []apipub.MembershipRequest{}
//	requests2 = append(requests2, invitation2)
//	requests2 = append(requests2, joinRequest2)
//
//	group3 := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members2,
//		MaxMembers:         100,
//		MembershipRequests: &requests2,
//	}
//
//	g.Describe("ModifyMembershipForGroupAndMember", func() {
//		g.It("should get bad request when group returns error", func() {
//			//get group returns error.  Server returns 500 server error..
//			jsonbytes, _ := json.Marshal(invitation)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusUnprocessableEntity, errs.EGroupsGroupMemberNil))
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*invitation.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get not found when group returns nil", func() {
//			//get group returns nil, nil.  Server returns 404 not found.
//			jsonbytes, _ := json.Marshal(invitation)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*invitation.Memberid))
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("should get bad request when sending chat message to group", func() {
//			g.Timeout(10 * time.Second)
//
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID2,
//				Status:     apipub.Requested,
//				Approverid: approverID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//			//Err sending chat message - Join
//			jsonbytes, _ := json.Marshal(joinRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group3, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			// mock.Ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			// mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil).AnyTimes()
//			// mock.Ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any()).AnyTimes()
//			// mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), &group3, gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(errs.New(http.StatusUnprocessableEntity, errs.EGroupsGroupMemberNil))
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should succeed", func() {
//			g.Timeout(10 * time.Second)
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID2,
//				Status:     apipub.Requested,
//				Approverid: tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//			jsonbytes, _ := json.Marshal(joinRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group3, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: *joinRequest.Memberid}, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//
//	g.Describe("ModifyMembershipForGroupAndMember Revoke Invite", func() {
//		g.It("should get bad request when nil approverid", func() {
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//			revokeReq := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Revoked,
//				Approverid: "",
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(revokeReq)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get bad request when empty approverid", func() {
//			emptyId := ""
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//			revokeReq := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Revoked,
//				Approverid: emptyId,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(revokeReq)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get bad request when invalid user revokes invite", func() {
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//			revokeReq := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Revoked,
//				Approverid: approverID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(revokeReq)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get bad request if leader tries to revoke invite from another approverid", func() {
//			leader := apipub.GroupMemberResponse{
//				Userid: tokenUserID,
//				Role:   apipub.Leader,
//			}
//
//			var members = []apipub.GroupMemberResponse{}
//			members = append(members, leader)
//
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//			revokeReq := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Revoked,
//				Approverid: approverID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(revokeReq)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get bad request if approverid does not match", func() {
//			leader := apipub.GroupMemberResponse{
//				Userid: tokenUserID,
//				Role:   apipub.Leader,
//			}
//
//			var members = []apipub.GroupMemberResponse{}
//			members = append(members, leader)
//
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//			revokeReq := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Revoked,
//				Approverid: tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//			deleteErr := errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
//
//			jsonbytes, _ := json.Marshal(revokeReq)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any()).Return(deleteErr)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get ok when inviter revokes invite", func() {
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: tokenUserID,
//			}
//			revokeReq := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Revoked,
//				Approverid: tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(revokeReq)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//
//	//and fin
//
//}
//
//func TestMembershipInvite(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateRandomDNAID()
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//	userID3 := utils.GenerateRandomDNAID()
//	leaderUserID := utils.GenerateRandomDNAID()
//
//	joinRequest := apipub.MembershipRequest{
//		Groupid:      groupID,
//		Productid:    &productID,
//		Status:       apipub.Requested,
//		Approverid:   userID2,
//		CanCrossPlay: aws.Bool(true),
//	}
//
//	invitation := apipub.MembershipRequest{
//		Groupid:      groupID,
//		Productid:    &productID,
//		Status:       apipub.Invited,
//		CanCrossPlay: aws.Bool(true),
//		Memberid:     userID3,
//	}
//
//	var requests = []apipub.MembershipRequest{}
//	requests = append(requests, invitation)
//	requests = append(requests, joinRequest)
//
//	member := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Member,
//	}
//
//	leader := apipub.GroupMemberResponse{
//		Userid: leaderUserID,
//		Role:   apipub.Leader,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, member, leader)
//
//	//Member is in group
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	g.Describe("MembershipInvite", func() {
//		g.It("should return bad request with db error", func() {
//
//			//Get group returns error.  Server returns 500 server error..
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
//			w, r := Login(User1JWT)
//			jsonbytes, _ := json.Marshal(invitation)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should return not found", func() {
//
//			//Get group returns nil, nil.  Server returns 404 not found.
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, r := Login(User1JWT)
//			jsonbytes, _ := json.Marshal(invitation)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("empty memberid on invitation error", func() {
//
//			//For Invitation, same userID was added to both the group member and the invitation.
//			//This will return err 400.
//
//			member3 := apipub.GroupMemberResponse{
//				Userid: "b287e655461f4b3085c8f244e394ff7e",
//				Role:   apipub.Member,
//			}
//			var newMembers = []apipub.GroupMemberResponse{}
//			newMembers = append(newMembers, leader, member3)
//			group.Members = &newMembers
//			w, r := Login(User1JWT)
//			jsonbytes, _ := json.Marshal(invitation)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get bad request for invitation, logged in user cannot invite", func() {
//
//			//For Invitation, same userID was added to both the group member and the invitation.
//			//This will return err 400.
//
//			member3 := apipub.GroupMemberResponse{
//				Userid: "b287e655461f4b3085c8f244e394ff7e",
//				Role:   apipub.Member,
//			}
//			invitation2 := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Status:       apipub.Invited,
//				CanCrossPlay: aws.Bool(true),
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//			}
//			var newMembers = []apipub.GroupMemberResponse{}
//			newMembers = append(newMembers, leader, member3)
//			group.Members = &newMembers
//			w, r := Login(User1JWT)
//			jsonbytes, _ := json.Marshal(invitation2)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("invite exists", func() {
//
//			w, r := Login(User1JWT)
//			token, tErr := authheader.ParseJWTFromRequest(r)
//			if tErr != nil {
//				errs.Return(w, r, tErr)
//				return
//			}
//			loggedInUser := token.Claims.Subject
//			leader.Userid = loggedInUser
//
//			members2 := []apipub.GroupMemberResponse{}
//			members2 = append(members2, leader)
//			requests = []apipub.MembershipRequest{}
//
//			//Member is in group
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members2,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invitation2 := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Approverid:   leader.Userid,
//				Status:       apipub.Invited,
//				CanCrossPlay: aws.Bool(true),
//				Memberid:     userID3,
//			}
//
//			jsonbytes, _ := json.Marshal(invitation2)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(true)
//
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//
//		g.It("should get bad request for final send request call", func() {
//
//			w, r := Login(User1JWT)
//			token, tErr := authheader.ParseJWTFromRequest(r)
//			if tErr != nil {
//				errs.Return(w, r, tErr)
//				return
//			}
//			loggedInUser := token.Claims.Subject
//			leader.Userid = loggedInUser
//
//			members2 := []apipub.GroupMemberResponse{}
//			members2 = append(members2, leader)
//			requests = []apipub.MembershipRequest{}
//
//			//Member is in group
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members2,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invitation2 := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Approverid:   leader.Userid,
//				Status:       apipub.Invited,
//				CanCrossPlay: aws.Bool(true),
//				Memberid:     userID3,
//			}
//
//			jsonbytes, _ := json.Marshal(invitation2)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
//
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should successfully create invitation", func() {
//
//			w, r := Login(User1JWT)
//			token, tErr := authheader.ParseJWTFromRequest(r)
//			if tErr != nil {
//				errs.Return(w, r, tErr)
//				return
//			}
//			loggedInUser := token.Claims.Subject
//			leader.Userid = loggedInUser
//
//			members2 := []apipub.GroupMemberResponse{}
//			members2 = append(members2, leader)
//			requests = []apipub.MembershipRequest{}
//
//			//Member is in group
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members2,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invitation3 := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Approverid:   leader.Userid,
//				Memberid:     userID3,
//				Status:       apipub.Invited,
//				CanCrossPlay: aws.Bool(true),
//			}
//
//			//Send an invitation - happy path
//			jsonbytes, _ := json.Marshal(invitation3)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//	})
//}
//
//func TestFirstPartyMembershipInvite(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateNewULID()
//	userID := "b287e655461f4b3085c8f244e394ff7e"
//	memberID := utils.GenerateRandomDNAID()
//
//	invitation := apipub.MembershipRequest{
//		Groupid:            groupID,
//		Productid:          &productID,
//		Approverid:         userID,
//		Memberid:           memberID,
//		Status:             apipub.Invited,
//		CanCrossPlay:       aws.Bool(true),
//		IsFirstPartyInvite: aws.Bool(true),
//	}
//
//	var requests = []apipub.MembershipRequest{}
//
//	leader := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Leader,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, leader)
//
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	g.Describe("FirstPartyInviteJoinRequest", func() {
//		RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//		g.It("should successfully send invite", func() {
//			g.Timeout(1000 * time.Second)
//			jsonbytes, _ := json.Marshal(invitation)
//
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetFirstPartyLookup(gomock.Any(), memberID, 24).Return(aws.String(memberID), nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			w := httptest.NewRecorder()
//
//			mock.api.SendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//	})
//}
//
//func TestRevokeInvite(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := "4029a6ffe9924f969955aa2e1c0782aa"
//	tokenUserID := "b287e655461f4b3085c8f244e394ff7e"
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//	approverID := utils.GenerateRandomDNAID()
//
//	joinRequest := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID2,
//		Approverid: tokenUserID,
//		Status:     apipub.Approved,
//	}
//
//	invited := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID,
//		Approverid: approverID,
//		Status:     apipub.Invited,
//	}
//
//	requested := apipub.MembershipRequest{
//		Groupid:    groupID,
//		Productid:  &productID,
//		Memberid:   userID2,
//		Approverid: approverID,
//		Status:     apipub.Requested,
//	}
//
//	var requests = []apipub.MembershipRequest{}
//	requests = append(requests, invited)
//	requests = append(requests, requested)
//
//	member := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Member,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, member)
//
//	//Member is in group
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//	}
//
//	err := errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
//
//	g.Describe("RevokeInvite", func() {
//		g.It("should get bad request when invalid user revokes invite", func() {
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			r.Body = http.NoBody
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any()).Return(err)
//
//			w := httptest.NewRecorder()
//			mock.api.RevokeInvite(w, r, groupID, *joinRequest.Memberid)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get bad request if leader tries to revoke invite from another approverid", func() {
//			leader := apipub.GroupMemberResponse{
//				Userid: tokenUserID,
//				Role:   apipub.Leader,
//			}
//
//			var members = []apipub.GroupMemberResponse{}
//			members = append(members, leader)
//
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: approverID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			r.Body = http.NoBody
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any()).Return(err)
//			w := httptest.NewRecorder()
//			mock.api.RevokeInvite(w, r, groupID, *joinRequest.Memberid)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get ok when inviter revokes invite", func() {
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			r.Body = http.NoBody
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			w := httptest.NewRecorder()
//			mock.api.RevokeInvite(w, r, groupID, *joinRequest.Memberid)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//
//		g.It("should get ok when inviter revokes invite and isfirstparty is wrong", func() {
//			requested = apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   userID,
//				Status:     apipub.Invited,
//				Approverid: tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, requested)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//			}
//
//			r.Body = http.NoBody
//			err2 := errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidIsFirstParty)
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any()).Return(err2)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			w := httptest.NewRecorder()
//			mock.api.RevokeInvite(w, r, groupID, apipub.Memberid(*joinRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestMembershipRequestJoin(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateRandomDNAID()
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//	userID3 := utils.GenerateRandomDNAID()
//	leaderUserID := utils.GenerateRandomDNAID()
//
//	joinRequest := apipub.MembershipRequest{
//		Groupid:      groupID,
//		Productid:    &productID,
//		Status:       apipub.Requested,
//		Approverid:   userID2,
//		CanCrossPlay: aws.Bool(true),
//	}
//
//	invitation := apipub.MembershipRequest{
//		Groupid:      groupID,
//		Productid:    &productID,
//		Status:       apipub.Invited,
//		CanCrossPlay: aws.Bool(true),
//		Memberid:     userID3,
//	}
//
//	var requests = []apipub.MembershipRequest{}
//	requests = append(requests, invitation)
//	requests = append(requests, joinRequest)
//
//	member := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Member,
//	}
//
//	leader := apipub.GroupMemberResponse{
//		Userid: leaderUserID,
//		Role:   apipub.Leader,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, member, leader)
//
//	// Member is in group
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	g.Describe("MembershipRequestJoin", func() {
//		g.It("should return bad request with db error", func() {
//
//			//Get group returns error.  Server returns 500 server error..
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
//			w, r := Login(User1JWT)
//			jsonbytes, _ := json.Marshal(joinRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should return not found", func() {
//
//			//Get group returns nil, nil.  Server returns 404 not found.
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, r := Login(User1JWT)
//			jsonbytes, _ := json.Marshal(joinRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.api.MembershipInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("should successfully create joinrequest", func() {
//			//Join requet uses a different user ID so it should pass that point.
//			//Send a join request - happy path
//			joinRequest3 := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Approverid:   &leaderUserID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//			}
//
//			w, r := Login(User4JWT)
//			jsonbytes, _ := json.Marshal(joinRequest3)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			mock.api.MembershipRequestJoin(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//	})
//}
//
//func TestMembershipRequestApprove(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := "4029a6ffe9924f969955aa2e1c0782aa"
//	tokenUserID := "b287e655461f4b3085c8f244e394ff7e"
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//
//	member := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Leader,
//	}
//	member2 := apipub.GroupMemberResponse{
//		Userid: userID2,
//		Role:   apipub.Member,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, member, member2)
//
//	group := apipub.GroupResponse{
//		Groupid:           groupID,
//		Productid:         productID,
//		Members:           &members,
//		MaxMembers:        100,
//		CanMembersInvite:  aws.Bool(false),
//		JoinRequestAction: apipub.Manual,
//		CanCrossPlay:      aws.Bool(true),
//	}
//
//	tokenLeader := apipub.GroupMemberResponse{
//		Userid: tokenUserID,
//		Role:   apipub.Leader,
//	}
//
//	var membersToken = []apipub.GroupMemberResponse{}
//	membersToken = append(membersToken, tokenLeader)
//
//	groupToken := apipub.GroupResponse{
//		Groupid:           groupID,
//		Productid:         productID,
//		Members:           &membersToken,
//		MaxMembers:        100,
//		CanMembersInvite:  aws.Bool(false),
//		JoinRequestAction: apipub.Manual,
//		CanCrossPlay:      aws.Bool(true),
//	}
//
//	joinApprove := apipub.ApproveJoinRequestBody{}
//
//	jsonbytes, _ := json.Marshal(joinApprove)
//
//	g.Describe("JoinRequestApprove - Manual Group CanMemberInvite false", func() {
//		g.It("should get bad request - incorrect approverid", func() {
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//
//			w := httptest.NewRecorder()
//			mock.api.ApproveJoinRequest(w, r, groupID, apipub.Memberid(userID))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should succeed - correct approverid", func() {
//			g.Timeout(10 * time.Second)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&groupToken, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&groupToken, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.JoinRequestApprove(w, r, groupID, apipub.Memberid(userID))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//
//	g.Describe("JoinRequestReject - Manual Group CanMemberInvite false", func() {
//		g.It("should get bad request - incorrect approverid", func() {
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//
//			w := httptest.NewRecorder()
//			mock.api.JoinRequestReject(w, r, groupID, apipub.Memberid(userID))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should succeed - correct approverid", func() {
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&groupToken, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//
//			w := httptest.NewRecorder()
//			mock.api.JoinRequestReject(w, r, groupID, apipub.Memberid(userID))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestMemberRequests(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	productID := "4029a6ffe9924f969955aa2e1c0782aa"
//	tokenUserID := "b287e655461f4b3085c8f244e394ff7e"
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//
//	var requests = []apipub.MembershipRequest{}
//
//	member := apipub.GroupMemberResponse{
//		Userid: userID,
//		Role:   apipub.Leader,
//	}
//	member2 := apipub.GroupMemberResponse{
//		Userid: userID2,
//		Role:   apipub.Member,
//	}
//	memberTokenIDLeader := apipub.GroupMemberResponse{
//		Userid: tokenUserID,
//		Role:   apipub.Leader,
//	}
//	memberTokenID := apipub.GroupMemberResponse{
//		Userid: tokenUserID,
//		Role:   apipub.Member,
//	}
//
//	var members = []apipub.GroupMemberResponse{}
//	members = append(members, member, member2)
//
//	group := apipub.GroupResponse{
//		Groupid:           groupID,
//		Productid:         productID,
//		Members:           &members,
//		MaxMembers:        100,
//		CanMembersInvite:  aws.Bool(false),
//		JoinRequestAction: apipub.Manual,
//		CanCrossPlay:      aws.Bool(true),
//	}
//
//	g.Describe("JoinRequests - Manual Group CanMemberInvite false", func() {
//		g.It("should get forbidden - approverid is member", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:    groupID,
//				Productid:  &productID,
//				Memberid:   &tokenUserID,
//				Status:     apipub.Requested,
//				Approverid: &userID2,
//			}
//
//			jsonbytes, _ := json.Marshal(memberRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.SendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusForbidden)
//		})
//
//		g.It("should succeed - approverid is leader", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     tokenUserID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   userID,
//			}
//
//			jsonbytes, _ := json.Marshal(memberRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), tokenUserID, productID, groupID, userID).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.SendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//
//		g.It("should get bad request if member tries to approve", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID2,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, memberRequest)
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member, memberTokenID)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(false),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			approveRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID2,
//				Status:       apipub.Approved,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			jsonbytes, _ := json.Marshal(approveRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*approveRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get ok if leader tries to approve", func() {
//			g.Timeout(10 * time.Second)
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, memberRequest)
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member2, memberTokenIDLeader)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(false),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			approveRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID,
//				Status:       apipub.Approved,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			jsonbytes, _ := json.Marshal(approveRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*approveRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//
//	g.Describe("JoinRequests - Manual Group CanMemberInvite true", func() {
//		g.It("should succeed - approverid is leader", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     tokenUserID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   userID,
//			}
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member, member2)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(true),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(memberRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), tokenUserID, productID, groupID, userID).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.SendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//
//		g.It("should get bad request if member tries to approve request for leader", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID2,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   userID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, memberRequest)
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member, memberTokenID)
//
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(true),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			approveRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID2,
//				Status:       apipub.Approved,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			jsonbytes, _ := json.Marshal(approveRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			deleteErr := errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any()).Return(deleteErr)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*approveRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get ok if leader tries to approve request for leader", func() {
//			g.Timeout(10 * time.Second)
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, memberRequest)
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member2, memberTokenIDLeader)
//
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(true),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			approveRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID,
//				Status:       apipub.Approved,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			jsonbytes, _ := json.Marshal(approveRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*approveRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//
//		g.It("should succeed - approverid is member", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     tokenUserID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   userID2,
//			}
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member, member2)
//
//			group = apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(true),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			jsonbytes, _ := json.Marshal(memberRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), tokenUserID, productID, groupID, userID2).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID2}, nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().AddMembershipRequestToGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.SendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//
//		g.It("should get bad request if leader tries to approve request for member", func() {
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   userID2,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, memberRequest)
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member2, memberTokenIDLeader)
//
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(true),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			approveRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID,
//				Status:       apipub.Approved,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			jsonbytes, _ := json.Marshal(approveRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			deleteErr := errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidIsFirstParty)
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any()).Return(deleteErr)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*approveRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should get ok if member tries to approve request for member", func() {
//			g.Timeout(10 * time.Second)
//			memberRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID2,
//				Status:       apipub.Requested,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			requests = append([]apipub.MembershipRequest{}, memberRequest)
//
//			members = []apipub.GroupMemberResponse{}
//			members = append(members, member, memberTokenID)
//
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members,
//				MaxMembers:         100,
//				CanMembersInvite:   aws.Bool(true),
//				JoinRequestAction:  apipub.Manual,
//				CanCrossPlay:       aws.Bool(true),
//				MembershipRequests: &requests,
//			}
//
//			approveRequest := apipub.MembershipRequest{
//				Groupid:      groupID,
//				Productid:    &productID,
//				Memberid:     userID2,
//				Status:       apipub.Approved,
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   tokenUserID,
//			}
//
//			jsonbytes, _ := json.Marshal(approveRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), productID, groupID, gomock.Any())
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), productID, groupID).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: userID}, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			w := httptest.NewRecorder()
//			mock.api.ModifyMembershipForGroupAndMember(w, r, groupID, apipub.Memberid(*approveRequest.Memberid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestAcceptInvite(t *testing.T) {
//
//	g := goblin.Goblin(t)
//
//	mock := NewMockAPI(t)
//	defer mock.ctrl.Finish()
//	_, r := Login(User1JWT)
//
//	groupID := utils.GenerateNewULID()
//	approverID := utils.GenerateNewULID()
//
//	members := []apipub.GroupMemberResponse{
//		{
//			Userid: "member",
//			Role:   "member",
//		},
//	}
//
//	membershipRequests := []apipub.MembershipRequest{
//		{
//			Groupid:    groupID,
//			Status:     apipub.Invited,
//			Memberid:   "b287e655461f4b3085c8f244e394ff7e",
//			Approverid: approverID,
//		},
//	}
//
//	group := apipub.GroupResponse{
//		Groupid:            groupID,
//		Members:            &members,
//		MembershipRequests: &membershipRequests,
//		MaxMembers:         100,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	invite := apipub.MembershipRequest{
//		Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//		Status:       "invited",
//		Approverid:   approverID,
//		CanCrossPlay: aws.Bool(true),
//		Groupid:      group.Groupid,
//	}
//
//	g.Describe("AcceptInvite", func() {
//		g.It("FAIL - group not found", func() {
//			reqBody := apipub.MembershipRequest{
//				Memberid:   "b287e655461f4b3085c8f244e394ff7e",
//				Approverid: approverID,
//				Status:     "joined",
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(false)
//			w := httptest.NewRecorder()
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("FAIL - failed to get group", func() {
//			reqBody := apipub.MembershipRequest{
//				Memberid:   "b287e655461f4b3085c8f244e394ff7e",
//				Approverid: approverID,
//				Status:     "joined",
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&invite, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
//
//			w := httptest.NewRecorder()
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("FAIL - group size maxed", func() {
//			groupID := utils.GenerateNewULID()
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Members:            &members,
//				MembershipRequests: &membershipRequests,
//				MaxMembers:         100,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			group.MaxMembers = 1
//
//			reqBody := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "joined",
//				Approverid:   approverID,
//				CanCrossPlay: aws.Bool(true),
//			}
//
//			invite := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "invited",
//				Approverid:   approverID,
//				CanCrossPlay: aws.Bool(true),
//				Groupid:      group.Groupid,
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&invite, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			w := httptest.NewRecorder()
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusForbidden)
//			group.MaxMembers = 100
//		})
//
//		g.It("FAIL - user does not have invite", func() {
//			groupID := utils.GenerateNewULID()
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Members:            &members,
//				MembershipRequests: &membershipRequests,
//				MaxMembers:         100,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			group.MembershipRequests = &[]apipub.MembershipRequest{}
//
//			reqBody := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "joined",
//				Approverid:   approverID,
//				CanCrossPlay: aws.Bool(true),
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//
//			w := httptest.NewRecorder()
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("FAIL - crossplay mismatch", func() {
//			groupID := utils.GenerateNewULID()
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Members:            &members,
//				MembershipRequests: &membershipRequests,
//				MaxMembers:         100,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			reqBody := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "joined",
//				Approverid:   approverID,
//				CanCrossPlay: aws.Bool(false),
//			}
//
//			invite := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "invited",
//				Approverid:   approverID,
//				CanCrossPlay: aws.Bool(true),
//				Groupid:      group.Groupid,
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&invite, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			// mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfileResponse{Userid: *invite.Memberid}, nil)
//			// mock.Ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any()).AnyTimes()
//			// mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
//
//			w := httptest.NewRecorder()
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusForbidden)
//		})
//
//		g.It("OK - user accept the invite", func() {
//			g.Timeout(10 * time.Second)
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Members:            &members,
//				MembershipRequests: &membershipRequests,
//				MaxMembers:         100,
//				CanCrossPlay:       aws.Bool(true),
//			}
//			membershipRequests := []apipub.MembershipRequest{
//				{
//					Groupid:    groupID,
//					Status:     apipub.Invited,
//					Memberid:   "b287e655461f4b3085c8f244e394ff7e",
//					Approverid: approverID,
//				},
//			}
//			group.MembershipRequests = &membershipRequests
//
//			reqBody := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "joined",
//				CanCrossPlay: aws.Bool(true),
//				Approverid:   approverID,
//			}
//
//			invite := apipub.MembershipRequest{
//				Memberid:     "b287e655461f4b3085c8f244e394ff7e",
//				Status:       "invited",
//				Approverid:   approverID,
//				CanCrossPlay: aws.Bool(true),
//				Groupid:      group.Groupid,
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&invite, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any())
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//
//			w := httptest.NewRecorder()
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//
//		g.It("FAIL - first party join can't search dna", func() {
//			RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//
//			w := httptest.NewRecorder()
//
//			reqBody := apipub.MembershipRequest{
//				Memberid:           "12345",
//				Approverid:         "54321",
//				Status:             "joined",
//				OnlineServiceType:  (*apipub.OnlineServiceType)(aws.Int(3)),
//				IsFirstPartyInvite: aws.Bool(true),
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invite := apipub.MembershipRequest{
//				Memberid:           "12345",
//				Status:             "invited",
//				Approverid:         approverID,
//				IsFirstPartyInvite: aws.Bool(true),
//				CanCrossPlay:       aws.Bool(true),
//				Groupid:            group.Groupid,
//			}
//
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&invite, nil)
//			mock.id.EXPECT().SearchAccounts(gomock.Any(), gomock.Any()).Return(nil, errors.New("dna error"))
//
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusForbidden)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsMembershipNotOwned).Error()))
//		})
//
//		g.It("FAIL - first party join user does not own the first party id", func() {
//			reqBody := apipub.MembershipRequest{
//				Memberid:           "12345",
//				Approverid:         "54321",
//				Status:             "joined",
//				IsFirstPartyInvite: aws.Bool(true),
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invite := apipub.MembershipRequest{
//				Memberid:           "12345",
//				Status:             "invited",
//				Approverid:         approverID,
//				IsFirstPartyInvite: aws.Bool(true),
//				CanCrossPlay:       aws.Bool(true),
//				Groupid:            group.Groupid,
//			}
//			w := httptest.NewRecorder()
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			accounts := apipub.SearchAccountResponse{
//				{
//					// not 12345
//					FirstPartyId:      aws.String("123456"),
//					OnlineServiceType: aws.Int(3),
//				},
//			}
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&invite, nil)
//			mock.id.EXPECT().SearchAccounts(gomock.Any(), gomock.Any()).Return(&accounts, nil)
//
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusForbidden)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsMembershipNotOwned).Error()))
//		})
//
//		g.It("OK - first party user join the group", func() {
//			g.Timeout(10 * time.Second)
//
//			groupID := utils.GenerateNewULID()
//			group := apipub.GroupResponse{
//				Groupid:            groupID,
//				Members:            &members,
//				MembershipRequests: &membershipRequests,
//				MaxMembers:         100,
//				CanCrossPlay:       aws.Bool(true),
//			}
//			reqBody := apipub.MembershipRequest{
//				Memberid:           "12345",
//				Approverid:         "54321",
//				Status:             "joined",
//				CanCrossPlay:       aws.Bool(true),
//				OnlineServiceType:  (*apipub.OnlineServiceType)(aws.Int(3)),
//				IsFirstPartyInvite: aws.Bool(true),
//			}
//
//			invite := apipub.MembershipRequest{
//				Memberid:           "12345",
//				Status:             "invited",
//				Approverid:         approverID,
//				CanCrossPlay:       aws.Bool(true),
//				Groupid:            group.Groupid,
//				IsFirstPartyInvite: aws.Bool(true),
//			}
//
//			w := httptest.NewRecorder()
//			jsonbytes, _ := json.Marshal(reqBody)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//
//			ost := apipub.OnlineServiceTypeSTEAM
//
//			accounts := apipub.SearchAccountResponseList{
//				{
//					FirstPartyId:      aws.String("12345"),
//					OnlineServiceType: &ost,
//					Id:                aws.String("b287e655461f4b3085c8f244e394ff7e"),
//				},
//			}
//
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().CachedObjExists(gomock.Any(), gomock.Any()).Return(true)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), aws.Bool(true)).Return(&invite, nil)
//			mock.id.EXPECT().SearchAccounts(gomock.Any(), gomock.Any()).Return(&accounts, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//
//			mock.api.AcceptInvite(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
