package api

import (
	"context"
	"errors"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

func (api *SocialPublicAPI) GetInvites(w http.ResponseWriter, r *http.Request, params apipub.GetInvitesParams) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	limit := 100
	limit64 := int64(limit)
	if api.Cfg != nil && api.Cfg.ListFriendsLimit >= 1 {
		limit = api.Cfg.ListFriendsLimit
		limit64 = int64(limit)
	}

	if params.Limit != nil && *params.Limit >= 1 && *params.Limit <= 100 {
		limit = *params.Limit
		limit64 = int64(limit)
	}

	userid := token.Claims.Subject
	nextVal := ""
	if params.Next != nil {
		nextVal = *params.Next
	}

	var ctx context.Context
	if r == nil || r.Context() == nil {
		ctx = context.Background()
	} else {
		ctx = r.Context()
	}

	membershipRequests, next, err := api.Cache.GetInvitesForUser(ctx, userid, token.Claims.ProductID, &limit64, &nextVal)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get invites for user")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
		return
	}

	var response apipub.InvitesNext
	response.Items = []apipub.GetInviteResponse{}
	if membershipRequests != nil {
		for _, membershipRequest := range *membershipRequests {
			ost := apipub.OnlineServiceTypeUNKNOWN
			if membershipRequest.OnlineServiceType != nil {
				ost = *membershipRequest.OnlineServiceType
			}
			fromDisplayName := ""
			if membershipRequest.FromDisplayName != nil {
				fromDisplayName = *membershipRequest.FromDisplayName
			}
			invite := apipub.GetInviteResponse{
				CanCrossPlay:       membershipRequest.CanCrossPlay,
				Approverid:         membershipRequest.Approverid,
				DisplayName:        fromDisplayName,
				FirstPartyid:       membershipRequest.FirstPartyid,
				Groupid:            membershipRequest.Groupid,
				IsFirstPartyInvite: membershipRequest.IsFirstPartyInvite,
				Memberid:           membershipRequest.Memberid,
				OnlineServiceType:  ost,
			}
			response.Items = append(response.Items, invite)
		}
		if next != "" {
			response.Nextid = &next
		}
		ReturnOK(w, r, response)
		return
	}
	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) SendInviteWithHttpReturn(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, productid, appid string, ost apipub.OnlineServiceType, memberid, approverid string, bHttpReturnOk, isFirstPartyInvite bool, expiresIn int, teleMeta *apipub.TelemetryMetaData) *errs.Error {
	log := logger.FromContext(r.Context())
	groupid := g

	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to get group")
		return errs.ToError(err)

	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("no group found")
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)

	}
	if group.GetMemberRole(approverid) == apipub.Nonmember {
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
	}
	//Check if logged in user can invite.
	if !group.CanMemberInvite(approverid) {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Str("approverid", approverid).Msg("user is not leader")

		return errs.New(http.StatusForbidden, errs.EGroupsOnlyLeaderCanSendInvites)

	}

	if group.Members != nil && len(*group.Members) >= group.MaxMembers {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Int("maxMembers", group.MaxMembers).Msg("group is at max members")
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsFull)
	}

	// set first party Id here if it's a first party invite because we overwrite the memberid with the userid for join requests
	// for security reasons below.
	var membershipRequest apipub.MembershipRequest
	fullAccountid := memberid
	if isFirstPartyInvite {
		membershipRequest.FirstPartyid = &memberid
		//Try to get the 2K parent account for MQTT invite topic if isFirstPartyInvite

		fullAccountid = api.GetParentAccountId(r.Context(), memberid, ost, &isFirstPartyInvite)
		msg := "parent account lookup"
		log.Debug().Str("firstpartyid", memberid).Str("fullAccountId", fullAccountid).Str("event", msg).Msg(msg)
	}

	ttl := apipub.Ttl(expiresIn)

	membershipRequest.Groupid = groupid
	membershipRequest.Approverid = approverid
	membershipRequest.Status = apipub.Invited
	membershipRequest.Memberid = fullAccountid
	membershipRequest.Productid = &productid
	membershipRequest.OnlineServiceType = &ost
	membershipRequest.IsFirstPartyInvite = &isFirstPartyInvite
	membershipRequest.Ttl = &ttl
	membershipRequest.TeleMeta = teleMeta

	// process telemeta if exists for telemetry
	additionalInfo := make(map[string]string)
	if membershipRequest.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*membershipRequest.TeleMeta, &additionalInfo)
	}

	if membershipRequest.CanCrossPlay == nil {
		membershipRequest.CanCrossPlay = aws.Bool(true)
	}

	//if invite for this approver/group/member combo exists already, just return
	if api.Cache.InviteExistsInCache(r.Context(), membershipRequest.Memberid, *membershipRequest.Productid, membershipRequest.Groupid, membershipRequest.Approverid, membershipRequest.IsFirstPartyInvite) {
		if bHttpReturnOk {
			ReturnEmptyOK(w, r)
		}
		return nil
	}

	//For invites, check if person being invited has blocked current user and eat request if so
	isInviterBlocked, _ := api.doesBlockerBlockBlockee(r.Context(), membershipRequest.Memberid, approverid)
	if isInviterBlocked {
		if bHttpReturnOk {
			ReturnEmptyOK(w, r)
		}
		return nil
	}

	//If member is already in the group, then just return okay since that's the point of the invite anyway
	if group.IsMember(membershipRequest.Memberid) {
		if bHttpReturnOk {
			ReturnEmptyOK(w, r)
		}
		return nil
	}
	//Check if an submitted status already exists and ignore if it does.
	requests := group.GetMemberships(membershipRequest.Memberid, []string{string(membershipRequest.Status)})
	if requests != nil {
		for _, memshp := range *requests {
			//If one exists. just return ok because the invite you're trying to create already exists and is not expired.
			if memshp.Status == membershipRequest.Status && memshp.Approverid == membershipRequest.Approverid && memshp.IsFirstPartyInvite == membershipRequest.IsFirstPartyInvite {
				if bHttpReturnOk {
					membershipRequest.TeleMeta = nil
					ReturnOK(w, r, membershipRequest)
				}
				return nil
			}
		}
	}

	// If a join request or invite and group's max members reached, return err
	if group.Members != nil && len(*group.Members) >= group.MaxMembers {
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsFull)
	}

	// get user profile for inviter in user to get display name.
	profile, _ := api.getUserProfile(r.Context(), membershipRequest.Approverid, true)
	if profile != nil && profile.DisplayName != nil {
		membershipRequest.FromDisplayName = profile.DisplayName
	}

	// adds invites and manual join requests to group
	err = api.Cache.AddMembershipRequestToGroup(r.Context(), group, membershipRequest)
	if err != nil {
		var socialError *errs.Error
		if errors.As(err, &socialError) {
			return errs.ToError(socialError)
		}
		return errs.New(http.StatusInternalServerError, errs.EGroupsGeneric)
	}

	//if first party, return first party member Id
	if isFirstPartyInvite {
		membershipRequest.Memberid = *membershipRequest.FirstPartyid
	}

	// send invite MQTT
	inviteMessage := apipub.MqttInviteReceived{
		Groupid:            group.Groupid,
		Productid:          &group.Productid,
		Approverid:         approverid,
		Memberid:           membershipRequest.Memberid,
		DisplayName:        membershipRequest.FromDisplayName,
		IsFirstPartyInvite: membershipRequest.IsFirstPartyInvite,
		Status:             "invited",
		Ttl:                membershipRequest.Ttl,
	}

	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	topic := tenant + "/user/" + fullAccountid
	messenger.SendMqttMessage(r.Context(), api.Cfg, topic, messenger.MqttMessageTypeGroupInviteReceived, inviteMessage)

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupInvite, membershipRequest.Approverid, ost, []string{membershipRequest.Memberid}, &appid, &additionalInfo))

	if bHttpReturnOk {
		membershipRequest.TeleMeta = nil
		ReturnOK(w, r, membershipRequest)
	}
	return nil
}

func (api *SocialPublicAPI) SendInvite(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, m apipub.PMemberid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := token.Claims.ProductID
	approverid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer

	memberid := m

	if memberid == token.Claims.Subject {
		ReturnEmptyOK(w, r)
		return
	}

	//	decode request
	var groupMembershipInvite apipub.SendInviteRequestBody
	if !DecodeBody(w, r, &groupMembershipInvite) {
		return
	}

	isFirstPartyInvite := false
	if groupMembershipInvite.IsFirstPartyInvite != nil && *groupMembershipInvite.IsFirstPartyInvite {
		isFirstPartyInvite = true
	}
	expiresIn := api.Cfg.TtlMembership
	if groupMembershipInvite.Ttl != nil {
		expiresIn = int(*groupMembershipInvite.Ttl)
	}

	e := api.SendInviteWithHttpReturn(w, r, g, productid, appid, ost, memberid, approverid, true, isFirstPartyInvite, expiresIn, groupMembershipInvite.TeleMeta)
	if e != nil {
		errs.Return(w, r, e)
		return
	}
}

// AcceptInvite accept an invite
func (api *SocialPublicAPI) AcceptInvite(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, a apipub.PApproverid) {
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	groupid := g
	productid := token.Claims.ProductID
	memberid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	approverid := a

	groupKey := apipub.BuildGroupRedisKey(tenant, productid, groupid)
	if !api.Cache.CachedObjExists(r.Context(), groupKey) {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	loggedInUsersOST := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	// get the request
	var acceptInviteRequest apipub.AcceptInviteRequestBody
	if !DecodeBody(w, r, &acceptInviteRequest) {
		return
	}

	membershipRequest, err := api.Cache.GetInvite(r.Context(), memberid, productid, groupid, approverid, acceptInviteRequest.IsFirstPartyInvite)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get invite %s#%s#%s#%s#%v", memberid, productid, groupid, approverid, acceptInviteRequest.IsFirstPartyInvite)
	}

	//could not find pending invite
	if membershipRequest == nil || membershipRequest.Status == apipub.Revoked {
		log.Error().Msgf("no valid invites found for %s-%s", groupid, memberid)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsInviteNotFound))
		return
	}

	//set crossplay of user accepting invite
	membershipRequest.CanCrossPlay = &acceptInviteRequest.CanCrossPlay

	// set OST to OST of the user who accepted the invite
	membershipRequest.OnlineServiceType = &loggedInUsersOST
	if membershipRequest.CanCrossPlay == nil {
		membershipRequest.CanCrossPlay = aws.Bool(true)
	}

	// check DNA links to make sure belongs to logged int user
	if membershipRequest.IsFirstPartyInvite != nil && !checkOwnership(r.Context(), api, memberid, membershipRequest.Memberid, membershipRequest.Approverid, int64(ost), *membershipRequest.IsFirstPartyInvite) {
		log.Error().Err(err).Msgf("user %s does not have ownership", memberid)
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMembershipNotOwned))
		return
	}

	// only allow different memberid from memberid if first party
	if membershipRequest.Memberid != memberid && (membershipRequest.IsFirstPartyInvite == nil || !*membershipRequest.IsFirstPartyInvite) {
		errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EGroupsNotAllowed))
		return
	}

	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get group %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	// check max members of group
	if group == nil {
		api.Cache.DeleteMembership(r.Context(), membershipRequest)
		log.Error().Err(err).Msgf("group %s:%s not found", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	if group.Members != nil && len(*group.Members) >= group.MaxMembers {
		log.Error().Err(err).Msgf("group is at max members %d", group.MaxMembers)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsFull))
		return
	}

	//Cannot accept invite into a cross play group without being a full account
	if group.CanCrossPlay != nil && *group.CanCrossPlay && !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	// check crossplay
	if !api.validCrossPlayRequest(*group, *membershipRequest) {
		log.Error().Err(err).Msgf("not a valid crossplay request")
		// send mqtt message to inviter to inform them that the invited user cannot join due to mismatched crossplay settings
		userModified := apipub.MqttGroupMemberModified{
			Action:   "failedToJoin",
			Userid:   membershipRequest.Memberid,
			Reason:   "crossplayMismatch",
			PreRole:  apipub.Nonmember,
			PostRole: apipub.Nonmember,
			Groupid:  groupid,
		}
		messenger.SendMqttMessage(r.Context(), api.Cfg, group.Topic(tenant), messenger.MqttMessageTypeGroupMembersModified, userModified)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsCrossplayValidationFailed))
		return
	}

	//have to delete previous before setting memberid to memberid for first party
	//makes sense to delete this above member check, because it can clean up entries if left hanging
	api.deletePreviousMembershipRequestFull(r.Context(), group, *membershipRequest)

	//if user is a member/leader already just return ok.
	if group.GetMemberRole(memberid) != apipub.Nonmember {
		ReturnEmptyOK(w, r)
		return
	}

	membershipRequest.Status = apipub.Accepted
	additionalInfo := make(map[string]string)
	if acceptInviteRequest.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*acceptInviteRequest.TeleMeta, &additionalInfo)
	}

	// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = membershipRequest.OnlineServiceType

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupAccept, membershipRequest.Memberid, ost, []string{membershipRequest.Approverid}, &appid, &additionalInfo))

	var teleMeta apipub.TelemetryMetaData
	if acceptInviteRequest.TeleMeta != nil {
		teleMeta = *acceptInviteRequest.TeleMeta
	}

	group.OnlineServiceType = originalOST
	_, err = api.JoinGroupFinalizeHelper(r, group, appid, ost, token, membershipRequest.Memberid, membershipRequest.Approverid, teleMeta)
	if err != nil {
		log.Error().Err(err).Msgf("group finalize helper failed")
		errs.Return(w, r, err.(*errs.Error))
		return
	}

	api.Cache.ClearAllMemberships(r.Context(), membershipRequest.Memberid, productid, groupid)

	ReturnEmptyOK(w, r)
}

// DeclineInvites reject an invite
func (api *SocialPublicAPI) DeclineInvites(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, a apipub.Dnaid) {
	log := logger.FromContext(r.Context())
	groupid := g
	approverid := a
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	//remove invite from group's memberships
	invite := apipub.MembershipRequest{
		Memberid:   userid,
		Groupid:    groupid,
		Approverid: approverid,
		Status:     apipub.Invited,
		Productid:  &productid,
	}

	var group *apipub.GroupResponse
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get group %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EDynamodbReadFailed))
		return
	}
	if group == nil {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	//We purposefully not checking the error here.  The only error returned is not found, which is fine.
	//Any invite that had the values provided is not accepted and removed.
	//This handles the case of the invite timing out in Redis before the invite is processed.  Since in the end the invite is not accepted, we're good.
	api.Cache.RemoveMembershipRequestFromGroup(r.Context(), group, invite)

	// telemetry
	additionalInfo := make(map[string]string)

	// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = &ost
	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupDecline, userid, ost, []string{approverid}, &appid, &additionalInfo))
	group.OnlineServiceType = originalOST

	ReturnEmptyOK(w, r)
}

// RevokeInvite revokes all invites from the user to the memberid
func (api *SocialPublicAPI) RevokeInvite(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, m apipub.PMemberid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	groupid := g
	log := logger.FromContext(r.Context())
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer
	userid := token.Claims.Subject

	memberid := m

	var group *apipub.GroupResponse
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get group %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EDynamodbReadFailed))
		return
	}
	if group == nil {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	// do not allow modification of a cross play group membership unless full account
	if group.CanCrossPlay != nil && *group.CanCrossPlay && !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	if userid == memberid {
		log.Error().Err(err).Msgf("not allowed to revoke own invite")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotAllowed))
		return
	}

	//create new request
	var membershipRequest apipub.MembershipRequest
	membershipRequest.Status = apipub.Revoked
	membershipRequest.Memberid = memberid
	membershipRequest.Approverid = userid
	membershipRequest.Groupid = groupid
	if membershipRequest.Productid == nil {
		membershipRequest.Productid = &productid
	}

	//delete old request if request details match
	//only throw EGroupsInvalidIsFirstParty if the referrer is not RevokeInvite
	if err = api.deletePreviousMembershipRequestFull(r.Context(), group, membershipRequest); err != nil {
		log.Error().Err(err).Msgf("did not delete previous group membership")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	//api.Cache.AddMembershipRequestToGroup(r.Context(), group, membershipRequest)

	// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = membershipRequest.OnlineServiceType

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupRevoke, membershipRequest.Approverid, ost, []string{m}, &appid, nil))
	group.OnlineServiceType = originalOST

	membershipRequest.TeleMeta = nil
	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) AcceptInviteByFirstPartySessionIds(w http.ResponseWriter, r *http.Request, pOnlineServiceType apipub.POnlineServiceType, pSessionid apipub.PSessionid, pFirstPartyApproverid apipub.PFirstPartyApproverid) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := pOnlineServiceType
	sessionid := pSessionid
	approverid := pFirstPartyApproverid

	log.Debug().Str("userid", userid).Str("productid", productid).Int("ost", int(ost)).Str("sessionid", sessionid).Str("approverid", approverid).Str("event", "sync_session_to_group").Msg("sync_session_to_group")
}
