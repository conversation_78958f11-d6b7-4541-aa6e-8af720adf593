<script lang="ts">
  export let online = false;
  export let playing = false;
  export let offline = false;
  export let away = false;
  export let className = '';
</script>

<style>
  .status {
    width: 8px;
    height: 8px;
    border-radius: 50%;
  }

  .status.online {
    background-color: rgba(83, 190, 49, 1);
  }

  .status.playing {
    background-color: rgba(83, 190, 49, 1);
  }

  .status.away {
    background-color: rgba(255, 193, 7, 1);
  }

  .status.offline {
    background: #2d2d2d;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-sizing: border-box;
  }
</style>

<div
  class="status {className}"
  class:online
  class:playing
  class:away
  class:offline
></div>
