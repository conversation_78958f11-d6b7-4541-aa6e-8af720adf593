{erl_opts, [warnings_as_errors]}.
{deps, [base64url, jsx]}.
{plugins, [coveralls]}.
{eunit_compile_opts, [export_all]}.

{profiles, [
    {edown, [
        {deps, [
            {edown, "0.8.1"}
        ]},
        {edoc_opts, [
            {doclet, edown_doclet},
            {top_level_readme,
                {"./README.md", "http://github.com/artemeff/jwt", "master"}}
        ]}
    ]}
]}.

{cover_enabled, true}.
{cover_export_enabled, true}.
{coveralls_coverdata, "_build/test/cover/eunit.coverdata"}.
{coveralls_service_name, "travis-ci"}.
