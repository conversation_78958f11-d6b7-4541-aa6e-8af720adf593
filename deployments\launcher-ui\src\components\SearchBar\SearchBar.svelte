<script lang="ts">
  import debounce from 'lodash.debounce';
  import { createEventDispatcher } from 'svelte';
  import { SVGSearch } from '../../assets/icons';
  import type { CustomEventMap } from '../CustomInput';

  export let placeholder = '';
  export let value = '';
  const dispatch = createEventDispatcher<CustomEventMap>();

  const onTextChange = debounce((event: Event) => {
    event.preventDefault();
    event.stopPropagation();

    dispatch('inputChange', {
      text: (event.target as HTMLInputElement).value,
    });
  }, 500);
</script>

<style>
  .search-bar {
    width: 100%;
    display: flex;
    position: relative;
  }

  .search-bar input {
    color: #ffffff;
    width: 100%;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    background-color: #2d2d2d;
    height: 36px;
    margin: 0;
    padding-inline-start: 2.5rem;
    line-height: 0;
  }

  .search-bar input::placeholder {
    color: rgba(255, 255, 255, 0.4);
    font-size: 14px;
    font-weight: 500;
  }
  .search-bar .search-icon {
    display: flex;
    position: absolute;
    top: 0.6rem;
    left: 1rem;
  }
</style>

<div class="search-bar">
  <span class="search-icon">
    <SVGSearch />
  </span>

  <input
    placeholder="{placeholder}"
    on:input="{onTextChange}"
    value="{value}"
  />
</div>
