package oapi

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/getkin/kin-openapi/openapi3filter"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

// BearerAuthenticate seems unnecessary right now but I think it will be needed once we try to expand this to multiple identity providers.
func BearerAuthenticate(ctx context.Context, input *openapi3filter.AuthenticationInput, cfg *config.Config, id identity.IdentityInterface) error {
	if input.SecuritySchemeName != "bearerAuth" {
		return fmt.Errorf("security scheme %s != 'bearerAuth'", input.SecuritySchemeName)
	}
	_, err := AuthenticateRequest(ctx, input.RequestValidationInput.Request, cfg, id)

	if err != nil {
		return input.NewError(errors.New(err.Message))
	}
	return nil
}

// AuthenticateRequest validates the JWT in the bearer token in the request
func AuthenticateRequest(ctx context.Context, r *http.Request, cfg *config.Config, id identity.IdentityInterface) (*jwt.Token, *errs.Error) {
	// we're not using scopes with bearer auth currently.  might need this later.
	// checkBearer := ctx.Value(apipub.BearerAuthScopes)
	// if checkBearer == nil {
	// 	log.Error().Msgf("API spec does not have bearer authentication requirement")
	// 	return nil, errs.New(http.StatusForbidden, errs.EInvalidAuthHeader)
	// }

	// if skipauth just return the token if it's parseable.
	token, err := authheader.ParseJWTFromRequest(r)
	if cfg.SkipAuth {
		return token, err
	}

	// err would be checked in ParseJWTFromRequest above
	authHeader, _ := authheader.GetAuthHeaderArr(r)
	_, err2 := id.Authenticate(ctx, authHeader, true)
	if err2 != nil {
		// It occurres if an app id enables single point of presence and caller wants to override
		// a logged in session. DNA will generate a new token which will be invalid in 65 seconds.
		// Prior to that, the token remains invalid.
		if strings.Contains(err2.Error(), "invalid token issued timestamp") {
			return nil, errs.New(http.StatusUnauthorized, errs.ENotYetValid)
		}
		return nil, errs.New(http.StatusUnauthorized, errs.EAuthorizationFailed)
	}

	return token, nil
}

// BasicAuthenticate check for basic auth
func BasicAuthenticate(ctx context.Context, input *openapi3filter.AuthenticationInput, cfg *config.Config, id identity.IdentityInterface) error {
	if input.SecuritySchemeName != "basicAuth" {
		return fmt.Errorf("security scheme %s != 'basicAuth'", input.SecuritySchemeName)
	}
	return nil
}

// TrustServiceAuthenticate check for basic auth or bearer auth
func TrustServiceAuthenticate(ctx context.Context, input *openapi3filter.AuthenticationInput, cfg *config.Config, id identity.IdentityInterface) error {
	for i := range input.Scopes {
		if input.Scopes[i] == "auth" && input.SecuritySchemeName != "basicAuth" {
			return fmt.Errorf("security scheme %s != 'basicAuth' for authentication paths", input.SecuritySchemeName)
		} else if input.Scopes[i] == "operation" && input.SecuritySchemeName != "bearerAuth" {
			return fmt.Errorf("security scheme %s != 'bearerAuth' for operation paths", input.SecuritySchemeName)
		}
	}
	return nil
}
