<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { Route, Router } from 'svelte-routing';
  import backgroundImage from '../../assets/images/social-background.jpg';
  import {
    FriendInvites,
    FriendRequests,
    FriendsList,
    TaskBar,
    TopBar,
  } from '../../components';
  import {
    EVENT_FRIENDS_FETCH,
    EVENT_FRIENDS_FETCH_RESULT,
    EVENT_FRIENDS_PAGE_MOUNT,
    EVENT_FRIENDS_WINDOW_CLOSE,
    EVENT_FRIENDS_WINDOW_MINIMIZE,
    EVENT_LANGUAGE_CHANGED,
    EVENT_MQTT_FRIEND_INVITE_MESSAGE_RECEIVED,
    EVENT_MQTT_FRIEND_REMOVED_MESSAGE_RECEIVED,
    EVENT_MQTT_PRESENCE_MESSAGE_RECEIVED,
    EVENT_PENDING_FRIENDS_FETCH,
    EVENT_PENDING_FRIENDS_FETCH_RESULT,
    EVENT_USER_PROFILE_FETCH_RESULT,
    ROUTE_FRIENDS_LIST,
    ROUTE_FRIEND_INVITES,
    ROUTE_FRIEND_REQUESTS,
    ROUTE_INITIAL_PAGE,
  } from '../../constant';
  import {
    useLocale,
    useLogService,
    useQueryService,
    useTranslator,
    useTransportService,
    useUserQuery,
  } from '../../hooks';
  import { useFriendsQuery } from '../../hooks/useFriendsQuery';
  import { usePendingFriendsQuery } from '../../hooks/usePendingFriendsQuery';
  import type {
    MqttPresenceMessageData,
    MqttUserMessageData,
    PendingFriend,
    QueryResult,
    SocialFriend,
    UserProfile,
  } from '../../services';
  import type { socialTheme } from '../../utils';
  import { themefunc } from '../../utils';

  export let url = '/';

  export let theme: socialTheme = {
    color: '',
    bgColorTopBar: '',
    bgColorActionBar: '',
    bgColorButton: '',
  };

  $: style = themefunc(theme);

  const transportService = useTransportService();
  const logService = useLogService();
  const userQueryResult = useUserQuery();
  const queryService = useQueryService();
  const t = useTranslator();
  const locale = useLocale();
  const pendingFriendsQueryResult = usePendingFriendsQuery();
  const friendsQueryResult = useFriendsQuery();

  const refreshFriendsData = () => {
    transportService.publishEvent(EVENT_PENDING_FRIENDS_FETCH);
    transportService.publishEvent(EVENT_FRIENDS_FETCH);
  };

  const onCloseButtonClicked = () => {
    transportService.publishEvent(EVENT_FRIENDS_WINDOW_CLOSE);
  };

  const onMinimizedButtonClicked = () => {
    transportService.publishEvent(EVENT_FRIENDS_WINDOW_MINIMIZE);
  };

  onMount(() => {
    queryService.mount();

    transportService.subscribeEvent(
      EVENT_LANGUAGE_CHANGED,
      (_eventName: string, lang: string) => {
        locale.update(() => lang);
      }
    );

    transportService.subscribeEvent(
      EVENT_USER_PROFILE_FETCH_RESULT,
      (_, result: QueryResult<UserProfile>) => {
        userQueryResult.set(result);
      }
    );

    transportService.subscribeEvent(
      EVENT_PENDING_FRIENDS_FETCH_RESULT,
      (_: string, result: QueryResult<PendingFriend[]>) => {
        logService.log('handling pending friends fetch result...');
        pendingFriendsQueryResult.set(result);
      }
    );

    transportService.subscribeEvent(
      EVENT_FRIENDS_FETCH_RESULT,
      (_: string, result: QueryResult<PendingFriend[]>) => {
        logService.log('handling friends fetch result...');
        friendsQueryResult.set(result);
      }
    );

    transportService.subscribeEvent(
      EVENT_MQTT_PRESENCE_MESSAGE_RECEIVED,
      (
        _,
        { userId, data }: { userId: string; data: MqttPresenceMessageData }
      ) => {
        logService.log('handling friend presence message...', data);
        const isReceivedFriend = (f: SocialFriend) => f.userid === userId;
        const currentResult = $friendsQueryResult;

        if (currentResult && currentResult.data) {
          const myFriends = currentResult.data;
          const friend = myFriends.find(isReceivedFriend);
          const otherFriends = myFriends.filter(f => !isReceivedFriend(f));
          if (friend) {
            friend.presence[0] = data;
            currentResult.data = [friend, ...otherFriends];
            friendsQueryResult.set(currentResult);
          }
        }
      }
    );

    transportService.subscribeEvent(
      EVENT_MQTT_FRIEND_REMOVED_MESSAGE_RECEIVED,
      () => {
        logService.log('handling friends remove message...');
        refreshFriendsData();
      }
    );

    transportService.subscribeEvent(
      EVENT_MQTT_FRIEND_INVITE_MESSAGE_RECEIVED,
      (_, { userId, data }: { userId: string; data: MqttUserMessageData }) => {
        logService.log('handling friend invite message...', data);
        switch (data.status) {
          case 'pending':
            logService.log('received friend request', userId, data);
            break;

          case 'friend':
            logService.log('now being friend with', userId, data);
            break;
        }

        refreshFriendsData();
      }
    );

    // loading initial data on friends page mount
    transportService.publishEvent(EVENT_FRIENDS_PAGE_MOUNT);
    transportService.publishEvent(EVENT_FRIENDS_FETCH);
    transportService.publishEvent(EVENT_PENDING_FRIENDS_FETCH);
  });

  onDestroy(() => {
    transportService.unsubscribe(EVENT_LANGUAGE_CHANGED);
    transportService.unsubscribe(EVENT_PENDING_FRIENDS_FETCH_RESULT);
    transportService.unsubscribe(EVENT_FRIENDS_FETCH_RESULT);
    transportService.unsubscribe(EVENT_MQTT_PRESENCE_MESSAGE_RECEIVED);
    transportService.unsubscribe(EVENT_MQTT_FRIEND_REMOVED_MESSAGE_RECEIVED);
    transportService.unsubscribe(EVENT_MQTT_FRIEND_INVITE_MESSAGE_RECEIVED);

    queryService.unmount();
  });
</script>

<style>
  :root {
    --default-color: #ffffff;
    --default-bg-color-top-bar: rgba(26, 26, 26, 1);
    --default-bg-color-action-bar: rgba(26, 26, 26, 0.7);
    --default-bg-color-friend-bar: rgba(64, 64, 64, 1);
    --default-bg-color-import-friend-card: rgba(26, 26, 26, 0.4);
    --default-bg-color-button: rgba(30, 102, 168, 1);
    --default-bg-color-header: rgba(45, 45, 45, 1);
  }

  .friends {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .friends .content {
    flex: 1;
    background-color: var(
      --social-bg-color-action-bar,
      var(--default-bg-color-action-bar)
    );
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
</style>

<Router url="{url}">
  <div style="{style}" class="friends">
    <TaskBar
      on:close="{onCloseButtonClicked}"
      on:minimize="{onMinimizedButtonClicked}"
    />
    <TopBar title="{$t('Friends')}" />

    <main
      style="{`background-image: url("${backgroundImage}")`}"
      class="content"
    >
      <Route path="{ROUTE_FRIEND_REQUESTS}">
        <FriendRequests />
      </Route>
      <Route path="{ROUTE_FRIEND_INVITES}">
        <FriendInvites />
      </Route>
      <Route path="{ROUTE_FRIENDS_LIST}">
        <FriendsList />
      </Route>
      <Route path="{ROUTE_INITIAL_PAGE}">
        <FriendsList />
      </Route>
    </main>
  </div>
</Router>
