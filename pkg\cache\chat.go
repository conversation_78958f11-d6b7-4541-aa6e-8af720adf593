package cache

import (
	"context"
	"encoding/json"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

// SetChatMessage set chat message to redis
func (rc *RedisCache) SetChatMessage(ctx context.Context, tenant string, message *apipub.ChatMessage, ttl time.Duration) error {
	log := logger.FromContext(ctx)

	messageString, err := json.Marshal(message)
	if err != nil {
		return err
	}

	subjectKey := message.RedisKey(tenant, message.SubjectId)
	targetKey := message.RedisKey(tenant, message.TargetId)
	_, err = rc.pipelined(ctx, func(pipe redis.Pipeliner) error {
		err := rc.zAdd(ctx, subjectKey, redis.Z{
			Score:  float64(message.PostedTime.Unix()),
			Member: messageString,
		}).Err()
		if err != nil {
			return err
		}
		err = rc.zAdd(ctx, targetKey, redis.Z{
			Score:  float64(message.PostedTime.Unix()),
			Member: messageString,
		}).Err()
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		log.Error().Err(err).Msgf("failed to set chat message")
		return err
	}

	return nil
}
