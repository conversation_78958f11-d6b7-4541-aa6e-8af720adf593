<script>
  import { getContext } from 'svelte';
  import { CONTEXT_KEY_TABS } from '../../constant';

  const panel = {};
  const { registerPanel, selectedPanel } = getContext(CONTEXT_KEY_TABS);

  registerPanel(panel);
</script>

<style>
  .tab-panel {
    display: flex;
    width: 100%;
    height: 100%;
  }
</style>

{#if $selectedPanel === panel}
  <div class="tab-panel">
    <slot />
  </div>
{/if}
