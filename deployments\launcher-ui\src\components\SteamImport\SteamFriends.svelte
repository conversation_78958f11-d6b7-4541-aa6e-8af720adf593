<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { writable } from 'svelte/store';
  import { SVGEmptyFace, SVGRefresh } from '../../assets/icons';
  import {
    EVENT_FRIEND_REQUEST_MAKE,
    EVENT_FRIEND_REQUEST_MAKE_ERROR,
    EVENT_FRIEND_REQUEST_SENT,
    EVENT_STEAM_FRIENDS_FETCH,
    EVENT_STEAM_FRIENDS_FETCH_RESULT,
  } from '../../constant';
  import {
    useLinkedSteamId,
    useSteamFriendsQuery,
    useTranslator,
    useTransportService,
  } from '../../hooks';
  import type { QueryResult, SteamFriend } from '../../services';
  import { getMinElapsed, isQueryLoading, sortByName } from '../../utils';
  import { Button } from '../Button';
  import type {
    CheckboxClickEventDetail,
    ImportFriendTileClickEventDetail,
  } from '../CustomInput';
  import { CustomInput } from '../CustomInput';
  import { FriendImportCard } from '../FriendImportCard';
  import { LoadingSpinner } from '../LoadingSpinner';

  const t = useTranslator();
  const transportService = useTransportService();
  const linkedSteamId = useLinkedSteamId();
  const steamFriendsQueryResult = useSteamFriendsQuery();

  const lastUpdatedInMins = writable(0);
  const selectedFriendUserIds = writable<string[]>([]);

  let sendingRequest = false;
  const onRefreshButtonClicked = () => {
    transportService.publishEvent(EVENT_STEAM_FRIENDS_FETCH, $linkedSteamId);
  };

  const onTileClick = ({
    detail,
  }: CustomEvent<ImportFriendTileClickEventDetail>) => {
    const { checked, value } = detail;
    if (checked) {
      selectedFriendUserIds.set([...$selectedFriendUserIds, value]);
    } else {
      if ($selectedFriendUserIds.includes(value)) {
        selectedFriendUserIds.set(
          $selectedFriendUserIds.filter(userId => userId !== value)
        );
      }
    }
  };

  const onSelectAllClick = ({
    detail,
  }: CustomEvent<CheckboxClickEventDetail>) => {
    const { checked } = detail;
    if (checked) {
      selectedFriendUserIds.set(
        steamFriends.map(steamFriend => steamFriend.profileDNA.parentAccountId)
      );
    } else {
      selectedFriendUserIds.set([]);
    }
  };

  const onAddFriendsClicked = () => {
    sendingRequest = true;
    transportService.publishEvent(
      EVENT_FRIEND_REQUEST_MAKE,
      $selectedFriendUserIds
    );
  };

  onMount(() => {
    transportService.publishEvent(EVENT_STEAM_FRIENDS_FETCH, $linkedSteamId);

    transportService.subscribeEvent(
      EVENT_STEAM_FRIENDS_FETCH_RESULT,
      (_, result: QueryResult<SteamFriend[]>) => {
        steamFriendsQueryResult.set(result);
      }
    );

    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_SENT, () => {
      sendingRequest = false;
    });

    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_MAKE_ERROR, () => {
      sendingRequest = false;
    });

    setInterval(() => {
      const minElapsed = getMinElapsed(dataUpdatedAt);
      if (minElapsed > $lastUpdatedInMins) {
        lastUpdatedInMins.set(minElapsed);
      }
    }, 1000);
  });

  onDestroy(() => {
    transportService.unsubscribe(EVENT_STEAM_FRIENDS_FETCH_RESULT);
  });

  $: dataUpdatedAt = $steamFriendsQueryResult?.dataUpdatedAt || Date.now();
  $: steamFriends = ($steamFriendsQueryResult?.data || [])
    .filter(d => !!d.profileDNA)
    .sort((a, b) =>
      sortByName(
        a.profileSteam.PersonaName.toUpperCase(),
        b.profileSteam.PersonaName.toUpperCase()
      )
    );
  $: allSelected = $selectedFriendUserIds.length === steamFriends.length;
  $: partialyChecked =
    $selectedFriendUserIds.length > 0 &&
    $selectedFriendUserIds.length < steamFriends.length;
  $: queryLoading = isQueryLoading($steamFriendsQueryResult);
  $: steamFriendsFound = steamFriends && steamFriends.length > 0;
</script>

<style>
  .icons {
    display: flex;
    align-items: center;
  }

  .title {
    opacity: 0.8;
    text-transform: uppercase;
    font-weight: 700;
    color: var(--social-color, var(--default-color));
    margin-top: 1rem;
    margin-bottom: 0;
  }

  .description {
    color: var(--social-color, var(--default-color));
    opacity: 0.8;
    margin: 0.5rem 2.5rem 1rem 0;
    text-align: center;
    font-size: 0.875rem;
    line-height: 150%;
    font-weight: 500;
  }

  .content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .content .info-container,
  .action-container {
    width: 100%;
    padding: 0.625rem 0;
    min-height: 3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    color: var(--social-color, var(--default-color));
  }

  .info-container {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .info-container .info {
    font-size: 0.75rem;
    font-style: italic;
    line-height: 150%;
    font-weight: 500;
    opacity: 0.8;
  }

  .data-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }
</style>

{#if queryLoading}
  <LoadingSpinner />
{:else if !queryLoading && steamFriendsFound}
  <div class="content">
    <div class="info-container">
      <span class="info">
        {#if $lastUpdatedInMins < 1}
          {`last updated less than 1 min ago`}
        {:else}{`last updated ${$lastUpdatedInMins} mins ago`}{/if}
      </span>
      <Button
        on:buttonClick="{onRefreshButtonClicked}"
        outline
        loading="{queryLoading}"
        showIcon
      >
        <span slot="icon">
          <SVGRefresh />
        </span>
        <span slot="label"> {$t('Refresh')} </span>
      </Button>
    </div>
    <div class="action-container">
      <CustomInput
        type="checkbox"
        on:checkboxClick="{onSelectAllClick}"
        checked="{allSelected || partialyChecked}"
        showPartialCheck="{partialyChecked}"
        name="select-all-checkbox"
      >
        {$t('Select all')}
      </CustomInput>

      <Button
        on:buttonClick="{onAddFriendsClicked}"
        disabled="{$selectedFriendUserIds.length === 0}"
        loading="{sendingRequest}"
      >
        <span slot="label">
          {$t('Add Friends')}
          ({$selectedFriendUserIds.length})
        </span>
      </Button>
    </div>
    <div class="data-container">
      {#each steamFriends as steamFriend}
        <FriendImportCard
          initials="{steamFriend.profileSteam.PersonaName &&
            steamFriend.profileSteam.PersonaName[0]}"
          displayName="{steamFriend.profileSteam.PersonaName}"
          userId="{steamFriend.profileDNA.parentAccountId}"
          platform="steam"
          avatar="{steamFriend.profileSteam.avatar}"
          selected="{$selectedFriendUserIds.includes(
            steamFriend.profileDNA.parentAccountId
          )}"
          on:tileClick="{onTileClick}"
        />
      {/each}
    </div>
  </div>
{:else}
  <div class="icons">
    <span><SVGEmptyFace /></span>
  </div>

  <p class="title">{$t('no steam friends to add')}</p>
  <p class="description">(steamId: {$linkedSteamId})</p>
  <p class="description">{$t('Manage Connection in Account Overview')}</p>

  <Button on:buttonClick="{onRefreshButtonClicked}" outline showIcon>
    <span slot="icon">
      <SVGRefresh />
    </span>
    <span slot="label"> {$t('Refresh')} </span>
  </Button>
{/if}
