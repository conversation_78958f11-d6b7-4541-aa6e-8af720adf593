FROM vernemq/vernemq:1.13.0
USER root

# Apt-get
RUN apt-get update
RUN apt-get install -y gnupg gnupg1 gnupg2

# Add erlang repo
RUN echo "deb http://binaries2.erlang-solutions.com/debian/ bullseye-elixir-1.15 contrib" >> /etc/apt/sources.list
RUN curl -o GPG-KEY-pmanager.asc https://binaries2.erlang-solutions.com/GPG-KEY-pmanager.asc && apt-key add GPG-KEY-pmanager.asc

# Apt-get
RUN apt-get update
RUN apt-get install -y git curl wget openssl openssh-server ca-certificates erlang erlang-dev

# Allow man in the middle attack SSL cert from T2 IT
RUN echo quit | openssl s_client -showcerts -servername proxy.golang.org -connect proxy.golang.org:443  2>&1 | sed -ne '/-BEGIN CERTIFICATE-/,/-END CERTIFICATE-/p' > /etc/ssl/certs/ca-cert-t2.pem

ENV PATH=/vernemq/bin:/vernemq/erts-13.2.2.1/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
ENV ERL_LIBS=/vernemq/lib:/usr/lib/erlang/lib:/app/_build/default/lib
