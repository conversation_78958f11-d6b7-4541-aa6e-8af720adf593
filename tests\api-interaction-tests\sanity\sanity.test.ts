import { StatusCodes } from "http-status-codes";
import { TwokAccounts, config } from "../integration/lib/config";
import * as socialApi from '../integration/lib/social-api';
import request from 'supertest';
import { unlinkSync, statSync, existsSync } from 'fs';
import { join } from 'path';
import { MqttClientManager } from "../integration/lib/mqttWrapper";

async function submitMetrics(testResult: number, testName: string) {
  const resp: request.Response = await request('https://api.datadoghq.com/api/v2')
    .post('/series')
    .set({
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'DD-API-KEY': config.ddApiKey
    })
    .send({
      "series": [
        {
          "metric": "social.smoke-test",
          "type": 3,
          "points": [
            {
              "timestamp": Math.round(new Date().getTime() / 1000).toString(),
              "value": testResult
            }
          ],
          "tags": [
            `env:${config.socialServiceEnvironment}`,
            `test-case:${testName}`
          ]
        }
      ]
    });

  // TODO: For debugging.  Remove me later.
  console.log(`DD Metrics | status: ${resp.status} | body: ${JSON.stringify(resp.body, null, 4)}`);

  expect(resp.status).toBe(StatusCodes.ACCEPTED);
  expect(resp.body).toEqual({ "errors": [] });
}

enum TestResult {
  Fail = 1,
  Pass = 2,
}

let testResult = TestResult.Fail;

afterEach(async () => {
  await submitMetrics(testResult, expect.getState().currentTestName);
  testResult = TestResult.Fail;
})

describe('', () => {
  it('check social API server', async () => {
    let url = `${config.socialService.currAccessLevel.currEnv.currVer.api}/health`;
    let id = "dna";

    const r: request.Response = await request(url)
      .get(`?id=${id}`);

    if (r.status === 200 && r.body) {
      for (let d of Object.values(r.body.dependencies)) {
        expect(d).toEqual('OK');
      }
    }

    testResult = TestResult.Pass;
  });
});

describe('', () => {
  let downloadedFile = 'temp_ci_test_accounts';

  beforeEach(async () => {
    // clear temp file if exists
    if (existsSync(join(__dirname, downloadedFile))) {
      unlinkSync(join(__dirname, downloadedFile));
    }
  })

  afterEach(async () => {
    // clear temp file if exists
    if (existsSync(join(__dirname, downloadedFile))) {
      unlinkSync(join(__dirname, downloadedFile));
    }
  })

  it('check test account manifest', async () => {
    const { execSync } = require('child_process');

    // download CI test account file
    execSync(`aws s3 cp s3://t2gp-social-develop/ci-test-account-envs/.env.twok_accounts_FOR_CI_DO_NOT_USE ./${downloadedFile}`, { cwd: __dirname });

    // expect file size is more than 0
    let s = statSync(join(__dirname, downloadedFile));
    expect(s.size).toBeGreaterThan(0);

    testResult = TestResult.Pass;
  });
});

describe('', () => {
  let usersTwok: TwokAccounts;
  let mqttClientMgr: MqttClientManager;
  let expectedMqttMsgType = "friendInvite";
  let mgroupId: string;

  beforeAll(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "user"]);
    await usersTwok.loginAll({});

    mqttClientMgr = new MqttClientManager(usersTwok.acct);
    await mqttClientMgr.waitConnectAll();
  });

  afterAll(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mgroupId);

    await socialApi.deleteFriend(
      usersTwok.acct["leader"],
      usersTwok.acct["user"].publicId
    );

    mqttClientMgr.stopAll();
    await usersTwok.logoutAll({});
  });

  it('create group', async () => {
    // Create a group that maxMembers without value
    let actualStatuses;
    switch (config.socialAPIVersion) {
      case "v1":
        actualStatuses = await socialApi.createGroupV1(
          usersTwok.acct["leader"],
          {
            maxMembers: 6,
            joinRequestAction: 'manual',
            canCrossPlay: true
          }
        );
        break;
      case "v2":
        actualStatuses = await socialApi.createGroup(
          usersTwok.acct["leader"],
          {
            maxMembers: 6,
            joinRequestAction: 'manual',
            canCrossPlay: true
          }
        )
        break;
      default:
        throw new Error(`Unknown social API version [${config.socialAPIVersion}]`);
    }

    socialApi.testStatus(StatusCodes.CREATED, actualStatuses);
    mgroupId = socialApi.getGroupId(actualStatuses);

    const actualGroupInfo = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mgroupId
    );

    // Expect the group is created with the groupMaxMembersDefault
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: { groupid: mgroupId, maxMembers: 6 },
    };
    expect(actualGroupInfo).toMatchObject(expectedGroupInfo);

    testResult = TestResult.Pass;
  });

  it('make friend and get the friendInvite mqtt message', async () => {
    // verify message contents
    let occurCount = { cnt: 0 };
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          inviter: usersTwok.acct["leader"].publicId,
          status: 'pending'
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserTopic(usersTwok.acct["user"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["user"].getClient().on('message', vmc);

    // member1 sends member2 friend invitation
    let resp: request.Response;
    switch (config.socialAPIVersion) {
      case "v1":
        resp = await socialApi.makeFriendsV1(
          usersTwok.acct["leader"],
          { userid: usersTwok.acct["user"].publicId, message: 'T2GP Social Automated Testing' }
        );
        break;
      case "v2":
        resp = await socialApi.makeFriends(
          usersTwok.acct["leader"],
          usersTwok.acct["user"].publicId,
          { message: 'T2GP Social Automated Testing' }
        );
        break;
      default:
        throw new Error(`Unknown social API version [${config.socialAPIVersion}]`);
    }

    socialApi.testStatus(StatusCodes.OK, resp);

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 1000, 1);

    // verify occurrence
    expect(occurCount.cnt).toBe(1);

    // verify the friend list info of leader the inviter
    let actualFriendsInfo = await socialApi.getFriends(usersTwok.acct["leader"], {});

    let expectedFriendsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [
          {
            friendid: usersTwok.acct["user"].publicId,
            invitee: usersTwok.acct["user"].publicId,
            status: 'pending',
            userid: usersTwok.acct["leader"].publicId,
          },
        ],
      },
    };
    expect(actualFriendsInfo).toMatchObject(expectedFriendsInfo);

    testResult = TestResult.Pass;
  });
});