import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccount, TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

/**
 * The max number of IDs allowed to be sent in one block list update request.
 */
const maxReqSize = 20;
let usersTwok: TwokAccounts;

/**
 * NOTE: these setup functions are used instead of beforeEach
 * because they take parameters from the test cases themselves.
 */
async function setup(blockedNum: number) {
  // Define labels: 1 blocker and 0-n blockees.
  let bLabels: string[] = ["blocker"];
  for (let i = 1; i <= blockedNum; i++) {
    bLabels.push("blockee" + i.toString());
  }

  let usersTwok = new TwokAccounts(bLabels.length, bLabels);
  await usersTwok.acct["blocker"].login({});

  // create an input list of blocked IDs
  let blockedIdArray: string[] = [];
  for (let i = 1; i <= blockedNum; i++) {
    let userIndex = "blockee" + i.toString();
    blockedIdArray.push(usersTwok.acct[userIndex].publicId)
  }

  return { usersTwok, blockedIdArray };
}

describe(`block list[public v2]${_ds}happy cases${_ds}`, () => {
  describe('', () => {
    let s: {usersTwok: TwokAccounts, blockedIdArray: string[]};

    afterEach(async () => {
      const r = await socialApi.delBlockList(s.usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await s.usersTwok.acct["blocker"].logout({});
    });

    it.each`
      desc                            | blockedNum
      ${"can add 0 item"}             | ${0}
      ${"can add 1 item"}             | ${1}
      ${"can add a few items[happy]"} | ${5}
      ${"can add max-limit items"}    | ${100}
    `('$desc', async ({blockedNum}) => {
      s = await setup(blockedNum);

      let testCase = {
        description: `add ${blockedNum} valid user IDs to the block list`,
        expected: "all added users are present in the block list; order unimportant"
      }

      // blocker adds the list of IDs to his/her block list
      for (let i = 0; i < s.blockedIdArray.length; i += maxReqSize) {
        const r = await socialApi.updateBlockList(
          s.usersTwok.acct["blocker"],
          {
            userids: s.blockedIdArray.slice(i, i+maxReqSize),
          }
        );
        socialApi.testStatus(StatusCodes.OK, r);
      }

      // get the actual block list
      const actualBL = await socialApi.getBlockList(
        s.usersTwok.acct["blocker"],
        {}
      );

      // create an expected array of blockee objects
      let blockedIds = [];
      for (let id of s.blockedIdArray) {
        blockedIds.push(expect.objectContaining({ blockedid: id }));
      }

      // verify the block list contains the expected list of blockee objects
      const expectedBL = {
        status: StatusCodes.OK,
        body: {
          items: expect.arrayContaining(blockedIds),
        },
      };
      socialApi.expectMore(
        () => {expect(actualBL).toMatchObject(expectedBL)},
        testCase,
        {
          resp: actualBL,
          additionalInfo: {
            "fail reason": "the actual block list does not match the input user IDs"
          }
        }
      );
    });
  });

  describe('', () => {
    afterEach(async () => {
      await usersTwok.acct["blocker"].logout({});
    });

    async function removeSpecificUser() {
      const resp = await socialApi.removeUserFromBlocklist(usersTwok.acct["blocker"], usersTwok.acct["blockee1"].publicId);
      socialApi.testStatus(StatusCodes.OK, resp);
    }

    async function removeAllUsers() {
      const resp = await socialApi.delBlockList(usersTwok.acct["blocker"]);
      socialApi.testStatus(StatusCodes.OK, resp);
    }

    it.each`
      testSubjectFunc       | desc                               | blockedNum
      ${removeAllUsers}     | ${"can remove 0 item"}             | ${0}
      ${removeSpecificUser} | ${"can remove 1 item"}             | ${1}
      ${removeAllUsers}     | ${"can remove a few items[happy]"} | ${5}
      ${removeAllUsers}     | ${"can remove max-limit items"}    | ${100}
    `('$desc', async ({testSubjectFunc, blockedNum}) => {
      let s = await setup(blockedNum);
      usersTwok = s.usersTwok;

      let testCase = {
        description: `add ${blockedNum} valid user IDs to the block list; remove the same users from the block list`,
        expected: `the block list is empty`
      }

      // blocker add the list of IDs to his/her block list
      for (let i = 0; i < s.blockedIdArray.length; i += maxReqSize) {
        let r = await socialApi.updateBlockList(
          usersTwok.acct["blocker"],
          {
            userids: s.blockedIdArray.slice(i, i+maxReqSize),
          }
        );
        socialApi.testStatus(StatusCodes.OK, r);
      }

      // blocker remove the list of IDs from his/her block list
      await testSubjectFunc();

      // get the actual block list
      const actualBL = await socialApi.getBlockList(
        usersTwok.acct["blocker"],
        {}
      );

      // verify the block list is empty
      socialApi.testStatus(StatusCodes.OK, actualBL);
      socialApi.expectMore(
        () => {expect(actualBL.body).toEqual({items: [], nextid: null})},
        testCase,
        {
          resp: actualBL,
          additionalInfo: {
            "fail reason": "some users remain on the block list"
          }
        }
      );
    });
  });

  // Note: be mindful about how many pages there are for a test case.  Large blockedNum and small limit result in many pages.
  // Each page results in an API call, too many of which would make a test case take too long to run.
  // Keep the number of pages similar across test caes should also ensure test cases all run within similar amount of time.
  describe('', () => {
    let s: {usersTwok: TwokAccounts, blockedIdArray: string[]};

    afterEach(async () => {
      const r = await socialApi.delBlockList(s.usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await s.usersTwok.acct["blocker"].logout({});
    });

    it.each`
      desc                                                                                      | blockedNum | limit
      ${"can paginate a few items with last page having 1 item; verify number of pages[happy]"} | ${9}       | ${2}
      ${"can paginate a few items with last page full; verify number of pages[happy]"}          | ${8}       | ${2}
      ${"can paginate max-limit items with last page having 1 item; verify number of pages"}    | ${100}     | ${33}
      ${"can paginate max-limit items with last page full; verify number of pages"}             | ${100}     | ${25}
    `('$desc', async ({blockedNum, limit}) => {
      s = await setup(blockedNum);

      let testCase = {
        description: `add ${blockedNum} user IDs to the block  list; paginate the blocklist with ${limit} items per page`,
        expected: "the numbers of pages are correct"
      }

      // blocker adds the list of IDs to his/her block list
      for (let i = 0; i < s.blockedIdArray.length; i += maxReqSize) {
        let r = await socialApi.updateBlockList(
          s.usersTwok.acct["blocker"],
          {
            userids: s.blockedIdArray.slice(i, i+maxReqSize),
          }
        );
        socialApi.testStatus(StatusCodes.OK, r);
      }

      // get the actual number of pages
      let actualPageNum = 0;
      let next: string = "";
      let r;
      do {
        r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], { limit: limit, ...(next != "" && { next: next }) });
        next = r.body.nextid;
        actualPageNum = actualPageNum + 1;
      } while (next !== null)

      // verify the page numbers are correct
      const expectedPageNum = Math.ceil(s.blockedIdArray.length / limit);
      socialApi.expectMore(
        () => {expect(actualPageNum).toEqual(expectedPageNum)},
        testCase,
        {
          resp: r,
          additionalInfo: {
            "fail reason": "the number of pages is not correct"
          }
        }
      );
    });

    it.each`
      desc                                                                               | blockedNum | limit
      ${"can paginate a few items with last page having 1 item; verify contents[happy]"} | ${9}       | ${2}
      ${"can paginate a few items with last page full; verify contents[happy]"}          | ${8}       | ${2}
      ${"can paginate max-limit items with last page having 1 item; verify contents"}    | ${100}     | ${33}
      ${"can paginate max-limit items with last page full; verify contents"}             | ${100}     | ${25}
    `('$desc', async ({blockedNum, limit}) => {
      s = await setup(blockedNum);

      let testCase = {
        description: `add ${blockedNum} user IDs to the block list; paginate the blocklist with ${limit} items per page`,
        expected: "the unpaginated and paginated block lists have the same contents"
      }

      // blocker adds the list of users to his/her block list
      for (let i = 0; i < s.blockedIdArray.length; i += maxReqSize) {
        let r = await socialApi.updateBlockList(
          s.usersTwok.acct["blocker"],
          {
            userids: s.blockedIdArray.slice(i, i+maxReqSize),
          }
        );
        socialApi.testStatus(StatusCodes.OK, r);
      }

      // concatenate paginated block list as the actual result
      let next: string = "";
      let actualBL: any[] = [];
      do {
        let r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], { limit: limit, ...(next != "" && { next: next }) });
        next = r.body.nextid;
        // The dynamo db only stores the blocked userid. 
        // So the rest of the fields in the blocklist object get freshly populated if they are newly pulled from the db.
        // on dev, sets the created time as time.Now().UTC().
        // here as the created is dynamic value, we do not check this field
        for (let i = 0; i < r.body.items.length; i++) {
          delete r.body.items[i].created;
        }
        actualBL.push.apply(actualBL, r.body.items);
      } while (next !== null)

      // get the unpaginated block list as the expected result
      let r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], {});
      for (let i = 0; i < r.body.items.length; i++) {
        delete r.body.items[i].created;
      }
      let expectedBL = r.body.items;

      // comparison function for the sort
      function compareBlockedid(a: { blockedid: string; }, b: { blockedid: string; }) {
        if (a.blockedid < b.blockedid) {
          return -1;
        };
        if (a.blockedid > b.blockedid) {
          return 1;
        };
        return 0;
      }

      // sort the expected and actual block lists
      expectedBL.sort(compareBlockedid);
      actualBL.sort(compareBlockedid);

      // verify the unpaginated and paginated block lists have the same contents
      socialApi.expectMore(
        () => {expect(actualBL).toEqual(expectedBL)},
        testCase,
        {
          resp: r,
          additionalInfo: {
            "fail reason": "the unpaginated and paginated block lists do not have the same contents",
            "note": "response body is of the unpaginated list, which is the expected result. actual not shown since it spans multiple pages (and therefore responses)"
          }
        }
      )
    });
  });

  describe('', () => {
    let blockedNum = 4;
    let s: { usersTwok: TwokAccounts, blockedIdArray: string[] };

    beforeAll(async () => {
      s = await setup(blockedNum);

      // blocker adds some users on his/her block list
      const r = await socialApi.updateBlockList(
        s.usersTwok.acct['blocker'],
        {
          userids: s.blockedIdArray,
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);
    });

    afterAll(async () => {
      const r = await socialApi.delBlockList(s.usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await s.usersTwok.acct["blocker"].logout({});
    });

    it('can show all items with limit = 0', async () => {
      let testCase = {
        description: `get blocklist with limit = 0`,
        expected: "all items are in one page"
      };

      // get the unpaginated block list as the expected result
      let r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], {});
      for (let i = 0; i < r.body.items.length; i++) {
        delete r.body.items[i].created;
      }
      let expectedBL = r.body.items;

      // get the block list with limit = 0 as the actual result
      r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], { limit: 0 })
      for (let i = 0; i < r.body.items.length; i++) {
        delete r.body.items[i].created;
      }
      let actualBL = r.body.items;

      // verify the block list with limit = 0 equals the expected list
      socialApi.expectMore(
        () => {expect(actualBL).toEqual(expectedBL)},
        testCase,
        {
          resp: actualBL,
          additionalInfo: {
            "fail reason": "get unexpected items because of unexpected pagination page"
          }
        }
      );
    });

    it('can result in the first page with empty next string', async () => {
      let limitNum = 2;

      let testCase = {
        description: `get blocklist with ${limitNum} items per page and empty next string`,
        expected: "the blocklist remains in the first page"
      };

      // get the block list with limit = ${limitNum} as the expected result
      let r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], { limit: limitNum });
      for (let i = 0; i < r.body.items.length; i++) {
        delete r.body.items[i].created;
      }
      let expectedBL = r.body.items;

      // get the block list with limit = ${limitNum} and empty next as the actual result
      r = await socialApi.getBlockList(s.usersTwok.acct["blocker"], { limit: limitNum, next: "" });
      for (let i = 0; i < r.body.items.length; i++) {
        delete r.body.items[i].created;
      }
      let actualBL = r.body.items;

      // verify the block list with empty next equals the expected list
      socialApi.expectMore(
        () => {expect(actualBL).toEqual(expectedBL)},
        testCase,
        {
          resp: r,
          additionalInfo: {
            "fail reason": `get unexpected items because of unexpected pagination page`
          }
        }
      );
    });
  });
})

describe(`block list${_ds}corner cases[public v2]${_ds}`, () => {
  describe(`modify block list${_ds}`, () => {
    beforeEach(async () => {
      usersTwok = new TwokAccounts(2, ['blocker', 'blockee1']);
      await usersTwok.loginAll({});
    });

    afterEach(async () => {
      // blocker removes blockee1 on his/her block list
      const r = await socialApi.delBlockList(usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await usersTwok.logoutAll({});
    });

    it('cannot add a duplicated ID', async () => {
      let testCase = {
        description: "add an ID that already exists on the the block list",
        expected: "only one instance of the ID is present in the block list"
      }
      // blocker adds blockee1 on his/her block list
      let r = await socialApi.updateBlockList(
        usersTwok.acct['blocker'],
        {
          userids: [usersTwok.acct['blockee1'].publicId],
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // blocker adds blockee1 again
      r = await socialApi.updateBlockList(
        usersTwok.acct['blocker'],
        {
          userids: [usersTwok.acct['blockee1'].publicId],
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      const actualBL = await socialApi.getBlockList(
        usersTwok.acct['blocker'],
        {}
      );

      // verify blocker's block list does not have duplicated entries of blockee1
      const expectedBL = {
        status: StatusCodes.OK,
        body: {
          items: [{ blockedid: usersTwok.acct['blockee1'].publicId }]
        },
      };
      socialApi.expectMore(
        () => {expect(actualBL).toMatchObject(expectedBL)},
        testCase,
        {
          resp: actualBL,
          additionalInfo: {
            "fail reason": "the block list contains IDs other than the one instance of the blocked ID"
          }
        }
      );
    });
  });

  // TODO: types of invalid IDs include invalid 2K ID, and valid 2K ID with no social profile
  describe(`modify block list${_ds}`, () => {
    // create a non-existing id
    let nonExistUserId = '11111111111111111111111111111111';

    beforeEach(async () => {
      usersTwok = new TwokAccounts(1, ['blocker']);
      await usersTwok.loginAll({});
    });

    afterEach(async () => {
      const r = await socialApi.delBlockList(usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await usersTwok.logoutAll({});
    });

    it('cannot add a non-existing 2k user', async () => {
      let testCase = {
        description: "add a non-existing 2k user to the block list",
        expected: "the non-existing 2k user is not present in the block list"
      }
      // blocker adds a non-existing 2k user on his/her block list
      const r = await socialApi.updateBlockList(
        usersTwok.acct['blocker'],
        {
          userids: [nonExistUserId],
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);

      const actualBL = await socialApi.getBlockList(
        usersTwok.acct['blocker'],
        {}
      );

      // verify the non-existing 2k user is not in blocker's block list
      const expectedBL = {
        status: StatusCodes.OK,
        body: {
          items: []
        },
      };
      socialApi.expectMore(
        () => {expect(actualBL).toMatchObject(expectedBL)},
        testCase,
        {
          resp: actualBL,
          additionalInfo: {
            "fail reason": "the block list is not empty"
          }
        }
      );
    });
  });

  describe('', () => {
    beforeEach(async () => {
      usersTwok = new TwokAccounts(1, ['blocker']);
      await usersTwok.loginAll({});
    });

    afterEach(async () => {
      const r = await socialApi.delBlockList(usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await usersTwok.logoutAll({});
    });

    describe(`modify block list${_ds}`, () => {
      it('cannot have empty userid string', async () => {
        let testCase = {
          description: "add empty userid to blocklist",
          expected: "4xx status"
        }

        const actualResp: request.Response = await socialApi.updateBlockList(
          usersTwok.acct["blocker"],
          {
            userids: [''],
          }
        );

        socialApi.expectMore(
          () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
          testCase,
          {
            additionalInfo: {
              "fail reason": "unexpected status code"
            }
          }
        );
      });

      it('cannot add self', async () => {
        let testCase = {
          description: "add self to blocklist",
          expected: "4xx status"
        }

        const actualResp: request.Response = await socialApi.updateBlockList(
          usersTwok.acct["blocker"],
          {
            userids: [usersTwok.acct['blocker'].publicId],
          }
        );

        socialApi.expectMore(
          () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
          testCase,
          {
            additionalInfo: {
              "fail reason": "unexpected status code"
            }
          }
        );
      });

      it('cannot have invalid token', async () => {
        let testCase = {
          description: "add blocklist with invalid token",
          expected: "4xx status"
        }

        let mockUser = new TwokAccount("<EMAIL>", "123", "");
        const actualResp: request.Response = await socialApi.updateBlockList(
          mockUser,
          {
            userids: [usersTwok.acct['blocker'].publicId],
          }
        );

        socialApi.expectMore(
          () => {expect(actualResp.status).toEqual(StatusCodes.UNAUTHORIZED)},
          testCase,
          {
            additionalInfo: {
              "fail reason": "unexpected status code"
            }
          }
        );
      });
    });

    describe(`modify block list${_ds}`, () => {
      let s: {usersTwok: TwokAccounts, blockedIdArray: string[]};

      afterEach(async () => {
        const r = await socialApi.delBlockList(s.usersTwok.acct['blocker']);
        socialApi.testStatus(StatusCodes.OK, r);
      });

      it('cannot add more than 20 users at a time', async () => {
        let testCase = {
          description: "add the number of ids included in one request greater than 20",
          expected: "the block list is empty"
        };
        s = await setup(maxReqSize + 1);

        let actualResp = await socialApi.updateBlockList(
          s.usersTwok.acct["blocker"],
          {
            userids: s.blockedIdArray,
          }
        );

        socialApi.expectMore(
          () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
          testCase,
          {
            resp: actualResp,
            additionalInfo: {
              "fail reason": "unexpected status code"
            }
          }
        );

        // get the actual block list
        const actualBL = await socialApi.getBlockList(
          s.usersTwok.acct["blocker"],
          {}
        );

        // verify the block list is empty
        socialApi.testStatus(StatusCodes.OK, actualBL);
        socialApi.expectMore(
          () => {expect(actualBL.body).toEqual({items: [], nextid: null})},
          testCase,
          {
            resp: actualBL,
            additionalInfo: {
              "fail reason": "some users are present in the block list"
            }
          }
        );
      });

      it.each`
        desc                                | blockedNum
        ${"cannot add max-limit + 1 items"} | ${101}
      `('$desc', async ({ blockedNum }) => {
        s = await setup(blockedNum);

        let testCase = {
          description: `add max-limit number of users to the block list, then attempt to add one more user to the block list`,
          expected: `the max-limit number of users are present in the block list, and the extra user is not present in the block list`
        }

        let maxLimitItems = s.blockedIdArray.slice(0, 100);
        let plusOneItem = s.blockedIdArray.slice(100);

        // blocker adds max-limit number of IDs to his/her block list
        for (let i = 0; i < maxLimitItems.length; i += maxReqSize) {
          const r = await socialApi.updateBlockList(
            s.usersTwok.acct["blocker"],
            {
              userids: maxLimitItems.slice(i, i + maxReqSize),
            }
          );
          socialApi.testStatus(StatusCodes.OK, r);
        }

        // blocker adds one more ID to his/her block list
        const r = await socialApi.updateBlockList(
          s.usersTwok.acct["blocker"],
          {
            userids: plusOneItem,
          }
        );
        socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, r);

        // get the actual block list
        const actualBL = await socialApi.getBlockList(
          s.usersTwok.acct["blocker"],
          {}
        );

        // create an expected array of blockee objects (only max-limit items)
        let blockedIds = [];
        for (let id of maxLimitItems) {
          blockedIds.push(expect.objectContaining({ blockedid: id }));
        }

        // verify the block list contains the expected list of blockee objects
        const expectedBL = {
          status: StatusCodes.OK,
          body: {
            items: expect.arrayContaining(blockedIds),
          },
        };
        socialApi.expectMore(
          () => {expect(actualBL).toMatchObject(expectedBL)},
          testCase,
          {
            resp: actualBL,
            additionalInfo: {
              "fail reason": "the block list does not contain all of the original max-limit number of users"
            }
          }
        );

        // TODO: also need to verify if the actual block list contains exactly max-limit (100) number of users.
        // Currently it checks only if the actual block list "contains" the 100 users.
        // It also need to check if the size of the actual block list is exactly 100.
      });
    });
  });

  describe(`get block list${_ds}`, () => {
    const maxItemPerPage: number = 100;

    beforeEach(async () => {
      usersTwok = new TwokAccounts(2, ['blocker', 'blockee1']);
      await usersTwok.loginAll({});

      // blocker adds blockee1 on his/her block list
      const r = await socialApi.updateBlockList(
        usersTwok.acct['blocker'],
        {
          userids: [usersTwok.acct['blockee1'].publicId],
        }
      );
      socialApi.testStatus(StatusCodes.OK, r);
    });

    afterEach(async () => {
      const r = await socialApi.delBlockList(usersTwok.acct['blocker']);
      socialApi.testStatus(StatusCodes.OK, r);

      await usersTwok.logoutAll({});
    });

    it.each`
      scenario                                    | value
      ${"cannot have limit > max items per page"} | ${maxItemPerPage + 1}
      ${"cannot have empty limit string"}         | ${""}
    `('$scenario', async ({value}) => {
      let testCase = {
        description: `get blocklist with limit = ${value}`,
        expected: "4xx status"
      }

      let actualResp: request.Response;

      actualResp = await socialApi.getBlockList(usersTwok.acct["blocker"], { limit: value });

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNPROCESSABLE_ENTITY)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });

    it('cannot have invalid token', async () => {
      let testCase = {
        description: "get blocklist with invalid token",
        expected: "4xx status"
      }

      let mockUser = new TwokAccount("<EMAIL>", "123", "");

      const actualResp: request.Response = await socialApi.getBlockList(
        mockUser,
        {}
      );

      socialApi.expectMore(
        () => {expect(actualResp.status).toEqual(StatusCodes.UNAUTHORIZED)},
        testCase,
        {
          additionalInfo: {
            "fail reason": "unexpected status code"
          }
        }
      );
    });
  });
});