package apipub

import (
	"net/http"
	"os"
	"testing"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
)

func TestErrors(t *testing.T) {
	g := goblin.Goblin(t)
	os.Setenv("DD_ENV", "test")

	g.<PERSON>cribe("Errors", func() {
		g.It("should return correct values", func() {
			var e errs.SocialErrorInterface
			e = &UnescapedCookieParamError{}
			g.<PERSON>ser<PERSON>(e.GetHttpErrorCode()).Equal(http.StatusBadRequest)
			g.<PERSON>sert(e.GetSocialErrorCode()).Equal(errs.EOapiUnescapedCookieParam)
			g.<PERSON>sert(e.GetStack()).IsNil()

			e = &UnmarshalingParamError{}
			g.<PERSON>sert(e.GetHttpErrorCode()).Equal(http.StatusBadRequest)
			g.Assert(e.GetSocialErrorCode()).Equal(errs.EOapiUnmarshalingParam)
			g.<PERSON>sert(e.GetStack()).IsNil()

			e = &RequiredParamError{}
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusBadRequest)
			g.Assert(e.GetSocialErrorCode()).Equal(errs.EOapiRequireParam)
			g.Assert(e.GetStack()).IsNil()

			e = &RequiredHeaderError{}
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusBadRequest)
			g.Assert(e.GetSocialErrorCode()).Equal(errs.EOApiRequireHeader)
			g.Assert(e.GetStack()).IsNil()

			e = &InvalidParamFormatError{}
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusBadRequest)
			g.Assert(e.GetSocialErrorCode()).Equal(errs.EOapiInvalidParamFormat)
			g.Assert(e.GetStack()).IsNil()

			e = &TooManyValuesForParamError{}
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusBadRequest)
			g.Assert(e.GetSocialErrorCode()).Equal(errs.EOapiTooManyValuesForParam)
			g.Assert(e.GetStack()).IsNil()
		})
	})

}
