import { TwokAccounts } from '../integration/lib/config';
import * as socialApi from '../integration/lib/social-api';


// investigate whether getting a list returns "{}" occasionally instead of the expected "{items:[]}"

describe('', () => {
  let usersTwok: TwokAccounts;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(100);
    await usersTwok.loginAll({});
  });

  afterEach(async () => {
    await usersTwok.logoutAll({});
  });


  it('', async () => {
    // outer loop: account idx
    for (let i = 1; i <= 100; i++) {
      let hasItemCnt = 0;
      let noItemCnt = 0;

      let idx = i.toString().padStart(3, '0');
      console.log(`idx: ${idx} | current test account: ${usersTwok.acct[idx].publicId}`);

      // inner loop: number of times to get list
      for (let j = 0; j < 100; j++) {
        /**
         * uncomment the line you wish to test with.
         */
        // let info = await socialApi.getFriends(usersTwok.acct[idx].accessToken, {});
        // let info = await socialApi.getBlockList(usersTwok.acct[idx].accessToken, {});
        let info = await socialApi.getGroupsInfo(usersTwok.acct[idx]);

        if (!info.body.hasOwnProperty("items")) {
          console.log(`no items! body is: ${JSON.stringify(info.body, null, 4)}`);
          noItemCnt += 1;
        } else {
          hasItemCnt += 1;
        }
      }

      console.log(`no item count: ${noItemCnt} | has item count: ${hasItemCnt}`);
    }
  }, 5000000); // For reference, 500 loops take 200 secs.  Tune the timeout to adjust to the test length.
});