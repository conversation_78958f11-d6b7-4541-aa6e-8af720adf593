-module(t2gp_social).

-include_lib("vernemq_dev/include/vernemq_dev.hrl").
-include_lib("t2gp_social.hrl").

% -behaviour(auth_on_register_hook).
% -behaviour(auth_on_register_m5_hook).
% -behaviour(auth_on_publish_hook).
% -behaviour(auth_on_publish_m5_hook).
% -behaviour(auth_on_subscribe_m5_hook).
% -behaviour(on_client_wakeup_hook).
% -behaviour(on_client_offline_hook).
% -behaviour(on_client_gone_hook).

%% API exports
-export([
    auth_on_register/5,
    auth_on_register_m5/6,
    auth_on_publish/6,
    auth_on_publish_m5/7,
    auth_on_subscribe_m5/4,
    on_client_wakeup/1,
    on_client_offline/1,
    on_client_gone/1
]).

-export([
    auto_subscribe/4,
    check_auth/3,
    send_connection_info/3,
    get_friendid/2,
    check_user/2,
    get_provider/1,
    get_provider_str/1,
    get_productid/1,
    get_username/1
]).

%%====================================================================
%% API functions
%%====================================================================

-define(LOG(Args0), begin
    [Hook | Args1] = Args0,
    Str = atom_to_list(Hook) ++ "(" ++ string:join(["~p" || _ <- Args1], " ") ++ ")~n",
    lager:info(Str, Args1)
end).

%%========================
%%    Register hooks
%%========================
-spec auth_on_register(peer(), subscriber_id(), username(), password(), boolean()) ->
    {error, term()}.
auth_on_register(Peer, SubscriberId, UserName, Password, CleanSession) ->
    ?LOG([auth_on_register, Peer, SubscriberId, UserName, Password, CleanSession]),
    % {error, #{
    %     reason_code => ?PROTOCOL_ERROR,
    %     reason_string => <<"Only MQTTv5 is supported">>
    % }}.
    auth_on_register_m5(Peer, SubscriberId, UserName, Password, CleanSession, "").

check_ts_auth(UserId, Password) ->
    ?LOG([check_ts_auth, UserId, Password]),
    {ok, PreUser} = application:get_env(t2gp_social, predefined_user),
    PredefinedUser = list_to_binary(PreUser),
    lager:info("check_ts_auth_users PreUser=~p PredefinedUser=~p UserId=~p", [
        PreUser, PredefinedUser, UserId
    ]),
    if
        UserId == PredefinedUser ->
            lager:info(
                "check_ts_auth_PredefinedUser matched PreUser=~p PredefinedUser=~p UserId=~p", [
                    PreUser, PredefinedUser, UserId
                ]
            ),
            ok;
        UserId == PreUser ->
            lager:info("check_ts_auth_PreUser matched PreUser=~p PredefinedUser=~p UserId=~p", [
                PreUser, PredefinedUser, UserId
            ]),
            ok;
        true ->
            {_, State} = t2gp_social_db:init(""),
            case t2gp_social_db:handle_call({get_ts_auth, UserId, Password}, "junk", State) of
                error ->
                    error;
                _ ->
                    ok
            end
    end.

-spec auth_on_register_m5(peer(), subscriber_id(), username(), password(), boolean(), map()) -> ok.
auth_on_register_m5(Peer, SubscriberId, InUsername, Password, CleanStart, Properties) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => InUsername}),
        ?LOG([auth_on_register_m5, Peer, SubscriberId, InUsername, Password, CleanStart, Properties]),
        case utils:is_jwt(Password) of
            true ->
                {ok, AllowAlgNone} = application:get_env(t2gp_social, jwt_allow_alg_none),
                case check_auth(AllowAlgNone, InUsername, Password) of
                    {ok, Claims} ->
                        % promote parent account id (AKA DNA full account)
                        Username = get_username(Claims),
                        Tenant = get_provider_str(Password),
                        % create user object if this is the first time
                        lager:info("adding subscriber tenant = ~p user=~p subscriberid=~p", [
                            Tenant, Username, SubscriberId
                        ]),
                        t2gp_social_db:add_subscriber_id(Username, SubscriberId),
                        % let register complete then we can call subscribe
                        timer:apply_after(300, t2gp_social, auto_subscribe, [
                            Tenant, Username, SubscriberId, Claims
                        ]),
                        ok;
                    Error ->
                        lager:info("check_auth error error=~p", [Error]),
                        Error
                end;
            false ->
                % else check ts auth
                check_ts_auth(InUsername, Password)
        end
    end).

-spec check_auth(boolean(), binary(), binary()) -> {ok, map()} | {error, any()}.
check_auth(false, Username, Password) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => Username}),
        IdentityProvider = get_provider(Password),
        case gen_server:call(IdentityProvider, {validate_jwt, Password}) of
            {ok, Claims} ->
                check_user(Username, Claims);
            {error, expired} ->
                lager:info("can't validate jwt for user ~p due to '~p'", [Username, expired]),
                {error, ?E_JWT_EXPIRED};
            {error, Reason} ->
                lager:error("can't validate jwt for user ~p due to '~p'", [Username, Reason]),
                {error, ?E_JWT_VALIDATION_FAILED};
            Error ->
                lager:warning("check_auth unhandled error ~p ", [Error]),
                {error, ?E_INTERNAL_SERVER_ERROR}
        end
    end);
check_auth(true, Username, Password) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => Username}),
        % Check alg=none in JWT header
        try
            [Header, Claims, _] = binary:split(Password, <<".">>, [global]),
            Decoded = jsx:decode(base64url:decode(Header)),
            case proplists:get_value(<<"alg">>, Decoded) of
                <<"none">> ->
                    {ok, maps:from_list(jsx:decode(base64url:decode(Claims)))};
                _ ->
                    check_auth(false, Username, Password)
            end
        of
            Result -> Result
        catch
            Exception:Reason ->
                lager:error("check_auth exception ~p ~p", [Exception, Reason]),
                {error, #{
                    reason_code => ?BAD_USERNAME_OR_PASSWORD,
                    reason_string => <<"internal server error">>
                }}
        end
    end).

-spec check_user(binary(), map()) -> {ok, map()} | {error, term()}.
check_user(Username, Claims) ->
    % Validate "sub" in claims equals Username
    Subject = maps:get(<<"sub">>, Claims, undefined),
    ParentAccountID = maps:get(<<"pai">>, Claims, undefined),
    NameID = maps:get(<<"nameid">>, Claims, undefined),
    if
        Subject == Username -> {ok, Claims};
        ParentAccountID == Username -> {ok, Claims};
        NameID == Username -> {ok, Claims};
        true -> {error, ?E_JWT_SUB_USERNAME_MISMATCH}
    end.

-spec get_provider(binary()) -> t2gp_social_dna | t2gp_social_pd | t2gp_social_rsg.
get_provider(Password) ->
    case binary:split(Password, <<".">>, [global]) of
        [_, Claims, _] ->
            Decoded = jsx:decode(base64url:decode(Claims)),
            case proplists:get_value(<<"iss">>, Decoded) of
                <<"privatedivision.com">> ->
                    t2gp_social_pd;
                <<"https://signin.rockstargames.com">> ->
                    t2gp_social_rsg;
                _ ->
                    t2gp_social_dna
            end;
        _ ->
            t2gp_social_dna
    end.

%<<"dna">> | <<"pdi">> || <<"rsg">> || <<"ukn">>
-spec get_provider_str(binary()) -> string().
get_provider_str(Password) ->
    case get_provider(Password) of
        t2gp_social_dna ->
            <<"dna">>;
        t2gp_social_pd ->
            <<"pdi">>;
        t2gp_social_rsg ->
            <<"rsg">>;
        _ ->
            <<"unk">>
    end.

%%========================
%%    Publish hooks
%%========================
-spec auth_on_publish(
    username(), subscriber_id(), qos(), topic(), payload(), flag()
) -> ok | {error, term()}.
auth_on_publish(UserName, SubscriberId, QoS, Topic, Payload, IsRetain) ->
    ?LOG([auth_on_publish, UserName, SubscriberId, QoS, Topic, Payload, IsRetain]),
    ok.

-spec auth_on_publish_m5(
    username(), subscriber_id(), qos(), topic(), payload(), flag(), properties()
) -> ok | {error, term()}.
auth_on_publish_m5(
    Username, _SubscriberId, _QoS, [<<"lwt">>, UserId] = _Topic, _Payload, _IsRetain, _Properties
) when Username == UserId ->
    % allow last will and testament publish to go through
    ok;
auth_on_publish_m5(_Username, _SubscriberId, _QoS, _Topic, _Payload, _IsRetain, _Properties) ->
    % ?LOG([auth_on_publish_m5, Username, SubscriberId, QoS, Topic, Payload, IsRetain, Properties]),
    {error, #{
        reason_code => ?NOT_AUTHORIZED,
        reason_string => <<"Not authorized">>
    }}.

%%=========================
%%    Subscribe hooks
%%=========================
-spec auth_on_subscribe_m5(username(), subscriber_id(), [topic()], properties()) ->
    ok | {error, term()}.
auth_on_subscribe_m5(_Username, _SubscriberId, _Topics, _Properties) ->
    % ?LOG([auth_on_subscribe_m5, Username, SubscriberId, Topics, Properties]),
    % Never allow subscription
    {error, #{
        reason_code => ?NOT_AUTHORIZED,
        reason_string => <<"Not authorized">>
    }}.

-spec on_client_wakeup(subscriber_id()) -> ok.
on_client_wakeup(_SubscriberId) ->
    % lager:info("t2gp_social:on_client_wakeup ~p", [SubscriberId]),
    ok.

-spec on_client_offline(subscriber_id()) -> ok.
on_client_offline({_, ClientId} = SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        lager:info("t2gp_social:on_client_offline ~p", [SubscriberId]),
        t2gp_social_db:del_subscriber_id(SubscriberId),
        ok
    end).

-spec on_client_gone(subscriber_id()) -> ok.
on_client_gone({_, ClientId} = SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        lager:info("t2gp_social:on_client_gone ~p", [SubscriberId]),
        t2gp_social_db:del_subscriber_id(SubscriberId),
        ok
    end).

-spec send_connection_info(binary(), username(), subscriber_id()) -> ok | {error, term()}.
send_connection_info(Tenant, UserId, {_, ClientId} = SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        Topic = [Tenant, <<"user">>, UserId],
        % connection info for debugging purposes
        JSON = [
            {<<"type">>, <<"connectionInfo">>},
            {<<"node">>, node()}
        ],
        Payload = jsx:encode(JSON),
        Opts = #{},
        lager:info("t2gp_social_vmq:publish(~p, ~p, ~p, ~p)", [SubscriberId, Topic, Payload, Opts]),
        t2gp_social_vmq:publish(SubscriberId, Topic, Payload, Opts)
    end).

-spec get_friendid(list(), any()) -> binary().
get_friendid(Tenant, Friend) ->
    case proplists:get_value(<<"sk">>, Friend) of
        undefined -> <<>>;
        Value -> binary:replace(Value, utils:concat([Tenant, <<"#friend#">>], binary), <<>>)
    end.

-spec get_productid(map()) -> binary().
get_productid(Claims) ->
    case maps:get(<<"iss">>, Claims, <<"">>) of
        <<"privatedivision.com">> ->
            maps:get(<<"aud">>, Claims, <<"">>);
        <<"https://signin.rockstargames.com">> ->
            <<"default">>;
        _ ->
            maps:get(<<"pid">>, Claims, <<"">>)
    end.

-spec auto_subscribe(binary(), username(), subscriber_id(), map()) -> ok | {error, term()}.
auto_subscribe(Tenant, UserId, {_, ClientId} = SubscriberId, Claims) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        t2gp_social_apm:tags(#{userid => UserId, clientid => ClientId}),
        ProductId = get_productid(Claims),
        Topic = [Tenant, <<"user">>, UserId],
        lager:info("subscribing user=~p subscriberid=~p to topic=~p", [UserId, SubscriberId, Topic]),
        t2gp_social_vmq:subscribe(SubscriberId, Topic),
        send_connection_info(Tenant, UserId, SubscriberId),
        % subscribe to friends
        case t2gp_social_db:get_friends(Tenant, UserId) of
            {ok, Friends} ->
                % create list of topics fmt.Sprintf("%s/prod/%s/%s/user/%s/presence", tenant, presence.Productid, utils.GetEnvironment(), presence.Userid)
                StatusFriends = lists:filter(
                    fun(X) -> proplists:get_value(<<"status">>, X) == <<"friend">> end, Friends
                ),
                FriendPresenceTopics = [
                    [
                        Tenant,
                        <<"prod">>,
                        ProductId,
                        utils:get_env(),
                        <<"user">>,
                        get_friendid(Tenant, H),
                        <<"presence">>
                    ]
                 || H <- StatusFriends
                ],
                lager:info("subscribing to friends ~p", [FriendPresenceTopics]),
                SubResult1 = [t2gp_social_vmq:subscribe(UserId, H) || H <- FriendPresenceTopics],
                lager:info("subscribing to friends results ~p", [SubResult1]),
                ok;
            Error2 ->
                lager:error("can't get get friends ~p ~p", [UserId, Error2]),
                Error2
        end,
        % subscribe to groups
        case t2gp_social_db:get_groups(Tenant, UserId, ProductId) of
            {ok, Groups} ->
                % create list of topics fmt.Sprintf("%s/prod/%s/%s/group/%s", tenant, group.Productid, utils.GetEnvironment(), group.Groupid)
                GroupTopics = [
                    [Tenant, <<"prod">>, ProductId, utils:get_env(), <<"group">>, H]
                 || H <- Groups
                ],
                lager:info("subscribing to groups ~p", [GroupTopics]),
                SubResult2 = [t2gp_social_vmq:subscribe(SubscriberId, H) || H <- GroupTopics],
                lager:info("subscribing to groups results ~p", [SubResult2]),
                ok;
            Error3 ->
                lager:error("can't get get groups ~p ~p ~p ~p", [Tenant, UserId, ProductId, Error3]),
                Error3
        end
    end).

-spec get_username(map()) -> username().
get_username(Claims) ->
    maps:get(
        <<"pai">>,
        Claims,
        maps:get(
            <<"nameid">>,
            Claims,
            maps:get(<<"sub">>, Claims, undefined)
        )
    ).
