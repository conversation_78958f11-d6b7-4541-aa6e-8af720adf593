import { render } from '@testing-library/svelte';
import Tooltip from '../Tooltip.svelte';

describe('Tooltip', () => {
  it('should render UI', () => {
    const TOOL_TIP_VALUE = 'tooltip value';
    const { container } = render(Tooltip, {
      props: {
        value: TOOL_TIP_VALUE,
      },
    });

    const element: HTMLElement = container.querySelector('[data-tooltip]');
    expect(element.dataset.tooltip).toEqual(TOOL_TIP_VALUE);
  });
});
