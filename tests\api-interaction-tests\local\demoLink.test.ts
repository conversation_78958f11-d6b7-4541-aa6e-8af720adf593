import { TwokAccounts, SteamAccounts, EpicAccounts, SwitchAccounts, XbxAccounts, Xb1Accounts,
         Ps4Accounts, Ps5Accounts, PlatformAccounts } from '../integration/lib/config';


// NOTE the following 3 caveats about linking/unlinking:
// 1. unlinking would invalidate the unlinked platform token.
// 2. xbx and xb1, ps4 and ps5, are consider the same "types" and cannot be linked to a parent account at the same time
// 3. ps4/ps5 accounts have permanent binding to 2k accounts once linked.  They can't be linked to different 2k accounts.

// NOTE: Switch accounts can't be generated at this moment (2024 March).

// NOTE: For practical purposes, ps4 and ps5 accounts can't be used, since they have permanent binidng to 2k accounts (2024 March).

// NOTE: The "tests" are meant to be run one by one and observe the results.  Some are meant to "fail".

let usersTwok: TwokAccounts;
let usersSteam: SteamAccounts;
let usersEpic: EpicAccounts;
// let usersSwitch: SwitchAccounts;
let usersXbx: XbxAccounts;
let usersXb1: Xb1Accounts;
let usersPs4: Ps4Accounts;
let usersPs5: Ps5Accounts;

function printTwokAccts(ta: TwokAccounts) {
  let acctInfoStr = "";
  for (let a of Object.values(ta.acct)) {
    acctInfoStr += `${a.email} | ${a.publicId} | ${a.displayName} | ${a.accessToken.slice(0,3)}...${a.accessToken.slice(-3)}\n`;
  }
  console.log(acctInfoStr);
}

function printPlatformAccts(pa: PlatformAccounts) {
  let acctInfoStr = "";
  for (let a of Object.values(pa.acct)) {
    acctInfoStr += `${a.platformId} | ${a.publicId} | ${a.alias} | ${a.accessToken.slice(0,3)}...${a.accessToken.slice(-3)}\n`;
  }
  console.log(acctInfoStr);
}

beforeAll(async () => {
  // login
  let labels = ["leader", "m1"];

  usersTwok = new TwokAccounts(labels.length, labels);
  await usersTwok.loginAll({});

  usersSteam = new SteamAccounts(labels.length, labels);
  await usersSteam.loginAll();

  usersEpic = new EpicAccounts(labels.length, labels);
  await usersEpic.loginAll();

  // usersSwitch = new SwitchAccounts(labels.length, labels);
  // await usersSwitch.loginAll();

  usersXbx = new XbxAccounts(labels.length, labels);
  await usersXbx.loginAll();

  usersXb1 = new Xb1Accounts(labels.length, labels);
  await usersXb1.loginAll();

  usersPs4 = new Ps4Accounts(labels.length, labels);
  await usersPs4.loginAll();

  usersPs5 = new Ps5Accounts(labels.length, labels);
  await usersPs5.loginAll();


  // print account info
  printTwokAccts(usersTwok);
  printPlatformAccts(usersSteam);
  printPlatformAccts(usersEpic);
  // printPlatformAccts(usersSwitch);
  printPlatformAccts(usersXbx);
  printPlatformAccts(usersXb1);
  printPlatformAccts(usersPs4);
  printPlatformAccts(usersPs5);
}, 60000);

afterAll(async () => {
  // unlink
  await usersSteam.unlinkParentAccts();
  await usersEpic.unlinkParentAccts();
  // await usersSwitch.unlinkParentAccts();
  await usersXbx.unlinkParentAccts();
  await usersXb1.unlinkParentAccts();
  await usersPs4.unlinkParentAccts();
  await usersPs5.unlinkParentAccts();

  // logout
  await usersTwok.logoutAll({});
  await usersSteam.logoutAll();
  await usersEpic.logoutAll();
  // await usersSwitch.logoutAll();
  await usersXbx.logoutAll();
  await usersXb1.logoutAll();
  await usersPs4.logoutAll();
  await usersPs5.logoutAll();
}, 60000);


describe('', () => {
  /**
   * platform links twok
   *
   * This shows API usage in the context of a platform account.
   *
   * This also demonstrates caveat #1.
   */
  it.each`
    platform
    ${"steam"}
    ${"epic"}
    ${"switch"}
    ${"xbx"}
    ${"xb1"}
    ${"ps4"}
    ${"ps5"}
  `('$platform links twok', async ({platform}) => {
    let usersPlatform: PlatformAccounts;
    switch (platform) {
      case "steam":
        usersPlatform = usersSteam;
        break;
      case "epic":
        usersPlatform = usersEpic;
        break;
      // case "switch":
      //   usersPlatform = usersSwitch;
      //   break;
      case "xbx":
        usersPlatform = usersXbx;
        break;
      case "xb1":
        usersPlatform = usersXb1;
        break;
      case "ps4":
        usersPlatform = usersPs4;
        break;
      case "ps5":
        usersPlatform = usersPs5;
        break;
      default:
        throw new Error(`Unknown platform [${platform}]`);
    }

    let r;

    /**
     * add links
     */
    await usersPlatform.acct["leader"].linkParent(usersTwok.acct["leader"]);

    /**
     * get links
     */
    r = await usersPlatform.acct["leader"].getLinkedParent();
    console.log(r.body);

    r = await usersPlatform.acct["leader"].getLinkedAccounts("full");
    console.log(r.body);

    /**
     * remove links
     */
    // this invalidates the platform token
    await usersPlatform.acct["leader"].unlinkParent();

    // platform accounts login again
    await new Promise(x => setTimeout(x, 1000));
    await usersPlatform.acct["leader"].login();

    // Note that the next get links would NOT work without the above platform account login.
    // See caveat #1.

    /**
     * get links
     */
    r = await usersPlatform.acct["leader"].getLinkedParent();
    console.log(r.body);

    r = await usersPlatform.acct["leader"].getLinkedAccounts("full");
    console.log(r.body);
  });


  /**
   * twok links platform
   *
   * This shows API usage in the context of a twok account.
   */
  it.each`
    platform
    ${"steam"}
    ${"epic"}
    ${"switch"}
    ${"xbx"}
    ${"xb1"}
    ${"ps4"}
    ${"ps5"}
  `('twok links $platform', async ({platform}) => {
    let usersPlatform: PlatformAccounts;
    let linkType: string;
    switch (platform) {
      case "steam":
        usersPlatform = usersSteam;
        linkType = platform;
        break;
      case "epic":
        usersPlatform = usersEpic;
        linkType = platform;
        break;
      // case "switch":
      //   usersPlatform = usersSwitch;
      //   linkType = "nintendo";
      //   break;
      case "xbx":
        usersPlatform = usersXbx;
        linkType = "xbl";
        break;
      case "xb1":
        usersPlatform = usersXb1;
        linkType = "xbl";
        break;
      case "ps4":
        usersPlatform = usersPs4;
        linkType = "psn";
        break;
      case "ps5":
        usersPlatform = usersPs5;
        linkType = "psn";
        break;
      default:
        throw new Error(`Unknown platform [${platform}]`);
    }

    let r;

    /**
     * add links
     */
    await usersTwok.acct["leader"].linkPlatformAccount(usersPlatform.acct["leader"]);

    /**
     * get links
     */
    r = await usersTwok.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);

    /**
     * remove links
     */
    // this invalidates the platform token
    await usersTwok.acct["leader"].unlinkPlatformAccount(linkType);

    // platform accounts login again
    await new Promise(x => setTimeout(x, 1000));
    await usersPlatform.acct["leader"].login();

    // Note that the next get links would still work without the above platform account login,
    // since the get link is done on the twok account.
    // The login is done for the unlinking in "afterAll".

    /**
     * get links
     */
    r = await usersTwok.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);
  });


  /**
   * twok link multiple platforms
   *
   * This shows API usage for a twok account linking to multiple platform accounts.
   *
   * This also demonstrates caveat #2.
   */
  it('twok links multiple platforms', async () => {
    let r;

    /**
     * add links
     */
    await usersTwok.acct["leader"].linkPlatformAccount(usersSteam.acct["leader"]);
    await usersTwok.acct["leader"].linkPlatformAccount(usersEpic.acct["leader"]);

    // NOTE:
    // Only one account from an online service type can be linked to a full account.
    // Xbx and xb1 are of the same online service type (xbl); ps4 and ps5 belongs to psn.
    // For example, when a full account is linked to a xbx account, it can't be linked to a xb1 account.
    // uncomment one or both of the following to see the difference.
    await usersTwok.acct["leader"].linkPlatformAccount(usersXbx.acct["leader"]);
    // await usersTwok.acct["leader"].linkPlatformAccount(usersXb1.acct["leader"]);

    /**
     * get links
     */
    r = await usersTwok.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);

    /**
     * remove links
     */
    // unlink steam
    // this invalidates the platform token
    await usersTwok.acct["leader"].unlinkPlatformAccount("steam");

    // platform accounts login again
    await new Promise(x => setTimeout(x, 1000));
    await usersSteam.acct["leader"].login();

    // unlink epic
    // this invalidates the platform token
    await usersTwok.acct["leader"].unlinkPlatformAccount("epic");

    // platform accounts login again
    await new Promise(x => setTimeout(x, 1000));
    await usersEpic.acct["leader"].login();

    // Note that the next get links would still work without the above platform account login,
    // since the get link is done on the twok account.
    // The login is done for the unlinking in "afterAll".

    /**
     * get links
     */
    r = await usersTwok.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);
  });


  /**
   * twok links different accounts of platform
   *
   * This shows API usage for a twok account linking a different account.
   *
   * This also demonstrates caveat #3.
   */
  it.each`
    platform
    ${"steam"}
    ${"epic"}
    ${"switch"}
    ${"xbx"}
    ${"xb1"}
    ${"ps4"}
    ${"ps5"}
  `('twok links different accounts of $platform', async ({platform}) => {
    let usersPlatform: PlatformAccounts;
    let linkType: string;
    switch (platform) {
      case "steam":
        usersPlatform = usersSteam;
        linkType = platform;
        break;
      case "epic":
        usersPlatform = usersEpic;
        linkType = platform;
        break;
      // case "switch":
      //   usersPlatform = usersSwitch;
      //   linkType = "nintendo";
      //   break;
      case "xbx":
        usersPlatform = usersXbx;
        linkType = "xbl";
        break;
      case "xb1":
        usersPlatform = usersXb1;
        linkType = "xbl";
        break;
      case "ps4":
        usersPlatform = usersPs4;
        linkType = "psn";
        break;
      case "ps5":
        usersPlatform = usersPs5;
        linkType = "psn";
        break;
      default:
        throw new Error(`Unknown platform [${platform}]`);
    }

    let r;

    /**
     * add links
     */
    await usersTwok.acct["leader"].linkPlatformAccount(usersPlatform.acct["leader"]);
    await usersTwok.acct["leader"].unlinkPlatformAccount(linkType);

    // platform accounts login again
    await new Promise(x => setTimeout(x, 1000));
    await usersPlatform.acct["leader"].login();

    await usersTwok.acct["leader"].linkPlatformAccount(usersPlatform.acct["m1"]);

    /**
     * get links
     */
    r = await usersTwok.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);
  });


  /**
   * getLinkedAccounts
   *
   * This shows "getLinkedAccounts" called by twok or platform accounts returns the same results.
   */
  it('getLinkedAccounts', async () => {
    let r;

    /**
     * add links
     */
    await usersSteam.acct["leader"].linkParent(usersTwok.acct["leader"]);

    /**
     * get links
     */

    // Note that the first set of 3 calls and the second set of 3 calls produce the same results.

    r = await usersSteam.acct["leader"].getLinkedAccounts();
    console.log(r.body);

    r = await usersSteam.acct["leader"].getLinkedAccounts("full");
    console.log(r.body);

    r = await usersSteam.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);


    r = await usersTwok.acct["leader"].getLinkedAccounts();
    console.log(r.body);

    r = await usersTwok.acct["leader"].getLinkedAccounts("full");
    console.log(r.body);

    r = await usersTwok.acct["leader"].getLinkedAccounts("platform");
    console.log(r.body);

    /**
     * remove links
     */
    // this invalidates the platform token
    await usersSteam.acct["leader"].unlinkParent();

    // platform accounts login again
    await new Promise(x => setTimeout(x, 1000));
    await usersSteam.acct["leader"].login();
  });
});