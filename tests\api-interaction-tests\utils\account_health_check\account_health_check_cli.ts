import {
  allTwokAcctHealthCheck
} from './account_health_check';


/**
 * NOTE: while this can be called directly from a temrinal, this was created to be called from the
 * account management tool.
 */

let replaceBadAccount = false;
let owner = "";

if (process.argv[2] == "replace") {
  replaceBadAccount = true;
}

if (process.argv[3]) {
  owner = process.argv[3];
}

allTwokAcctHealthCheck(
  // list of health checks to be performed
  [
    "block",
    "friend",
    "group",
    "childAccount"
  ],
  // replace bad accounts
  replaceBadAccount,
  // account owner
  owner,
  // don't dump log to console
  false
);