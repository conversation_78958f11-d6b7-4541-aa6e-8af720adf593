name: Run Loadtest

on:
  workflow_dispatch:
    inputs:
      commit_sha:
        type: string
        description: Commit sha of deployed env (If manual run, use value of "loadtesting" env from pinned in \#t2gp-social-releases )
        required: true
permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  setup-loadtest-env:
    name: 'Setup Loadtest env'
    runs-on: [t2gp-arc-linux]
    steps:
      - name: Dispatch Loadtest Env GH Run
        uses: take-two-t2gp/dispatcherchecker@v1
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          repo: t2gp-social-service-infrastructure
          branch: master
          workflow: scheduled-loadtest-env-setup.yaml
          msg: "Deploying Loadtest Environment"
          wfinputs: '"shard_count":"3","ddb_min_read":"100","ddb_min_write":"100"'
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Scale up Locust API Workers
        run: |
          kubectl scale deployment -n locust t2gp-social-service-api-locust-worker --replicas=100 --context t2gp-testing
      - name: Wait until workers are ready
        timeout-minutes: 5
        run: |
          #!/bin/bash

          # URL of the endpoint
          url="https://t2gp-social-service-api-locust.d2dragon.net/stats/requests"

          while true; do
              # Make the HTTP request and store the response
              response=$(curl -s "$url")

              # Filter the "workers" array for objects where "state" is "ready"
              ready_workers_count=$(echo "$response" | jq '[.workers[] | select(.state == "ready")] | length')

              # Check if the number of ready workers is 100
              if [[ "$ready_workers_count" -ge 100 ]]; then
                  echo "Ready workers have reached 100 or more. Exiting..."
                  break
              else
                  echo "Current number of ready workers: $ready_workers_count"
              fi

              # Wait for a short period before making the next request
              sleep 1
          done


  kickoff-loadtest:
    needs: [setup-loadtest-env]
    name: 'Kickoff loadtest'
    runs-on: [t2gp-arc-linux]
    steps:
      - name: Check out code
        uses: actions/checkout@v3
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Kickoff test
        working-directory: ".github/assets"
        run: |
          set -x
          # Reset stats first
          curl -L https://t2gp-social-service-api-locust.d2dragon.net/reset

          # Record current time
          recorded_date=$(date -u +%Y-%m-%d_%H-%M-%SZ)
          curl -L -X POST -H "Content-Type: application/x-www-form-urlencoded" --data-binary "@loadtest-config.form" https://t2gp-social-service-api-locust.d2dragon.net/swarm
          sleep 600
          curl -L "https://t2gp-social-service-api-locust.d2dragon.net/stats/report?download=1" -o report.html
          aws s3 cp ./report.html "s3://t2gp-locust/t2gp-social-service-ci-loadtest-reports/API_${recorded_date}_${{inputs.commit_sha}}.html"
  teardown-loadtest-env:
    needs: [kickoff-loadtest]
    name: 'Teardown loadtest env'
    runs-on: [t2gp-arc-linux]
    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Scale up Locust API Workers
        run: |
          kubectl scale deployment -n locust t2gp-social-service-api-locust-worker --replicas=0 --context t2gp-testing
      - name: Dispatch Loadtest Env Teardown GH Run
        uses: take-two-t2gp/dispatcherchecker@v1
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          repo: t2gp-social-service-infrastructure
          branch: master
          workflow: scheduled-loadtest-env-teardown.yaml
          msg: "Destroying Loadtest Environment"
  # clean-up:
  #   name: ''