package api

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	api2 "github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"

	"github.com/aws/aws-sdk-go-v2/aws"
	ulidv2 "github.com/oklog/ulid/v2"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipriv"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/store"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// SocialPrivateAPI social api struct
type SocialPrivateAPI struct {
	Ds         store.DataStoreInterface
	Cfg        *config.Config
	Cache      cache.RedisCacheInterface
	Monitor    *health.ServiceMonitor
	HttpClient utils.HTTPClientInterface
	Tele       telemetry.TelemetryInterface
	Id         identity.IdentityInterface
}

var _ apipriv.ServerInterface = &SocialPrivateAPI{}

// NewSocialPrivateAPI create private social api object
func NewSocialPrivateAPI(cfg *config.Config, db store.DataStoreInterface, rdb cache.RedisCacheInterface, sm *health.ServiceMonitor, t telemetry.TelemetryInterface, id identity.IdentityInterface) *SocialPrivateAPI {

	privapi := &SocialPrivateAPI{
		Cfg:        cfg,
		Ds:         db,
		Cache:      rdb,
		Monitor:    sm,
		HttpClient: http.DefaultClient,
		Tele:       t,
		Id:         id,
	}

	return privapi
}
func redact(in string) string {
	if len(in) >= 10 {
		return fmt.Sprintf("%s********%s", in[0:3], in[len(in)-3:len(in)-1])
	}
	return "********"
}

// GetConfig retrive server config
func (pApi *SocialPrivateAPI) GetConfig(w http.ResponseWriter, r *http.Request) {
	redacted := *pApi.Cfg

	// redact secrets
	redacted.AppBasicAuth = redact(redacted.AppBasicAuth)
	redacted.AppSecret = redact(redacted.AppSecret)
	redacted.VMQApiKey = redact(redacted.VMQApiKey)

	utils.WriteJsonResponse(w, r, http.StatusOK, &redacted)
}

// GetVmqVersion retrive the vmq plugin version
func (pApi *SocialPrivateAPI) GetVmqVersion(w http.ResponseWriter, r *http.Request) {
	result, err := messenger.VMQApiCall(r.Context(), pApi.Cfg, "version", messenger.Params{})
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVmqGeneric))
		return
	}
	if result != nil && len(*result) > 0 {
		api.ReturnOK(w, r, (*result)[0])
		return
	} else {
		api.ReturnOK(w, r, map[string]interface{}{})
		return
	}
}

func (pApi *SocialPrivateAPI) DecodeUlid(w http.ResponseWriter, r *http.Request, ulid apipriv.Ulid) {
	log := logger.FromContext(r.Context())
	id, err := ulidv2.Parse(ulid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to parse ulid")
		errs.Return(w, r, errs.New(http.StatusBadRequest, errs.EUlidParse))
		return
	}

	ts := int64(id.Time() / 1000)

	response := map[string]interface{}{
		"createdDate": time.Unix(ts, 0).UTC(),
	}
	api.ReturnOK(w, r, response)
}

// GetVmqStatus retrive the vmq status
func (pApi *SocialPrivateAPI) GetVmqStatus(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())
	result, err := messenger.VMQStatus(r.Context(), pApi.Cfg)
	if err != nil {
		log.Error().Err(err).Msgf("vmq server error - falied to get status")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVmqAlertError))
		return
	}
	var response interface{}
	responseCode := http.StatusOK
	if result != nil {
		api.ReturnRaw(w, r, responseCode, constants.KApplicationJson, []byte(*result))
		return
	} else {
		response = []string{}
	}
	utils.WriteJsonResponse(w, r, responseCode, response)
}

// GetVmqSessions retrive the vmq plugin version
func (pApi *SocialPrivateAPI) GetVmqSessions(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())
	result, err := messenger.VMQSessions(r.Context(), pApi.Cfg)
	if err != nil {
		log.Error().Err(err).Msgf("vmq server error - failed to get vmq sessions")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVmqAlertError))
		return
	}
	api.ReturnOK(w, r, result)
}

// MakeFriend create friendship
func (pApi *SocialPrivateAPI) MakeFriend(w http.ResponseWriter, r *http.Request, params apipriv.MakeFriendParams) {
	log := logger.FromContext(r.Context())
	userid := strings.TrimSpace(params.Id1)
	friendid := strings.TrimSpace(params.Id2)

	status1, err1 := pApi.makeFriendWithCache(r.Context(), userid, friendid, "", false, 0)
	if err1 != nil {
		log.Error().Err(err1).Msgf("failed to make friendship for user %s and friend %s", userid, friendid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
		return
	}
	if status1 == "pending" {
		//make the call a second time to force friendship
		status1, err1 = pApi.makeFriendWithCache(r.Context(), friendid, userid, "", false, 0)
		if err1 != nil {
			log.Error().Err(err1).Msgf("failed to force friendship for user %s and friend %s", userid, friendid)
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
			return
		}
	}
	// status2, err2 := pApi.makeFriendWithCache(r.Context(), friendid, userid, "", false, 0)
	// if err2 != nil {
	// 	log.Error().Err(err2).Msgf("failed to make friendship for friend %s and user %s", friendid, userid)
	// 	errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
	// 	return
	// }

	// messenger.Subscribe(r.Context(), friendid, "presence/"+userid)
	// messenger.Subscribe(r.Context(), userid, "presence/"+friendid)

	api.ReturnOK(w, r, map[string]string{
		"status1": status1,
		"status2": status1,
	})
}

// MakeUnfriend destroy friendship
func (pApi *SocialPrivateAPI) MakeUnfriend(w http.ResponseWriter, r *http.Request, params apipriv.MakeUnfriendParams) {
	log := logger.FromContext(r.Context())
	err := pApi.Cache.MakeUnfriend(r.Context(), params.Id2, params.Id1)
	if err != nil {
		log.Error().Err(err).Msgf("failed to unfriend user %s and user %s", params.Id2, params.Id1)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
		return
	}

	api.ReturnOK(w, r, map[string]string{
		"status": "OK",
	})
}

// GetVmqSubscriptions list mqtt subscriptions for a user
func (pApi *SocialPrivateAPI) GetVmqSubscriptions(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	log := logger.FromContext(r.Context())
	result, err := messenger.GetSubscriptions(r.Context(), pApi.Cfg, userid)
	if err != nil {
		log.Error().Err(err).Msgf("could not get vmq subscriptions for user %s", userid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVmqGetSubscriptionError))
		return
	}
	api.ReturnOK(w, r, result)
}

// DeleteVmqSubscriptions clear all mqtt subscriptions for a user
func (pApi *SocialPrivateAPI) DeleteVmqSubscriptions(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	log := logger.FromContext(r.Context())
	err := messenger.DeleteSubscriptions(r.Context(), pApi.Cfg, userid)
	if err != nil {
		log.Error().Err(err).Msgf("could not delete subscriptions for user %s", userid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVmqDeleteSubscriptionError))
		return
	}

	api.ReturnOK(w, r, map[string]string{
		"status": "ok",
	})
}

func (pApi *SocialPrivateAPI) GetDevError(w http.ResponseWriter, r *http.Request, errorid apipriv.Errorid) {
	code, err := strconv.ParseInt(errorid, 10, 32)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusBadRequest, errs.EHttp+http.StatusBadRequest))
		return
	}
	if code == 500 {
		// crash for real
		var value *int = nil
		*value = 1
	}

	errs.Return(w, r, errs.New(int(code), errs.EHttp+errs.SocialError(code)))
}

func (pApi *SocialPrivateAPI) GetDynamoDBUserData(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	log := logger.FromContext(r.Context())
	userIds := []string{userid}
	results, err := pApi.Ds.GetUserProfiles(r.Context(), userIds)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get dynamo user data for %s", userid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
		return
	}

	var items []interface{}
	if results != nil {
		for _, i := range *results {
			items = append(items, i)
		}
	}

	response := ListResponse{}
	response.Items = items
	api.ReturnOK(w, r, response)
}

func (pApi *SocialPrivateAPI) GetAllGroupsForUser(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	//This call doesn't work anymore with the new Redis key format.
	// log := logger.get(r)

	// limit := int64(100)
	// next := ""

	// result, next, err := pApi.Cache.GetUserGroups(r.Context(), string(userid), "", &limit, &next)
	// if err != nil {
	// 	log.Error().Err(err).Msgf("GetGroups failed")
	// 	errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
	// 	return
	// }

	// var items []interface{}
	// if result != nil {
	// 	for _, i := range *result {
	// 		items = append(items, i)
	// 	}
	// }

	// linksResult := ListResponse{
	// 	Items: items,
	// 	Next:  &next,
	// }

	// utils.WriteJsonResponse(w, r, http.StatusOK, linksResult)
	utils.WriteJsonResponse(w, r, http.StatusNotImplemented, nil)
}

func (pApi *SocialPrivateAPI) SetUserDefinedPresence(w http.ResponseWriter, r *http.Request, params apipriv.SetUserDefinedPresenceParams) {
	log := logger.FromContext(r.Context())
	// decode request
	var presenceReq apipriv.UpdateUserPresenceStatusRequest
	if !api.DecodeBody(w, r, &presenceReq) {
		return
	}

	if params.ClearPresence != nil && bool(*params.ClearPresence) {
		if presenceReq.Userid == "" {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidParams))
			return
		}
		err := pApi.ClearPresence(r.Context(), presenceReq.Userid, presenceReq.Productid)
		if err != nil {
			log.Error().Err(err).Msgf("failed to clear presence")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceGetUserPresenceFailed))
			return
		}
		api.ReturnEmptyOK(w, r)
		return
	}

	if !apipub.IsValidPresenceStatus(string(presenceReq.Status)) {
		log.Info().Msgf("invalid presence status")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidStatus))
		return
	}

	presence := &apipub.PresenceResponse{
		Userid:       presenceReq.Userid,
		Status:       apipub.PresenceStatus(presenceReq.Status),
		RichPresence: presenceReq.RichPresence,
		Priority:     apipub.PresesencePriorityUserSet,
		Timestamp:    time.Now().UTC(),
	}

	pApi.SavePresenceAndBroadcast(r.Context(), presence)

	api.ReturnEmptyOK(w, r)
}

func (pApi *SocialPrivateAPI) UpdateUserPresence(w http.ResponseWriter, r *http.Request, params apipriv.UpdateUserPresenceParams) {
	log := logger.FromContext(r.Context())
	// decode request
	var presenceReq apipriv.UpdateUserPresenceStatusRequest
	if !api.DecodeBody(w, r, &presenceReq) {
		return
	}

	//errors don't matter.  we are just trying to pull existing presence for possible clear.
	presence, _ := pApi.Cache.GetPresence(r.Context(), presenceReq.Userid, presenceReq.Productid, "")

	if params.ClearPresence != nil && bool(*params.ClearPresence) {
		if presence == nil || presence.Userid == "" || presence.Priority < 0 {
			log.Info().Msgf("failed to clear presence")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidParams))
			return
		}
		err := pApi.ClearPresence(r.Context(), presence.Userid, presenceReq.Productid)
		if err != nil {
			log.Error().Err(err).Msgf("failed to clear presence")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceClearFailed))
			return
		}
	}

	//if nil presence and ClearPresence is not true, then create a new presence to be added.
	if presence == nil {
		presence = &apipub.PresenceResponse{}
	}
	if presenceReq.Userid == "" || presenceReq.Status == "" || presenceReq.Priority <= apipub.PresesencePriorityUserSet || presenceReq.Productid == "" {
		log.Info().Msgf("user presense data is empty")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidParams))
		return
	}

	ttl := int64(pApi.Cfg.TtlPresence)
	if presenceReq.Ttl != nil {
		ttl = int64(*presenceReq.Ttl)
	}

	presence.Status = apipub.PresenceStatus(presenceReq.Status)
	presence.RichPresence = presenceReq.RichPresence
	presence.Ttl = &ttl
	presence.Meta = presenceReq.Meta
	presence.Priority = presenceReq.Priority
	presence.Userid = presenceReq.Userid
	presence.GameData = presenceReq.GameData
	presence.GameName = presenceReq.GameName

	pApi.SavePresenceAndBroadcast(r.Context(), presence)

	api.ReturnOK(w, r, presence)
}

func (pApi *SocialPrivateAPI) SavePresenceAndBroadcast(ctx context.Context, presence *apipub.PresenceResponse) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, pApi.Id)
	if presence == nil {
		return nil
	}

	err := pApi.Cache.SetPresence(ctx, presence, "", "", "", "", int64(0), int64(0), time.Duration(pApi.Cfg.TtlPresence)*time.Second)
	if err != nil {
		log.Error().Msgf("put presence failed with %s", err)
		return err
	}

	messenger.SendMqttMessage(ctx, pApi.Cfg, presence.Topic(tenant), messenger.MqttMessageTypePresence, presence)
	groups, _, err := pApi.Cache.GetUserGroups(ctx, presence.Userid, presence.Productid, nil, nil)
	if err != nil {
		log.Err(err).Str("userid", presence.Userid).Str("productid", presence.Productid).Msg("failed to get groups for user")
	} else {
		if groups != nil {
			for _, group := range *groups {
				messenger.SendMqttMessage(ctx, pApi.Cfg, group.Topic(tenant), messenger.MqttMessageTypePresence, presence)
			}
		}
	}

	return nil
}

// ClearPresence clears user presence
func (pApi *SocialPrivateAPI) ClearPresence(ctx context.Context, userid string, productid string) error {

	if userid == "" || productid == "" {
		return errors.New("invalid presence request")
	}

	presence, err := pApi.Cache.GetPresence(ctx, userid, productid, "")
	if err != nil || presence == nil {
		return err
	}
	presence.Status = apipub.Offline
	pApi.SavePresenceAndBroadcast(ctx, presence)
	err = pApi.Cache.DeletePresence(ctx, presence)
	if err != nil {
		return err
	}
	return nil
}

func buildSearchRequest(ctx context.Context, searchRequestType apipub.SearchAccountRequestType, query string, ost apipub.OnlineServiceType) (*apipub.SearchAccountRequest, *errs.Error) {
	log := logger.FromContext(ctx)

	queries := strings.Split(query, ",")
	var criteria []apipub.SearchAccountCriteria

	var searchRequest *apipub.SearchAccountRequest

	switch searchRequestType {
	case apipub.AccountsByFirstPartyAlias:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				FirstPartyAlias:   aws.String(q),
				OnlineServiceType: aws.Int(int(ost)),
			})
		}
	case apipub.AccountsByFirstPartyId:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				FirstPartyId:      aws.String(q),
				OnlineServiceType: aws.Int(int(ost)),
			})
		}
	case apipub.AccountsById:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				AccountId: aws.String(q),
			})
		}
	case apipub.FullAccountByDisplayName:
		for _, q := range queries {
			criteria = append(criteria, apipub.SearchAccountCriteria{
				DisplayName: aws.String(q),
			})
		}
	default:
		log.Error().Msgf("Unsupported search type %s", searchRequestType)
		return nil, errs.New(http.StatusInternalServerError, errs.EFriendsInvalidSearchQuery)
	}

	searchRequest = &apipub.SearchAccountRequest{
		Type:      &searchRequestType,
		Criterias: &criteria,
	}
	return searchRequest, nil
}

func (pApi *SocialPrivateAPI) UserSearchDNA(w http.ResponseWriter, r *http.Request, params apipriv.UserSearchDNAParams) {
	log := logger.FromContext(r.Context())
	platform := apipub.OnlineServiceTypeUNKNOWN
	query := strings.TrimSpace(params.Q)
	if params.SearchPlatform != nil {
		platform = apipub.OnlineServiceType(*params.SearchPlatform)
	}

	searchRequestType := apipub.FullAccountByDisplayName

	if params.SearchPlatform != nil {
		searchRequestType = apipub.AccountsByFirstPartyAlias
	}
	if params.Type != nil {
		searchRequestType = apipub.SearchAccountRequestType(*params.Type)
	}

	searchRequest, searchRequestErr := buildSearchRequest(r.Context(), searchRequestType, query, platform)
	if searchRequestErr != nil {
		errs.Return(w, r, searchRequestErr)
		return
	}

	searchResponse, err := pApi.Id.SearchAccounts(r.Context(), searchRequest)
	if err != nil {
		log.Error().Err(err).Msgf("failed to search accounts dna")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDnaSearchAccountsFailed))
		return
	}

	var items []interface{}
	if searchResponse != nil {
		for _, i := range *searchResponse {
			items = append(items, i)
		}
	}

	searchResult := ListResponse{
		Items: items,
	}

	utils.WriteJsonResponse(w, r, http.StatusOK, searchResult)
}

func (pApi *SocialPrivateAPI) GetUserLinksDNA(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	log := logger.FromContext(r.Context())

	result, err := pApi.Id.GetUserProfileAccountLinks(r.Context(), userid)
	if err != nil {
		log.Error().Err(err).Msgf("GetUserProfileAccountLinksDNA failed")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDnaGetLinkedAccountDataFailed))
		return
	}

	var items []interface{}

	if result != nil {
		for _, i := range *result {
			items = append(items, i)
		}
	}

	linksResult := ListResponse{
		Items: items,
	}

	utils.WriteJsonResponse(w, r, http.StatusOK, linksResult)
}

func (pApi *SocialPrivateAPI) GetFriendsForUser(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	log := logger.FromContext(r.Context())

	result, err := pApi.Ds.GetFullFriendList(r.Context(), userid)
	if err != nil {
		log.Error().Err(err).Msgf("GetUserProfileAccountLinksDNA failed")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDnaGeneric))
		return
	}
	if result == nil {
		api.ReturnOK(w, r, map[string]interface{}{})
		return
	}

	ret := ListResponse{}
	var items []interface{}
	for _, i := range *result {
		items = append(items, i)
	}
	ret.Items = items
	utils.WriteJsonResponse(w, r, http.StatusOK, ret)
}

func (pApi *SocialPrivateAPI) GetPresenceForUser(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	// log := logger.get(r)

	// items := []interface{}{}

	// result, err := pApi.Cache.GetUserPresences(r.Context(), string(userid))
	// if err != nil {
	// 	log.Error().Err(err).Msgf("GetUserPresences failed")
	// 	errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceGetUserPresenceFailed))
	// 	return
	// }

	// if result != nil {
	// 	for _, i := range *result {
	// 		items = append(items, i)
	// 	}
	// }

	// linksResult := ListResponse{
	// 	Items: items,
	// }

	// utils.WriteJsonResponse(w, r, http.StatusOK, linksResult)
	utils.WriteJsonResponse(w, r, http.StatusNotImplemented, nil)
}

func (pApi *SocialPrivateAPI) GetVoipRoomsForUser(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {
	log := logger.FromContext(r.Context())
	if pApi.Cfg.VoipPrivateApiURL == "" {
		log.Warn().Msgf("Cfg.VoipPrivateApiURL is not configured properly")
		api.ReturnOK(w, r, &apipriv.ListResponse{})
	}

	url := pApi.Cfg.VoipPrivateApiURL + "/v1/user/" + userid
	var req *http.Request
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error().Err(err).Msgf("failed to create new request")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVoipCreateRequestFailed))
		return
	}

	req.Header.Add(constants.KContentType, constants.KApplicationJson)

	span, _ := tracer.StartSpanFromContext(r.Context(), "voip.user", tracer.ResourceName(req.URL.Path))
	var resp *http.Response
	resp, err = http.DefaultClient.Do(req)
	if err != nil {
		log.Error().Err(err).Msgf("voip failed get url=%s", url)
		span.Finish()
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVoipRequestFailed))
		return
	}
	span.Finish()

	if resp.StatusCode != http.StatusOK {
		log.Error().Err(err).Msgf("voip failed get url=%s status=%d", url, resp.StatusCode)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EVoipRequestFailed))
		return
	}

	// forward response as is
	defer resp.Body.Close()
	body, _ := io.ReadAll(resp.Body)
	w.Header().Add(constants.KContentType, resp.Header.Get(constants.KContentType))
	w.WriteHeader(resp.StatusCode)
	w.Write(body)
}

func (pApi *SocialPrivateAPI) GetHealthDetails(w http.ResponseWriter, r *http.Request) {
	sm := pApi.Monitor
	h := sm.GetOverallHealth(nil)
	if h != health.OK {
		w.WriteHeader(http.StatusInternalServerError)
	}
	w.Header().Set(constants.KContentType, constants.KApplicationJson)
	w.Write(sm.GetHealthReport(health.HealthReportPrivate, nil).Bytes())
}

func (pApi *SocialPrivateAPI) SyncUserProfile(w http.ResponseWriter, r *http.Request, userid apipriv.Userid) {

	profile, err := pApi.Id.SyncUserProfile(r.Context(), userid)
	if err == nil {
		pApi.saveUserProfile(r.Context(), profile)
	}

	utils.WriteJsonResponse(w, r, http.StatusOK, map[string]string{})
}

func (pApi *SocialPrivateAPI) makeFriendWithCache(ctx context.Context, userid1, userid2, message string, isInviterBlocked bool, inviterOST apipub.OnlineServiceType) (string, error) {
	log := logger.FromContext(ctx)

	// save the friend to the store
	status, err := pApi.Ds.MakeFriend(ctx, userid1, userid2, message, isInviterBlocked, inviterOST)
	if err != nil {
		log.Error().Err(err).Msgf("failed to save friend to database")
		return "", err
	}

	// save the friend to the Cache
	err = pApi.Cache.MakeFriend(ctx, userid1, userid2, message, isInviterBlocked, inviterOST, time.Duration(pApi.Cfg.TtlFriend)*time.Second)
	if err != nil {
		log.Error().Err(err).Msgf("failed to save friend to Cache")
		return "", err
	}

	return status, nil
}

func (pApi *SocialPrivateAPI) GetAllStats(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())

	result := map[string]interface{}{}
	pendingFriendsCount, err := pApi.Ds.GetTotalFriendsCount(r.Context(), "pending")
	if err != nil {
		log.Error().Err(err).Msgf("failed to get pending friends")
	}

	friendsCount, err := pApi.Ds.GetTotalFriendsCount(r.Context(), "friend")
	if err != nil {
		log.Error().Err(err).Msgf("failed to get total friends")
	}

	userCount, err := pApi.Ds.GetTotalUserCount(r.Context())
	if err != nil {
		log.Error().Err(err).Msgf("failed to get total users")
	}

	result["pending-friends-count"] = pendingFriendsCount
	result["friends-count"] = friendsCount
	result["user-count"] = userCount
	utils.WriteJsonResponse(w, r, http.StatusOK, result)
}

// UpdateConfig update server config
func (pApi *SocialPrivateAPI) UpdateConfig(w http.ResponseWriter, r *http.Request) {
	if !api.DecodeBody(w, r, pApi.Cfg) {
		return
	}
	utils.WriteJsonResponse(w, r, http.StatusOK, pApi.Cfg)
}

func (pApi *SocialPrivateAPI) saveUserProfile(ctx context.Context, profile *apipub.UserProfileResponse) {
	if profile != nil {
		// save the user profile to dynamo.
		pApi.Ds.PutUserProfile(ctx, profile)
		// save the user profile to redis.
		pApi.Cache.SetUserProfile(ctx, profile, time.Duration(pApi.Cfg.TtlProfile)*time.Second)
	}
}

func (pApi *SocialPrivateAPI) TrustedCreate(w http.ResponseWriter, r *http.Request) {
	// decode request
	var req apipriv.TrustedLoginCreateRequest
	if !api.DecodeBody(w, r, &req) {
		return
	}

	tid := req.TenantId
	pid := req.ProductId
	//if no pass, then autogenerate
	pass := req.Password
	if pass == nil {
		passStr := utils.GenerateRandomPass(43)
		pass = &passStr
	}
	hash := utils.HashPassword(pass)

	clientId := pid
	//check if clientId already exists
	tsCid, _ := pApi.Ds.GetTsClientId(r.Context(), pid)
	if tsCid != nil {
		//if exists, autogen a new clientId.
		clientId = utils.GenerateRandomDNAID()
	}

	tsInfo := api2.TsClientIdInfo{
		ClientId:  clientId,
		ProductId: pid,
		TenantId:  tid,
		Hash:      *hash,
	}

	//store the info in dynamo
	err := pApi.Ds.PutTsClientId(r.Context(), &tsInfo)
	if err != nil {
		//if db put fails, return error
		log.Error().Err(err).Msgf("TS DB set fail")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
		return
	}
	//store in Cache
	err = pApi.Cache.SetTsClientId(r.Context(), &tsInfo)
	if err != nil {
		log.Error().Err(err).Msgf("TS Cache set fail")
	} else {
		err = pApi.Cache.SetRangeTsClientId(r.Context(), &tsInfo)
		if err != nil {
			log.Error().Err(err).Msgf("TS Cache range set fail")
		}
	}

	resp := &apipriv.TrustedLoginResponse{
		ClientId: clientId,
		Password: *pass,
	}

	api.ReturnCreated(w, r, resp)
}

func (pApi *SocialPrivateAPI) TrustedUpdate(w http.ResponseWriter, r *http.Request, clientid string) {
	// decode request
	var req apipriv.TrustedLoginUpdateRequest
	api.DecodeOptionalBody(w, r, &req)

	//if no pass, then autogenerate
	pass := req.Password
	if pass == nil {
		passStr := utils.GenerateRandomPass(43)
		pass = &passStr
	}
	hash := utils.HashPassword(pass)

	//check if clientid exists
	tsCid, err := pApi.Cache.GetTsClientId(r.Context(), clientid)
	if err != nil {
		log.Error().Err(err).Str("clientid", clientid).Msgf("error looking for ts clientid in Cache")
	}
	if tsCid == nil {
		//not in Cache, check db
		tsCid, err = pApi.Ds.GetTsClientId(r.Context(), clientid)
		if err != nil {
			log.Error().Err(err).Str("clientid", clientid).Msgf("ts clientid not found in db")
		}
		if tsCid == nil {
			//if db get fails, return error
			errs.Return(w, r, errs.New(http.StatusNotFound, errs.EInvalidLogin))
			return
		}
	}

	tsCid.Hash = *hash

	//store the info in dynamo
	err = pApi.Ds.PutTsClientId(r.Context(), tsCid)
	if err != nil {
		//if db put fails, return error
		log.Error().Err(err).Msgf("TS DB set fail")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
		return
	}
	//store in Cache
	err = pApi.Cache.SetTsClientId(r.Context(), tsCid)
	if err != nil {
		log.Error().Err(err).Msgf("TS Cache set fail")
	}

	resp := &apipriv.TrustedLoginResponse{
		ClientId: clientid,
		Password: *pass,
	}

	api.ReturnOK(w, r, resp)
}

func (pApi *SocialPrivateAPI) TrustedDel(w http.ResponseWriter, r *http.Request, clientid string) {

	//check if clientid exists
	tsCid, err := pApi.Cache.GetTsClientId(r.Context(), clientid)
	if err != nil {
		log.Error().Err(err).Str("clientid", clientid).Msgf("error looking for ts clientid in Cache")
	}
	if tsCid == nil {
		//not in Cache, check db
		tsCid, err = pApi.Ds.GetTsClientId(r.Context(), clientid)
		if err != nil {
			log.Error().Err(err).Str("clientid", clientid).Msgf("ts clientid not found in db")
		}
		if tsCid == nil {
			//if db get fails, return error
			errs.Return(w, r, errs.New(http.StatusNotFound, errs.EInvalidLogin))
			return
		}
	}

	err = pApi.Cache.DelRangeTsClientId(r.Context(), tsCid)
	if err != nil {
		log.Error().Err(err).Msgf("TS Cache range del fail")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheDeleteFailed))
		return
	}

	//del from Cache
	err = pApi.Cache.DelTsClientId(r.Context(), tsCid.ClientId)
	if err != nil {
		log.Error().Err(err).Msgf("TS Cache del fail")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheDeleteFailed))
		return
	}

	//del from dynamo
	err = pApi.Ds.DelTsClientId(r.Context(), tsCid.ClientId)
	if err != nil {
		//if db del fails, return error
		log.Error().Err(err).Msgf("TS DB del fail")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbDeleteFailed))
		return
	}
	api.ReturnEmptyOK(w, r)
}

func (pApi *SocialPrivateAPI) GetFirstPartyRefreshToken(w http.ResponseWriter, r *http.Request, firstPartyid apipriv.FirstPartyid, platform apipriv.Platform) {
	log := logger.FromContext(r.Context())

	result, err := pApi.Cache.GetUserFirstPartyRefresh(r.Context(), firstPartyid, int(platform))
	if err != nil {
		log.Error().Err(err).Msgf("GetUserFirstPartyRefresh failed")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	ret := apipriv.FirstPartyRefreshResponse{
		FirstPartyid: &firstPartyid,
		RefreshToken: &result,
	}

	api.ReturnOK(w, r, ret)
}

func (pApi *SocialPrivateAPI) GetFirstPartyToken(w http.ResponseWriter, r *http.Request, firstPartyid apipriv.FirstPartyid, platform apipriv.Platform) {
	log := logger.FromContext(r.Context())

	result, err := pApi.Cache.GetUserFirstPartyToken(r.Context(), firstPartyid, int(platform))
	if err != nil {
		log.Error().Err(err).Msgf("GetUserFirstPartyRefresh failed")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	ret := apipriv.FirstPartyTokenResponse{
		FirstPartyid: &firstPartyid,
		Token:        &result,
	}

	api.ReturnOK(w, r, ret)
}

func (pApi *SocialPrivateAPI) SetFirstPartyToken(w http.ResponseWriter, r *http.Request, firstPartyid apipriv.FirstPartyid, platform apipriv.Platform) {
	// decode request
	var firstPartyToken apipriv.FirstPartyTokenRequest
	if !api.DecodeBody(w, r, &firstPartyToken) {
		return
	}

	refresh := ""
	if firstPartyToken.RefreshToken != nil {
		refresh = *firstPartyToken.RefreshToken
	}
	refreshTtl := 0
	if firstPartyToken.RefreshTokenTtl != nil {
		refreshTtl = *firstPartyToken.RefreshTokenTtl
	}

	err := pApi.Cache.SetUserFirstPartyToken(r.Context(), firstPartyid, int(platform), firstPartyToken.Token, time.Duration(firstPartyToken.Ttl), refresh, time.Duration(refreshTtl))
	if err != nil {
		log.Error().Err(err).Msgf("SetFirstPartyToken fail")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed))
	}
	api.ReturnEmptyOK(w, r)
}

type ListResponse struct {
	Items []interface{} `json:"items"`
	Next  *string       `json:"next"`
}
