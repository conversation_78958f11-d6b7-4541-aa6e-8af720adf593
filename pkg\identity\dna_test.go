package identity

import (
	"context"
	"fmt"
	"net/http"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

type MockDNA struct {
	cfg  *config.Config
	dna  DNAServiceInterface
	http utils.HTTPClientInterface
}

// MockClient is the mock client
type MockClient struct {
	DoFunc func(req *http.Request) (*http.Response, error)
}

// Do is the mock client's `Do` func
func (m *MockClient) Do(req *http.Request) (*http.Response, error) {
	if m.DoFunc != nil {
		return m.DoFunc(req)
	}
	return nil, fmt.Errorf("no do func")
}

func createMockDNA() *MockDNA {

	return &MockDNA{
		cfg:  cfg,
		dna:  NewDNAService(cfg),
		http: http.DefaultClient,
	}
}

func TestNewDNAService(t *testing.T) {
	g := goblin.Goblin(t)
	mock := createMockDNA()

	g.Describe("NewError", func() {
		g.It("should should succeed", func() {
			s := NewDNAService(mock.cfg)
			g.Assert(s).IsNotNil()
		})
	})
}

func TestSearchAccounts(t *testing.T) {
	g := goblin.Goblin(t)
	mock := createMockDNA()

	goodUserID := "b287e655461f4b3085c8f244e394ff7e"
	badUserID := "b287e655461f4b3085c8f244e394ff70"

	g.Describe("SearchAccounts", func() {
		g.It("should succeed", func() {
			searchType := apipub.AccountsById
			criterias := []apipub.SearchAccountCriteria{
				{AccountId: aws.String(goodUserID)},
			}
			searchRequest := &apipub.SearchAccountRequest{
				Type:      &searchType,
				Criterias: &criterias,
			}
			mock.http = http.DefaultClient
			result, err := mock.dna.SearchAccounts(ctx, searchRequest)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result)).Equal(1)
			g.Assert(string(*(*result)[0].Email)).Equal("<EMAIL>")
			g.Assert(*(*result)[0].Id).Equal(goodUserID)
			g.Assert(*(*result)[0].Type).Equal(apipub.AccountTypeDNAFULL)
		})

		g.It("should fail", func() {
			searchType := apipub.AccountsById
			criterias := []apipub.SearchAccountCriteria{
				{AccountId: aws.String(badUserID)},
			}
			ctx := context.Background()
			searchRequest := &apipub.SearchAccountRequest{
				Type:      &searchType,
				Criterias: &criterias,
			}
			result, err := mock.dna.SearchAccounts(ctx, searchRequest)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result)).Equal(0)
		})
	})

	g.Describe("SearchAccountsByUserID", func() {
		g.It("should succeed", func() {
			result, err := mock.dna.SearchAccountsByUserID(ctx, goodUserID)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result)).Equal(1)
			g.Assert(string(*(*result)[0].Email)).Equal("<EMAIL>")
			g.Assert(*(*result)[0].Id).Equal(goodUserID)
			g.Assert(*(*result)[0].Type).Equal(apipub.AccountTypeDNAFULL)
		})

		g.It("should fail", func() {
			result, err := mock.dna.SearchAccountsByUserID(ctx, badUserID)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result)).Equal(0)
		})
	})
}

func TestGetUserProfileAccountLinks(t *testing.T) {
	g := goblin.Goblin(t)
	mock := createMockDNA()
	mockId := NewMockIdentity(t)
	defer mockId.ctrl.Finish()

	goodUserID := "b287e655461f4b3085c8f244e394ff7e"
	badUserID := "b287e655461f4b3085c8f244e394ff70"

	accountLink := apipub.AccountLinkDNA{
		AccountId: &goodUserID,
	}
	links := []apipub.AccountLinkDNA{}
	links = append(links, accountLink)

	g.Describe("GetUserProfileAccountLinks", func() {
		g.It("should succeed with valid accountid", func() {
			result, err := mock.dna.GetUserProfileAccountLinks(ctx, goodUserID)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			//remove this as real account could unlink accounts
			//g.Assert(len(*result) > 0).IsTrue()
		})

		g.It("should succeed with mock results", func() {
			//mock account with guaranteed link return
			mockId.ids.EXPECT().GetUserProfileAccountLinks(ctx, goodUserID).Return(&links, nil)
			result, err := mockId.id.GetUserProfileAccountLinks(ctx, goodUserID)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result) > 0).IsTrue()
		})

		g.It("should fail with invalid accountid", func() {
			result, err := mock.dna.GetUserProfileAccountLinks(ctx, badUserID)
			g.Assert(err).IsNotNil()
			g.Assert(result).IsNil()
		})
	})
}

func TestDNAService(t *testing.T) {
	g := goblin.Goblin(t)
	mock := createMockDNA()

	g.Describe("DNAService", func() {
		g.It("should no longer be critical", func() {
			g.Assert(mock.dna.IsCritical()).IsFalse()
			g.Assert(mock.dna.CheckHealth()).IsTrue()
			lastStatus := mock.dna.LastStatus()
			g.Assert(lastStatus).IsNotNil()
			g.Assert(lastStatus.Status).Equal(health.OK)
		})
	})
}
