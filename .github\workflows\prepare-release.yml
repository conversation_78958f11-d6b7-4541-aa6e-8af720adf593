name: Prepare Release Candidate
run-name: Prepare Release Candidate ${{ github.event.inputs.version || github.head_ref  }}

on:
  push:
    branches:
      - release/*
  # workflow_dispatch:
  #   inputs:
  #     version:
  #       required: false
  #       description: 'Version to release. ex: v1.0.0-########'

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

concurrency: release-${{ github.head_ref }}
jobs:
  build-social-service:
    name: Build social service docker images
    runs-on: [t2gp-arc-linux]
    env:
      WORK_DIR: ./
    outputs:
      image_tag: ${{ steps.build-docker.outputs.image_tag }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
      - name: Get RC tag (manual)
        if: github.event.inputs.version != ''
        run: |
          VER=${{github.event.inputs.version}}
          echo RC_TAG=${VER:0:-9} >> $GITHUB_ENV
      - name: Get RC tag
        if: github.event.inputs.version == ''
        run: |
          BRANCH_NAME=${{ github.ref_name }}
          echo RC_TAG=${BRANCH_NAME:8} >> $GITHUB_ENV
      - name: Get remaining envvars
        run: |
          git_sha=${{ github.sha }}
          git_sha_short=${git_sha:0:8}
          echo RC_SHA=${git_sha} >> $GITHUB_ENV
          echo RC_SHA_SHORT=${git_sha_short} >> $GITHUB_ENV
      - name: AWS Assume Role (LOCAL)
        if: ${{ env.ACT }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: us-east-1
      - name: AWS Assume Role
        if: ${{ !env.ACT }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Build docker images
        id: build-docker
        env:
          NODE_AUTH_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        run: |
          echo "image_tag=${{env.RC_TAG}}-${{env.RC_SHA_SHORT}}" >> $GITHUB_OUTPUT
          echo "image_tag=${{env.RC_TAG}}-${{env.RC_SHA_SHORT}}" >> $GITHUB_OUTPUT
          DATE=$(date "+%Y-%m-%d-%H-%M-%S")
          docker build --progress plain -t t2gp-social-api . \
            --build-arg VERSION="${{env.RC_TAG}}-${{env.RC_SHA_SHORT}}" \
            --build-arg GIT_HASH="${{env.RC_SHA}}" \
            --build-arg BUILD_DATE="$DATE" \
            --build-arg git_token=${{secrets.SERVICE_ACCOUNT_GH_PAT}} \
            --label "org.opencontainers.image.revision=${{ env.RC_SHA }}" \
            --label "org.opencontainers.image.source=https://github.com/${{ github.repository }}"
      - name: Push api image
        uses: jwalton/gh-ecr-push@v1
        with:
          access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          region: us-east-1
          local-image: 't2gp-social-api'
          image: 't2gp-social-api:${{env.RC_TAG}}-${{env.RC_SHA_SHORT}}, t2gp-social-api:${{env.RC_TAG}}'
  run-helm:
    needs: [build-social-service]
    name: 'Run Helm'
    runs-on: [t2gp-arc-linux]
    env:
      CLUSTER: t2gp-non-production
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
      release_name: ${{ steps.output_info.outputs.release_name }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Get RC tag (manual)
        if: github.event.inputs.version != ''
        run: |
          VER=${{github.event.inputs.version}}
          echo RC_TAG=${VER:0:-9} >> $GITHUB_ENV
      - name: Get RC tag
        if: github.event.inputs.version == ''
        run: |
          BRANCH_NAME=${{ github.ref_name }}
          echo RC_TAG=${BRANCH_NAME:8} >> $GITHUB_ENV
      - name: Get remaining EnvVars
        run: |
          RC_TAG=${{env.RC_TAG}}
          git_sha=${{ github.sha }}
          git_sha_short=${git_sha:0:8}
          echo RC_SHA=${git_sha} >> $GITHUB_ENV
          echo LAST_TAG=$(git describe --tags `git rev-list --tags --max-count=1`) >> $GITHUB_ENV
          echo RC_TAG=${RC_TAG} >> $GITHUB_ENV
          echo RC_VER=${RC_TAG}-${git_sha_short} >> $GITHUB_ENV
          echo RELEASE_NAME="release-${RC_TAG//./-}" >> $GITHUB_ENV
      - name: 'Build Changelog'
        if: github.event.inputs.version == ''
        id: build_changelog
        uses: mikepenz/release-changelog-builder-action@v3.4.0
        with:
          fromTag: ${{env.LAST_TAG}}
          toTag: ${{ github.ref }}
          configurationJson: |
            {
              "template": "#{{CHANGELOG}}\n\n<details>\n<summary>Uncategorized</summary>\n\n#{{UNCATEGORIZED}}\n</details>"
            }
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
      - name: AWS Assume Role (LOCAL)
        if: ${{ env.ACT }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-session-token: ${{ secrets.AWS_SESSION_TOKEN }}
          aws-region: us-east-1
      - name: AWS Assume Role
        if: ${{ !env.ACT }}
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Helm Deploy social-service
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          environment: ${{ env.RELEASE_NAME }}
          helm-values: >
            social-api.groupsApi.image.tag=${{env.RC_VER}},social-api.groupsApi.commitSha=${{env.RC_SHA}},social-api.v2.enabled=true,global.socialMqttEnabled=true,global.ddEnv=${{ env.RELEASE_NAME }}
          raise-pr: false
          sandbox: true
      - name: Helm Deploy social-trusted-api
        id: helm_deploy_trusted
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-trusted-api
          environment: ${{ env.RELEASE_NAME }}
          chart-override: social-service
          helm-values: >
            social-api.groupsApi.image.tag=${{env.RC_VER}},social-api.groupsApi.commitSha=${{env.RC_SHA}},social-api.v2.enabled=true,social-api.mqttEnv=${{ env.RELEASE_NAME }},social-api.trustedApi.enabled=true,global.project=social-trusted,global.socialMqttEnabled=false,global.ddEnv=${{ env.RELEASE_NAME }}
          raise-pr: false
          sandbox: true
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: social-env-ver-mapping
          item: '{ "env_label": "${{ env.RELEASE_NAME }}", "version": "${{env.RC_VER}}", "api_url":"https://social-service-${{ env.RELEASE_NAME }}.dev.d2dragon.net/v2", "api_private_url":"https://social-service-${{ env.RELEASE_NAME }}-private.dev.d2dragon.net", "mqtt_url":"wss://social-service-${{ env.RELEASE_NAME }}.dev.d2dragon.net/mqtt" }'
      - name: Output Info
        id: output_info
        run: |
          echo "image_tag=${{ env.RC_VER }}" >> $GITHUB_OUTPUT
          echo "release_name=${{ env.RELEASE_NAME }}" >> $GITHUB_OUTPUT
  update-discovery:
    needs: [run-helm]
    uses: ./.github/workflows/update-discovery.yml
    with:
      action: add
      id: ${{ needs.run-helm.outputs.release_name }}
  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: ${{ needs.run-helm.outputs.release_name }}
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      api_test_note: 'deploy ${{ needs.run-helm.outputs.release_name }}'
    secrets: inherit
