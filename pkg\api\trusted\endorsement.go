package api

import (
	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"net/http"
)

func (tApi *SocialTrustedAPI) ResetEndorsementForUser(w http.ResponseWriter, r *http.Request, pEndorsementName apitrusted.PEndorsementName, pUserid apitrusted.PUserid) {
	tenant := "dna"
	claims, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || claims == nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := pUserid
	productid := claims.ProductID
	ost := apipub.OnlineServiceType(claims.OnlinePlatformType)
	appid := claims.Issuer
	endorsementName := pEndorsementName

	tApi.SocialApi.Cache.ResetEndorsement(r.Context(), userid, productid, endorsementName)
	tApi.SocialApi.Ds.ResetEndorsement(r.Context(), userid, productid, endorsementName)

	mqttEndorse := apipub.MqttEndorsementReceived{
		EndorsementName:         endorsementName,
		CurrentEndorsementCount: 0,
	}

	messenger.SendMqttMessage(r.Context(), tApi.cfg, apipub.BuildUserTopic(tenant, userid), messenger.MqttMessageTypeEndorseReset, mqttEndorse)

	endorsement := apipub.EndorsementResponse{
		EndorsementName: endorsementName,
	}

	tApi.SocialApi.Tele.SendEndorsementEvent(r.Context(), telemetry.BuildEndorsementTeleMeta(telemetry.KEndorsementReset, productid, pUserid, ost, []string{endorsementName}, endorsement, &appid, nil))

	api.ReturnEmptyOK(w, r)
}

func (tApi *SocialTrustedAPI) RemoveEndorsementForUser(w http.ResponseWriter, r *http.Request, pEndorsementName apitrusted.PEndorsementName, pUserid apitrusted.PUserid) {
	tenant := "dna"
	claims, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || claims == nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := pUserid
	productid := claims.ProductID
	ost := apipub.OnlineServiceType(claims.OnlinePlatformType)
	appid := claims.Issuer
	endorsementName := pEndorsementName

	tApi.SocialApi.Cache.RemoveEndorsement(r.Context(), userid, productid, endorsementName)
	tApi.SocialApi.Ds.RemoveEndorsement(r.Context(), userid, productid, endorsementName)

	mqttEndorse := apipub.MqttEndorsementReceived{
		EndorsementName:         endorsementName,
		CurrentEndorsementCount: 0,
	}

	messenger.SendMqttMessage(r.Context(), tApi.cfg, apipub.BuildUserTopic(tenant, userid), messenger.MqttMessageTypeEndorseRemove, mqttEndorse)

	endorsement := apipub.EndorsementResponse{
		EndorsementName: endorsementName,
	}

	tApi.SocialApi.Tele.SendEndorsementEvent(r.Context(), telemetry.BuildEndorsementTeleMeta(telemetry.KEndorsementRemove, productid, pUserid, ost, []string{endorsementName}, endorsement, &appid, nil))

	api.ReturnEmptyOK(w, r)
}
