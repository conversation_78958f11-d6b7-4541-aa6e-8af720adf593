import { StatusCodes } from 'http-status-codes';
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';

let tokenHost: string;
let roomId: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  roomId = await (await socialApi.createRoom(tokenHost, 2)).body.groupid;
});
afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
});
describe('', () => {
  /**
   * checking edit room
   * - create a room
   * - edit the room maxMembers
   * - check if the maxMembers is updated
   */
  it('Edit Room', async () => {
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .patch(`/chat/rooms/${roomId}`)
      .set('Authorization', 'Bearer ' + tokenHost)
      .send({ maxMembers: 3, meta: {} });
    expect(resp.status).toEqual(StatusCodes.OK);

    const resp2: request.Response = await socialApi.getRoom(tokenHost, roomId);
    expect(resp2.body).toHaveProperty('maxMembers', 3);
  });

  /**
   * checking edit room
   * - create a room
   * - edit the room maxMembers
   * - change the maxMembers -1
   */
  it('Edit Room maxMembers negative', async () => {
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .patch(`/chat/rooms/${roomId}`)
      .set('Authorization', 'Bearer ' + tokenHost)
      .send({ maxMembers: -1, meta: {} });
    //expect(resp.status).toEqual(StatusCodes.OK);
    //console.log(resp.body);

    const resp2: request.Response = await socialApi.getRoom(tokenHost, roomId);
    //console.log(resp2.body);
    //expect(resp2.body).toHaveProperty('maxMembers', 3);
  });
});
