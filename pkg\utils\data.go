package utils

// ConvertMapInterfaceToMapString converters a map of interfaces to a map of strings if it can.  skips non string values.
func ConvertMapInterfaceToMapString(input map[string]interface{}, output *map[string]string) {
	if input != nil {
		if output == nil {
			output = &map[string]string{}
		}
		teleMeta := input
		//additionalInfo = teleMeta.(map[string]string)
		for key, value := range teleMeta {
			switch value := value.(type) {
			case string:
				(*output)[key] = value
			}
		}
	}
	output = &map[string]string{}
}
