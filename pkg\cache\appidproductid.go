package cache

import (
	"context"
	"fmt"
	"time"
)

func BuildAppIdProductIdRedisKey(appid string) string {
	return fmt.Sprintf("appidpid:%s", appid)
}

func BuildProductIdToNameRedisKey(tenant string, productid string) string {
	return fmt.Sprintf("%s:prod:%s", tenant, productid)
}

func (rc *RedisCache) GetProductIdFromAppId(ctx context.Context, appid string) (*string, error) {
	key := BuildAppIdProductIdRedisKey(appid)
	return getCachedObject[string](ctx, rc, key)
}

func (rc *RedisCache) SetAppIdProductId(ctx context.Context, appid string, productid string, ttl time.Duration) error {
	return setCachedObject(ctx, rc, &productid, BuildAppIdProductIdRedisKey(appid), ttl)
}

func (rc *RedisCache) GetProductIdToName(ctx context.Context, tenant string, productid string) (string, error) {
	key := BuildProductIdToNameRedisKey(tenant, productid)
	return rc.get(ctx, key).Result()
}

func (rc *RedisCache) SetProductIdToName(ctx context.Context, tenant string, productid string, name string) error {
	key := BuildProductIdToNameRedisKey(tenant, productid)
	return rc.set(ctx, key, name, 0).Err()
}
