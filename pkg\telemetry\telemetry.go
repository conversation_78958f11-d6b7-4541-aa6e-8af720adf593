// Package telemetry functions for telemetry service
package telemetry

import (
	"context"
	"fmt"
	"os"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/DataDog/datadog-go/v5/statsd"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
)

type EventType string

const (
	// auth
	KAuthLoginSuccess        = EventType("t2gp.social.auth.login.success")
	KAuthLoginFail           = EventType("t2gp.social.auth.login.fail")
	KAuthLogoutSuccess       = EventType("t2gp.social.auth.logout.success")
	KAuthLogoutFail          = EventType("t2gp.social.auth.logout.fail")
	KAuthTokenRefreshSuccess = EventType("t2gp.social.auth.tokenRefresh.success")
	KAuthTokenRefreshFail    = EventType("t2gp.social.auth.tokenRefresh.fail")
	//KAuthLinkSuccess         = EventType("t2gp.social.auth.link.success") // Can't implement
	//KAuthLinkFail            = EventType("t2gp.social.auth.link.fail")    // Can't implement

	// friends
	KFriendInvite  = EventType("t2gp.social.friend.invite")
	KFriendAccept  = EventType("t2gp.social.friend.accept")
	KFriendDecline = EventType("t2gp.social.friend.decline")
	KFriendRemove  = EventType("t2gp.social.friend.remove")

	// search
	//KSearchEmail           = EventType("t2gp.social.search.email")
	//KSearchFirstPartyAlias = EventType("t2gp.social.search.firstPartyAlias")
	//KSearchFirstPartyId    = EventType("t2gp.social.search.firstPartyId")
	//KSearchAccountId       = EventType("t2gp.social.search.accountId")
	KSearchDisplayName = EventType("t2gp.social.search.displayName")

	// blocklist
	KBlocklistAdd    = EventType("t2gp.social.blocklist.add")
	KBlocklistRemove = EventType("t2gp.social.blocklist.remove")

	// report
	KReportPlayer = EventType("t2gp.social.report.player")

	// groups
	KGroupCreate        = EventType("t2gp.social.group.create")
	KGroupInvite        = EventType("t2gp.social.group.invite")
	KGroupDecline       = EventType("t2gp.social.group.decline")
	KGroupAccept        = EventType("t2gp.social.group.accept")
	KGroupRequestToJoin = EventType("t2gp.social.group.requestToJoin")
	KGroupApprove       = EventType("t2gp.social.group.approve")
	KGroupRevoke        = EventType("t2gp.social.group.revoke")
	KGroupReject        = EventType("t2gp.social.group.reject")
	KGroupLeave         = EventType("t2gp.social.group.leave")
	KGroupJoin          = EventType("t2gp.social.group.join")
	KGroupDisband       = EventType("t2gp.social.group.disband")
	KGroupKick          = EventType("t2gp.social.group.kick")
	KGroupLeaderChange  = EventType("t2gp.social.group.leaderChange")
	KGroupControlMsg    = EventType("t2gp.social.group.controlMsg")
	KGroupModify        = EventType("t2gp.social.group.update")
	KGroupAutoKick      = EventType("t2gp.social.group.autoKicked")
	KGroupAuthenticate  = EventType("t2gp.social.group.authenticate")

	// presence
	KPresenceOnline         = EventType("t2gp.social.presence.online")
	KPresenceOffline        = EventType("t2gp.social.presence.offline")
	KPresenceAway           = EventType("t2gp.social.presence.away")
	KPresencePlaying        = EventType("t2gp.social.presence.playing")
	KPresenceCustom         = EventType("t2gp.social.presence.custom")
	KPresenceDND            = EventType("t2gp.social.presence.dnd")
	KPresenceChat           = EventType("t2gp.social.presence.chat")
	KPresencePlayTime       = EventType("t2gp.social.presence.playTime")
	KPresenceAuthenticating = EventType("t2gp.social.presence.authenticating")

	// endorse
	KEndorsementIncrement = EventType("t2gp.social.endorsement.increment")
	KEndorsementReset     = EventType("t2gp.social.endorsement.reset")
	KEndorsementRemove    = EventType("t2gp.social.endorsement.remove")

	// client (alpha race)
	KClientCrash = EventType("t2gp.social.client.crash")
)

type EventInfo struct {
	Title string
	Text  string
}

var EventMap = map[EventType]EventInfo{
	// auth
	KAuthLoginSuccess:        {Title: string(KAuthLoginSuccess), Text: "login success"},
	KAuthLoginFail:           {Title: string(KAuthLoginFail), Text: "login fail"},
	KAuthLogoutSuccess:       {Title: string(KAuthLogoutSuccess), Text: "logout success"},
	KAuthLogoutFail:          {Title: string(KAuthLogoutFail), Text: "logout fail"},
	KAuthTokenRefreshSuccess: {Title: string(KAuthTokenRefreshSuccess), Text: "token refresh success"},
	KAuthTokenRefreshFail:    {Title: string(KAuthTokenRefreshFail), Text: "token refresh fail"},
	//KAuthLinkSuccess:         {Title: string(KAuthLinkSuccess), Text: "link success"},
	//KAuthLinkFail:            {Title: string(KAuthLinkFail), Text: "link fail"},

	// friends
	KFriendInvite:  {Title: string(KFriendInvite), Text: "friend invite"},
	KFriendAccept:  {Title: string(KFriendAccept), Text: "friend accept"},
	KFriendDecline: {Title: string(KFriendDecline), Text: "friend decline"},
	KFriendRemove:  {Title: string(KFriendRemove), Text: "friend remove"},

	// search
	//KSearchEmail:           {Title: string(KSearchEmail), Text: "search email"},
	//KSearchFirstPartyAlias: {Title: string(KSearchFirstPartyAlias), Text: "search firstparty alias"},
	//KSearchFirstPartyId:    {Title: string(KSearchFirstPartyId), Text: "search firstparty id"},
	//KSearchAccountId:       {Title: string(KSearchAccountId), Text: "search account id"},
	KSearchDisplayName: {Title: string(KSearchDisplayName), Text: "search display name"},

	// blocklist
	KBlocklistAdd:    {Title: string(KBlocklistAdd), Text: "blocklist add"},
	KBlocklistRemove: {Title: string(KBlocklistRemove), Text: "blocklist remove"},

	// groups
	KGroupCreate:        {Title: string(KGroupCreate), Text: "group create"},
	KGroupInvite:        {Title: string(KGroupInvite), Text: "group invite"},
	KGroupDecline:       {Title: string(KGroupDecline), Text: "group decline"},
	KGroupAccept:        {Title: string(KGroupAccept), Text: "group accept"},
	KGroupRequestToJoin: {Title: string(KGroupRequestToJoin), Text: "group request to join"},
	KGroupKick:          {Title: string(KGroupKick), Text: "group kick"},
	KGroupApprove:       {Title: string(KGroupApprove), Text: "group approve join request"},
	KGroupRevoke:        {Title: string(KGroupRevoke), Text: "group revoke invite"},
	KGroupReject:        {Title: string(KGroupReject), Text: "group reject join request"},
	KGroupLeave:         {Title: string(KGroupLeave), Text: "group leave"},
	KGroupJoin:          {Title: string(KGroupJoin), Text: "group join"},
	KGroupDisband:       {Title: string(KGroupDisband), Text: "group disband"},
	KGroupLeaderChange:  {Title: string(KGroupLeaderChange), Text: "group leader change"},
	KGroupControlMsg:    {Title: string(KGroupControlMsg), Text: "group control message"},
	KGroupModify:        {Title: string(KGroupModify), Text: "group update"},
	KGroupAutoKick:      {Title: string(KGroupAutoKick), Text: "user auto kicked"},
	KGroupAuthenticate:  {Title: string(KGroupAuthenticate), Text: "group authenticate"},

	// presence
	KPresenceOnline:         {Title: string(KPresenceOnline), Text: "presence online"},
	KPresenceOffline:        {Title: string(KPresenceOffline), Text: "presence offline"},
	KPresenceAway:           {Title: string(KPresenceAway), Text: "presence away"},
	KPresencePlaying:        {Title: string(KPresencePlaying), Text: "presence playing"},
	KPresenceCustom:         {Title: string(KPresenceCustom), Text: "presence custom"},
	KPresenceDND:            {Title: string(KPresenceDND), Text: "presence dnd"},
	KPresenceChat:           {Title: string(KPresenceChat), Text: "presence chat"},
	KPresencePlayTime:       {Title: string(KPresencePlayTime), Text: "presence play time"},
	KPresenceAuthenticating: {Title: string(KPresenceAuthenticating), Text: "presence authenticating"},

	// endorsement
	KEndorsementIncrement: {Title: string(KEndorsementIncrement), Text: "endorsement increment"},
	KEndorsementReset:     {Title: string(KEndorsementReset), Text: "endorsement reset"},
	KEndorsementRemove:    {Title: string(KEndorsementRemove), Text: "endorsement remove"},

	// client
	KClientCrash: {Title: string(KClientCrash), Text: "client crash"},
}

// Telemetry telemetry object
type Telemetry struct {
	cfg      *config.Config
	client   statsd.ClientInterface
	hostname string
	ki       KinesisInterface
}

type TelemetryInterface interface {
	SendReportEvent(ctx context.Context, meta *ReportTelemetryMeta) error
	SendBlocklistEvent(ctx context.Context, meta *BlocklistTelemetryMeta) error
	SendFriendEvent(ctx context.Context, meta *FriendTelemetryMeta) error
	SendGroupEvent(ctx context.Context, meta *GroupTelemetryMeta) error
	SendEndorsementEvent(ctx context.Context, meta *EndorsementTelemetryMeta) error
	SendGenericEvent(ctx context.Context, meta *GenericTelemetryMeta) error
}

// NewTelemetry create new telemetry object
func NewTelemetry(cfg *config.Config, ki KinesisInterface) *Telemetry {
	var client statsd.ClientInterface
	hostname := ""
	if cfg.DatadogAPMEnabled {
		var err error
		host := os.Getenv("DD_AGENT_HOST")
		if host == "" {
			host = "localhost"
		}
		port := os.Getenv("DD_AGENT_PORT")
		if port == "" {
			port = "8125"
		}
		endpoint := fmt.Sprintf("%s:%s", host, port)
		log.Info().Msgf("telemetry connecting to %s", endpoint)
		client, err = statsd.New(endpoint)
		if err != nil {
			log.Error().Err(err).Msg("failed to create dogstatsd client")
			client = nil
		}

		hostname, err = os.Hostname()
		if err == nil {
			hostname = "unknown"
		}

	} else {
		log.Info().Msgf("dogstatsd not configured")
		client = &NullStatsdClient{}
	}
	return &Telemetry{
		cfg:      cfg,
		client:   client,
		hostname: hostname,
		ki:       ki,
	}
}

func JWTInfo(ctx context.Context) (string, string) {
	token, tokenOK := ctx.Value(constants.BearerAuthJWT).(*jwt.Token)
	if tokenOK {
		return token.Claims.GetApplicationID(), token.Claims.Subject
	}
	return "", ""
}

// SendReportEvent send abuse report tele event
func (t *Telemetry) SendReportEvent(ctx context.Context, meta *ReportTelemetryMeta) error {
	sendToKinesis(ctx, t, meta)
	return nil
}

// SendBlocklistEvent send blocklist tele event
func (t *Telemetry) SendBlocklistEvent(ctx context.Context, meta *BlocklistTelemetryMeta) error {
	sendToKinesis(ctx, t, meta)
	return nil
}

// SendFriendEvent send friend tele event
func (t *Telemetry) SendFriendEvent(ctx context.Context, meta *FriendTelemetryMeta) error {
	sendToKinesis(ctx, t, meta)
	return nil
}

// SendGroupEvent send group tele event
func (t *Telemetry) SendGroupEvent(ctx context.Context, meta *GroupTelemetryMeta) error {
	sendToKinesis(ctx, t, meta)
	return nil
}

// SendEndorsementEvent send friend tele event
func (t *Telemetry) SendEndorsementEvent(ctx context.Context, meta *EndorsementTelemetryMeta) error {
	sendToKinesis(ctx, t, meta)
	return nil
}

// SendGenericEvent send group tele event
func (t *Telemetry) SendGenericEvent(ctx context.Context, meta *GenericTelemetryMeta) error {
	sendToKinesis(ctx, t, meta)
	return nil
}
