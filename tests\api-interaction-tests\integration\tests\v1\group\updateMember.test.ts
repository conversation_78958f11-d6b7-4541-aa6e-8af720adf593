/* eslint-disable @typescript-eslint/no-explicit-any */
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 2,
        joinRequestAction: 'auto-approve',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    // Member1 joins the auto-approve group
    const resp = await socialApi.requestToJoinV1(
      usersTwok.acct["member1"],
      groupId,
      {
        memberid: usersTwok.acct["member1"].publicId,
        status: 'requested',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, resp);

  });
  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["member1"], groupId);
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('A member is promoted to the leader[public v1 happy trusted]', async () => {
    let testCase = {
      description: "group leader promotes group member to be the leader",
      expected: "the group member becomes the group leader; the leader is demoted to a member of the group"
    };

    // The leader promotes member1 to be the leader
    let actualUgmResp: request.Response = await socialApi.updateGroupMember(
      usersTwok.acct["leader"],
      usersTwok.acct["member1"].publicId,
      groupId,
      { role: 'leader' }
    );

    // expect update resp showing member1 promoted to the leader
    const expectedUgmResp = {
      status: StatusCodes.OK,
      body: {
        role: 'leader',
        userid: usersTwok.acct["member1"].publicId,
      },
    };
    socialApi.expectMore(
      () => {expect(actualUgmResp).toMatchObject(expectedUgmResp)},
      testCase,
      {
        resp: actualUgmResp,
        additionalInfo: {
          "fail reason": "group member is not promoted to the leader"
        }
      }
    );

    let actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["leader"], groupId);
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            groupid: groupId,
            role: 'member',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            groupid: groupId,
            role: 'leader',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    // expect
    // - member1 to be the leader of the group
    // - the leader is demoted to a member of the group
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected group leader or member info"
        }
      }
    );
  });
});

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 2,
        joinRequestAction: 'auto-approve',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    const resp = await socialApi.requestToJoinV1(
      usersTwok.acct["member1"],
      groupId,
      {
        memberid: usersTwok.acct["member1"].publicId,
        status: 'requested',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, resp);
  });

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
    done();
  });

  it('Demoting the leader to a member is not allowed[public v1 trusted]', async () => {
    let testCase = {
      description: "group leader attempts to demote him/herself to be a member",
      expected: "the leader is not demoted to be a member"
    };

    // The leader tries to demote him/herself to be a member
    const actualStatuses: request.Response = await socialApi.updateGroupMember(
      usersTwok.acct["leader"],
      usersTwok.acct["leader"].publicId,
      groupId,
      { role: 'member' }
    );

    socialApi.testStatus(StatusCodes.FORBIDDEN, actualStatuses);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );
    const expectedGroupInfos = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            groupid: groupId,
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            groupid: groupId,
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    // expect
    // - the leader is not demoted
    // - the member1 is not promoted
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfos)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected group leader or member info"
        }
      }
    );
  });
});