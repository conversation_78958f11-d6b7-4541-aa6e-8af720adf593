package api

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

func (api *SocialPublicAPI) GetUserPresence(w http.ResponseWriter, r *http.Request) {

	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	appid := token.Claims.GetApplicationID()
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime

	var presenceResponse apipub.PresenceNext
	presenceResponse.Items = []apipub.PresenceResponse{}
	// TODO: [SPOP] - revert the commenting out
	// sessionId := token.Claims.SessionID

	presences, err := api.Cache.GetUserPresences(r.Context(), userid, productid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get user presence")
	}

	if presences != nil {
		// TODO: [SPOP] - revert the commenting out
		//sessionPolicy := pubApi.Ds.GetSessionPolicyConfig(r.Context(), productid)
		for _, presence := range *presences {
			// Filter out presences not associated with current session, if session policy is single session
			// TODO: [SPOP] - revert the commenting out
			/*
				if sessionPolicy.SessionPolicy == "Single" && presence.ActiveSessionid != sessionId {
					continue
				}
			*/

			// if priority and productid params are both nil, append all
			if presence != nil {
				presenceResponse.Items = append(presenceResponse.Items, *presence)
			}

			// clear active group if group doesn't exist.  couldn't remove on deletes consistently well.
			if presence != nil && presence.ActiveGroup != nil {
				activeGroup := apipub.BuildGroupRedisKey(tenant, productid, presence.ActiveGroup.Groupid)
				if !api.Cache.CachedObjExists(r.Context(), activeGroup) {
					err2 := api.UnsetActiveGroup(r.Context(), productid, appid, sessionid, userid, createdTime, expiresTime)
					if err2 != nil {
						log.Error().Str("userid", userid).Str("groupid", presence.ActiveGroup.Groupid).Err(err2).Msgf("failed to unset active group")
					}
				}
			}
		}
	}
	ReturnOK(w, r, presenceResponse)
}

func (api *SocialPublicAPI) SetUserPresence(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	// decode request
	var updateRequest apipub.SetPresenceRequestBody
	if !DecodeBody(w, r, &updateRequest) {
		return
	}

	if !apipub.IsValidPresenceStatus(string(updateRequest.Status)) {
		log.Error().Msgf("presence status is invalid")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidStatus))
		return
	}

	if updateRequest.GameData != nil && len(*updateRequest.GameData) > api.Cfg.PresenceGameDataMax {
		log.Error().Msgf("GameData is too large and greater than %v", api.Cfg.PresenceGameDataMax)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidGameData))
		return
	}

	if updateRequest.Meta != nil {
		ja, _ := json.Marshal(updateRequest.Meta)
		if len(ja) > api.Cfg.MaxMetaSize {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceMetaTooLong))
			return
		}
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	appid := token.Claims.GetApplicationID()
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	var priority int
	var activeGroup *apipub.ActiveGroupResponse
	var userPresence *apipub.PresenceResponse

	//if launcher set priority to launcher automated. Otherwise determin appropriate priority
	if appid == "0730770bdf204acc8e78851f67990847" {
		priority = apipub.PresencePriorityLauncherAutomated
	} else {
		//if presence already exists for appid get priority.  otherwise get lowest available value.
		userPresence, _ = api.Cache.GetPresence(r.Context(), userid, productid, token.Claims.SessionID)
		if userPresence != nil {
			priority = userPresence.Priority
			activeGroup = userPresence.ActiveGroup
		} else {
			priority, _ = api.Cache.GetLowestAvailablePriority(r.Context(), userid, productid)
		}
	}

	// telemetry
	additionalInfo := make(map[string]string)
	switch updateRequest.Status {
	case apipub.Online:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceOnline, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Offline:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceOffline, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Away:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceAway, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Playing:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresencePlaying, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Custom:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceCustom, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Dnd:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceDND, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Chat:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceChat, productid, userid, ost, &appid, &additionalInfo))
	case apipub.Authenticating:
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresenceAuthenticating, productid, userid, ost, &appid, &additionalInfo))
	}

	// track the time spent paying
	if userPresence != nil && userPresence.Status == apipub.Playing && updateRequest.Status != apipub.Playing {
		playTime := time.Since(userPresence.Timestamp)

		addInfo := make(map[string]string)
		addInfo["playtime"] = playTime.String()

		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KPresencePlayTime, productid, userid, ost, &appid, &addInfo))
	}

	// get original token from auth header to get platformid
	var platformid string
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	if tenant == "dna" {
		authHeader, err := authheader.GetAuthHeaderArr(r)
		if err == nil {
			ogToken, err2 := jwt.ParseJWTTokenWithoutValidation(authHeader[1])
			// Do the check this way instead of using IsFullAccount() because it needs to be a platform account that is linked to a parent account.
			// A full account only token should return blank
			if err2 == nil && ogToken.Claims.DnaAccountType == jwt.PlatformAccount && ogToken.Claims.DnaFullAccountID != "" {
				platformid = ogToken.Claims.Subject
			}
		}
	}

	newPresence := apipub.PresenceResponse{
		Userid:            userid,
		Productid:         productid,
		Status:            updateRequest.Status,
		CustomStatus:      updateRequest.CustomStatus,
		RichPresence:      updateRequest.RichPresence,
		GameData:          updateRequest.GameData,
		GameName:          updateRequest.GameName,
		JoinContext:       updateRequest.JoinContext,
		Meta:              updateRequest.Meta,
		Ttl:               updateRequest.Ttl,
		Priority:          priority,
		Timestamp:         time.Now().UTC(),
		ActiveGroup:       activeGroup,
		OnlineServiceType: ost,
		Platformid:        &platformid,
	}

	if activeGroup != nil {
		newPresence.ActiveGroup = activeGroup
	}

	if updateRequest.Ttl != nil && *updateRequest.Ttl > 0 {
		if utils.IsNonProdCluster() || (int(*updateRequest.Ttl) <= api.Cfg.PresenceTimeoutMax && int(*updateRequest.Ttl) >= api.Cfg.PresenceTimeoutMin) {
			// pubApi.presenceTimer
			newPresence.Ttl = updateRequest.Ttl
		} else {
			ttl := 0
			if updateRequest.Ttl != nil {
				ttl = int(*updateRequest.Ttl)
			}
			log.Error().Int("ttl", ttl).Msgf("ttl is out of bounds")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceInvalidKeepAlive))
			return
		}
	}

	err2 := api.Cache.SavePresence(r.Context(), &newPresence, productid, appid, sessionid, userid, createdTime, expiresTime, true)
	if err2 != nil {
		log.Error().Err(err2).Msgf("save and broadcast presence failed")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EPresenceSaveAndBroadcastFailed))
		return
	}

	ReturnEmptyOK(w, r)
}

// PresenceHeartBeat trigger a heartbeat for presence
func (api *SocialPublicAPI) PresenceHeartBeat(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID

	offlinePresence := &apipub.PresenceResponse{
		Userid:    userid,
		Productid: productid,
		Status:    apipub.Offline,
		Timestamp: time.Now().UTC(),
	}

	presence, err := api.Cache.GetPresence(r.Context(), userid, productid, token.Claims.SessionID)
	if err != nil {
		log.Error().Err(err).Msgf("GetUserPresence(%v, %v) failed", userid, productid)
		ReturnOK(w, r, offlinePresence)
		return
	}

	if presence == nil {
		ReturnOK(w, r, offlinePresence)
		return
	}

	err = api.Cache.RefreshPresenceKeepAlive(r.Context(), presence)
	if err != nil {
		log.Error().Err(err).Msgf("RefreshPresenceKeepAlive(%v, %v) failed", userid, productid)
	}

	ReturnOK(w, r, presence)
}

// ClearPresence clears user presence
func (api *SocialPublicAPI) ClearPresence(w http.ResponseWriter, r *http.Request) {

	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID

	err := api.ClearPresenceFromCache(r.Context(), api.Cache, tenant, userid, productid, token.Claims.SessionID)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EPresenceGetUserPresenceFailed))
		return
	}

	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) ClearPresenceFromCache(ctx context.Context, rc cache.RedisCacheInterface, tenant, userid, productid, sessionid string) error {
	log := logger.FromContext(ctx)
	presence, err := rc.GetPresence(ctx, userid, productid, sessionid)
	if err != nil {
		log.Error().Err(err).Msgf("could not find user %s presence by product %s", userid, productid)
		return err
	}
	if presence == nil {
		return nil
	}

	rc.DeletePresence(ctx, presence)

	//send empty presence mqtt messages to show cleared presence status.
	emptyPresence := apipub.PresenceResponse{}

	messenger.SendMqttMessage(ctx, api.Cfg, presence.Topic(tenant), messenger.MqttMessageTypePresence, emptyPresence)
	groups, _, err := rc.GetUserGroups(ctx, userid, productid, nil, nil)
	if err != nil {
		log.Err(err).Str("userid", userid).Str("productid", productid).Str("tenant", tenant).Msg("failed to get groups for user")
	} else {
		if groups != nil {
			for _, group := range *groups {
				messenger.SendMqttMessage(ctx, api.Cfg, group.Topic(tenant), messenger.MqttMessageTypePresence, emptyPresence)
			}
		}
	}

	return nil
}

// SetActiveGroup sets the active group for a user's presence for the current product. Also subscribes to the group presence object.
func (api *SocialPublicAPI) SetActiveGroup(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	appid := token.Claims.GetApplicationID()
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime

	// decode request
	var activeGroupid apipub.SetActiveGroupRequestBody
	if !DecodeBody(w, r, &activeGroupid) {
		return
	}

	group, err := api.Cache.GetGroup(r.Context(), productid, activeGroupid.ActiveGroupid)
	if err != nil {
		msg := "failed to find a group"
		log.Error().Err(err).Str("productid", productid).Str("activeGroupid", activeGroupid.ActiveGroupid).Str("event", msg).Msg(msg)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EGroupsGeneric))
		return
	}

	if group == nil {
		msg := "could not find a group"
		log.Error().Str("productid", productid).Str("activeGroupid", activeGroupid.ActiveGroupid).Str("event", msg).Msg(msg)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotFound))
		return
	}

	_, err = api.Cache.SetActiveGroup(r.Context(), group, productid, appid, sessionid, userid, createdTime, expiresTime, false)
	if err != nil {
		format := "failed to setActiveGroupHelper"
		log.Error().Err(err).Str("event", format).Str("group", group.Groupid).Str("productid", productid).Str("userid", userid).Str("sessionid", sessionid).Msg(format)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EGroupsSetActiveGroupFailed))
	}

	ReturnEmptyOK(w, r)
}

// ClearActiveGroup clears the active group for the user/product
func (api *SocialPublicAPI) ClearActiveGroup(w http.ResponseWriter, r *http.Request) {

	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID

	err := api.clearActiveGroup(r.Context(), userid, productid, nil)
	if err != nil {
		errs.Return(w, r, err)
		return
	}

	ReturnOK(w, r, apipub.EmptyObject{})
}

// clearActiveGroup clears the active group for the user/product if it matches the groupid or if the group Id is nil
func (api *SocialPublicAPI) clearActiveGroup(ctx context.Context, userid, productid string, groupid *string) *errs.Error {
	log := logger.FromContext(ctx)

	//check for presence, return 404 error if not there.
	presences, err := api.Cache.GetUserPresences(ctx, userid, productid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get user presence")
		return errs.New(http.StatusNotFound, errs.EPresenceNotFound)
	}

	if presences != nil {
		for _, presence := range *presences {
			if presence != nil && presence.ActiveGroup != nil &&
				(groupid == nil || *groupid == presence.ActiveGroup.Groupid) {
				presence.ActiveGroup = nil
				api.Cache.SavePresence(ctx, presence, productid, "", "", userid, int64(0), int64(0), true)
			}
		}
	}
	return nil
}

// UnsetActiveGroup unsets the active group for a user's presence for the current product. Also unsubscribes from the group presence object.
func (api *SocialPublicAPI) UnsetActiveGroup(ctx context.Context, productid, appid, sessionid, userid string, createdTime, expiresTime int64) *errs.Error {
	log := logger.FromContext(ctx)
	presence, err := api.Cache.GetPresence(ctx, userid, productid, sessionid)
	if err != nil {
		log.Error().Str("userid", userid).Str("productid", productid).Err(err).Msgf("failed to get user presence")
		return nil
	}
	if presence == nil {
		return nil
	}
	presence.ActiveGroup = nil
	api.Cache.SavePresence(ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, true)
	return nil
}
