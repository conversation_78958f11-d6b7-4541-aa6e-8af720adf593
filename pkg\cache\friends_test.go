package cache

import (
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func Test_Friendlist(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	var userid, friendid, tenant string
	var friend *apipub.FriendResponse
	var friends *[]*apipub.FriendResponse
	var err error

	g.Describe("set get delete friendlist", func() {
		g.Before(func() {
			userid = utils.GenerateRandomDNAID()
			friendid = utils.GenerateRandomDNAID()

			friend = &apipub.FriendResponse{
				Userid:   userid,
				Friendid: friendid,
				Status:   apipub.Pending,
			}

			rc.SetFriend(ctx, friend, time.Duration(60)*time.Second)

			sub := index.NewUserRelationSubject("dna", userid)
			key := sub.FriendsListKey()
			idx := index.NewSecondaryIndex(*key, apipub.BuildFriendRedisKey(tenant, userid, friendid))

			err = rc.setSecondaryIndex(ctx, idx)

		})

		g.It("get friendlist ", func() {

			limit := int64(10)
			next := ""
			friends, next, err = rc.GetFriends(ctx, userid, nil, &limit, &next)
			g.Assert(err).IsNil()
			g.Assert(friends).IsNotNil()
		})

		g.After(func() {
			rc.DeleteFriend(ctx, friend)
		})
	})
}
