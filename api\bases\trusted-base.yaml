openapi: 3.0.3
info:
  title: Social Trusted API
  version: 'REPLACE_ME'
servers:
  - url: https://social-trusted-develop.dev.d2dragon.net/v2
  - url: https://social-trusted-integration.d2dragon.net/v2
  - url: https://social-trusted-staging.d2dragon.net/v2
  - url: https://social-trusted-cert.d2dragon.net/v2
  - url: https://social-trusted-production.d2dragon.net/v2
  - url: /v2
tags:
  - name: Groups
    description: Create and manage groups of users
  - name: Discovery
    description: Update discovery info for a product
  - name: Status
    description: Get health and version info
paths:
  $ref: '../paths/trusted-paths.yaml'
components:
  $ref: '../securityschemas/bearerAuth.yaml'
security:
  - bearerAuth: []
