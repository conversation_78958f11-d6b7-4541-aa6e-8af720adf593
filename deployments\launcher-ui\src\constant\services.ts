// PAGE STATE
export const EVENT_FRIENDS_PAGE_MOUNT = 'onFriendsPageMount';

// EVENTS
export const EVENT_USER_PROFILE_FETCH_REQUEST = 'onUserPriofileFetchRequest';
export const EVENT_USER_PROFILE_FETCH_RESULT = 'onUserPriofileFetchResult';
export const EVENT_FRIENDS_FETCH = 'onfriendsFetch';
export const EVENT_FRIENDS_FETCH_RESULT = 'onfriendsFetchResult';
export const EVENT_PENDING_FRIENDS_FETCH = 'onPendingFriendsFetch';
export const EVENT_PENDING_FRIENDS_FETCH_RESULT = 'onPendingFriendsFetchResult';
export const EVENT_PRESENCE_CHANGED = 'onPresenceChanged';
export const EVENT_LANGUAGE_CHANGED = 'onLanguageChanged';
export const EVENT_FRIEND_REQUEST_MAKE = 'onFriendRequestMake';
export const EVENT_FRIEND_REQUEST_MAKE_ERROR = 'onFriendRequestMakeError';
export const EVENT_FRIEND_REQUEST_VIEWED = 'onFriendRequestViewed';
export const EVENT_FRIEND_REQUEST_SENT = 'onFriendRequestSent';
export const EVENT_FRIEND_REQUEST_DELETE = 'onFriendRequestDelete';
export const EVENT_FRIEND_REQUEST_DELETED = 'onFriendRequestDeleted';
export const EVENT_FRIEND_REQUEST_DELETE_ERROR = 'onFriendRequestDeleteError';
export const EVENT_FRIEND_DELETE = 'onFriendDelete';
export const EVENT_FRIEND_DELETED = 'onFriendDeleted';
export const EVENT_FRIEND_DELETE_ERROR = 'onFriendDeleteError';
export const EVENT_FRIEND_REQUEST_ACCEPT = 'onFriendRequestAccept';
export const EVENT_FRIEND_REQUEST_ACCEPTED = 'onFriendRequestAccepted';
export const EVENT_FRIEND_REQUEST_ACCEPT_ERROR = 'onFriendRequestAcceptError';
export const EVENT_FRIENDS_ADD_WINDOW_OPEN = 'onFriendsAddWindowOpen';
export const EVENT_FRIENDS_ADD_WINDOW_CLOSE = 'onFriendsAddWindowClose';
export const EVENT_FRIENDS_ADD_WINDOW_MINIMIZE = 'onFriendsAddWindowMinimize';
export const EVENT_FRIENDS_SEARCH_REQUEST = 'onFriendsSearchRequest';
export const EVENT_FRIENDS_SEARCH_RESULT = 'onFriendsSearchResult';

export const EVENT_FRIENDS_WINDOW_CLOSE = 'onFriendsWindowClose';
export const EVENT_FRIENDS_WINDOW_MINIMIZE = 'onFriendsWindowMinimize';

// STEAM EVENTS
export const EVENT_STEAM_LINK_WINDOW_OPEN = 'onSteamLinkWindowOpen';
export const EVENT_STEAM_ACCOUNT_LINKING = 'onSteanAccountLinking';
export const EVENT_STEAM_ACCOUNT_LINKED = 'onSteamAccountLinked';
export const EVENT_STEAM_LINK_WINDOW_CLOSE = 'onSteamLinkWindowClose';
export const EVENT_STEAM_LINK_WINDOW_CLOSED = 'onSteamLinkWindowClosed';
export const EVENT_STEAM_FRIENDS_FETCH = 'onSteamFriendsFetch';
export const EVENT_STEAM_FRIENDS_FETCH_RESULT = 'onSteamFriendsFetchResult';
export const EVENT_STEAM_CONNECT_PAGE_EXIT = 'onSteamConnectPageExit';

// MQTT EVENTS
export const EVENT_MQTT_PRESENCE_MESSAGE_RECEIVED =
  'onMqttPresenceMessageReceived';

export const EVENT_MQTT_FRIEND_INVITE_MESSAGE_RECEIVED =
  'onMqttFriendInvtieMessageReceived';

export const EVENT_MQTT_FRIEND_REMOVED_MESSAGE_RECEIVED =
  'onMqttFriendRemovedMessageReceived';

// MQTT
export const MQTT_BROKER_URL = 'wss://social-service-develop.d2dragon.net/mqtt';
export const MQTT_MESSAGE_TOPIC = {
  presence: 'presence',
  user: 'user',
};

export const STEAM_LINK_URL =
  'https://steamcommunity.com/openid/login?openid.claimed_id=http://specs.openid.net/auth/2.0/identifier_select&openid.identity=http://specs.openid.net/auth/2.0/identifier_select&openid.mode=checkid_setup&openid.ns=http://specs.openid.net/auth/2.0';
export const STEAM_LINK_WINDOW_NAME = 'linkToSteam';

export const LINK_WINDOW_FEATURES =
  'toolbar=no, menubar=no, width=600, height=700, top=100, left=100';

export const PRESENCE_STATUS = {
  online: 'online',
  away: 'away',
  playing: 'playing',
  offline: 'offline',
};
