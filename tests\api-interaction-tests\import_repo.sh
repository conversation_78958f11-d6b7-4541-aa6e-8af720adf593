#!/bin/bash

mkdir __temp

cd __temp

echo "cloning social-auto"
git clone https://github.com/t2gp-harry/social-auto.git

cd social-auto

if [ -s ../../last_commit_id ]; then
    lci=`cat ../../last_commit_id`
    echo "last commit id is: $lci"
    git log --pretty=format:"%h %an %s" $lci..HEAD > ../../commit_history
else
    echo "last commit id not found. Getting the whole history."
    git log --pretty=format:"%h %an %s" > ../../commit_history
fi

echo "Update the last commit id to be HEAD"
git log --pretty=format:"%h" HEAD^..HEAD > ../../last_commit_id

cd ..

echo "remove git info"
rm -rf social-auto/.git

cd ..

echo "wiping out test scripts"
rm -rf integration

echo "copying repo files"
cp -a __temp/social-auto/. .

echo "removing temp files"
rm -rf __temp
