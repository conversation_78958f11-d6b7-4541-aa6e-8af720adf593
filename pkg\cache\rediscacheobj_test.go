package cache

import (
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func Test_CachedObjects(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	var groupid, productid, memberid, user2 string
	var group *apipub.GroupResponse
	var members *[]apipub.GroupMemberResponse

	groupid = utils.GenerateRandomDNAID()
	productid = utils.GenerateRandomDNAID()
	memberid = utils.GenerateRandomDNAID()
	user2 = utils.GenerateRandomDNAID()

	members = &[]apipub.GroupMemberResponse{
		{
			Userid:    memberid,
			Productid: productid,
			Role:      "leader",
		},
		{
			Userid:    user2,
			Productid: productid,
			Role:      "member",
		},
	}
	nowutc := time.Now().UTC()
	group = &apipub.GroupResponse{
		Groupid:   groupid,
		Productid: productid,
		Members:   members,
		Created:   &nowutc,
	}

	g.Describe("getCachedObjects", func() {

		g.It("should return err if empty key", func() {
			_, err := getCachedObject[apipub.GroupResponse](ctx, rc, "")
			g.Assert(err).IsNotNil()
			g.Assert(errs.IsEqual(err, errs.ERedisInvalidKey)).IsTrue()
		})
		g.It("set get del group ", func() {

			err := setCachedObject(ctx, rc, group, group.RedisKey("test"), time.Duration(cfg.TtlDefault)*time.Second)
			g.Assert(err).IsNil()
			groupRet, err := getCachedObject[apipub.GroupResponse](ctx, rc, group.RedisKey("test"))
			g.Assert(err).IsNil()
			g.Assert(groupRet).IsNotNil()
			err = rc.DeleteCachedObj(ctx, group.RedisKey("test"))
			g.Assert(err).IsNil()
			groupRet, err = getCachedObject[apipub.GroupResponse](ctx, rc, group.RedisKey("test"))
			g.Assert(err).IsNil()
			g.Assert(groupRet).IsNil()
		})
	})
}
