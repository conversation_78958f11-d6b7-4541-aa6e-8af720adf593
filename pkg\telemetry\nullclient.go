package telemetry

import (
	"time"

	"github.com/DataDog/datadog-go/v5/statsd"
)

type NullStatsdClient struct{}

func (c *NullStatsdClient) Gauge(name string, value float64, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Count(name string, value int64, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Histogram(name string, value float64, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Distribution(name string, value float64, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Decr(name string, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Incr(name string, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Set(name string, value string, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Timing(name string, value time.Duration, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) TimeInMilliseconds(name string, value float64, tags []string, rate float64) error {
	return nil
}
func (c *NullStatsdClient) Event(e *statsd.Event) error {
	return nil
}
func (c *NullStatsdClient) SimpleEvent(title, text string) error {
	return nil
}
func (c *NullStatsdClient) ServiceCheck(sc *statsd.ServiceCheck) error {
	return nil
}
func (c *NullStatsdClient) SimpleServiceCheck(name string, status statsd.ServiceCheckStatus) error {
	return nil
}
func (c *NullStatsdClient) Close() error {
	return nil
}
func (c *NullStatsdClient) Flush() error {
	return nil
}
func (c *NullStatsdClient) IsClosed() bool {
	return true
}
func (c *NullStatsdClient) GetTelemetry() statsd.Telemetry {
	return statsd.Telemetry{}
}
func (c *NullStatsdClient) CountWithTimestamp(name string, value int64, tags []string, rate float64, timestamp time.Time) error {
	return nil
}
func (c *NullStatsdClient) GaugeWithTimestamp(name string, value float64, tags []string, rate float64, timestamp time.Time) error {
	return nil
}
