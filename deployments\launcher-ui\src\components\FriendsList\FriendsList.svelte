<script lang="ts">
  import { writable } from 'svelte/store';
  import { ACCORDION_MODE } from '../../constant';
  import { useFriendsQuery, useTranslator } from '../../hooks';
  import {
    isOffline,
    isOnline,
    isPlaying,
    isQueryError,
    isQueryLoading,
  } from '../../utils';
  import { Accordion, AccordionSection } from '../Accordion';
  import type { CustomEventMap } from '../CustomInput';
  import { FriendCard } from '../FriendCard';
  import { FriendsListActionBar } from '../FriendsListActionBar';
  import { LoadingSpinner } from '../LoadingSpinner';
  import FriendsListEmptry from './FriendsListEmpty.svelte';
  import FriendsListError from './FriendsListError.svelte';

  const searchTerm = writable('');

  const t = useTranslator();
  const friendsQueryResult = useFriendsQuery();

  const onSearchTextChange = ({
    detail,
  }: CustomEvent<CustomEventMap['inputChange']>) => {
    searchTerm.set(detail.text);
  };

  $: myFriends = $friendsQueryResult?.data || [];

  $: filteredFriends = myFriends.filter(f => {
    return f.name.toLowerCase().includes($searchTerm.toLowerCase());
  });

  $: onlineFriends = filteredFriends.filter(friend =>
    friend.presence?.some(p => isOnline(p.status))
  );
  $: playingFriends = filteredFriends.filter(friend =>
    friend.presence?.some(p => isPlaying(p.status))
  );

  $: offlineFriends = filteredFriends.filter(friend =>
    friend.presence?.some(p => isOffline(p.status))
  );

  $: console.log(filteredFriends, onlineFriends);

  $: queryLoading = isQueryLoading($friendsQueryResult);
  $: queryError = isQueryError($friendsQueryResult);

  $: noFriends = myFriends.length === 0;
</script>

<style>
  .friends-list {
    height: 100%;
  }

  .friends-list .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .friends-list .content :global(.accordion) {
    display: flex;
    flex-direction: column;
  }
  .friends-list .content :global(.online-section-container) {
    flex: 0;
  }
</style>

<div class="friends-list">
  <FriendsListActionBar on:inputChange="{onSearchTextChange}" />
  <div class="content">
    {#if queryLoading}
      <LoadingSpinner />
    {:else if queryError}
      <FriendsListError />
    {:else if noFriends}
      <FriendsListEmptry />
    {:else}
      <Accordion
        value="{['friend']}"
        className="accordion"
        mode="{ACCORDION_MODE.single}"
      >
        <AccordionSection
          title="{`${$t('friend')} (${onlineFriends.length}/${
            myFriends.length
          })`}"
          key="friend"
          mode="{ACCORDION_MODE.single}"
          containerClassName="online-section-container"
        >
          {#each onlineFriends as friend}
            <FriendCard friend="{friend}" />
          {/each}
          {#each playingFriends as friend}
            <FriendCard friend="{friend}" />
          {/each}
          {#each offlineFriends as friend}
            <FriendCard friend="{friend}" />
          {/each}
        </AccordionSection>
      </Accordion>
    {/if}
  </div>
</div>
