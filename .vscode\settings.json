{"erlang.includePaths": ["./deployments/vmq-plugin-social/_build/default/lib"], "editor.quickSuggestions": {"other": true, "comments": true, "strings": false}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features", "editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false}, "yaml.schemas": {"https://json.schemastore.org/github-workflow.json": ".github/workflows/*"}, "erlang.formattingLineLength": 300, "editor.formatOnSave": true}