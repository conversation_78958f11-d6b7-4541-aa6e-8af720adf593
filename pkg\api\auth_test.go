package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/franela/goblin"
	"github.com/jarcoal/httpmock"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"go.uber.org/mock/gomock"
)

// Possibly delete this method, have the Main Test run Login()
// and then have a TestLogout Function or something..
func TestLoginFlow(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	loginUrl := fmt.Sprintf("%s/auth/tokens", cfg.SsoURL)
	//refreshUrl := fmt.Sprintf("%s/auth/tokens", cfg.SsoURL)
	refreshToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nWq0FnaTlrsbRXjTs8DfFZX7mN3yNQoMAouai81pc_4"
	accessToken := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MzU4NDI3NDEsImlhdCI6MTYzNTgzOTE0MSwianRpIjoiZTk5ODA4YzRmZjk1NDM1ZjgyN2FjNzUzYjM2MGVmMWEiLCJ0dHkiOjAsInBpZCI6IjQwMjlhNmZmZTk5MjRmOTY5OTU1YWEyZTFjMDc4MmFhIiwiZ2lkIjoiYzdkY2Q2MjJjMmE2NGI2ODgyM2NjNTNmNDliYjEzYjkiLCJsb2MiOiJlbi1VUyIsImN0eSI6IkFzaGJ1cm4iLCJjdHIiOiJVUyIsImxhdCI6MzkuMDQ2OSwibG9uIjotNzcuNDkwMywicnRpIjoiOWY5NzU4NTdlMzJhNDI1MGE4N2QzYzFkNmE2MzNmOGIiLCJyZXgiOjE2MzU4NDYzNDEsImlzcyI6ImUzYzY0ZWU5MGI4MDQ0ZDJiYTM1ZjEyZWExNjFmYWU0Iiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJhdHkiOjMsImFncCI6NSwic2lkIjoiZTBkOTllYTQ0ZGY2NGNhOWFiZjg5ZmIzNzQxNzQzN2YiLCJ2ZXIiOnRydWUsImFnciI6MTA3MCwiZG9iIjoiM2sweGxwK2FZUWtIVlBvcmlMT3V5Zz09In0.XqxDuU1xawmUWD6uIraj5gOCxC7cMapuwEBxHjd0zWQ"
	tokenResponse := `{
		"accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MzU4NDI3NDEsImlhdCI6MTYzNTgzOTE0MSwianRpIjoiZTk5ODA4YzRmZjk1NDM1ZjgyN2FjNzUzYjM2MGVmMWEiLCJ0dHkiOjAsInBpZCI6IjQwMjlhNmZmZTk5MjRmOTY5OTU1YWEyZTFjMDc4MmFhIiwiZ2lkIjoiYzdkY2Q2MjJjMmE2NGI2ODgyM2NjNTNmNDliYjEzYjkiLCJsb2MiOiJlbi1VUyIsImN0eSI6IkFzaGJ1cm4iLCJjdHIiOiJVUyIsImxhdCI6MzkuMDQ2OSwibG9uIjotNzcuNDkwMywicnRpIjoiOWY5NzU4NTdlMzJhNDI1MGE4N2QzYzFkNmE2MzNmOGIiLCJyZXgiOjE2MzU4NDYzNDEsImlzcyI6ImUzYzY0ZWU5MGI4MDQ0ZDJiYTM1ZjEyZWExNjFmYWU0Iiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJhdHkiOjMsImFncCI6NSwic2lkIjoiZTBkOTllYTQ0ZGY2NGNhOWFiZjg5ZmIzNzQxNzQzN2YiLCJ2ZXIiOnRydWUsImFnciI6MTA3MCwiZG9iIjoiM2sweGxwK2FZUWtIVlBvcmlMT3V5Zz09In0.XqxDuU1xawmUWD6uIraj5gOCxC7cMapuwEBxHjd0zWQ",
		"accessTokenExpiresIn": 3600,
		"refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.nWq0FnaTlrsbRXjTs8DfFZX7mN3yNQoMAouai81pc_4",
		"refreshTokenExpiresIn": 7200,
		"accountId": "b287e655461f4b3085c8f244e394ff7e",
		"sessionId": "e0d99ea44df64ca9abf89fb37417437f",
		"legalManifest": [],
		"accountType": 3,
		"displayName": "ctpshared#09979"
	}`

	// Login and profile exists
	body := apipub.LoginRequestBody{
		Email:    "<EMAIL>",
		Password: "D2CTesting",
		Locale:   "en-US",
	}

	var LoginInfo apipub.LoginResponse

	mockLoginResponse := &apipub.LoginResponse{
		AccessToken:          accessToken,
		RefreshToken:         refreshToken,
		AccountId:            "b287e655461f4b3085c8f244e394ff7e",
		AccountType:          3,
		AccessTokenExpiresIn: 3600,
	}

	g.Describe("LoginFlow", func() {
		g.It("login should succeed", func() {
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()

			httpmock.RegisterResponder("POST", loginUrl, httpmock.NewStringResponder(200, tokenResponse))

			reqBodyBytes := new(bytes.Buffer)
			json.NewEncoder(reqBodyBytes).Encode(body)
			r := reqBodyBytes

			mock.id.EXPECT().Login(context.Background(), "<EMAIL>", "D2CTesting", "en-US", "e3c64ee90b8044d2ba35f12ea161fae4").Return(mockLoginResponse, nil)
			mock.tele.EXPECT().SendGenericEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			req := httptest.NewRequest("GET", "http://example.com/foo", r)
			w := httptest.NewRecorder()
			mock.api.Login(w, req)

			resp := w.Result()
			respBody, _ := io.ReadAll(resp.Body)

			json.Unmarshal(respBody, &LoginInfo)

			expected := apipub.LoginResponse{
				AccountId:            "b287e655461f4b3085c8f244e394ff7e",
				AccountType:          3,
				AccessTokenExpiresIn: 3600,
			}

			g.Assert(LoginInfo.AccessToken != "").IsTrue()
			g.Assert(LoginInfo.RefreshToken != "").IsTrue()
			g.Assert(LoginInfo.AccountId).Equal(expected.AccountId)
			g.Assert(LoginInfo.AccountType).Equal(expected.AccountType)
			g.Assert(LoginInfo.AccessTokenExpiresIn).Equal(expected.AccessTokenExpiresIn)
		})

		// g.It("refresh should succeed", func() {
		// 	httpmock.ActivateNonDefault(http.DefaultClient)
		// 	defer httpmock.DeactivateAndReset()

		// 	httpmock.RegisterResponder("POST", refreshUrl, httpmock.NewStringResponder(200, tokenResponse))

		// 	// Refresh Token
		// 	refreshTokenBody := apipub.RefreshTokenRequest{
		// 		RefreshToken: refreshToken,
		// 		Locale:       "en-US",
		// 	}

		// 	reqBodyBytes := new(bytes.Buffer)
		// 	json.NewEncoder(reqBodyBytes).Encode(refreshTokenBody)
		// 	r := reqBodyBytes
		// 	req := httptest.NewRequest("GET", "http://example.com/foo", r)

		// 	mock := NewMockAPI(t)
		// 	defer mock.ctrl.Finish()

		// 	mock.id.EXPECT().RefreshToken(context.Background(), refreshToken, "en-US").Return(mockLoginResponse, nil)
		// 	mock.tele.EXPECT().SendGenericEvent(context.Background(), gomock.Any()).AnyTimes()
		// 	w := httptest.NewRecorder()
		// 	mock.api.RefreshToken(w, req)
		// 	g.Assert(w.Code).Equal(200)
		// })

		g.It("logout should succeed", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			req := httptest.NewRequest("GET", "http://example.com/foo", nil)
			ctx := context.WithValue(context.Background(), constants.BearerAuthString, accessToken)
			req = req.WithContext(ctx)
			req.Header.Add("Authorization", "Bearer "+LoginInfo.AccessToken)
			w := httptest.NewRecorder()

			mock.id.EXPECT().Logout(ctx, accessToken).Return(nil)
			mock.tele.EXPECT().SendGenericEvent(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			// Logout
			mock.api.Logout(w, req)
			g.Assert(w.Code).Equal(200)
		})
	})

}

func TestGetAuthHeader(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GetAuthHeader", func() {
		g.It("should return error when there is not auth header", func() {
			req := httptest.NewRequest("GET", "http://example.com/foo", nil)
			// Test no Auth Header

			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			_, err := authheader.GetAuthorizationHeaderStr(req)
			g.Assert(err).IsNotNil()
		})

		g.It("should return error incorrect auth header format", func() {
			req := httptest.NewRequest("GET", "http://example.com/foo", nil)

			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			// Test incorrect auth header format
			req.Header.Add("Authorization", "Bearer boop test")
			_, err := authheader.GetAuthHeaderArr(req)
			g.Assert(err).IsNotNil()
		})

		g.It("should return succeed with appropriate auth header", func() {
			req := httptest.NewRequest("GET", "http://example.com/foo", nil)

			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()

			// appropriate header
			req.Header.Del("Authorization")
			req.Header.Add("Authorization", "Bearer awerlwerjlwekjrlwekrj")
			_, err := authheader.GetAuthorizationHeaderStr(req)
			g.Assert(err).IsNil()
		})
	})
}

//The whole ValidateRequest function was moved out of auth.go
// func TestValidateRequest(t *testing.T) {
// 	cfg := config.ConfigForTests()
// 	cfg.AppSecret = "hello"
// 	cfg.SkipAuth = false

// 	g := goblin.Goblin(t)

// 	// request with no auth bearer

// 	g.Describe("ValidateRequest", func() {
// 		g.It("should throw error when request is missing bearerAuth.Scopes", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			ctx := context.Background()
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			_, err := authheader.GetAuthorizationHeaderStr(r)

// 			g.Assert(err).IsNotNil()
// 			g.Assert(err.Error()).Equal("invalid auth header")
// 		})

// 		g.It("should throw error for expired jwt", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			// expired jwt alg=hs256
// 			jwtString := ExpiredJWT
// 			ctx := context.Background()
// 			token, _ := jwt.ParseJWTTokenWithoutValidation(jwtString)
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthString, jwtString)
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			r.Header.Add("Authorization", "Bearer "+jwtString)
// 			mock.id.EXPECT().Authenticate(ctx, []string{"Bearer", jwtString}, true).Return(nil, fmt.Errorf("token Expired"))
// 			//no err here:
// 			//_, err := authheader.GetAuthorizationHeaderStr(r)

// 			g.Assert(err).IsNotNil()
// 			g.Assert(err.Error()).Equal("token Expired")
// 		})

// 		g.It("should throw error for invalid jwt signature", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			// invalid signature jwt alg=hs256
// 			jwtString := InvalidSigJWT
// 			ctx := context.Background()
// 			token, _ := jwt.ParseJWTTokenWithoutValidation(jwtString)
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthString, jwtString)
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			r.Header.Add("Authorization", "Bearer "+jwtString)

// 			mock.id.EXPECT().Authenticate(ctx, []string{"Bearer", jwtString}, true).Return(nil, fmt.Errorf("invalid Token: invalid token"))
// 			_, err := authheader.GetAuthorizationHeaderStr(r)

// 			g.Assert(err).IsNotNil()
// 			// see dna-common auth.validateJwtSignature
// 			g.Assert(err.Error()).Equal("invalid Token: invalid token")
// 		})

// 		g.It("should succeed with invalid jwt signature and skip auth enabled", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			// invalid signature jwt alg=hs256
// 			jwtString := InvalidSigJWT
// 			ctx := context.Background()
// 			token, _ := jwt.ParseJWTTokenWithoutValidation(jwtString)
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthString, jwtString)
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)

// 			r.Header.Add("Authorization", "Bearer "+jwtString)

// 			cfg.SkipAuth = true
// 			token, err := oapi.AuthenticateRequest(ctx, r, cfg, nil)
// 			cfg.SkipAuth = false

// 			g.Assert(err).IsNil()
// 			g.Assert(token).IsNotNil()
// 		})

// 		g.It("should fail with nil claims", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()
// 			RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

// 			token, _ := jwt.ParseJWTTokenWithoutValidation(InvalidSigJWT)
// 			token.Claims = nil
// 			ctx := context.Background()
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthString, InvalidSigJWT)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)

// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			token, err := oapi.AuthenticateRequest(ctx, r, cfg, nil)
// 			g.Assert(token).IsNil()
// 			Ω(err.Error()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EInvalidUserID).Error()))
// 		})

// 		g.It("should fail with missing tokens in context", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			// expired jwt alg=hs256
// 			ctx := context.Background()
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			_, err := authheader.GetAuthorizationHeaderStr(r)

// 			g.Assert(err).IsNotNil()
// 			g.Assert(err.Error()).Equal(errs.ErrorString(errs.EInvalidJWT))
// 		})

// 		g.It("should fail with invalid jwt if missing header", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			ctx := context.Background()
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			cfg.SkipAuth = true
// 			_, err := authheader.GetAuthorizationHeaderStr(r)
// 			cfg.SkipAuth = false

// 			g.Assert(err).IsNotNil()
// 			g.Assert(err.Error()).Equal(errs.ErrorString(errs.EInvalidAuthHeader))
// 		})

// 		g.It("should succeed with successful authentication", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			// invalid signature jwt alg=hs256
// 			jwtString := UserPlatformLinkedJWT
// 			ctx := context.Background()
// 			token, _ := jwt.ParseJWTTokenWithoutValidation(jwtString)
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthString, jwtString)
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)

// 			r.Header.Add("Authorization", "Bearer "+jwtString)
// 			mock.id.EXPECT().Authenticate(ctx, []string{"Bearer", jwtString}, true).Return(&authn.AuthenticationData{}, nil)
// 			token, err := oapi.AuthenticateRequest(ctx, r, cfg, nil)

// 			g.Assert(err).IsNil()
// 			g.Assert(token).IsNotNil()
// 			// verify the pai in the jwt has been promoted to subject
// 			g.Assert(token.Claims.Subject).Equal("2017e9305ccc4e5781d076403c1b6725")
// 		})

// 		g.It("should fail because jwt is yet invalid", func() {
// 			mock := NewMockAPI(t)
// 			defer mock.ctrl.Finish()

// 			// expired jwt alg=hs256
// 			jwtString := NotYetValidJWT
// 			ctx := context.Background()
// 			token, _ := jwt.ParseJWTTokenWithoutValidation(jwtString)
// 			ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{""})
// 			ctx = context.WithValue(ctx, middleware.BearerAuthJWT, token)
// 			ctx = context.WithValue(ctx, middleware.BearerAuthString, jwtString)
// 			r, _ := http.NewRequestWithContext(ctx, "GET", "/friends", nil)
// 			r.Header.Add("Authorization", "Bearer "+jwtString)
// 			mock.id.EXPECT().Authenticate(ctx, []string{"Bearer", jwtString}, true).Return(nil, fmt.Errorf("token is not yet valid"))
// 			_, err := oapi.AuthenticateRequest(ctx, r, cfg, nil)

// 			g.Assert(err).IsNotNil()
// 			g.Assert(err.Error()).Equal("token is not yet valid")
// 		})
// 	})
// }
