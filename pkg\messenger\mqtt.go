package messenger

import (
	"bytes"
	"context"
	"net/http"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/segmentio/encoding/json"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// NewMqttMessage create a new MqttEvent
func NewMqttMessage(msgType MqttMessageType, data interface{}) *MqttMessage {
	msg := MqttMessage{
		Type: msgType,
		Data: data,
	}
	return &msg
}

// SendMqttMessage send mqtt message
func SendMqttMessage(ctx context.Context, cfg *config.Config, topic string, msgType MqttMessageType, data interface{}) error {
	fromUserid, jwtStr := getUserForMqtt(ctx, cfg)
	if fromUserid == "" || jwtStr == "" {
		return errs.New(http.StatusUnauthorized, errs.EAuthorizationFailed)
	}
	msg := NewMqttMessage(msgType, data)
	buf := &bytes.Buffer{}
	enc := json.NewEncoder(buf)
	enc.SetEscapeHTML(false)
	err := enc.Encode(msg)
	bytes.TrimRight(buf.Bytes(), `%0A`)

	if err != nil {
		logger.FromContext(ctx).Error().Err(err).Msgf("Marshalling data %v failed", data)
		return nil
	}

	return Publish(ctx, cfg, fromUserid, topic, buf.String(), jwtStr)
}

// SendBinMqttMessage send binary mqtt message
func SendBinMqttMessage(ctx context.Context, cfg *config.Config, topic string, msgType MqttMessageType, data *[]byte) error {
	fromUserid, jwtStr := getUserForMqtt(ctx, cfg)
	if fromUserid == "" || jwtStr == "" {
		return errs.New(http.StatusUnauthorized, errs.EAuthorizationFailed)
	}

	return PublishBin(ctx, cfg, fromUserid, topic, data, jwtStr)
}

func getUserForMqtt(ctx context.Context, cfg *config.Config) (string, string) {
	fromUserid := ""
	jwtStr := ""
	token, tokenOK := ctx.Value(constants.BearerAuthJWT).(*jwt.Token)
	if tokenOK && token != nil {
		fromUserid = token.Claims.Subject
		jwtStr = ctx.Value(constants.BearerAuthString).(string)
	} else {
		//check trusted api login
		fromUserid = utils.GetValueFromContext(ctx, constants.T2GPCtxTrustedId)
		jwtStr = utils.GetValueFromContext(ctx, constants.T2GPCtxTrustedHash)
	}

	if fromUserid == "" || jwtStr == "" {
		// no user info, so use a default user
		fromUserid = cfg.VMQHttpPubUser //os.Getenv("VMQ_HTTPPUB_USER")
		jwtStr = fromUserid
	}
	return fromUserid, jwtStr
}
