import requests

# TODO: TEST
import json
import pprint
pp = pprint.PrettyPrinter(indent=4)

import config
import urllib.parse

def post_auth_login(email, password):
    url = "{}/auth/login".format(config.server)
    headers = {
        "accept" : "application/json",
        "Content-Type" : "application/json"
    }
    data = {
        "email" : email,
        "password" : password,
        "locale" : "en-US"
    }
    response = requests.post(url, headers=headers, json=data)
    
    #print(response.status_code)
    #pp.pprint(json.loads(response.text))

    return response


def post_auth_logout(accessToken):
    url = "{}/auth/logout".format(config.server)
    
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    
    response = requests.post(url, headers=headers)
    
    #print(response.status_code)
    #pp.pprint(json.loads(response.text))

    return response


def post_auth_refresh(refreshToken):
    url = "{}/auth/refresh".format(config.server)
    headers = {
        "accept" : "application/json",
        "Content-Type" : "application/json"
    }
    data = {
        "locale" : "en-US",
        "refreshToken" : refreshToken,
    }
    response = requests.post(url, headers=headers, json=data)
    
#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def get_user_profile(accessToken):
    url = "{}/user/profile".format(config.server)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    response = requests.get(url, headers=headers)
    
#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def patch_user_profile(accessToken, changes):
    url = "{}/user/profile".format(config.server)
    headers = {
        "accept" : "*/*",
        "Authorization" : "Bearer {}".format(accessToken),
        "Content-Type" : "application/json"
    }
    data = {
        **changes
    }
    response = requests.patch(url, headers=headers, json=data)
    
#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def get_friends(accessToken, params):
    ps = ""
    if params:
        ps = "&".join(["{}={}".format(k,v) for k, v in params.items()])
        ps = "?"+ps

    url = "{}/friends{}".format(config.server, ps)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    response = requests.get(url, headers=headers)
    
#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


friend_request_message = "be my friend"
def post_friends(accessToken, userid):
    url = "{}/friends".format(config.server)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken),
        "Content-Type" : "application/json"
    }
    data = {
        "userid" : userid,
        "message" : friend_request_message
    }
    response = requests.post(url, headers=headers, json=data)

#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def get_friends_search(accessToken, platform, q):
    if platform == "":
        query = "?q={}".format(q)
    else:
        query = "?q={}&platform={}".format(q, platform)

    url = "{}/friends/search{}".format(config.server, query)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    response = requests.get(url, headers=headers)

#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def get_friends_import(accessToken, platform, ids):
    query = "?id={}&platform={}".format(urllib.parse.quote_plus(ids), platform)

    url = "{}/friends/import{}".format(config.server, query)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    response = requests.get(url, headers=headers)

#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def patch_friends_id(accessToken, friend_id, viewed):
    url = "{}/friends/{}".format(config.server, friend_id)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken),
        "Content-Type" : "application/json"
    }
    data = {
        "viewed":viewed
    }
    response = requests.patch(url, headers=headers, json=data)

#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def delete_friends_id(accessToken, friend_id):
    url = "{}/friends/{}".format(config.server, friend_id)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    response = requests.delete(url, headers=headers)

#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def get_user_blocklist(accessToken):
    url = "{}/user/blocklist".format(config.server)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken)
    }
    response = requests.get(url, headers=headers)

#    print(response.status_code)
#    pp.pprint(json.loads(response.text))

    return response


def post_user_blocklist(accessToken, addlist, removelist):
    url = "{}/user/blocklist".format(config.server)
    headers = {
        "accept" : "application/json",
        "Authorization" : "Bearer {}".format(accessToken),
        "Content-Type" : "application/json"
    }
    data = {}
    if addlist != None:
        data["add"] = addlist
    if removelist != None:
        data["remove"] = removelist
    response = requests.post(url, headers=headers, json=data)

#    print(response.status_code)
#    print(response.url)
#    print(response.request.body)
#    pp.pprint(json.loads(response.text))

    return response

