name: Build and distribute

on:
  push:
    branches:
      - '*'
  release:
    types:
      - published

jobs:
  build-vernemq:
    name: Build VernemQ
    runs-on: [t2gp-arc-linux]
    env:
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin

    steps:
      - name: Check out code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          submodules: recursive
          token: ${{ secrets.GH_PAT }}
      - name: Short SHA
        uses: benjlevesque/short-sha@v2.1
        id: short-sha
        with:
          length: 8
      - name: Make Build
        shell: bash
        run: make build
      - name: Make Release
        shell: bash
        run: make release
      - name: Make Docker
        run: make docker
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
        with:
          mask-password: 'false'
      - name: Build, tag, and push docker image to Amazon ECR
        env:
          REGISTRY: 354767525209.dkr.ecr.us-east-1.amazonaws.com
          REPOSITORY: t2gp-vernemq
          IMAGE_TAG: ${{ steps.short-sha.outputs.sha }}
        run: |
          docker build -t $REGISTRY/$REPOSITORY:$IMAGE_TAG -f docker/Dockerfile .
          docker push $REGISTRY/$REPOSITORY:$IMAGE_TAG
      - name: Upload Release to Bucket
        run: |
          aws s3 cp --region us-east-1 _build/vmq-plugin-social.tar.gz s3://${{ env.VERNEMQ_PLUGIN_BUCKET }}/${{ steps.short-sha.outputs.sha }}.tar.gz
      - name: Cleanup
        uses: rokroskar/workflow-run-cleanup-action@v0.3.3
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          DEBUG: FALSE
