package api

//
//import (
//	"github.com/aws/aws-sdk-go-v2/aws"
//	"github.com/franela/goblin"
//	. "github.com/onsi/gomega"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
//	"go.uber.org/mock/gomock"
//	"net/http"
//	"testing"
//)
//
//func TestGetDiscoveryServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//
//	g.Describe("GetDiscovery", func() {
//		productID := "000000000000000000000000deadbeef"
//		tsInfo := apitrusted.TsClientIdInfo{
//			ClientId:  productID,
//			ProductId: productID,
//			TenantId:  "dna",
//			Hash:      TrustedPassHash,
//		}
//		pubUrlHttp := apipub.DiscoveryURL{Type: apipub.Http, Url: "http://localhost:8000/v1"}
//		pubUrlTrusted := apipub.DiscoveryURL{Type: apipub.Trusted, Url: "http://localhost:8005/v1"}
//		pubUrlMqtt := apipub.DiscoveryURL{Type: apipub.Mqtt, Url: "wss://localhost:8080/mqtt"}
//		pubDefault := apipub.Discovery{Description: "default", Id: "default", Urls: []apipub.DiscoveryURL{pubUrlHttp, pubUrlMqtt}, CanList: aws.Bool(true)}
//		pubSecret := apipub.Discovery{Description: "secret", Id: "secret", Urls: []apipub.DiscoveryURL{pubUrlTrusted}}
//
//		pubDiscovery := []apipub.Discovery{
//			pubDefault,
//			{Description: "number2", Id: "number2", Urls: []apipub.DiscoveryURL{pubUrlHttp}, CanList: aws.Bool(true)},
//			pubSecret,
//		}
//
//		g.It("discovery not found", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(nil, nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerGetDiscovery(w, r, apitrusted.ServerGetDiscoveryParams{})
//
//			Expect(w.Code).Should(Equal(http.StatusNotFound))
//		})
//
//		g.It("all discovery found", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&pubDiscovery, nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerGetDiscovery(w, r, apitrusted.ServerGetDiscoveryParams{})
//
//			response := w.Body.String()
//			Expect(w.Code).Should(Equal(http.StatusOK))
//			Ω(response).Should(ContainSubstring(`"id":"default"`))
//			Ω(response).Should(ContainSubstring(`"id":"number2"`))
//			Ω(response).Should(ContainSubstring(`"id":"secret"`))
//		})
//
//		g.It("specific discovery found", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&[]apipub.Discovery{pubSecret}, nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerGetDiscovery(w, r, apitrusted.ServerGetDiscoveryParams{Id: aws.String("secret")})
//
//			response := w.Body.String()
//			Expect(w.Code).Should(Equal(http.StatusOK))
//			Ω(response).Should(ContainSubstring(`"id":"secret"`))
//			Ω(response).ShouldNot(ContainSubstring(`"id":"default"`))
//		})
//
//		g.It("specific discovery not found", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&[]apipub.Discovery{pubSecret}, nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerGetDiscovery(w, r, apitrusted.ServerGetDiscoveryParams{Id: aws.String("secret1")})
//
//			Expect(w.Code).Should(Equal(http.StatusNotFound))
//		})
//	})
//}
//
//func TestSetDiscoveryServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//
//	g.Describe("set/del Discovery", func() {
//		productID := "000000000000000000000000deadbeef"
//		tsInfo := apitrusted.TsClientIdInfo{
//			ClientId:  productID,
//			ProductId: productID,
//			TenantId:  "dna",
//			Hash:      TrustedPassHash,
//		}
//		pubUrlHttp := apipub.DiscoveryURL{Type: apipub.Http, Url: "http://localhost:8000/v1"}
//		pubUrlMqtt := apipub.DiscoveryURL{Type: apipub.Mqtt, Url: "wss://localhost:8080/mqtt"}
//		pubDefault := apipub.Discovery{Description: "default", Id: "default", Urls: []apipub.DiscoveryURL{pubUrlHttp, pubUrlMqtt}, CanList: aws.Bool(true)}
//
//		pubDiscovery := []apipub.Discovery{
//			pubDefault,
//			{Description: "number2", Id: "number2", Urls: []apipub.DiscoveryURL{pubUrlHttp}, CanList: aws.Bool(true)},
//		}
//
//		tsUrlHttp := apitrusted.DiscoveryURL{Type: apitrusted.Http, Url: "http://localhost:8000/v1"}
//		tsUrlTrusted := apitrusted.DiscoveryURL{Type: apitrusted.Trusted, Url: "http://localhost:8005/v1"}
//		tsUrlMqtt := apitrusted.DiscoveryURL{Type: apitrusted.Mqtt, Url: "wss://localhost:8080/mqtt"}
//		tsDefault := apitrusted.Discovery{Description: "default", Id: "default", Urls: []apitrusted.DiscoveryURL{tsUrlHttp, tsUrlMqtt}, CanList: aws.Bool(true)}
//		tsSecret := apitrusted.Discovery{Description: "secret", Id: "secret", Urls: []apitrusted.DiscoveryURL{tsUrlTrusted}}
//
//		tsDiscovery := []apitrusted.Discovery{
//			tsDefault,
//			{Description: "number2", Id: "number2", Urls: []apitrusted.DiscoveryURL{tsUrlHttp}, CanList: aws.Bool(true)},
//		}
//
//		g.It("create discovery", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().SetDiscovery(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w, r := AddBodyToRequestBasic(tsDiscovery, productID, "pass")
//
//			mock.apiTrusted.ServerUpsertDiscovery(w, r)
//
//			response := w.Body.String()
//			Expect(w.Code).Should(Equal(http.StatusCreated))
//			Ω(response).Should(ContainSubstring(`"id":"default"`))
//			Ω(response).Should(ContainSubstring(`"id":"number2"`))
//		})
//
//		g.It("update partial discovery", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&pubDiscovery, nil)
//			mock.ds.EXPECT().SetDiscovery(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w, r := AddBodyToRequestBasic([]apitrusted.Discovery{tsSecret}, productID, "pass")
//
//			mock.apiTrusted.ServerUpsertDiscovery(w, r)
//
//			response := w.Body.String()
//			Expect(w.Code).Should(Equal(http.StatusOK))
//			Ω(response).Should(ContainSubstring(`"id":"default"`))
//			Ω(response).Should(ContainSubstring(`"id":"number2"`))
//			Ω(response).Should(ContainSubstring(`"id":"secret"`))
//		})
//
//		g.It("del single discovery", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&pubDiscovery, nil)
//			mock.ds.EXPECT().SetDiscovery(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerDeleteDiscovery(w, r, apitrusted.ServerDeleteDiscoveryParams{Id: aws.String("secret")})
//
//			Expect(w.Code).Should(Equal(http.StatusOK))
//		})
//
//		g.It("del all discovery empty", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&pubDiscovery, nil)
//			mock.ds.EXPECT().DeleteDiscovery(gomock.Any(), gomock.Any()).Return(nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerDeleteDiscovery(w, r, apitrusted.ServerDeleteDiscoveryParams{Id: aws.String("")})
//
//			Expect(w.Code).Should(Equal(http.StatusOK))
//		})
//
//		g.It("del all discovery nil", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(&pubDiscovery, nil)
//			mock.ds.EXPECT().DeleteDiscovery(gomock.Any(), gomock.Any()).Return(nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerDeleteDiscovery(w, r, apitrusted.ServerDeleteDiscoveryParams{})
//
//			Expect(w.Code).Should(Equal(http.StatusOK))
//		})
//
//		g.It("del missing discovery", func() {
//			mock := NewMockSocialTrustedAPI(t)
//			defer mock.ctrl.Finish()
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.ds.EXPECT().GetDiscovery(gomock.Any(), gomock.Any()).Return(nil, nil)
//
//			w, r := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerDeleteDiscovery(w, r, apitrusted.ServerDeleteDiscoveryParams{})
//
//			Expect(w.Code).Should(Equal(http.StatusNotFound))
//		})
//	})
//}
