#!account_mgmt_cli_env/bin/python3
import social_account_intv
import procarg
import sys
from botocore.exceptions import SSOTokenLoadError, TokenRetrievalError

if __name__ == "__main__":
    a = procarg.ProcArg("Social Test Account Management Tool",
                        [
                            "twok",
                            "pd",
                            "steamp",
                            "epicp",
                            "switchp",
                            "xbxp",
                            "xb1p",
                            "ps4p",
                            "ps5p"
                        ]
                       )

    a.g.add_argument('-a', '--health-check', nargs=3, metavar=('SEARCH_BY', 'QUERY', 'REPLACE_BAD_ACCOUNTS'),
                    help='health check for accounts. \
                    Search_by is "owner", "email", "public ID", "display name", "status", or "remark". \
                    Replace_bad_accounts is either "replace" or "no_replace", and available only when \
                    search_by is "owner", owner is not "None", and there\'s no existing lock')

    # update help text
    for action in a.ap._actions:
        if action.dest == "search_account":
            action.help = action.help.replace('Output_mode is "view", "json", or "csv"',
                                              'Output_mode is "view", "json", "csv", "ci", or "ci_file"')

    # If no arguments, print help and exit
    if len(sys.argv) == 1:
        a.ap.print_help(sys.stderr)
        sys.exit(1)

    args = a.ap.parse_args()

    # Determine account type
    if args.account_type == "twok":
        accountIntv = social_account_intv.SocialTwokAccountInteractive(args.server_type)

    elif args.account_type == "pd":
        accountIntv = social_account_intv.SocialPdAccountInteractive()

    elif args.account_type == "steamp":
        accountIntv = social_account_intv.SocialSteamPAccountInteractive()

    elif args.account_type == "epicp":
        accountIntv = social_account_intv.SocialEpicPAccountInteractive()

    elif args.account_type == "switchp":
        accountIntv = social_account_intv.SocialSwitchPAccountInteractive()

    elif args.account_type == "xbxp":
        accountIntv = social_account_intv.SocialXbxPAccountInteractive()

    elif args.account_type == "xb1p":
        accountIntv = social_account_intv.SocialXb1PAccountInteractive()

    elif args.account_type == "ps4p":
        accountIntv = social_account_intv.SocialPs4PAccountInteractive()

    elif args.account_type == "ps5p":
        accountIntv = social_account_intv.SocialPs5PAccountInteractive()

    else:
        a.ap.print_help(sys.stderr)
        sys.exit(1)

    try:
        a.process_args(args, accountIntv)
    except SSOTokenLoadError as e:
        print(e)
        print("try 'aws sso login'")
    except TokenRetrievalError as e:
        print(e)
        print("try 'aws sso login'")
    except Exception as e:
        if str(e) == "unknown arg":
            if args.health_check != None:
                accountIntv.health_check(*args.health_check)
        else:
            raise(e)