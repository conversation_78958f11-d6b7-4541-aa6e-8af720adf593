package api

//
//import (
//	"context"
//	"github.com/joho/godotenv"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/notification"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/store"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
//	"os"
//
//	"github.com/go-redis/redismock/v9"
//	redis "github.com/redis/go-redis/v9"
//	"go.uber.org/mock/gomock"
//	"testing"
//	"time"
//)
//
//var cfg *config.Config
//var ctx = context.Background()
//var sm *health.ServiceMonitor
//var id *identity.Identity
//var rc *cache.RedisCache
//var ds *store.DataStore
//var testSns notification.SNS
//var ddb *store.DynamoDBWithMonitoring
//var testS3 *store.S3WithMonitoring
//var pubApi *api.SocialPublicAPI
//var t *telemetry.Telemetry
//
//// bcrypt hash for "pass"
//var TrustedPassHash = "$2a$04$XmLdAAZpkmMSKSc0HAh55OePMtNcmNzJHvacQRJ2tCxjNOCMaJU5O"
//
//var User1JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
//var User2JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MzU3NTg5MjUsImlhdCI6MTYzNTc1NTMyNSwianRpIjoiM2MzYTFiNjVkNzFjNGNiOWE4OWZmZDcxMjExODM1M2YiLCJ0dHkiOjAsInBpZCI6IjQwMjlhNmZmZTk5MjRmOTY5OTU1YWEyZTFjMDc4MmFhIiwiZ2lkIjoiYzdkY2Q2MjJjMmE2NGI2ODgyM2NjNTNmNDliYjEzYjkiLCJsb2MiOiJlbi1VUyIsImN0eSI6IkFzaGJ1cm4iLCJjdHIiOiJVUyIsImxhdCI6MzkuMDQ2OSwibG9uIjotNzcuNDkwMywicnRpIjoiMzY2ZDBkYWQ3NmVhNGI4ZmE1YmI1YTRlYTQ1ZTg1MzgiLCJyZXgiOjE2MzU3NjI1MjUsImlzcyI6ImUzYzY0ZWU5MGI4MDQ0ZDJiYTM1ZjEyZWExNjFmYWU0Iiwic3ViIjoiZTEyYzNkZjQ4MDk4NDE0MWIyZjM4NTY0NmIyMDI0ZmEiLCJhdHkiOjMsImFncCI6NSwic2lkIjoiNDFlOTMxNDdiMzkwNGViZThlZGI4ZjBlY2Q1NzM2NDYiLCJ2ZXIiOnRydWUsImFnciI6MTA3MCwiZG9iIjoiM2sweGxwK2FZUWtIVlBvcmlMT3V5Zz09In0."
//var User3JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
//var User4JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
//
//// Platform with parent account (unlinked)
//var UserPlatformUnlinkedJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
//
//// Platform with parent account (linked)
//var UserPlatformLinkedJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
//
//// alg=HS256 sub=user1 secret=hello exp=***********
//var AlgJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************.hyqiRTXTKUwLOENLLgqxN6m3N2_hYtvf9xQfVw8HRiA"
//
//// alg=HS256 sub=user1 secret=goodbye exp=***********
//var InvalidSigJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUwNzQ3NzMzLCJydGkiOiIwMmQ4ZjNlZmI0MGI0NTliOWMzMzExMzY4Y2Y5YmFiMyIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiZWZlNjYxMzA4MjY1NDM4NWI4MTUwYTE2ZDZjOTEzMmMiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6OTk5OTk5OTk5OSwiaWF0IjoxNjUwNzQwNTMzLCJqdGkiOiI3Y2VhYTFmN2FiYjU0MmQxYjI3ZmQ2NTk3ZTVlNzk0NSIsImxhdCI6MzkuMDQ2OX0.FHd5r4KbS61gvbAZ_EWCbpBuYi2IYJjfBNgPZG_jAm8"
//
//// alg=HS256 sub=user1 secret=hello exp=632301738
//var ExpiredJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyMSIsImV4cCI6NjMyMzAxNzM4fQ.31qgsqKetV-v4B-9IpVt2eR5ay71wyKJ1iF123oVRmA"
//
//// alg=HS256 sub=user1 secret=hello iat=33227196787 exp=33227236787
//var NotYetValidJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjMzMjI3MjM2Nzg3LCJpYXQiOjMzMjI3MTk2Nzg3LCJqdGkiOiI5ZDYxMzBiZDQ0ZGQ0OWJmYThmZDYyODdjNjExMmM4NCIsInR0eSI6MCwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJnaWQiOiI3YjAyYTdlMTc1ODk0NjdmODhiMzY1YWQ1YmIxYzJiNyIsImxvYyI6ImVuLUdCIiwiY3R5IjoiU2VhdHRsZSIsImN0ciI6IlVTIiwibGF0Ijo0Ny42MDM0LCJsb24iOi0xMjIuMzQxNCwicnRpIjoiZTViOTk2ZTM2MDVjNDE3M2JhNTZjZDViZDc1ZjJlZmMiLCJyZXgiOjMzMjI3MjM2Nzg4LCJpc3MiOiI5NTVlMmY2YzdjZTM0NWE2YmEwZDRkNGY3N2Q5MjU3ZCIsInN1YiI6ImI2MDU5NWM1MTFlMDRkODFiNWRkOTVlZTQ1MTc5NGQ4IiwiYXR5IjoyLCJwYWkiOiI0ZTIyZDdhNzBiYTU0MDc1ODQ3MWE2ZDdiYzhhOWVkMiIsIm90eSI6MywiYWdwIjo1LCJzaWQiOiJkNjhkMTY3ZWE1Yzg0MDMxOWQ3MTg5NGM4N2Y2OTk0YyIsInZlciI6dHJ1ZSwiZnBpIjoiRlB5MEJ6eFZZUWJNLy96T1ArVnNQeTFSZUpFcWxnb1dmN2VPK2hYZzY3OD0iLCJmcGEiOiJQa3ZwODJTbVlUT2pOZUtrenJIK2V3OUhFV1NtN2NyL0QwajZycWZYT1dJPSIsImRvYiI6InV5Z0E1L28rVkM2TzFDSW1rQmNURWc9PSIsInByaSI6ImI2MDU5NWM1MTFlMDRkODFiNWRkOTVlZTQ1MTc5NGQ4IiwiaW5zIjoiZmQwZGUwMmQ3MDZmNDNkMDk0YjQ0MWM4YmFlZDY5OWYiLCJkdHkiOjExLCJhZ2MiOjMsImFwYyI6NX0.QWZYAvNcg4pCuOhW2cyck8bITmH0CpVpnAdYOOBypKY"
//
//// invalid JWT token
//var BadJWT = "deadbeef"
//
//type MockTrustedAPI struct {
//	ctrl        *gomock.Controller
//	ds          *store.MockDataStoreInterface
//	rc          *cache.MockRedisCacheInterface
//	redisClient *redis.ClusterClient
//	redisMock   redismock.ClusterClientMock
//	apiTrusted  *SocialTrustedAPI
//	//apipub      *api.SocialPublicAPI
//	//apiPriv     *pApi.SocialPrivateAPI
//	//apiTrusted  *tApi.SocialTrustedAPI
//	testTimeout time.Duration
//	tele        *telemetry.MockTelemetryInterface
//	id          *identity.MockIdentityInterface
//}
//
//// NewMockSocialPublicAPI create social pubApi object
//func NewMockSocialTAPI(pubApi *api.SocialPublicAPI) *SocialTrustedAPI {
//	cfg.SkipAuth = true
//	return &SocialTrustedAPI{
//		SocialApi: pubApi,
//	}
//}
//
//// NewMockSocialTrustedAPI create social api object
//func NewMockSocialTrustedAPI(t *testing.T) *MockTrustedAPI {
//	mockCtrl := gomock.NewController(t)
//	client, redisMock := redismock.NewClusterMock()
//
//	localds := store.NewMockDataStoreInterface(mockCtrl)
//	localrc := cache.NewMockRedisCacheInterface(mockCtrl)
//	localtl := telemetry.NewMockTelemetryInterface(mockCtrl)
//	localid := identity.NewMockIdentityInterface(mockCtrl)
//
//	//localPubApi := api.NewSocialPublicAPI(cfg, localds, localrc, sm, localtl, localid, testSns)
//
//	//apiPriv := NewMockSocialPrivateAPI(cfg, localds, localrc, localid)
//	apiTrusted := NewMockSocialTAPI(pubApi)
//
//	return &MockTrustedAPI{
//		ctrl:        mockCtrl,
//		ds:          localds,
//		redisClient: client,
//		redisMock:   redisMock,
//		rc:          localrc,
//		//apipub:      localPubApi,
//		//api:         apiPub,
//		//apiPriv:     apiPriv,
//		apiTrusted:  apiTrusted,
//		testTimeout: time.Second * 30,
//		tele:        localtl,
//		id:          localid,
//	}
//}
//
//func TestMain(m *testing.M) {
//	setup()
//	code := m.Run()
//	teardown()
//	os.Exit(code)
//}
//
//func setup() {
//	godotenv.Load("../../../.env")
//	cfg = config.ConfigForTests()
//	sm = health.NewServiceMonitor("test-social", cfg.Version.Version)
//	id = identity.NewDNAIdentity(cfg, sm)
//	t = telemetry.NewTelemetry(cfg)
//	rc = cache.NewRedisCache(ctx, cfg, t, id)
//	ddb = store.NewDynamoDB(cfg)
//	testS3 = store.NewS3(cfg)
//	ds = store.NewDataStore(cfg, ddb, testS3, id)
//	testSns = notification.SNS{}
//	pubApi = api.NewSocialPublicAPI(cfg, ds, rc, sm, t, nil, testSns)
//
//	clientId := apitrusted.TsClientIdInfo{
//		ClientId:  "c71f50c3533c462785a2fc22f24c9fad",
//		ProductId: "c71f50c3533c462785a2fc22f24c9fad",
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//	rc.SetTsClientId(ctx, &clientId)
//
//}
//func teardown() {
//	clientId := apitrusted.TsClientIdInfo{
//		ClientId:  "c71f50c3533c462785a2fc22f24c9fad",
//		ProductId: "c71f50c3533c462785a2fc22f24c9fad",
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//	rc.DelRangeTsClientId(ctx, &clientId)
//}
