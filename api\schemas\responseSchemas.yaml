accountsNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/searchAccountResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
friendsNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/friendResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
groupChatsNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/chatMessageGroupResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
dmsNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/chatMessageDMResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
presenceNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/presenceResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
groupsNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/groupResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
healthResponse:
  type: object
  required:
    - name
    - version
    - overall-status
    - generated
    - services
  properties:
    name:
      type: string
      example: t2gp-social-service
      nullable: true
    version:
      type: string
      example: v0.1.0-deadbeef
      nullable: true
    overall-status:
      type: string
      example: OK
      nullable: true
    generated:
      type: string
      example: '2023-10-30T20:01:11.199405036Z'
      nullable: true
    services:
      type: object
      nullable: true
      example:
        social-service-dynamodb: FAIL
        social-service-id-dna: OK
        social-service-redis: OK
        social-service-s3: OK
blocklistsNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/blocklistResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
membersNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/groupMemberResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
playedPlayersNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/recentlyPlayedUserResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
abuseReturn:
  type: object
  required:
    - messageId
  description: Abuse report SNS message id
  properties:
    messageId:
      type: string
      example: 4e8f419e-4ac3-5222-b71d-1da980edf33a
emptyObject:
  type: object
  description: Empty object
  example: '{}'
getInviteResponse:
  type: object
  description: schema for response for a user's invites
  required:
    - memberid
    - approverid
    - groupid
    - onlineServiceType
    - displayName
    - firstPartyid
    - isFirstPartyInvite
  properties:
    memberid:
      $ref: './fields.yaml#/dnaid'
    approverid:
      $ref: './fields.yaml#/dnaid'
    groupid:
      $ref: './fields.yaml#/groupid'
    onlineServiceType:
      $ref: './fields.yaml#/onlineServiceType'
    displayName:
      allOf:
        - $ref: './fields.yaml#/dnaDisplayName'
    firstPartyid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/firstPartyid'
    isFirstPartyInvite:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/isFirstPartyInvite'
    canCrossPlay:
      nullable: true
      type: boolean
invitesNext:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/getInviteResponse'
    nextid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/nextid'
joinRequestResponse:
  type: object
  description: schema for a join request response
  required:
    - approverid
    - memberid
    - groupid
    - displayName
  properties:
    approverid:
      $ref: './fields.yaml#/dnaid'
    memberid:
      $ref: './fields.yaml#/dnaid'
    groupid:
      $ref: './fields.yaml#/groupid'
    displayName:
      allOf:
        - $ref: './fields.yaml#/dnaDisplayName'
versionResponse:
  type: object
  required: [ version, gitHash, buildDate ]
  properties:
    version:
      type: string
      example: 1.0.2-6623bad
    gitHash:
      type: string
      example: 6623bad133a5bbf8d6123e445d1c8d9ceeb45548
    buildDate:
      type: string
      example: '2023-10-27-19-19-32'
importBlocklistResponse:
  type: object
  required:
    - blockedids
    - onlineServiceType
  properties:
    blockedids:
      type: array
      items:
        type: string
      example: [ 'b287e655461f4b3085c8f244e394ff7e', 'effe28b27efc6594e43bfc0879b40085' ]
    onlineServiceType:
      $ref: './fields.yaml#/onlineServiceType'
setFriendResponse:
  type: object
  required:
    - status
  properties:
    status:
      $ref: './fields.yaml#/friendStatus'
search2kUserResponse:
  type: object
  required:
    - items
    - nextid
  properties:
    items:
      type: array
      items:
        $ref: '#/twoKSearch'
twoKSearch:
  type: object
  required:
    - userid
    - name
    - links
    - simplePresence
  properties:
    userid:
      type: string
      example: effe28b27efc6594e43bfc0879b40085
      pattern: '^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$'
    name:
      type: string
      example: discopotato
      nullable: true
    links:
      description: Linked accounts. Filtered to current OST.
      type: 'array'
      nullable: true
      items:
        $ref: './schemas.yaml#/accountLinkDNA'
    simplePresence:
      nullable: true
      type: array
      items:
        $ref: '#/simplePresenceResponse'
loginResponse:
  description: Successful authentication response
  type: object
  required:
    - accessToken
    - accessTokenExpiresIn
    - refreshToken
    - refreshTokenExpiresIn
    - accountId
    - accountType
  properties:
    accessToken:
      description: The access token
      type: string
      format: Id
      example: eyJhbGci...0jOtn_Bo
    accessTokenExpiresIn:
      description: The number of seconds until the access token expires
      type: integer
      format: uint32
      example: 3600
    refreshToken:
      description: The refresh token
      type: string
      format: Id
      example: eyJhbGci...xUpOD5qlY
    refreshTokenExpiresIn:
      description: The number of seconds until the refresh token expires
      type: integer
      format: uint32
      example: 7200
    accountId:
      $ref: './fields.yaml#/dnaid'
    accountType:
      description: The type of the account that was authenticated
      type: integer
      format: uint32
      example: 3
serverLoginResponse:
  description: Successful server authentication response
  type: object
  required:
    - accessToken
    - accessTokenExpiresIn
    - refreshToken
    - refreshTokenExpiresIn
    - instanceId
  properties:
    accessToken:
      description: The access token
      type: string
      format: Id
      example: eyJhbGci...0jOtn_Bo
    accessTokenExpiresIn:
      description: The number of seconds until the access token expires
      type: integer
      format: uint32
      example: 3600
    refreshToken:
      description: The refresh token
      type: string
      format: Id
      example: eyJhbGci...xUpOD5qlY
    refreshTokenExpiresIn:
      description: The number of seconds until the refresh token expires
      type: integer
      format: uint32
      example: 7200
    instanceId:
      description: The instance id used to authenticate
      type: string
searchAccountResponse:
  type: object
  description: schema for DNA search results
  required:
    - id
    - type
    - firstPartyId
    - firstPartyAlias
    - parentAccountId
    - onlineServiceType
    - email
    - dob
    - displayName
    - links
    - locale
    - simplePresence
  properties:
    id:
      nullable: true
      description: 'The account Id'
      type: 'string'
      format: 'Id'
    type:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/accountTypeDNA'
    firstPartyId:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/firstPartyid'
    firstPartyAlias:
      nullable: true
      description: 'The account first Party Alias'
      type: 'string'
      format: 'Id'
    parentAccountId:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dnaid'
    onlineServiceType:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
    email:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/email'
    dob:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dob'
    displayName:
      nullable: true
      type: 'string'
      description: 'The display name'
    links:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/links'
    locale:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/locale'
friendResponse:
  type: object
  required:
    - userid
    - friendid
    - invitee
    - status
    - version
    - inviterOST
    - message
    - name
    - viewed
    - presence
    - links
  properties:
    userid:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "userid"
    friendid:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "friendid"
    invitee:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "invitee"
    inviterOST:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
      x-oapi-codegen-extra-tags:
        dynamodbav: "inviterOST"
    message:
      nullable: true
      type: string
      description: message to include with friend request
      example: Want to be my friend?
      x-oapi-codegen-extra-tags:
        dynamodbav: "message"
    name:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dnaDisplayName'
      x-oapi-codegen-extra-tags:
        dynamodbav: "name"
    viewed:
      nullable: true
      type: boolean
      x-oapi-codegen-extra-tags:
        dynamodbav: "viewed"
    presence:
      nullable: true
      type: array
      x-oapi-codegen-extra-tags:
        dynamodbav: "presence"
      items:
        $ref: '#/presenceResponse'
    status:
      allOf:
        - $ref: './fields.yaml#/friendStatus'
      x-oapi-codegen-extra-tags:
        dynamodbav: "status"
    links:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/links'
      x-oapi-codegen-extra-tags:
        dynamodbav: "links"
presenceResponse:
  type: object
  description: a presence record
  x-oapi-codegen-extra-tags:
    dynamodbav: "presence"
  required:
    - userid
    - status
    - timestamp
    - productid
    - gameName
    - priority
    - customStatus
    - onlineServiceType
    - activeSessionid
    - richPresence
    - gameData
    - activeGroup
    - clientid
    - ttl
    - platformid
    - joinContext
  properties:
    userid:
      $ref: './fields.yaml#/dnaid'
    status:
      $ref: './fields.yaml#/presenceStatus'
    customStatus:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/customStatus'
    richPresence:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/richPresence'
    gameName:
      $ref: './fields.yaml#/gameName'
    gameData:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/gameData'
    activeGroup:
      nullable: true
      allOf:
        - $ref: '#/activeGroupResponse'
    productid:
      $ref: './fields.yaml#/productid'
    clientid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/clientid'
    ttl:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/ttl'
      description:
        How long in seconds before this presence will be considered offline if no presence Heartbeat is made.   |
        This is an optional value for those who are not using our MQTT.  People using our MQTT will have this functionality via our mqtt plugin.  Timeout set to 5 minutes for auto drop from group.
      minimum: 35
      maximum: 1800
    priority:
      $ref: './fields.yaml#/priority'
    onlineServiceType:
      $ref: './fields.yaml#/onlineServiceType'
    meta:
      $ref: './schemas.yaml#/meta'
    timestamp:
      $ref: './fields.yaml#/timestamp'
    joinContext:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/joinContext'
    platformid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dnaid'
      description: DNA only. at the time the presence is set, if the token has a pai claim.  this will be the sub claim, which should be the platformid.  we do not guarantee this value will be returned since full account tokens will not have it.
    activeSessionid:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      description: id of active login session.
userProfileResponse:
  type: object
  description: a user's profile record.
  required:
    - userid
    - ageGroup
    - locale
    - created
    - lastLogin
    - presence
    - links
    - parentAccountId
    - displayName
    - onlineServiceType
    - dob
    - email
  properties:
    userid:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "userid"
    ageGroup:
      nullable: true
      type: integer
      description:
        'This is currently, only available for DNA accounts.
        The age group of the specified Platform Account.
        
        List of types for reference:
        
        For United States:
        * 0: Unknown
        * 1: 0 ↔ 12 (Child)
        * 2: 13 ↔ 17 (Teen)
        * 3: 18 ↔ 24 (Adult)
        * 4: 25 ↔ 34 (Adult)
        * 5: 35 ↔ N (Adult)
        * 6: 13 ↔ N (Adult)
        
        For Europe:
        * 7: 0 ↔ 16 (Teen)
        * 8: 16 ↔ 24 (Adult)
        * 9: 24 ↔ N (Adult)'
      example: 3
      x-oapi-codegen-extra-tags:
        dynamodbav: "ageGroup"
    locale:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/locale'
      x-oapi-codegen-extra-tags:
        dynamodbav: "locale"
    created:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/created'
      x-oapi-codegen-extra-tags:
        dynamodbav: "created"
    lastLogin:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/timestamp'
      x-oapi-codegen-extra-tags:
        dynamodbav: "lastLogin"
      description: Placeholder. Not currently used.
    presence:
      nullable: true
      allOf:
        - $ref: '#/presenceResponse'
      x-oapi-codegen-extra-tags:
        dynamodbav: "presence"
    links:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/links'
      x-oapi-codegen-extra-tags:
        dynamodbav: "links"
    parentAccountId:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "parentAccountId"
    displayName:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dnaDisplayName'
      x-oapi-codegen-extra-tags:
        dynamodbav: "displayName"
    onlineServiceType:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
      x-oapi-codegen-extra-tags:
        dynamodbav: "onlineServiceType"
    dob:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dob'
      x-oapi-codegen-extra-tags:
        dynamodbav: "dob"
    email:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/email'
      x-oapi-codegen-extra-tags:
        dynamodbav: "email"
chatMessageGroupResponse:
  type: object
  required:
    - fromUserid
    - roomid
    - message
    - chatGroupType
    - timestamp
    - ttl
    - meta
    - eventData
  properties:
    fromUserid:
      $ref: './fields.yaml#/dnaid'
    toUserid:
      $ref: './fields.yaml#/dnaid'
    roomid:
      $ref: './fields.yaml#/groupid'
    message:
      type: string
    chatGroupType:
      $ref: './fields.yaml#/chatMessageGroupType'
    timestamp:
      $ref: './fields.yaml#/timestamp'
    meta:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/meta'
    eventData:
      nullable: true
      allOf:
        - $ref: '#/chatEventDataResponse'
    ttl:
      $ref: './fields.yaml#/ttl'
chatEventDataResponse:
  type: object
  required:
    - userid
    - eventType
  properties:
    userid:
      $ref: './fields.yaml#/dnaid'
    eventType:
      $ref: './fields.yaml#/chatMessageEventType'
chatMessageDMResponse:
  type: object
  required:
    - fromUserid
    - message
    - timestamp
    - toUserid
    - ttl
    - meta
  properties:
    fromUserid:
      $ref: './fields.yaml#/dnaid'
    toUserid:
      $ref: './fields.yaml#/dnaid'
    message:
      type: string
      description: the chat message
      example: 'Hello World!'
    timestamp:
      $ref: './fields.yaml#/timestamp'
    ttl:
      $ref: './fields.yaml#/ttl'
    meta:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/meta'
groupResponse:
  type: object
  required:
    - groupid
    - maxMembers
    - productid
    - joinRequestAction
    - created
    - membershipRequests
    - password
    - canMembersInvite
    - canCrossPlay
    - onlineServiceType
    - groupCompositionId
    - firstPartySessionid
  description: 'Productid only set as required due to codegen issues.'
  properties:
    created:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/created'
    groupid:
      $ref: './fields.yaml#/groupid'
    productid:
      $ref: './fields.yaml#/productid'
    membershipRequests:
      type: array
      nullable: true
      items:
        allOf:
          - $ref: './schemas.yaml#/membershipRequest'
    meta:
      $ref: './schemas.yaml#/meta'
    members:
      type: array
      items:
        $ref: '#/groupMemberResponse'
    maxMembers:
      $ref: './fields.yaml#/maxMembers'
    joinRequestAction:
      $ref: './fields.yaml#/joinRequestAction'
    password:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/password'
    canMembersInvite:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/canMembersInvite'
    canCrossPlay:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/canCrossPlay'
    onlineServiceType:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
    groupCompositionId:
      type: integer
      format: int64
      description: incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
      nullable: true
    firstPartySessionid:
      $ref: './fields.yaml#/firstPartySessionid'
groupMemberResponse:
  type: object
  required:
    - userid
    - productid
    - role
    - name
    - presence
    - links
    - meta
    - metaLastUpdated
  properties:
    userid:
      $ref: './fields.yaml#/dnaid'
    name:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/name'
    productid:
      $ref: './fields.yaml#/productid'
    role:
      $ref: './fields.yaml#/groupMemberRole'
    presence:
      nullable: true
      allOf:
        - $ref: '#/presenceResponse'
    links:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/links'
    meta:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/meta'
    metaLastUpdated:
      description: Represents the time when an update request is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
      nullable: true
      type: integer
      format: uint64
      example: 1737484653930
groupWithErrorsResponse:
  required:
    - groupid
    - maxMembers
    - productid
    - joinRequestAction
    - created
    - membershipRequests
    - meta
    - members
    - password
    - canMembersInvite
    - canCrossPlay
    - onlineServiceType
    - ttl
    - errors
    - groupCompositionId
  description: 'ProductID only set as required due to codegen issues.'
  type: 'object'
  properties:
    created:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/created'
    groupid:
      $ref: './fields.yaml#/groupid'
    productid:
      $ref: './fields.yaml#/productid'
    membershipRequests:
      type: array
      nullable: true
      items:
        allOf:
        - $ref: './schemas.yaml#/membershipRequest'
    meta:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/meta'
    members:
      type: array
      nullable: true
      items:
        allOf:
          - $ref: '#/groupMemberResponse'
    maxMembers:
      $ref: './fields.yaml#/maxMembers'
    joinRequestAction:
      $ref: './fields.yaml#/joinRequestAction'
    password:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/password'
    canMembersInvite:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/canMembersInvite'
    canCrossPlay:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/canCrossPlay'
    onlineServiceType:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
      description: the group creator's online service type. used when xplay membership requests are validated.
      example: 3
    ttl:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/ttl'
    errors:
      type: array
      nullable: true
      items:
        allOf:
          - $ref: '#/groupMembershipErrorResponse'
    groupCompositionId:
      type: integer
      format: int64
      description: incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
      nullable: true
groupMembershipErrorResponse:
  type: object
  required:
    - memberid
    - error
  properties:
    memberid:
      $ref: './fields.yaml#/userid'
    error:
      $ref: './schemas.yaml#/error'
simplePresenceResponse:
  type: object
  description: very basic presence information for possibly showing some limited presence information in certain lists like search where full presence details would definitely not be shared. game teams can use their discretion on whether they would
    like to display any of this information in their UI in places that it's offered.  currently only user search results and recently played.
  required:
    - status
    - userid
    - gameName
    - onlineServiceType
  properties:
    userid:
      $ref: './fields.yaml#/dnaid'
    status:
      $ref: './fields.yaml#/presenceStatus'
    gameName:
      type: string
      example: Sample Game
      description: Possibly pull this from somewhere for localization in the future. Empty string is possible.
      nullable: true
    onlineServiceType:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
recentlyPlayedUserResponse:
  type: object
  description: a recently played user record
  required:
    - userid
    - productid
    - weight
    - context
    - lastPlayed
    - name
    - links
    - forUserid
    - simplePresence
  properties:
    userid:
      $ref: './fields.yaml#/dnaid'
    productid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/productid'
    weight:
      description: Sorting is done by last played unix time stamp in milliseconds + weight descending.
      nullable: true
      type: integer
      example: 100
    context:
      nullable: true
      description: Used by the game to store any context for the recenly played user.
      type: object
    lastPlayed:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/timestamp'
    name:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/name'
    links:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/links'
    forUserid:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/dnaid'
    simplePresence:
      type: array
      nullable: true
      items:
        allOf:
          - $ref: '#/simplePresenceResponse'
joinContext:
  type: object
  description: Context used to join a game session
  required:
    - sessionid
    - launchGameArgs
  properties:
    sessionid:
      type: string
    launchGameArgs:
      type: string
activeGroupResponse:
  type: object
  required:
    - groupid
    - maxMembers
    - currentMemberCount
    - canRequestJoin
    - canCrossPlay
  properties:
    groupid:
      $ref: './fields.yaml#/groupid'
    canRequestJoin:
      type: boolean
      description: in v2, this boolean is basically just a check on if the group is full.  it used to check group join request action but now any group can be joined using a password even as long as it's not full.
    canCrossPlay:
      $ref: './fields.yaml#/canCrossPlay'
    maxMembers:
      $ref: './fields.yaml#/maxMembers'
    currentMemberCount:
      type: integer
      description: 'count of current members of the group'
discoveryResponse:
  type: object
  required:
    - id
    - description
    - urls
    - canList
  properties:
    id:
      type: string
      example: precert
      description: must be unique.  can be any string identifier. env/guid/etc.
    description:
      type: string
      example: Production Environment
    urls:
      type: array
      items:
        $ref: '#/discoveryURLResponse'
    canList:
      type: boolean
      example: true
      description: if true, this discovery will be returned in the discovery list response
      nullable: true
discoveryURLResponse:
  type: object
  required:
    - type
    - url
    - schema
    - host
    - port
    - path
    - query
    - fragment
  properties:
    type:
      type: string
      enum: ['http', 'mqtt', 'trusted']
      example: http
    url:
      type: string
      description: string url with port included with domain if needed
      example: wss://social-service-staging-additionalname.d2dragon.net/mqtt
    scheme:
      type: string
      description: optional piece of uri
      nullable: true
      example: wss
    host:
      type: string
      description: optional piece of uri
      nullable: true
      example: social-service-staging-additionalname.d2dragon.net
    port:
      type: string
      description: optional piece of uri
      nullable: true
      example: '443'
    path:
      type: string
      description: optional piece of uri
      nullable: true
      example: '/mqtt'
    query:
      type: string
      description: optional piece of uri
      nullable: true
      example: '?isQuery=true'
    fragment:
      type: string
      description: optional piece of uri
      nullable: true
      example: '#'
blocklistResponse:
  type: object
  required:
    - userid
    - blockedid
    - onlineServiceType
    - productid
    - created
    - name
    - links
  properties:
    userid:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: 'userid'
    blockedid:
      allOf:
        - $ref: './fields.yaml#/dnaid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "blockedid"
    name:
      nullable: true
      allOf:
        - $ref: './fields.yaml#/name'
      x-oapi-codegen-extra-tags:
        dynamodbav: "name"
    onlineServiceType:
      allOf:
        - $ref: './fields.yaml#/onlineServiceType'
      x-oapi-codegen-extra-tags:
        dynamodbav: "onlineServiceType"
    productid:
      allOf:
        - $ref: './fields.yaml#/productid'
      x-oapi-codegen-extra-tags:
        dynamodbav: "productid"
    created:
      allOf:
        - $ref: './fields.yaml#/created'
      x-oapi-codegen-extra-tags:
        dynamodbav: "created"
    links:
      nullable: true
      allOf:
        - $ref: './schemas.yaml#/links'
      x-oapi-codegen-extra-tags:
        dynamodbav: "links"
discoveryListResponse:
  type: array
  required:
    - items
  items:
    $ref: '#/discoveryResponse'
endorsementListResponse:
  type: object
  required:
    - items
  properties:
    items:
      type: array
      items:
        $ref: '#/endorsementResponse'
endorsementResponse:
  type: object
  required:
    - endorsementName
    - isPositive
    - currentEndorsementCount
    - isPrivate
    - totalEndorsementCount
  properties:
    endorsementName:
      allOf:
        - $ref: './fields.yaml#/endorsementName'
      x-oapi-codegen-extra-tags:
        dynamodbav: "endorsementName"
    isPositive:
      allOf:
        - $ref: './fields.yaml#/isPositive'
      x-oapi-codegen-extra-tags:
        dynamodbav: "isPositive"
    isPrivate:
      allOf:
        - $ref: './fields.yaml#/isPrivate'
      x-oapi-codegen-extra-tags:
        dynamodbav: "isPrivate"
    currentEndorsementCount:
      allOf:
        - $ref: './fields.yaml#/currentEndorsementCount'
      x-oapi-codegen-extra-tags:
        dynamodbav: "currentEndorsementCount"
    totalEndorsementCount:
      allOf:
        - $ref: './fields.yaml#/totalEndorsementCount'
      x-oapi-codegen-extra-tags:
        dynamodbav: "totalEndorsementCount"
