services:
  dynamodb-local:
    image: amazon/dynamodb-local:latest
    container_name: dynamodb-local
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb"]
    environment:
      AWS_REGION: 'us-east-1'
      AWS_ACCESS_KEY_ID: 'local'
      AWS_SECRET_ACCESS_KEY: 'local'
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - "6666:8000"
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - "8002:8001"
    environment:
      DYNAMO_ENDPOINT: "*************:8000"
      AWS_REGION: 'us-east-1'
      AWS_ACCESS_KEY_ID: 'local'
      AWS_SECRET_ACCESS_KEY: 'local'
    depends_on:
      - dynamodb-local
  redis0:
    image: 'redislabs/redismod'

    volumes:
      - './docker/redis0/redis.conf:/usr/local/etc/redis.conf'
    entrypoint:
      - 'redis-server'
      - '/usr/local/etc/redis.conf'
    # command:
    #   - 'redis-server'
    #   - '/usr/local/etc/redis.conf'
    hostname: redis0
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - 6379:6379

  redis1:
    image: 'redislabs/redismod'

    volumes:
      - './docker/redis1/redis.conf:/usr/local/etc/redis.conf'
    entrypoint:
      - 'redis-server'
      - '/usr/local/etc/redis.conf'
    # command:
    #   - 'redis-server'
    #   - '/usr/local/etc/redis.conf'
    hostname: redis1
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - 6380:6380
  redis2:
    image: 'redislabs/redismod'

    volumes:
      - './docker/redis2/redis.conf:/usr/local/etc/redis.conf'
    entrypoint:
      - 'redis-server'
      - '/usr/local/etc/redis.conf'
    # command:
    #   - 'redis-server'
    #   - '/usr/local/etc/redis.conf'
    depends_on:
      - redis0
      - redis1
    hostname: redis2
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - 6381:6381
  t2gp-social-service:
    volumes:
      - './:/workdir'
    build:
      context: .
      dockerfile: .devcontainer/Dockerfile
    depends_on:
      - redis0
      - redis1
      - redis2
      - dynamodb-local
    entrypoint:
      - 'sleep'
      - 'infinity'
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - 8001:8001
  vmq0:
    env_file:
      - ./deployments/vmq-plugin-social/.env
    hostname: vmq0.take2games.com
    domainname: take2games.com
    # image: 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-vernemq:latest
    image: vernemq/vernemq:1.13.0
    depends_on:
      - dynamodb-local
      - redis0
      - redis1
      - redis2
    environment:
      DOCKER_VERNEMQ_ACCEPT_EULA: 'yes'
      DOCKER_VERNEMQ_ALLOW_ANONYMOUS: 'off'
      DOCKER_VERNEMQ_NODENAME: vmq0.take2games.com
      DOCKER_VERNEMQ_ERLANG__DISTRIBUTION__PORT_RANGE__MAXIMUM: '9109'
      DOCKER_VERNEMQ_ERLANG__DISTRIBUTION__PORT_RANGE__MINIMUM: '9100'
      DOCKER_VERNEMQ_LISTENER__HTTP__DEFAULT: '0.0.0.0:8100'
      DOCKER_VERNEMQ_LISTENER__HTTP__METRICS: '0.0.0.0:8888'
      DOCKER_VERNEMQ_LISTENER__TCP__ALLOWED_PROTOCOL_VERSIONS: '3,4,5'
      # DOCKER_VERNEMQ_LISTENER__TCP__DEFAULT: '0.0.0.0:1883'
      DOCKER_VERNEMQ_LISTENER__WS__ALLOWED_PROTOCOL_VERSIONS: '3,4,5'
      DOCKER_VERNEMQ_LISTENER__WS__DEFAULT: '0.0.0.0:8080'
      DOCKER_VERNEMQ_PLUGINS__VMQ_ACL: 'off'
      DOCKER_VERNEMQ_PLUGINS__VMQ_PASSWD: 'off'
      DOCKER_VERNEMQ_PLUGINS__VMQ_WEBHOOKS: 'off'
      DOCKER_VERNEMQ_PLUGINS__T2GP_SOCIAL: 'on'
      DOCKER_VERNEMQ_PLUGINS__T2GP_SOCIAL__PATH: '/devel/_build/default'
      DOCKER_VERNEMQ_MAX_INFLIGHT_MESSAGES: '0'
      DOCKER_VERNEMQ_PLUGINS__VMQ_HTTP_PUB: 'on'
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB: '0.0.0.0:8110'
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB__HTTP_MODULE__VMQ_HTTP_PUB__AUTH__MODE: 'apikey'
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB__HTTP_MODULES: 'vmq_http_pub'
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB__HTTP_MODULES__VMQ_HTTP_PUB__MQTT_AUTH__MODE: 'on-behalf-of'
      # Temporarily turn on debug logging
      DOCKER_VERNEMQ_LOG__CONSOLE__LEVEL: 'info'
      # DD_AGENT_HOST: 'dd-agent'
      DD_ENV: local
      # SET these in test.yml
#      AWS_ACCESS_KEY_ID: '${AWS_ACCESS_KEY_ID}'
#      AWS_SECRET_ACCESS_KEY: '${AWS_SECRET_ACCESS_KEY}'
#      SOCIAL_APP_ID: '${SOCIAL_APP_ID}'
#      SOCIAL_APP_SECRET: '${SOCIAL_APP_SECRET}'
#      SOCIAL_APP_BASIC_AUTH: '${SOCIAL_APP_BASIC_AUTH}'
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - '8080:8080' # Websocket port
      - '8888:8888' # Metrics port
      - '8100:8100' # API port
    volumes:
      - ./deployments/vmq-plugin-social:/devel
      - ./deployments/vmq-plugin-social/priv/t2gp_social.schema:/vernemq/share/schema/99-t2gp_social.schema # this is needed to add custom config values
  vmq1:
    env_file:
      - ./deployments/vmq-plugin-social/.env
    # image: 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-vernemq:latest
    image: vernemq/vernemq:1.13.0
    hostname: vmq1.take2games.com
    domainname: take2games.com
    depends_on:
      - vmq0
    environment:
      DOCKER_VERNEMQ_ACCEPT_EULA: 'yes'
      DOCKER_VERNEMQ_ALLOW_ANONYMOUS: 'off'
      DOCKER_VERNEMQ_NODENAME: vmq1.take2games.com
      DOCKER_VERNEMQ_DISCOVERY_NODE: vmq0.take2games.com
      DOCKER_VERNEMQ_ERLANG__DISTRIBUTION__PORT_RANGE__MAXIMUM: '9109'
      DOCKER_VERNEMQ_ERLANG__DISTRIBUTION__PORT_RANGE__MINIMUM: '9100'
      DOCKER_VERNEMQ_LISTENER__HTTP__DEFAULT: '0.0.0.0:8100'
      DOCKER_VERNEMQ_LISTENER__HTTP__METRICS: '0.0.0.0:8888'
      DOCKER_VERNEMQ_LISTENER__TCP__ALLOWED_PROTOCOL_VERSIONS: '3,4,5'
      # DOCKER_VERNEMQ_LISTENER__TCP__DEFAULT: '0.0.0.0:1883'
      DOCKER_VERNEMQ_LISTENER__WS__ALLOWED_PROTOCOL_VERSIONS: '3,4,5'
      DOCKER_VERNEMQ_LISTENER__WS__DEFAULT: '0.0.0.0:8080'
      DOCKER_VERNEMQ_PLUGINS__VMQ_ACL: 'off'
      DOCKER_VERNEMQ_PLUGINS__VMQ_PASSWD: 'off'
      DOCKER_VERNEMQ_PLUGINS__VMQ_WEBHOOKS: 'off'
      DOCKER_VERNEMQ_PLUGINS__T2GP_SOCIAL: 'on'
      DOCKER_VERNEMQ_PLUGINS__T2GP_SOCIAL__PATH: '/devel/_build/default'
      DOCKER_VERNEMQ_MAX_INFLIGHT_MESSAGES: '0'
      DOCKER_VERNEMQ_PLUGINS__VMQ_HTTP_PUB: "on"
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB: "0.0.0.0:8110"
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB__HTTP_MODULE__VMQ_HTTP_PUB__AUTH__MODE: "apikey"
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB__HTTP_MODULES: "vmq_http_pub"
      DOCKER_VERNEMQ_LISTENER__HTTP__HTTP_PUB__HTTP_MODULES__VMQ_HTTP_PUB__MQTT_AUTH__MODE: "on-behalf-of"
      # Temporarily turn on debug logging
      DOCKER_VERNEMQ_LOG__CONSOLE__LEVEL: 'info'
      # DD_AGENT_HOST: 'dd-agent'
      DD_ENV: local
      # SET these in test.yml
#      AWS_ACCESS_KEY_ID: '${AWS_ACCESS_KEY_ID}'
#      AWS_SECRET_ACCESS_KEY: '${AWS_SECRET_ACCESS_KEY}'
#      SOCIAL_APP_ID: '${SOCIAL_APP_ID}'
#      SOCIAL_APP_SECRET: '${SOCIAL_APP_SECRET}'
#      SOCIAL_APP_BASIC_AUTH: '${SOCIAL_APP_BASIC_AUTH}'
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - '8082:8080' # Websocket port
      - '8101:8100' # API port
    volumes:
      - ./deployments/vmq-plugin-social:/devel
      - ./deployments/vmq-plugin-social/priv/t2gp_social.schema:/vernemq/share/schema/99-t2gp_social.schema # this is needed to add custom config values
networks:
  devcontainer:
    ipam:
      driver: default
      config:
        - subnet: '*************/24'
          gateway: '*************'
