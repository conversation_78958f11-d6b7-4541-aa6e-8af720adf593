package api

import (
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
	"net/http"
)

// GetDiscovery get the discovery urls
func (api *SocialPublicAPI) GetDiscovery(w http.ResponseWriter, r *http.Request, params apipub.GetDiscoveryParams) {
	api.GetDiscoveryHelper(w, r, params, false)
}

// GetDiscoveryHelper get the discovery urls
func (api *SocialPublicAPI) GetDiscoveryHelper(w http.ResponseWriter, r *http.Request, params apipub.GetDiscoveryParams, bIsTrustedServer bool) {

	httpDefault := apipub.DiscoveryURLResponse{
		Type: "http",
		Url:  api.Cfg.HttpURL,
	}
	mqttDefault := apipub.DiscoveryURLResponse{
		Type: "mqtt",
		Url:  api.Cfg.MqttURL,
	}

	defaultDiscovery := apipub.DiscoveryResponse{
		Id:          "serverDefault",
		Description: "serverDefault",
		Urls:        []apipub.DiscoveryURLResponse{httpDefault, mqttDefault},
	}

	defaultResponse := []apipub.DiscoveryResponse{
		defaultDiscovery,
	}

	// we send the default if no auth header
	if params.DiscoveryPid == nil || (params.DiscoveryPid != nil && params.Discoveryid == nil) {
		ReturnOK(w, r, defaultResponse)
		return
	}

	//validate Productid
	err := validation.Validate.VarCtx(r.Context(), params.DiscoveryPid, validation.KValidateUserID)
	if err != nil || len(*params.DiscoveryPid) != 32 {
		log.Error().Str("productid", *params.DiscoveryPid).Str("event", "valid productid required for discovery").Msg("valid productid required for discovery")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EDiscoveryNotFound))
		return
	}

	productid := params.DiscoveryPid

	// try to fetch from S3
	discoveryItems, err := api.Ds.GetDiscovery(r.Context(), *productid)
	if err != nil || (discoveryItems == nil || len(*discoveryItems) <= 0) {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EDiscoveryNotFound))
		return
	}

	var ret []apipub.DiscoveryResponse
	for _, item := range *discoveryItems {
		if ((params.Discoveryid == nil || *params.Discoveryid == "") && item.CanList != nil && *item.CanList) || (params.Discoveryid != nil && item.Id == *params.Discoveryid) ||
			(bIsTrustedServer && (params.Discoveryid == nil || *params.Discoveryid == "")) {
			ret = append(ret, item)
		}

	}
	if len(ret) <= 0 {
		log.Info().Str("productid", *params.DiscoveryPid).Str("event", "no discovery found for product").Msg("no discovery found for product")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EDiscoveryNotFound))
		return
	}
	ReturnOK(w, r, ret)
}
