import type { IAPIService } from '../api';

export const apiServiceMock: IAPIService = {
  setAccessToken: () => {},
  LoginAsync: () => Promise.resolve(new Response()),
  getFriendsAsync: () => Promise.resolve(new Response()),
  getPendingFriendsAsync: () => Promise.resolve(new Response()),
  getUserAsync: () => Promise.resolve(new Response()),
  getSteamFriendsAsync: () => Promise.resolve(new Response()),
  postMakeFriendAsync: () => Promise.resolve(new Response()),
  deleteFriendAsync: () => Promise.resolve(new Response()),
  searchFriendsAsync: () => Promise.resolve(new Response()),
  patchPendingFriendAsync: () => Promise.resolve(new Response()),
};
