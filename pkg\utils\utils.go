// Package utils random utilities
package utils

import (
	"context"
	"html"
	"net/http"
	"os"
	"reflect"
	"runtime"
	"strings"
	"time"

	"golang.org/x/crypto/bcrypt"

	"github.com/segmentio/encoding/json"

	"math/rand"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/oklog/ulid/v2"
	"github.com/rs/zerolog/log"

	"github.com/take-two-t2gp/t2gp-social-service/constants"
)

type HTTPClientInterface interface {
	Do(req *http.Request) (*http.Response, error)
}

var seed = time.Now().UnixNano()
var source = rand.NewSource(seed)
var entropy = rand.New(source)

const lastValidUTF8Rune = '\U0010FFFF'

// ArrayContainsString check if string in array
func ArrayContainsString(arr []string, str string) bool {
	for _, a := range arr {
		if a == str {
			return true
		}
	}
	return false
}

// ArrayStrContainsString check if string in array
func ArrayStrContainsString(arr []string, str string) bool {
	for _, a := range arr {
		if a == str {
			return true
		}
	}
	return false
}

// StringContainsSubstr contains substring
func StringContainsSubstr(path string, subPaths ...string) bool {
	for _, subPath := range subPaths {
		if i := strings.Index(path, subPath); i >= 0 {
			return true
		}
	}
	return false
}

// GenerateNewULID create a new ULID
func GenerateNewULID() string {
	return GenerateULID(time.Now())
}

// GenerateULID based the time pass in
func GenerateULID(t time.Time) string {
	id, err := ulid.New(ulid.Timestamp(t), entropy)
	if err != nil {
		log.Error().Err(err).Msgf("failed create new ulid time=%v", t)
		return ""
	}
	return id.String()
}

var rnd = rand.New(rand.NewSource(time.Now().UnixNano()))

const letterRunes = "0123456789abcdef"

func GenerateRandomDNAID() string {
	b := make([]byte, 32)
	for i := range b {
		b[i] = letterRunes[rnd.Intn(len(letterRunes))]
	}
	return string(b)
}

// UniqueStringArray make string array unique
func UniqueStringArray(arr []string) []string {
	occurred := map[string]bool{}
	var result []string
	for e := range arr {

		// check if already the mapped
		// variable is set to true or not
		if !occurred[arr[e]] {
			occurred[arr[e]] = true

			// Append to result slice.
			result = append(result, arr[e])
		}
	}

	return result
}

func GetEnvironment() string {
	env, found := os.LookupEnv("DD_ENV")
	if found {
		return env
	}
	return "develop"
}

func IsProduction() bool {
	return GetEnvironment() == "production"
}

func IsStaging() bool {
	return GetEnvironment() == "staging"
}

func IsCert() bool {
	return GetEnvironment() == "cert"
}

func IsDevelop() bool {
	return GetEnvironment() == "develop"
}

func IsLocal() bool {
	return GetEnvironment() == "local"
}

func IsLoadtest() bool {
	return GetEnvironment() == "loadtesting"
}

func IsReleaseCantidate() bool {
	return strings.Contains(GetEnvironment(), "release")
}

func IsDevOrLocal() bool {
	return IsDevelop() || IsLocal()
}

func IsNonProdCluster() bool {
	return IsDevOrLocal() || IsReleaseCantidate() || IsLoadtest() || IsPullRequest()
}

func IsProdCluster() bool {
	return IsStaging() || IsProduction() || IsCert()
}

func IsPullRequest() bool {
	return StringContainsSubstr(os.Getenv("DD_ENV"), "pr-")
}

func IsTrustedServer() bool {

	ddSvc, _ := os.LookupEnv("DD_SERVICE")
	return ddSvc == constants.DDServiceTrusted
}

func GetStack() *string {
	var stack *string = nil
	if IsNonProdCluster() {
		stack = ForceGetStack()
	}
	return stack
}

func ForceGetStack() *string {
	stackSlice := make([]byte, 8196)
	s := runtime.Stack(stackSlice, false)
	return aws.String(string(stackSlice[0:s]))
}

func WriteJsonResponse(w http.ResponseWriter, r *http.Request, responseCode int, responseBody interface{}) {
	// add request id to response header
	reqID := middleware.GetReqID(r.Context())
	if reqID != "" {
		w.Header().Add("X-Request-ID", reqID)
	}
	w.Header().Add(constants.KContentType, constants.KApplicationJson)
	w.WriteHeader(responseCode)
	json.NewEncoder(w).Encode(responseBody)
}

func EncodeJson(data interface{}) string {
	result, err := json.Marshal(data)
	if err != nil {
		log.Error().Err(err).Msgf("faild to serialize %v", data)
		return ""
	}
	return string(result)
}

func EscapeString(str string) string {
	// json escape the string
	escapedStr := string(json.Escape(str))
	// remove "..." from beginning and end
	escapedStrTrimmed := escapedStr[1 : len(escapedStr)-1]
	return escapedStrTrimmed
}

// EscapeXSS escapes html special characters to prevent cross site scripting
func EscapeXSS(s interface{}) {
	value := reflect.ValueOf(s)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	var sptr *string
	var mapptr *map[string]interface{}
	// loop over the struct
	for i := 0; i < value.NumField(); i++ {
		field := value.Field(i)

		// check if the field is a string
		if field.Type() == reflect.TypeOf("") {
			str := field.Interface().(string)
			// set field to escaped version of the string
			field.SetString(html.EscapeString(str))
		}

		// check if the field is a string ptr
		if field.Type() == reflect.TypeOf(sptr) {
			str := field.Interface().(*string)
			if str != nil {
				cleanStr := html.EscapeString(*str)
				field.Set(reflect.ValueOf(&cleanStr))
			}
		}

		// check for map interfaces (specifically in apipub.UpdateGroupRequest)
		if field.Type() == reflect.TypeOf(mapptr) {
			mapField := field.Interface().(*map[string]interface{})
			if mapField != nil {
				for key, val := range *mapField {
					if s, ok := val.(string); ok {
						(*mapField)[key] = html.EscapeString(s)
					}
					if sp, ok := val.(*string); ok {
						(*mapField)[key] = aws.String(html.EscapeString(*sp))
					}
				}
			}
		}
	}
}

var formatISO8601 = "2006-01-02T15:04:05.999Z"

func NowISOString() string {
	return time.Now().Format(formatISO8601)
}

// SortKeyToGroupID parses group#productid#groupid
func SortKeyToGroupID(skIn string) string {
	sk := strings.Split(skIn, "#")

	if sk[0] == "group" {
		// support old groups style format and new scoped groups
		if len(sk) == 2 {
			return sk[1]
		} else {
			return sk[2]
		}
	}
	return ""
}

func IgnoreRequest(r *http.Request) bool {
	uri := r.URL.Path
	ua := r.UserAgent()

	// don't process /health or /favicon.ico
	if StringContainsSubstr(uri, "/health", "/favicon.ico") ||
		StringContainsSubstr(ua, "ELB-HealthChecker", "kube-probe") {
		return true
	}
	return false
}
func HashPassword(pw *string) *string {
	return HashPasswordCost(pw, bcrypt.MinCost)
}
func HashPasswordCost(pw *string, cost int) *string {
	if pw == nil {
		return nil
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(*pw), cost)
	if err != nil {
		log.Error().Err(err).Msgf("failed to hash password %s", *pw)
		return aws.String("invalid-pw-hash")
	}
	return aws.String(string(hashedPassword))
}

func CheckPassword(hashedPw string, pw string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPw), []byte(pw))
	return err == nil
}

func RemoveCurlyBrackets(bracketedString string) string {
	ret, _ := strings.CutPrefix(bracketedString, "{")
	ret, _ = strings.CutSuffix(ret, "}")
	return ret
}

func GetMaxUTF8() string {
	return string(lastValidUTF8Rune)
}

func ConvertProductIdToUuid(productid string) string {
	uuid := ""
	if len(productid) != 32 {
		return productid
	}

	//8-4-4-4-12
	for i, r := range productid {
		uuid += string(r)
		if i == 7 || i == 11 || i == 15 || i == 19 {
			uuid += "-"
		}
	}
	return uuid
}

func ConvertUuidToProductId(uuid string) string {
	return strings.ReplaceAll(uuid, "-", "")
}

// TypeConverter convert an object into the same object of another type ie: trustedserver.xyz to apipub.xyx
func TypeConverter[R any](data any) (*R, error) {
	var result R
	b, err := json.Marshal(&data)
	if err != nil {
		return nil, err
	}
	err = json.Unmarshal(b, &result)
	if err != nil {
		return nil, err
	}
	return &result, err
}

func GenerateRandomPass(length int) string {
	if length < 8 {
		length = 8
	}

	lower := "abcdefghijklmnopqrstuvwxyz"
	upper := "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	numbers := "1234567890"
	symbols := "~!@#$%^&*()_+-={}[]:;<>?,."
	all := lower + upper + numbers + symbols

	b := make([]byte, length)

	//set at least one of each type
	b[randomIndex(b)] = randomCharacter(lower)
	b[randomIndex(b)] = randomCharacter(upper)
	b[randomIndex(b)] = randomCharacter(numbers)
	b[randomIndex(b)] = randomCharacter(symbols)

	//set all 0 entries to a random character
	for i := 0; i < length; i++ {
		if b[i] == 0 {
			b[i] = randomCharacter(all)
		}
	}
	return string(b)
}

func randomCharacter(chars string) byte {
	return chars[rnd.Intn(len(chars))]
}

func randomIndex(b []byte) int {
	temp := rnd.Intn(len(b))
	for b[temp] != 0 {
		temp++
		if temp >= len(b) {
			temp = 0
		}
	}
	return temp
}

func GetValueFromContext(ctx context.Context, str string) string {
	value := ctx.Value(str)
	if value != nil {
		return value.(string)
	}
	return ""
}
