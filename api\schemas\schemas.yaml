accountLinkDNA:
  type: object
  description: First party DNA account links.  This will be filtered by the current user's OST.
  properties:
    linkType:
      type: string
      enum: [xbl, steam, psn, epic, nintendo, parent]
    accountId:
      $ref: "./fields.yaml#/dnaid"
    accountType:
      $ref: "./fields.yaml#/accountTypeDNA"
    onlineServiceType:
      $ref: "./fields.yaml#/onlineServiceType"
    firstPartyid:
      $ref: "./fields.yaml#/firstPartyid"
error:
  type: object
  required:
    - code
    - errorCode
    - message
  properties:
    code:
      description: HTTP error code
      type: integer
      format: uint32
      example: 500
    errorCode:
      $ref: "./fields.yaml#/errorCode"
    message:
      type: string
      description: error message
      example: Exception occured
    stack:
      type: string
      description: Stack trace of the error (will be only returned in dev environment)
telemetryMetaData:
  type: object
  description:
    Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. |
    NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
  nullable: true
  example:
    some_boolean_field: "true"
    some_integer_field: "0"
    some_string_field: "asdf1234"
membershipRequest:
  type: object
  description: this schema defines a membership request.  which can be either a join request or an invite using the status field as a determiner.
  required:
    - groupid
    - memberid
    - approverid
    - status
  properties:
    ttl:
      allOf:
        - $ref: "./fields.yaml#/ttl"
      default: 3600
      minimum: 1
      maximum: 2628288
    memberid:
      $ref: "./fields.yaml#/dnaid"
    approverid:
      $ref: "./fields.yaml#/dnaid"
    groupid:
      $ref: "./fields.yaml#/groupid"
    productid:
      $ref: "./fields.yaml#/productid"
    onlineServiceType:
      $ref: "./fields.yaml#/onlineServiceType"
    status:
      $ref: "./fields.yaml#/membershipStatus"
    canCrossPlay:
      $ref: "./fields.yaml#/canCrossPlay"
    firstPartyid:
      $ref: "./fields.yaml#/firstPartyid"
    isFirstPartyInvite:
      $ref: "./fields.yaml#/isFirstPartyInvite"
    fromDisplayName:
      allOf:
        - $ref: "./fields.yaml#/dnaDisplayName"
      description: the display name of the user that this request is from.  provided to display in the UI of the invite.
    teleMeta:
      $ref: "#/telemetryMetaData"
meta:
  type: object
  description: free form map (json format) to store metadata for this object.
  nullable: true
  example:
    key1: value1
    key2: value2
joinGroupMember:
  type: object
  required:
    - memberid
    - canCrossPlay
    - onlineServiceType
  properties:
    memberid:
      $ref: "./fields.yaml#/dnaid"
    canCrossPlay:
      $ref: "./fields.yaml#/canCrossPlay"
    onlineServiceType:
      $ref: "./fields.yaml#/onlineServiceType"
inviteGroupMember:
  type: object
  required:
    - memberid
  properties:
    memberid:
      $ref: "./fields.yaml#/userid"
    isFirstPartyInvite:
      $ref: "./fields.yaml#/isFirstPartyInvite"
links:
  description: Linked accounts. Filtered to current OST.
  type: array
  items:
    $ref: "#/accountLinkDNA"
joinContext:
  type: object
  description: Context used to join a game session
  required:
    - sessionid
    - launchGameArgs
  properties:
    sessionid:
      type: string
    launchGameArgs:
      type: string
messageBodySegement:
  type: object
  description: Message body segement
  required:
    - type
    - text
  properties:
    type:
      type: string
      enum: [text, hyperLink, deepLink]
    text:
      description: The text to be displayed in the message.
      type: string
    objectId:
      description: The id of the object being linked to. It's required for deepLink type.
      type: string
    objectType:
      description: The type of the object being linked to. It's required for deepLink type.
      type: string
    objectDescription:
      description: The description of the object being linked to. It's required for deepLink type.
      type: string
chatMessage:
  type: object
  description: Chat message objet
  required:
    - subjectId
    - targetId
    - postedTime
    - body
    - productId
    - type
  properties:
    subjectId:
      description: The user id of the user sending the message
      type: string
    targetId:
      description: The id of message receipent. For 1on1 this is the userid. For group this is the groupid.
      type: string
    postedTime:
      $ref: "./fields.yaml#/timestamp"
    body:
      description: The body of the message supports rich text. The sender's client should tokenize the raw text into a messageBodySegment array. The recipient's client can then render a rich text message from the messageBodySegment array.
      type: array
      items:
        $ref: "#/messageBodySegement"
    productId:
      $ref: "./fields.yaml#/productid"
    type:
      description: The type of chat message. 1on1 is a direct message between two users. Group is a message sent to a group.
      type: string
      enum: [group, 1on1]
    shardId:
      description: The shard id of a chat room.
      type: string
