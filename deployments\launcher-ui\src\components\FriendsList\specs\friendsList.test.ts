import { render, waitFor } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  MOCKED_FRIENDS,
  transportServiceMock,
} from '../../../services/__mocks__';
import { AccordionMock, AccordionSectionMock } from '../../Accordion/__mock__';
import FriendsListActionBarMock from '../../FriendsListActionBar/__mock__/FriendsListActionBar.svelte';
import LoadingSpinnerMock from '../../LoadingSpinner/__mock__/LoadingSpinner.svelte';
import FriendsListTestWrapper from './FriendsListTestWrapper.svelte';

jest.mock('../../LoadingSpinner', () => ({
  LoadingSpinner: LoadingSpinnerMock,
}));

jest.mock('../../FriendsListActionBar', () => ({
  FriendsListActionBar: FriendsListActionBarMock,
}));

jest.mock('../../../assets/icons', () => ({
  SVGRefresh: SVGIconMock,
  SVGEmptyFace: SVGIconMock,
  SVGErrorFace: SVGIconMock,
}));

jest.mock('../../Accordion', () => ({
  Accordion: AccordionMock,
  AccordionSection: AccordionSectionMock,
}));

const fetchFriendsAsyncMock = jest.fn();
friendsServiceMock.getFriendsAsync = fetchFriendsAsyncMock;
describe('FriendsList', () => {
  it('should render UI friends list', async () => {
    fetchFriendsAsyncMock.mockResolvedValue(MOCKED_FRIENDS);
    const socialServicesMock = new SocialServices({
      transportService: transportServiceMock,
      friendsService: friendsServiceMock,
    });
    const { getByText, getByTestId } = render(FriendsListTestWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    expect(getByTestId('action-bar-mock')).not.toBeNull();

    await waitFor(() => {
      expect(getByText(MOCKED_FRIENDS[0].name)).not.toBeNull();
    });
  });

  it('should render UI with empty state', async () => {
    fetchFriendsAsyncMock.mockResolvedValue([]);
    const socialServicesMock = new SocialServices({
      transportService: transportServiceMock,
      friendsService: friendsServiceMock,
    });
    const { getByText } = render(FriendsListTestWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    await waitFor(() => {
      expect(getByText('Add Friends')).not.toBeNull();
    });
  });

  it('should render UI with error state', async () => {
    fetchFriendsAsyncMock.mockRejectedValue('');
    const socialServicesMock = new SocialServices({
      queryOptions: {
        queries: {
          retry: false,
        },
      },
      transportService: transportServiceMock,
      friendsService: friendsServiceMock,
    });
    const { getByText, getByTestId } = render(FriendsListTestWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    expect(getByTestId('action-bar-mock')).not.toBeNull();

    await waitFor(() => {
      expect(getByTestId('loading-spinner-mock')).not.toBeNull();
    });

    await waitFor(() => {
      expect(getByText('Refresh')).not.toBeNull();
    });
  });
});
