package cache

import (
	"testing"
)

func Test_Presence(t *testing.T) {
	// TODO: [SPOP] - revert the commenting out
	// g := goblin.Goblin(t)

	// userId := "e12c3df480984141b2f385646b2024fa"
	// productId := "ProductId"
	// presence1 := apipub.Presence {
	// 	ActiveSessionid: "1",
	// 	Userid: userId,
	// 	Productid: productId,
	// }

	// presence2 := apipub.Presence {
	// 	ActiveSessionid: "2",
	// 	Userid: userId,
	// 	Productid: productId,
	// }

	// now := time.Now().UTC().Unix()
	// jtw1 := jwt.Claims{
	// 	Jti:         "3ab01e86f4824d4e95979b03775d73e6",
	// 	ExpiresTime: now + 3600,
	// 	CreatedTime: now,
	// 	SessionID: "1",
	// 	ProductID: productId,
	// 	Subject: userId,
	// }

	// jtw2 := jwt.Claims{
	// 	Jti:         "3ab01e86f4824d4e95979b03775d73e6",
	// 	ExpiresTime: now + 3600,
	// 	CreatedTime: now,
	// 	SessionID: "2",
	// 	ProductID: productId,
	// 	Subject: userId,
	// }

	// ttl := time.Duration(time.Second * 3600)

	// g.Describe("test set presence", func() {
	// 	g.AfterEach(func() {
	// 		rdb.DeletePresence(ctx, &presence1)
	// 		rdb.DeletePresence(ctx, &presence2)
	// 	})
	// 	g.It("set two presences", func() {
	// 		err := rdb.SetPresence(ctx, &presence1, &jtw1, ttl)
	// 		g.Assert(err).IsNil()

	// 		err = rdb.SetPresence(ctx, &presence2, &jtw2, ttl)
	// 		g.Assert(err).IsNil()

	// 		presence1, err := rdb.GetPresence(ctx, userId, "ProductId", "1")
	// 		g.Assert(err).IsNil()
	// 		g.Assert(presence1.ActiveSessionid).Equal("1")

	// 		presence2, err := rdb.GetPresence(ctx, userId, "ProductId", "2")
	// 		g.Assert(err).IsNil()
	// 		g.Assert(presence2.ActiveSessionid).Equal("2")
	// 	})
	// 	g.It("get two presences", func() {
	// 		_ = rdb.SetPresence(ctx, &presence1, &jtw1, ttl)
	// 		_ = rdb.SetPresence(ctx, &presence2, &jtw2, ttl)

	// 		presences, err := rdb.GetUserPresences(ctx, userId, productId)
	// 		g.Assert(err).IsNil()
	// 		g.Assert(len(*presences)).Equal(2)

	// 		p1 := *(*presences)[0]
	// 		p2 := *(*presences)[1]
	// 		g.Assert(p1.ActiveSessionid).Equal("1")
	// 		g.Assert(p2.ActiveSessionid).Equal("2")
	// 	})
	// })
}
