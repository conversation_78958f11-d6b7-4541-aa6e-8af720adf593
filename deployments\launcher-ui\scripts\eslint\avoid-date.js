module.exports = {
  meta: {
    docs: {
      description: 'disallow use of Date to ensure time-travel is respected',
      category: 'Possible Errors',
    },
    schema: [],
  },
  create(context) {
    return {
      CallExpression: function (node) {
        let callee = node.callee;
        if (
          callee.type !== 'MemberExpression' ||
          callee.object.type !== 'Identifier' ||
          callee.object.name !== 'Date' ||
          callee.property.type !== 'Identifier' ||
          callee.property.name !== 'now'
        ) {
          return;
        }

        context.report({
          node,
          message:
            'use getMSecondsSinceEpoch() from /utils/time.js functions instead of Date.now() to ensure time-travel is respected',
        });
      },
      NewExpression: function (node) {
        let callee = node.callee;
        if (
          callee.type !== 'Identifier' ||
          callee.name !== 'Date' ||
          node.arguments.length > 0
        ) {
          return;
        }

        context.report({
          node,
          message:
            'use getMSecondsSinceEpoch() from /utils/time.js functions instead of new Date() to ensure time-travel is respected',
        });
      },
    };
  },
};
