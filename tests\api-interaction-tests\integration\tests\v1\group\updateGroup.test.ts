import request from 'supertest';
import { TwokAccounts } from '../../../../integration/lib/config';
import * as socialApi from '../../../lib/social-api';
import { StatusCodes } from 'http-status-codes';

describe('[public v1]', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;
  const groupInitSize: number = 3;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(1, ["leader"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: groupInitSize,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
  });

  it('Increasing maxMembers is allowed[happy trusted]', async () => {
    const newMaxMembers: number = groupInitSize + 1;

    let testCase = {
      description: "update the value of group maxMembers with increased value",
      expected: "the value of group maxMembers is increased"
    };

    // update the value of maxMembers with increased value
    const r = await socialApi.updateGroup(usersTwok.acct["leader"], groupId, { maxMembers: newMaxMembers });
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        groupid: groupId,
        maxMembers: newMaxMembers,
      },
    };
    //expect maxMembers is increased to be newMaxMembers
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the value of maxMembers is not increased to be the expected value"
        }
      }
    );
  });

  it('Decreasing maxMembers is allowed[happy trusted]', async () => {
    const newMaxMembers: number = groupInitSize - 1;

    let testCase = {
      description: "update the value of group maxMembers with decreased value",
      expected: "the value of group maxMembers is decreased"
    };

    // update the value of maxMembers with decreased value
    const r = await socialApi.updateGroup(usersTwok.acct["leader"], groupId, { maxMembers: newMaxMembers });
    socialApi.testStatus(StatusCodes.OK, r);

    const respGetGroup: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );
    // expect maxMembers updated to be the decreased value
    socialApi.expectMore(
      () => {expect(respGetGroup.body.maxMembers).toEqual(newMaxMembers)},
      testCase,
      {
        resp: respGetGroup,
        additionalInfo: {
          "fail reason": "the value of maxMembers is not decreased to be the expected value"
        }
      }
    );
  });

  it('Negative maxMembers is not allowed[trusted]', async () => {
    const negMaxMembers: number = -1;

    let testCase = {
      description: "update the value of maxMembers to be negative value",
      expected: "the value of maxMembers remains at its original value"
    };

    // update the value of maxMembers with negative value
    const r = await socialApi.updateGroup(usersTwok.acct["leader"], groupId, { maxMembers: negMaxMembers });
    socialApi.testStatus(StatusCodes.BAD_REQUEST, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // expect maxMembers remains at its original value
    socialApi.expectMore(
      () => {expect(actualGroupInfo.body.maxMembers).toEqual(groupInitSize)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the value of maxMembers is not the expected original value"
        }
      }
    );
  });
});

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;
  const groupInitSize: number = 3;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: groupInitSize,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
  });

  it('Update group by non-member[public v1]', async () => {
    const newMaxMembers: number = groupInitSize + 1;

    let testCase = {
      description: "non-member updates the value of maxMembers",
      expected: "the value of maxMembers remains at its original value"
    };

    // update the value of maxMembers by the non-member
    const r = await socialApi.updateGroup(usersTwok.acct["member1"], groupId, { maxMembers: newMaxMembers });
    socialApi.testStatus(StatusCodes.FORBIDDEN, r);

    const actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["leader"], groupId);

    // expect maxMembers remains at its original value
    socialApi.expectMore(
      () => {expect(actualGroupInfo.body.maxMembers).toEqual(groupInitSize)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the value of maxMembers is not the expected original value"
        }
      }
    );
  });
});