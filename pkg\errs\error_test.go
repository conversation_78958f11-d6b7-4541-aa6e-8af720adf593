package errs

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func TestNewError(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("NewError", func() {
		g.It("should should succeed", func() {
			e := New(http.StatusForbidden, EInvalidJWT)
			g.<PERSON>sert(e).IsNotNil()
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusForbidden)
			g.Assert(e.GetSocialErrorCode()).Equal(EInvalidJWT)
			g.<PERSON>sert(e.Error()).Equal(ErrorMap[EInvalidJWT].Message)

			g.Assert(IsEqual(e, EInvalidJWT)).IsTrue()
			g.<PERSON>ser<PERSON>(IsEqual(e, EInvalidAccountType)).IsFalse()
		})
	})

	g.Describe("NewMessage", func() {
		g.It("should should succeed", func() {
			e := NewMessage(http.StatusForbidden, "custom error", EInvalidJWT)
			g.Assert(e).IsNotNil()
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusForbidden)
			g.Assert(e.GetSocialErrorCode()).Equal(EInvalidJWT)
			g.Assert(e.Error()).Equal("custom error")
		})
	})

	g.Describe("Panic", func() {
		g.It("should should succeed", func() {
			os.Setenv("DD_ENV", "test")
			e := Panic()
			g.Assert(e).IsNotNil()
			g.Assert(e.GetHttpErrorCode()).Equal(http.StatusInternalServerError)
			g.Assert(e.GetSocialErrorCode()).Equal(EPanic)
			g.Assert(e.Error()).Equal("panic")
			// stack should be nil because DD_ENV is test
			g.Assert(e.GetStack()).IsNil()
		})
	})
}

func TestReturn(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("ErrorString", func() {
		g.It("should should succeed", func() {
			prevEnv := os.Getenv("DD_ENV")
			os.Setenv("DD_ENV", "test")
			ctx := context.Background()
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
			w := httptest.NewRecorder()
			e := New(http.StatusNotFound, EFriendsNotFriend)
			Return(w, r, e)
			g.Assert(w.Code).Equal(http.StatusNotFound)
			g.Assert(w.Body.String()).Equal(utils.EncodeJson(e) + "\n")

			os.Setenv("DD_ENV", "develop")
			Return(w, r, e)
			g.Assert(utils.StringContainsSubstr(w.Body.String(), "stack")).IsTrue()
			os.Setenv("DD_ENV", prevEnv)
		})
	})
}

func TestErrorString(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("ErrorString", func() {
		g.It("should should succeed", func() {
			g.Assert(ErrorString(EInvalidJWT)).Equal("invalid token")
			g.Assert(ErrorString(SocialError(1000000000))).Equal("unknown error")
		})
	})
}
