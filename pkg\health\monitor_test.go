package health

import (
	"sync/atomic"
	"testing"
	"time"
	"unsafe"

	"github.com/franela/goblin"
)

type TestService struct {
	critical   *int32
	health     *int32
	lastStatus *ServiceStatus
	checkCount *int32
}

func (s *TestService) IsCritical() bool {
	return atomic.LoadInt32(s.critical) == 1
}

func (s *TestService) CheckHealth() bool {
	atomic.StoreInt32(s.checkCount, atomic.LoadInt32(s.checkCount)+1)
	return atomic.LoadInt32(s.health) == 1
}

func (s *TestService) LastStatus() *ServiceStatus {
	return s.lastStatus
}

func NewTestService() *TestService {
	return &TestService{
		critical:   new(int32),
		health:     new(int32),
		checkCount: new(int32),
	}
}

func TestHealthText(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("HealthText", func() {
		g.It("should get correct heath text", func() {
			g.<PERSON>sert(HealthText(Health(0))).Equal("UNKNOWN")
			g.<PERSON><PERSON>(HealthText(Health(1))).Equal("OK")
			g.<PERSON>(HealthText(Health(2))).Equal("FAIL")

			json, err := Health(0).MarshalJSON()
			g.Assert(err).IsNil()
			g.Assert(string(json)).Equal("\"UNKNOWN\"")
		})
	})
}

func TestSetInstance(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("SetInstance", func() {
		g.It("should update instance correctly", func() {
			i := InstanceInfo{}
			ts := time.Now()
			dur := int64(100)
			SetInstance(&i, OK, ts, dur, "foobar")
			g.Assert(i.Status).Equal(OK)
			g.Assert(i.ExtraInfo).Equal("foobar")
			g.Assert(i.LastCheck).Equal(ts)
			g.Assert(i.LastCheckDuration).Equal(dur)
			g.Assert(i.RunningFailures).Equal(0)
			g.Assert(i.LastKnownWorking).Equal(ts)

			extra := map[string]string{
				"foo": "bar",
			}
			ts2 := time.Now()
			SetInstance(&i, FAIL, ts2, dur, extra)
			g.Assert(i.Status).Equal(FAIL)
			g.Assert(i.ExtraInfo).Equal(extra)
			g.Assert(i.LastCheck).Equal(ts2)
			g.Assert(i.LastCheckDuration).Equal(dur)
			g.Assert(i.RunningFailures).Equal(1)
			g.Assert(i.LastKnownWorking).Equal(ts)
		})
	})
}

func TestNewServiceMonitor(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("NewServiceMonitor", func() {
		g.It("should create monitor properly", func() {
			m := NewServiceMonitor("test", "1.0")
			g.Assert(m).IsNotNil()
			g.Assert(*m.overallHealth).Equal(int32(0))
		})
	})
}

func TestServiceMonitor(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("ServiceMonitor", func() {
		g.It("should marshal json correctly", func() {
			m := NewServiceMonitor("test", "1.0")

			jsonBuf := m.GetHealthReport(HealthReportPublic, nil)
			json := jsonBuf.String()
			g.Assert(len(json) > 0).IsTrue()

			jsonBuf = m.GetHealthReport(HealthReportPrivate, nil)
			json = jsonBuf.String()
			g.Assert(len(json) > 0).IsTrue()

		})

		g.It("should add dependent service", func() {
			m := NewServiceMonitor("test", "1.0")
			s := NewTestService()
			m.AddDependentService("foo", s)
			service, ok := m.Registry["foo"]
			g.Assert(ok).IsTrue()
			ts, ok := service.(*TestService)
			g.Assert(ok).IsTrue()
			g.Assert(ts).Equal(s)
		})

		g.It("should get/set overall health properly", func() {
			m := NewServiceMonitor("test", "1.0")
			h := m.GetOverallHealth(nil)
			g.Assert(h).Equal(UNKNOWN)
			m.setOverallHealth(OK)
			h = m.GetOverallHealth(nil)
			g.Assert(h).Equal(OK)

			report := m.GetHealthReport(HealthReportPrivate, nil)
			g.Assert(report).IsNotNil()
			g.Assert(len(report.Bytes())).IsNotZero()

			badResult := m.GetHealthReport(HealthReportType(100), nil)
			g.Assert(badResult).IsNil()
		})

		g.It("should update report properly", func() {
			m := NewServiceMonitor("test", "1.0")
			s := NewTestService()
			m.AddDependentService("foo", s)

			idx := m.reportIdx
			m.updateReport()
			g.Assert(m.reportIdx).Equal(idx ^ 1)
		})

		g.It("should call service probe", func() {
			//
			m := NewServiceMonitor("test", "1.0")
			s := NewTestService()
			atomic.StoreInt32(s.critical, 1)
			m.AddDependentService("foo", s)
			m.pollInterval = 20 * time.Millisecond
			m.Start()
			time.Sleep(m.pollInterval * 2)
			m.Stop()
			g.Assert(atomic.LoadInt32(s.checkCount) >= 1).IsTrue() // could be called twice if the timing is right
			g.Assert(m.GetOverallHealth(nil)).Equal(FAIL)
		})

		g.It("should call service probe with ok status", func() {
			m := NewServiceMonitor("test", "1.0")
			s := NewTestService()
			atomic.StoreInt32(s.critical, 1)
			m.AddDependentService("foo", s)
			m.pollInterval = 20 * time.Millisecond
			atomic.StoreInt32(s.health, 1)
			ptr := unsafe.Pointer(s.lastStatus)
			atomic.StorePointer(&ptr, unsafe.Pointer(&ServiceStatus{
				Status: OK,
			}))
			m.Start()
			time.Sleep(m.pollInterval * 2)
			m.Stop()
			g.Assert(m.GetOverallHealth(nil)).Equal(OK)
		})

	})
}
