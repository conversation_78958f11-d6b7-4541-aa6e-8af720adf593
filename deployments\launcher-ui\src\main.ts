import { FriendsAddModule } from '.';
import {
  EVENT_FRIENDS_ADD_WINDOW_CLOSE,
  EVENT_FRIENDS_ADD_WINDOW_OPEN,
} from './constant';
import FriendsModule from './FriendsModule.svelte';
import {
  APIService,
  EventService,
  FriendsService,
  ILogService,
  MqttService,
  SocialServices,
  TransportService,
  UserService,
} from './services';

class MyTransportService extends TransportService {
  constructor(logService?: ILogService) {
    super(logService);
  }

  publishEvent(eventName: string, data?: unknown) {
    EventService.send(eventName, data);
  }

  subscribeEvent(eventName: string, subscriber: () => void) {
    return EventService.on(eventName, subscriber);
  }

  subscribeEventOnce(eventName: string, subscriber: () => void) {
    EventService.once(eventName, subscriber);
  }

  unsubscribe(eventName: string) {
    EventService.off(eventName);
  }

  unbind(token: string) {
    EventService.unbind(token);
  }
}

const userId = 'b287e655461f4b3085c8f244e394ff7e';
const email = '<EMAIL>';
const appId = '3bb92115af724e509f639113b0d521f8';
const password = 'D2CTesting';
const accessToken = '';
const apiService = new APIService(accessToken);

// TODO: get access token from launcher properly insetad of logging in
// See: https://github.com/take-two-t2gp/d2c-launcher-ui/pull/545
apiService
  .LoginAsync({
    locale: 'en-US',
    email,
    password,
    appId,
  })
  .then(async (response: Response) => {
    if (response.ok) {
      const { accessToken } = await response.json();
      apiService.setAccessToken(accessToken);

      const userService = new UserService(apiService, {
        host: window.location.origin,
        redirectUrl: `${window.location.origin}/authRedirect.html`,
      });
      const friendsService = new FriendsService(apiService);
      const mqttService = new MqttService(accessToken, userId);
      const transportService = new MyTransportService();
      const services = new SocialServices({
        transportService,
        friendsService,
        userService,
        mqttService,
      });

      transportService.subscribeEvent(EVENT_FRIENDS_ADD_WINDOW_OPEN, () => {
        document.getElementById('children').style.display = 'block';
      });

      transportService.subscribeEvent(EVENT_FRIENDS_ADD_WINDOW_CLOSE, () => {
        document.getElementById('children').style.display = 'none';
      });

      new FriendsModule({
        target: document.getElementById('main'),
        props: {
          services,
          lang: 'en',
          url: '',
        },
      });

      new FriendsAddModule({
        target: document.getElementById('children'),
        props: {
          services,
          lang: 'en',
          url: '',
        },
      });
    }
  });
