package apipub

import (
	"fmt"
)

// RedisKey redis key
func (friend *FriendResponse) RedisKey(tenant string) string {
	return fmt.Sprintf("%s:friend:{%s}:%s", tenant, friend.Userid, friend.Friendid)
}

func BuildFriendRedisKey(tenant, userid, friendid string) string {
	return fmt.Sprintf("%s:friend:{%s}:%s", tenant, userid, friendid)
}

// PK partition key
func (friend *FriendResponse) PK(tenant string) string {
	return tenant + "#user#" + friend.Userid
}

// SK sort key
func (friend *FriendResponse) SK(tenant string) string {
	return tenant + "#friend#" + friend.Friendid
}
