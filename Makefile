# Put the following in your .env
# T2_GITHUB_USERNAME
# T2_GITHUB_ACCESS_TOKEN
# GITHUB_USERNAME
# GITHUB_ACCESS_TOKEN
ifneq ("$(wildcard .env)","")
	include .env
endif

BIN_DIR = build
BIN_EXT =
ifeq ($(OS),Windows_NT)
	BIN_EXT = .exe
endif

OAPI_CODEGEN=github.com/oapi-codegen/oapi-codegen/v2/cmd/oapi-codegen@v2.3.0

SERVER_BIN = ${BIN_DIR}/social-api${BIN_EXT}
ALL_TARGETS = ${SERVER_BIN}

# This version-strategy uses git tags to set the version string
# GIT_HASH := $(shell git rev-list -1 HEAD)
# GIT_HASH_SHORT := $(shell git rev-parse --short HEAD)
# UI_GIT_HASH := $(shell cd deployments/launcher-ui && git rev-list -1 HEAD)
# VERSION := $(shell git describe --tags --abbrev=0 ${GIT_HASH})-$(GIT_HASH_SHORT)
# BUILD_DATE := $(shell date +"%Y-%m-%dT%H:%M:%S%:z")

MOCKS = pkg/store/mockdatastore.gen.go \
	pkg/cache/mockrediscache.gen.go \
	pkg/health/mockhealth.gen.go \
	pkg/identity/mockidentity.gen.go \
	pkg/middleware/mockdnaidentity.gen.go \
	pkg/telemetry/mocktelemetry.gen.go

pkg/store/mockdatastore.gen.go: pkg/store/datastore.go
pkg/cache/mockrediscache.gen.go: pkg/cache/rediscache.go
pkg/health/mockhealth.gen.go: pkg/health/monitor.go
pkg/middleware/mockdnaidentity.gen.go: pkg/middleware/basicauth_test.go
pkg/telemetry/mocktelemetry.gen.go: pkg/telemetry/telemetry.go


# HELP
# This will output the help for each task
# thanks to https://marmelab.com/blog/2016/02/29/auto-documented-makefile.html
.PHONY: help api build test

help:
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help

version: ## Show version
	@echo $(VERSION) \(git commit: $(GIT_HASH)\)

build: docs-bundle pkg/apipub/apipub.gen.go pkg/apipriv/apipriv.gen.go pkg/apitrusted/apitrusted.gen.go ## Build server
	go generate ./...
	go build -o ${SERVER_BIN} cmd/social/main.go

pkg/identity/mockidentity.gen.go:
	mockgen -source=pkg/identity/identity.go -destination=pkg/identity/mockidentity.gen.go -package=identity -self_package=github.com/take-two-t2gp/t2gp-social-service/pkg/identity > $@

%.gen.go:
	@echo Generating $@
	@if ! type "mockgen" > /dev/null; then \
  	go install go.uber.org/mock/mockgen@latest; \
	fi
	-@mockgen -source $< -destination $@ -package $(shell echo "$<" | sed "s/pkg\/\([[:alpha:]]\+\)\/.*/\1/g") $0 -self_package=$0
	-@if [ "$(shell uname)" == "Windows_NT" ]; then unix2dos $@; fi


mocks:  ## Build mock interfaces for unit testing
	rm -f ${MOCKS}
	${MAKE} ${MOCKS}

cleantest: $(MOCKS) ## Run uncached unit tests
	go clean -cache
	go test ./pkg/... -race -coverprofile cp.out -cover $(OPTS)

cleantestwithreport: $(MOCKS)
	go clean -cache
	go install github.com/jstemmer/go-junit-report@latest
	go test 2>&1 ./pkg/... -race -coverprofile cp.out -cover $(OPTS) | sed -e 's/\x1b\[[0-9;]*m//g' | ${GOPATH}/bin/go-junit-report -set-exit-code > report.xml

cleantestwithcoverage: $(MOCKS) ## Run uncached unit tests
	@echo "Generating coverage cp.out"
	go clean -cache
	go test ./pkg/... -race -coverprofile cp.out

cleantestexceptid: $(MOCKS) ## Run uncached unit tests
	@echo "Generating coverage cp.out"
	go clean -cache
	go test $$(go list ./pkg/... | grep -v identity) -coverprofile cp.out $(OPTS)

coverage: ## View coverage report in browser
	@if [ ! -e cp.out ]; then ${MAKE} cleantestwithcoverage; fi
	go tool cover -html cp.out

test: $(MOCKS) ## Run unit tests
	go test ./pkg/... -race -coverprofile cp.out $(OPTS)

test-%:
	go test -coverprofile cp.out -v ./pkg/$*

testexceptid:
	go test $$(go list ./pkg/... | grep -v identity) -coverprofile cp.out $(OPTS)

goenv: ## Setup GOPRIVATE environment variable
	go env -w GOPRIVATE="github.com/take-two-t2gp,github.com/2kg-coretech"

pkg/apipub/apipub.gen.go: api/openapi-social-public.yml oapi-codegen.yml
	go install ${OAPI_CODEGEN}
	oapi-codegen -package apipub -config oapi-codegen.yml $< > $@.tmp
	mv $@.tmp $@
	-@if [ "$(shell uname)" == "Windows_NT" ]; then unix2dos $@; fi

pkg/apipriv/apipriv.gen.go: api/openapi-social-private.yml oapi-codegen.yml
	go install ${OAPI_CODEGEN}
	oapi-codegen -package apipriv -config oapi-codegen.yml $< > $@.tmp
	mv $@.tmp $@
	-@if [ "$(shell uname)" == "Windows_NT" ]; then unix2dos $@; fi

pkg/apitrusted/apitrusted.gen.go: api/openapi-social-trusted-server.yml oapi-codegen.yml
	go install ${OAPI_CODEGEN}
	oapi-codegen -package apitrusted -config oapi-codegen.yml $< > $@.tmp
	mv $@.tmp $@
	-@if [ "$(shell uname)" == "Windows_NT" ]; then unix2dos $@; fi


run: ## Start the server
	$(SERVER_BIN)

submodule-update:
	git submodule update --init --recursive --remote

vmq-%: ## start/stop/etc.. vmq server
	${MAKE} -C deployments/vmq-plugin-social $*

# T2_USER and T2_PASS is github.take2games.com user and personal access token
docker: ## Build docker image for social-api service
	docker build --progress plain -t t2gp/social-api . \
		--build-arg VERSION="${VERSION}" \
		--build-arg GIT_HASH="${GIT_HASH}" \
		--build-arg BUILD_DATE="${BUILD_DATE}" \
		--build-arg git_token=${GIT_TOKEN}
	docker tag t2gp/social-api 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-social-api:latest

publish: ## Publish social-api service docker image to AWS ECR
	aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 354767525209.dkr.ecr.us-east-1.amazonaws.com
	docker push 354767525209.dkr.ecr.us-east-1.amazonaws.com/t2gp-social-api:latest

ecr: ## build and publish to ecr
	${MAKE} docker vmq-docker publish vmq-publish

poc-start: ## Start POC server
	docker-compose -f deployments/docker-compose.yml up -d

poc-stop: ## Stop POC server
	docker-compose -f deployments/docker-compose.yml down

poc-pull: ## Pull POC images
	aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 354767525209.dkr.ecr.us-east-1.amazonaws.com
	docker-compose -f deployments/docker-compose.yml pull

poc-restart: ## Start POC server
	${MAKE} poc-stop poc-start

poc-logs: ## Start POC server
	docker-compose -f deployments/docker-compose.yml logs -f --tail 10

poc-%-shell: ## Start POC server
	docker-compose -f deployments/docker-compose.yml exec $* /bin/sh

docs-upload: ## Publish docs
	t2gp-docs-upload --publish

errors: ## Generate docs/api-error-codes.md
	go run cmd/social-errors/main.go > docs/api-error-codes.md

lint: ## Run staticcheck linter
	go install honnef.co/go/tools/cmd/staticcheck@v0.6.1
	staticcheck ./pkg/... ./cmd/...

vet:
	go vet ./...

build-launcher-ui: ## Build launcher ui
	cd deployments/launcher-ui && \
		npm i && \
		npm run build
	rm -rf web/launcher
	cp -r deployments/launcher-ui/public web/launcher

sync-launcher-ui: ## Sync pre-built launcher ui from S3
	rm -rf web/launcher
	aws s3 sync s3://t2gp-social/t2gp-social-frontend/${UI_GIT_HASH} web/launcher

docs-bundle: ## stitches the various doc yaml files into one file for processing by devdocs.  requires redocly cli.  npm i -g @redocly/cli@latest
	redocly bundle ./api/bases/public-base.yaml -o ./api/openapi-social-public.yml
	redocly bundle ./api/bases/trusted-base.yaml -o ./api/openapi-social-trusted-server.yml