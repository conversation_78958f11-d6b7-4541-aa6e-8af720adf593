-module(t2gp_social_db).

-include_lib("vernemq_dev/include/vernemq_dev.hrl").
-include_lib("erlcloud/include/erlcloud.hrl").
-include_lib("erlcloud/include/erlcloud_aws.hrl").
-include_lib("erlcloud/include/erlcloud_ddb2.hrl").
-include_lib("t2gp_social.hrl").

% gen_server callbacks
-export([
    start_link/0,
    init/1,
    handle_call/3,
    handle_cast/2,
    handle_info/2,
    terminate/2,
    code_change/3
]).

-export([
    on_reload/0,
    create_table/0,
    get_user/2,
    get_friends/2,
    get_groups/3,
    list_subscriber_id/1,
    get_userid_from_subscriber_id/1,
    add_subscriber_id/2,
    del_subscriber_id/1,
    get_subscriber_id/1,
    del_all_subscriptions/1,
    put_item/1,
    delete_item/1,
    profile_table/0,
    key/2
]).

-record(state, {
    my_cluster
}).

-define(IF(Cond, E1, E2),
    (case (Cond) of
        true -> (E1);
        false -> (E2)
    end)
).

-define(SERVICE, <<"aws.dynamodb">>).

-spec get_user(any(), username()) -> {ok, user()} | {error, term()}.
get_user(Tenant, UserId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        Table = profile_table(),
        PK = utils:concat([Tenant, <<"#">>, key(?USER, UserId)], binary),
        SK = utils:concat([Tenant, "#profile#", UserId], binary),
        case erlcloud_ddb2:get_item(Table, [{?PK, PK}, {?SK, SK}]) of
            {ok, User} ->
                {ok, User};
            Error ->
                Error
        end
    end).

-spec put_item(item()) -> {ok, item()}.
put_item(Item) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{
            pk => proplists:get_value(<<"pk">>, Item, <<"">>),
            sk => proplists:get_value(<<"sk">>, Item, <<"">>)
        }),
        Table = profile_table(),
        erlcloud_ddb2:put_item(Table, Item)
    end).

-spec delete_item(item()) -> {ok, item()}.
delete_item(Item) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{
            pk => proplists:get_value(<<"pk">>, Item, <<"">>),
            sk => proplists:get_value(<<"sk">>, Item, <<"">>)
        }),
        Table = profile_table(),
        erlcloud_ddb2:delete_item(Table, Item)
    end).

-spec create_table() -> {ok, list()} | {error, term()}.
create_table() ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{table => profile_table()}),
        Table = profile_table(),
        {ok, TableList} = erlcloud_ddb2:list_tables(),
        lager:info("table_list()=~p, Table=~p", [TableList, Table]),
        case lists:member(Table, TableList) of
            false ->
                % create table
                lager:info("Creating dynamodb table ~p", [Table]),
                erlcloud_ddb2:create_table(
                    Table,
                    [{?PK, s}, {?SK, s}],
                    {?PK, ?SK},
                    [{provisioned_throughput, {5, 5}}]
                );
            Else ->
                lager:info("Table ~p is available", [Table]),
                Else
        end
    end).

-spec on_reload() -> ok.
on_reload() ->
    % start_link(),
    ok.

-spec query_items(pk(), sk()) -> {ok, [list()]} | {error, term()}.
query_items(PK, SK) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{pk => PK, sk => SK}),
        lager:info("query_items pk= ~p sk = ~p", [PK, SK]),
        Table = profile_table(),
        erlcloud_ddb2:q(
            Table,
            <<"#pk = :pk AND begins_with(#sk, :sk)">>,
            [
                {expression_attribute_values, [
                    {<<":pk">>, PK},
                    {<<":sk">>, SK}
                ]},
                {expression_attribute_names, [
                    {<<"#pk">>, ?PK},
                    {<<"#sk">>, ?SK}
                ]},
                {select, all_attributes}
            ]
        )
    end).

-spec get_friends(any(), username()) -> {ok, [item()]} | {error, term()}.
get_friends(Tenant, UserId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        PK = utils:concat([Tenant, <<"#">>, key(?USER, UserId)], binary),
        SK = utils:concat([Tenant, <<"#">>, key(?FRIEND, <<>>)], binary),
        query_items(PK, SK)
    end).

%% will only get group ids from redis
-spec get_groups(any(), username(), any()) -> {ok, [item()]} | {error, term()}.
get_groups(Tenant, UserId, ProductId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        gen_server:call(t2gp_social_db, {get_groups, Tenant, UserId, ProductId})
    end).

-spec add_subscriber_id(username(), subscriber_id()) -> ok | {error, term()}.
add_subscriber_id(UserId, SubscriberId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        vmq_metadata:put(?META_SUBID_USER, SubscriberId, UserId),
        case get_subscriber_id(UserId) of
            undefined ->
                Set = sets:from_list([SubscriberId]),
                vmq_metadata:put(?META_USER_SUBID, UserId, Set);
            {error, _} = Error ->
                Error;
            Set ->
                Set1 = sets:add_element(SubscriberId, Set),
                vmq_metadata:put(?META_USER_SUBID, UserId, Set1)
        end
    end).

-spec list_subscriber_id(username()) -> [subscriber_id()].
list_subscriber_id(UserId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        case vmq_metadata:get(?META_USER_SUBID, UserId) of
            undefined -> [];
            {error, _} -> [];
            Set -> sets:to_list(Set)
        end
    end).

-spec get_subscriber_id(username()) -> sets:set() | undefined.
get_subscriber_id(UserId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        vmq_metadata:get(?META_USER_SUBID, UserId)
    end).

-spec get_userid_from_subscriber_id(subscriber_id()) -> username().
get_userid_from_subscriber_id({_, ClientId} = SubscriberId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        vmq_metadata:get(?META_SUBID_USER, SubscriberId)
    end).

-spec del_subscriber_id(subscriber_id()) -> ok.
del_subscriber_id({_, ClientId} = SubscriberId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        case vmq_metadata:get(?META_SUBID_USER, SubscriberId) of
            undefined ->
                ok;
            {error, _} = Error ->
                Error;
            UserId ->
                vmq_metadata:delete(?META_SUBID_USER, SubscriberId),
                case get_subscriber_id(UserId) of
                    undefined ->
                        ok;
                    Set ->
                        Set1 = sets:del_element(SubscriberId, Set),
                        vmq_metadata:put(?META_USER_SUBID, UserId, Set1)
                end
        end
    end).

-spec del_all_subscriptions(username()) -> ok.
del_all_subscriptions(UserId) ->
    t2gp_social_apm:span(?SERVICE, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => UserId}),
        vmq_metadata:delete(?META_USER_SUBID, UserId)
    end).
%
% gen_server
%

-spec start_link() -> {ok, pid()} | {error, {already_started, pid()}}.
start_link() ->
    lager:info("t2gp_social_db:start_link"),
    gen_server:start_link({local, ?MODULE}, ?MODULE, [], []).

-spec init(term()) -> {ok, state()}.
init(_) ->
    % used only for connecting to local dynamodb
    case utils:get_env() of
        <<"local">> ->
            % Endpoint = application:get_env(t2gp_social, dynamodb_endpoint, "http://*************:8000"),
            % lager:info("t2gp_social_db:init setting up dynamodb connection to ~p", [Endpoint]),
            % Url = uri_string:parse(Endpoint),
            % Scheme = maps:get(scheme, Url) ++ "://",
            % Host = maps:get(host, Url),
            % Port = maps:get(port, Url, ?IF(Scheme == "https://", 443, 80)),
            erlcloud_ddb2:configure("local", "local", "*************", 8000);
        _ ->
            ok
    end,
    %% setup redis
    ElasticacheURL = application:get_env(t2gp_social, elasticache_url, "localhost:6379"),
    lager:info("t2gp_social_db:elasticache_url ~p", [ElasticacheURL]),
    [ElasticacheHost, ElasticachePortStr] = string:tokens(ElasticacheURL, ":"),
    {ElasticachePort, _} = string:to_integer(ElasticachePortStr),
    application:set_env(eredis_cluster, pool_size, 5),
    application:set_env(eredis_cluster, pool_max_overflow, 10),
    application:set_env(eredis_cluster, socket_options, [{send_timeout, 6000}]),
    eredis_cluster:start(),
    eredis_cluster:connect([{ElasticacheHost, ElasticachePort}]),
    NewState = #state{},
    {ok, NewState}.

-spec handle_call({atom(), term()}, {pid(), term()}, state()) -> {reply, term(), state()}.
handle_call({get_groups, Tenant, UserId, ProductId}, _From, State) ->
    Key = lists:flatten(
        io_lib:format("~s:prod:~s:~s:user:{~s}:memberOf", [
            Tenant, ProductId, utils:get_env(), UserId
        ])
    ),
    lager:info("get_groups key: ~p", [Key]),
    case eredis_cluster:q(["ZRANGE", Key, "-", "+", "BYLEX"]) of
        {ok, Groups} ->
            lager:info("get_groups groups ~p", [Groups]),
            DataHashedGroupids = [lists:nth(6, binary:split(G, <<":">>, [global])) || G <- Groups],
            lager:info("datahashedgroupids ~p", [DataHashedGroupids]),
            Groupids = lists:map(fun(X) -> string:trim(X, both, [${, $}]) end, DataHashedGroupids),
            lager:info("groupids ~p", [Groupids]),
            {reply, {ok, Groupids}, State};
        _ ->
            {reply, {ok, []}, State}
    end;
handle_call({get_ts_auth, UserId, Password}, _From, State) ->
    Key = lists:flatten(io_lib:format("ts:~s:clientid:~s", [utils:get_cluster_env(), UserId])),
    lager:info("get_ts_auth key: ~p", [Key]),
    case eredis_cluster:q(["JSON.GET", Key, "$.hash"]) of
        {ok, Value} when (Value /= undefined) and (Value /= nil) ->
            % get returns binary in this format: ["hashstringhere"],
            ValStr = binary_to_list(Value),
            H = list_to_binary(string:substr(ValStr, 3, string:len(ValStr) - 4)),
            % don't log these values. may be useful for debugging
            % lager:info("get_ts_auth groups \nbin  >~s<\nstr  >~s<\nhash >~s<\npass >~s<", [Value, ValStr, H, Password]),
            if
                H == Password ->
                    {reply, ok, State};
                H /= Password ->
                    % lager:info("get_ts_auth not equal hash >~w<\npass >~w<", [H, Password]),
                    {reply, error, State}
            end;
        _ ->
            {reply, error, State}
    end;
handle_call(Msg, _From, State) ->
    lager:info("Unhandled message: ~p", [Msg]),
    {reply, ok, State}.

-spec handle_cast(term(), state()) -> {noreply, state()}.
handle_cast(_Msg, State) ->
    {noreply, State}.

-spec handle_info(term(), state()) -> {noreply, state()}.
handle_info(_Msg, State) ->
    {noreply, State}.

-spec terminate(normal | shutdown | {shutdown, term()} | term(), term()) -> ok.
terminate(Reason, State) ->
    lager:info("t2gp_social_db:terminate ~p, ~p", [Reason, State]),
    ok.

-spec code_change(term() | {down, term()}, state(), term()) -> {ok, state()} | {error, term()}.
code_change(OldVsn, State, Extra) ->
    lager:info("t2gp_social_db:code_change ~p, ~p, ~p", [OldVsn, State, Extra]),
    {ok, State}.

-spec profile_table() -> binary().
profile_table() ->
    {ok, Table} = application:get_env(t2gp_social, profile_table),
    list_to_binary(Table).

-spec key(binary(), binary()) -> binary().
key(Type, Id) ->
    erlang:iolist_to_binary([Type, <<"#">>, Id]).
