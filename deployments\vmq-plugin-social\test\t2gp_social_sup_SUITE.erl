-module(t2gp_social_sup_SUITE).

-compile(nowarn_export_all).
-compile(export_all).

init_per_suite(Config) ->
    cover:start(),
    Config.

end_per_suite(_Config) ->
    ok.

all() ->
    [
        test_case
    ].

test_case(_) ->
    Result = t2gp_social_sup:init([]),
    SupFlags =
        #{strategy => one_for_one, intensity => 1000, period => 10},
    ChildSpecs = [
        #{
            id => t2gp_social_db,
            start => {t2gp_social_db, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_db]
        },
        #{
            id => t2gp_social_dna,
            start => {t2gp_social_dna, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_dna]
        },
        #{
            id => t2gp_social_pd,
            start => {t2gp_social_pd, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_pd]
        },
        #{
            id => t2gp_social_rsg,
            start => {t2gp_social_rsg, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_rsg]
        }
    ],

    Result = {ok, {SupFlags, ChildSpecs}},
    ok.
