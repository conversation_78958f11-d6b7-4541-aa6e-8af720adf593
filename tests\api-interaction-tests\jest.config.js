module.exports = {
  transform: { '\\.tsx?$': 'ts-jest' },
  testTimeout: 15000,
  reporters: [
    'default',
    [
      'jest-html-reporter',
      {
        pageTitle: 'Social API Test Report',
        outputPath: `reports/${new Date().getFullYear()}${
          new Date().getMonth() + 1
        }${new Date().getDate()}-${new Date().getHours()}${new Date().getMinutes()}-social-api-test-report.html`,
        includeFailureMsg: true,
      },
    ],
    [
      'jest-json-cumulative-reporter',
      {
        filename: `reports/${new Date().getFullYear()}${
          new Date().getMonth() + 1
        }${new Date().getDate()}-${new Date().getHours()}${new Date().getMinutes()}-social-api-test-report.json`,
        ignore: ['sandbox.spec.js', '/ignoredTests'],
      },
    ],
    'jest-github-actions-reporter',
  ],
  testLocationInResults: true,
  verbose: true,
  globalSetup: "./globalSetup.ts",
  testEnvironment: 'node'
};
