import { StatusCodes } from 'http-status-codes';
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';

let tokenHost: string;
let tokenInvited: string;
let tokenRestricted: string;
let roomId: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  tokenInvited = await socialApi.loginIn(
    config.invitedUsername,
    config.invitedPassword
  );
  roomId = await (await socialApi.createRoom(tokenHost, 3)).body.groupid;
});

afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
  await socialApi.loginOut(tokenInvited);
});
// eslint-disable-next-line max-lines-per-function
describe('', () => {
  /**
   * Checking sending message to all users in room
   * - Host create a room
   * - One use join the room
   * - Host sends a mesage,check if host and the user get the mqtt message
   * - User sends a message,check if host and the user get the mqtt message
   * - Call restful API to get room message to check the message
   */
  it('Send message to all users in room', async () => {
    const messageHost = 'hi there' + ' ' + Math.round(Math.random() * 1000);
    const messageInvited = 'hi friend' + ' ' + Math.round(Math.random() * 1000);
    await socialApi.createRoom(tokenHost, 3);
    await socialApi.joinRoom(tokenInvited, roomId);
    const respSendHost: request.Response = await socialApi.sendRoomMessage(
      tokenHost,
      roomId,
      messageHost
    );
    expect(respSendHost.status).toEqual(StatusCodes.OK);
    const respSendInvited: request.Response = await socialApi.sendRoomMessage(
      tokenInvited,
      roomId,
      messageInvited
    );

    expect(respSendInvited.status).toEqual(StatusCodes.OK);

    const respHost: request.Response = await socialApi.getRoomMessage(
      tokenHost,
      roomId
    );
    //console.log(respHost.body);
    expect(respHost.status).toEqual(StatusCodes.OK);

    expect(respHost.body.items[0].message).toEqual(messageInvited);
    expect(respHost.body.items[1].message).toEqual(messageHost);

    const respInvited: request.Response = await socialApi.getRoomMessage(
      tokenInvited,
      roomId
    );
    expect(respInvited.status).toEqual(StatusCodes.OK);
    expect(respInvited.body.items[0].message).toEqual(messageInvited);
    expect(respInvited.body.items[1].message).toEqual(messageHost);
  });

  it('send private message to someone in the group', async () => {
    tokenRestricted = await socialApi.loginIn(
      config.restrictedUsername,
      config.restrictedPassword
    );

    const messageHost = 'hi there' + ' ' + Math.round(Math.random() * 1000);
    await socialApi.joinRoom(tokenInvited, roomId);
    await socialApi.joinRoom(tokenRestricted, roomId);
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .post(`/chat/rooms/${roomId}/messages`)
      .set('Authorization', 'Bearer ' + tokenInvited)
      .send({
        message: messageHost,
        type: 'group-private',
        toUserid: config.restrictedUserId,
      });

    expect(resp.status).toEqual(StatusCodes.OK);
    await socialApi.loginOut(tokenRestricted);
  });

  /**
   * Checking sending unacceptable message
   * - host create room
   * - user join the room
   * - host send an unacceptable message in the group
   * - user send an unacceptable message in the group
   */
  it('send message with profanity check', async () => {
    await socialApi.createRoom(tokenHost, 3);
    await socialApi.joinRoom(tokenInvited, roomId);

    const respSendHost: request.Response = await socialApi.sendRoomMessage(
      tokenHost,
      roomId,
      config.Bad_Words
    );

    expect(respSendHost.body).toEqual({
      code: 406,
      message: 'Message is unacceptable',
    });

    const respSendInvited: request.Response = await socialApi.sendRoomMessage(
      tokenInvited,
      roomId,
      config.Bad_Words
    );
    expect(respSendInvited.body).toEqual({
      code: 406,
      message: 'Message is unacceptable',
    });
  });
});
