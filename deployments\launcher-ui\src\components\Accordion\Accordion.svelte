<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { writable } from 'svelte/store';
  import { ACCORDION_MODE } from '../../constant';
  import { setAccordionContext } from '../../context';

  export let value: string[] = [];
  export let className = '';
  export let mode = ACCORDION_MODE.multiple;
  const dispatch = createEventDispatcher();
  const selected = writable([...value]);

  const onHeaderClicked = (headerKey: string) => {
    if (mode === ACCORDION_MODE.multiple) {
      // when multiple mode, clicking header will toggle the accordion section
      if ($selected.includes(headerKey)) {
        selected.set($selected.filter(key => key !== headerKey));
      } else {
        selected.set([...$selected, headerKey]);
      }
    } else {
      selected.set([headerKey]);
    }
    dispatch('change', headerKey);
  };

  setAccordionContext({
    onHeaderClicked,
    selected,
  });
</script>

<style>
  .accordion {
    list-style: none;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
  }
</style>

<ul class="{`accordion ${className}`}">
  <slot />
</ul>
