package main

import (
	"fmt"
	"sort"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
)

func main() {

	fmt.Printf("| Code | ID | Message |\n")
	fmt.Printf("|------|----|---------|\n")
	var errors []errs.SocialError
	for k := range errs.ErrorMap {
		errors = append(errors, k)
	}

	sort.Slice(errors, func(i, j int) bool {
		return errors[i] < errors[j]
	})

	for _, k := range errors {
		v := errs.ErrorMap[k]
		fmt.Printf("| %d | %s | %s |\n", k, v.ID, v.Message)
	}

}
