# Define the service endpoint and payload
$uri = "https://api.example.com/v1/resource"
$method = "POST"
$headers = @{
    "Content-Type" = "application/json"
    "Authorization" = "Bearer YOUR_API_TOKEN"
}
$body = @{
    name = "<PERSON>"
    email = "<EMAIL>"
} | ConvertTo-Json

# Make the HTTP request
try {
    Write-Host "Sending request to $uri..."
    $response = Invoke-RestMethod -Uri $uri -Method $method -Headers $headers -Body $body
    Write-Host "Response received:"
    $response | ConvertTo-Json -Depth 10
}
catch {
    Write-Host "Error occurred: $($_.Exception.Message)"
}
