package apipub

import (
	"fmt"
)

// PK partition key
func (blocklist *BlocklistResponse) PK(tenant string) string {
	return tenant + "#user#" + blocklist.Userid
}

// SK sort key
func (blocklist *BlocklistResponse) SK(tenant string) string {
	return tenant + "#blocklist#" + blocklist.Blockedid
}

func (blocklist *BlocklistResponse) RedisKey(tenant string) string {
	return fmt.Sprintf("%s:user:{%s}:blocks:%s", tenant, blocklist.Userid, blocklist.Blockedid)
}

func BuildBlocklistRedisKey(tenant, userid, blockedid string) string {
	return fmt.Sprintf("%s:user:{%s}:blocks:%s", tenant, userid, blockedid)
}

func ExistsInBlocklistArr(blocklists *[]*BlocklistResponse, blockedid string) bool {
	if blocklists != nil {
		for _, blocklistItem := range *blocklists {
			if blocklistItem != nil && blocklistItem.Blockedid == blockedid {
				return true
			}
		}
	}
	return false
}
