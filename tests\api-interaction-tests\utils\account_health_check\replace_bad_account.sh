#!/bin/bash

if [ $# -ne 3 ]; then
    echo "usage: $0 owner bad_accnt_email remark"
    exit 1
fi

owner=$1
bad_accnt_email=$2
remark=$3

# go to the dir where the account management tool is
cd ../account_mgmt

# setup environment
./setup.sh

# sanity check to ensure the input owner currently has no accounts marked as "bad"
if [[ $(./account_mgmt_cli.py -at twok -y | grep "$owner" | awk -F "|" '{print $4}') -ne 0 ]]; then
    echo "error: [$owner] currently has accounts marked as bad"
    exit 1
fi

# sanity check to ensure the input email belongs to the input owner
if ! ./account_mgmt_cli.py -at twok -s owner "$owner" view | grep "$bad_accnt_email" -q; then
    echo "error: [$bad_accnt_email] does not belong to [$owner]"
    exit 1
fi

# force unlock
./account_mgmt_cli.py -at twok -f

# obtain lock
./account_mgmt_cli.py -at twok -l "$owner"

# mark account as "bad"
./account_mgmt_cli.py -at twok -t "email" "$bad_accnt_email" b

# add remark
./account_mgmt_cli.py -at twok -r "email" "$bad_accnt_email" "$remark"

# move 1 "bad" account from the input owner to None
./account_mgmt_cli.py -at twok -m 1 b "$owner" None

# TODO: if "None" ran out of good accounts, generate more.

# move 1 "good" account from None to the input owner
./account_mgmt_cli.py -at twok -m 1 g None "$owner"

# commit changes
./account_mgmt_cli.py -at twok -p

if [[ "$owner" == "CI" ]]; then
    # generate the new account env file, and upload it
    ./account_mgmt_cli.py -at twok -s owner "$owner" ci > .env.twok_accounts_FOR_CI_DO_NOT_USE
    aws s3 cp ./.env.twok_accounts_FOR_CI_DO_NOT_USE s3://t2gp-social-develop/ci-test-account-envs/.env.twok_accounts_FOR_CI_DO_NOT_USE
fi

# unlock
./account_mgmt_cli.py -at twok -u