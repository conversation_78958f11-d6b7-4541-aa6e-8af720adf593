package api

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/go-redis/redismock/v9"
	redis "github.com/redis/go-redis/v9"
	"github.com/rs/zerolog"
	"github.com/segmentio/encoding/json"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/notification"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/store"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"go.uber.org/mock/gomock"
)

var cfg *config.Config
var ctx = context.Background()
var sm *health.ServiceMonitor
var id *identity.Identity
var rc *cache.RedisCache
var ds *store.DataStore
var testSns notification.SNS
var pubApi *SocialPublicAPI

// var privApi *pApi.SocialPrivateAPI
var t *telemetry.Telemetry

// alg=none sub=user1
var User1JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
var User2JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
var User3JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."
var User4JWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."

// Platform with parent account (unlinked)
var UserPlatformUnlinkedJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."

// Platform with parent account (linked)
var UserPlatformLinkedJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************."

// alg=HS256 sub=user1 secret=hello exp=***********
var AlgJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************.hyqiRTXTKUwLOENLLgqxN6m3N2_hYtvf9xQfVw8HRiA"

// alg=HS256 sub=user1 secret=goodbye exp=***********
var InvalidSigJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUwNzQ3NzMzLCJydGkiOiIwMmQ4ZjNlZmI0MGI0NTliOWMzMzExMzY4Y2Y5YmFiMyIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiZWZlNjYxMzA4MjY1NDM4NWI4MTUwYTE2ZDZjOTEzMmMiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6OTk5OTk5OTk5OSwiaWF0IjoxNjUwNzQwNTMzLCJqdGkiOiI3Y2VhYTFmN2FiYjU0MmQxYjI3ZmQ2NTk3ZTVlNzk0NSIsImxhdCI6MzkuMDQ2OX0.FHd5r4KbS61gvbAZ_EWCbpBuYi2IYJjfBNgPZG_jAm8"

// alg=HS256 sub=user1 secret=hello exp=632301738
var ExpiredJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ1c2VyMSIsImV4cCI6NjMyMzAxNzM4fQ.31qgsqKetV-v4B-9IpVt2eR5ay71wyKJ1iF123oVRmA"

// alg=HS256 sub=user1 secret=hello iat=33227196787 exp=33227236787
var NotYetValidJWT string = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjMzMjI3MjM2Nzg3LCJpYXQiOjMzMjI3MTk2Nzg3LCJqdGkiOiI5ZDYxMzBiZDQ0ZGQ0OWJmYThmZDYyODdjNjExMmM4NCIsInR0eSI6MCwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJnaWQiOiI3YjAyYTdlMTc1ODk0NjdmODhiMzY1YWQ1YmIxYzJiNyIsImxvYyI6ImVuLUdCIiwiY3R5IjoiU2VhdHRsZSIsImN0ciI6IlVTIiwibGF0Ijo0Ny42MDM0LCJsb24iOi0xMjIuMzQxNCwicnRpIjoiZTViOTk2ZTM2MDVjNDE3M2JhNTZjZDViZDc1ZjJlZmMiLCJyZXgiOjMzMjI3MjM2Nzg4LCJpc3MiOiI5NTVlMmY2YzdjZTM0NWE2YmEwZDRkNGY3N2Q5MjU3ZCIsInN1YiI6ImI2MDU5NWM1MTFlMDRkODFiNWRkOTVlZTQ1MTc5NGQ4IiwiYXR5IjoyLCJwYWkiOiI0ZTIyZDdhNzBiYTU0MDc1ODQ3MWE2ZDdiYzhhOWVkMiIsIm90eSI6MywiYWdwIjo1LCJzaWQiOiJkNjhkMTY3ZWE1Yzg0MDMxOWQ3MTg5NGM4N2Y2OTk0YyIsInZlciI6dHJ1ZSwiZnBpIjoiRlB5MEJ6eFZZUWJNLy96T1ArVnNQeTFSZUpFcWxnb1dmN2VPK2hYZzY3OD0iLCJmcGEiOiJQa3ZwODJTbVlUT2pOZUtrenJIK2V3OUhFV1NtN2NyL0QwajZycWZYT1dJPSIsImRvYiI6InV5Z0E1L28rVkM2TzFDSW1rQmNURWc9PSIsInByaSI6ImI2MDU5NWM1MTFlMDRkODFiNWRkOTVlZTQ1MTc5NGQ4IiwiaW5zIjoiZmQwZGUwMmQ3MDZmNDNkMDk0YjQ0MWM4YmFlZDY5OWYiLCJkdHkiOjExLCJhZ2MiOjMsImFwYyI6NX0.QWZYAvNcg4pCuOhW2cyck8bITmH0CpVpnAdYOOBypKY"

// invalid JWT token
var BadJWT = "deadbeef"

type MockAPI struct {
	ctrl        *gomock.Controller
	ds          *store.MockDataStoreInterface
	rc          *cache.MockRedisCacheInterface
	redisClient *redis.ClusterClient
	redisMock   redismock.ClusterClientMock
	api         *SocialPublicAPI
	//apiPriv     *pApi.SocialPrivateAPI
	//apiTrusted  *tApi.SocialTrustedAPI
	testTimeout time.Duration
	tele        *telemetry.MockTelemetryInterface
	id          *identity.MockIdentityInterface
}

func NewMockAPI(t *testing.T) *MockAPI {
	mockCtrl := gomock.NewController(t)
	client, redisMock := redismock.NewClusterMock()

	localds := store.NewMockDataStoreInterface(mockCtrl)
	localrc := cache.NewMockRedisCacheInterface(mockCtrl)
	localtl := telemetry.NewMockTelemetryInterface(mockCtrl)
	localid := identity.NewMockIdentityInterface(mockCtrl)

	localapi := NewMockSocialPublicAPI(cfg, localds, localrc, localtl, localid)
	//apiPriv := NewMockSocialPrivateAPI(cfg, localds, localrc, localid)
	//apiTrusted := NewMockSocialTrustedAPI(localapi)

	return &MockAPI{
		ctrl:        mockCtrl,
		ds:          localds,
		redisClient: client,
		redisMock:   redisMock,
		rc:          localrc,
		api:         localapi,
		//apiPriv:     apiPriv,
		//apiTrusted:  apiTrusted,
		testTimeout: time.Second * 30,
		tele:        localtl,
		id:          localid,
	}
}

func TestMain(m *testing.M) {
	setup()
	code := m.Run()
	teardown()
	os.Exit(code)
}

func Login(jwt string) (*httptest.ResponseRecorder, *http.Request) {

	ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{})

	req, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
	req.Header.Add("Authorization", "Bearer "+jwt)
	w := httptest.NewRecorder()
	return w, req
}

func AddBodyToRequest(val interface{}, jwt string) (*httptest.ResponseRecorder, *http.Request) {
	ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{})
	reqBodyBytes := new(bytes.Buffer)
	json.NewEncoder(reqBodyBytes).Encode(val)
	r := reqBodyBytes
	req, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", r)
	req.Header.Add("Authorization", "Bearer "+jwt)
	req.Header.Add(constants.KContentType, constants.KApplicationJson)
	w := httptest.NewRecorder()

	return w, req
}

func CreateRequestWithApplicationID(method string, val interface{}, appID string) (*httptest.ResponseRecorder, *http.Request) {
	ctx = context.WithValue(ctx, apipub.BearerAuthScopes, []string{})
	reqBodyBytes := new(bytes.Buffer)
	json.NewEncoder(reqBodyBytes).Encode(val)
	r := reqBodyBytes
	req, _ := http.NewRequestWithContext(ctx, method, "http://example.com/foo", r)
	req.Header.Add("Authorization", "Application "+appID)
	req.Header.Add(constants.KContentType, constants.KApplicationJson)
	w := httptest.NewRecorder()

	return w, req
}

func setup() {
	cfg = config.ConfigForTests()
	sm = health.NewServiceMonitor("test-social", cfg.Version.Version)
	id = identity.NewIdentity(ctx, cfg, sm)
	t = telemetry.NewTelemetry(cfg, nil)
	rc = cache.NewRedisCache(ctx, cfg, t, id)
	ds = store.NewTestDataStore(ctx, cfg)
	testSns = notification.SNS{}
	pubApi = NewSocialPublicAPI(cfg, ds, rc, sm, t, id, testSns)
	//privApi = pApi.NewSocialPrivateAPI(cfg, ds, rc, sm, t, id)

	// disable log
	zerolog.SetGlobalLevel(zerolog.FatalLevel)
}

func teardown() {
}

// NewMockSocialPublicAPI create social pubApi object
func NewMockSocialPublicAPI(cfg *config.Config, ds store.DataStoreInterface, rc cache.RedisCacheInterface, t telemetry.TelemetryInterface, id identity.IdentityInterface) *SocialPublicAPI {
	cfg.SkipAuth = true
	return &SocialPublicAPI{
		Cfg:        cfg,
		Ds:         ds,
		Cache:      rc,
		Tele:       t,
		Id:         id,
		HttpClient: http.DefaultClient,
	}
}

//// NewMockSocialPrivateAPI create social pubApi object
//func NewMockSocialPrivateAPI(cfg *config.Config, ds store.DataStoreInterface, rc cache.RedisCacheInterface, id identity.IdentityInterface) *pApi.SocialPrivateAPI {
//	cfg.SkipAuth = true
//	return &pApi.SocialPrivateAPI{
//		Cfg:        cfg,
//		Ds:         ds,
//		Id:         id,
//		Cache:      rc,
//		HttpClient: http.DefaultClient,
//	}
//}
//

// type mockIdentity struct{}

// func (id *mockIdentity) CreateFullAccount(clientID string, request *identity.CreateFullAccountRequest, additionalHeaders map[string]string) error {
// 	return nil
// }
// func (id *mockIdentity) ExchangeAuthCode(tokenRequest *identity.TokenRequest, clientID, clientSecret string) (*identity.TokenResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) ExchangeAuthCodeWithApp(tokenRequest *identity.TokenRequest, clientID string) (*identity.TokenResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) ExchangeDeviceCodeForToken(clientID, deviceCode, grantType string) (*identity.TokenResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) FetchValidationKeys() (jwk.set, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetAccount(accountID string) (*identity.Account, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetAccountsMe(bearer string) (*identity.AccountsMeResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetAppLegalManifests(clientID string) ([]identity.LegalManifest, error) {
// 	return []identity.LegalManifest{}, nil
// }
// func (id *mockIdentity) GetApplication(appID string) (*identity.Application, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetAuthCode(authCode *identity.AuthCodeRequest) (*identity.AuthCodeResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetHealth() error {
// 	return nil
// }
// func (id *mockIdentity) GetLegalDocument(clientID, documentID string) (*identity.LegalDocumentResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetLinksMe(bearer string) ([]identity.LinksMeResponse, error) {
// 	return []identity.LinksMeResponse{}, nil
// }
// func (id *mockIdentity) GetToken(tokenRequest *identity.TokenRequest) (*identity.TokenResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetTokenWithRefreshToken(tokenRequest *identity.TokenRequest, appID string) (*identity.TokenResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) GetUserLegalManifests(accountID string) ([]identity.UserLegalResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) LinkPlatformID(bearer, platform, platformID string) error {
// 	return nil
// }
// func (id *mockIdentity) LinkFullToPlatformAccount(fullAccountJWT string, platformAccountJWT string) error {
// 	return nil
// }
// func (id *mockIdentity) LoginWithExternalID(externalID, platform string) (*identity.TokenResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) RequestDeviceCode(clientID string) (*identity.RequestDeviceCodeResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) RequestUpdatePendingEmail(accountID string, request *identity.UpdatePendingEmailRequest) error {
// 	return nil
// }
// func (id *mockIdentity) RevokeToken(bearer string) error {
// 	return nil
// }
// func (id *mockIdentity) SendEmail(emailRequest *identity.EmailRequest) error {
// 	return nil
// }
// func (id *mockIdentity) UnlinkPlatformAccount(bearer, platform string) error {
// 	return nil
// }
// func (id *mockIdentity) UpdateAccount(bearer string, request *identity.AccountUpdateRequest) error {
// 	return nil
// }
// func (id *mockIdentity) UpdateDeviceAuthStatus(request *identity.UpdateDeviceAuthStatusRequest) error {
// 	return nil
// }
// func (id *mockIdentity) UpdateDisplayName(bearer, displayName string) error {
// 	return nil
// }
// func (id *mockIdentity) UpdateFullAccountLegalManifests(accountId string, legalResponse []identity.UserLegalResponse) error {
// 	return nil
// }
// func (id *mockIdentity) UpdatePassword(bearer, password string) error {
// 	return nil
// }
// func (id *mockIdentity) UpdateUserLegalManifests(bearer string, legalResponse []identity.UserLegalResponse) error {
// 	return nil
// }
// func (id *mockIdentity) ValidateDeviceAuthUserCode(userCode string) (*identity.ValidateDeviceAuthUserCodeResponse, error) {
// 	return nil, nil
// }
// func (id *mockIdentity) ValidateTokenSignature(bearer string) (bool, error) {
// 	return true, nil
// }
// func (id *mockIdentity) ValidateTokenStatus(tokenID string) (bool, error) {
// 	return true, nil
// }

// func NewMockIdentityClient() identity.Identity {
// 	return &mockIdentity{}
// }

func ErrorJson(httpCode, errorCode errs.SocialError) string {
	return fmt.Sprintf("{\"code\":%d,\"errorCode\":%d,\"message\":\"%s\"}\n", httpCode, errorCode, errs.ErrorString(errorCode))
}
