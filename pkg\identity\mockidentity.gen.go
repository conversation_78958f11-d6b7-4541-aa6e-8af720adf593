// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/identity/identity.go

// Package identity is a generated GoMock package.
package identity

import (
	context "context"
	reflect "reflect"

	authn "github.com/2kg-coretech/dna-common/pkg/authn"
	gomock "go.uber.org/mock/gomock"
	apipub "github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	health "github.com/take-two-t2gp/t2gp-social-service/pkg/health"
)

// MockIdentityServiceInterface is a mock of IdentityServiceInterface interface.
type MockIdentityServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockIdentityServiceInterfaceMockRecorder
}

// MockIdentityServiceInterfaceMockRecorder is the mock recorder for MockIdentityServiceInterface.
type MockIdentityServiceInterfaceMockRecorder struct {
	mock *MockIdentityServiceInterface
}

// NewMockIdentityServiceInterface creates a new mock instance.
func NewMockIdentityServiceInterface(ctrl *gomock.Controller) *MockIdentityServiceInterface {
	mock := &MockIdentityServiceInterface{ctrl: ctrl}
	mock.recorder = &MockIdentityServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdentityServiceInterface) EXPECT() *MockIdentityServiceInterfaceMockRecorder {
	return m.recorder
}

// Authenticate mocks base method.
func (m *MockIdentityServiceInterface) Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authenticate", ctx, authHeader, skipRevokedTokenValidation)
	ret0, _ := ret[0].(*authn.AuthenticationData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authenticate indicates an expected call of Authenticate.
func (mr *MockIdentityServiceInterfaceMockRecorder) Authenticate(ctx, authHeader, skipRevokedTokenValidation interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authenticate", reflect.TypeOf((*MockIdentityServiceInterface)(nil).Authenticate), ctx, authHeader, skipRevokedTokenValidation)
}

// CheckHealth mocks base method.
func (m *MockIdentityServiceInterface) CheckHealth() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHealth")
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckHealth indicates an expected call of CheckHealth.
func (mr *MockIdentityServiceInterfaceMockRecorder) CheckHealth() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHealth", reflect.TypeOf((*MockIdentityServiceInterface)(nil).CheckHealth))
}

// GetIdentityServiceStr mocks base method.
func (m *MockIdentityServiceInterface) GetIdentityServiceStr() IdentityService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityServiceStr")
	ret0, _ := ret[0].(IdentityService)
	return ret0
}

// GetIdentityServiceStr indicates an expected call of GetIdentityServiceStr.
func (mr *MockIdentityServiceInterfaceMockRecorder) GetIdentityServiceStr() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityServiceStr", reflect.TypeOf((*MockIdentityServiceInterface)(nil).GetIdentityServiceStr))
}

// GetProductIdFromAppId mocks base method.
func (m *MockIdentityServiceInterface) GetProductIdFromAppId(ctx context.Context, appId string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductIdFromAppId", ctx, appId)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductIdFromAppId indicates an expected call of GetProductIdFromAppId.
func (mr *MockIdentityServiceInterfaceMockRecorder) GetProductIdFromAppId(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductIdFromAppId", reflect.TypeOf((*MockIdentityServiceInterface)(nil).GetProductIdFromAppId), ctx, appId)
}

// GetUserProfileAccountLinks mocks base method.
func (m *MockIdentityServiceInterface) GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfileAccountLinks", ctx, userid)
	ret0, _ := ret[0].(*[]apipub.AccountLinkDNA)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfileAccountLinks indicates an expected call of GetUserProfileAccountLinks.
func (mr *MockIdentityServiceInterfaceMockRecorder) GetUserProfileAccountLinks(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfileAccountLinks", reflect.TypeOf((*MockIdentityServiceInterface)(nil).GetUserProfileAccountLinks), ctx, userid)
}

// IsCritical mocks base method.
func (m *MockIdentityServiceInterface) IsCritical() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCritical")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsCritical indicates an expected call of IsCritical.
func (mr *MockIdentityServiceInterfaceMockRecorder) IsCritical() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCritical", reflect.TypeOf((*MockIdentityServiceInterface)(nil).IsCritical))
}

// LastStatus mocks base method.
func (m *MockIdentityServiceInterface) LastStatus() *health.ServiceStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LastStatus")
	ret0, _ := ret[0].(*health.ServiceStatus)
	return ret0
}

// LastStatus indicates an expected call of LastStatus.
func (mr *MockIdentityServiceInterfaceMockRecorder) LastStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LastStatus", reflect.TypeOf((*MockIdentityServiceInterface)(nil).LastStatus))
}

// Login mocks base method.
func (m *MockIdentityServiceInterface) Login(ctx context.Context, username, password, local, appID string) (*apipub.LoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Login", ctx, username, password, local, appID)
	ret0, _ := ret[0].(*apipub.LoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockIdentityServiceInterfaceMockRecorder) Login(ctx, username, password, local, appID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockIdentityServiceInterface)(nil).Login), ctx, username, password, local, appID)
}

// Logout mocks base method.
func (m *MockIdentityServiceInterface) Logout(ctx context.Context, token string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Logout", ctx, token)
	ret0, _ := ret[0].(error)
	return ret0
}

// Logout indicates an expected call of Logout.
func (mr *MockIdentityServiceInterfaceMockRecorder) Logout(ctx, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logout", reflect.TypeOf((*MockIdentityServiceInterface)(nil).Logout), ctx, token)
}

// RefreshToken mocks base method.
func (m *MockIdentityServiceInterface) RefreshToken(ctx context.Context, refreshToken, locale string) (*apipub.LoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", ctx, refreshToken, locale)
	ret0, _ := ret[0].(*apipub.LoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockIdentityServiceInterfaceMockRecorder) RefreshToken(ctx, refreshToken, locale interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockIdentityServiceInterface)(nil).RefreshToken), ctx, refreshToken, locale)
}

// SearchAccounts mocks base method.
func (m *MockIdentityServiceInterface) SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAccounts", ctx, request)
	ret0, _ := ret[0].(*apipub.SearchAccountResponseList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAccounts indicates an expected call of SearchAccounts.
func (mr *MockIdentityServiceInterfaceMockRecorder) SearchAccounts(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAccounts", reflect.TypeOf((*MockIdentityServiceInterface)(nil).SearchAccounts), ctx, request)
}

// SearchAccountsByUserID mocks base method.
func (m *MockIdentityServiceInterface) SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAccountsByUserID", ctx, userid)
	ret0, _ := ret[0].(*apipub.SearchAccountResponseList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAccountsByUserID indicates an expected call of SearchAccountsByUserID.
func (mr *MockIdentityServiceInterfaceMockRecorder) SearchAccountsByUserID(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAccountsByUserID", reflect.TypeOf((*MockIdentityServiceInterface)(nil).SearchAccountsByUserID), ctx, userid)
}

// SetAgeGroupFromDob mocks base method.
func (m *MockIdentityServiceInterface) SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetAgeGroupFromDob", ctx, profile)
}

// SetAgeGroupFromDob indicates an expected call of SetAgeGroupFromDob.
func (mr *MockIdentityServiceInterfaceMockRecorder) SetAgeGroupFromDob(ctx, profile interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAgeGroupFromDob", reflect.TypeOf((*MockIdentityServiceInterface)(nil).SetAgeGroupFromDob), ctx, profile)
}

// SyncUserProfile mocks base method.
func (m *MockIdentityServiceInterface) SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncUserProfile", ctx, userid)
	ret0, _ := ret[0].(*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUserProfile indicates an expected call of SyncUserProfile.
func (mr *MockIdentityServiceInterfaceMockRecorder) SyncUserProfile(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUserProfile", reflect.TypeOf((*MockIdentityServiceInterface)(nil).SyncUserProfile), ctx, userid)
}

// SyncUserProfiles mocks base method.
func (m *MockIdentityServiceInterface) SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncUserProfiles", ctx, userids)
	ret0, _ := ret[0].(*[]*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUserProfiles indicates an expected call of SyncUserProfiles.
func (mr *MockIdentityServiceInterfaceMockRecorder) SyncUserProfiles(ctx, userids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUserProfiles", reflect.TypeOf((*MockIdentityServiceInterface)(nil).SyncUserProfiles), ctx, userids)
}

// UpdateConfig mocks base method.
func (m *MockIdentityServiceInterface) UpdateConfig(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfig", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfig indicates an expected call of UpdateConfig.
func (mr *MockIdentityServiceInterfaceMockRecorder) UpdateConfig(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfig", reflect.TypeOf((*MockIdentityServiceInterface)(nil).UpdateConfig), ctx)
}

// MockIdentityInterface is a mock of IdentityInterface interface.
type MockIdentityInterface struct {
	ctrl     *gomock.Controller
	recorder *MockIdentityInterfaceMockRecorder
}

// MockIdentityInterfaceMockRecorder is the mock recorder for MockIdentityInterface.
type MockIdentityInterfaceMockRecorder struct {
	mock *MockIdentityInterface
}

// NewMockIdentityInterface creates a new mock instance.
func NewMockIdentityInterface(ctrl *gomock.Controller) *MockIdentityInterface {
	mock := &MockIdentityInterface{ctrl: ctrl}
	mock.recorder = &MockIdentityInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdentityInterface) EXPECT() *MockIdentityInterfaceMockRecorder {
	return m.recorder
}

// Authenticate mocks base method.
func (m *MockIdentityInterface) Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Authenticate", ctx, authHeader, skipRevokedTokenValidation)
	ret0, _ := ret[0].(*authn.AuthenticationData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Authenticate indicates an expected call of Authenticate.
func (mr *MockIdentityInterfaceMockRecorder) Authenticate(ctx, authHeader, skipRevokedTokenValidation interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Authenticate", reflect.TypeOf((*MockIdentityInterface)(nil).Authenticate), ctx, authHeader, skipRevokedTokenValidation)
}

// GetIdentityService mocks base method.
func (m *MockIdentityInterface) GetIdentityService(providerType IdentityService) IdentityServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityService", providerType)
	ret0, _ := ret[0].(IdentityServiceInterface)
	return ret0
}

// GetIdentityService indicates an expected call of GetIdentityService.
func (mr *MockIdentityInterfaceMockRecorder) GetIdentityService(providerType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityService", reflect.TypeOf((*MockIdentityInterface)(nil).GetIdentityService), providerType)
}

// GetIdentityServiceFromContext mocks base method.
func (m *MockIdentityInterface) GetIdentityServiceFromContext(ctx context.Context) IdentityServiceInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityServiceFromContext", ctx)
	ret0, _ := ret[0].(IdentityServiceInterface)
	return ret0
}

// GetIdentityServiceFromContext indicates an expected call of GetIdentityServiceFromContext.
func (mr *MockIdentityInterfaceMockRecorder) GetIdentityServiceFromContext(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityServiceFromContext", reflect.TypeOf((*MockIdentityInterface)(nil).GetIdentityServiceFromContext), ctx)
}

// GetIdentityServiceStr mocks base method.
func (m *MockIdentityInterface) GetIdentityServiceStr(ctx context.Context) IdentityService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIdentityServiceStr", ctx)
	ret0, _ := ret[0].(IdentityService)
	return ret0
}

// GetIdentityServiceStr indicates an expected call of GetIdentityServiceStr.
func (mr *MockIdentityInterfaceMockRecorder) GetIdentityServiceStr(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIdentityServiceStr", reflect.TypeOf((*MockIdentityInterface)(nil).GetIdentityServiceStr), ctx)
}

// GetProductIdFromAppId mocks base method.
func (m *MockIdentityInterface) GetProductIdFromAppId(ctx context.Context, appId string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductIdFromAppId", ctx, appId)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductIdFromAppId indicates an expected call of GetProductIdFromAppId.
func (mr *MockIdentityInterfaceMockRecorder) GetProductIdFromAppId(ctx, appId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductIdFromAppId", reflect.TypeOf((*MockIdentityInterface)(nil).GetProductIdFromAppId), ctx, appId)
}

// GetUserProfileAccountLinks mocks base method.
func (m *MockIdentityInterface) GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfileAccountLinks", ctx, userid)
	ret0, _ := ret[0].(*[]apipub.AccountLinkDNA)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfileAccountLinks indicates an expected call of GetUserProfileAccountLinks.
func (mr *MockIdentityInterfaceMockRecorder) GetUserProfileAccountLinks(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfileAccountLinks", reflect.TypeOf((*MockIdentityInterface)(nil).GetUserProfileAccountLinks), ctx, userid)
}

// Login mocks base method.
func (m *MockIdentityInterface) Login(ctx context.Context, username, password, local, appID string) (*apipub.LoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Login", ctx, username, password, local, appID)
	ret0, _ := ret[0].(*apipub.LoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Login indicates an expected call of Login.
func (mr *MockIdentityInterfaceMockRecorder) Login(ctx, username, password, local, appID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Login", reflect.TypeOf((*MockIdentityInterface)(nil).Login), ctx, username, password, local, appID)
}

// Logout mocks base method.
func (m *MockIdentityInterface) Logout(ctx context.Context, token string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Logout", ctx, token)
	ret0, _ := ret[0].(error)
	return ret0
}

// Logout indicates an expected call of Logout.
func (mr *MockIdentityInterfaceMockRecorder) Logout(ctx, token interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Logout", reflect.TypeOf((*MockIdentityInterface)(nil).Logout), ctx, token)
}

// RefreshToken mocks base method.
func (m *MockIdentityInterface) RefreshToken(ctx context.Context, refreshToken, locale string) (*apipub.LoginResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshToken", ctx, refreshToken, locale)
	ret0, _ := ret[0].(*apipub.LoginResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RefreshToken indicates an expected call of RefreshToken.
func (mr *MockIdentityInterfaceMockRecorder) RefreshToken(ctx, refreshToken, locale interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshToken", reflect.TypeOf((*MockIdentityInterface)(nil).RefreshToken), ctx, refreshToken, locale)
}

// SearchAccounts mocks base method.
func (m *MockIdentityInterface) SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAccounts", ctx, request)
	ret0, _ := ret[0].(*apipub.SearchAccountResponseList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAccounts indicates an expected call of SearchAccounts.
func (mr *MockIdentityInterfaceMockRecorder) SearchAccounts(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAccounts", reflect.TypeOf((*MockIdentityInterface)(nil).SearchAccounts), ctx, request)
}

// SearchAccountsByUserID mocks base method.
func (m *MockIdentityInterface) SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SearchAccountsByUserID", ctx, userid)
	ret0, _ := ret[0].(*apipub.SearchAccountResponseList)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SearchAccountsByUserID indicates an expected call of SearchAccountsByUserID.
func (mr *MockIdentityInterfaceMockRecorder) SearchAccountsByUserID(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SearchAccountsByUserID", reflect.TypeOf((*MockIdentityInterface)(nil).SearchAccountsByUserID), ctx, userid)
}

// SetAgeGroupFromDob mocks base method.
func (m *MockIdentityInterface) SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetAgeGroupFromDob", ctx, profile)
}

// SetAgeGroupFromDob indicates an expected call of SetAgeGroupFromDob.
func (mr *MockIdentityInterfaceMockRecorder) SetAgeGroupFromDob(ctx, profile interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAgeGroupFromDob", reflect.TypeOf((*MockIdentityInterface)(nil).SetAgeGroupFromDob), ctx, profile)
}

// SyncUserProfile mocks base method.
func (m *MockIdentityInterface) SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncUserProfile", ctx, userid)
	ret0, _ := ret[0].(*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUserProfile indicates an expected call of SyncUserProfile.
func (mr *MockIdentityInterfaceMockRecorder) SyncUserProfile(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUserProfile", reflect.TypeOf((*MockIdentityInterface)(nil).SyncUserProfile), ctx, userid)
}

// SyncUserProfiles mocks base method.
func (m *MockIdentityInterface) SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncUserProfiles", ctx, userids)
	ret0, _ := ret[0].(*[]*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SyncUserProfiles indicates an expected call of SyncUserProfiles.
func (mr *MockIdentityInterfaceMockRecorder) SyncUserProfiles(ctx, userids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncUserProfiles", reflect.TypeOf((*MockIdentityInterface)(nil).SyncUserProfiles), ctx, userids)
}
