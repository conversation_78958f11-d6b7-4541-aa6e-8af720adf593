package cache

import (
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func Test_Group(t *testing.T) {
	g := goblin.Goblin(t)

	var productid, userid1, userid2, groupid, groupid2 string
	var group, group2, retGroup *apipub.GroupResponse
	var members *[]apipub.GroupMemberResponse
	var err error

	g.Describe("set get delete group", func() {
		g.Before(func() {
			productid = utils.GenerateRandomDNAID()
			userid1 = utils.GenerateRandomDNAID()
			userid2 = utils.GenerateRandomDNAID()
			groupid = utils.GenerateRandomDNAID()
			groupid2 = utils.GenerateRandomDNAID()

			members = &[]apipub.GroupMemberResponse{
				{
					Userid:    userid1,
					Productid: productid,
					Role:      "leader",
				},
				{
					Userid:    userid2,
					Productid: productid,
					Role:      "member",
				},
			}

			group = &apipub.GroupResponse{
				Productid: productid,
				Members:   members,
				Groupid:   groupid,
			}
			group2 = &apipub.GroupResponse{
				Productid: productid,
				Members:   members,
				Groupid:   groupid2,
			}
		})
		g.It("set user group", func() {
			err = rc.SetGroup(ctx, group, time.Duration(60)*time.Second)
			g.Assert(err).IsNil()

			err = rc.SetUserGroupIdxs(ctx, userid1, group)
			g.Assert(err).IsNil()

			err = rc.SetGroup(ctx, group2, time.Duration(60)*time.Second)
			g.Assert(err).IsNil()

			err = rc.SetUserGroupIdxs(ctx, userid1, group2)
			g.Assert(err).IsNil()
		})

		g.It("get group ", func() {
			retGroup, err = rc.GetGroup(ctx, productid, groupid)
			g.Assert(err).IsNil()
			g.Assert(retGroup).IsNotNil()

			limit := int64(10)
			next := ""
			retGroups, next, err := rc.GetUserGroups(ctx, userid1, productid, &limit, &next)
			g.Assert(err).IsNil()
			g.Assert(retGroups).IsNotNil()
			g.Assert(len(*retGroups)).Equal(2)
			g.Assert(next).Equal("")

			err = rc.DeleteCachedObj(ctx, group.RedisKey("test"))
			g.Assert(err).IsNil()
			err = rc.DeleteCachedObj(ctx, group2.RedisKey("test"))
			g.Assert(err).IsNil()
		})

		g.It("del user group ", func() {
			err = rc.DeleteUserGroupIdxs(ctx, group.Productid, group.Groupid, userid1)
			g.Assert(err).IsNil()
			err = rc.DeleteUserGroupIdxs(ctx, group2.Productid, group2.Groupid, userid1)
			g.Assert(err).IsNil()
		})
	})
}

func TestKickOrLeaveHelper(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockRC(t)
	var productid, userid1, userid2, groupid string
	var group *apipub.GroupResponse
	var members *[]apipub.GroupMemberResponse

	g.Describe("Test KickOrLeaveHelper", func() {
		g.Before(func() {
			productid = "1"
			userid1 = "2"
			userid2 = "3"
			groupid = "4"
			members = &[]apipub.GroupMemberResponse{
				{
					Userid:    userid1,
					Productid: productid,
					Role:      "leader",
				},
				{
					Userid:    userid2,
					Productid: productid,
					Role:      "member",
				},
			}

			group = &apipub.GroupResponse{
				Productid:  productid,
				Members:    members,
				Groupid:    groupid,
				MaxMembers: 5,
			}
		})
		g.BeforeEach(func() {
			members = &[]apipub.GroupMemberResponse{
				{
					Userid:    userid1,
					Productid: productid,
					Role:      "leader",
				},
				{
					Userid:    userid2,
					Productid: productid,
					Role:      "member",
				},
			}
			group = &apipub.GroupResponse{
				Productid:  productid,
				Members:    members,
				Groupid:    groupid,
				MaxMembers: 5,
			}
			rc.SetGroup(ctx, group, time.Duration(1000)*time.Second)
		})
		g.It("kick out a member", func() {
			g.Timeout(time.Duration(mock.testTimeout))
			_, _, err := rc.KickOrLeaveHelper(ctx, group, userid1, userid2, nil)
			g.Assert(err).IsNil()

			res, _ := rc.GetGroup(ctx, productid, groupid)
			g.Assert(len(*res.Members)).Equal(1)
			g.Assert((*res.Members)[0].Userid).Equal(userid1)
		})
		g.It("kick out a member with a customized reason", func() {
			g.Timeout(time.Duration(mock.testTimeout))
			reason := "I don't like you"
			_, _, err := rc.KickOrLeaveHelper(ctx, group, userid1, userid2, &reason)
			g.Assert(err).IsNil()

			res, _ := rc.GetGroup(ctx, productid, groupid)
			g.Assert(len(*res.Members)).Equal(1)
			g.Assert((*res.Members)[0].Userid).Equal(userid1)
		})
		g.It("group leader leaves", func() {
			g.Timeout(time.Duration(mock.testTimeout))
			_, _, err := rc.KickOrLeaveHelper(ctx, group, userid1, userid1, nil)
			g.Assert(err).IsNil()

			res, _ := rc.GetGroup(ctx, productid, groupid)
			g.Assert(res).IsNotNil()
			g.Assert(len(*res.Members)).Equal(1)
			g.Assert((*res.Members)[0].Userid).Equal(userid2)
		})
		g.It("group gets disbanded if empty", func() {
			g.Timeout(time.Duration(mock.testTimeout))
			_, _, err := rc.KickOrLeaveHelper(ctx, group, userid1, userid2, nil)
			g.Assert(err).IsNil()
			time.Sleep(time.Second)
			res, _ := rc.GetGroup(ctx, productid, groupid)
			g.Assert(res).IsNotNil()
			g.Assert(len(*res.Members)).Equal(1)

			time.Sleep(time.Second)
			_, _, err = rc.KickOrLeaveHelper(ctx, group, userid1, userid1, nil)
			g.Assert(err).IsNil()
			time.Sleep(time.Second)
			res, _ = rc.GetGroup(ctx, productid, groupid)
			g.Assert(res).IsNil()
		})
	})
}
