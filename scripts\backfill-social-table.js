const { DynamoDBClient, ScanCommand, PutItemCommand } = require("@aws-sdk/client-dynamodb");
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

let ddbClient;

async function PutItem(item, tableName) {
    const putCmd = {
        Item: item,
        TableName: tableName
    };

    //console.log(JSON.stringify(putCmd, null, 2));
    while (true) {
        try {
            await ddbClient.send(new PutItemCommand(putCmd));
            break;
        } catch (ex) {
            console.error(ex)
            await sleep(2000);
        }
    }
    //console.log(JSON.stringify(response, null, 2));
}

function sleep(t) {
    return new Promise(function(resolve) {
        setTimeout(resolve, t);
    });
}

async function main() {
    const args = yargs(hideBin(process.argv))
        .option('source-table-name', {
            type: 'string',
            describe: 'source table name',
            type: 'string',
            required: true,
        })
        .option('destination-table-name', {
            type: 'string',
            describe: 'destination table name',
            type: 'string',
            required: true,
        })
        .option('region', {
            type: 'string',
            describe: 'AWS region',
            type: 'string',
            default: 'us-east-1',
        })
        .option('force', {
            type: 'string',
            describe: 'force production backfill',
            type: 'boolean',
            default: false,
        })
        .help().argv; 

    if (!args.force && args.sourceTableName.match(/(prod|prd)/)) {
        throw new Error(`Cannot backfill table ${args.sourceTableName} without --force`);
    }

    ddbClient = new DynamoDBClient({ region: args.region});

    const scan = {
        TableName: args.sourceTableName,
        Limit: 100
    }
    let done = false;
    let iterCounter = 1;
    let startKey = undefined;
    console.log(`Begin backfill ${args.destinationTableName} from table ${args.sourceTableName}`);
    while (!done) {
        console.log(`Iteration ${iterCounter}`);
        scan.ExclusiveStartKey = startKey;
        const response = await ddbClient.send(new ScanCommand(scan));

        console.log(`  Table: ${args.sourceTableName} item count: ${response.Items.length}`);

        response.Items.forEach(item => {
            PutItem(item, args.destinationTableName);
        });

        startKey = response.LastEvaluatedKey;
        done = startKey == undefined;
        iterCounter++;
    }
}
main();