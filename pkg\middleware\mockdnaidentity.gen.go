// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/middleware/basicauth_test.go

// Package middleware is a generated GoMock package.
package middleware

import (
	context "context"
	reflect "reflect"

	identity "github.com/2kg-coretech/dna-common/pkg/identity"
	gomock "github.com/golang/mock/gomock"
	jwk "github.com/lestrrat-go/jwx/jwk"
)

// MockIdentity is a mock of Identity interface.
type MockIdentity struct {
	ctrl     *gomock.Controller
	recorder *MockIdentityMockRecorder
}

// MockIdentityMockRecorder is the mock recorder for MockIdentity.
type MockIdentityMockRecorder struct {
	mock *MockIdentity
}

// NewMockIdentity creates a new mock instance.
func NewMockIdentity(ctrl *gomock.Controller) *MockIdentity {
	mock := &MockIdentity{ctrl: ctrl}
	mock.recorder = &MockIdentityMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIdentity) EXPECT() *MockIdentityMockRecorder {
	return m.recorder
}

// CheckChildAccount mocks base method.
func (m *MockIdentity) CheckChildAccount(accountID string) (*identity.AccountDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChildAccount", accountID)
	ret0, _ := ret[0].(*identity.AccountDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChildAccount indicates an expected call of CheckChildAccount.
func (mr *MockIdentityMockRecorder) CheckChildAccount(accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChildAccount", reflect.TypeOf((*MockIdentity)(nil).CheckChildAccount), accountID)
}

// CheckChildAccountWithContext mocks base method.
func (m *MockIdentity) CheckChildAccountWithContext(ctx context.Context, accountID string) (*identity.AccountDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckChildAccountWithContext", ctx, accountID)
	ret0, _ := ret[0].(*identity.AccountDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckChildAccountWithContext indicates an expected call of CheckChildAccountWithContext.
func (mr *MockIdentityMockRecorder) CheckChildAccountWithContext(ctx, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckChildAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).CheckChildAccountWithContext), ctx, accountID)
}

// CreateFullAccount mocks base method.
func (m *MockIdentity) CreateFullAccount(clientID string, request *identity.CreateFullAccountRequest, additionalHeaders map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFullAccount", clientID, request, additionalHeaders)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateFullAccount indicates an expected call of CreateFullAccount.
func (mr *MockIdentityMockRecorder) CreateFullAccount(clientID, request, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFullAccount", reflect.TypeOf((*MockIdentity)(nil).CreateFullAccount), clientID, request, additionalHeaders)
}

// CreateFullAccountWithContext mocks base method.
func (m *MockIdentity) CreateFullAccountWithContext(ctx context.Context, clientID string, request *identity.CreateFullAccountRequest, additionalHeaders map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateFullAccountWithContext", ctx, clientID, request, additionalHeaders)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateFullAccountWithContext indicates an expected call of CreateFullAccountWithContext.
func (mr *MockIdentityMockRecorder) CreateFullAccountWithContext(ctx, clientID, request, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFullAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).CreateFullAccountWithContext), ctx, clientID, request, additionalHeaders)
}

// DeleteAccountsMe mocks base method.
func (m *MockIdentity) DeleteAccountsMe(bearer string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAccountsMe", bearer)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAccountsMe indicates an expected call of DeleteAccountsMe.
func (mr *MockIdentityMockRecorder) DeleteAccountsMe(bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAccountsMe", reflect.TypeOf((*MockIdentity)(nil).DeleteAccountsMe), bearer)
}

// DeleteAccountsMeWithContext mocks base method.
func (m *MockIdentity) DeleteAccountsMeWithContext(ctx context.Context, bearer string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAccountsMeWithContext", ctx, bearer)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAccountsMeWithContext indicates an expected call of DeleteAccountsMeWithContext.
func (mr *MockIdentityMockRecorder) DeleteAccountsMeWithContext(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAccountsMeWithContext", reflect.TypeOf((*MockIdentity)(nil).DeleteAccountsMeWithContext), ctx, bearer)
}

// DeletePendingEmail mocks base method.
func (m *MockIdentity) DeletePendingEmail(accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePendingEmail", accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePendingEmail indicates an expected call of DeletePendingEmail.
func (mr *MockIdentityMockRecorder) DeletePendingEmail(accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePendingEmail", reflect.TypeOf((*MockIdentity)(nil).DeletePendingEmail), accountID)
}

// DeletePendingEmailWithContext mocks base method.
func (m *MockIdentity) DeletePendingEmailWithContext(ctx context.Context, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePendingEmailWithContext", ctx, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePendingEmailWithContext indicates an expected call of DeletePendingEmailWithContext.
func (mr *MockIdentityMockRecorder) DeletePendingEmailWithContext(ctx, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePendingEmailWithContext", reflect.TypeOf((*MockIdentity)(nil).DeletePendingEmailWithContext), ctx, accountID)
}

// ExchangeAuthCode mocks base method.
func (m *MockIdentity) ExchangeAuthCode(tokenRequest *identity.TokenRequest, clientID, clientSecret string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeAuthCode", tokenRequest, clientID, clientSecret, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeAuthCode indicates an expected call of ExchangeAuthCode.
func (mr *MockIdentityMockRecorder) ExchangeAuthCode(tokenRequest, clientID, clientSecret, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeAuthCode", reflect.TypeOf((*MockIdentity)(nil).ExchangeAuthCode), tokenRequest, clientID, clientSecret, additionalHeaders)
}

// ExchangeAuthCodeWithApp mocks base method.
func (m *MockIdentity) ExchangeAuthCodeWithApp(tokenRequest *identity.TokenRequest, clientID string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeAuthCodeWithApp", tokenRequest, clientID, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeAuthCodeWithApp indicates an expected call of ExchangeAuthCodeWithApp.
func (mr *MockIdentityMockRecorder) ExchangeAuthCodeWithApp(tokenRequest, clientID, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeAuthCodeWithApp", reflect.TypeOf((*MockIdentity)(nil).ExchangeAuthCodeWithApp), tokenRequest, clientID, additionalHeaders)
}

// ExchangeAuthCodeWithAppAndContext mocks base method.
func (m *MockIdentity) ExchangeAuthCodeWithAppAndContext(ctx context.Context, tokenRequest *identity.TokenRequest, clientID string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeAuthCodeWithAppAndContext", ctx, tokenRequest, clientID, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeAuthCodeWithAppAndContext indicates an expected call of ExchangeAuthCodeWithAppAndContext.
func (mr *MockIdentityMockRecorder) ExchangeAuthCodeWithAppAndContext(ctx, tokenRequest, clientID, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeAuthCodeWithAppAndContext", reflect.TypeOf((*MockIdentity)(nil).ExchangeAuthCodeWithAppAndContext), ctx, tokenRequest, clientID, additionalHeaders)
}

// ExchangeAuthCodeWithContext mocks base method.
func (m *MockIdentity) ExchangeAuthCodeWithContext(ctx context.Context, tokenRequest *identity.TokenRequest, clientID, clientSecret string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeAuthCodeWithContext", ctx, tokenRequest, clientID, clientSecret, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeAuthCodeWithContext indicates an expected call of ExchangeAuthCodeWithContext.
func (mr *MockIdentityMockRecorder) ExchangeAuthCodeWithContext(ctx, tokenRequest, clientID, clientSecret, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeAuthCodeWithContext", reflect.TypeOf((*MockIdentity)(nil).ExchangeAuthCodeWithContext), ctx, tokenRequest, clientID, clientSecret, additionalHeaders)
}

// ExchangeDeviceCodeForToken mocks base method.
func (m *MockIdentity) ExchangeDeviceCodeForToken(clientID, deviceCode, grantType string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeDeviceCodeForToken", clientID, deviceCode, grantType, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeDeviceCodeForToken indicates an expected call of ExchangeDeviceCodeForToken.
func (mr *MockIdentityMockRecorder) ExchangeDeviceCodeForToken(clientID, deviceCode, grantType, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeDeviceCodeForToken", reflect.TypeOf((*MockIdentity)(nil).ExchangeDeviceCodeForToken), clientID, deviceCode, grantType, additionalHeaders)
}

// ExchangeDeviceCodeForTokenWithContext mocks base method.
func (m *MockIdentity) ExchangeDeviceCodeForTokenWithContext(ctx context.Context, clientID, deviceCode string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExchangeDeviceCodeForTokenWithContext", ctx, clientID, deviceCode, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ExchangeDeviceCodeForTokenWithContext indicates an expected call of ExchangeDeviceCodeForTokenWithContext.
func (mr *MockIdentityMockRecorder) ExchangeDeviceCodeForTokenWithContext(ctx, clientID, deviceCode, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExchangeDeviceCodeForTokenWithContext", reflect.TypeOf((*MockIdentity)(nil).ExchangeDeviceCodeForTokenWithContext), ctx, clientID, deviceCode, additionalHeaders)
}

// FetchValidationKeys mocks base method.
func (m *MockIdentity) FetchValidationKeys(kid string) (jwk.Set, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchValidationKeys", kid)
	ret0, _ := ret[0].(jwk.Set)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchValidationKeys indicates an expected call of FetchValidationKeys.
func (mr *MockIdentityMockRecorder) FetchValidationKeys(kid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchValidationKeys", reflect.TypeOf((*MockIdentity)(nil).FetchValidationKeys), kid)
}

// FetchValidationKeysWithContext mocks base method.
func (m *MockIdentity) FetchValidationKeysWithContext(ctx context.Context, kid string) (jwk.Set, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchValidationKeysWithContext", ctx, kid)
	ret0, _ := ret[0].(jwk.Set)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchValidationKeysWithContext indicates an expected call of FetchValidationKeysWithContext.
func (mr *MockIdentityMockRecorder) FetchValidationKeysWithContext(ctx, kid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchValidationKeysWithContext", reflect.TypeOf((*MockIdentity)(nil).FetchValidationKeysWithContext), ctx, kid)
}

// FlagAccountDeletion mocks base method.
func (m *MockIdentity) FlagAccountDeletion(accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlagAccountDeletion", accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// FlagAccountDeletion indicates an expected call of FlagAccountDeletion.
func (mr *MockIdentityMockRecorder) FlagAccountDeletion(accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlagAccountDeletion", reflect.TypeOf((*MockIdentity)(nil).FlagAccountDeletion), accountID)
}

// FlagAccountDeletionWithContext mocks base method.
func (m *MockIdentity) FlagAccountDeletionWithContext(ctx context.Context, accountID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlagAccountDeletionWithContext", ctx, accountID)
	ret0, _ := ret[0].(error)
	return ret0
}

// FlagAccountDeletionWithContext indicates an expected call of FlagAccountDeletionWithContext.
func (mr *MockIdentityMockRecorder) FlagAccountDeletionWithContext(ctx, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlagAccountDeletionWithContext", reflect.TypeOf((*MockIdentity)(nil).FlagAccountDeletionWithContext), ctx, accountID)
}

// GetAccount mocks base method.
func (m *MockIdentity) GetAccount(accountID string) (*identity.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccount", accountID)
	ret0, _ := ret[0].(*identity.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccount indicates an expected call of GetAccount.
func (mr *MockIdentityMockRecorder) GetAccount(accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccount", reflect.TypeOf((*MockIdentity)(nil).GetAccount), accountID)
}

// GetAccountDetailsMe mocks base method.
func (m *MockIdentity) GetAccountDetailsMe(bearer string) (*identity.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountDetailsMe", bearer)
	ret0, _ := ret[0].(*identity.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountDetailsMe indicates an expected call of GetAccountDetailsMe.
func (mr *MockIdentityMockRecorder) GetAccountDetailsMe(bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountDetailsMe", reflect.TypeOf((*MockIdentity)(nil).GetAccountDetailsMe), bearer)
}

// GetAccountDetailsMeWithContext mocks base method.
func (m *MockIdentity) GetAccountDetailsMeWithContext(ctx context.Context, bearer string) (*identity.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountDetailsMeWithContext", ctx, bearer)
	ret0, _ := ret[0].(*identity.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountDetailsMeWithContext indicates an expected call of GetAccountDetailsMeWithContext.
func (mr *MockIdentityMockRecorder) GetAccountDetailsMeWithContext(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountDetailsMeWithContext", reflect.TypeOf((*MockIdentity)(nil).GetAccountDetailsMeWithContext), ctx, bearer)
}

// GetAccountLegalManifests mocks base method.
func (m *MockIdentity) GetAccountLegalManifests(appID, accountID string) ([]identity.LegalManifest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountLegalManifests", appID, accountID)
	ret0, _ := ret[0].([]identity.LegalManifest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountLegalManifests indicates an expected call of GetAccountLegalManifests.
func (mr *MockIdentityMockRecorder) GetAccountLegalManifests(appID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountLegalManifests", reflect.TypeOf((*MockIdentity)(nil).GetAccountLegalManifests), appID, accountID)
}

// GetAccountLegalManifestsWithContext mocks base method.
func (m *MockIdentity) GetAccountLegalManifestsWithContext(ctx context.Context, appID, accountID string) ([]identity.LegalManifest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountLegalManifestsWithContext", ctx, appID, accountID)
	ret0, _ := ret[0].([]identity.LegalManifest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountLegalManifestsWithContext indicates an expected call of GetAccountLegalManifestsWithContext.
func (mr *MockIdentityMockRecorder) GetAccountLegalManifestsWithContext(ctx, appID, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountLegalManifestsWithContext", reflect.TypeOf((*MockIdentity)(nil).GetAccountLegalManifestsWithContext), ctx, appID, accountID)
}

// GetAccountStatus mocks base method.
func (m *MockIdentity) GetAccountStatus(bearer string) (*identity.AccountStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountStatus", bearer)
	ret0, _ := ret[0].(*identity.AccountStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountStatus indicates an expected call of GetAccountStatus.
func (mr *MockIdentityMockRecorder) GetAccountStatus(bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatus", reflect.TypeOf((*MockIdentity)(nil).GetAccountStatus), bearer)
}

// GetAccountStatusWithContext mocks base method.
func (m *MockIdentity) GetAccountStatusWithContext(ctx context.Context, bearer string) (*identity.AccountStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountStatusWithContext", ctx, bearer)
	ret0, _ := ret[0].(*identity.AccountStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountStatusWithContext indicates an expected call of GetAccountStatusWithContext.
func (mr *MockIdentityMockRecorder) GetAccountStatusWithContext(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountStatusWithContext", reflect.TypeOf((*MockIdentity)(nil).GetAccountStatusWithContext), ctx, bearer)
}

// GetAccountWithContext mocks base method.
func (m *MockIdentity) GetAccountWithContext(ctx context.Context, accountID string) (*identity.Account, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountWithContext", ctx, accountID)
	ret0, _ := ret[0].(*identity.Account)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountWithContext indicates an expected call of GetAccountWithContext.
func (mr *MockIdentityMockRecorder) GetAccountWithContext(ctx, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).GetAccountWithContext), ctx, accountID)
}

// GetAccountsMe mocks base method.
func (m *MockIdentity) GetAccountsMe(ctx context.Context, bearer string) (*identity.AccountsMeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAccountsMe", ctx, bearer)
	ret0, _ := ret[0].(*identity.AccountsMeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAccountsMe indicates an expected call of GetAccountsMe.
func (mr *MockIdentityMockRecorder) GetAccountsMe(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAccountsMe", reflect.TypeOf((*MockIdentity)(nil).GetAccountsMe), ctx, bearer)
}

// GetAppLegalManifests mocks base method.
func (m *MockIdentity) GetAppLegalManifests(clientID string) ([]identity.LegalManifest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppLegalManifests", clientID)
	ret0, _ := ret[0].([]identity.LegalManifest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppLegalManifests indicates an expected call of GetAppLegalManifests.
func (mr *MockIdentityMockRecorder) GetAppLegalManifests(clientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppLegalManifests", reflect.TypeOf((*MockIdentity)(nil).GetAppLegalManifests), clientID)
}

// GetAppLegalManifestsWithContext mocks base method.
func (m *MockIdentity) GetAppLegalManifestsWithContext(ctx context.Context, clientID string) ([]identity.LegalManifest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAppLegalManifestsWithContext", ctx, clientID)
	ret0, _ := ret[0].([]identity.LegalManifest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAppLegalManifestsWithContext indicates an expected call of GetAppLegalManifestsWithContext.
func (mr *MockIdentityMockRecorder) GetAppLegalManifestsWithContext(ctx, clientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAppLegalManifestsWithContext", reflect.TypeOf((*MockIdentity)(nil).GetAppLegalManifestsWithContext), ctx, clientID)
}

// GetApplication mocks base method.
func (m *MockIdentity) GetApplication(appID string) (*identity.Application, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplication", appID)
	ret0, _ := ret[0].(*identity.Application)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplication indicates an expected call of GetApplication.
func (mr *MockIdentityMockRecorder) GetApplication(appID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplication", reflect.TypeOf((*MockIdentity)(nil).GetApplication), appID)
}

// GetApplicationWithContext mocks base method.
func (m *MockIdentity) GetApplicationWithContext(ctx context.Context, appID string) (*identity.Application, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetApplicationWithContext", ctx, appID)
	ret0, _ := ret[0].(*identity.Application)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetApplicationWithContext indicates an expected call of GetApplicationWithContext.
func (mr *MockIdentityMockRecorder) GetApplicationWithContext(ctx, appID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetApplicationWithContext", reflect.TypeOf((*MockIdentity)(nil).GetApplicationWithContext), ctx, appID)
}

// GetAuthCode mocks base method.
func (m *MockIdentity) GetAuthCode(authCode *identity.AuthCodeRequest) (*identity.AuthCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthCode", authCode)
	ret0, _ := ret[0].(*identity.AuthCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthCode indicates an expected call of GetAuthCode.
func (mr *MockIdentityMockRecorder) GetAuthCode(authCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthCode", reflect.TypeOf((*MockIdentity)(nil).GetAuthCode), authCode)
}

// GetAuthCodeWithContext mocks base method.
func (m *MockIdentity) GetAuthCodeWithContext(ctx context.Context, authCode *identity.AuthCodeRequest) (*identity.AuthCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAuthCodeWithContext", ctx, authCode)
	ret0, _ := ret[0].(*identity.AuthCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAuthCodeWithContext indicates an expected call of GetAuthCodeWithContext.
func (mr *MockIdentityMockRecorder) GetAuthCodeWithContext(ctx, authCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAuthCodeWithContext", reflect.TypeOf((*MockIdentity)(nil).GetAuthCodeWithContext), ctx, authCode)
}

// GetHealth mocks base method.
func (m *MockIdentity) GetHealth() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealth")
	ret0, _ := ret[0].(error)
	return ret0
}

// GetHealth indicates an expected call of GetHealth.
func (mr *MockIdentityMockRecorder) GetHealth() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealth", reflect.TypeOf((*MockIdentity)(nil).GetHealth))
}

// GetHealthWithContext mocks base method.
func (m *MockIdentity) GetHealthWithContext(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthWithContext", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// GetHealthWithContext indicates an expected call of GetHealthWithContext.
func (mr *MockIdentityMockRecorder) GetHealthWithContext(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthWithContext", reflect.TypeOf((*MockIdentity)(nil).GetHealthWithContext), ctx)
}

// GetLegalDocument mocks base method.
func (m *MockIdentity) GetLegalDocument(clientID, documentID string) (*identity.LegalDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLegalDocument", clientID, documentID)
	ret0, _ := ret[0].(*identity.LegalDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLegalDocument indicates an expected call of GetLegalDocument.
func (mr *MockIdentityMockRecorder) GetLegalDocument(clientID, documentID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLegalDocument", reflect.TypeOf((*MockIdentity)(nil).GetLegalDocument), clientID, documentID)
}

// GetLegalDocumentWithContext mocks base method.
func (m *MockIdentity) GetLegalDocumentWithContext(ctx context.Context, clientID, documentID string) (*identity.LegalDocumentResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLegalDocumentWithContext", ctx, clientID, documentID)
	ret0, _ := ret[0].(*identity.LegalDocumentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLegalDocumentWithContext indicates an expected call of GetLegalDocumentWithContext.
func (mr *MockIdentityMockRecorder) GetLegalDocumentWithContext(ctx, clientID, documentID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLegalDocumentWithContext", reflect.TypeOf((*MockIdentity)(nil).GetLegalDocumentWithContext), ctx, clientID, documentID)
}

// GetLinksMe mocks base method.
func (m *MockIdentity) GetLinksMe(bearer string) ([]identity.LinksMeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinksMe", bearer)
	ret0, _ := ret[0].([]identity.LinksMeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinksMe indicates an expected call of GetLinksMe.
func (mr *MockIdentityMockRecorder) GetLinksMe(bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinksMe", reflect.TypeOf((*MockIdentity)(nil).GetLinksMe), bearer)
}

// GetLinksMeWithContext mocks base method.
func (m *MockIdentity) GetLinksMeWithContext(ctx context.Context, bearer string) ([]identity.LinksMeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLinksMeWithContext", ctx, bearer)
	ret0, _ := ret[0].([]identity.LinksMeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLinksMeWithContext indicates an expected call of GetLinksMeWithContext.
func (mr *MockIdentityMockRecorder) GetLinksMeWithContext(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLinksMeWithContext", reflect.TypeOf((*MockIdentity)(nil).GetLinksMeWithContext), ctx, bearer)
}

// GetParentStatus mocks base method.
func (m *MockIdentity) GetParentStatus(platformToken, clientID string) (*identity.ParentStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParentStatus", platformToken, clientID)
	ret0, _ := ret[0].(*identity.ParentStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParentStatus indicates an expected call of GetParentStatus.
func (mr *MockIdentityMockRecorder) GetParentStatus(platformToken, clientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParentStatus", reflect.TypeOf((*MockIdentity)(nil).GetParentStatus), platformToken, clientID)
}

// GetParentStatusWithContext mocks base method.
func (m *MockIdentity) GetParentStatusWithContext(ctx context.Context, platformToken, clientID string) (*identity.ParentStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetParentStatusWithContext", ctx, platformToken, clientID)
	ret0, _ := ret[0].(*identity.ParentStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetParentStatusWithContext indicates an expected call of GetParentStatusWithContext.
func (mr *MockIdentityMockRecorder) GetParentStatusWithContext(ctx, platformToken, clientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetParentStatusWithContext", reflect.TypeOf((*MockIdentity)(nil).GetParentStatusWithContext), ctx, platformToken, clientID)
}

// GetProduct mocks base method.
func (m *MockIdentity) GetProduct(productID string) (*identity.Product, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProduct", productID)
	ret0, _ := ret[0].(*identity.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProduct indicates an expected call of GetProduct.
func (mr *MockIdentityMockRecorder) GetProduct(productID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProduct", reflect.TypeOf((*MockIdentity)(nil).GetProduct), productID)
}

// GetProductWithContext mocks base method.
func (m *MockIdentity) GetProductWithContext(ctx context.Context, productID string) (*identity.Product, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductWithContext", ctx, productID)
	ret0, _ := ret[0].(*identity.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductWithContext indicates an expected call of GetProductWithContext.
func (mr *MockIdentityMockRecorder) GetProductWithContext(ctx, productID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductWithContext", reflect.TypeOf((*MockIdentity)(nil).GetProductWithContext), ctx, productID)
}

// GetToken mocks base method.
func (m *MockIdentity) GetToken(tokenRequest *identity.TokenRequest, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetToken", tokenRequest, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetToken indicates an expected call of GetToken.
func (mr *MockIdentityMockRecorder) GetToken(tokenRequest, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetToken", reflect.TypeOf((*MockIdentity)(nil).GetToken), tokenRequest, additionalHeaders)
}

// GetTokenWithRefreshToken mocks base method.
func (m *MockIdentity) GetTokenWithRefreshToken(tokenRequest *identity.TokenRequest, appID string, additionalHeaders map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTokenWithRefreshToken", tokenRequest, appID, additionalHeaders)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTokenWithRefreshToken indicates an expected call of GetTokenWithRefreshToken.
func (mr *MockIdentityMockRecorder) GetTokenWithRefreshToken(tokenRequest, appID, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTokenWithRefreshToken", reflect.TypeOf((*MockIdentity)(nil).GetTokenWithRefreshToken), tokenRequest, appID, additionalHeaders)
}

// GetUserLegalManifests mocks base method.
func (m *MockIdentity) GetUserLegalManifests(accountID string) ([]identity.UserLegalResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLegalManifests", accountID)
	ret0, _ := ret[0].([]identity.UserLegalResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLegalManifests indicates an expected call of GetUserLegalManifests.
func (mr *MockIdentityMockRecorder) GetUserLegalManifests(accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLegalManifests", reflect.TypeOf((*MockIdentity)(nil).GetUserLegalManifests), accountID)
}

// GetUserLegalManifestsWithContext mocks base method.
func (m *MockIdentity) GetUserLegalManifestsWithContext(ctx context.Context, accountID string) ([]identity.UserLegalResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserLegalManifestsWithContext", ctx, accountID)
	ret0, _ := ret[0].([]identity.UserLegalResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserLegalManifestsWithContext indicates an expected call of GetUserLegalManifestsWithContext.
func (mr *MockIdentityMockRecorder) GetUserLegalManifestsWithContext(ctx, accountID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserLegalManifestsWithContext", reflect.TypeOf((*MockIdentity)(nil).GetUserLegalManifestsWithContext), ctx, accountID)
}

// LinkFullToPlatformAccount mocks base method.
func (m *MockIdentity) LinkFullToPlatformAccount(fullAccountJWT, platformAccountJWT string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkFullToPlatformAccount", fullAccountJWT, platformAccountJWT)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkFullToPlatformAccount indicates an expected call of LinkFullToPlatformAccount.
func (mr *MockIdentityMockRecorder) LinkFullToPlatformAccount(fullAccountJWT, platformAccountJWT interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkFullToPlatformAccount", reflect.TypeOf((*MockIdentity)(nil).LinkFullToPlatformAccount), fullAccountJWT, platformAccountJWT)
}

// LinkFullToPlatformAccountWithContext mocks base method.
func (m *MockIdentity) LinkFullToPlatformAccountWithContext(ctx context.Context, fullAccountJWT, platformAccountJWT string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkFullToPlatformAccountWithContext", ctx, fullAccountJWT, platformAccountJWT)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkFullToPlatformAccountWithContext indicates an expected call of LinkFullToPlatformAccountWithContext.
func (mr *MockIdentityMockRecorder) LinkFullToPlatformAccountWithContext(ctx, fullAccountJWT, platformAccountJWT interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkFullToPlatformAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).LinkFullToPlatformAccountWithContext), ctx, fullAccountJWT, platformAccountJWT)
}

// LinkPlatformID mocks base method.
func (m *MockIdentity) LinkPlatformID(bearer, platform, platformID, alias string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkPlatformID", bearer, platform, platformID, alias)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkPlatformID indicates an expected call of LinkPlatformID.
func (mr *MockIdentityMockRecorder) LinkPlatformID(bearer, platform, platformID, alias interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkPlatformID", reflect.TypeOf((*MockIdentity)(nil).LinkPlatformID), bearer, platform, platformID, alias)
}

// LinkPlatformIDWithBasicAuth mocks base method.
func (m *MockIdentity) LinkPlatformIDWithBasicAuth(accountID, platformID, alias, platform string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkPlatformIDWithBasicAuth", accountID, platformID, alias, platform)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkPlatformIDWithBasicAuth indicates an expected call of LinkPlatformIDWithBasicAuth.
func (mr *MockIdentityMockRecorder) LinkPlatformIDWithBasicAuth(accountID, platformID, alias, platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkPlatformIDWithBasicAuth", reflect.TypeOf((*MockIdentity)(nil).LinkPlatformIDWithBasicAuth), accountID, platformID, alias, platform)
}

// LinkPlatformIDWithBasicAuthAndContext mocks base method.
func (m *MockIdentity) LinkPlatformIDWithBasicAuthAndContext(ctx context.Context, accountID, platformID, alias, platform string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkPlatformIDWithBasicAuthAndContext", ctx, accountID, platformID, alias, platform)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkPlatformIDWithBasicAuthAndContext indicates an expected call of LinkPlatformIDWithBasicAuthAndContext.
func (mr *MockIdentityMockRecorder) LinkPlatformIDWithBasicAuthAndContext(ctx, accountID, platformID, alias, platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkPlatformIDWithBasicAuthAndContext", reflect.TypeOf((*MockIdentity)(nil).LinkPlatformIDWithBasicAuthAndContext), ctx, accountID, platformID, alias, platform)
}

// LinkPlatformIDWithContext mocks base method.
func (m *MockIdentity) LinkPlatformIDWithContext(ctx context.Context, bearer, platform, platformID, alias string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LinkPlatformIDWithContext", ctx, bearer, platform, platformID, alias)
	ret0, _ := ret[0].(error)
	return ret0
}

// LinkPlatformIDWithContext indicates an expected call of LinkPlatformIDWithContext.
func (mr *MockIdentityMockRecorder) LinkPlatformIDWithContext(ctx, bearer, platform, platformID, alias interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LinkPlatformIDWithContext", reflect.TypeOf((*MockIdentity)(nil).LinkPlatformIDWithContext), ctx, bearer, platform, platformID, alias)
}

// LoginWithExternalID mocks base method.
func (m *MockIdentity) LoginWithExternalID(externalID, platform string, additionalHeaders, additionalFields map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoginWithExternalID", externalID, platform, additionalHeaders, additionalFields)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoginWithExternalID indicates an expected call of LoginWithExternalID.
func (mr *MockIdentityMockRecorder) LoginWithExternalID(externalID, platform, additionalHeaders, additionalFields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoginWithExternalID", reflect.TypeOf((*MockIdentity)(nil).LoginWithExternalID), externalID, platform, additionalHeaders, additionalFields)
}

// LoginWithExternalIDAndContext mocks base method.
func (m *MockIdentity) LoginWithExternalIDAndContext(ctx context.Context, externalID, platform string, additionalHeaders, additionalFields map[string]string) (*identity.TokenResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoginWithExternalIDAndContext", ctx, externalID, platform, additionalHeaders, additionalFields)
	ret0, _ := ret[0].(*identity.TokenResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoginWithExternalIDAndContext indicates an expected call of LoginWithExternalIDAndContext.
func (mr *MockIdentityMockRecorder) LoginWithExternalIDAndContext(ctx, externalID, platform, additionalHeaders, additionalFields interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoginWithExternalIDAndContext", reflect.TypeOf((*MockIdentity)(nil).LoginWithExternalIDAndContext), ctx, externalID, platform, additionalHeaders, additionalFields)
}

// QueryAccountsByEmail mocks base method.
func (m *MockIdentity) QueryAccountsByEmail(email string) (identity.AccountQueryResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAccountsByEmail", email)
	ret0, _ := ret[0].(identity.AccountQueryResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAccountsByEmail indicates an expected call of QueryAccountsByEmail.
func (mr *MockIdentityMockRecorder) QueryAccountsByEmail(email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAccountsByEmail", reflect.TypeOf((*MockIdentity)(nil).QueryAccountsByEmail), email)
}

// QueryAccountsByEmailWithContext mocks base method.
func (m *MockIdentity) QueryAccountsByEmailWithContext(ctx context.Context, email string) (identity.AccountQueryResults, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueryAccountsByEmailWithContext", ctx, email)
	ret0, _ := ret[0].(identity.AccountQueryResults)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// QueryAccountsByEmailWithContext indicates an expected call of QueryAccountsByEmailWithContext.
func (mr *MockIdentityMockRecorder) QueryAccountsByEmailWithContext(ctx, email interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueryAccountsByEmailWithContext", reflect.TypeOf((*MockIdentity)(nil).QueryAccountsByEmailWithContext), ctx, email)
}

// RequestDeviceCode mocks base method.
func (m *MockIdentity) RequestDeviceCode(clientID string) (*identity.RequestDeviceCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestDeviceCode", clientID)
	ret0, _ := ret[0].(*identity.RequestDeviceCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestDeviceCode indicates an expected call of RequestDeviceCode.
func (mr *MockIdentityMockRecorder) RequestDeviceCode(clientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestDeviceCode", reflect.TypeOf((*MockIdentity)(nil).RequestDeviceCode), clientID)
}

// RequestDeviceCodeWithContext mocks base method.
func (m *MockIdentity) RequestDeviceCodeWithContext(ctx context.Context, clientID string) (*identity.RequestDeviceCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestDeviceCodeWithContext", ctx, clientID)
	ret0, _ := ret[0].(*identity.RequestDeviceCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RequestDeviceCodeWithContext indicates an expected call of RequestDeviceCodeWithContext.
func (mr *MockIdentityMockRecorder) RequestDeviceCodeWithContext(ctx, clientID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestDeviceCodeWithContext", reflect.TypeOf((*MockIdentity)(nil).RequestDeviceCodeWithContext), ctx, clientID)
}

// RequestUpdatePendingEmail mocks base method.
func (m *MockIdentity) RequestUpdatePendingEmail(accountID string, request *identity.UpdatePendingEmailRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestUpdatePendingEmail", accountID, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// RequestUpdatePendingEmail indicates an expected call of RequestUpdatePendingEmail.
func (mr *MockIdentityMockRecorder) RequestUpdatePendingEmail(accountID, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestUpdatePendingEmail", reflect.TypeOf((*MockIdentity)(nil).RequestUpdatePendingEmail), accountID, request)
}

// RequestUpdatePendingEmailWithContext mocks base method.
func (m *MockIdentity) RequestUpdatePendingEmailWithContext(ctx context.Context, accountID string, request *identity.UpdatePendingEmailRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RequestUpdatePendingEmailWithContext", ctx, accountID, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// RequestUpdatePendingEmailWithContext indicates an expected call of RequestUpdatePendingEmailWithContext.
func (mr *MockIdentityMockRecorder) RequestUpdatePendingEmailWithContext(ctx, accountID, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RequestUpdatePendingEmailWithContext", reflect.TypeOf((*MockIdentity)(nil).RequestUpdatePendingEmailWithContext), ctx, accountID, request)
}

// RevokeToken mocks base method.
func (m *MockIdentity) RevokeToken(bearer string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeToken", bearer)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeToken indicates an expected call of RevokeToken.
func (mr *MockIdentityMockRecorder) RevokeToken(bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeToken", reflect.TypeOf((*MockIdentity)(nil).RevokeToken), bearer)
}

// RevokeTokenWithContext mocks base method.
func (m *MockIdentity) RevokeTokenWithContext(ctx context.Context, bearer string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RevokeTokenWithContext", ctx, bearer)
	ret0, _ := ret[0].(error)
	return ret0
}

// RevokeTokenWithContext indicates an expected call of RevokeTokenWithContext.
func (mr *MockIdentityMockRecorder) RevokeTokenWithContext(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RevokeTokenWithContext", reflect.TypeOf((*MockIdentity)(nil).RevokeTokenWithContext), ctx, bearer)
}

// SendEmail mocks base method.
func (m *MockIdentity) SendEmail(emailRequest *identity.EmailRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEmail", emailRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendEmail indicates an expected call of SendEmail.
func (mr *MockIdentityMockRecorder) SendEmail(emailRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmail", reflect.TypeOf((*MockIdentity)(nil).SendEmail), emailRequest)
}

// SendEmailWithContext mocks base method.
func (m *MockIdentity) SendEmailWithContext(ctx context.Context, emailRequest *identity.EmailRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEmailWithContext", ctx, emailRequest)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendEmailWithContext indicates an expected call of SendEmailWithContext.
func (mr *MockIdentityMockRecorder) SendEmailWithContext(ctx, emailRequest interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEmailWithContext", reflect.TypeOf((*MockIdentity)(nil).SendEmailWithContext), ctx, emailRequest)
}

// TransferChildAccount mocks base method.
func (m *MockIdentity) TransferChildAccount(request *identity.TransferChildAccountRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransferChildAccount", request)
	ret0, _ := ret[0].(error)
	return ret0
}

// TransferChildAccount indicates an expected call of TransferChildAccount.
func (mr *MockIdentityMockRecorder) TransferChildAccount(request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransferChildAccount", reflect.TypeOf((*MockIdentity)(nil).TransferChildAccount), request)
}

// TransferChildAccountWithContext mocks base method.
func (m *MockIdentity) TransferChildAccountWithContext(ctx context.Context, request *identity.TransferChildAccountRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TransferChildAccountWithContext", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// TransferChildAccountWithContext indicates an expected call of TransferChildAccountWithContext.
func (mr *MockIdentityMockRecorder) TransferChildAccountWithContext(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TransferChildAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).TransferChildAccountWithContext), ctx, request)
}

// UnlinkPlatformAccount mocks base method.
func (m *MockIdentity) UnlinkPlatformAccount(bearer, platform string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlinkPlatformAccount", bearer, platform)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlinkPlatformAccount indicates an expected call of UnlinkPlatformAccount.
func (mr *MockIdentityMockRecorder) UnlinkPlatformAccount(bearer, platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlinkPlatformAccount", reflect.TypeOf((*MockIdentity)(nil).UnlinkPlatformAccount), bearer, platform)
}

// UnlinkPlatformAccountWithContext mocks base method.
func (m *MockIdentity) UnlinkPlatformAccountWithContext(ctx context.Context, bearer, platform string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlinkPlatformAccountWithContext", ctx, bearer, platform)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlinkPlatformAccountWithContext indicates an expected call of UnlinkPlatformAccountWithContext.
func (mr *MockIdentityMockRecorder) UnlinkPlatformAccountWithContext(ctx, bearer, platform interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlinkPlatformAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).UnlinkPlatformAccountWithContext), ctx, bearer, platform)
}

// UpdateAccount mocks base method.
func (m *MockIdentity) UpdateAccount(bearer string, request *identity.AccountUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccount", bearer, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccount indicates an expected call of UpdateAccount.
func (mr *MockIdentityMockRecorder) UpdateAccount(bearer, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccount", reflect.TypeOf((*MockIdentity)(nil).UpdateAccount), bearer, request)
}

// UpdateAccountByID mocks base method.
func (m *MockIdentity) UpdateAccountByID(accountID string, request *identity.AccountUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountByID", accountID, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountByID indicates an expected call of UpdateAccountByID.
func (mr *MockIdentityMockRecorder) UpdateAccountByID(accountID, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountByID", reflect.TypeOf((*MockIdentity)(nil).UpdateAccountByID), accountID, request)
}

// UpdateAccountByIDWithContext mocks base method.
func (m *MockIdentity) UpdateAccountByIDWithContext(ctx context.Context, accountID string, request *identity.AccountUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountByIDWithContext", ctx, accountID, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountByIDWithContext indicates an expected call of UpdateAccountByIDWithContext.
func (mr *MockIdentityMockRecorder) UpdateAccountByIDWithContext(ctx, accountID, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountByIDWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateAccountByIDWithContext), ctx, accountID, request)
}

// UpdateAccountWithContext mocks base method.
func (m *MockIdentity) UpdateAccountWithContext(ctx context.Context, bearer string, request *identity.AccountUpdateRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAccountWithContext", ctx, bearer, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAccountWithContext indicates an expected call of UpdateAccountWithContext.
func (mr *MockIdentityMockRecorder) UpdateAccountWithContext(ctx, bearer, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAccountWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateAccountWithContext), ctx, bearer, request)
}

// UpdateDeviceAuthStatus mocks base method.
func (m *MockIdentity) UpdateDeviceAuthStatus(request *identity.UpdateDeviceAuthStatusRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDeviceAuthStatus", request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDeviceAuthStatus indicates an expected call of UpdateDeviceAuthStatus.
func (mr *MockIdentityMockRecorder) UpdateDeviceAuthStatus(request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDeviceAuthStatus", reflect.TypeOf((*MockIdentity)(nil).UpdateDeviceAuthStatus), request)
}

// UpdateDeviceAuthStatusWithContext mocks base method.
func (m *MockIdentity) UpdateDeviceAuthStatusWithContext(ctx context.Context, request *identity.UpdateDeviceAuthStatusRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDeviceAuthStatusWithContext", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDeviceAuthStatusWithContext indicates an expected call of UpdateDeviceAuthStatusWithContext.
func (mr *MockIdentityMockRecorder) UpdateDeviceAuthStatusWithContext(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDeviceAuthStatusWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateDeviceAuthStatusWithContext), ctx, request)
}

// UpdateDisplayName mocks base method.
func (m *MockIdentity) UpdateDisplayName(bearer, displayName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDisplayName", bearer, displayName)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDisplayName indicates an expected call of UpdateDisplayName.
func (mr *MockIdentityMockRecorder) UpdateDisplayName(bearer, displayName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDisplayName", reflect.TypeOf((*MockIdentity)(nil).UpdateDisplayName), bearer, displayName)
}

// UpdateDisplayNameWithContext mocks base method.
func (m *MockIdentity) UpdateDisplayNameWithContext(ctx context.Context, bearer, displayName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDisplayNameWithContext", ctx, bearer, displayName)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDisplayNameWithContext indicates an expected call of UpdateDisplayNameWithContext.
func (mr *MockIdentityMockRecorder) UpdateDisplayNameWithContext(ctx, bearer, displayName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDisplayNameWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateDisplayNameWithContext), ctx, bearer, displayName)
}

// UpdateFullAccountLegalManifests mocks base method.
func (m *MockIdentity) UpdateFullAccountLegalManifests(accountId string, legalResponse []identity.UserLegalResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFullAccountLegalManifests", accountId, legalResponse)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFullAccountLegalManifests indicates an expected call of UpdateFullAccountLegalManifests.
func (mr *MockIdentityMockRecorder) UpdateFullAccountLegalManifests(accountId, legalResponse interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFullAccountLegalManifests", reflect.TypeOf((*MockIdentity)(nil).UpdateFullAccountLegalManifests), accountId, legalResponse)
}

// UpdateFullAccountLegalManifestsHeaderWithContext mocks base method.
func (m *MockIdentity) UpdateFullAccountLegalManifestsHeaderWithContext(ctx context.Context, accountId string, legalResponse []identity.UserLegalResponse, additionalHeaders map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFullAccountLegalManifestsHeaderWithContext", ctx, accountId, legalResponse, additionalHeaders)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFullAccountLegalManifestsHeaderWithContext indicates an expected call of UpdateFullAccountLegalManifestsHeaderWithContext.
func (mr *MockIdentityMockRecorder) UpdateFullAccountLegalManifestsHeaderWithContext(ctx, accountId, legalResponse, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFullAccountLegalManifestsHeaderWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateFullAccountLegalManifestsHeaderWithContext), ctx, accountId, legalResponse, additionalHeaders)
}

// UpdateFullAccountLegalManifestsWithContext mocks base method.
func (m *MockIdentity) UpdateFullAccountLegalManifestsWithContext(ctx context.Context, accountId string, legalResponse []identity.UserLegalResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFullAccountLegalManifestsWithContext", ctx, accountId, legalResponse)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFullAccountLegalManifestsWithContext indicates an expected call of UpdateFullAccountLegalManifestsWithContext.
func (mr *MockIdentityMockRecorder) UpdateFullAccountLegalManifestsWithContext(ctx, accountId, legalResponse interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFullAccountLegalManifestsWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateFullAccountLegalManifestsWithContext), ctx, accountId, legalResponse)
}

// UpdateFullAccountLegalManifestsWithHeader mocks base method.
func (m *MockIdentity) UpdateFullAccountLegalManifestsWithHeader(accountId string, legalResponse []identity.UserLegalResponse, additionalHeaders map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFullAccountLegalManifestsWithHeader", accountId, legalResponse, additionalHeaders)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFullAccountLegalManifestsWithHeader indicates an expected call of UpdateFullAccountLegalManifestsWithHeader.
func (mr *MockIdentityMockRecorder) UpdateFullAccountLegalManifestsWithHeader(accountId, legalResponse, additionalHeaders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFullAccountLegalManifestsWithHeader", reflect.TypeOf((*MockIdentity)(nil).UpdateFullAccountLegalManifestsWithHeader), accountId, legalResponse, additionalHeaders)
}

// UpdatePassword mocks base method.
func (m *MockIdentity) UpdatePassword(bearer, password string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePassword", bearer, password)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePassword indicates an expected call of UpdatePassword.
func (mr *MockIdentityMockRecorder) UpdatePassword(bearer, password interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePassword", reflect.TypeOf((*MockIdentity)(nil).UpdatePassword), bearer, password)
}

// UpdatePasswordWithContext mocks base method.
func (m *MockIdentity) UpdatePasswordWithContext(ctx context.Context, bearer, password string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePasswordWithContext", ctx, bearer, password)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePasswordWithContext indicates an expected call of UpdatePasswordWithContext.
func (mr *MockIdentityMockRecorder) UpdatePasswordWithContext(ctx, bearer, password interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePasswordWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdatePasswordWithContext), ctx, bearer, password)
}

// UpdateUserLegalManifests mocks base method.
func (m *MockIdentity) UpdateUserLegalManifests(bearer string, legalResponse []identity.UserLegalResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserLegalManifests", bearer, legalResponse)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserLegalManifests indicates an expected call of UpdateUserLegalManifests.
func (mr *MockIdentityMockRecorder) UpdateUserLegalManifests(bearer, legalResponse interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserLegalManifests", reflect.TypeOf((*MockIdentity)(nil).UpdateUserLegalManifests), bearer, legalResponse)
}

// UpdateUserLegalManifestsWithContext mocks base method.
func (m *MockIdentity) UpdateUserLegalManifestsWithContext(ctx context.Context, bearer string, legalResponse []identity.UserLegalResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserLegalManifestsWithContext", ctx, bearer, legalResponse)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUserLegalManifestsWithContext indicates an expected call of UpdateUserLegalManifestsWithContext.
func (mr *MockIdentityMockRecorder) UpdateUserLegalManifestsWithContext(ctx, bearer, legalResponse interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserLegalManifestsWithContext", reflect.TypeOf((*MockIdentity)(nil).UpdateUserLegalManifestsWithContext), ctx, bearer, legalResponse)
}

// ValidateDeviceAuthUserCode mocks base method.
func (m *MockIdentity) ValidateDeviceAuthUserCode(userCode string) (*identity.ValidateDeviceAuthUserCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateDeviceAuthUserCode", userCode)
	ret0, _ := ret[0].(*identity.ValidateDeviceAuthUserCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateDeviceAuthUserCode indicates an expected call of ValidateDeviceAuthUserCode.
func (mr *MockIdentityMockRecorder) ValidateDeviceAuthUserCode(userCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateDeviceAuthUserCode", reflect.TypeOf((*MockIdentity)(nil).ValidateDeviceAuthUserCode), userCode)
}

// ValidateDeviceAuthUserCodeWithContext mocks base method.
func (m *MockIdentity) ValidateDeviceAuthUserCodeWithContext(ctx context.Context, userCode string) (*identity.ValidateDeviceAuthUserCodeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateDeviceAuthUserCodeWithContext", ctx, userCode)
	ret0, _ := ret[0].(*identity.ValidateDeviceAuthUserCodeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateDeviceAuthUserCodeWithContext indicates an expected call of ValidateDeviceAuthUserCodeWithContext.
func (mr *MockIdentityMockRecorder) ValidateDeviceAuthUserCodeWithContext(ctx, userCode interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateDeviceAuthUserCodeWithContext", reflect.TypeOf((*MockIdentity)(nil).ValidateDeviceAuthUserCodeWithContext), ctx, userCode)
}

// ValidateTokenSignature mocks base method.
func (m *MockIdentity) ValidateTokenSignature(bearer string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTokenSignature", bearer)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateTokenSignature indicates an expected call of ValidateTokenSignature.
func (mr *MockIdentityMockRecorder) ValidateTokenSignature(bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTokenSignature", reflect.TypeOf((*MockIdentity)(nil).ValidateTokenSignature), bearer)
}

// ValidateTokenSignatureWithContext mocks base method.
func (m *MockIdentity) ValidateTokenSignatureWithContext(ctx context.Context, bearer string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTokenSignatureWithContext", ctx, bearer)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateTokenSignatureWithContext indicates an expected call of ValidateTokenSignatureWithContext.
func (mr *MockIdentityMockRecorder) ValidateTokenSignatureWithContext(ctx, bearer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTokenSignatureWithContext", reflect.TypeOf((*MockIdentity)(nil).ValidateTokenSignatureWithContext), ctx, bearer)
}

// ValidateTokenStatus mocks base method.
func (m *MockIdentity) ValidateTokenStatus(tokenID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTokenStatus", tokenID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateTokenStatus indicates an expected call of ValidateTokenStatus.
func (mr *MockIdentityMockRecorder) ValidateTokenStatus(tokenID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTokenStatus", reflect.TypeOf((*MockIdentity)(nil).ValidateTokenStatus), tokenID)
}

// ValidateTokenStatusWithContext mocks base method.
func (m *MockIdentity) ValidateTokenStatusWithContext(ctx context.Context, tokenID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateTokenStatusWithContext", ctx, tokenID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateTokenStatusWithContext indicates an expected call of ValidateTokenStatusWithContext.
func (mr *MockIdentityMockRecorder) ValidateTokenStatusWithContext(ctx, tokenID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateTokenStatusWithContext", reflect.TypeOf((*MockIdentity)(nil).ValidateTokenStatusWithContext), ctx, tokenID)
}
