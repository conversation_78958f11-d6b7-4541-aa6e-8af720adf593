package cache

//
//import (
//	"context"
//	"github.com/dranikpg/gtrs"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
//	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
//	"strings"
//)
//
//type KeyspaceExpired struct {
//	Type string
//	Key  string
//}
//
//func (rc *RedisCache) ProcessKeyspaceExpiredStream(ctx context.Context) {
//	go rc.redisStreamHandler(ctx)
//}
//
//func (rc *RedisCache) redisStreamHandler(ctx context.Context) {
//	log := logger.FromContext(ctx)
//	consumerName := rc.cfg.Hostname
//	keyspaceExpired := gtrs.NewGroupConsumer[KeyspaceExpired](ctx, rc.rdb, rc.cfg.RedisConsumerGroup, consumerName, "keyspace:expired", "0-0")
//
//	for {
//		var msg gtrs.Message[KeyspaceExpired]
//		var ackTarget *gtrs.GroupConsumer[KeyspaceExpired]
//
//		select {
//		case <-ctx.Done():
//			log.Debug().Str("event", "key space consumer context finished").Msg("key space consumer context finished")
//			return
//
//		case msg = <-keyspaceExpired.Chan():
//			ackTarget = keyspaceExpired
//		}
//
//		switch errv := msg.Err.(type) {
//		case nil:
//			log.Info().Str("msgid", msg.ID).Str("type", msg.Data.Type).Str("key", msg.Data.Key).Str("event", "Redis Stream expired event").Msg("Redis Stream expired event")
//
//			if msg.Data.Type == "presence" {
//				span, spanCtx := tracer.StartSpanFromContext(ctx, "redisStreamHandler", tracer.ServiceName(rc.cfg.ServiceName), tracer.ResourceName("presenceExpired"), tracer.SpanType("redis"))
//				pieces := strings.Split(msg.Data.Key, ":")
//				tenant := pieces[0]
//				productid := pieces[2]
//				userid := utils.RemoveCurlyBrackets(pieces[5])
//				sessionid := ""
//				// TODO: [SPOP] - revert the commenting out
//				// if len(pieces) >= 8 {
//				// 	sessionid = utils.RemoveCurlyBrackets(pieces[7])
//				// }
//				log.Debug().Str("tenant", tenant).Str("productid", productid).Str("userid", userid).Msg("Presence expiration")
//
//				userSubject := index.NewUserSubject(tenant, productid, userid)
//				if userSubject == nil {
//					continue
//				}
//				memberOf := userSubject.MemberOfKey()
//				if memberOf == nil {
//					continue
//				}
//				userGroupIdx := index.NewSecondaryIndex(*memberOf, "")
//				offlinePresence := &apipub.Presence{
//					Userid:    userid,
//					Productid: productid,
//					Status:    apipub.Offline,
//				}
//
//				bRemoveMissingGroups := false
//				//get groups that the user is a member of and leave them if the presence for that productid expires.
//				groups, _, err2 := getObjsFromSecondaryIndex[apipub.Group](spanCtx, rc, userGroupIdx, nil, nil, false)
//				//if group related redis object is missing, ignore error
//				if err2 != nil && errs.IsEqual(err2, errs.ERedisObjectMissing) {
//					err2 = nil
//					bRemoveMissingGroups = true
//				}
//				if err2 != nil {
//					log.Error().Err(err2).Msg("Unable to get members group from expiring presence")
//				}
//				if groups != nil {
//					for _, group := range *groups {
//						if productid == group.Productid {
//							reason := "Kicking from Group for presence expiry"
//							log.Info().Str("groupid", group.Groupid).Msg(reason)
//							rc.tele.Event(rc.tele.NewEvent(telemetry.KUserAutoKicked, spanCtx))
//							_, err := rc.KickOrLeaveHelper(spanCtx, group, rc.cfg.AppID, userid, &reason)
//							if err != nil {
//								log.Error().Err(err).Str("groupid", group.Groupid).Str("userid", userid).Str("productid", productid).Msg("Unable to delete group on expiry")
//							} else {
//								messenger.SendMqttMessage(ctx, group.Topic(tenant), messenger.MqttMessageTypePresence, offlinePresence)
//							}
//						}
//					}
//				}
//
//				if bRemoveMissingGroups {
//					//remove any groups that expired for this product but are still in user memberOf list
//					valKeys, _, err := getKeysFromSecondaryIndex(spanCtx, rc, userGroupIdx, nil, nil, false)
//					if err == nil && valKeys != nil {
//						for _, groupKey := range *valKeys {
//							keyParts := strings.Split(groupKey, ":")
//							if len(keyParts) >= 6 && keyParts[2] == productid && keyParts[4] == "group" {
//
//								//check for existing object in redis. if not found, delete
//								_, err2 := getCachedObjects[apipub.Group](spanCtx, rc, &[]string{groupKey})
//								if err2 != nil && errs.IsEqual(err2, errs.ERedisObjectMissing) {
//									missingGroup := &apipub.Group{
//										Productid: productid,
//										Groupid:   utils.RemoveCurlyBrackets(keyParts[5]),
//									}
//									rc.DeleteUserGroup(spanCtx, userid, missingGroup)
//									log.Debug().Str("groupKey", groupKey).Str("productid", productid).Str("userid", userid).Msg("Remove old expired groups")
//								}
//							}
//						}
//					}
//				}
//
//				userSubject = index.NewUserSubject(tenant, productid, userid)
//				if userSubject == nil {
//					continue
//				}
//				userPresenceKey := userSubject.PresencesSetForKey()
//				if userPresenceKey == nil {
//					continue
//				}
//
//				idx := index.NewSecondaryIndex(*userPresenceKey, apipub.BuildPresenceRedisKey(tenant, productid, userid, sessionid))
//				//Also delete secondary index for presence since obj is expired
//				err2 = rc.delSecondaryIndex(spanCtx, idx)
//				if err2 != nil {
//					log.Error().Err(err2).Str("idxKey", idx.IdxKey()).Msg("Unable to delete secondary index for presence")
//				}
//
//				messenger.SendMqttMessage(ctx, offlinePresence.Topic(tenant), messenger.MqttMessageTypePresence, offlinePresence)
//				span.Finish()
//			}
//
//			if ackTarget != nil {
//				ackTarget.Ack(msg)
//			}
//		case gtrs.ReadError:
//			log.Error().Str("err", msg.Err.Error()).Str("event", "Redis Stream read error").Msg("Redis Stream read error")
//			return
//		case gtrs.AckError:
//			log.Error().Str("err", msg.Err.Error()).Str("stream", msg.Stream).Str("msgid", msg.ID).Str("event", "Redis Stream ACK error").Msg("Redis Stream ACK error")
//		case gtrs.ParseError:
//			log.Error().Str("err", msg.Err.Error()).Interface("data", errv.Data).Str("event", "Redis Stream Parse error").Msg("Redis Stream Parse error")
//		}
//	}
//}
