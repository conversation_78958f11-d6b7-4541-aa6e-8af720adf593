const AWS = require('aws-sdk');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const fs = require('fs');

const errorFile = './lastEvaluatedKey.txt';
let totalItemCount = 0;

function scanTable(dynamodb, sourceTable, targetTable, type, startKey) {
    const scan = {
        TableName: sourceTable,
        KeyConditionExpression: "begins_with (sk, :value)",
        ExpressionAttributeValues: {
            ":value": { "S": "user#" },
        },
        Limit: 30,
        ExclusiveStartKey: startKey ? JSON.parse(startKey) : null
    }

    return new Promise((resolve, reject) => {
        dynamodb.scan(scan, (err, data) => {
            if (err) {
                fs.writeFileSync(errorFile, JSON.stringify(scan.ExclusiveStartKey));
                reject(err);
            } else if (data.LastEvaluatedKey === null || data.Items.length == 0) {
                resolve();
            } else {
                // Write items to target table when count equals 100 or LastEvaluatedKey is undefined
                const batchParams = {
                    RequestItems: {
                        [targetTable]: data.Items.map(
                            item => ({
                                PutRequest: {
                                    Item: Object.assign({}, item, {
                                        pk: {
                                            S: 'dna#' + item.pk.S
                                        },
                                        sk: {
                                            S: 'dna#' + type + '#' + item.userid.S
                                        },
                                    })
                                }
                            })
                        )
                    }
                }

                totalItemCount += data.Items.length;
                dynamodb.batchWriteItem(batchParams, (err, data) => {
                    if (err) {
                        fs.writeFileSync(errorFile, JSON.stringify(scan.ExclusiveStartKey));
                        reject(err);
                    } else if (data.UnprocessedItems[targetTable]) {
                        const retryParams = {
                            RequestItems: {
                                [targetTable]: data.UnprocessedItems[targetTable]
                            }
                        };
                        dynamodb.batchWriteItem(retryParams, (err, data) => {
                            if (err) {
                                fs.writeFileSync(errorFile, JSON.stringify(scan.ExclusiveStartKey));
                                reject(err);
                            } else {
                                console.log(`Backfilled ${totalItemCount} items`);
                                resolve();
                            }
                        });
                    } else {
                        console.log(`Backfilled ${totalItemCount} items`);
                        resolve();
                    }
                });
                scan.ExclusiveStartKey = data.LastEvaluatedKey;
                resolve(scanTable(dynamodb, sourceTable, targetTable, type, startKey));
            }
        });
    });
}

async function main() {
    const args = yargs(hideBin(process.argv))
        .option('source-table-name', {
            type: 'string',
            describe: 'source table name',
            required: true,
        })
        .option('destination-table-name', {
            type: 'string',
            describe: 'destination table name',
            required: true,
        })
        .option('type', {
            type: 'string',
            describe: 'backfill date type',
            required: true,
        })
        .option('region', {
            type: 'string',
            describe: 'AWS region',
            default: 'us-east-1',
        })
        .option('start-key', {
            type: 'string',
            describe: 'Start key',
            default: undefined,
        })
        .help().argv;

    const dynamodb = new AWS.DynamoDB({ region: args.region });
    scanTable(dynamodb, args.sourceTableName, args.destinationTableName, args.type, args.startKey)
        .then(() => console.log('Table scan complete'))
        .catch(err => console.error(err));
}

main();