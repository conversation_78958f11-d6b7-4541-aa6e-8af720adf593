package api

import (
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// CreateChatRoom create new chat room
func (api *SocialPublicAPI) CreateChatRoom(w http.ResponseWriter, r *http.Request) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	api.CreateGroup(w, r)
}

// GetChatRooms get chat rooms
func (api *SocialPublicAPI) GetChatRooms(w http.ResponseWriter, r *http.Request, params apipub.GetGroupsParams) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	api.GetGroups(w, r, params)
}

// GetChatRoom get chat room
func (api *SocialPublicAPI) GetChatRoom(w http.ResponseWriter, r *http.Request, rm apipub.Roomid) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	var group *apipub.GroupResponse
	roomid := rm
	roomidParts := strings.Split(roomid, "/")
	if len(roomidParts) < 3 {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidGroupID))
		return
	}
	group, err := api.Cache.GetGroup(r.Context(), roomidParts[2], roomidParts[1])
	if err != nil {
		log.Error().Err(err).Msgf("failed to get chat room for group %s#%s and room %s", group.Productid, group.Groupid, roomid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Msgf("room %s was not found", rm)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EChatGroupNotFound))
		return
	}
	ReturnOK(w, r, group)
}

// UpdateChatRoom get chat room
func (api *SocialPublicAPI) UpdateChatRoom(w http.ResponseWriter, r *http.Request, rm apipub.Roomid) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	var group *apipub.GroupResponse
	roomid := rm
	roomidParts := strings.Split(roomid, "/")
	if len(roomidParts) < 3 {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EChatInvalidRoomID))
		return
	}
	group, err := api.Cache.GetGroup(r.Context(), roomidParts[2], roomidParts[1])
	if err != nil {
		log.Error().Err(err).Msgf("failed to get group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Msgf("room %s was not found", rm)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}
	ReturnEmptyOK(w, r)
}

// SendChatMessage send chat room messages
func (api *SocialPublicAPI) SendChatMessage(w http.ResponseWriter, r *http.Request, rm apipub.Roomid) {
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	roomid := rm

	// decode request
	var requestBody apipub.SendChatMessageJSONRequestBody
	if !DecodeBody(w, r, &requestBody) {
		return
	}

	message := requestBody.Message
	if message.Body == nil || len(message.Body) == 0 {
		errs.Return(w, r, errs.New(http.StatusBadRequest, errs.EChatMessageBodyRequired))
		return
	}

	topic := ""
	var messageType messenger.MqttMessageType
	// If message type is group, validate group membership
	if message.Type == apipub.Group {
		group, err := api.Cache.GetGroup(r.Context(), message.TargetId, message.ProductId)
		if err != nil {
			log.Error().Err(err).Msgf("failed to get group")
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EChatGroupNotFound))
			return
		}
		if group == nil {
			log.Error().Msgf("did not find room %s", roomid)
			errs.Return(w, r, errs.New(http.StatusNotFound, errs.EChatGroupNotFound))
			return
		}
		topic = group.Topic(tenant)
		messageType = messenger.MqttMessageTypeChatMessageGroup
	} else {
		// 1on1
		topic = fmt.Sprintf("%s/user/%s", tenant, message.TargetId)
		messageType = messenger.MqttMessageTypeChatMessageDM
	}

	err := messenger.SendMqttMessage(r.Context(), api.Cfg, topic, messageType, &message.Body)
	if err != nil {
		log.Error().Err(err).Msgf("failed to send mqtt message")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EChatMessagePublishFailed))
		return
	}

	// We only store 1on1 messages
	if message.Type == apipub.N1on1 {
		err = api.Ds.SaveChatMessage(r.Context(), &message)
		if err != nil {
			log.Error().Err(err).Msgf("failed to add chat message")
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
			return
		}

		err = api.Cache.SetChatMessage(r.Context(), tenant, &message, time.Duration(api.Cfg.TtlChatMessage)*time.Second)
		if err != nil {
			log.Error().Err(err).Msgf("failed to set chat message")
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed))
			return
		}
	}

	ReturnEmptyOK(w, r)
}

// GetChatRoomMembers get chat room members
func (api *SocialPublicAPI) GetChatRoomMembers(w http.ResponseWriter, r *http.Request, rm apipub.Roomid) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	roomid := rm
	userid := token.Claims.Subject

	var group *apipub.GroupResponse
	roomidParts := strings.Split(roomid, "/")
	if len(roomidParts) < 3 {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidGroupID))
		return
	}
	group, err := api.Cache.GetGroup(r.Context(), roomidParts[2], roomidParts[1])
	if err != nil {
		log.Error().Err(err).Msgf("failed to get group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Msgf("could not find room %s", rm)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EChatGroupNotFound))
		return
	}

	if !group.IsMember(userid) {
		log.Error().Err(err).Msgf("user %s is not a member of the group", userid)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMemberNotInGroup))
		return
	}

	api.GetGroupMembers(w, r, group.Groupid)
}

// JoinChatRoom join chat room
func (api *SocialPublicAPI) JoinChatRoom(w http.ResponseWriter, r *http.Request, rm apipub.Roomid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	roomid := rm
	userid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	roomidParts := strings.Split(roomid, "/")
	if len(roomidParts) < 3 {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidGroupID))
		return
	}
	group, err := api.Cache.GetGroup(r.Context(), roomidParts[2], roomidParts[1])
	if group == nil || err != nil {
		api.Ds.DeleteItemByPkSk(r.Context(), "user#"+userid, "group-invite#"+roomid)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	membershipRequest := apipub.MembershipRequest{
		Groupid:   group.Groupid,
		Productid: &group.Productid,
	}

	//Join chatroom if auto approved
	if group.JoinRequestAction == apipub.AutoApprove {
		membershipRequest.Approverid = userid
		membershipRequest.Status = apipub.Approved
		api.JoinGroupFinalizeHelper(r, group, appid, ost, token, "", "", nil)
		ReturnEmptyOK(w, r)
		return
	} else if group.JoinRequestAction == apipub.Manual {
		// private group: request to join
		membershipRequest.Status = apipub.Requested
		group.AddMembershipIfNotExist(&membershipRequest)
	}
	//TODO: How to handle chat rooms when group is auto-reject
}

// GetChatRoomMember get chat room member
func (api *SocialPublicAPI) GetChatRoomMember(w http.ResponseWriter, r *http.Request, rm apipub.Roomid, userid apipub.PUserid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	roomid := rm
	roomidParts := strings.Split(roomid, "/")

	api.GetGroupMember(w, r, roomidParts[2], userid)
}

// KickOrLeaveChatRoom kick/leave chat room
func (api *SocialPublicAPI) KickOrLeaveChatRoom(w http.ResponseWriter, r *http.Request, rm apipub.Roomid, userid apipub.PUserid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	productid := token.Claims.ProductID
	requestorid := token.Claims.Subject
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	roomid := rm
	roomidParts := strings.Split(roomid, "/")

	api.KickOrLeaveGroup(w, r, roomidParts[2], requestorid, userid, nil, productid, appid, sessionid, ost, createdTime, expiresTime)
}
