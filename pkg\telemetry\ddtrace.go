package telemetry

import (
	"context"
	"net/http/httptrace"

	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	awsMiddleware "github.com/aws/aws-sdk-go-v2/aws/middleware"
	"github.com/aws/aws-sdk-go-v2/aws/retry"
	"github.com/aws/smithy-go/middleware"
	"github.com/aws/smithy-go/transport/http"
	"github.com/rs/zerolog/log"
)

const (
	tagAWSAgent        = "aws.agent"
	tagAWSOperation    = "aws.operation"
	tagAWSRegion       = "aws.region"
	tagAWSRetryCount   = "aws.retry_count"
	tagAWSRequestID    = "aws.request_id"
	tagHTTPQueryString = "http.query_string"
)

type OnComplete struct {
	count int
}

type OnBuild struct{}

var _ middleware.FinalizeMiddleware = (*OnComplete)(nil)
var _ middleware.SerializeMiddleware = (*OnBuild)(nil)

func (*OnComplete) ID() string {
	return "OnComplete"
}

func (*OnBuild) ID() string {
	return "OnBuild"
}

func (r *OnComplete) HandleFinalize(ctx context.Context, in middleware.FinalizeInput, next middleware.FinalizeHandler) (
	out middleware.FinalizeOutput, metadata middleware.Metadata, err error) {
	out, metadata, err = next.HandleFinalize(ctx, in)
	request, ok := in.Request.(*http.Request)
	if request != nil && ok {
		if err != nil {
			r.count++
			op := middleware.GetOperationName(ctx)
			serviceId := middleware.GetServiceID(ctx)
			log.Err(err).Msgf("Retry %s.%s. Attempt: %d", serviceId, op, r.count)
		}

		span, ok := tracer.SpanFromContext(ctx)
		if !ok {
			return
		}

		requestId, ok := awsMiddleware.GetRequestIDMetadata(metadata)
		if ok {
			span.SetTag(tagAWSRequestID, requestId)
		}

		rawResponse, ok := awsMiddleware.GetRawResponse(metadata).(*http.Response)
		if rawResponse != nil && ok {
			span.SetTag(ext.HTTPCode, rawResponse.StatusCode)
		}

		retry, ok := retry.GetAttemptResults(metadata)
		if ok {
			span.SetTag(tagAWSRetryCount, len(retry.Results))
		}
		span.Finish(tracer.WithError(err))
	}

	return out, metadata, err
}

func (*OnBuild) HandleSerialize(ctx context.Context, in middleware.SerializeInput, next middleware.SerializeHandler) (
	out middleware.SerializeOutput, metadata middleware.Metadata, err error) {
	opts := []ddtrace.StartSpanOption{
		tracer.SpanType(ext.SpanTypeHTTP),
	}

	op := middleware.GetOperationName(ctx)
	serviceId := middleware.GetServiceID(ctx)
	request, ok := in.Request.(*http.Request)
	if request != nil && ok {
		tracer.ServiceName(serviceName(serviceId))
		tracer.ResourceName(resourceName(serviceId, op))
		tracer.Tag(tagAWSAgent, awsAgent(request))
		tracer.Tag(ext.HTTPMethod, request.Method)
		tracer.Tag(tagAWSOperation, operationName(serviceId))
		tracer.Tag(ext.HTTPURL, request.URL.String())
		tracer.Tag(tagHTTPQueryString, request.URL.RawQuery)

		span, _ := tracer.StartSpanFromContext(ctx, resourceName(serviceId, op), opts...)
		ctx = tracer.ContextWithSpan(ctx, span)
	}

	return next.HandleSerialize(ctx, in)
}

// Keep the following section for reference
// type handlers struct {}
//
// func TraceAWSSession(s *session.Session) *session.Session {
// 	h := &handlers{}
// 	s = s.Copy()
// 	s.Handlers.Send.PushFrontNamed(request.NamedHandler{
// 		Name: "github.com/take-two-t2gp/t2gp-social-service/pkg/store/handlers.Send",
// 		Fn:   h.Send,
// 	})
// 	s.Handlers.Complete.PushBackNamed(request.NamedHandler{
// 		Name: "github.com/take-two-t2gp/t2gp-social-service/pkg/store/handlers.Complete",
// 		Fn:   h.Complete,
// 	})
// 	return s
// }

// func (h *handlers) Send(req *request.Request) {
// 	if req.RetryCount != 0 {
// 		return
// 	}
// 	opts := []ddtrace.StartSpanOption{
// 		tracer.SpanType(ext.SpanTypeHTTP),
// 		tracer.ServiceName(h.serviceName(req)),
// 		tracer.ResourceName(h.resourceName(req)),
// 		tracer.Tag(tagAWSAgent, h.awsAgent(req)),
// 		tracer.Tag(tagAWSOperation, h.awsOperation(req)),
// 		tracer.Tag(tagAWSRegion, h.awsRegion(req)),
// 		tracer.Tag(ext.HTTPMethod, req.Operation.HTTPMethod),
// 		tracer.Tag(ext.HTTPURL, req.HTTPRequest.URL.String()),
// 	}
// 	if req.HTTPRequest.URL.RawQuery != "" {
// 		opts = append(opts, tracer.Tag(tagHTTPQueryString, req.HTTPRequest.URL.RawQuery))
// 	}
// 	_, ctx := tracer.StartSpanFromContext(req.Context(), h.operationName(req), opts...)
// 	req.SetContext(WithHttpTrace(ctx))
// }

// func (h *handlers) Complete(req *request.Request) {
// 	span, ok := tracer.SpanFromContext(req.Context())
// 	if !ok {
// 		return
// 	}
// 	span.SetTag(tagAWSRetryCount, req.RetryCount)
// 	span.SetTag(tagAWSRequestID, req.RequestID)
// 	if req.HTTPResponse != nil {
// 		span.SetTag(ext.HTTPCode, strconv.Itoa(req.HTTPResponse.StatusCode))
// 	}
// 	span.Finish(tracer.WithError(req.Error))
// }

func operationName(service string) string {
	return service + ".command"
}

func resourceName(service, operation string) string {
	return service + "." + operation
}

func serviceName(service string) string {
	if service != "" {
		return "aws." + service
	}
	return "aws.Unknown"
}

// func (h *handlers) awsAgent(req *request.Request) string {
// 	if agent := req.HTTPRequest.Header.Get("User-Agent"); agent != "" {
// 		return agent
// 	}
// 	return "aws-sdk-go"
// }

// func (h *handlers) awsOperation(req *request.Request) string {
// 	return req.Operation.Name
// }

// func (h *handlers) awsRegion(req *request.Request) string {
// 	return req.ClientInfo.SigningRegion
// }

// func (h *handlers) awsService(req *request.Request) string {
// 	return req.ClientInfo.ServiceName
// }

func awsAgent(request *http.Request) string {
	if agent := request.Header.Get("User-Agent"); agent != "" {
		return agent
	}
	return "aws-sdk-go"
}

type traceContext struct {
	dnsLookupSpan tracer.Span
	connectSpan   tracer.Span
}

// WithHttpTrace creates a context w/ httptrace dns lookup
func WithHttpTrace(ctx context.Context) context.Context {
	t := traceContext{}
	trace := &httptrace.ClientTrace{
		DNSStart: func(startInfo httptrace.DNSStartInfo) {
			t.dnsLookupSpan, _ = tracer.StartSpanFromContext(ctx, "dns.lookup",
				tracer.SpanType(ext.SpanTypeDNS),
				tracer.ResourceName(startInfo.Host),
			)
		},
		DNSDone: func(dnsInfo httptrace.DNSDoneInfo) {
			s := t.dnsLookupSpan
			if s != nil {
				s.SetTag("ips", dnsInfo.Addrs)
				s.Finish()
			}
		},
		ConnectStart: func(_, addr string) {
			t.connectSpan, _ = tracer.StartSpanFromContext(ctx, "tcp.connect",
				tracer.ResourceName(addr))
		},
		ConnectDone: func(_, _ string, err error) {
			s := t.connectSpan
			if s != nil {
				if err != nil {
					s.Finish(tracer.WithError(err))
				} else {
					s.Finish()
				}
			}
		},
	}
	return httptrace.WithClientTrace(ctx, trace)
}
