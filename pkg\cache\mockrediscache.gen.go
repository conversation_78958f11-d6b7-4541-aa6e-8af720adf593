// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/cache/rediscache.go

// Package cache is a generated GoMock package.
package cache

import (
	context "context"
	reflect "reflect"
	time "time"

	redislock "github.com/bsm/redislock"
	gomock "go.uber.org/mock/gomock"
	redis "github.com/redis/go-redis/v9"
	apipub "github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	apitrusted "github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	errs "github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	health "github.com/take-two-t2gp/t2gp-social-service/pkg/health"
)

// MockRedisCacheInterface is a mock of RedisCacheInterface interface.
type MockRedisCacheInterface struct {
	ctrl     *gomock.Controller
	recorder *MockRedisCacheInterfaceMockRecorder
}

// MockRedisCacheInterfaceMockRecorder is the mock recorder for MockRedisCacheInterface.
type MockRedisCacheInterfaceMockRecorder struct {
	mock *MockRedisCacheInterface
}

// NewMockRedisCacheInterface creates a new mock instance.
func NewMockRedisCacheInterface(ctrl *gomock.Controller) *MockRedisCacheInterface {
	mock := &MockRedisCacheInterface{ctrl: ctrl}
	mock.recorder = &MockRedisCacheInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRedisCacheInterface) EXPECT() *MockRedisCacheInterfaceMockRecorder {
	return m.recorder
}

// AddGroupMember mocks base method.
func (m *MockRedisCacheInterface) AddGroupMember(ctx context.Context, group *apipub.GroupResponse, member *apipub.GroupMemberResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddGroupMember", ctx, group, member)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddGroupMember indicates an expected call of AddGroupMember.
func (mr *MockRedisCacheInterfaceMockRecorder) AddGroupMember(ctx, group, member interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddGroupMember", reflect.TypeOf((*MockRedisCacheInterface)(nil).AddGroupMember), ctx, group, member)
}

// AddMembershipRequestToGroup mocks base method.
func (m *MockRedisCacheInterface) AddMembershipRequestToGroup(ctx context.Context, group *apipub.GroupResponse, membership apipub.MembershipRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddMembershipRequestToGroup", ctx, group, membership)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddMembershipRequestToGroup indicates an expected call of AddMembershipRequestToGroup.
func (mr *MockRedisCacheInterfaceMockRecorder) AddMembershipRequestToGroup(ctx, group, membership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddMembershipRequestToGroup", reflect.TypeOf((*MockRedisCacheInterface)(nil).AddMembershipRequestToGroup), ctx, group, membership)
}

// AddToUserBlockList mocks base method.
func (m *MockRedisCacheInterface) AddToUserBlockList(ctx context.Context, blocklist *[]*apipub.BlocklistResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddToUserBlockList", ctx, blocklist, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddToUserBlockList indicates an expected call of AddToUserBlockList.
func (mr *MockRedisCacheInterfaceMockRecorder) AddToUserBlockList(ctx, blocklist, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddToUserBlockList", reflect.TypeOf((*MockRedisCacheInterface)(nil).AddToUserBlockList), ctx, blocklist, ttl)
}

// CachedObjExists mocks base method.
func (m *MockRedisCacheInterface) CachedObjExists(ctx context.Context, key string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CachedObjExists", ctx, key)
	ret0, _ := ret[0].(bool)
	return ret0
}

// CachedObjExists indicates an expected call of CachedObjExists.
func (mr *MockRedisCacheInterfaceMockRecorder) CachedObjExists(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CachedObjExists", reflect.TypeOf((*MockRedisCacheInterface)(nil).CachedObjExists), ctx, key)
}

// CheckHealth mocks base method.
func (m *MockRedisCacheInterface) CheckHealth() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckHealth")
	ret0, _ := ret[0].(bool)
	return ret0
}

// CheckHealth indicates an expected call of CheckHealth.
func (mr *MockRedisCacheInterfaceMockRecorder) CheckHealth() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckHealth", reflect.TypeOf((*MockRedisCacheInterface)(nil).CheckHealth))
}

// ClearAllMemberships mocks base method.
func (m *MockRedisCacheInterface) ClearAllMemberships(ctx context.Context, memberid, productid, groupid string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "ClearAllMemberships", ctx, memberid, productid, groupid)
}

// ClearAllMemberships indicates an expected call of ClearAllMemberships.
func (mr *MockRedisCacheInterfaceMockRecorder) ClearAllMemberships(ctx, memberid, productid, groupid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearAllMemberships", reflect.TypeOf((*MockRedisCacheInterface)(nil).ClearAllMemberships), ctx, memberid, productid, groupid)
}

// ClearFriendlist mocks base method.
func (m *MockRedisCacheInterface) ClearFriendlist(ctx context.Context, userid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearFriendlist", ctx, userid)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearFriendlist indicates an expected call of ClearFriendlist.
func (mr *MockRedisCacheInterfaceMockRecorder) ClearFriendlist(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearFriendlist", reflect.TypeOf((*MockRedisCacheInterface)(nil).ClearFriendlist), ctx, userid)
}

// ClearUserBlocklist mocks base method.
func (m *MockRedisCacheInterface) ClearUserBlocklist(ctx context.Context, userid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClearUserBlocklist", ctx, userid)
	ret0, _ := ret[0].(error)
	return ret0
}

// ClearUserBlocklist indicates an expected call of ClearUserBlocklist.
func (mr *MockRedisCacheInterfaceMockRecorder) ClearUserBlocklist(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClearUserBlocklist", reflect.TypeOf((*MockRedisCacheInterface)(nil).ClearUserBlocklist), ctx, userid)
}

// CountFriendlistMembers mocks base method.
func (m *MockRedisCacheInterface) CountFriendlistMembers(ctx context.Context, userid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountFriendlistMembers", ctx, userid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountFriendlistMembers indicates an expected call of CountFriendlistMembers.
func (mr *MockRedisCacheInterfaceMockRecorder) CountFriendlistMembers(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountFriendlistMembers", reflect.TypeOf((*MockRedisCacheInterface)(nil).CountFriendlistMembers), ctx, userid)
}

// CountUserGroups mocks base method.
func (m *MockRedisCacheInterface) CountUserGroups(ctx context.Context, userid, productid string) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountUserGroups", ctx, userid, productid)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountUserGroups indicates an expected call of CountUserGroups.
func (mr *MockRedisCacheInterfaceMockRecorder) CountUserGroups(ctx, userid, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountUserGroups", reflect.TypeOf((*MockRedisCacheInterface)(nil).CountUserGroups), ctx, userid, productid)
}

// DelRangeTsClientId mocks base method.
func (m *MockRedisCacheInterface) DelRangeTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelRangeTsClientId", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelRangeTsClientId indicates an expected call of DelRangeTsClientId.
func (mr *MockRedisCacheInterfaceMockRecorder) DelRangeTsClientId(ctx, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRangeTsClientId", reflect.TypeOf((*MockRedisCacheInterface)(nil).DelRangeTsClientId), ctx, info)
}

// DelRecentlyPlayedUsers mocks base method.
func (m *MockRedisCacheInterface) DelRecentlyPlayedUsers(ctx context.Context, productid, userid string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "DelRecentlyPlayedUsers", ctx, productid, userid)
}

// DelRecentlyPlayedUsers indicates an expected call of DelRecentlyPlayedUsers.
func (mr *MockRedisCacheInterfaceMockRecorder) DelRecentlyPlayedUsers(ctx, productid, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelRecentlyPlayedUsers", reflect.TypeOf((*MockRedisCacheInterface)(nil).DelRecentlyPlayedUsers), ctx, productid, userid)
}

// DelTsClientId mocks base method.
func (m *MockRedisCacheInterface) DelTsClientId(ctx context.Context, clientid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTsClientId", ctx, clientid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelTsClientId indicates an expected call of DelTsClientId.
func (mr *MockRedisCacheInterfaceMockRecorder) DelTsClientId(ctx, clientid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTsClientId", reflect.TypeOf((*MockRedisCacheInterface)(nil).DelTsClientId), ctx, clientid)
}

// DeleteCachedObj mocks base method.
func (m *MockRedisCacheInterface) DeleteCachedObj(ctx context.Context, key string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteCachedObj", ctx, key)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteCachedObj indicates an expected call of DeleteCachedObj.
func (mr *MockRedisCacheInterfaceMockRecorder) DeleteCachedObj(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCachedObj", reflect.TypeOf((*MockRedisCacheInterface)(nil).DeleteCachedObj), ctx, key)
}

// DeleteFriend mocks base method.
func (m *MockRedisCacheInterface) DeleteFriend(ctx context.Context, friend *apipub.FriendResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteFriend", ctx, friend)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteFriend indicates an expected call of DeleteFriend.
func (mr *MockRedisCacheInterfaceMockRecorder) DeleteFriend(ctx, friend interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFriend", reflect.TypeOf((*MockRedisCacheInterface)(nil).DeleteFriend), ctx, friend)
}

// DeleteGroup mocks base method.
func (m *MockRedisCacheInterface) DeleteGroup(ctx context.Context, group *apipub.GroupResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteGroup", ctx, group)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteGroup indicates an expected call of DeleteGroup.
func (mr *MockRedisCacheInterfaceMockRecorder) DeleteGroup(ctx, group interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteGroup", reflect.TypeOf((*MockRedisCacheInterface)(nil).DeleteGroup), ctx, group)
}

// DeleteMembership mocks base method.
func (m *MockRedisCacheInterface) DeleteMembership(ctx context.Context, request *apipub.MembershipRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMembership", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMembership indicates an expected call of DeleteMembership.
func (mr *MockRedisCacheInterfaceMockRecorder) DeleteMembership(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMembership", reflect.TypeOf((*MockRedisCacheInterface)(nil).DeleteMembership), ctx, request)
}

// DeletePresence mocks base method.
func (m *MockRedisCacheInterface) DeletePresence(ctx context.Context, presence *apipub.PresenceResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePresence", ctx, presence)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePresence indicates an expected call of DeletePresence.
func (mr *MockRedisCacheInterfaceMockRecorder) DeletePresence(ctx, presence interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePresence", reflect.TypeOf((*MockRedisCacheInterface)(nil).DeletePresence), ctx, presence)
}

// DeleteUserProfile mocks base method.
func (m *MockRedisCacheInterface) DeleteUserProfile(ctx context.Context, userid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUserProfile", ctx, userid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUserProfile indicates an expected call of DeleteUserProfile.
func (mr *MockRedisCacheInterfaceMockRecorder) DeleteUserProfile(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserProfile", reflect.TypeOf((*MockRedisCacheInterface)(nil).DeleteUserProfile), ctx, userid)
}

// DoesBlockerBlockBlockee mocks base method.
func (m *MockRedisCacheInterface) DoesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoesBlockerBlockBlockee", ctx, blockerid, blockeeid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*errs.Error)
	return ret0, ret1
}

// DoesBlockerBlockBlockee indicates an expected call of DoesBlockerBlockBlockee.
func (mr *MockRedisCacheInterfaceMockRecorder) DoesBlockerBlockBlockee(ctx, blockerid, blockeeid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoesBlockerBlockBlockee", reflect.TypeOf((*MockRedisCacheInterface)(nil).DoesBlockerBlockBlockee), ctx, blockerid, blockeeid)
}

// FriendlistExistsInCache mocks base method.
func (m *MockRedisCacheInterface) FriendlistExistsInCache(ctx context.Context, userid string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FriendlistExistsInCache", ctx, userid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// FriendlistExistsInCache indicates an expected call of FriendlistExistsInCache.
func (mr *MockRedisCacheInterfaceMockRecorder) FriendlistExistsInCache(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FriendlistExistsInCache", reflect.TypeOf((*MockRedisCacheInterface)(nil).FriendlistExistsInCache), ctx, userid)
}

// GetEndorsement mocks base method.
func (m *MockRedisCacheInterface) GetEndorsement(ctx context.Context, userid, productid, endorsementName string) (*apipub.EndorsementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEndorsement", ctx, userid, productid, endorsementName)
	ret0, _ := ret[0].(*apipub.EndorsementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEndorsement indicates an expected call of GetEndorsement.
func (mr *MockRedisCacheInterfaceMockRecorder) GetEndorsement(ctx, userid, productid, endorsementName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndorsement", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetEndorsement), ctx, userid, productid, endorsementName)
}

// GetEndorsements mocks base method.
func (m *MockRedisCacheInterface) GetEndorsements(ctx context.Context, userid, productid string) (*[]*apipub.EndorsementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEndorsements", ctx, userid, productid)
	ret0, _ := ret[0].(*[]*apipub.EndorsementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEndorsements indicates an expected call of GetEndorsements.
func (mr *MockRedisCacheInterfaceMockRecorder) GetEndorsements(ctx, userid, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndorsements", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetEndorsements), ctx, userid, productid)
}

// GetFirstPartyLookup mocks base method.
func (m *MockRedisCacheInterface) GetFirstPartyLookup(ctx context.Context, fpid string, ost apipub.OnlineServiceType) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFirstPartyLookup", ctx, fpid, ost)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFirstPartyLookup indicates an expected call of GetFirstPartyLookup.
func (mr *MockRedisCacheInterfaceMockRecorder) GetFirstPartyLookup(ctx, fpid, ost interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFirstPartyLookup", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetFirstPartyLookup), ctx, fpid, ost)
}

// GetFriend mocks base method.
func (m *MockRedisCacheInterface) GetFriend(ctx context.Context, userid, friendid string) (*apipub.FriendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFriend", ctx, userid, friendid)
	ret0, _ := ret[0].(*apipub.FriendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFriend indicates an expected call of GetFriend.
func (mr *MockRedisCacheInterfaceMockRecorder) GetFriend(ctx, userid, friendid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFriend", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetFriend), ctx, userid, friendid)
}

// GetFriends mocks base method.
func (m *MockRedisCacheInterface) GetFriends(ctx context.Context, userid string, status *apipub.FriendStatus, limit *int64, next *string) (*[]*apipub.FriendResponse, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFriends", ctx, userid, status, limit, next)
	ret0, _ := ret[0].(*[]*apipub.FriendResponse)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetFriends indicates an expected call of GetFriends.
func (mr *MockRedisCacheInterfaceMockRecorder) GetFriends(ctx, userid, status, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFriends", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetFriends), ctx, userid, status, limit, next)
}

// GetGroup mocks base method.
func (m *MockRedisCacheInterface) GetGroup(ctx context.Context, groupid, productid string) (*apipub.GroupResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroup", ctx, groupid, productid)
	ret0, _ := ret[0].(*apipub.GroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroup indicates an expected call of GetGroup.
func (mr *MockRedisCacheInterfaceMockRecorder) GetGroup(ctx, groupid, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroup", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetGroup), ctx, groupid, productid)
}

// GetGroupMembers mocks base method.
func (m *MockRedisCacheInterface) GetGroupMembers(ctx context.Context, productid, groupid string) (*[]apipub.GroupMemberResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupMembers", ctx, productid, groupid)
	ret0, _ := ret[0].(*[]apipub.GroupMemberResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupMembers indicates an expected call of GetGroupMembers.
func (mr *MockRedisCacheInterfaceMockRecorder) GetGroupMembers(ctx, productid, groupid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupMembers", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetGroupMembers), ctx, productid, groupid)
}

// GetGroupTTL mocks base method.
func (m *MockRedisCacheInterface) GetGroupTTL(ctx context.Context, productid, groupid string) (time.Duration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetGroupTTL", ctx, productid, groupid)
	ret0, _ := ret[0].(time.Duration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetGroupTTL indicates an expected call of GetGroupTTL.
func (mr *MockRedisCacheInterfaceMockRecorder) GetGroupTTL(ctx, productid, groupid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetGroupTTL", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetGroupTTL), ctx, productid, groupid)
}

// GetInvite mocks base method.
func (m *MockRedisCacheInterface) GetInvite(ctx context.Context, memberid, productid, groupid, approverid string, isFirstParty *bool) (*apipub.MembershipRequest, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvite", ctx, memberid, productid, groupid, approverid, isFirstParty)
	ret0, _ := ret[0].(*apipub.MembershipRequest)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInvite indicates an expected call of GetInvite.
func (mr *MockRedisCacheInterfaceMockRecorder) GetInvite(ctx, memberid, productid, groupid, approverid, isFirstParty interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvite", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetInvite), ctx, memberid, productid, groupid, approverid, isFirstParty)
}

// GetInvitesForUser mocks base method.
func (m *MockRedisCacheInterface) GetInvitesForUser(ctx context.Context, memberid, productid string, limit *int64, next *string) (*[]*apipub.MembershipRequest, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInvitesForUser", ctx, memberid, productid, limit, next)
	ret0, _ := ret[0].(*[]*apipub.MembershipRequest)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetInvitesForUser indicates an expected call of GetInvitesForUser.
func (mr *MockRedisCacheInterfaceMockRecorder) GetInvitesForUser(ctx, memberid, productid, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInvitesForUser", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetInvitesForUser), ctx, memberid, productid, limit, next)
}

// GetJoinRequestsByApproverId mocks base method.
func (m *MockRedisCacheInterface) GetJoinRequestsByApproverId(ctx context.Context, approverid, productid string, limit *int64, next *string) (*[]*apipub.MembershipRequest, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetJoinRequestsByApproverId", ctx, approverid, productid, limit, next)
	ret0, _ := ret[0].(*[]*apipub.MembershipRequest)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetJoinRequestsByApproverId indicates an expected call of GetJoinRequestsByApproverId.
func (mr *MockRedisCacheInterfaceMockRecorder) GetJoinRequestsByApproverId(ctx, approverid, productid, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetJoinRequestsByApproverId", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetJoinRequestsByApproverId), ctx, approverid, productid, limit, next)
}

// GetLowestAvailablePriority mocks base method.
func (m *MockRedisCacheInterface) GetLowestAvailablePriority(ctx context.Context, userid, productid string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetLowestAvailablePriority", ctx, userid, productid)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetLowestAvailablePriority indicates an expected call of GetLowestAvailablePriority.
func (mr *MockRedisCacheInterfaceMockRecorder) GetLowestAvailablePriority(ctx, userid, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetLowestAvailablePriority", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetLowestAvailablePriority), ctx, userid, productid)
}

// GetPresence mocks base method.
func (m *MockRedisCacheInterface) GetPresence(ctx context.Context, userid, productid, sessionid string) (*apipub.PresenceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPresence", ctx, userid, productid, sessionid)
	ret0, _ := ret[0].(*apipub.PresenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPresence indicates an expected call of GetPresence.
func (mr *MockRedisCacheInterfaceMockRecorder) GetPresence(ctx, userid, productid, sessionid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPresence", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetPresence), ctx, userid, productid, sessionid)
}

// GetProductIdFromAppId mocks base method.
func (m *MockRedisCacheInterface) GetProductIdFromAppId(ctx context.Context, appid string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductIdFromAppId", ctx, appid)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductIdFromAppId indicates an expected call of GetProductIdFromAppId.
func (mr *MockRedisCacheInterfaceMockRecorder) GetProductIdFromAppId(ctx, appid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductIdFromAppId", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetProductIdFromAppId), ctx, appid)
}

// GetProductIdToName mocks base method.
func (m *MockRedisCacheInterface) GetProductIdToName(ctx context.Context, tenantid, productid string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProductIdToName", ctx, tenantid, productid)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProductIdToName indicates an expected call of GetProductIdToName.
func (mr *MockRedisCacheInterfaceMockRecorder) GetProductIdToName(ctx, tenantid, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProductIdToName", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetProductIdToName), ctx, tenantid, productid)
}

// GetRecentlyPlayed mocks base method.
func (m *MockRedisCacheInterface) GetRecentlyPlayed(ctx context.Context, userid, productid string, limit *int64, next *string) (*[]*apipub.RecentlyPlayedUserResponse, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRecentlyPlayed", ctx, userid, productid, limit, next)
	ret0, _ := ret[0].(*[]*apipub.RecentlyPlayedUserResponse)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetRecentlyPlayed indicates an expected call of GetRecentlyPlayed.
func (mr *MockRedisCacheInterfaceMockRecorder) GetRecentlyPlayed(ctx, userid, productid, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRecentlyPlayed", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetRecentlyPlayed), ctx, userid, productid, limit, next)
}

// GetSyncLock mocks base method.
func (m *MockRedisCacheInterface) GetSyncLock(ctx context.Context, key string, ttl time.Duration) *redislock.Lock {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSyncLock", ctx, key, ttl)
	ret0, _ := ret[0].(*redislock.Lock)
	return ret0
}

// GetSyncLock indicates an expected call of GetSyncLock.
func (mr *MockRedisCacheInterfaceMockRecorder) GetSyncLock(ctx, key, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSyncLock", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetSyncLock), ctx, key, ttl)
}

// GetTsClientId mocks base method.
func (m *MockRedisCacheInterface) GetTsClientId(ctx context.Context, clientid string) (*apitrusted.TsClientIdInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTsClientId", ctx, clientid)
	ret0, _ := ret[0].(*apitrusted.TsClientIdInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTsClientId indicates an expected call of GetTsClientId.
func (mr *MockRedisCacheInterfaceMockRecorder) GetTsClientId(ctx, clientid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTsClientId", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetTsClientId), ctx, clientid)
}

// GetUserBlocklist mocks base method.
func (m *MockRedisCacheInterface) GetUserBlocklist(ctx context.Context, userid string, limit *int64, next *string) (*[]*apipub.BlocklistResponse, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserBlocklist", ctx, userid, limit, next)
	ret0, _ := ret[0].(*[]*apipub.BlocklistResponse)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserBlocklist indicates an expected call of GetUserBlocklist.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserBlocklist(ctx, userid, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserBlocklist", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserBlocklist), ctx, userid, limit, next)
}

// GetUserCacheMeta mocks base method.
func (m *MockRedisCacheInterface) GetUserCacheMeta(ctx context.Context, userid string) (*apipub.UserCacheMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserCacheMeta", ctx, userid)
	ret0, _ := ret[0].(*apipub.UserCacheMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserCacheMeta indicates an expected call of GetUserCacheMeta.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserCacheMeta(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserCacheMeta", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserCacheMeta), ctx, userid)
}

// GetUserFirstPartyRefresh mocks base method.
func (m *MockRedisCacheInterface) GetUserFirstPartyRefresh(ctx context.Context, firstPartyid string, ost int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFirstPartyRefresh", ctx, firstPartyid, ost)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFirstPartyRefresh indicates an expected call of GetUserFirstPartyRefresh.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserFirstPartyRefresh(ctx, firstPartyid, ost interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFirstPartyRefresh", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserFirstPartyRefresh), ctx, firstPartyid, ost)
}

// GetUserFirstPartyToken mocks base method.
func (m *MockRedisCacheInterface) GetUserFirstPartyToken(ctx context.Context, firstPartyid string, ost int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserFirstPartyToken", ctx, firstPartyid, ost)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserFirstPartyToken indicates an expected call of GetUserFirstPartyToken.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserFirstPartyToken(ctx, firstPartyid, ost interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserFirstPartyToken", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserFirstPartyToken), ctx, firstPartyid, ost)
}

// GetUserGroups mocks base method.
func (m *MockRedisCacheInterface) GetUserGroups(ctx context.Context, userid, productid string, limit *int64, next *string) (*[]*apipub.GroupResponse, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserGroups", ctx, userid, productid, limit, next)
	ret0, _ := ret[0].(*[]*apipub.GroupResponse)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetUserGroups indicates an expected call of GetUserGroups.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserGroups(ctx, userid, productid, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserGroups", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserGroups), ctx, userid, productid, limit, next)
}

// GetUserPresences mocks base method.
func (m *MockRedisCacheInterface) GetUserPresences(ctx context.Context, userid, product string) (*[]*apipub.PresenceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserPresences", ctx, userid, product)
	ret0, _ := ret[0].(*[]*apipub.PresenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserPresences indicates an expected call of GetUserPresences.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserPresences(ctx, userid, product interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserPresences", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserPresences), ctx, userid, product)
}

// GetUserProfile mocks base method.
func (m *MockRedisCacheInterface) GetUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", ctx, userid)
	ret0, _ := ret[0].(*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserProfile(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserProfile), ctx, userid)
}

// GetUserProfiles mocks base method.
func (m *MockRedisCacheInterface) GetUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfiles", ctx, userids)
	ret0, _ := ret[0].(*[]*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfiles indicates an expected call of GetUserProfiles.
func (mr *MockRedisCacheInterfaceMockRecorder) GetUserProfiles(ctx, userids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfiles", reflect.TypeOf((*MockRedisCacheInterface)(nil).GetUserProfiles), ctx, userids)
}

// IncrementEndorsement mocks base method.
func (m *MockRedisCacheInterface) IncrementEndorsement(ctx context.Context, userid, productid string, endorsement *apipub.EndorsementResponse, incrementValue int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementEndorsement", ctx, userid, productid, endorsement, incrementValue)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrementEndorsement indicates an expected call of IncrementEndorsement.
func (mr *MockRedisCacheInterfaceMockRecorder) IncrementEndorsement(ctx, userid, productid, endorsement, incrementValue interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementEndorsement", reflect.TypeOf((*MockRedisCacheInterface)(nil).IncrementEndorsement), ctx, userid, productid, endorsement, incrementValue)
}

// InviteExistsInCache mocks base method.
func (m *MockRedisCacheInterface) InviteExistsInCache(ctx context.Context, memberid, productid, groupid, approverid string, isFirstParty *bool) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InviteExistsInCache", ctx, memberid, productid, groupid, approverid, isFirstParty)
	ret0, _ := ret[0].(bool)
	return ret0
}

// InviteExistsInCache indicates an expected call of InviteExistsInCache.
func (mr *MockRedisCacheInterfaceMockRecorder) InviteExistsInCache(ctx, memberid, productid, groupid, approverid, isFirstParty interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InviteExistsInCache", reflect.TypeOf((*MockRedisCacheInterface)(nil).InviteExistsInCache), ctx, memberid, productid, groupid, approverid, isFirstParty)
}

// IsCritical mocks base method.
func (m *MockRedisCacheInterface) IsCritical() bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsCritical")
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsCritical indicates an expected call of IsCritical.
func (mr *MockRedisCacheInterfaceMockRecorder) IsCritical() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsCritical", reflect.TypeOf((*MockRedisCacheInterface)(nil).IsCritical))
}

// JoinRequestExistsInCache mocks base method.
func (m *MockRedisCacheInterface) JoinRequestExistsInCache(ctx context.Context, approverid, productid, groupid, memberid string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JoinRequestExistsInCache", ctx, approverid, productid, groupid, memberid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// JoinRequestExistsInCache indicates an expected call of JoinRequestExistsInCache.
func (mr *MockRedisCacheInterfaceMockRecorder) JoinRequestExistsInCache(ctx, approverid, productid, groupid, memberid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JoinRequestExistsInCache", reflect.TypeOf((*MockRedisCacheInterface)(nil).JoinRequestExistsInCache), ctx, approverid, productid, groupid, memberid)
}

// KickOrLeaveHelper mocks base method.
func (m *MockRedisCacheInterface) KickOrLeaveHelper(ctx context.Context, group *apipub.GroupResponse, requestorid, targetuserid string, reason *string) (apipub.ChatMessageEventType, bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "KickOrLeaveHelper", ctx, group, requestorid, targetuserid, reason)
	ret0, _ := ret[0].(apipub.ChatMessageEventType)
	ret1, _ := ret[1].(bool)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// KickOrLeaveHelper indicates an expected call of KickOrLeaveHelper.
func (mr *MockRedisCacheInterfaceMockRecorder) KickOrLeaveHelper(ctx, group, requestorid, targetuserid, reason interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "KickOrLeaveHelper", reflect.TypeOf((*MockRedisCacheInterface)(nil).KickOrLeaveHelper), ctx, group, requestorid, targetuserid, reason)
}

// LastStatus mocks base method.
func (m *MockRedisCacheInterface) LastStatus() *health.ServiceStatus {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LastStatus")
	ret0, _ := ret[0].(*health.ServiceStatus)
	return ret0
}

// LastStatus indicates an expected call of LastStatus.
func (mr *MockRedisCacheInterfaceMockRecorder) LastStatus() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LastStatus", reflect.TypeOf((*MockRedisCacheInterface)(nil).LastStatus))
}

// MakeFriend mocks base method.
func (m *MockRedisCacheInterface) MakeFriend(ctx context.Context, userid, friendid, message string, isUserBlocked bool, userOST apipub.OnlineServiceType, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeFriend", ctx, userid, friendid, message, isUserBlocked, userOST, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeFriend indicates an expected call of MakeFriend.
func (mr *MockRedisCacheInterfaceMockRecorder) MakeFriend(ctx, userid, friendid, message, isUserBlocked, userOST, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeFriend", reflect.TypeOf((*MockRedisCacheInterface)(nil).MakeFriend), ctx, userid, friendid, message, isUserBlocked, userOST, ttl)
}

// MakeUnfriend mocks base method.
func (m *MockRedisCacheInterface) MakeUnfriend(ctx context.Context, userid, friendid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeUnfriend", ctx, userid, friendid)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeUnfriend indicates an expected call of MakeUnfriend.
func (mr *MockRedisCacheInterfaceMockRecorder) MakeUnfriend(ctx, userid, friendid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeUnfriend", reflect.TypeOf((*MockRedisCacheInterface)(nil).MakeUnfriend), ctx, userid, friendid)
}

// RefreshPresenceKeepAlive mocks base method.
func (m *MockRedisCacheInterface) RefreshPresenceKeepAlive(ctx context.Context, presence *apipub.PresenceResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RefreshPresenceKeepAlive", ctx, presence)
	ret0, _ := ret[0].(error)
	return ret0
}

// RefreshPresenceKeepAlive indicates an expected call of RefreshPresenceKeepAlive.
func (mr *MockRedisCacheInterfaceMockRecorder) RefreshPresenceKeepAlive(ctx, presence interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RefreshPresenceKeepAlive", reflect.TypeOf((*MockRedisCacheInterface)(nil).RefreshPresenceKeepAlive), ctx, presence)
}

// RemoveEndorsement mocks base method.
func (m *MockRedisCacheInterface) RemoveEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveEndorsement", ctx, userid, productid, endorsementName)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveEndorsement indicates an expected call of RemoveEndorsement.
func (mr *MockRedisCacheInterfaceMockRecorder) RemoveEndorsement(ctx, userid, productid, endorsementName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveEndorsement", reflect.TypeOf((*MockRedisCacheInterface)(nil).RemoveEndorsement), ctx, userid, productid, endorsementName)
}

// RemoveFromUserBlockList mocks base method.
func (m *MockRedisCacheInterface) RemoveFromUserBlockList(ctx context.Context, blocklist *[]*apipub.BlocklistResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveFromUserBlockList", ctx, blocklist)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveFromUserBlockList indicates an expected call of RemoveFromUserBlockList.
func (mr *MockRedisCacheInterfaceMockRecorder) RemoveFromUserBlockList(ctx, blocklist interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveFromUserBlockList", reflect.TypeOf((*MockRedisCacheInterface)(nil).RemoveFromUserBlockList), ctx, blocklist)
}

// RemoveGroupMember mocks base method.
func (m *MockRedisCacheInterface) RemoveGroupMember(ctx context.Context, group *apipub.GroupResponse, memberid string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveGroupMember", ctx, group, memberid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveGroupMember indicates an expected call of RemoveGroupMember.
func (mr *MockRedisCacheInterfaceMockRecorder) RemoveGroupMember(ctx, group, memberid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveGroupMember", reflect.TypeOf((*MockRedisCacheInterface)(nil).RemoveGroupMember), ctx, group, memberid)
}

// RemoveMembershipRequestFromGroup mocks base method.
func (m *MockRedisCacheInterface) RemoveMembershipRequestFromGroup(ctx context.Context, group *apipub.GroupResponse, membership apipub.MembershipRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveMembershipRequestFromGroup", ctx, group, membership)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveMembershipRequestFromGroup indicates an expected call of RemoveMembershipRequestFromGroup.
func (mr *MockRedisCacheInterfaceMockRecorder) RemoveMembershipRequestFromGroup(ctx, group, membership interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveMembershipRequestFromGroup", reflect.TypeOf((*MockRedisCacheInterface)(nil).RemoveMembershipRequestFromGroup), ctx, group, membership)
}

// ResetEndorsement mocks base method.
func (m *MockRedisCacheInterface) ResetEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetEndorsement", ctx, userid, productid, endorsementName)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetEndorsement indicates an expected call of ResetEndorsement.
func (mr *MockRedisCacheInterfaceMockRecorder) ResetEndorsement(ctx, userid, productid, endorsementName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetEndorsement", reflect.TypeOf((*MockRedisCacheInterface)(nil).ResetEndorsement), ctx, userid, productid, endorsementName)
}

// SavePresence mocks base method.
func (m *MockRedisCacheInterface) SavePresence(ctx context.Context, presence *apipub.PresenceResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, shouldBroadcast bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SavePresence", ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, shouldBroadcast)
	ret0, _ := ret[0].(error)
	return ret0
}

// SavePresence indicates an expected call of SavePresence.
func (mr *MockRedisCacheInterfaceMockRecorder) SavePresence(ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, shouldBroadcast interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SavePresence", reflect.TypeOf((*MockRedisCacheInterface)(nil).SavePresence), ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, shouldBroadcast)
}

// SetActiveGroup mocks base method.
func (m *MockRedisCacheInterface) SetActiveGroup(ctx context.Context, group *apipub.GroupResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, onlyModifySizes bool) (*apipub.PresenceResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetActiveGroup", ctx, group, productid, appid, sessionid, userid, createdTime, expiresTime, onlyModifySizes)
	ret0, _ := ret[0].(*apipub.PresenceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetActiveGroup indicates an expected call of SetActiveGroup.
func (mr *MockRedisCacheInterfaceMockRecorder) SetActiveGroup(ctx, group, productid, appid, sessionid, userid, createdTime, expiresTime, onlyModifySizes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetActiveGroup", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetActiveGroup), ctx, group, productid, appid, sessionid, userid, createdTime, expiresTime, onlyModifySizes)
}

// SetAppIdProductId mocks base method.
func (m *MockRedisCacheInterface) SetAppIdProductId(ctx context.Context, appid, productid string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAppIdProductId", ctx, appid, productid, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAppIdProductId indicates an expected call of SetAppIdProductId.
func (mr *MockRedisCacheInterfaceMockRecorder) SetAppIdProductId(ctx, appid, productid, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAppIdProductId", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetAppIdProductId), ctx, appid, productid, ttl)
}

// SetChatMessage mocks base method.
func (m *MockRedisCacheInterface) SetChatMessage(ctx context.Context, tenant string, message *apipub.ChatMessage, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetChatMessage", ctx, tenant, message, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetChatMessage indicates an expected call of SetChatMessage.
func (mr *MockRedisCacheInterfaceMockRecorder) SetChatMessage(ctx, tenant, message, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetChatMessage", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetChatMessage), ctx, tenant, message, ttl)
}

// SetFirstPartyLookup mocks base method.
func (m *MockRedisCacheInterface) SetFirstPartyLookup(ctx context.Context, fpid string, ost apipub.OnlineServiceType, parentid string, ttl time.Duration) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFirstPartyLookup", ctx, fpid, ost, parentid, ttl)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SetFirstPartyLookup indicates an expected call of SetFirstPartyLookup.
func (mr *MockRedisCacheInterfaceMockRecorder) SetFirstPartyLookup(ctx, fpid, ost, parentid, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFirstPartyLookup", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetFirstPartyLookup), ctx, fpid, ost, parentid, ttl)
}

// SetFriend mocks base method.
func (m *MockRedisCacheInterface) SetFriend(ctx context.Context, friend *apipub.FriendResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetFriend", ctx, friend, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetFriend indicates an expected call of SetFriend.
func (mr *MockRedisCacheInterfaceMockRecorder) SetFriend(ctx, friend, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFriend", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetFriend), ctx, friend, ttl)
}

// SetFriends mocks base method.
func (m *MockRedisCacheInterface) SetFriends(ctx context.Context, friends *[]*apipub.FriendResponse, ttl time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetFriends", ctx, friends, ttl)
}

// SetFriends indicates an expected call of SetFriends.
func (mr *MockRedisCacheInterfaceMockRecorder) SetFriends(ctx, friends, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetFriends", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetFriends), ctx, friends, ttl)
}

// SetGroup mocks base method.
func (m *MockRedisCacheInterface) SetGroup(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroup", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroup indicates an expected call of SetGroup.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroup(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroup", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroup), ctx, group, ttl)
}

// SetGroupCanMembersInvite mocks base method.
func (m *MockRedisCacheInterface) SetGroupCanMembersInvite(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupCanMembersInvite", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupCanMembersInvite indicates an expected call of SetGroupCanMembersInvite.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroupCanMembersInvite(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupCanMembersInvite", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroupCanMembersInvite), ctx, group, ttl)
}

// SetGroupCompositionId mocks base method.
func (m *MockRedisCacheInterface) SetGroupCompositionId(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupCompositionId", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupCompositionId indicates an expected call of SetGroupCompositionId.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroupCompositionId(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupCompositionId", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroupCompositionId), ctx, group, ttl)
}

// SetGroupJoinRequestAction mocks base method.
func (m *MockRedisCacheInterface) SetGroupJoinRequestAction(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupJoinRequestAction", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupJoinRequestAction indicates an expected call of SetGroupJoinRequestAction.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroupJoinRequestAction(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupJoinRequestAction", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroupJoinRequestAction), ctx, group, ttl)
}

// SetGroupMaxMembers mocks base method.
func (m *MockRedisCacheInterface) SetGroupMaxMembers(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupMaxMembers", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupMaxMembers indicates an expected call of SetGroupMaxMembers.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroupMaxMembers(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupMaxMembers", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroupMaxMembers), ctx, group, ttl)
}

// SetGroupMeta mocks base method.
func (m *MockRedisCacheInterface) SetGroupMeta(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupMeta", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupMeta indicates an expected call of SetGroupMeta.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroupMeta(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupMeta", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroupMeta), ctx, group, ttl)
}

// SetGroupPassword mocks base method.
func (m *MockRedisCacheInterface) SetGroupPassword(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetGroupPassword", ctx, group, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetGroupPassword indicates an expected call of SetGroupPassword.
func (mr *MockRedisCacheInterfaceMockRecorder) SetGroupPassword(ctx, group, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetGroupPassword", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetGroupPassword), ctx, group, ttl)
}

// SetPresence mocks base method.
func (m *MockRedisCacheInterface) SetPresence(ctx context.Context, presence *apipub.PresenceResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPresence", ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPresence indicates an expected call of SetPresence.
func (mr *MockRedisCacheInterfaceMockRecorder) SetPresence(ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPresence", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetPresence), ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, ttl)
}

// SetProductIdToName mocks base method.
func (m *MockRedisCacheInterface) SetProductIdToName(ctx context.Context, tenant, productid, name string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetProductIdToName", ctx, tenant, productid, name)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetProductIdToName indicates an expected call of SetProductIdToName.
func (mr *MockRedisCacheInterfaceMockRecorder) SetProductIdToName(ctx, tenant, productid, name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetProductIdToName", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetProductIdToName), ctx, tenant, productid, name)
}

// SetRangeTsClientId mocks base method.
func (m *MockRedisCacheInterface) SetRangeTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRangeTsClientId", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRangeTsClientId indicates an expected call of SetRangeTsClientId.
func (mr *MockRedisCacheInterfaceMockRecorder) SetRangeTsClientId(ctx, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRangeTsClientId", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetRangeTsClientId), ctx, info)
}

// SetRecentlyPlayedUser mocks base method.
func (m *MockRedisCacheInterface) SetRecentlyPlayedUser(ctx context.Context, productid string, recentlyPlayed *apipub.RecentlyPlayedUserResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRecentlyPlayedUser", ctx, productid, recentlyPlayed, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRecentlyPlayedUser indicates an expected call of SetRecentlyPlayedUser.
func (mr *MockRedisCacheInterfaceMockRecorder) SetRecentlyPlayedUser(ctx, productid, recentlyPlayed, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRecentlyPlayedUser", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetRecentlyPlayedUser), ctx, productid, recentlyPlayed, ttl)
}

// SetRecentlyPlayedUsers mocks base method.
func (m *MockRedisCacheInterface) SetRecentlyPlayedUsers(ctx context.Context, productid string, recentlyPlayedUsers *[]*apipub.RecentlyPlayedUserResponse, ttl time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetRecentlyPlayedUsers", ctx, productid, recentlyPlayedUsers, ttl)
}

// SetRecentlyPlayedUsers indicates an expected call of SetRecentlyPlayedUsers.
func (mr *MockRedisCacheInterfaceMockRecorder) SetRecentlyPlayedUsers(ctx, productid, recentlyPlayedUsers, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRecentlyPlayedUsers", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetRecentlyPlayedUsers), ctx, productid, recentlyPlayedUsers, ttl)
}

// SetTsClientId mocks base method.
func (m *MockRedisCacheInterface) SetTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTsClientId", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTsClientId indicates an expected call of SetTsClientId.
func (mr *MockRedisCacheInterfaceMockRecorder) SetTsClientId(ctx, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTsClientId", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetTsClientId), ctx, info)
}

// SetUserCacheMeta mocks base method.
func (m *MockRedisCacheInterface) SetUserCacheMeta(ctx context.Context, userid string, meta *apipub.UserCacheMeta, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserCacheMeta", ctx, userid, meta, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserCacheMeta indicates an expected call of SetUserCacheMeta.
func (mr *MockRedisCacheInterfaceMockRecorder) SetUserCacheMeta(ctx, userid, meta, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserCacheMeta", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetUserCacheMeta), ctx, userid, meta, ttl)
}

// SetUserFirstPartyToken mocks base method.
func (m *MockRedisCacheInterface) SetUserFirstPartyToken(ctx context.Context, firstPartyid string, ost int, token string, ttl time.Duration, refreshToken string, refreshTokenTtl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserFirstPartyToken", ctx, firstPartyid, ost, token, ttl, refreshToken, refreshTokenTtl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserFirstPartyToken indicates an expected call of SetUserFirstPartyToken.
func (mr *MockRedisCacheInterfaceMockRecorder) SetUserFirstPartyToken(ctx, firstPartyid, ost, token, ttl, refreshToken, refreshTokenTtl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserFirstPartyToken", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetUserFirstPartyToken), ctx, firstPartyid, ost, token, ttl, refreshToken, refreshTokenTtl)
}

// SetUserGroupIdxs mocks base method.
func (m *MockRedisCacheInterface) SetUserGroupIdxs(ctx context.Context, userid string, group *apipub.GroupResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserGroupIdxs", ctx, userid, group)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserGroupIdxs indicates an expected call of SetUserGroupIdxs.
func (mr *MockRedisCacheInterfaceMockRecorder) SetUserGroupIdxs(ctx, userid, group interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserGroupIdxs", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetUserGroupIdxs), ctx, userid, group)
}

// SetUserProfile mocks base method.
func (m *MockRedisCacheInterface) SetUserProfile(ctx context.Context, profile *apipub.UserProfileResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserProfile", ctx, profile, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserProfile indicates an expected call of SetUserProfile.
func (mr *MockRedisCacheInterfaceMockRecorder) SetUserProfile(ctx, profile, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserProfile", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetUserProfile), ctx, profile, ttl)
}

// SetUserProfiles mocks base method.
func (m *MockRedisCacheInterface) SetUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetUserProfiles", ctx, profiles, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetUserProfiles indicates an expected call of SetUserProfiles.
func (mr *MockRedisCacheInterfaceMockRecorder) SetUserProfiles(ctx, profiles, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetUserProfiles", reflect.TypeOf((*MockRedisCacheInterface)(nil).SetUserProfiles), ctx, profiles, ttl)
}

// UpdateFirstPartyLookupTtl mocks base method.
func (m *MockRedisCacheInterface) UpdateFirstPartyLookupTtl(ctx context.Context, key string, ttl time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFirstPartyLookupTtl", ctx, key, ttl)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateFirstPartyLookupTtl indicates an expected call of UpdateFirstPartyLookupTtl.
func (mr *MockRedisCacheInterfaceMockRecorder) UpdateFirstPartyLookupTtl(ctx, key, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFirstPartyLookupTtl", reflect.TypeOf((*MockRedisCacheInterface)(nil).UpdateFirstPartyLookupTtl), ctx, key, ttl)
}

// UpdateGroupMember mocks base method.
func (m *MockRedisCacheInterface) UpdateGroupMember(ctx context.Context, group *apipub.GroupResponse, member *apipub.GroupMemberResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateGroupMember", ctx, group, member)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateGroupMember indicates an expected call of UpdateGroupMember.
func (mr *MockRedisCacheInterfaceMockRecorder) UpdateGroupMember(ctx, group, member interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateGroupMember", reflect.TypeOf((*MockRedisCacheInterface)(nil).UpdateGroupMember), ctx, group, member)
}

// UpdateUserCacheMetaTtl mocks base method.
func (m *MockRedisCacheInterface) UpdateUserCacheMetaTtl(ctx context.Context, userid string, ttl time.Duration) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserCacheMetaTtl", ctx, userid, ttl)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserCacheMetaTtl indicates an expected call of UpdateUserCacheMetaTtl.
func (mr *MockRedisCacheInterfaceMockRecorder) UpdateUserCacheMetaTtl(ctx, userid, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserCacheMetaTtl", reflect.TypeOf((*MockRedisCacheInterface)(nil).UpdateUserCacheMetaTtl), ctx, userid, ttl)
}

// UpdateUserTtls mocks base method.
func (m *MockRedisCacheInterface) UpdateUserTtls(ctx context.Context, userid string, ttl time.Duration) ([]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUserTtls", ctx, userid, ttl)
	ret0, _ := ret[0].([]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateUserTtls indicates an expected call of UpdateUserTtls.
func (mr *MockRedisCacheInterfaceMockRecorder) UpdateUserTtls(ctx, userid, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUserTtls", reflect.TypeOf((*MockRedisCacheInterface)(nil).UpdateUserTtls), ctx, userid, ttl)
}

// UserBlocklistExistsInCache mocks base method.
func (m *MockRedisCacheInterface) UserBlocklistExistsInCache(ctx context.Context, userid string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserBlocklistExistsInCache", ctx, userid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// UserBlocklistExistsInCache indicates an expected call of UserBlocklistExistsInCache.
func (mr *MockRedisCacheInterfaceMockRecorder) UserBlocklistExistsInCache(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserBlocklistExistsInCache", reflect.TypeOf((*MockRedisCacheInterface)(nil).UserBlocklistExistsInCache), ctx, userid)
}

// del mocks base method.
func (m *MockRedisCacheInterface) del(ctx context.Context, key string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "del", ctx, key)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// del indicates an expected call of del.
func (mr *MockRedisCacheInterfaceMockRecorder) del(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "del", reflect.TypeOf((*MockRedisCacheInterface)(nil).del), ctx, key)
}

// exists mocks base method.
func (m *MockRedisCacheInterface) exists(ctx context.Context, keys ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range keys {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "exists", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// exists indicates an expected call of exists.
func (mr *MockRedisCacheInterfaceMockRecorder) exists(ctx interface{}, keys ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, keys...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "exists", reflect.TypeOf((*MockRedisCacheInterface)(nil).exists), varargs...)
}

// existsInSortedSet mocks base method.
func (m *MockRedisCacheInterface) existsInSortedSet(ctx context.Context, key, member string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "existsInSortedSet", ctx, key, member)
	ret0, _ := ret[0].(bool)
	return ret0
}

// existsInSortedSet indicates an expected call of existsInSortedSet.
func (mr *MockRedisCacheInterfaceMockRecorder) existsInSortedSet(ctx, key, member interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "existsInSortedSet", reflect.TypeOf((*MockRedisCacheInterface)(nil).existsInSortedSet), ctx, key, member)
}

// expire mocks base method.
func (m *MockRedisCacheInterface) expire(ctx context.Context, key string, ttl time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "expire", ctx, key, ttl)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// expire indicates an expected call of expire.
func (mr *MockRedisCacheInterfaceMockRecorder) expire(ctx, key, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "expire", reflect.TypeOf((*MockRedisCacheInterface)(nil).expire), ctx, key, ttl)
}

// get mocks base method.
func (m *MockRedisCacheInterface) get(ctx context.Context, key string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "get", ctx, key)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// get indicates an expected call of get.
func (mr *MockRedisCacheInterfaceMockRecorder) get(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "get", reflect.TypeOf((*MockRedisCacheInterface)(nil).get), ctx, key)
}

// jSONGet mocks base method.
func (m *MockRedisCacheInterface) jSONGet(ctx context.Context, key string, paths ...string) *redis.JSONCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key}
	for _, a := range paths {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "jSONGet", varargs...)
	ret0, _ := ret[0].(*redis.JSONCmd)
	return ret0
}

// jSONGet indicates an expected call of jSONGet.
func (mr *MockRedisCacheInterfaceMockRecorder) jSONGet(ctx, key interface{}, paths ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key}, paths...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "jSONGet", reflect.TypeOf((*MockRedisCacheInterface)(nil).jSONGet), varargs...)
}

// jSONMGet mocks base method.
func (m *MockRedisCacheInterface) jSONMGet(ctx context.Context, path string, keys ...string) *redis.JSONSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, path}
	for _, a := range keys {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "jSONMGet", varargs...)
	ret0, _ := ret[0].(*redis.JSONSliceCmd)
	return ret0
}

// jSONMGet indicates an expected call of jSONMGet.
func (mr *MockRedisCacheInterfaceMockRecorder) jSONMGet(ctx, path interface{}, keys ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, path}, keys...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "jSONMGet", reflect.TypeOf((*MockRedisCacheInterface)(nil).jSONMGet), varargs...)
}

// jSONSet mocks base method.
func (m *MockRedisCacheInterface) jSONSet(ctx context.Context, key, path string, value interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "jSONSet", ctx, key, path, value)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// jSONSet indicates an expected call of jSONSet.
func (mr *MockRedisCacheInterfaceMockRecorder) jSONSet(ctx, key, path, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "jSONSet", reflect.TypeOf((*MockRedisCacheInterface)(nil).jSONSet), ctx, key, path, value)
}

// ping mocks base method.
func (m *MockRedisCacheInterface) ping(ctx context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ping", ctx)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ping indicates an expected call of ping.
func (mr *MockRedisCacheInterfaceMockRecorder) ping(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ping", reflect.TypeOf((*MockRedisCacheInterface)(nil).ping), ctx)
}

// pipeline mocks base method.
func (m *MockRedisCacheInterface) pipeline() redis.Pipeliner {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "pipeline")
	ret0, _ := ret[0].(redis.Pipeliner)
	return ret0
}

// pipeline indicates an expected call of pipeline.
func (mr *MockRedisCacheInterfaceMockRecorder) pipeline() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "pipeline", reflect.TypeOf((*MockRedisCacheInterface)(nil).pipeline))
}

// pipelined mocks base method.
func (m *MockRedisCacheInterface) pipelined(ctx context.Context, fn func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "pipelined", ctx, fn)
	ret0, _ := ret[0].([]redis.Cmder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// pipelined indicates an expected call of pipelined.
func (mr *MockRedisCacheInterfaceMockRecorder) pipelined(ctx, fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "pipelined", reflect.TypeOf((*MockRedisCacheInterface)(nil).pipelined), ctx, fn)
}

// set mocks base method.
func (m *MockRedisCacheInterface) set(ctx context.Context, key string, name interface{}, ttl time.Duration) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "set", ctx, key, name, ttl)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// set indicates an expected call of set.
func (mr *MockRedisCacheInterfaceMockRecorder) set(ctx, key, name, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "set", reflect.TypeOf((*MockRedisCacheInterface)(nil).set), ctx, key, name, ttl)
}

// setMembership mocks base method.
func (m *MockRedisCacheInterface) setMembership(ctx context.Context, request *apipub.MembershipRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "setMembership", ctx, request)
	ret0, _ := ret[0].(error)
	return ret0
}

// setMembership indicates an expected call of setMembership.
func (mr *MockRedisCacheInterfaceMockRecorder) setMembership(ctx, request interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "setMembership", reflect.TypeOf((*MockRedisCacheInterface)(nil).setMembership), ctx, request)
}

// tTL mocks base method.
func (m *MockRedisCacheInterface) tTL(ctx context.Context, key string) *redis.DurationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "tTL", ctx, key)
	ret0, _ := ret[0].(*redis.DurationCmd)
	return ret0
}

// tTL indicates an expected call of tTL.
func (mr *MockRedisCacheInterfaceMockRecorder) tTL(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "tTL", reflect.TypeOf((*MockRedisCacheInterface)(nil).tTL), ctx, key)
}

// zAdd mocks base method.
func (m *MockRedisCacheInterface) zAdd(ctx context.Context, key string, members ...redis.Z) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key}
	for _, a := range members {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "zAdd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// zAdd indicates an expected call of zAdd.
func (mr *MockRedisCacheInterfaceMockRecorder) zAdd(ctx, key interface{}, members ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key}, members...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zAdd", reflect.TypeOf((*MockRedisCacheInterface)(nil).zAdd), varargs...)
}

// zCard mocks base method.
func (m *MockRedisCacheInterface) zCard(ctx context.Context, key string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zCard", ctx, key)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// zCard indicates an expected call of zCard.
func (mr *MockRedisCacheInterfaceMockRecorder) zCard(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zCard", reflect.TypeOf((*MockRedisCacheInterface)(nil).zCard), ctx, key)
}

// zCount mocks base method.
func (m *MockRedisCacheInterface) zCount(ctx context.Context, key, min, max string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zCount", ctx, key, min, max)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// zCount indicates an expected call of zCount.
func (mr *MockRedisCacheInterfaceMockRecorder) zCount(ctx, key, min, max interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zCount", reflect.TypeOf((*MockRedisCacheInterface)(nil).zCount), ctx, key, min, max)
}

// zRangeByLex mocks base method.
func (m *MockRedisCacheInterface) zRangeByLex(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zRangeByLex", ctx, key, opt)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// zRangeByLex indicates an expected call of zRangeByLex.
func (mr *MockRedisCacheInterfaceMockRecorder) zRangeByLex(ctx, key, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zRangeByLex", reflect.TypeOf((*MockRedisCacheInterface)(nil).zRangeByLex), ctx, key, opt)
}

// zRangeByScore mocks base method.
func (m *MockRedisCacheInterface) zRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zRangeByScore", ctx, key, opt)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// zRangeByScore indicates an expected call of zRangeByScore.
func (mr *MockRedisCacheInterfaceMockRecorder) zRangeByScore(ctx, key, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zRangeByScore", reflect.TypeOf((*MockRedisCacheInterface)(nil).zRangeByScore), ctx, key, opt)
}

// zRangeByScoreWithScores mocks base method.
func (m *MockRedisCacheInterface) zRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zRangeByScoreWithScores", ctx, key, opt)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// zRangeByScoreWithScores indicates an expected call of zRangeByScoreWithScores.
func (mr *MockRedisCacheInterfaceMockRecorder) zRangeByScoreWithScores(ctx, key, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zRangeByScoreWithScores", reflect.TypeOf((*MockRedisCacheInterface)(nil).zRangeByScoreWithScores), ctx, key, opt)
}

// zRem mocks base method.
func (m *MockRedisCacheInterface) zRem(ctx context.Context, key string, members ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key}
	for _, a := range members {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "zRem", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// zRem indicates an expected call of zRem.
func (mr *MockRedisCacheInterfaceMockRecorder) zRem(ctx, key interface{}, members ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key}, members...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zRem", reflect.TypeOf((*MockRedisCacheInterface)(nil).zRem), varargs...)
}

// zRevRangeByScoreWithScores mocks base method.
func (m *MockRedisCacheInterface) zRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zRevRangeByScoreWithScores", ctx, key, opt)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// zRevRangeByScoreWithScores indicates an expected call of zRevRangeByScoreWithScores.
func (mr *MockRedisCacheInterfaceMockRecorder) zRevRangeByScoreWithScores(ctx, key, opt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zRevRangeByScoreWithScores", reflect.TypeOf((*MockRedisCacheInterface)(nil).zRevRangeByScoreWithScores), ctx, key, opt)
}

// zScore mocks base method.
func (m *MockRedisCacheInterface) zScore(ctx context.Context, key, member string) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "zScore", ctx, key, member)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// zScore indicates an expected call of zScore.
func (mr *MockRedisCacheInterfaceMockRecorder) zScore(ctx, key, member interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "zScore", reflect.TypeOf((*MockRedisCacheInterface)(nil).zScore), ctx, key, member)
}
