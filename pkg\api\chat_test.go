package api

// import (
// 	"github.com/segmentio/encoding/json"
// 	"errors"
// 	"io/ioutil"
// 	"net/http/httptest"
// 	"testing"

// 	"github.com/aws/aws-sdk-go-v2/aws"
// 	"go.uber.org/mock/gomock"
// 	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
// 	"github.com/take-two-t2gp/t2gp-social-service/pkg/store"
// )

// func TestGetChatRoomMessages(t *testing.T) {

// 	mockCtrl := gomock.NewController(t)
// 	defer mockCtrl.Finish()

// 	mockObj := store.NewMockDataStoreInterface(mockCtrl)

// 	// Test Message
// 	msg := &apipub.ChatMessageGroup{
// 		FromUserid: "tacocat",
// 		Message:    "Hello!",
// 	}
// 	var messages []*apipub.ChatMessageGroup
// 	messages = append(messages, msg)

// 	// Test Room
// 	fAPI := NewMockSocialPublicAPI(cfg, mockObj)
// 	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
// 	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"
// 	var roomID apipub.Roomid = "group/01E5V475QFRCEHXKJAS3BRS6BV/01E5V5Z9J0GX72VFSENBCKMHF0"

// 	w, r := Login(User1JWT)

// 	// db.GetGroup(par)(nil, error) fails
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("fake error"))
// 	fAPI.GetChatRoomMessages(w, r, roomID)
// 	if w.Code != 400 {
// 		t.Errorf("incorrect response code. Have %d, want %d", w.Code, 400)
// 	}

// 	// db.GetGroup(par)(nil, nil) returns none
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
// 	w = httptest.NewRecorder()
// 	fAPI.GetChatRoomMessages(w, r, roomID)
// 	if w.Code != 404 {
// 		t.Errorf("incorrect response code. Have %d, want %d", w.Code, 404)
// 	}

// 	group := apipub.Group{
// 		Groupid:    groupID,
// 		Productid:  productID,
// 		MaxMembers: 10,
// 		// Public:     aws.Bool(true),
// 		JoinRequestAction: apipub.JoinRequestActionAutoApprove,
// 		Type:              aws.String("group"),
// 	}
// 	groupMember := apipub.GroupMember{
// 		Userid: "b287e655461f4b3085c8f244e394ff7e",
// 		Role:   apipub.GroupMemberRoleMember,
// 		// Status:    apipub.GroupMemberStatusJoined,
// 		Groupid:   groupID,
// 		Productid: productID,
// 	}

// 	//
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)

// 	w = httptest.NewRecorder()
// 	fAPI.GetChatRoomMessages(w, r, roomID)
// 	if w.Code != 403 {
// 		t.Errorf("incorrect response code. Have %d, want %d", w.Code, 403)
// 	}

// 	group.AddMemberIfNotExist(&groupMember)
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	mockObj.EXPECT().GetChatMessagesGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("fake error"))
// 	w = httptest.NewRecorder()

// 	fAPI.GetChatRoomMessages(w, r, roomID)

// 	if w.Code != 400 {
// 		t.Logf("code was not 400")
// 		t.Fail()
// 	}

// 	// Success
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	mockObj.EXPECT().GetChatMessagesGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(messages, nil)
// 	w = httptest.NewRecorder()
// 	fAPI.GetChatRoomMessages(w, r, roomID)

// 	if w.Code != 200 {
// 		t.Logf("code was not 200")
// 		t.Fail()
// 	}

// 	resp := w.Result()
// 	respBody, _ := ioutil.ReadAll(resp.Body)
// 	var DirectMessages apipub.ListResponse
// 	json.Unmarshal(respBody, &DirectMessages)
// 	// If the return data is not an array of one message, fail
// 	if len(DirectMessages.Items) != 1 {
// 		t.Logf("incorrect number of returned messages")
// 		t.Fail()
// 	}

// }

// func TestSendChatRoomMessage(t *testing.T) {

// 	mockCtrl := gomock.NewController(t)
// 	defer mockCtrl.Finish()

// 	mockObj := store.NewMockDataStoreInterface(mockCtrl)
// 	fAPI := NewMockSocialPublicAPI(cfg, mockObj)
// 	// Test Room

// 	w, r := Login(User1JWT)
// 	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
// 	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"
// 	var roomID apipub.Roomid = "group/01E5V475QFRCEHXKJAS3BRS6BV/01E5V5Z9J0GX72VFSENBCKMHF0"
// 	// db.GetGroup(par)(nil, error) fails
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("new error"))
// 	fAPI.SendChatRoomMessage(w, r, roomID)
// 	if w.Code != 400 {
// 		t.Errorf("incorrect response code. Have %d, want %d", w.Code, 400)
// 	}

// 	// db.GetGroup(par)(nil, nil) returns none
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
// 	w = httptest.NewRecorder()
// 	fAPI.SendChatRoomMessage(w, r, roomID)
// 	if w.Code != 404 {
// 		t.Errorf("incorrect response code. Have %d, want %d", w.Code, 404)
// 	}

// 	// db.GetGroup(par)(group, nil) user is not in group
// 	group := apipub.Group{
// 		Groupid:    groupID,
// 		Productid:  productID,
// 		MaxMembers: 10,
// 		// Public:     aws.Bool(true),
// 		JoinRequestAction: apipub.JoinRequestActionAutoApprove,
// 		Type:              aws.String("group"),
// 	}

// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	w = httptest.NewRecorder()
// 	fAPI.SendChatRoomMessage(w, r, roomID)
// 	if w.Code != 403 {
// 		t.Errorf("incorrect response code. Have %d, want %d", w.Code, 403)
// 	}

// 	// Failing the message
// 	groupMember := apipub.GroupMember{
// 		Userid: "b287e655461f4b3085c8f244e394ff7e",
// 		Role:   apipub.GroupMemberRoleMember,
// 		// Status:    apipub.GroupMemberStatusJoined,
// 		Groupid:   groupID,
// 		Productid: productID,
// 	}
// 	group.AddMemberIfNotExist(&groupMember)
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	mockObj.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(errors.New("Fake Error"))
// 	msg := apipub.ChatMessageRequest{
// 		Message: "Hello again!",
// 	}

// 	w, req := AddBodyToRequest(msg, User1JWT)

// 	fAPI.SendChatRoomMessage(w, req, roomID)

// 	if w.Code != 400 {
// 		t.Logf("Response code wasn't 400")
// 		t.Fail()
// 	}

// 	// Test sending message to group

// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	mockObj.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
// 	// .
// 	// 	Do(func(msg *apipub.ChatMessageGroup) {
// 	// 		if msg.Type != "group" {
// 	// 			t.Logf("message type was incorrect")
// 	// 			t.Fail()
// 	// 		}
// 	// 	})
// 	w, req = AddBodyToRequest(msg, User1JWT)

// 	fAPI.SendChatRoomMessage(w, req, roomID)

// 	if w.Code != 200 {
// 		t.Logf("response code wasn't 200")
// 		t.Fail()
// 	}

// 	// Test sending message for group-private
// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	mockObj.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
// 	// .
// 	// 	Do(func(msg *apipub.ChatMessageGroup) {
// 	// 		if msg.Type != "group-private" {
// 	// 			t.Logf("message type was incorrect")
// 	// 			t.Fail()
// 	// 		}
// 	// 	})

// 	toUserID := "burgerdog"
// 	msg = apipub.ChatMessageRequest{
// 		Message:  "Hello again!",
// 		ToUserid: &toUserID,
// 	}

// 	w, req = AddBodyToRequest(msg, User1JWT)

// 	fAPI.SendChatRoomMessage(w, req, roomID)

// 	if w.Code != 200 {
// 		t.Logf("response code wasn't 200")
// 		t.Fail()
// 	}

// }

// func TestGetChatRoomMember(t *testing.T) {
// 	// This is tested in groups_test
// }

// func TestSendDirectMessage(t *testing.T) {

// 	mockCtrl := gomock.NewController(t)
// 	defer mockCtrl.Finish()

// 	mockObj := store.NewMockDataStoreInterface(mockCtrl)

// 	mockObj.EXPECT().AddChatMessageDM(gomock.Any(), gomock.Any()).Return(nil)
// 	mockObj.EXPECT().GetFriend(gomock.Any(), gomock.Any(), gomock.Any()).Return(&apipub.Friend{
// 		Status: apipub.FriendStatusFriend,
// 	}, nil)

// 	msg := apipub.ChatMessageRequest{
// 		Message: "Hello again!",
// 	}
// 	w, req := AddBodyToRequest(msg, User1JWT)

// 	var userID apipub.Userid = "tacocat"

// 	fAPI := NewMockSocialPublicAPI(cfg, mockObj)

// 	fAPI.SendDirectmessage(w, req, userID)

// 	if w.Code != 200 {
// 		t.Logf("response code wasn't 200")
// 		t.Fail()
// 	}

// 	// Make the db call fail
// 	mockObj.EXPECT().AddChatMessageDM(gomock.Any(), gomock.Any()).Return(errors.New("Fake error"))
// 	mockObj.EXPECT().GetFriend(gomock.Any(), gomock.Any(), gomock.Any()).Return(&apipub.Friend{
// 		Status: apipub.FriendStatusFriend,
// 	}, nil)
// 	w, req = AddBodyToRequest(msg, User1JWT)
// 	fAPI.SendDirectmessage(w, req, userID)
// 	if w.Code != 400 {
// 		t.Logf("response code wasn't 400")
// 		t.Fail()
// 	}
// }

// // func TestGetDirectMessages(t *testing.T) {
// // 	// Create mock data for payload
// // 	mockCtrl := gomock.NewController(t)
// // 	defer mockCtrl.Finish()
// // 	mockObj := store.NewMockDataStoreInterface(mockCtrl)
// // 	// mocktime := time.Date(2020, time.Month(2), 21, 1, 10, 30, 0, time.UTC)

// // 	msg := &apipub.ChatMessageDM{
// // 		FromUserid: "tacocat",
// // 		Message:    "Hello!",
// // 		ToUserid:   "burgerdog",
// // 	}
// // 	var messages []*apipub.ChatMessageDM
// // 	messages = append(messages, msg)

// // 	// Assert that the mock of store.GetChatMessagesDM will return our mock message
// // 	mockObj.EXPECT().GetChatMessagesDM(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(messages, nil, nil)
// // 	mockObj.EXPECT().GetFriend(gomock.Any(), gomock.Any()).Return(&apipub.Friend{
// // 		Status: apipub.FriendStatusFriend,
// // 	}, nil)

// // 	// Create a new pubApi and pass our mock obj into to init
// // 	fAPI := NewMockSocialPublicAPI(cfg, mockObj)
// // 	w, req := Login(User1JWT)

// // 	// Create mock vals to pass to method
// // 	var next apipub.Next
// // 	var limit apipub.Limit
// // 	next = "e12c3df480984141b2f385646b2024fa"
// // 	limit = 5

// // 	// Start the Test
// // 	fAPI.GetDirectMessages(w, req, "e12c3df480984141b2f385646b2024fa", apipub.GetDirectMessagesParams{Next: &next, Limit: &limit})

// // 	// If the returned status code is not 200, fail
// // 	if w.Code != 200 {
// // 		t.Logf("response code wasn't 200")
// // 		t.Fail()
// // 	}
// // 	resp := w.Result()
// // 	respBody, _ := ioutil.ReadAll(resp.Body)
// // 	var DirectMessages apipub.ListResponse
// // 	json.Unmarshal(respBody, &DirectMessages)
// // 	// If the return data is not an array of one message, fail
// // 	if len(DirectMessages.Items) != 1 {
// // 		t.Logf("incorrect number of returned messages")
// // 		t.Fail()
// // 	}

// // 	// Assert our mock to fail
// // 	mockObj.EXPECT().GetChatMessagesDM(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, errors.New("Fake error"))
// // 	w = httptest.NewRecorder()
// // 	mockObj.EXPECT().GetFriend(gomock.Any(), gomock.Any()).Return(&apipub.Friend{
// // 		Status: apipub.FriendStatusFriend,
// // 	}, nil)

// // 	// Start the Test
// // 	fAPI.GetDirectMessages(w, req, "e12c3df480984141b2f385646b2024fa", apipub.GetDirectMessagesParams{Next: &next, Limit: &limit})

// // 	// If the code doesn't fail, then this test fails
// // 	if w.Code != 500 {
// // 		t.Logf("response code wasn't 500")
// // 		t.Fail()
// // 	}
// // }

// func TestValidateChatMessage(t *testing.T) {
// 	message := "Hello, this should be a valid message"
// 	val, err := pubApi.ValidateChatMessage(message)
// 	if err != nil {
// 		t.Logf("code incorrectly errored")
// 		t.Fail()
// 	}
// 	if val != message {
// 		t.Logf("code manipulated the message incorrectly")
// 		t.Fail()
// 	}

// 	// 1413 characters
// 	message = "Character Count Tool - Character Counter is a free character counter tool that provides instant character count & word count statistics for a given text. This tool reports the number of character with spaces and without spaces, also the number of words and sentences... Character Count Tool - Character Counter monitors and reports the character counts & word counts of the text that you type in real time. Thus it is suitable for writing text with word/ character limit.Character Count Tool - Character Counter is a free character counter tool that provides instant character count & word count statistics for a given text. This tool reports the number of character with spaces and without spaces, also the number of words and sentences... Character Count Tool - Character Counter monitors and reports the character counts & word counts of the text that you type in real time. Thus it is suitable for writing text with word/ character limit.Character Count Tool - Character Counter is a free character counter tool that provides instant character count & word count statistics for a given text. This tool reports the number of character with spaces and without spaces, also the number of words and sentences... Character Count Tool - Character Counter monitors and reports the character counts & word counts of the text that you type in real time. Thus it is suitable for writing text with word/ character limit."
// 	// message cut down to 1024 (config.MaxMessageLength)
// 	expected := "Character Count Tool - Character Counter is a free character counter tool that provides instant character count & word count statistics for a given text. This tool reports the number of character with spaces and without spaces, also the number of words and sentences... Character Count Tool - Character Counter monitors and reports the character counts & word counts of the text that you type in real time. Thus it is suitable for writing text with word/ character limit.Character Count Tool - Character Counter is a free character counter tool that provides instant character count & word count statistics for a given text. This tool reports the number of character with spaces and without spaces, also the number of words and sentences... Character Count Tool - Character Counter monitors and reports the character counts & word counts of the text that you type in real time. Thus it is suitable for writing text with word/ character limit.Character Count Tool - Character Counter is a free character counter tool that pro"

// 	val, err = pubApi.ValidateChatMessage(message)

// 	if err != nil {
// 		t.Logf("code incorrectly errored")
// 		t.Fail()
// 	}

// 	if val != expected {
// 		t.Logf("val should be expected ")
// 		t.Fail()
// 	}

// 	// TODO: Test the profanity filter...
// 	// }
// 	// func TestJoinChatRoomPublic(t *testing.T) {
// 	// 	// Create mock DataStore
// 	// 	mockCtrl := gomock.NewController(t)
// 	// 	defer mockCtrl.Finish()

// 	// 	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
// 	// 	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"
// 	// 	var roomID apipub.Roomid = "group/01E5V475QFRCEHXKJAS3BRS6BV/01E5V5Z9J0GX72VFSENBCKMHF0"

// 	// 	mockObj := store.NewMockDataStoreInterface(mockCtrl)
// 	// 	fAPI := NewMockSocialPublicAPI(cfg, mockObj)
// 	// 	now := time.Now().UTC()
// 	// 	group := apipub.Group{
// 	// 		Groupid:    groupID,
// 	// 		Productid:  productID,
// 	// 		MaxMembers: 10,
// 	// 		// Public:     aws.Bool(true),
// 	// 		JoinRequestAction: apipub.JoinRequestActionAutoApprove,
// 	// 		Type:              aws.String("group"),
// 	// 		Created:           &now,
// 	// 	}
// 	// 	member := apipub.GroupMember{
// 	// 		Userid:    productID,
// 	// 		Groupid:   groupID,
// 	// 		Productid: productID,
// 	// 	}
// 	// 	members := []apipub.GroupMember{member}
// 	// 	groupWithMember := group
// 	// 	groupWithMember.Members = &members

// 	// 	_, req := AddBodyToRequest(group, User1JWT)

// 	// 	// bad roomid format
// 	// 	// put GroupMember fails
// 	// 	badRoomID := "group/1ff323f"
// 	// 	w := httptest.NewRecorder()
// 	// 	fAPI.JoinChatRoom(w, req, apipub.Roomid(badRoomID))
// 	// 	if w.Code != 400 {
// 	// 		t.Errorf("response code incorrect. Have %d, want %d", w.Code, 400)
// 	// 	}

// 	// 	// Group is nil, throw error
// 	// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
// 	// 	mockObj.EXPECT().DeleteItemByPkSk(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
// 	// 	w = httptest.NewRecorder()
// 	// 	fAPI.JoinChatRoom(w, req, roomID)

// 	// 	if w.Code != 404 {
// 	// 		t.Errorf("response code incorrect. Have %d, want %d", w.Code, 404)
// 	// 	}

// 	// 	// Success
// 	// 	mockObj.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil).Times(2)
// 	// 	mockObj.EXPECT().PutGroupMemberInGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
// 	// 	mockObj.EXPECT().PutGroup(gomock.Any(), gomock.Any()).Return(&group, nil)
// 	// 	mockObj.EXPECT().UpdateGroupMembersProfileVals(gomock.Any(), gomock.Any()).Return(&group, nil)
// 	// 	mockObj.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)

// 	// 	w = httptest.NewRecorder()
// 	// 	fAPI.JoinChatRoom(w, req, roomID)
// 	// 	if w.Code != 200 {
// 	// 		t.Errorf("response code incorrect. Have %d, want %d", w.Code, 200)
// 	// 	}

// 	// private flow tested in groups_test.go

// 	// var newGroup *apipub.Group
// 	// newGroup, err = Ds.GetGroup("chat2")
// 	// if err != nil {
// 	// 	t.Fatal(err)
// 	// }
// 	// if !newGroup.IsMemberInGroup("b287e655461f4b3085c8f244e394ff7e") {
// 	// 	t.Fatalf("user1 should be in the group")
// 	// }
// }
