name: Deploy develop

on:
  repository_dispatch:
    types: ['Deploy to develop']
  workflow_dispatch:
    inputs:
      version:
        required: false
        description: 'Release image tag (https://go.aws/37MtoXh) with githash ex: 1.0.0-0a1b2c3d'
      skip_plugin_swap:
        type: boolean
        default: false
        description: 'Check this to skip plugin swap on this release'

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  run-helm:
    environment: develop
    name: 'Run Helm'
    runs-on: [t2gp-arc-linux]
    env:
      AWS_DEFAULT_REGION: us-east-1
      TARGET_ENV: develop
      CLUSTER: t2gp-non-production
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
          ref: ${{ github.event.client_payload.ref }}
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables
        run: |
          ver=${{ github.event.client_payload.image_tag }}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${ver}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
      - name: Environment Variables (Manual)
        if: github.event.inputs.version != ''
        run: |
          ver=${{github.event.inputs.version}}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
      - name: Helm Deploy (develop)
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          raise-pr: false
          environment: ${{ env.TARGET_ENV }}
          helm-values: 'social-api.groupsApi.image.tag="${{ env.IMAGE_TAG }}",social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Helm Trusted Deploy (develop)
        id: helm_trusted_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-trusted-api
          raise-pr: false
          environment: ${{ env.TARGET_ENV }}
          helm-values: 'social-api.groupsApi.image.tag="${{ env.IMAGE_TAG }}",social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "develop", "version": "${{env.IMAGE_TAG}}", "api_url":"https://social-service-develop.dev.d2dragon.net/v2", "api_private_url":"https://social-service-develop-private.dev.d2dragon.net", "mqtt_url":"wss://social-service-develop.dev.d2dragon.net/mqtt" }'
      - name: AWS Assume Role (DEV)
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::692664816152:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-west-2
      - name: Get plugin version on AWS Parameter Store
        run: |
          set -ex
          CURRENTLY_STORED=$(aws ssm get-parameter --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --output text --query 'Parameter.Value')
          if [ "$CURRENTLY_STORED" == "${{ env.SUBMODULE_HASH }}" ]; then
            echo "UPDATE_PLUGIN=false" >> $GITHUB_ENV
          else
            echo "UPDATE_PLUGIN=true" >> $GITHUB_ENV
          fi
      - name: Swap VernemQ Plugin (DEV)
        if: ${{ env.UPDATE_PLUGIN == 'true' }}
        working-directory: deployments
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Update plugin version to AWS Parameter Store (DEV)
        if: ${{ env.UPDATE_PLUGIN == 'true' }}
        run: |
          aws ssm put-parameter --overwrite --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --value ${{ env.SUBMODULE_HASH }}
      # - name: Notify Slack Channel (failure)
      #   if: failure()
      #   uses: tokorom/action-slack-incoming-webhook@main
      #   env:
      #     INCOMING_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_DEPLOY_STATUS }}
      #   with:
      #     attachments: |
      #       [
      #         {
      #           "color": "#ff0000",
      #           "blocks": [
      #             {
      #               "type": "header",
      #               "text": {
      #                 "type": "plain_text",
      #                 "text": "Deploy to Develop Failed"
      #               }
      #             },
      #             {
      #               "type": "section",
      #               "text": {
      #                 "type": "mrkdwn",
      #                 "text": "*Info*\nGit SHA: ${{ env.GIT_SHA }} \nAction:<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|${{ github.run_id }}>"
      #               }
      #             }
      #           ]
      #         }
      #       ]
      - name: Output Info
        id: output_info
        run: |
          echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: develop
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      api_test_note: 'deploy develop'
    secrets: inherit
