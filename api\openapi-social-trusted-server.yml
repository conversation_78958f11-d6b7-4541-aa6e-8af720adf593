openapi: 3.0.3
info:
  title: Social Trusted API
  version: REPLACE_ME
servers:
  - url: https://social-trusted-develop.dev.d2dragon.net/v2
  - url: https://social-trusted-integration.d2dragon.net/v2
  - url: https://social-trusted-staging.d2dragon.net/v2
  - url: https://social-trusted-cert.d2dragon.net/v2
  - url: https://social-trusted-production.d2dragon.net/v2
  - url: /v2
security:
  - bearerAuth: []
tags:
  - name: Groups
    description: Create and manage groups of users
  - name: Discovery
    description: Update discovery info for a product
  - name: Status
    description: Get health and version info
paths:
  /server/groups:
    post:
      summary: Create a Group
      operationId: serverCreateGroup
      tags:
        - Groups
      requestBody:
        $ref: '#/components/requestBodies/serverCreateGroupRequestBody'
      responses:
        '201':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/groupWithErrorsResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
  /server/groups/{pGroupid}:
    delete:
      tags:
        - Groups
      summary: Disband/Delete a Group
      operationId: serverDeleteGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                type: object
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    get:
      tags:
        - Groups
      summary: Get a Group
      operationId: serverGetGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/groupResponse'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    patch:
      tags:
        - Groups
      summary: Update a Group's size or metadata
      operationId: serverUpdateGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      requestBody:
        $ref: '#/components/requestBodies/serverUpdateGroupRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/groups/{pGroupid}/members/{pMemberid}:
    patch:
      tags:
        - Groups
      summary: Update group member
      operationId: serverUpdateGroupMember
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
      requestBody:
        $ref: '#/components/requestBodies/serverUpdateGroupMemberRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Groups
      summary: Kick a member from group
      operationId: serverKickMemberFromGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
        - $ref: '#/components/parameters/pMemberid'
        - name: reason
          in: query
          description: reason for removing a group member
          required: false
          schema:
            type: string
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/groups/{pGroupid}/control:
    post:
      tags:
        - Groups
      summary: Send a control message (json or binary)
      operationId: serverSendControlMessage
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      requestBody:
        $ref: '#/components/requestBodies/serverSendControlMessageRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/memberships/invites/groups/{pGroupid}:
    post:
      summary: Send invitation for group impersonating a user
      description: S2S method that works exactly like the Social API to send an invite to a group but the server impersonats a user. Does not naturally grant permissions for non members to invite.  Only works impersonating leader unless the group setting is set to allow member invites.
      tags:
        - Memberships
      operationId: serverSendInvitesForGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      requestBody:
        $ref: '#/components/requestBodies/serverSendInviteRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/memberships/requests/groups/{pGroupid}:
    post:
      summary: Joins a user to group
      description: Join requests made from the Social Trusted API will add users to the group no matter what the joinRequestAction is on the group.
      tags:
        - Memberships
      operationId: serverSendJoinRequestsForGroup
      parameters:
        - $ref: '#/components/parameters/pGroupid'
      requestBody:
        $ref: '#/components/requestBodies/serverJoinRequestRequestBody'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/membershipRequest'
              examples:
                Membership Return:
                  value:
                    expiresIn: 3600
                    memberid: ********************************
                    approverid: ********************************
                    groupid: 01EYRSXN4DCFF1AV128Y5A211J
                    productid: f9ba143d26e86d5f9479dc9267177aae
                    onlineServiceType: 3
                    status: requested
                    canCrossPlay: false
                    firstPartyId: string
                    isFirstPartyInvite: false
                    version: 0
                    displayName: string
                Empty Return invite/request already exists:
                  value: {}
        '201':
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/membershipRequest'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/endorsements/{pEndorsementName}/users/{pUserid}/reset:
    delete:
      tags:
        - Endorsements
      summary: reset endorsement for user.  this sets the resetable counter to 0.
      parameters:
        - $ref: '#/components/parameters/pEndorsementName'
        - $ref: '#/components/parameters/pUserid'
      operationId: resetEndorsementForUser
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/endorsements/{pEndorsementName}/users/{pUserid}/remove:
    delete:
      tags:
        - Endorsements
      summary: completely removes an endorsement for user.  as opposed to just reseting the resetable counter.
      parameters:
        - $ref: '#/components/parameters/pEndorsementName'
        - $ref: '#/components/parameters/pUserid'
      operationId: removeEndorsementForUser
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/discovery:
    get:
      tags:
        - Discovery
      summary: Find endpoints to use.  Nil discoveryid or Empty '?discoveryid=' will return all discovery. The trusted API Ignores the canList=true flag in the discovery list and will return all values.
      operationId: serverGetDiscovery
      parameters:
        - $ref: '#/components/parameters/discoveryid'
        - $ref: '#/components/parameters/discoveryPid'
      responses:
        '200':
          description: Discovery response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/discoveryListResponse'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
    patch:
      tags:
        - Discovery
      summary: Upsert discovery entries.  It will add any new ids and update existing ids.
      operationId: serverUpsertDiscovery
      requestBody:
        $ref: '#/components/requestBodies/serverUpsertDiscoveryRequestBody'
      responses:
        '200':
          $ref: '#/components/responses/200empty'
        '201':
          description: Created discovery response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/discoveryListResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
    delete:
      tags:
        - Discovery
      summary: Delete discovery entry. Nil or Empty '?id=' will delete all discovery info.
      operationId: serverDeleteDiscovery
      parameters:
        - $ref: '#/components/parameters/discoveryid'
        - $ref: '#/components/parameters/discoveryPid'
      responses:
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/emptyObject'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '404':
          $ref: '#/components/responses/404'
        '429':
          $ref: '#/components/responses/429'
        '500':
          $ref: '#/components/responses/500'
  /server/version:
    get:
      security: []
      tags:
        - Status
      summary: Get the server version
      operationId: serverGetVersion
      responses:
        '200':
          description: Version
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/versionResponse'
        '429':
          $ref: '#/components/responses/429'
  /server/health:
    get:
      tags:
        - Status
      security: []
      summary: Get the server health status
      operationId: serverGetHealth
      parameters:
        - $ref: '#/components/parameters/healthid'
      responses:
        '200':
          description: Server is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/healthResponse'
        '404':
          description: Identity parameter not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/healthResponse'
        '429':
          $ref: '#/components/responses/429'
        '500':
          description: Server is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/healthResponse'
  /server/auth/token:
    get:
      description: Get a JWT token for the current server instance
      operationId: serverGetToken
      tags:
        - Authentication
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/serverLoginResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
  /server/auth/logout:
    post:
      description: Logs the current user out of the session
      operationId: serverLogout
      tags:
        - Authentication
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                type: object
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
  /server/auth/refresh:
    post:
      description: Refresh the JWT token. You will need to send the `refreshToken` from the login response. After logout, the `refreshToken` will not longer work. Recommended that game teams use DCL rather than our API to refresh token.
      operationId: serverRefreshToken
      tags:
        - Authentication
      requestBody:
        $ref: '#/components/requestBodies/serverRefreshTokenRequestBody'
      responses:
        '200':
          description: Success-Response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/serverLoginResponse'
        '400':
          $ref: '#/components/responses/400'
        '401':
          $ref: '#/components/responses/401'
        '403':
          $ref: '#/components/responses/403'
        '500':
          $ref: '#/components/responses/500'
components:
  schemas:
    maxMembers:
      type: integer
      description: default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
      example: 6
      minimum: 2
      maximum: 100
    joinRequestAction:
      type: string
      description: The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
      enum:
        - manual
        - auto-approve
        - auto-reject
      example: manual
    password:
      type: string
      description: password for the join request
      format: password
    canMembersInvite:
      type: boolean
      description: Should all members be allowed to invite other users, not just the leader?
      example: true
    canCrossPlay:
      type: boolean
      description: Does the user sending this have cross play enabled on their local system
      example: false
    meta:
      type: object
      description: free form map (json format) to store metadata for this object.
      nullable: true
      example:
        key1: value1
        key2: value2
    dnaid:
      type: string
      description: an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
      example: effe28b27efc6594e43bfc0879b40085
      pattern: ^[\da-f]{8}-?([\da-f]{4}-?){3}[\da-f]{12}$
    onlineServiceType:
      type: integer
      description: basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 9
        - 10
        - 11
        - 12
        - 13
        - 14
        - 15
        - 16
        - 17
        - 18
        - 19
        - 20
        - 21
        - 22
        - 23
        - 24
        - 99
      x-enumNames:
        - UNKNOWN
        - XBOX LIVE
        - SONY ENTERTAINMENT NETWORK
        - STEAM
        - WEB
        - LEGACY GAME CENTER
        - GOOGLE PLAY
        - WINDOWS PHONE
        - CALICO
        - NINTENDO
        - GAME CENTER
        - WEGAME
        - VORTEX
        - EPIC
        - STADIA
        - FACEBOOK
        - GOOGLE
        - TWITTER
        - TWITCH
        - DEVICE
        - APPLE
        - ZENDESK
        - T2GP
        - WINDOWS DEVELOPER
      example: 3
    joinGroupMember:
      type: object
      required:
        - memberid
        - canCrossPlay
        - onlineServiceType
      properties:
        memberid:
          $ref: '#/components/schemas/dnaid'
        canCrossPlay:
          $ref: '#/components/schemas/canCrossPlay'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
    telemetryMetaData:
      type: object
      description: Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
      nullable: true
      example:
        some_boolean_field: 'true'
        some_integer_field: '0'
        some_string_field: asdf1234
    created:
      type: string
      description: timestamp that this record was created.
      format: date-time
      example: '2020-09-14T20:30:35.129Z'
    ulid:
      type: string
      description: lexigraphicaly sorted unique identifier
      example: 01EYRSXN4DCFF1AV128Y5A211J
      pattern: ^[0123456789ABCDEFGHJKMNPQRSTVWXYZ]{26}$
    groupid:
      allOf:
        - $ref: '#/components/schemas/ulid'
      description: the id of the group.  validates ULID pattern.
    productid:
      allOf:
        - $ref: '#/components/schemas/dnaid'
      description: the id of the product. validates as a dnaid.
    ttl:
      type: integer
      format: int64
      description: time in seconds until the object expires
      example: 3600
    membershipStatus:
      type: string
      description: The membership status of the invite or request to join state. * requested - a request to join flow initiated * approved - group join request has been approved * rejected - group join request has been rejected * invited - the user has been invited to the group * accepted - the user has accepted the invite to the group * declined - the invite has been declined * revoked - the invite has been revoked
      enum:
        - requested
        - approved
        - rejected
        - invited
        - accepted
        - declined
        - revoked
      example: rejected
    firstPartyid:
      type: string
      description: The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
      example: *****************
    isFirstPartyInvite:
      type: boolean
      description: a flag to indicate whether this invite should be processed as a first party invite
      example: false
    dnaDisplayName:
      type: string
      description: 2k display name with optional 5 digit discrimnating hash
      pattern: ^[0-9a-zA-Z]{3,16}($|\#\d{5}$)
      example: discopotato#12345
    membershipRequest:
      type: object
      description: this schema defines a membership request.  which can be either a join request or an invite using the status field as a determiner.
      required:
        - groupid
        - memberid
        - approverid
        - status
      properties:
        ttl:
          allOf:
            - $ref: '#/components/schemas/ttl'
          default: 3600
          minimum: 1
          maximum: 2628288
        memberid:
          $ref: '#/components/schemas/dnaid'
        approverid:
          $ref: '#/components/schemas/dnaid'
        groupid:
          $ref: '#/components/schemas/groupid'
        productid:
          $ref: '#/components/schemas/productid'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        status:
          $ref: '#/components/schemas/membershipStatus'
        canCrossPlay:
          $ref: '#/components/schemas/canCrossPlay'
        firstPartyid:
          $ref: '#/components/schemas/firstPartyid'
        isFirstPartyInvite:
          $ref: '#/components/schemas/isFirstPartyInvite'
        fromDisplayName:
          allOf:
            - $ref: '#/components/schemas/dnaDisplayName'
          description: the display name of the user that this request is from.  provided to display in the UI of the invite.
        teleMeta:
          $ref: '#/components/schemas/telemetryMetaData'
    name:
      type: string
      description: The name associated with this user. If friend name is empty string, it is possible that it is not a full 2K account.
      example: discopotato
    groupMemberRole:
      type: string
      description: the role of a group member
      enum:
        - leader
        - member
        - nonmember
      example: leader
    presenceStatus:
      type: string
      description: the status of the presence record for the user
      enum:
        - online
        - offline
        - playing
        - custom
        - away
        - dnd
        - chat
        - authenticating
      example: online
    customStatus:
      type: string
      example: I like waffles
    richPresence:
      type: string
      description: string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
    gameName:
      type: string
      description: pre-localized game name.
      example: Sample Game
    gameData:
      type: string
      description: free form field for games to send additional presence information for their internal use.
      maxLength: 1024
    activeGroupResponse:
      type: object
      required:
        - groupid
        - maxMembers
        - currentMemberCount
        - canRequestJoin
        - canCrossPlay
      properties:
        groupid:
          $ref: '#/components/schemas/groupid'
        canRequestJoin:
          type: boolean
          description: in v2, this boolean is basically just a check on if the group is full.  it used to check group join request action but now any group can be joined using a password even as long as it's not full.
        canCrossPlay:
          $ref: '#/components/schemas/canCrossPlay'
        maxMembers:
          $ref: '#/components/schemas/maxMembers'
        currentMemberCount:
          type: integer
          description: count of current members of the group
    clientid:
      type: string
      example: random-mqtt-client-id-string
      description: client id of mqtt connection for mqtt presence
    priority:
      type: integer
      description: Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
    timestamp:
      type: string
      description: timstamp of the event
      format: date-time
      example: '2020-09-14T20:30:35.129Z'
    joinContext:
      type: object
      description: Context used to join a game session
      required:
        - sessionid
        - launchGameArgs
      properties:
        sessionid:
          type: string
        launchGameArgs:
          type: string
    presenceResponse:
      type: object
      description: a presence record
      x-oapi-codegen-extra-tags:
        dynamodbav: presence
      required:
        - userid
        - status
        - timestamp
        - productid
        - gameName
        - priority
        - customStatus
        - onlineServiceType
        - activeSessionid
        - richPresence
        - gameData
        - activeGroup
        - clientid
        - ttl
        - platformid
        - joinContext
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        status:
          $ref: '#/components/schemas/presenceStatus'
        customStatus:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/customStatus'
        richPresence:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/richPresence'
        gameName:
          $ref: '#/components/schemas/gameName'
        gameData:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/gameData'
        activeGroup:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/activeGroupResponse'
        productid:
          $ref: '#/components/schemas/productid'
        clientid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/clientid'
        ttl:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ttl'
          description: How long in seconds before this presence will be considered offline if no presence Heartbeat is made.   | This is an optional value for those who are not using our MQTT.  People using our MQTT will have this functionality via our mqtt plugin.  Timeout set to 5 minutes for auto drop from group.
          minimum: 35
          maximum: 1800
        priority:
          $ref: '#/components/schemas/priority'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        meta:
          $ref: '#/components/schemas/meta'
        timestamp:
          $ref: '#/components/schemas/timestamp'
        joinContext:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/joinContext'
        platformid:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/dnaid'
          description: DNA only. at the time the presence is set, if the token has a pai claim.  this will be the sub claim, which should be the platformid.  we do not guarantee this value will be returned since full account tokens will not have it.
        activeSessionid:
          allOf:
            - $ref: '#/components/schemas/dnaid'
          description: id of active login session.
    accountTypeDNA:
      type: integer
      description: The type of Account according to DNA.  Notable is 3 for full.
      format: uint32
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 7
      x-enumNames:
        - UNKNOWN
        - ANONYMOUS
        - PLATFORM
        - FULL
        - TOOLS
        - NEWSLETTER
        - UNDISCLOSED
        - PRIVACY POLICY ACCEPTED ONLY
      example: 2
    accountLinkDNA:
      type: object
      description: First party DNA account links.  This will be filtered by the current user's OST.
      properties:
        linkType:
          type: string
          enum:
            - xbl
            - steam
            - psn
            - epic
            - nintendo
            - parent
        accountId:
          $ref: '#/components/schemas/dnaid'
        accountType:
          $ref: '#/components/schemas/accountTypeDNA'
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        firstPartyid:
          $ref: '#/components/schemas/firstPartyid'
    links:
      description: Linked accounts. Filtered to current OST.
      type: array
      items:
        $ref: '#/components/schemas/accountLinkDNA'
    groupMemberResponse:
      type: object
      required:
        - userid
        - productid
        - role
        - name
        - presence
        - links
        - meta
        - metaLastUpdated
      properties:
        userid:
          $ref: '#/components/schemas/dnaid'
        name:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/name'
        productid:
          $ref: '#/components/schemas/productid'
        role:
          $ref: '#/components/schemas/groupMemberRole'
        presence:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/presenceResponse'
        links:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/links'
        meta:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/meta'
        metaLastUpdated:
          description: Represents the time when an update request is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
          nullable: true
          type: integer
          format: uint64
          example: 1737484653930
    userid:
      type: string
      description: The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
      example: effe28b27efc6594e43bfc0879b40085
    errorCode:
      type: integer
      description: error code.  list of errors on docsite.
      format: uint32
      example: 100120
    error:
      type: object
      required:
        - code
        - errorCode
        - message
      properties:
        code:
          description: HTTP error code
          type: integer
          format: uint32
          example: 500
        errorCode:
          $ref: '#/components/schemas/errorCode'
        message:
          type: string
          description: error message
          example: Exception occured
        stack:
          type: string
          description: Stack trace of the error (will be only returned in dev environment)
    groupMembershipErrorResponse:
      type: object
      required:
        - memberid
        - error
      properties:
        memberid:
          $ref: '#/components/schemas/userid'
        error:
          $ref: '#/components/schemas/error'
    groupWithErrorsResponse:
      required:
        - groupid
        - maxMembers
        - productid
        - joinRequestAction
        - created
        - membershipRequests
        - meta
        - members
        - password
        - canMembersInvite
        - canCrossPlay
        - onlineServiceType
        - ttl
        - errors
        - groupCompositionId
      description: ProductID only set as required due to codegen issues.
      type: object
      properties:
        created:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/created'
        groupid:
          $ref: '#/components/schemas/groupid'
        productid:
          $ref: '#/components/schemas/productid'
        membershipRequests:
          type: array
          nullable: true
          items:
            allOf:
              - $ref: '#/components/schemas/membershipRequest'
        meta:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/meta'
        members:
          type: array
          nullable: true
          items:
            allOf:
              - $ref: '#/components/schemas/groupMemberResponse'
        maxMembers:
          $ref: '#/components/schemas/maxMembers'
        joinRequestAction:
          $ref: '#/components/schemas/joinRequestAction'
        password:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/password'
        canMembersInvite:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/canMembersInvite'
        canCrossPlay:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/canCrossPlay'
        onlineServiceType:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
          description: the group creator's online service type. used when xplay membership requests are validated.
          example: 3
        ttl:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/ttl'
        errors:
          type: array
          nullable: true
          items:
            allOf:
              - $ref: '#/components/schemas/groupMembershipErrorResponse'
        groupCompositionId:
          type: integer
          format: int64
          description: incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
          nullable: true
    firstPartySessionid:
      type: string
      description: The First Party Session Id of the specified.
      example: *****************
    groupResponse:
      type: object
      required:
        - groupid
        - maxMembers
        - productid
        - joinRequestAction
        - created
        - membershipRequests
        - password
        - canMembersInvite
        - canCrossPlay
        - onlineServiceType
        - groupCompositionId
        - firstPartySessionid
      description: Productid only set as required due to codegen issues.
      properties:
        created:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/created'
        groupid:
          $ref: '#/components/schemas/groupid'
        productid:
          $ref: '#/components/schemas/productid'
        membershipRequests:
          type: array
          nullable: true
          items:
            allOf:
              - $ref: '#/components/schemas/membershipRequest'
        meta:
          $ref: '#/components/schemas/meta'
        members:
          type: array
          items:
            $ref: '#/components/schemas/groupMemberResponse'
        maxMembers:
          $ref: '#/components/schemas/maxMembers'
        joinRequestAction:
          $ref: '#/components/schemas/joinRequestAction'
        password:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/password'
        canMembersInvite:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/canMembersInvite'
        canCrossPlay:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/canCrossPlay'
        onlineServiceType:
          nullable: true
          allOf:
            - $ref: '#/components/schemas/onlineServiceType'
        groupCompositionId:
          type: integer
          format: int64
          description: incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
          nullable: true
        firstPartySessionid:
          $ref: '#/components/schemas/firstPartySessionid'
    emptyObject:
      type: object
      description: Empty object
      example: '{}'
    inviteGroupMember:
      type: object
      required:
        - memberid
      properties:
        memberid:
          $ref: '#/components/schemas/userid'
        isFirstPartyInvite:
          $ref: '#/components/schemas/isFirstPartyInvite'
    serverReturnMembershipErrors:
      type: boolean
      description: Should this trusted API call return errors that happened when adding users
      example: true
    endorsementName:
      type: string
      description: the name of the endorsement to be acted upon
    discoveryURLResponse:
      type: object
      required:
        - type
        - url
        - schema
        - host
        - port
        - path
        - query
        - fragment
      properties:
        type:
          type: string
          enum:
            - http
            - mqtt
            - trusted
          example: http
        url:
          type: string
          description: string url with port included with domain if needed
          example: wss://social-service-staging-additionalname.d2dragon.net/mqtt
        scheme:
          type: string
          description: optional piece of uri
          nullable: true
          example: wss
        host:
          type: string
          description: optional piece of uri
          nullable: true
          example: social-service-staging-additionalname.d2dragon.net
        port:
          type: string
          description: optional piece of uri
          nullable: true
          example: '443'
        path:
          type: string
          description: optional piece of uri
          nullable: true
          example: /mqtt
        query:
          type: string
          description: optional piece of uri
          nullable: true
          example: '?isQuery=true'
        fragment:
          type: string
          description: optional piece of uri
          nullable: true
          example: '#'
    discoveryResponse:
      type: object
      required:
        - id
        - description
        - urls
        - canList
      properties:
        id:
          type: string
          example: precert
          description: must be unique.  can be any string identifier. env/guid/etc.
        description:
          type: string
          example: Production Environment
        urls:
          type: array
          items:
            $ref: '#/components/schemas/discoveryURLResponse'
        canList:
          type: boolean
          example: true
          description: if true, this discovery will be returned in the discovery list response
          nullable: true
    discoveryListResponse:
      type: array
      required:
        - items
      items:
        $ref: '#/components/schemas/discoveryResponse'
    serverDiscoveryURLRequest:
      description: url for trusted api discovery request schema
      type: object
      required:
        - type
        - url
      properties:
        type:
          type: string
          enum:
            - http
            - mqtt
            - trusted
          example: http
        url:
          type: string
          description: string url with port included with domain if needed
          example: wss://social-service-staging-additionalname.d2dragon.net/mqtt
        scheme:
          type: string
          description: optional piece of uri
          example: wss
        host:
          type: string
          description: optional piece of uri
          example: social-service-staging-additionalname.d2dragon.net
        port:
          type: string
          description: optional piece of uri
          example: '443'
        path:
          type: string
          description: optional piece of uri
          example: /mqtt
        query:
          type: string
          description: optional piece of uri
          example: '?isQuery=true'
        fragment:
          type: string
          description: optional piece of uri
          example: '#'
    versionResponse:
      type: object
      required:
        - version
        - gitHash
        - buildDate
      properties:
        version:
          type: string
          example: 1.0.2-6623bad
        gitHash:
          type: string
          example: 6623bad133a5bbf8d6123e445d1c8d9ceeb45548
        buildDate:
          type: string
          example: 2023-10-27-19-19-32
    healthResponse:
      type: object
      required:
        - name
        - version
        - overall-status
        - generated
        - services
      properties:
        name:
          type: string
          example: t2gp-social-service
          nullable: true
        version:
          type: string
          example: v0.1.0-deadbeef
          nullable: true
        overall-status:
          type: string
          example: OK
          nullable: true
        generated:
          type: string
          example: '2023-10-30T20:01:11.199405036Z'
          nullable: true
        services:
          type: object
          nullable: true
          example:
            social-service-dynamodb: FAIL
            social-service-id-dna: OK
            social-service-redis: OK
            social-service-s3: OK
    serverLoginResponse:
      description: Successful server authentication response
      type: object
      required:
        - accessToken
        - accessTokenExpiresIn
        - refreshToken
        - refreshTokenExpiresIn
        - instanceId
      properties:
        accessToken:
          description: The access token
          type: string
          format: Id
          example: eyJhbGci...0jOtn_Bo
        accessTokenExpiresIn:
          description: The number of seconds until the access token expires
          type: integer
          format: uint32
          example: 3600
        refreshToken:
          description: The refresh token
          type: string
          format: Id
          example: eyJhbGci...xUpOD5qlY
        refreshTokenExpiresIn:
          description: The number of seconds until the refresh token expires
          type: integer
          format: uint32
          example: 7200
        instanceId:
          description: The instance id used to authenticate
          type: string
  requestBodies:
    serverCreateGroupRequestBody:
      description: Create group with members
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - groupLeader
              - onlineServiceType
              - joinRequestAction
            properties:
              maxMembers:
                $ref: '#/components/schemas/maxMembers'
              joinRequestAction:
                $ref: '#/components/schemas/joinRequestAction'
              password:
                $ref: '#/components/schemas/password'
              canMembersInvite:
                $ref: '#/components/schemas/canMembersInvite'
              canCrossPlay:
                $ref: '#/components/schemas/canCrossPlay'
              meta:
                $ref: '#/components/schemas/meta'
              groupLeader:
                $ref: '#/components/schemas/dnaid'
              onlineServiceType:
                $ref: '#/components/schemas/onlineServiceType'
              groupMembers:
                type: array
                items:
                  $ref: '#/components/schemas/joinGroupMember'
                maxItems: 100
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
              returnMembershipErrors:
                type: boolean
                default: false
                example: true
          examples:
            Create public group that anyone can join:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                maxMembers: 5
                joinRequestAction: auto-approve
            Create manual group with a password:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                joinRequestAction: manual
                password: '********'
            Create invite only group:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                joinRequestAction: auto-reject
                canMembersInvite: true
            Create a group where inviter has to accept invite:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                joinRequestAction: manual
                canMembersInvite: true
            Create crossplay group:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                joinRequestAction: manual
                canCrossPlay: true
            Create a group with meta data:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                joinRequestAction: manual
                meta:
                  key1: value1
                  key2: value2
            Create a non-crossplay group with additional group members:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                canCrossPlay: false
                maxMembers: 3
                joinRequestAction: auto-reject
                groupMembers:
                  - memberid: abcdef1234567890abcdef1234567890
                    canCrossPlay: false
                    onlineServiceType: 3
                  - memberid: 1234567890abcdef1234567890abcdef
                    canCrossPlay: true
                    onlineServiceType: 3
            Create a crossplay group with group additional members that returns membership errors:
              value:
                groupLeader: ********************************
                onlineServiceType: 3
                canCrossPlay: true
                maxMembers: 3
                joinRequestAction: auto-reject
                groupMembers:
                  - memberid: abcdef1234567890abcdef1234567890
                    canCrossPlay: true
                    onlineServiceType: 2
                  - memberid: 1234567890abcdef1234567890abcdef
                    canCrossPlay: true
                    onlineServiceType: 1
                returnMembershipErrors: true
    serverUpdateGroupRequestBody:
      description: Update group request body
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              maxMembers:
                $ref: '#/components/schemas/maxMembers'
              meta:
                $ref: '#/components/schemas/meta'
              joinRequestAction:
                $ref: '#/components/schemas/joinRequestAction'
              password:
                $ref: '#/components/schemas/password'
              canMembersInvite:
                $ref: '#/components/schemas/canMembersInvite'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    serverUpdateGroupMemberRequestBody:
      description: Update group member role body
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              role:
                $ref: '#/components/schemas/groupMemberRole'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
    serverSendControlMessageRequestBody:
      description: Control message request body. Max size for binary is 5120 bytes
      required: true
      content:
        application/json:
          schema:
            required:
              - payload
            properties:
              payload:
                type: string
                example: ZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFHRUFBQUJ4Q0FZQUFBREYwTTA0QUFBQUFYTlNSMElBcnM0YzZRQUFBQVJuUVUxQkFBQ3hqd3Y4WVFVQUFBQUpjRWhaY3dBQURzUUFBQTdFQVpVckRoc0FBQUt0U1VSQlZIaGU3WnRiVGdOQkRBU3puQ3hIejgwQ1NIelRJejg2SGFoSWZIbkg5bFF4bTNnRDErMTJlMzc5OEhvaGdZOFgxcWIwRHdFa0JQd3FJQUVKQVFRQ1d1QWtJQ0dBUUVBTG5BUWtCQkFJYUlHVGdJUUFBZ0V0Y0JJQ0pGeU9aMGZQNTNzL25ycXViMHg3TDA3Q0h0dmp6RWc0UnJWM0lSTDIyQjVuUnNJeHFyMExrYkRIOWpnekVvNVI3VjJJaEQyMng1bmJjOExKRExEOU9mdDR0OFVMMVI2NysrTWtGTVZNTGtQQ0pNMWlMaVFVd1UwdVE4SWt6V0l1SkJUQlRTNUR3aVROWWk0a0ZNRk5Ma1BDSk0xaUxpUVV3VTB1UThJa3pXSXVKQlRCVFM1RHdpVE5ZaTRrRk1GTkxrUENKTTFpTGlRVXdVMHVpL2crWWZ0NWZUZC9kNzBTeGtsUWhBeHhKQmdncXhKSVVJUU1jU1FZSUtzU1NGQ0VESEVrR0NDckVraFFoQXp4OXB6d2VEeGttL2Y3WFY2VGZJSGFZM2QvbklRQSswaEFRZ0NCZ0JZNENVZ0lJQkRRQWljQkNRRUVBbHF3ekFrQisxeHRnVGxoRmE4bk9lOEpIczYvVmtFQ0VnSUlCTFRBU1VCQ0FJR0FGamdKQVJJaTVnVDFPVnY5M1kvaXFQN1BXSDFmb1BLci90VjZUb0lpWklnandRQlpsVUNDSW1TSUk4RUFXWlZBZ2lKa2lDUEJBRm1WUUlJaVpJaEh6QW1HZmE2V1lFNVl4ZXRKenUzSXc1bnZFd0k0SXdFSjZRUUMrdU05QVFrQkJBSmFhTThKSjN2b1BxOC9xYkY1VFhjT1VMMXhPMUtFREhFa0dDQ3JFa2hRaEF4eEpCZ2dxeEpJVUlRTWNTUVlJS3NTU0ZDRURISExuTkRkeC9hY3NUMEhxUDF6RWhRaFF4d0pCc2lxQkJJVUlVTWNDUWJJcWdRU0ZDRkRIQWtHeUtvRUVoUWhRL3d0NWdURm9UdEhNQ2Nvd3Y4Z3p1MG9RRElTa0JCQUlLQUZUZ0lTQWdnRXRNQkpRRUlBZ1lBVy9zU3dGc0N4MVFLM294YSttY1ZJbU9IWXlvS0VGcjZaeFVpWTRkaktnb1FXdnBuRlNKamgyTXFDaEJhK21jVkltT0hZeW9LRUZyNlp4VWlZNGRqS2dvUVd2cG5GbjBlVFA4dGMwOTkvQUFBQUFFbEZUa1N1UW1DQw
              senderid:
                allOf:
                  - $ref: '#/components/schemas/dnaid'
                description: sender should be a dna userid or a dna server instance id.
              event:
                type: string
                example: pingLocation
                description: a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
              timestamp:
                $ref: '#/components/schemas/timestamp'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
        application/octet-stream:
          schema:
            type: string
            format: binary
    serverSendInviteRequestBody:
      description: Request Body for Trusted API invite users to group.  All users must have same OST and isFirstParty values.
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - members
              - inviterid
              - onlineServiceType
            properties:
              members:
                type: array
                items:
                  $ref: '#/components/schemas/inviteGroupMember'
              inviterid:
                $ref: '#/components/schemas/dnaid'
              onlineServiceType:
                $ref: '#/components/schemas/onlineServiceType'
              returnMembershipErrors:
                $ref: '#/components/schemas/serverReturnMembershipErrors'
              ttl:
                allOf:
                  - $ref: '#/components/schemas/ttl'
                default: 3600
                minimum: 1
                maximum: 2628288
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Invite a user:
              value:
                inviterid: 575190c5933c4fa6a8086477e4d33e23
                onlineServiceType: 3
                members:
                  - memberid: abcdef1234567890abcdef1234567890
            Invite a user via first party platform id:
              value:
                inviterid: 575190c5933c4fa6a8086477e4d33e23
                onlineServiceType: 3
                members:
                  - memberid: '12345684343'
                    isFirstPartyId: true
            Invite a user with non-default expiration:
              value:
                inviterid: 575190c5933c4fa6a8086477e4d33e23
                onlineServiceType: 3
                members:
                  - memberid: abcdef1234567890abcdef1234567890
                ttl: 300
            Invite multiple 2k users one of which is on a different platform:
              value:
                inviterid: 575190c5933c4fa6a8086477e4d33e23
                onlineServiceType: 3
                members:
                  - memberid: abcdef1234567890abcdef1234567890
                  - memberid: 1234567890abcdef1234567890abcdef
    serverJoinRequestRequestBody:
      description: membership request body
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - members
            properties:
              members:
                type: array
                items:
                  $ref: '#/components/schemas/joinGroupMember'
              password:
                $ref: '#/components/schemas/password'
              returnMembershipErrors:
                $ref: '#/components/schemas/serverReturnMembershipErrors'
              teleMeta:
                $ref: '#/components/schemas/telemetryMetaData'
          examples:
            Join a user to group:
              value:
                members:
                  - memberid: abcdef1234567890abcdef1234567890
                    canCrossPlay: true
                    onlineServiceType: 3
            Join 2 users to group:
              value:
                members:
                  - memberid: abcdef1234567890abcdef1234567890
                    canCrossPlay: true
                    onlineServiceType: 3
                  - memberid: 1234567890abcdef1234567890abcdef
                    canCrossPlay: true
                    onlineServiceType: 1
            Join a group with a password:
              value:
                password: somepass
                members:
                  - memberid: abcdef1234567890abcdef1234567890
                    canCrossPlay: true
                    onlineServiceType: 2
    serverUpsertDiscoveryRequestBody:
      description: Discovery entries
      required: true
      content:
        application/json:
          schema:
            type: array
            items:
              type: object
              required:
                - id
                - description
                - urls
              properties:
                id:
                  type: string
                  example: precert
                  description: must be unique.  can be any string identifier. env/guid/etc.
                description:
                  type: string
                  example: Production Environment
                urls:
                  type: array
                  items:
                    $ref: '#/components/schemas/serverDiscoveryURLRequest'
                canList:
                  type: boolean
                  example: true
                  description: if true, this discovery will be returned in the discovery list response
    serverRefreshTokenRequestBody:
      description: Refresh token request body
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - refreshToken
              - instanceId
            properties:
              refreshToken:
                description: Refresh token
                type: string
                example: eyJhbGci...xUpOD5qlY\
              instanceId:
                description: The instance id used to authenticate
                type: string
  responses:
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 400
            message: Bad request
    '401':
      description: Unauthorized - The request did not include the required authorization information
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 401
            message: Invalid bearer token
    '403':
      description: Forbidden - The requestor is not authorized to make the request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 403
            message: Forbidden
    '404':
      description: Not Found - The requested entity could not be found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 404
            message: Not found
    '429':
      description: Too Many Requests - The client has sent too many requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 429
            message: Too Many Requests
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 500
            message: Internal server error
    200empty:
      description: Success-Response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/emptyObject'
  parameters:
    pGroupid:
      name: pGroupid
      in: path
      required: true
      description: The id of the group
      schema:
        $ref: '#/components/schemas/groupid'
    pMemberid:
      name: pMemberid
      in: path
      required: true
      description: The id of the member
      schema:
        $ref: '#/components/schemas/userid'
    pEndorsementName:
      name: pEndorsementName
      in: path
      required: true
      description: The name of the endorsement to be acted upon
      schema:
        $ref: '#/components/schemas/endorsementName'
    pUserid:
      name: pUserid
      in: path
      required: true
      description: The id of the user
      schema:
        $ref: '#/components/schemas/userid'
    discoveryid:
      name: discoveryid
      in: query
      allowEmptyValue: true
      description: Get specific discovery endpoint based on id.  Must also send Authorization header
      schema:
        type: string
        example: precert
        description: The id of the discovery set desired.  '?id=' will return all sets for given product.
    discoveryPid:
      name: discoveryPid
      in: query
      allowEmptyValue: true
      description: productid to filter by.
      schema:
        $ref: '#/components/schemas/dnaid'
    healthid:
      name: id
      in: query
      description: Get specific identity provider for health check based on id.
      allowEmptyValue: true
      schema:
        type: string
        example:
          - dna
          - pdi
          - rsg
        description: The id of the identity provider desired. No parameter will return health check without identity info.
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
