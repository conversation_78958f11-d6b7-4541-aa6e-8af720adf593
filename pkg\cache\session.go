package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

func (rc *RedisCache) GetSession(ctx context.Context, userId, tenant, sessionId string) (*apipub.DnaSession, error) {
	log := logger.FromContext(ctx)
	key := buiidSessionCacheKey(userId, tenant, sessionId)
	res := rc.get(ctx, key)
	if res == nil || res.Err() != nil {
		log.Error().Str("key", key).Msgf("could not find the target session for user %s", userId)
		return nil, nil
	}

	return getDnaSession([]byte(res.Val())), nil
}

func getDnaSession(bytes []byte) *apipub.DnaSession {
	var result apipub.DnaSession
	_ = json.Unmarshal(bytes, &result)
	return &result
}

func (rc *RedisCache) SetSession(ctx context.Context, productid, appid, sessionid, userid string, createdTime, expiresTime int64, tenant string) error {

	switch tenant {
	case "dna":
		return setDnaSession(rc, ctx, productid, appid, sessionid, userid, createdTime, expiresTime)
	}

	return nil
}

func setDnaSession(rc *RedisCache, ctx context.Context, productid, appid, sessionid, userid string, createdTime, expiresTime int64) error {
	log := logger.FromContext(ctx)
	session := apipub.DnaSession{
		SessionId:   sessionid,
		ProductId:   productid,
		AppId:       appid,
		CreatedTime: createdTime,
		ExpiresTime: expiresTime,
	}

	sessionBytes, _ := json.Marshal(session)
	key := buiidSessionCacheKey(userid, "dna", session.SessionId)
	ttl := session.ExpiresTime - session.CreatedTime
	res := rc.set(ctx, key, sessionBytes, time.Duration(ttl)*time.Second)

	if res == nil || res.Err() != nil {
		log.Error().Str("key", key).Msg("failed to set DNA session")
		return res.Err()
	}

	return nil
}

func buiidSessionCacheKey(userId, tenant, sessionId string) string {
	return fmt.Sprintf("%s:user:%s:session:%s", tenant, userId, sessionId)
}
