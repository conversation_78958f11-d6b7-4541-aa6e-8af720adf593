#membershipStatuses:
#  name: membershipStatuses
#  in: query
#  allowEmptyValue: true
#  description: status to filter
#  schema:
#    type: array
#    items:
#      membershipStatus:
#        type: string
#        description:
#          The membership status of the invite or request to join state.
#          * requested - a request to join flow initiated
#          * approved - group join request has been approved
#          * rejected - group join request has been rejected
#          * invited - the user has been invited to the group
#          * accepted - the user has accepted the invite to the group
#          * declined - the invite has been declined
#          * revoked - the invite has been revoked
#        enum: [ 'requested', 'approved', 'rejected', 'invited', 'accepted', 'declined', 'revoked' ]
#    example: [ 'invited', 'requested' ]
#  allowEmptyValue: true
discoveryPid:
  name: discoveryPid
  in: query
  allowEmptyValue: true
  description: productid to filter by.
  schema:
    $ref: "../schemas/fields.yaml#/dnaid"
next:
  name: next
  in: query
  allowEmptyValue: true
  description: Next value used for pagination.  Empty value accepted for first page.
  schema:
    type: string
limit:
  name: limit
  in: query
  description: How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
  schema:
    type: integer
    maximum: 100
    example: 10
discoveryid:
  name: discoveryid
  in: query
  allowEmptyValue: true
  description: Get specific discovery endpoint based on id.  Must also send Authorization header
  schema:
    type: string
    example: precert
    description: The id of the discovery set desired.  '?id=' will return all sets for given product.
healthid:
  name: id
  in: query
  description: Get specific identity provider for health check based on id.
  allowEmptyValue: true
  schema:
    type: string
    example: [ 'dna', 'pdi', 'rsg' ]
    description: The id of the identity provider desired. No parameter will return health check without identity info.
status:
  name: status
  in: query
  allowEmptyValue: true
  schema:
    $ref: "../schemas/fields.yaml#/friendStatus"
displayName:
  name: displayName
  in: query
  description: The full account DNA displayname to be searched for.
  required: true
  schema:
    $ref: "../schemas/fields.yaml#/dnaDisplayName"