// Package net implements network related functions
package net

import (
	"context"
	"github.com/rs/zerolog/log"
	"net"
	"net/http"
	"sync"
	"sync/atomic"
	"time"

	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

const resolveTimeout = time.Second * 5

const defaultDnsTtl = time.Minute

var Dns = NewDNSResolver(defaultDnsTtl)

type DNSResolver struct {
	dnsTtl         time.Duration
	hosts          hostRegistry
	records        *recordsPool
	iprPool        *ipRecordsPool
	registerHost   chan string
	datadogEnabled bool
}

type IPResults interface {
	Next() (net.IP, bool)
	Free()
}

var ipListPool = sync.Pool{
	New: func() any {
		return &ipList{}
	},
}
var _ IPResults = &ipList{}

type ipList struct {
	ips     []net.IP
	current uint32
	read    int
}

func (i *ipList) Free() {
	i.ips = nil
	i.current = 0
	i.read = 0
	ipListPool.Put(i)
}

func (i *ipList) Next() (net.IP, bool) {
	size := len(i.ips)
	if i.read == size {
		return nil, false
	}
	idx := i.current % uint32(size)
	i.current++
	i.read++
	return i.ips[idx], true
}

var oneIpPool = sync.Pool{
	New: func() any {
		return &oneIp{}
	},
}
var _ IPResults = &oneIp{}

type oneIp struct {
	ip   net.IP
	read bool
}

func (o *oneIp) Free() {
	o.ip = nil
	o.read = false
	oneIpPool.Put(o)
}

func (o *oneIp) Next() (net.IP, bool) {
	if !o.read {
		o.read = true
		return o.ip, true
	}

	return nil, false
}

type hostRegistry map[string]struct{}
type dnsRecords map[string]*ipRecords

type recordsPool struct {
	buffer  [2]dnsRecords
	current atomic.Uint32
}

func (r *recordsPool) next() uint32 {
	ptr := r.current.Load()
	return ptr ^ 1
}

func (r *recordsPool) publish(ptr uint32) {
	r.current.Store(ptr)
}

func (r *recordsPool) get() dnsRecords {
	ptr := r.current.Load()
	return r.buffer[ptr]
}

type ipRecords struct {
	ips  []net.IP
	head atomic.Uint32
}

func (i *ipRecords) next() IPResults {
	if len(i.ips) == 1 {
		ret := oneIpPool.Get().(*oneIp)
		ret.ip = i.ips[0]
		return ret
	}
	ret := ipListPool.Get().(*ipList)
	ret.ips = i.ips
	ret.current = i.head.Add(1)
	return ret
}

func NewDNSResolver(dnsTtl time.Duration) *DNSResolver {
	capacity := 1024
	dns := &DNSResolver{
		dnsTtl: dnsTtl,
		hosts:  make(hostRegistry, capacity),
		records: &recordsPool{
			buffer: [2]dnsRecords{
				make(dnsRecords, capacity),
				make(dnsRecords, capacity),
			},
		},
		iprPool:      newIpRecordsPool(uint(capacity * 3)),
		registerHost: make(chan string, capacity*8),
	}
	return dns
}

func (d *DNSResolver) DatadogEnabled(enabled bool) {
	d.datadogEnabled = enabled
}

type TransportSetup func(t *http.Transport)

func (d *DNSResolver) HttpClient(timeout time.Duration, ts TransportSetup) *http.Client {
	t := http.DefaultTransport.(*http.Transport).Clone()
	ts(t)
	baseDial := t.DialContext
	t.DialContext = func(ctx context.Context, network, addr string) (net.Conn, error) {
		host, port, err := net.SplitHostPort(addr)
		if err != nil {
			return nil, err
		}

		ips, err := d.LookupIP(ctx, host)
		if err != nil {
			return nil, err
		}
		defer ips.Free()

		var dialErr error
		var conn net.Conn
		for {
			if ip, ok := ips.Next(); ok {
				conn, dialErr = baseDial(ctx, network, net.JoinHostPort(ip.String(), port))
				if dialErr == nil {
					return conn, nil
				}
			} else {
				break
			}
		}

		return nil, dialErr
	}
	c := &http.Client{
		Timeout:   timeout,
		Transport: t,
	}
	return c
}

func (d *DNSResolver) Start() {
	go func() {
		ctx := context.Background()
		ticker := time.NewTicker(d.dnsTtl)
		batchSize := 128
		for {
			select {
			case host := <-d.registerHost:
				//batch up the registrations, by waiting for a second
				buf := make(hostRegistry, batchSize)
				buf[host] = struct{}{}
				time.Sleep(time.Second * 1)
				batched := 1
			batch:
				for {
					select {
					case host = <-d.registerHost:
						buf[host] = struct{}{}
						batched++
						if batched >= batchSize {
							break batch
						}
					default:
						break batch
					}
				}
				//flush
				for h := range buf {
					d.hosts[h] = struct{}{}
				}
				d.update(ctx)
			case <-ticker.C:
				d.update(ctx)
			}
		}
	}()
}

func (d *DNSResolver) LookupIP(ctx context.Context, host string) (IPResults, error) {
	dns := d.records.get()
	if ips, ok := dns[host]; ok {
		return ips.next(), nil
	}
	//cache miss, either not registered in hosts or cache records don't have it yet.
	//register for dns caching
	d.registerHost <- host

	//use default net.DefaultResolver.LookupIPAddr and return
	ips, err := d.lookupIP(ctx, host)
	if err != nil {
		return nil, err
	}

	if len(ips) == 1 {
		ret := oneIpPool.Get().(*oneIp)
		ret.ip = ips[0]
		return ret, nil
	}
	ret := ipListPool.Get().(*ipList)
	ret.ips = ips
	return ret, nil
}

func (d *DNSResolver) update(ctx context.Context) {
	hosts := d.hosts
	if len(hosts) == 0 {
		return
	}

	idx := d.records.next()
	dns := d.records.buffer[idx]
	for host := range hosts {
		if ipr, ok := dns[host]; ok {
			d.iprPool.put(ipr)
		}
		if ips, err := d.lookupIP(ctx, host); err == nil {
			ipr := d.iprPool.get()
			ipr.ips = ips
			dns[host] = ipr
		} else {
			delete(dns, host)
			log.Err(err).Str("ctx", "t2gp.dns.cache").
				Str("op", "dns.lookup").
				Str("host", host).
				Str("ex", "dns lookup failed").
				Msg("")
		}
	}
	d.records.publish(idx)
}

func (d *DNSResolver) lookupIP(ctx context.Context, host string) ([]net.IP, error) {
	tCtx, cancel := context.WithTimeout(ctx, resolveTimeout)
	defer cancel()

	start := time.Now()
	addrs, err := net.DefaultResolver.LookupIPAddr(tCtx, host)
	end := time.Now()

	if d.datadogEnabled {
		if _, ok := tracer.SpanFromContext(tCtx); !ok {
			s, _ := tracer.StartSpanFromContext(tCtx, "dns.lookup",
				tracer.ServiceName("t2gp.dns.cache"),
				tracer.ResourceName(host),
				tracer.StartTime(start))
			if err == nil {
				s.SetTag("ips", addrs)
				s.Finish(tracer.FinishTime(end))
			} else {
				s.Finish(tracer.FinishTime(end),
					tracer.WithError(err))
			}
		}
	}
	if err != nil {
		return nil, err
	}
	ips := make([]net.IP, len(addrs))
	for i, ia := range addrs {
		ips[i] = ia.IP
	}
	log.Debug().Str("ctx", "t2gp.dns.cache").
		Str("op", "dns.lookup").
		Str("host", host).
		Interface("ips", ips).
		Dur("elapsed", end.Sub(start)).
		Msg("")

	return ips, nil
}

type ipRecordsPool struct {
	buffer []*ipRecords
	size   uint
	head   uint
}

func newIpRecordsPool(size uint) *ipRecordsPool {
	ret := &ipRecordsPool{
		buffer: make([]*ipRecords, size),
		size:   size,
		head:   0,
	}
	for i := uint(0); i < size; i++ {
		ret.buffer[i] = &ipRecords{
			ips:  nil,
			head: atomic.Uint32{},
		}
	}
	return ret
}

func (i *ipRecordsPool) get() *ipRecords {
	head := i.head
	if head < i.size {
		ret := i.buffer[head]
		i.buffer[head] = nil
		i.head++
		return ret
	}
	return &ipRecords{
		ips:  nil,
		head: atomic.Uint32{},
	}
}

func (i *ipRecordsPool) put(ipr *ipRecords) {
	if i.head == 0 {
		return
	}

	i.head--
	ipr.ips = nil
	ipr.head.Store(0)
	i.buffer[i.head] = ipr
}
