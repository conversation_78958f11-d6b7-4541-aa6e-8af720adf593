#!/bin/bash

set -ex

aws s3 cp --region us-east-1 s3://$VERNEMQ_PLUGIN_BUCKET/$SUBMODULE_HASH.tar.gz ./_build/vmq-plugin-social.tar.gz
kubectl --context $CLUSTER wait --for=condition=ready pod -l app=mqtt,env=$TARGET_ENV,project=social-service -n social-service --timeout=300s
PODS=$(kubectl --context $CLUSTER get pods --no-headers=true -l app=mqtt,env=$TARGET_ENV,project=social-service -n social-service -o custom-columns=":metadata.name" | awk '{print $1}' )
for pod in $PODS; do
  echo "Deploying to $pod";
  for i in 1 2 3 4 5; do # For every pod, retry swap upto 5 times
    (
      kubectl --context $CLUSTER cp -n social-service -c social-mqtt ./_build/vmq-plugin-social.tar.gz $pod:/tmp/vmq-plugin-social.tar.tgz &&
      kubectl --context $CLUSTER exec -i -n social-service $pod --container social-mqtt -- bash -c "cd /vernemq/plugins/t2gp-social && tar -xf /tmp/vmq-plugin-social.tar.tgz" &&
      kubectl --context $CLUSTER exec -i -n social-service $pod --container social-mqtt -- bash -c "vmq-admin t2gp reload" &&
      kubectl --context $CLUSTER exec -i -n social-service $pod --container social-mqtt -- bash -c "vmq-admin t2gp version"
    ) && break || (
      if [[ $i -eq 5 ]]; then
        echo "Plugin swap failed after 5 retries."
        exit 1
      fi
      sleep 3
    )
    if [[ $? -eq 1 ]]; then
      exit 1
    fi
  done
done