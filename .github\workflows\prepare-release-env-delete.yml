name: Destroy Release Candidate Env
run-name: Destroy Release Candidate Env ${{ github.event.inputs.version || github.head_ref  }}

on:
  release:
    types: [published]

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

concurrency: release-${{ github.head_ref }}
jobs:
  build:
    name: 'Destroy existing infrastructure'
    env:
      CLUSTER: t2gp-non-production
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
    runs-on: [t2gp-arc-linux]

    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_ecr_rw
          role-session-name: GHActionSession
          aws-region: us-east-1
      - uses: actions/checkout@v4
        with:
          ref: ${{github.sha}}
      - name: Environment Variables
        run: |
          REF=${{ env.GITHUB_REF }}
          RC_TAG=${GITHUB_REF:10}
          echo RELEASE_NAME="release-${RC_TAG//./-}" >> $GITHUB_ENV
          echo "release_name=${{ env.RELEASE_NAME }}" >> $GITHUB_OUTPUT
      - name: Helm Uninstall social-trusted-api
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          sandbox: true
          uninstall: true
          service: social-trusted-api
          environment: ${{env.RELEASE_NAME}}
      - name: Helm Uninstall social-service
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          sandbox: true
          uninstall: true
          service: social-service
          environment: ${{env.RELEASE_NAME}}
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: delete
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          key: '{ "env_label": "${{env.RELEASE_NAME}}" }'

  update-discovery:
    needs: [build]
    uses: ./.github/workflows/update-discovery.yml
    with:
      action: remove
      id: ${{ needs.build.outputs.release_name }}
