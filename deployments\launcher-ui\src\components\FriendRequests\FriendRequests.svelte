<script lang="ts">
  import { ACCORDION_MODE } from '../../constant';
  import { useTranslator, useUserQuery } from '../../hooks';
  import { usePendingFriendsQuery } from '../../hooks/usePendingFriendsQuery';
  import {
    isQueryLoaded,
    isQueryLoading,
    requestReceivedByUser,
    requestSentByUser,
  } from '../../utils';
  import { Accordion, AccordionSection } from '../Accordion';
  import { FriendRequestCard } from '../FriendRequestCard';
  import { FriendRequestsActionBar } from '../FriendRequestsActionBar';
  import { LoadingSpinner } from '../LoadingSpinner';

  const userQueryResult = useUserQuery();
  const t = useTranslator();
  const pendingFriendsQueryResult = usePendingFriendsQuery();

  $: userProfle = $userQueryResult?.data;
  $: queryLoading = isQueryLoading($pendingFriendsQueryResult);
  $: queryLoaded = isQueryLoaded($pendingFriendsQueryResult);
  $: pendingFriends = $pendingFriendsQueryResult?.data || [];
  $: userId = userProfle?.userid;
  $: sentRequests = [...pendingFriends.filter(requestSentByUser(userId))];
  $: receivedRequests = pendingFriends && [
    ...pendingFriends.filter(requestReceivedByUser(userId)),
  ];
</script>

<style>
  .friend-requests {
    height: 100%;
  }

  .friend-requests .content {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .friend-requests .content :global(.accordion) {
    display: flex;
    flex-direction: column;
  }

  .friend-requests .content :global(.request-section-container) {
    flex: 0;
  }

  .friend-requests .content :global(.request-section-content) {
    flex: 0;
    padding: 1rem;
  }
</style>

<div class="friend-requests">
  <FriendRequestsActionBar />
  <div class="content">
    {#if queryLoading}
      <LoadingSpinner />
    {:else if queryLoaded}
      <Accordion className="accordion" mode="{ACCORDION_MODE.multiple}">
        <AccordionSection
          title="{`requests received (${receivedRequests.length})`}"
          key="received-requests"
          containerClassName="request-section-container"
          mode="{ACCORDION_MODE.multiple}"
        >
          {#if receivedRequests.length > 0}
            <div class="request-section-content">
              {#each receivedRequests as request}
                <FriendRequestCard
                  displayName="{request.name || request.userid}"
                  platform="steam"
                  platformDisplayName="missing from api"
                  message="{request.message}"
                  friendId="{request.friendid}"
                  cancelTooltip="{$t('Reject')}"
                  acceptTooltip="{$t('Accept')}"
                  received
                />
              {/each}
            </div>
          {/if}
        </AccordionSection>
        <AccordionSection
          title="{`requests sent (${sentRequests.length})`}"
          key="sent-requests"
          containerClassName="request-section-container"
          mode="{ACCORDION_MODE.multiple}"
        >
          {#if sentRequests.length > 0}
            <div class="request-section-content">
              {#each sentRequests as request}
                <FriendRequestCard
                  displayName="{request.invitee}"
                  platform="steam"
                  platformDisplayName="missing from api"
                  friendId="{request.friendid}"
                  cancelTooltip="{$t('Cancel Request')}"
                />
              {/each}
            </div>
          {/if}
        </AccordionSection>
      </Accordion>
    {/if}
  </div>
</div>
