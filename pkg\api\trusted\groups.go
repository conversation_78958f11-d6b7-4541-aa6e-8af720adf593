package api

import (
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/rs/zerolog"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

// ServerCreateGroup create a group
func (tApi *SocialTrustedAPI) ServerCreateGroup(w http.ResponseWriter, r *http.Request) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := jwt.ProductID
	log := logger.Get(r)

	var createGroup apitrusted.ServerCreateGroupRequestBody
	if !api.DecodeBody(w, r, &createGroup) {
		log.Error().Msg("Error decoding ServerCreateGroupRequest")
		return
	}

	if createGroup.GroupLeader == "" {
		log.Error().Msg("No group leader in create group")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMissingLeader))
		return
	}

	publicCgr, err := utils.TypeConverter[apipub.CreateGroupRequestBody](createGroup)
	if err != nil {
		log.Error().Err(err).Msg("Error converting CreateGroupRequest from TS to Pub")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsGeneric))
		return
	}

	maxMembers := 2
	if createGroup.MaxMembers != nil {
		maxMembers = *createGroup.MaxMembers
	}

	if createGroup.Password != nil {
		publicCgr.Password = createGroup.Password
	}

	if leaderAndGroupMemberCount(createGroup.GroupLeader, createGroup.GroupMembers) > maxMembers {
		log.Error().Err(err).Str("productid", productid).Int("maxmembers", maxMembers).Msg("too many members to add")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsTooManyUsersForGroup))
		return
	}

	group := tApi.SocialApi.CreateAndGetGroup(w, r, productid, constants.TrustedServer, createGroup.GroupLeader, "", apipub.OnlineServiceType(createGroup.OnlineServiceType), publicCgr, int64(0), int64(0))
	if group == nil {
		//createAndGetGroup throws http errors on group create
		return
	}

	groupWithMembersAndErrs := tApi.addGroupMembers(w, r, group, createGroup.GroupMembers, createGroup.ReturnMembershipErrors, publicCgr.TeleMeta)

	//if we added group members, return that otherwise return original group
	if groupWithMembersAndErrs != nil {
		api.ReturnCreated(w, r, groupWithMembersAndErrs)
	} else {
		api.ReturnCreated(w, r, group)
	}
}

// ServerDeleteGroup delete a group item
func (tApi *SocialTrustedAPI) ServerDeleteGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	tApi.SocialApi.DeleteGroupHelper(w, r, g, constants.TrustedServer, jwt.ProductID, constants.TrustedServer, 0)
}

// ServerUpdateGroup edit group
func (tApi *SocialTrustedAPI) ServerUpdateGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	tApi.SocialApi.UpdateGroupHelper(w, r, g, constants.TrustedServer, jwt.ProductID, constants.TrustedServer, 0, "", 0, 0)
}

// ServerGetGroup get a specific group
func (tApi *SocialTrustedAPI) ServerGetGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	tApi.SocialApi.GetGroupHelper(w, r, g, constants.TrustedServer, jwt.ProductID, apipub.OnlineServiceTypeUNKNOWN)
}

// ServerKickMemberFromGroup kick member from group
func (tApi *SocialTrustedAPI) ServerKickMemberFromGroup(w http.ResponseWriter, r *http.Request, groupId, userId string, params apitrusted.ServerKickMemberFromGroupParams) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	tApi.SocialApi.KickOrLeaveGroup(w, r, groupId, constants.TrustedServer, userId, params.Reason, jwt.ProductID, constants.TrustedServer, "", 0, 0, 0)
}

// ServerUpdateGroupMember update group member
func (tApi *SocialTrustedAPI) ServerUpdateGroupMember(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, memberid apipub.PUserid) {
	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	tApi.SocialApi.UpdateGroupMemberHelper(w, r, g, memberid, constants.TrustedServer, jwt.ProductID, constants.TrustedServer, 0)
}

// ServerSendControlMessage send control message from trusted server
func (tApi *SocialTrustedAPI) ServerSendControlMessage(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	log := logger.Get(r)

	jwt, tErr := authheader.ParseServerJWTFromRequest(r, tApi.jwk)
	if tErr != nil || jwt == nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := jwt.ProductID

	if utils.ArrayStrContainsString(r.Header[constants.KContentType], constants.KApplicationOctetStream) {
		tApi.SocialApi.SendBinControlMessageHelper(w, r, g, constants.TrustedServer, productid, constants.TrustedServer, 0)
		return
	}

	var controlMessage apitrusted.ServerSendControlMessageRequestBody
	if !api.DecodeBody(w, r, &controlMessage) {
		log.Error().Msg("Error decoding ServerControlMessage")
		return
	}

	publicControl, err := utils.TypeConverter[apipub.SendControlMessageRequestBody](controlMessage)
	if err != nil {
		log.Error().Err(err).Msg("Error converting ServerControlMessage from TS to Pub")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsGeneric))
		return
	}

	tApi.SocialApi.SendControlMessageHelper(w, r, g, constants.TrustedServer, productid, constants.TrustedServer, 0, publicControl)
}

func (tApi *SocialTrustedAPI) addGroupMembers(w http.ResponseWriter, r *http.Request, group *apipub.GroupResponse, addMembers *[]apitrusted.JoinGroupMember, bRetErrors *bool, teleMeta *apipub.TelemetryMetaData) *apitrusted.GroupWithErrorsResponse {
	//if we have group members to add try to add them
	if addMembers != nil && len(*addMembers) > 0 {

		log := logger.Get(r)
		var groupWithMembersAndErrs *apitrusted.GroupWithErrorsResponse
		var groupMemberErrors []apitrusted.GroupMembershipErrorResponse

		for i, memberObj := range *addMembers {
			var memberErr *errs.Error
			//Try to get the parent account id if isFirstParty = true
			fullAccountId := tApi.SocialApi.GetParentAccountId(r.Context(), memberObj.Memberid, apipub.OnlineServiceType(memberObj.OnlineServiceType), aws.Bool(false))

			//check if member is already in group
			if group.IsMember(fullAccountId) {
				memberErr = errs.New(http.StatusUnprocessableEntity, errs.EGroupsAlreadyInGroup)
			} else {
				sendJoinRequest := apipub.RequestJoinRequestBody{
					CanCrossPlay: memberObj.CanCrossPlay,
					TeleMeta:     teleMeta,
				}
				memberErr = tApi.SocialApi.SendJoinRequestWithHttpReturn(w, r, group.Groupid, group.Productid, constants.TrustedServer, apipub.OnlineServiceType(memberObj.OnlineServiceType), nil, false, memberObj.Memberid, "leader", sendJoinRequest)
			}

			if memberErr != nil {
				{
					log.Error().Err(memberErr).Int("index", i).Str("memberId", memberObj.Memberid).Interface("memberObj", memberObj).Msg("Error adding group member")

					tsErr, memConvErr := utils.TypeConverter[apitrusted.Error](memberErr)
					if memConvErr != nil {
						log.Error().Err(memConvErr).Msg("Error converting memberErr from errs.Error to apitrusted.Error")
					}

					groupMembershipError := apitrusted.GroupMembershipErrorResponse{
						Memberid: memberObj.Memberid,
						Error:    *tsErr,
					}

					groupMemberErrors = append(groupMemberErrors, groupMembershipError)
				}
			}
		}
		//get group to get all members added.
		groupWithMembers, errMem := tApi.SocialApi.Cache.GetGroup(r.Context(), group.Productid, group.Groupid)

		if errMem != nil || groupWithMembers == nil {
			log.Error().Err(errMem).Str("groupid", group.Groupid).Msg("Error getting group after adding members")
			groupWithMembersAndErrs = convertGroupToGroupWithErrors(group, log)
		} else {
			groupWithMembersAndErrs = convertGroupToGroupWithErrors(groupWithMembers, log)
		}

		//if we want to return errors
		if groupWithMembersAndErrs != nil && bRetErrors != nil && *bRetErrors && len(groupMemberErrors) > 0 {
			//add errors to return obj
			groupWithMembersAndErrs.Errors = &groupMemberErrors
		}

		// iterate members to get presence and filter linked accounts
		if groupWithMembersAndErrs.Members != nil {
			var members []apitrusted.GroupMemberResponse
			memberids := make([]string, 0, len(*groupWithMembersAndErrs.Members))
			for _, member := range *groupWithMembersAndErrs.Members {
				memberids = append(memberids, member.Userid)
			}
			memberProfiles, _ := tApi.SocialApi.GetUserProfiles(r.Context(), memberids, true)
			var profile *apipub.UserProfileResponse

			for _, member := range *groupWithMembersAndErrs.Members {
				if member.Name == nil || *member.Name == "" || member.Links == nil {
					profile = nil
					if memberProfiles != nil {
						for _, p := range *memberProfiles {
							if p.Userid == member.Userid {
								profile = p
								break
							}
						}
					}

					if profile != nil && profile.DisplayName != nil {
						member.Name = profile.DisplayName
					}
					if profile != nil && profile.Links != nil {
						trustedLinks, _ := utils.TypeConverter[[]apitrusted.AccountLinkDNA](profile.Links)
						member.Links = trustedLinks
					}
				}

				// update active group record for all the members added then add presence record to response
				tApi.SocialApi.Cache.SetActiveGroup(r.Context(), group, constants.TrustedServer, constants.TrustedServer, "", member.Userid, 0, 0, true)

				memPresence, _ := tApi.SocialApi.Cache.GetPresence(r.Context(), member.Userid, constants.TrustedServer, "")
				trustedPresence, _ := utils.TypeConverter[apitrusted.PresenceResponse](memPresence)
				member.Presence = trustedPresence

				members = append(members, member)
			}
			groupWithMembersAndErrs.Members = &members
		}

		return groupWithMembersAndErrs
	}

	return nil
}

func convertGroupToGroupWithErrors(group *apipub.GroupResponse, log *zerolog.Logger) *apitrusted.GroupWithErrorsResponse {
	groupWithMembersAndErrs, err := utils.TypeConverter[apitrusted.GroupWithErrorsResponse](group)
	if err != nil {
		log.Error().Err(err).Msg("Error converting from apipub.Group to apitrusted.GroupWithErrors")
		return nil
	}
	return groupWithMembersAndErrs
}

func leaderAndGroupMemberCount(leaderId string, newMembers *[]apitrusted.JoinGroupMember) int {
	count := 1
	if newMembers == nil || len(*newMembers) == 0 {
		return count
	}

	occurred := map[string]bool{}
	for _, member := range *newMembers {
		occurred[member.Memberid] = true
	}
	count = len(occurred)

	if !occurred[leaderId] {
		count++
	}
	return count
}

//func uniqueGroupMemberCount(currentMembers *[]apipub.GroupMemberResponse, newMembers *[]apitrusted.JoinGroupMember) int {
//	currentLen := 0
//	if currentMembers != nil {
//		currentLen = len(*currentMembers)
//	}
//	if newMembers == nil || len(*newMembers) == 0 {
//		return currentLen
//	}
//
//	occurred := map[string]bool{}
//	if currentMembers != nil {
//		for _, member := range *currentMembers {
//			occurred[member.Userid] = true
//		}
//	}
//
//	for _, member := range *newMembers {
//		if !occurred[member.Memberid] {
//			occurred[member.Memberid] = true
//		}
//	}
//
//	return len(occurred)
//}
