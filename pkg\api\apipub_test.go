package api

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func makeRequest() *http.Request {
	req, _ := http.NewRequest("GET", "http://example.com/foo", nil)
	return req
}

func TestNewSocialPublicAPI(t *testing.T) {

	if pubApi == nil {
		t.<PERSON><PERSON>("NewSocialPublicAPI() returned nil")
		return
	}

	if ds == nil {
		t.<PERSON><PERSON>("socialAPI.Ds is nil")
	}
}

func TestReturn2XX(t *testing.T) {
	r := makeRequest()
	w := httptest.NewRecorder()

	ReturnEmptyOK(w, r)
	if w.Code != 200 {
		t.<PERSON><PERSON><PERSON>("Expect 200 but got %d", w.Code)
	}

	w = httptest.NewRecorder()
	ReturnCreated(w, r, map[string]string{})
	if w.Code != 201 {
		t.<PERSON><PERSON><PERSON>("Expect 201 but got %d", w.Code)
	}
}
