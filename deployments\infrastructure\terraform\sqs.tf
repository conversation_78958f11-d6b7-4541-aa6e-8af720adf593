resource "aws_sqs_queue" "social_profile_sync" {
  name                      = "t2gp-${local.resource_prefix}-social-profile-sync"
  delay_seconds             = 90
  max_message_size          = 2048
  message_retention_seconds = 86400
  receive_wait_time_seconds = 10
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.social_profile_sync_dlq.arn
    maxReceiveCount     = 3
  })

  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social-profile-sync"
  }, local.tags)
}

resource "aws_sqs_queue" "social_profile_sync_dlq" {
  name = "t2gp-${local.resource_prefix}-social-profile-sync-dlq"
  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    # provider 4.2.0 will cause a cyclic dependency if referring the ARN directly
    # until a fix is patched, hard code ARN is needed
    sourceQueueArns = ["arn:aws:sqs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:t2gp-${local.resource_prefix}-social-profile-sync"]
  })
  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social-profile-sync-dlq"
  }, local.tags)
}