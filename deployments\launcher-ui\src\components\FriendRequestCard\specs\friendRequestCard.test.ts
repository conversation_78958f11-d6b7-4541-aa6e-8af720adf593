import { render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  transportServiceMock,
} from '../../../services/__mocks__';
import AvatarMock from '../../Avatar/__mock__/Avatar.svelte';
import LoadingSpinnerMock from '../../LoadingSpinner/__mock__/LoadingSpinner.svelte';
import TooltipMock from '../../Tooltip/__mock__/Tooltip.svelte';
import FriendRequestCardWrapper from './FriendRequestCardWrapper.svelte';

jest.mock('../../Avatar', () => ({
  Avatar: AvatarMock,
}));

jest.mock('../../Tooltip', () => ({
  Tooltip: TooltipMock,
}));

jest.mock('../../../assets/icons', () => ({
  SVGSteam: SVGIconMock,
  SVGAddFriend: SVGIconMock,
  SVGAccept: SVGIconMock,
  SVGCancel: SVGIconMock,
}));

jest.mock('../../LoadingSpinner', () => ({
  LoadingSpinner: LoadingSpinnerMock,
}));

const socialServicesMock = new SocialServices({
  transportService: transportServiceMock,
  friendsService: friendsServiceMock,
});

describe('FriendRequestCard', () => {
  it('should render UI', () => {
    const { container } = render(FriendRequestCardWrapper, {
      props: {
        context: socialServicesMock,
      },
    });
    expect(container).not.toBeNull();
  });
});
