package cache

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

// AddToUserBlockList set blocklist to redis.  Set both index and cached object.
func (rc *RedisCache) AddToUserBlockList(ctx context.Context, blocklist *[]*apipub.BlocklistResponse, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	for _, block := range *blocklist {

		if block.Userid == "" || block.Blockedid == "" {
			continue
		}

		// set object
		err := setCachedObject(ctx, rc, block, block.RedisKey(tenant), ttl)
		if err != nil {
			return err
		}

		blockerid := block.Userid
		blockedid := block.Blockedid

		// set blocked index
		blockerSubject := index.NewUserRelationSubject(tenant, blockerid)
		if blockerSubject == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}
		blockedKey := blockerSubject.BlockedKey()
		if blockedKey == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}

		idx := index.NewSecondaryIndex(*blockedKey, apipub.BuildBlocklistRedisKey(tenant, blockerid, blockedid))
		if idx == nil {
			return errs.New(http.StatusNotFound, errs.EBlocklistNotFound)
		}

		err = rc.setSecondaryIndex(ctx, idx)
		if err != nil {
			idxKey := idx.IdxKey()
			valKey := idx.ValKey()
			log.Error().Err(err).Str("idxKey", idxKey).Str("valKey", valKey).Msg("Failed to create secondary index")
		}

		// set blocked by blocker index
		blockedBySubject := index.NewUserRelationSubject(tenant, blockedid)
		if blockedBySubject == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}
		blockedByKey := blockedBySubject.BlockedByKey()
		if blockedByKey == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}

		idx = index.NewSecondaryIndex(*blockedByKey, apipub.BuildUserRedisKey(tenant, blockerid))
		if idx == nil {
			return errs.New(http.StatusNotFound, errs.EBlocklistNotFound)
		}

		err = rc.setSecondaryIndex(ctx, idx)
		if err != nil {
			idxKey := idx.IdxKey()
			valKey := idx.ValKey()
			log.Error().Err(err).Str("idxKey", idxKey).Str("valKey", valKey).Msg("Failed to create secondary index")
		}

		//if successfully blocked Remove friends if they are friends
		rc.MakeUnfriend(ctx, blockerid, blockedid)

		//remove pending Invites sent to blocked user from user
		invites, _, _ := rc.GetInvitesForUser(ctx, blockedid, block.Productid, aws.Int64(0), aws.String(""))
		if invites != nil {
			for _, invite := range *invites {
				//Iterate through invites for user.  If approver id is blocked user, remove the pending invite.
				if invite != nil &&
					invite.Approverid == blockerid {
					var group *apipub.GroupResponse
					group, err2 := rc.GetGroup(ctx, *invite.Productid, invite.Groupid)
					if err2 != nil || group == nil {
						continue
					}
					rc.RemoveMembershipRequestFromGroup(ctx, group, *invite)
				}
			}
		}

		//remove requested join requests from blocked user
		requests, _, _ := rc.GetJoinRequestsByApproverId(ctx, blockedid, block.Productid, aws.Int64(0), aws.String(""))
		if requests != nil {
			for _, request := range *requests {
				//Iterate through join requests for user.  If member id is blocked user, remove the pending invite.
				if request != nil && request.Productid != nil &&
					request.Approverid == blockerid {
					var group *apipub.GroupResponse
					group, err2 := rc.GetGroup(ctx, *request.Productid, request.Groupid)
					if err2 != nil || group == nil {
						continue
					}
					rc.RemoveMembershipRequestFromGroup(ctx, group, *request)
				}
			}
		}
	}
	return nil
}

// GetUserBlocklist get blocklist from redis using index for pagination
func (rc *RedisCache) GetUserBlocklist(ctx context.Context, userid string, limit *int64, next *string) (*[]*apipub.BlocklistResponse, string, error) {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	lockKey := "blocklist:" + userid + ":lock"
	lockTTL := time.Duration(rc.cfg.RedisLockDuration) * time.Second
	lock := rc.GetSyncLock(ctx, lockKey, lockTTL)
	if lock != nil {
		defer lock.Release(ctx)
	}

	if lock == nil {
		log.Warn().Str("userid", userid).Msg("block list sync already started. Read from DynamoDb instead.")
		return nil, "", nil
	}

	if limit == nil {
		limit = aws.Int64(int64(rc.cfg.MaxBlocks))
	}

	blocklistSub := index.NewUserRelationSubject(tenant, userid)
	if blocklistSub == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	blockedKey := blocklistSub.BlockedKey()
	if blockedKey == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	idx := index.NewSecondaryIndex(*blockedKey, "")
	if idx == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.EBlocklistNotFound)
	}

	if next != nil {
		next = aws.String(apipub.BuildBlocklistRedisKey(tenant, userid, *next))
	}

	results, it, err3 := getObjsFromSecondaryIndex[apipub.BlocklistResponse](ctx, rc, idx, limit, next, false)
	if err3 != nil {
		log.Err(err3).Str("user id: ", userid).Msg("failed to get blocklist from cache")
	}

	parts := strings.Split(it, ":")
	if len(parts) == 3 {
		it = parts[2]
	}

	return results, it, err3
}

// RemoveFromUserBlockList remove blocklist from redis.  Remove both index and cached object.
func (rc *RedisCache) RemoveFromUserBlockList(ctx context.Context, blocklist *[]*apipub.BlocklistResponse) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	if blocklist == nil {
		return nil
	}
	for _, block := range *blocklist {

		// Delete obj
		err := rc.DeleteCachedObj(ctx, block.RedisKey(tenant))
		if err != nil {
			log.Error().Err(err).Msgf("failed to delete blocklist from cache")
			continue
		}

		blockerUserid := block.Userid
		blockedUserid := block.Blockedid

		// remove blocker blocked blockee index
		blockerSubject := index.NewUserRelationSubject(tenant, blockerUserid)
		if blockerSubject == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}
		blockedKey := blockerSubject.BlockedKey()
		if blockedKey == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}

		idx := index.NewSecondaryIndex(*blockedKey, apipub.BuildBlocklistRedisKey(tenant, blockerUserid, blockedUserid))
		if idx == nil {
			return errs.New(http.StatusNotFound, errs.EBlocklistNotFound)
		}

		err = rc.delSecondaryIndex(ctx, idx)
		if err != nil {
			idxKey := idx.IdxKey()
			valKey := idx.ValKey()
			log.Error().Err(err).Str("idxKey", idxKey).Str("valKey", valKey).Msg("Failed to init secondary index")
		}

		// remove blockee blocked by blocker index
		blockedBySubject := index.NewUserRelationSubject(tenant, blockedUserid)
		if blockedBySubject == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}
		blockedByKey := blockedBySubject.BlockedByKey()
		if blockedByKey == nil {
			return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}

		idx = index.NewSecondaryIndex(*blockedByKey, apipub.BuildUserRedisKey(tenant, blockerUserid))
		if idx == nil {
			return errs.New(http.StatusNotFound, errs.EBlocklistNotFound)
		}

		err = rc.delSecondaryIndex(ctx, idx)
		if err != nil {
			idxKey := idx.IdxKey()
			valKey := idx.ValKey()
			log.Error().Err(err).Str("idxKey", idxKey).Str("valKey", valKey).Msg("Failed to create secondary index")
		}

		//remove any existing pending friendships
		f1 := apipub.FriendResponse{
			Userid:   blockedUserid,
			Friendid: blockerUserid,
			Status:   apipub.Pending,
		}
		f2 := apipub.FriendResponse{
			Userid:   blockerUserid,
			Friendid: blockedUserid,
			Status:   apipub.Pending,
		}
		rc.DeleteFriend(ctx, &f1)
		rc.DeleteFriend(ctx, &f2)

	}
	return nil
}

// UserBlocklistExistsInCache check if blocklist exists in cache
func (rc *RedisCache) UserBlocklistExistsInCache(ctx context.Context, userid string) bool {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	blocklistSub := index.NewUserRelationSubject(tenant, userid)
	if blocklistSub == nil {
		return false
	}
	blockedKey := blocklistSub.BlockedKey()
	if blockedKey == nil {
		return false
	}

	return rc.exists(ctx, *blockedKey).Val() == 1
}

// ClearUserBlocklist clear blocklist from cache
func (rc *RedisCache) ClearUserBlocklist(ctx context.Context, userid string) error {

	entries, _, err := rc.GetUserBlocklist(ctx, userid, aws.Int64(int64(rc.cfg.MaxBlocks)), nil)
	if err != nil && !errs.IsEqual(err, errs.ERedisObjectMissing) {
		return err
	}
	if entries == nil || len(*entries) == 0 {
		return nil
	}

	err = rc.RemoveFromUserBlockList(ctx, entries)
	if err != nil {
		return err
	}

	return nil
}

func (rc *RedisCache) DoesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	blockerSubject := index.NewUserRelationSubject(tenant, blockerid)
	if blockerSubject == nil {
		return false, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	blockerKey := blockerSubject.BlockedKey()

	blockeeKey := apipub.BuildBlocklistRedisKey(tenant, blockerid, blockeeid)

	exists := rc.existsInSortedSet(ctx, *blockerKey, blockeeKey)
	return exists, nil
}
