<script lang="ts">
  import { onDestroy, setContext } from 'svelte';
  import { writable } from 'svelte/store';
  import { CONTEXT_KEY_TABS } from '../../constant';

  export let className = '';

  const tabs = [];
  const panels = [];
  const selectedTab = writable(null);
  const selectedPanel = writable(null);

  setContext(CONTEXT_KEY_TABS, {
    registerTab: (tab: unknown) => {
      tabs.push(tab);
      selectedTab.update(current => current || tab);

      onDestroy(() => {
        const i = tabs.indexOf(tab);
        tabs.splice(i, 1);
        selectedTab.update(current =>
          current === tab ? tabs[i] || tabs[tabs.length - 1] : current
        );
      });
    },

    registerPanel: (panel: unknown) => {
      panels.push(panel);
      selectedPanel.update(current => current || panel);

      onDestroy(() => {
        const i = panels.indexOf(panel);
        panels.splice(i, 1);
        selectedPanel.update(current =>
          current === panel ? panels[i] || panels[panels.length - 1] : current
        );
      });
    },

    selectTab: (tab: unknown) => {
      const i = tabs.indexOf(tab);
      selectedTab.set(tab);
      selectedPanel.set(panels[i]);
    },

    selectedTab,
    selectedPanel,
  });
</script>

<style>
  .tabs {
    display: flex;
    width: 100%;
    height: 100%;
    padding: 0;
  }
</style>

<div class="tabs {className}">
  <slot />
</div>
