serverCreateGroupResponseBody:
  201:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/groupWithErrorsResponse'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverDeleteGroupResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          type: object
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverGetGroupResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/groupResponse'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverUpdateGroupResponseBody:
  200:
    $ref: '../responses/responses.yaml#/responses/200empty'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverUpdateGroupMemberResponseBody:
  200:
    $ref: '../responses/responses.yaml#/responses/200empty'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverKickGroupMemberResponseBody:
  200:
    $ref: '../responses/responses.yaml#/responses/200empty'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverSendControlMessageResponseBody:
  200:
    $ref: '../responses/responses.yaml#/responses/200empty'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverSendInviteResponseBody:
  200:
    $ref: '../responses/responses.yaml#/responses/200empty'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverJoinRequestResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/membershipRequest'
        examples:
          'Membership Return':
            value:
              {
                'expiresIn': 3600,
                'memberid': 'b287e655461f4b3085c8f244e394ff7e',
                'approverid': 'b287e655461f4b3085c8f244e394ff7e',
                'groupid': '01EYRSXN4DCFF1AV128Y5A211J',
                'productid': 'f9ba143d26e86d5f9479dc9267177aae',
                'onlineServiceType': 3,
                'status': 'requested',
                'canCrossPlay': false,
                'firstPartyId': 'string',
                'isFirstPartyInvite': false,
                'version': 0,
                'displayName': 'string',
              }
          'Empty Return invite/request already exists':
            value: { }
  201:
    description: Created
    content:
      application/json:
        schema:
          $ref: '../schemas/schemas.yaml#/membershipRequest'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverAddGroupMembersResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/groupWithErrorsResponse'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverGetDiscoveryResponseBody:
  200:
    description: Discovery response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/discoveryListResponse'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
serverUpsertDiscoveryResponseBody:
  200:
    $ref: '../responses/responses.yaml#/responses/200empty'
  201:
    description: Created discovery response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/discoveryListResponse'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverDeleteDiscoveryResponseBody:
  200:
    description: OK
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/emptyObject'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  404:
    $ref: '../responses/responses.yaml#/responses/404'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverVersionResponseBody:
  200:
    description: Version
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/versionResponse'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
serverHealthResponseBody:
  200:
    description: Server is healthy
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/healthResponse'
  404:
    description: Identity parameter not found
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/healthResponse'
  429:
    $ref: '../responses/responses.yaml#/responses/429'
  500:
    description: Server is unhealthy
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/healthResponse'
serverGetTokenResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          $ref: '../schemas/responseSchemas.yaml#/serverLoginResponse'
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  500:
    $ref: '../responses/responses.yaml#/responses/500'
serverLogoutResponseBody:
  200:
    description: Success-Response
    content:
      application/json:
        schema:
          type: object
  400:
    $ref: '../responses/responses.yaml#/responses/400'
  401:
    $ref: '../responses/responses.yaml#/responses/401'
  403:
    $ref: '../responses/responses.yaml#/responses/403'
  500:
    $ref: '../responses/responses.yaml#/responses/500'