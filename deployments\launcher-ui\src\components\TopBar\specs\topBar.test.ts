import { render } from '@testing-library/svelte';
import UserAccountMock from '../../UserAccount/__mock__/UserAccount.svelte';
import TopBar from '../TopBar.svelte';

jest.mock('../../UserAccount', () => ({
  UserAccount: UserAccountMock,
}));

describe('TopBar', () => {
  it('should render title', () => {
    const title = 'title';
    const { getByText } = render(TopBar, {
      props: {
        title,
      },
    });
    expect(getByText(title)).not.toBeNull();
  });
});
