package middleware

//
//import (
//	"bytes"
//	"context"
//	"fmt"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
//	"io"
//	"net/http"
//	"time"
//
//	"github.com/aws/aws-sdk-go-v2/aws"
//	"github.com/aws/aws-sdk-go-v2/service/kinesis"
//	chimiddleware "github.com/go-chi/chi/v5/middleware"
//	zlog "github.com/rs/zerolog/log"
//	"github.com/segmentio/encoding/json"
//
//	"github.com/take-two-t2gp/t2gp-social-service/constants"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
//)
//
//type RequestEntry struct {
//	Method string      `json:"method"`
//	IP     string      `json:"ip"`
//	URL    string      `json:"url"`
//	Proto  string      `json:"proto"`
//	Header http.Header `json:"headers"`
//	Body   interface{} `json:"body"`
//}
//
//func (r *RequestEntry) FromReq(req *http.Request, bodyBytes *[]byte) *RequestEntry {
//	r.Method = req.Method
//	r.IP = req.RemoteAddr
//	r.URL = req.URL.String()
//	r.Proto = req.Proto
//	r.Header = req.Header
//	body := map[string]interface{}{}
//	if bodyBytes != nil && len(*bodyBytes) > 0 {
//		err := json.Unmarshal(*bodyBytes, &body)
//		if err != nil {
//			zlog.Error().Msgf("failed to unmarshall request")
//		}
//		r.Body = body
//	}
//	return r
//}
//
//type ResponseEntry struct {
//	StatusCode int         `json:"statusCode"`
//	Header     http.Header `json:"headers"`
//	Body       interface{} `json:"body"`
//}
//
//func (r *ResponseEntry) FromResp(resp chimiddleware.WrapResponseWriter, buffer *bytes.Buffer) *ResponseEntry {
//	r.StatusCode = resp.Status()
//	r.Header = resp.Header()
//	body := map[string]interface{}{}
//	if buffer.Len() > 0 {
//		err := json.Unmarshal(buffer.Bytes(), &body)
//		if err != nil {
//			zlog.Error().Msgf("failed to unmarshall response")
//		}
//		r.Body = body
//	}
//	return r
//}
//
//// KinesisInterface used to allow overriding while testing
//type KinesisInterface interface {
//	PutRecord(ctx context.Context, params *kinesis.PutRecordInput, optFns ...func(*kinesis.Options)) (*kinesis.PutRecordOutput, error)
//}
//
//func CreateKinesis(ctx context.Context, cfg *config.Config) KinesisInterface {
//	log := logger.FromContext(ctx)
//	awsconfig := aws.Config{
//		Region:      cfg.KinesisRegion,
//		Credentials: utils.GetV2CredentialProvider(ctx),
//	}
//	var client *kinesis.Client
//	for attempt := 1; attempt <= cfg.AwsRequestMaxRetryAttempt; attempt++ {
//		client = kinesis.NewFromConfig(awsconfig, func(o *kinesis.Options) {
//			o.Retryer = utils.GetDefaultRetryStandard(cfg.AwsRequestMaxRetryAttempt)
//		})
//
//		if client != nil {
//
//			streamArn := aws.String(cfg.KinesisStreamArn)
//
//			// check we have access to the arn
//			_, err := client.DescribeStream(ctx, &kinesis.DescribeStreamInput{StreamARN: streamArn})
//			if err != nil {
//				log.Error().Err(err).Msgf("Missing kinesis arn: %s", cfg.KinesisStreamArn)
//				return nil
//			}
//
//			return client
//		}
//
//		zlog.Error().Msg("failed to create a Kinesis client")
//		time.Sleep(time.Second * time.Duration(attempt))
//	}
//
//	return nil
//}
//
//// RequestRecorder middleware to send request and response to kinesis
//// This must be after response writer length check
//func RequestRecorder(ctx context.Context, cfg *config.Config, k KinesisInterface) func(next http.Handler) http.Handler {
//	log := logger.FromContext(ctx)
//	enableRecorder := true
//	if k == nil {
//		enableRecorder = false
//	}
//	if cfg != nil && !cfg.RequestRecorderEnabled {
//		log.Error().Msgf("Starting request recorder without feature flag enabled")
//		enableRecorder = false
//	}
//	if cfg.RequestRecorderStream == "" {
//		log.Error().Msgf("Missing kinesis stream name for request recorder")
//		enableRecorder = false
//	}
//	if !enableRecorder {
//		// pass through handler if there are errors
//		return func(next http.Handler) http.Handler {
//			return next
//		}
//	}
//
//	return func(next http.Handler) http.Handler {
//		fn := func(w http.ResponseWriter, r *http.Request) {
//			// get request body
//			reqBody, err := io.ReadAll(r.Body)
//			defer r.Body.Close()
//			if err != nil {
//				log.Error().Err(err).Msgf("failed to read request body")
//			}
//			reqReader := io.NopCloser(bytes.NewBuffer(reqBody))
//			r.Body = reqReader
//
//			// patch writer to capture response body
//			ww := chimiddleware.NewWrapResponseWriter(w, r.ProtoMajor)
//			respBody := &bytes.Buffer{}
//			ww.Tee(respBody)
//
//			// forward to next handler
//			next.ServeHTTP(ww, r)
//
//			if ww.Header().Get("content-type") == "application/json" {
//				req := (&RequestEntry{}).FromReq(r, &reqBody)
//				resp := (&ResponseEntry{}).FromResp(ww, respBody)
//
//				requestId := chimiddleware.GetReqID(r.Context())
//				JWT := r.Context().Value(constants.BearerAuthJWT)
//				data := fmt.Sprintf("{\"environment\":\"%s\",\"ts\":\"%s\",\"request\":%s,\"response\":%s, \"jwt\":%s}",
//					utils.GetEnvironment(), utils.NowISOString(), utils.EncodeJson(req), utils.EncodeJson(resp), utils.EncodeJson(JWT))
//				putResp, err := k.PutRecord(ctx, &kinesis.PutRecordInput{
//					Data:         []byte(data),
//					StreamName:   aws.String(cfg.RequestRecorderStream),
//					PartitionKey: aws.String(requestId),
//				})
//				if err != nil {
//					log.Error().Err(err).Msgf("failed kinsesis put command %v", putResp)
//				}
//
//				// uncomment to show kinesis responses
//				// log.Debug().Msgf("kinesis put %s %v", *streamName, putResp)
//			}
//		}
//		return http.HandlerFunc(fn)
//	}
//}
