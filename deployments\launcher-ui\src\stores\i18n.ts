import { derived, Readable, writable, Writable } from 'svelte/store';
import type { I18nService } from '../services';

export type LocaleStore = Writable<string>;
export type TranslateStore<T> = Readable<T>;

// create a local.
// 1. Create a writable store
// 2. Create a new set function that changes the i18n locale.
// 3. Create a new update function that changes the i18n locale.
// 4. Return modified writable.
export const createLocal = (i18nService: I18nService) => {
  const { subscribe, set, update } = writable<string>(
    i18nService.i18n.language
  );

  const setLocale = (newLocale: string) => {
    i18nService.changeLanguage(newLocale);
    set(newLocale);
  };

  const updateLocale = (updater: (value: string) => string) => {
    update(currentValue => {
      const nextLocale = updater(currentValue);
      i18nService.changeLanguage(nextLocale);
      return nextLocale;
    });
  };

  return {
    subscribe,
    update: updateLocale,
    set: setLocale,
  };
};

// Create a translate store.
// It is derived from the "locale" writable.
// This means it will be updated every time the locale changes.
export const createTranslate = (
  locale: LocaleStore,
  i18nService: I18nService
) => {
  return derived([locale], () => {
    return (key: string, replacements?: Record<string, unknown>) =>
      i18nService.t(key, replacements);
  });
};
