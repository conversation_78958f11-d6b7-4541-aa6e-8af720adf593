// Package notification implements nofication untility functions
package notification

import (
	"context"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sns"
	"github.com/aws/smithy-go/middleware"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

type SNS struct {
	SNSClient          *sns.Client
}

func NewSNS(ctx context.Context, cfg *config.Config) SNS {
	var snsInterface SNS
	snsConfig := aws.Config{
		Region:      cfg.S3BucketRegion,
		Credentials: utils.GetV2CredentialProvider(ctx),
	}

	snsConfig.APIOptions = append(snsConfig.APIOptions, func(s *middleware.Stack) error {
		return s.Serialize.Add(&telemetry.OnBuild{}, middleware.After)
	})
	snsConfig.APIOptions = append(snsConfig.APIOptions, func(s *middleware.Stack) error {
		return s.Finalize.Add(&telemetry.OnComplete{}, middleware.After)
	})

	for attempt := 1; attempt <= cfg.AwsRequestMaxRetryAttempt; attempt++ {
		snsInterface.SNSClient = sns.NewFromConfig(snsConfig, func(o *sns.Options) {
			o.Retryer = utils.GetDefaultRetryStandard(cfg.AwsRequestMaxRetryAttempt)
			o.BaseEndpoint = aws.String(fmt.Sprintf("https://sns.%s.amazonaws.com", cfg.S3BucketRegion))
		})

		if snsInterface.SNSClient != nil {
			return snsInterface
		}

		log.Error().Msg("failed to create a DynamoDB client")
		time.Sleep(time.Second * time.Duration(attempt))
	}

	return snsInterface
}
