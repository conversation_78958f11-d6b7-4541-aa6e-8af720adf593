import { StatusCodes } from 'http-status-codes';
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { MqttWrapper } from '../../../lib/mqttWrapper';

let tokenHost: string;
let tokenInvited: string;
let roomId: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  tokenInvited = await socialApi.loginIn(
    config.invitedUsername,
    config.invitedPassword
  );
  roomId = await (await socialApi.createRoom(tokenHost, 2)).body.groupid;
});
afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
});
describe('', () => {
  /**
   * checking get chat room message
   * - create a room
   * - one user join the room
   * - Host send a message in the room
   * - Check the mqtt if host and the user get the message
   * - Call restful API to get the room message
   */

  it('get chat room message', async () => {
    const messageHost = 'hi there' + ' ' + Math.round(Math.random() * 1000);
    await socialApi.createRoom(tokenHost, 3);

    await socialApi.joinRoom(tokenInvited, roomId);
    await socialApi.sendRoomMessage(tokenHost, roomId, messageHost);

    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .get(`/chat/rooms/${roomId}/messages`)
      .set('Authorization', 'Bearer ' + tokenHost)
      .send({ maxMembers: 7 });

    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items[0].message).toEqual(messageHost);
  });
});
