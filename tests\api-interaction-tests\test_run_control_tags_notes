Notes on the test run control tags (see README.md)


The following is a list of regex and test cases for the various run scenarios.


[Full test (0 run tag)]
^[^\[]+$|(?!\[.*\bbroken\b.*\]|\[.*\blowprio\b.*\])\[.*

npm run test:exportJson -- --testPathIgnorePatterns='local/|integration/chat/' --testPathPattern='integration/group/test1' -t='^[^\[]+$|(?!\[.*\bbroken\b.*\]|\[.*\blowprio\b.*\])\[.*'

[test cases]
(expect to not match) xxx [broken]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [broken lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to match)     xxx Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to match)     xxx []Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to match)     xxx [xxx yyy zzz]Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to not match) xxx [xxx yyy zzz broken]Create group, blah blah, broken lowprio happy , xxx yyy zzz



[1 run tag]
(?!\[.*\bbroken\b.*\]|\[.*\blowprio\b.*\])(?=\[.*\bhappy\b.*\])\[.*

npm run test:exportJson -- --testPathIgnorePatterns='local/|integration/chat/' --testPathPattern='integration/group/test1' -t='(?!\[.*\bbroken\b.*\]|\[.*\blowprio\b.*\])(?=\[.*\bhappy\b.*\])\[.*'

[test cases]
(expect to not match) xxx [broken]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [broken lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx []Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to not match) xxx [xxx yyy zzz]Create group, blah blah, broken lowprio happy , xxx yyy zzz

(expect to match)     xxx [happy]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy broken]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy broken lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx []Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to not match) xxx [xxx yyy zzz]Create group, blah blah, broken lowprio happy , xxx yyy zzz



[2 run tags]
(?!\[.*\bbroken\b.*\]|\[.*\blowprio\b.*\])(?=\[.*\bhappy\b.*\])(?=\[.*\blabel1\b.*\])\[.*

npm run test:exportJson -- --testPathIgnorePatterns='local/|integration/chat/' --testPathPattern='integration/group/test1' -t='(?!\[.*\bbroken\b.*\]|\[.*\blowprio\b.*\])(?=\[.*\bhappy\b.*\])(?=\[.*\blabel1\b.*\])\[.*'

[test cases]
(expect to not match) xxx [broken]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [broken lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx []Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to not match) xxx [xxx yyy zzz]Create group, blah blah, broken lowprio happy , xxx yyy zzz

(expect to not match) xxx [happy]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy broken]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy broken lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx []Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to not match) xxx [xxx yyy zzz]Create group, blah blah, broken lowprio happy , xxx yyy zzz

(expect to not match) xxx [happy]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to match)     xxx [happy label1]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy label1 broken]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy label1 lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx [happy label1 broken lowprio]Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx Create group, blah blah, broken lowprio happy, xxx yyy zzz
(expect to not match) xxx []Create group, blah blah, broken lowprio happy , xxx yyy zzz
(expect to not match) xxx [xxx yyy zzz]Create group, blah blah, broken lowprio happy , xxx yyy zzz

