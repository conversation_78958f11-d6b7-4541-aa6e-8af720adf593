package cache

import (
	"context"
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

// SetFriend set friend to redis
func (rc *RedisCache) SetFriend(ctx context.Context, friend *apipub.FriendResponse, ttl time.Duration) error {
	//set friend object then set appropriate idx
	log := logger.FromContext(ctx)

	_, err := rc.pipelined(ctx, func(pipe redis.Pipeliner) error {
		return rc.SetFriendPipe(ctx, friend, ttl, &pipe)
	})
	if err != nil {
		log.Error().Err(err).Msg("failed to set friend")
		return err
	}
	return nil
}

func (rc *RedisCache) SetFriendPipe(ctx context.Context, friend *apipub.FriendResponse, ttl time.Duration, pipe *redis.Pipeliner) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	jsonFriend, err := json.Marshal(friend)
	if err != nil {
		log.Error().Err(err).Str("friendid", friend.Friendid).Msg("failed to marshal friend.")
		return err
	}

	if pipe == nil {
		newPipe := rc.pipeline()
		pipe = &newPipe
	}

	err = (*pipe).Do(ctx, "JSON.SET", friend.RedisKey(tenant), ".", jsonFriend).Err()
	if err != nil {
		log.Error().Err(err).Str("friendid", friend.Friendid).Msg("failed to JSON.set friend.")
		return err
	}
	err = (*pipe).Expire(ctx, friend.RedisKey(tenant), ttl).Err()
	if err != nil {
		log.Error().Err(err).Str("friendid", friend.Friendid).Msg("failed to set expire for friend.")
		return err
	}

	// set friendlist idx and friend by status idx
	userSubject := index.NewUserRelationSubject(tenant, friend.Userid)
	if userSubject == nil {
		log.Warn().Str("friendid", friend.Userid).Str("event", "failed to create secondary index for usersub").Msg("failed to create secondary index for usersub")
	} else {
		friendslistKey := userSubject.FriendsListKey()

		if friendslistKey == nil {
			log.Warn().Str("friendid", friend.Userid).Str("event", "failed to create secondary index for friendslsit").Msg("failed to create secondary index for friendslist")
		} else {
			friendlistIdx := index.NewSecondaryIndex(*friendslistKey, apipub.BuildFriendRedisKey(tenant, friend.Userid, friend.Friendid))
			rc.setSecondaryIndexPipe(ctx, friendlistIdx, pipe)
		}
	}
	friendStatusKey := userSubject.FriendsStatusKey(string(friend.Status))
	if friendStatusKey == nil {
		log.Error().Str("userid", friend.Userid).Str("friendid", friend.Friendid).Msg("failed init get key")
	} else {
		friendStatusIdx := index.NewSecondaryIndex(*friendStatusKey, apipub.BuildFriendRedisKey(tenant, friend.Userid, friend.Friendid))
		return rc.setSecondaryIndexPipe(ctx, friendStatusIdx, pipe)
	}
	return nil
}

// SetFriends set friend to redis
func (rc *RedisCache) SetFriends(ctx context.Context, friends *[]*apipub.FriendResponse, ttl time.Duration) {
	log := logger.FromContext(ctx)
	// tenant := identity.GetTenantFromCtx(ctx, rdb.id)
	if friends == nil || len(*friends) == 0 {
		return
	}

	pipeline := rc.pipeline()
	for _, friend := range *friends {
		if friend.Userid == "" || friend.Friendid == "" {
			continue
		}
		rc.SetFriendPipe(ctx, friend, ttl, &pipeline)
	}
	res, err2 := pipeline.Exec(ctx)
	if err2 != nil {
		log.Error().Err(err2).Msgf("error executing pipeline %v", res)
	}
}

// GetFriend get friend from redis
func (rc *RedisCache) GetFriend(ctx context.Context, userid string, friendid string) (*apipub.FriendResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	friend, err := getCachedObject[apipub.FriendResponse](ctx, rc, apipub.BuildFriendRedisKey(tenant, userid, friendid))
	if err != nil {
		return nil, err
	}
	if friend != nil {
		return friend, nil
	}
	return nil, errs.New(http.StatusNotFound, errs.EFriendsNotFound)
}

// DeleteFriend delete friend from redis
func (rc *RedisCache) DeleteFriend(ctx context.Context, friend *apipub.FriendResponse) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := friend.RedisKey(tenant)
	//delete object.  absorb error since deleting the index is the important part.  obj will expire eventually.
	rc.DeleteCachedObj(ctx, key)

	//remove from friendlist
	friendByStatus := index.NewUserRelationSubject(tenant, friend.Userid)
	if friendByStatus == nil {
		log.Warn().Str("friendid", friend.Userid).Str("event", "failed to create secondary index for friendBy").Msg("failed to create secondary index for friendBy")
	} else {
		friendslistKey := friendByStatus.FriendsListKey()
		if friendslistKey == nil {
			log.Error().Str("userid", friend.Userid).Str("friendid", friend.Friendid).Msg("failed init get key")
		} else {
			friendlistIdx := index.NewSecondaryIndex(*friendslistKey, apipub.BuildFriendRedisKey(tenant, friend.Userid, friend.Friendid))
			rc.delSecondaryIndex(ctx, friendlistIdx)
		}
	}
	//remove from status idx
	byStatusKey := friendByStatus.FriendsStatusKey(string(friend.Status))
	if byStatusKey == nil {
		log.Error().Str("userid", friend.Userid).Str("friendid", friend.Friendid).Msg("failed init get key")
	} else {
		byStatusIdx := index.NewSecondaryIndex(*byStatusKey, apipub.BuildFriendRedisKey(tenant, friend.Userid, friend.Friendid))
		return rc.delSecondaryIndex(ctx, byStatusIdx)
	}
	return nil
}

// MakeFriend create friend relationships between 2 userids.  Pending if not exist.  Friend if pending.
func (rc *RedisCache) MakeFriend(ctx context.Context, userid, friendid, message string, isUserBlocked bool, userOST apipub.OnlineServiceType, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	if userid == "" || friendid == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidFriendID)
	}

	userFriend := apipub.FriendResponse{
		Userid:   userid,
		Friendid: friendid,
		Status:   apipub.Friend,
		Message:  &message,
		Invitee:  friendid,
	}

	revFriend := apipub.FriendResponse{
		Userid:   friendid,
		Friendid: userid,
		Status:   apipub.Friend,
		Message:  &message,
		Invitee:  friendid,
	}

	userSubject := index.NewUserRelationSubject(tenant, userFriend.Userid)
	if userSubject == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	revUserSubject := index.NewUserRelationSubject(tenant, revFriend.Userid)
	if revUserSubject == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	pipeline := rc.pipeline()
	//if friend doesn't exist, make pending relationship(s)
	if !rc.CachedObjExists(ctx, userFriend.RedisKey(tenant)) {

		var err error
		//pending friend

		userFriend.Status = apipub.Pending
		userFriend.InviterOST = &userOST
		err = rc.SetFriendPipe(ctx, &userFriend, ttl, &pipeline)
		if err != nil {
			return err
		}

		//if inviter is blocked only make 1 way pending friend
		if !isUserBlocked {
			revFriend.Status = apipub.Pending
			userFriend.InviterOST = &userOST
			err = rc.SetFriendPipe(ctx, &revFriend, ttl, &pipeline)
			if err != nil {
				return err
			}
		}

		//Else make pending friends, friends
	} else {
		// pull pending friends from cache if exist
		rcUserFriend, _ := rc.GetFriend(ctx, userFriend.Userid, userFriend.Friendid)
		if rcUserFriend == nil {
			rcUserFriend = &userFriend
		}
		rcRevFriend, _ := rc.GetFriend(ctx, userFriend.Friendid, userFriend.Userid)
		if rcRevFriend == nil {
			rcRevFriend = &revFriend
		}

		if rcUserFriend != nil && rcUserFriend.Status == apipub.Pending && rcUserFriend.Userid == rcUserFriend.Invitee {
			//delete entry from pending friends
			pendingKey := userSubject.FriendsStatusKey(string(apipub.Pending))
			if pendingKey == nil {
				log.Error().Str("userid", userFriend.Userid).Str("friendid", userFriend.Friendid).Msg("failed init get key")
			} else {
				pendingIdx := index.NewSecondaryIndex(*pendingKey, apipub.BuildFriendRedisKey(tenant, userFriend.Userid, userFriend.Friendid))
				rc.delSecondaryIndex(ctx, pendingIdx)
			}
			//and for the reverse pending
			revPendingKey := revUserSubject.FriendsStatusKey(string(apipub.Pending))
			if revPendingKey == nil {
				log.Error().Str("userid", revFriend.Userid).Str("friendid", revFriend.Friendid).Msg("failed init get key")
			} else {
				revPendingIdx := index.NewSecondaryIndex(*revPendingKey, apipub.BuildFriendRedisKey(tenant, revFriend.Userid, revFriend.Friendid))
				rc.delSecondaryIndex(ctx, revPendingIdx)
			}
			//set friend with 'friend' status
			rcUserFriend.Status = apipub.Friend
			rc.SetFriendPipe(ctx, rcUserFriend, ttl, &pipeline)
			// and reverse friend
			rcRevFriend.Status = apipub.Friend
			rc.SetFriendPipe(ctx, rcRevFriend, ttl, &pipeline)
		}
	}

	res, err2 := pipeline.Exec(ctx)
	if err2 != nil {
		log.Error().Err(err2).Msgf("error executing pipeline %v", res)
	}
	return nil
}

// GetFriends get user's friends from redis
func (rc *RedisCache) GetFriends(ctx context.Context, userid string, status *apipub.FriendStatus, limit *int64, next *string) (*[]*apipub.FriendResponse, string, error) {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	//friend by status friend Idx
	var key *string
	userSubject := index.NewUserRelationSubject(tenant, userid)
	if status == nil {
		key = userSubject.FriendsListKey()
	} else {
		key = userSubject.FriendsStatusKey(string(*status))
	}
	if key == nil {
		log.Error().Str("userid", userid).Str("friendid", userid).Msg("failed init get key")
		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisInvalidKey)
	}
	friendByStatusIdx := index.NewSecondaryIndex(*key, "")

	var nextKey string
	if next != nil {
		nextKey = apipub.BuildFriendRedisKey(tenant, userid, *next)
	}
	returnedFriends, nextStr, err2 := getObjsFromSecondaryIndex[apipub.FriendResponse](ctx, rc, friendByStatusIdx, limit, &nextKey, false)
	if nextStr != "" {
		// Key format for friend in Redis is "friend:{userId}:{friendId}", therefore, it's safe to assume that a non-empty key
		// can be splitted by ':' and the third element is friend id.
		parts := strings.Split(nextStr, ":")
		nextStr = parts[len(parts)-1]
	}

	return returnedFriends, nextStr, err2
}

func (rc *RedisCache) CountFriendlistMembers(ctx context.Context, userid string) (int64, error) {
	//get all user group index keys for user
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	//friend by status friend idx
	userSubject := index.NewUserRelationSubject(tenant, userid)
	if userSubject == nil {
		return 0, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	friendlistKey := userSubject.FriendsListKey()
	if friendlistKey == nil {
		log.Error().Str("userid", userid).Str("friendid", userid).Msg("failed init get key")
		return 0, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	return rc.zCard(ctx, *friendlistKey).Val(), nil
}

// MakeUnfriend break a friendship
func (rc *RedisCache) MakeUnfriend(ctx context.Context, userid1 string, userid2 string) error {
	if userid1 == "" || userid2 == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidFriendID)
	}

	friend, err := rc.GetFriend(ctx, userid1, userid2)
	if err != nil {
		return err
	}

	if friend != nil {
		err1 := rc.DeleteFriend(ctx, friend)
		friend.Userid = userid2
		friend.Friendid = userid1
		err2 := rc.DeleteFriend(ctx, friend)
		if err1 != nil {
			return err1
		}
		if err2 != nil {
			return err2
		}
	}
	return nil
}

// FriendlistExistsInCache check if friend list exists in cache
func (rc *RedisCache) FriendlistExistsInCache(ctx context.Context, userid string) bool {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	userSubject := index.NewUserRelationSubject(tenant, userid)
	if userSubject == nil {
		return false
	}
	friendlistKey := userSubject.FriendsListKey()
	if friendlistKey == nil {
		log.Error().Str("userid", userid).Msg("failed init get key")
		return false
	}
	return rc.exists(ctx, *friendlistKey).Val() == 1
}

// ClearFriendlist clear friend list from cache.
//
//	Doesn't delete the actual cached objs but that's ok. They will expire and have no links unless re-added.
func (rc *RedisCache) ClearFriendlist(ctx context.Context, userid string) error {
	if rc.FriendlistExistsInCache(ctx, userid) {
		friends, _, _ := rc.GetFriends(ctx, userid, nil, nil, nil)
		if friends != nil && len(*friends) > 0 {
			for _, friend := range *friends {
				rc.DeleteFriend(ctx, friend)
			}
		}
	}
	return nil
}
