export const enum LOG_LEVEL {
  all = 'all',
  debug = 'debug',
  error = 'error',
  warn = 'warn',
}

export interface ILogOption {
  level: LOG_LEVEL;
}

export interface ILogService {
  log: (message?: any, ...params: any[]) => void;
  debug: (message?: any, ...params: any[]) => void;
  error: (message?: any, ...params: any[]) => void;
  warn: (message?: any, ...params: any[]) => void;
}

const DEFAULT_LOG_OPTION = {
  level: LOG_LEVEL.all,
};

export class LogService implements ILogService {
  private logOption: ILogOption;
  constructor(option?: ILogOption) {
    this.logOption = {
      ...DEFAULT_LOG_OPTION,
      ...(option || {}),
    };
  }

  log(message?: any, ...params: any[]) {
    if (this.logOption.level === LOG_LEVEL.all) {
      console.log(message, ...params);
    }
  }

  debug(message?: any, ...params: any[]) {
    if (
      this.logOption.level === LOG_LEVEL.all ||
      this.logOption.level === LOG_LEVEL.debug
    ) {
      console.debug(message, ...params);
    }
  }

  error(message?: any, ...params: any[]) {
    if (
      this.logOption.level === LOG_LEVEL.all ||
      this.logOption.level === LOG_LEVEL.error
    ) {
      console.error(message, ...params);
    }
  }

  warn(message?: any, ...params: any[]) {
    if (
      this.logOption.level === LOG_LEVEL.all ||
      this.logOption.level === LOG_LEVEL.warn
    ) {
      console.warn(message, ...params);
    }
  }
}
