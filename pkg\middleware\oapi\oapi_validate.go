// Package oapi implements middleware function for go-chi or net/http,
// which validates incoming HTTP requests to make sure that they conform to the given OAPI 3.0 specification.
// When OAPI validation fails on the request, we return an HTTP/400.
// Copied from https://github.com/deepmap/oapi-codegen/tree/master/pkg/chi-middleware
package oapi

import (
	"context"
	"embed"
	"errors"
	"fmt"
	"log"
	"net/http"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
	"github.com/getkin/kin-openapi/openapi3filter"
	"github.com/getkin/kin-openapi/routers"
	"github.com/getkin/kin-openapi/routers/gorillamux"
	"github.com/go-chi/chi/v5"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
)

//go:generate cp -r ../../../api .

//go:embed api/openapi-social-public.yml
//go:embed api/openapi-social-trusted-server.yml
var content embed.FS

// ErrorHandler is called when there is an error in validation
type ErrorHandler func(w http.ResponseWriter, req *http.Request, message string, statusCode int)

// MultiErrorHandler is called when oapi returns a MultiError type
type MultiErrorHandler func(openapi3.MultiError) (int, error)

const BearerAuthJWT = "bearerAuth.JWT"
const BearerAuthString = "bearerAuth.String"
const BearerAuthScopes = "bearerAuth.Scopes"

// Options to customize request validation, openapi3filter specified options will be passed through.
type Options struct {
	Options           openapi3filter.Options
	ErrorHandler      ErrorHandler
	MultiErrorHandler MultiErrorHandler
	// SilenceServersWarning allows silencing a warning for https://github.com/deepmap/oapi-codegen/issues/882 that reports when an OpenAPI spec has `spec.Servers != nil`
	SilenceServersWarning bool
} /**/

// OapiRequestValidator Creates middleware to validate request by swagger spec.
// This middleware is good for net/http either since go-chi is 100% compatible with net/http.
func OapiRequestValidator(swagger *openapi3.T, cfg *config.Config, id identity.IdentityInterface) func(next http.Handler) http.Handler {
	return OapiRequestValidatorWithOptions(swagger, nil, cfg, id)
}

// OapiRequestValidatorWithOptions Creates middleware to validate request by swagger spec.
// This middleware is good for net/http either since go-chi is 100% compatible with net/http.
func OapiRequestValidatorWithOptions(swagger *openapi3.T, options *Options, cfg *config.Config, id identity.IdentityInterface) func(next http.Handler) http.Handler {
	if swagger.Servers != nil && (options == nil || !options.SilenceServersWarning) {
		log.Println("WARN: OapiRequestValidatorWithOptions called with an OpenAPI spec that has `Servers` set. This may lead to an HTTP 400 with `no matching operation was found` when sending a valid request, as the validator performs `Host` header validation. If you're expecting `Host` header validation, you can silence this warning by setting `Options.SilenceServersWarning = true`. See https://github.com/deepmap/oapi-codegen/issues/882 for more information.")
	}

	router, err := gorillamux.NewRouter(swagger)
	if err != nil {
		panic(err)
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {

			// validate request
			if statusCode, err := validateFields(r, router, options); err != nil {
				if options != nil && options.ErrorHandler != nil {
					options.ErrorHandler(w, r, err.Error(), statusCode)
				} else {
					http.Error(w, err.Error(), statusCode)
				}
				return
			}

			// serve
			next.ServeHTTP(w, r)
		})
	}

}

// AddValidateMiddleware adds the open api validator middleware to the chi router
func AddValidateMiddleware(r *chi.Mux, cfg *config.Config, id identity.IdentityInterface) {

	yamlFile, err := content.ReadFile("api/openapi-social-public.yml")
	if err != nil {
		panic(err)
	}

	swagger, err := openapi3.NewLoader().LoadFromData(yamlFile)
	if err != nil {
		panic(err)
	}

	options := Options{
		Options: openapi3filter.Options{
			ExcludeRequestBody:    false,
			ExcludeResponseBody:   true,
			IncludeResponseStatus: false,
			MultiError:            false,
			AuthenticationFunc:    NewBearerAuthenticator(cfg, id),
		},
		ErrorHandler: handleValidationError,
	}

	// register middleware
	r.Use(OapiRequestValidatorWithOptions(swagger, &options, cfg, id))

}

// AddValidateMiddlewareTrusted adds the open api validator middleware to the chi router
func AddValidateMiddlewareTrusted(r *chi.Mux, cfg *config.Config, id identity.IdentityInterface) {

	yamlFile, err := content.ReadFile("api/openapi-social-trusted-server.yml")
	if err != nil {
		panic(err)
	}

	swagger, err := openapi3.NewLoader().LoadFromData(yamlFile)
	if err != nil {
		panic(err)
	}

	options := Options{
		Options: openapi3filter.Options{
			ExcludeRequestBody:    false,
			ExcludeResponseBody:   true,
			IncludeResponseStatus: false,
			MultiError:            false,
			AuthenticationFunc:    NewTrustServiceAuthenticator(cfg, id),
		},
		ErrorHandler: handleValidationError,
	}

	// register middleware
	r.Use(OapiRequestValidatorWithOptions(swagger, &options, cfg, id))

}

func handleValidationError(w http.ResponseWriter, req *http.Request, message string, statusCode int) {
	e := errs.NewMessage(statusCode, message, errs.EOapiValidationFailed)
	errs.Return(w, req, e)
}

// validateFields is called from the middleware above and actually does the work
// of validating a request.
func validateFields(r *http.Request, router routers.Router, options *Options) (int, error) {

	// Find route
	route, pathParams, rErr := router.FindRoute(r)
	if rErr != nil {
		return http.StatusNotFound, rErr // We failed to find a matching route for the request.
	}

	// Validate request
	requestValidationInput := &openapi3filter.RequestValidationInput{
		Request:    r,
		PathParams: pathParams,
		Route:      route,
	}

	if options != nil {
		requestValidationInput.Options = &options.Options
	}

	if err := openapi3filter.ValidateRequest(r.Context(), requestValidationInput); err != nil {
		me := openapi3.MultiError{}
		if errors.As(err, &me) {
			errFunc := getMultiErrorHandlerFromOptions(options)
			return errFunc(me)
		}

		switch e := err.(type) {
		case *openapi3filter.RequestError:
			// We've got a bad request.  handle parse errors as 400 and rest as 422.
			// Split up the verbose error by lines and return the first one
			// openapi errors seem to be multi-line with a decent message on the first
			errorLines := strings.Split(e.Error(), "\n")
			var parseError *openapi3filter.ParseError
			ok := errors.As(err, &parseError)
			if ok {
				return http.StatusBadRequest, errors.New(errorLines[0])
			}
			return http.StatusUnprocessableEntity, errors.New(errorLines[0])
		case *openapi3filter.SecurityRequirementsError:
			return http.StatusUnauthorized, err
		default:
			// This should never happen today, but if our upstream code changes,
			// we don't want to crash the server, so handle the unexpected error.
			return http.StatusInternalServerError, fmt.Errorf("error validating route: %s", err.Error())
		}
	}

	return http.StatusOK, nil
}

// attempt to get the MultiErrorHandler from the options. If it is not set,
// return a default handler
func getMultiErrorHandlerFromOptions(options *Options) MultiErrorHandler {
	if options == nil {
		return defaultMultiErrorHandler
	}

	if options.MultiErrorHandler == nil {
		return defaultMultiErrorHandler
	}

	return options.MultiErrorHandler
}

// defaultMultiErrorHandler returns a StatusBadRequest (400) and a list
// of all the errors. This method is called if there are no other
// methods defined on the options.
func defaultMultiErrorHandler(me openapi3.MultiError) (int, error) {
	return http.StatusUnprocessableEntity, me
}

func NewBearerAuthenticator(cfg *config.Config, id identity.IdentityInterface) openapi3filter.AuthenticationFunc {
	return func(ctx context.Context, input *openapi3filter.AuthenticationInput) error {
		return BearerAuthenticate(ctx, input, cfg, id)
	}
}

func NewBasicAuthenticator(cfg *config.Config, id identity.IdentityInterface) openapi3filter.AuthenticationFunc {
	return func(ctx context.Context, input *openapi3filter.AuthenticationInput) error {
		return BasicAuthenticate(ctx, input, cfg, id)
	}
}

func NewTrustServiceAuthenticator(cfg *config.Config, id identity.IdentityInterface) openapi3filter.AuthenticationFunc {
	return func(ctx context.Context, input *openapi3filter.AuthenticationInput) error {
		return TrustServiceAuthenticate(ctx, input, cfg, id)
	}
}
