# How to profile

1. Connect to vmq using `make vmq-shell` or use k8s Lens.
2. Attach to vmq" `vernemq attach`
3. In the erlang shell, start he profiler
   ```
   fprof:start().
   fprof:trace([start, {procs, all}]).
   ```
4. Execute operation you want to profile.
5. Stop profile & save analysis file
   ```
   fprof:trace([stop]).
   fprof:profile().
   fprof:analyse([totals, {dest, "/tmp/application.analysis"}]).
   fprof:stop().
   ```
6. Look at `/tmp/application.analysis` for the operation.
