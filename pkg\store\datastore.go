// Package store datastore functions and interfaces
package store

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	zlog "github.com/rs/zerolog/log"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/smithy-go/middleware"
)

// DataStoreItem inteface for items
type DataStoreItem interface {
	PK(tenant string) string
	SK(tenant string) string
}

// DataStoreInterface interface
type DataStoreInterface interface {
	//blocklist.go
	DoesExceedMaxBlockCount(ctx context.Context, userId string, add int) (bool, error)
	ModifyBlocklist(ctx context.Context, userId string, add *[]*apipub.BlocklistResponse, remove *[]*apipub.BlocklistResponse) error
	GetBlocklistWithLimit(ctx context.Context, userid, productid string, ost apipub.OnlineServiceType, limit int, next *string) (*[]*apipub.BlocklistResponse, *string, error)
	DoesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error)

	//chat.go
	SaveChatMessage(ctx context.Context, message *apipub.ChatMessage) error

	//endorsement.go
	GetEndorsements(ctx context.Context, userid, productid string) (*[]*apipub.EndorsementResponse, error)
	IncrementEndorsement(ctx context.Context, userid, productid string, endorsement *apipub.EndorsementResponse, count int) error
	ResetEndorsement(ctx context.Context, userid, productid, endorsementName string) error
	RemoveEndorsement(ctx context.Context, userid, productid, endorsementName string) error

	//friend.go
	GetFriend(ctx context.Context, userid string, friendid string) (*apipub.FriendResponse, error)
	GetFullFriendList(ctx context.Context, userid string) (*[]*apipub.FriendResponse, error)
	GetFriends(ctx context.Context, userid string, status *apipub.FriendStatus, limit int, next *string) (*[]*apipub.FriendResponse, *string, error)
	MakeFriend(ctx context.Context, userid string, friendid string, message string, isUserBlocked bool, userOST apipub.OnlineServiceType) (string, error)
	MakeUnfriend(ctx context.Context, userid string, friendid string) error
	GetFriendsCount(ctx context.Context, userid string) (*int32, error)
	UpdateFriends(ctx context.Context, friends *[]*apipub.FriendResponse) error

	//profile.go
	GetUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error)
	GetUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error)
	PutUserProfile(ctx context.Context, profile *apipub.UserProfileResponse) error
	PutUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse) error

	//stats.go
	GetTotalFriendsCount(ctx context.Context, status string) (*int32, error)
	GetTotalUserCount(ctx context.Context) (*int32, error)

	//tsclientid.go
	GetTsClientId(ctx context.Context, clientid string) (*apitrusted.TsClientIdInfo, error)
	PutTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error
	DelTsClientId(ctx context.Context, clientid string) error

	//this file.  below.
	DeleteItemByPkSk(ctx context.Context, pk string, sk string) error
	PutItemInProfileTable(ctx context.Context, dsItem DataStoreItem) error
	GetDiscovery(ctx context.Context, productid string) (*[]apipub.DiscoveryResponse, error)
	SetDiscovery(ctx context.Context, productid string, list *[]apitrusted.DiscoveryResponse) error
	DeleteDiscovery(ctx context.Context, productid string) error
}

type DynamoDBInterface interface {
	Query(ctx context.Context, input *dynamodb.QueryInput, optFns ...func(*dynamodb.Options)) (*dynamodb.QueryOutput, error)
	Scan(ctx context.Context, input *dynamodb.ScanInput, optFns ...func(*dynamodb.Options)) (*dynamodb.ScanOutput, error)
	DeleteTable(ctx context.Context, input *dynamodb.DeleteTableInput, optFns ...func(*dynamodb.Options)) (*dynamodb.DeleteTableOutput, error)
	ListTables(ctx context.Context, input *dynamodb.ListTablesInput, optFns ...func(*dynamodb.Options)) (*dynamodb.ListTablesOutput, error)
	CreateTable(ctx context.Context, input *dynamodb.CreateTableInput, optFns ...func(*dynamodb.Options)) (*dynamodb.CreateTableOutput, error)
	BatchGetItem(ctx context.Context, params *dynamodb.BatchGetItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.BatchGetItemOutput, error)
	PutItem(ctx context.Context, input *dynamodb.PutItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.PutItemOutput, error)
	DeleteItem(ctx context.Context, input *dynamodb.DeleteItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.DeleteItemOutput, error)
	GetItem(ctx context.Context, input *dynamodb.GetItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.GetItemOutput, error)
	BatchWriteItem(ctx context.Context, params *dynamodb.BatchWriteItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.BatchWriteItemOutput, error)
	UpdateItem(ctx context.Context, params *dynamodb.UpdateItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.UpdateItemOutput, error)
}

type S3Interface interface {
	PutObject(ctx context.Context, input *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error)
	GetObject(ctx context.Context, input *s3.GetObjectInput, optFns ...func(*s3.Options)) (*s3.GetObjectOutput, error)
	HeadBucket(ctx context.Context, input *s3.HeadBucketInput, optFns ...func(*s3.Options)) (*s3.HeadBucketOutput, error)
	DeleteObject(ctx context.Context, input *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error)
}

// DataStore Structure
type DataStore struct {
	ddb DynamoDBInterface
	s3  S3Interface
	cfg *config.Config
	id  identity.IdentityInterface
	ds  DataStoreInterface
}

var _ health.DependentService = &DynamoDBWithMonitoring{}

type DynamoDBWithMonitoring struct {
	DynamoDBInterface
	socialTable   string
	chatTable     string
	serviceStatus *health.ServiceStatus
}

func newDynamodbClient(ctx context.Context, cfg *config.Config) *dynamodb.Client {
	config := aws.Config{
		Region:       cfg.AwsRegion,
		BaseEndpoint: aws.String(cfg.DynamoDBURL),
		Credentials:  utils.GetV2CredentialProvider(ctx),
	}

	config.APIOptions = append(config.APIOptions, func(s *middleware.Stack) error {
		return s.Serialize.Add(&telemetry.OnBuild{}, middleware.After)
	})
	config.APIOptions = append(config.APIOptions, func(s *middleware.Stack) error {
		return s.Finalize.Add(&telemetry.OnComplete{}, middleware.After)
	})

	var client *dynamodb.Client
	for attempt := 1; attempt <= cfg.AwsRequestMaxRetryAttempt; attempt++ {
		client = dynamodb.NewFromConfig(config, func(o *dynamodb.Options) {
			o.Retryer = utils.GetDefaultRetryStandard(cfg.AwsRequestMaxRetryAttempt)
		})

		if client != nil {
			return client
		}

		zlog.Error().Msg("failed to create a DynamoDB client")
		time.Sleep(time.Second * time.Duration(attempt))
	}
	return client
}

func NewDynamoDB(ctx context.Context, cfg *config.Config) *DynamoDBWithMonitoring {
	// mirror store api transport config
	// https://github.com/take-two-t2gp/d2c-pdstore-api/blob/e254b61fe61ffcc419847ba6d496739438a36bef/server/cmd/main.go#L85-L92
	// var httpClient *http.Client
	// awsTO := time.Second * 30
	// if cfg.DnsCaching {
	// 	httpClient = net.Dns.HttpClient(awsTO, func(t *http.Transport) {
	// 		t.MaxIdleConnsPerHost = t.MaxIdleConns
	// 	})
	// } else {
	// 	t := http.DefaultTransport.(*http.Transport).Clone()
	// 	t.MaxIdleConnsPerHost = t.MaxIdleConns
	// 	httpClient = &http.Client{
	// 		Timeout:   awsTO,
	// 		Transport: t,
	// 	}
	// }

	client := newDynamodbClient(ctx, cfg)
	endpoint := ""
	if client != nil && client.Options().BaseEndpoint != nil {
		endpoint = *client.Options().BaseEndpoint
	}

	status := &health.ServiceStatus{
		Instances: []*health.InstanceInfo{
			{
				Id:     endpoint,
				Status: health.UNKNOWN,
			},
		},
		Status: health.UNKNOWN,
	}

	return &DynamoDBWithMonitoring{
		DynamoDBInterface: client,
		socialTable:       cfg.ProfileTable,
		chatTable:         cfg.ChatMessagesTable,
		serviceStatus:     status,
	}
}

func (d *DynamoDBWithMonitoring) IsCritical() bool {
	return true
}

func (d *DynamoDBWithMonitoring) CheckHealth() bool {
	svc := d.serviceStatus.Instances[0]
	start := time.Now()

	extraInfo := struct {
		SocialTable string
		ChatTable   string
	}{
		SocialTable: d.socialTable,
		ChatTable:   d.chatTable,
	}

	// don't check on localhost
	if utils.StringContainsSubstr(svc.Id, "*************") || utils.StringContainsSubstr(svc.Id, "localhost") {
		health.SetInstance(svc, health.OK, start, 0, extraInfo)
		d.serviceStatus.Status = health.OK
		return true
	}

	req, err := http.NewRequest("GET", svc.Id, nil)
	if err != nil {
		health.SetInstance(svc, health.FAIL, start, 0, err.Error())
		zlog.Err(err).Msgf("DynamoDB unhealthy: failed to create request w/ url %s", svc.Id)
		return false
	}
	resp, err := http.DefaultClient.Do(req)
	elapsed := time.Since(start).Milliseconds()
	if err != nil {
		health.SetInstance(svc, health.FAIL, start, elapsed, err.Error())
		d.serviceStatus.Status = health.FAIL
		zlog.Err(err).Msgf("DynamoDB unhealthy: failed to GET %s", svc.Id)
		return false
	}

	defer resp.Body.Close()
	var body []byte
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		health.SetInstance(svc, health.FAIL, start, elapsed, err.Error())
		d.serviceStatus.Status = health.FAIL
		zlog.Err(err).Msg("DynamoDB unhealthy: failed to ready body")
		return false
	}

	if resp.StatusCode != http.StatusOK {
		health.SetInstance(svc, health.FAIL, start, elapsed, nil)
		d.serviceStatus.Status = health.FAIL
		zlog.Err(err).Msgf("DynamoDB unhealthy: status %d != 200", resp.StatusCode)
		return false
	}

	if !utils.StringContainsSubstr(string(body), "healthy") {
		health.SetInstance(svc, health.FAIL, start, elapsed, nil)
		d.serviceStatus.Status = health.FAIL
		zlog.Err(err).Str("ctx", "health-check").Msg("DynamoDB unhealthy: body does not contain healthy")
		return false
	}

	health.SetInstance(svc, health.OK, start, elapsed, extraInfo)
	d.serviceStatus.Status = health.OK
	return true
}

func (d *DynamoDBWithMonitoring) LastStatus() *health.ServiceStatus {
	return d.serviceStatus
}

var _ health.DependentService = &S3WithMonitoring{}

type S3WithMonitoring struct {
	S3Interface
	bucket        string
	serviceStatus *health.ServiceStatus
}

func NewS3Client(ctx context.Context, cfg *config.Config) *s3.Client {
	config := aws.Config{
		Region:      cfg.S3BucketRegion,
		Credentials: utils.GetV2CredentialProvider(ctx),
	}

	config.APIOptions = append(config.APIOptions, func(s *middleware.Stack) error {
		return s.Serialize.Add(&telemetry.OnBuild{}, middleware.After)
	})

	config.APIOptions = append(config.APIOptions, func(s *middleware.Stack) error {
		return s.Finalize.Add(&telemetry.OnComplete{}, middleware.After)
	})

	var s3Client *s3.Client
	for attempt := 1; attempt <= cfg.AwsRequestMaxRetryAttempt; attempt++ {
		s3Client = s3.NewFromConfig(config, func(o *s3.Options) {
			o.Retryer = utils.GetDefaultRetryStandard(cfg.AwsRequestMaxRetryAttempt)
		})
		if s3Client != nil {
			return s3Client
		}
		zlog.Error().Msg("failed to create a DynamoDB client")
		time.Sleep(time.Second * time.Duration(attempt))
	}
	return s3Client
}

func NewS3(ctx context.Context, cfg *config.Config) *S3WithMonitoring {
	s3Client := NewS3Client(ctx, cfg)
	endpoint := ""
	if s3Client != nil && s3Client.Options().BaseEndpoint != nil {
		endpoint = *s3Client.Options().BaseEndpoint
	}

	status := &health.ServiceStatus{
		Instances: []*health.InstanceInfo{
			{
				Id:     endpoint,
				Status: health.UNKNOWN,
			},
		},
		Status: health.UNKNOWN,
	}

	return &S3WithMonitoring{
		S3Interface:   s3Client,
		bucket:        cfg.SocialConfigBucket,
		serviceStatus: status,
	}
}

func (s *S3WithMonitoring) IsCritical() bool {
	return true
}

func (s *S3WithMonitoring) CheckHealth() bool {
	svc := s.serviceStatus.Instances[0]
	status := health.FAIL
	start := time.Now()

	headReq := &s3.HeadBucketInput{
		Bucket: &s.bucket,
	}
	_, err := s.HeadBucket(context.Background(), headReq)
	elapsed := time.Since(start).Milliseconds()
	if err != nil {
		health.SetInstance(svc, status, start, elapsed, err.Error())
		zlog.Err(err).Msg("S3 Bucket unreachable")
	} else {
		status = health.OK
		health.SetInstance(svc, status, start, elapsed, "bucket="+s.bucket)
	}

	s.serviceStatus.Status = status
	return status == health.OK
}

func (s *S3WithMonitoring) LastStatus() *health.ServiceStatus {
	return s.serviceStatus
}

func NewDataStore(ctx context.Context, cfg *config.Config, ddb DynamoDBInterface, s3 S3Interface, id identity.IdentityInterface) *DataStore {
	if utils.IsLocal() {
		createSocialTables(ddb, ctx, cfg)
	}

	return &DataStore{
		ddb: ddb,
		s3:  s3,
		cfg: cfg,
		id:  id,
		ds:  &MockDataStoreInterface{},
	}
}

func createSocialTables(ddb DynamoDBInterface, ctx context.Context, cfg *config.Config) {
	// create social table is needed
	res, lsterr := ddb.ListTables(ctx, nil)
	if lsterr != nil {
		zlog.Error().Msgf("Unable to list tables err=%v", lsterr)
	}
	if !utils.ArrayContainsString(res.TableNames, cfg.ProfileTable) {
		// we create a new table
		createTableInput := &dynamodb.CreateTableInput{
			TableName: &cfg.ProfileTable,
			AttributeDefinitions: []types.AttributeDefinition{
				{AttributeName: aws.String("pk"), AttributeType: "S"},
				{AttributeName: aws.String("sk"), AttributeType: "S"},
			},
			KeySchema: []types.KeySchemaElement{
				{AttributeName: aws.String("pk"), KeyType: "HASH"},
				{AttributeName: aws.String("sk"), KeyType: "RANGE"},
			},
			ProvisionedThroughput: &types.ProvisionedThroughput{ReadCapacityUnits: aws.Int64(5), WriteCapacityUnits: aws.Int64(5)},
		}
		_, tblerr := ddb.CreateTable(ctx, createTableInput)
		if tblerr != nil {
			zlog.Error().Msgf("CreateTable %s failed err=%v", cfg.ProfileTable, tblerr)
		}
	}

	if !utils.ArrayContainsString(res.TableNames, cfg.ChatMessagesTable) {
		// we create a new table
		createTableInput := &dynamodb.CreateTableInput{
			TableName: &cfg.ChatMessagesTable,
			AttributeDefinitions: []types.AttributeDefinition{
				{AttributeName: aws.String("pk"), AttributeType: "S"},
				{AttributeName: aws.String("sk"), AttributeType: "S"},
			},
			KeySchema: []types.KeySchemaElement{
				{AttributeName: aws.String("pk"), KeyType: "HASH"},
				{AttributeName: aws.String("sk"), KeyType: "RANGE"},
			},
			ProvisionedThroughput: &types.ProvisionedThroughput{ReadCapacityUnits: aws.Int64(5), WriteCapacityUnits: aws.Int64(5)},
		}
		_, tblerr := ddb.CreateTable(ctx, createTableInput)
		if tblerr != nil {
			zlog.Error().Msgf("CreateTable %s failed err=%v", cfg.ChatMessagesTable, tblerr)
		}
	}
}

// QueryByPkSkWithLimitAndNext query item with pk/sk and limit.
func (ds *DataStore) QueryByPkSkWithLimitAndNext(ctx context.Context, query dynamodb.QueryInput) ([]map[string]types.AttributeValue, *string, error) {
	log := logger.FromContext(ctx)
	var item *apipub.ItemPKSK
	var ret []map[string]types.AttributeValue
	limit := int32(0)
	if query.Limit != nil {
		limit = *query.Limit
	}

	for {
		span, _ := tracer.StartSpanFromContext(ctx, "ddbClient.Query", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("QueryByPkSkWithLimitAndNext"))
		setPkSkFromQueryExp(&span, query)
		results, err := ds.ddb.Query(ctx, &query)
		span.Finish()
		if err != nil {
			log.Error().Msgf("Query failed err=%v", err)
			return nil, nil, errs.SanitizeDynamoDBException(err)
		}
		if results == nil {
			return nil, nil, nil
		}
		ret = append(ret, results.Items...)

		attributevalue.UnmarshalMap(results.LastEvaluatedKey, &item)
		if results.LastEvaluatedKey != nil {
			query.ExclusiveStartKey = results.LastEvaluatedKey
			if len(ret) >= int(limit) {
				break
			}
		} else {
			break
		}
	}

	var next *string
	var lastItem *apipub.ItemPKSK
	lenRet := len(ret)

	if limit != 0 && lenRet > 0 && lenRet > int(limit) {
		attributevalue.UnmarshalMap(ret[limit-1], &lastItem)
		next = &lastItem.SK
		ret = ret[0:int(limit-1)]
	} else if limit != 0 && lenRet > 0 && lenRet == int(limit) {
		attributevalue.UnmarshalMap(ret[lenRet-1], &lastItem)
		next = &lastItem.SK
	} else {
		next = nil
	}

	return ret, next, nil
}

// BatchGetItems get multiple items based on array of IDs, and a pk startsby and sk startsby
func (ds *DataStore) BatchGetItems(ctx context.Context, ids []string, pkPrefix string, skPrefix string) ([]map[string]types.AttributeValue, error) {
	log := logger.FromContext(ctx)
	var keys []map[string]types.AttributeValue
	for _, id := range ids {
		attr := map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: pkPrefix + "#" + id},
		}
		if skPrefix != "" {
			attr["sk"] = &types.AttributeValueMemberS{Value: skPrefix + "#" + id}
		}
		keys = append(keys, attr)
	}

	// Specify the keys to retrieve for each table
	input := dynamodb.BatchGetItemInput{
		RequestItems:           map[string]types.KeysAndAttributes{ds.cfg.ProfileTable: {Keys: keys}},
		ReturnConsumedCapacity: "TOTAL",
	}
	span, _ := tracer.StartSpanFromContext(ctx, "ddbClient.BatchGetItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("BatchGetItem"))
	setPkSkFromBatchQueryExp(&span, keys)
	results, err := ds.ddb.BatchGetItem(ctx, &input)
	span.Finish()

	if err != nil {
		log.Error().Msgf("BatchGetItem failed err=%v", err)
		return nil, errs.SanitizeDynamoDBException(err)
	}
	if results != nil {
		return results.Responses[ds.cfg.ProfileTable], err
	}
	return nil, nil
}

func (ds *DataStore) BatchGetItemsExact(ctx context.Context, pks []string, sks []string) ([]map[string]types.AttributeValue, error) {
	log := logger.FromContext(ctx)
	var keys []map[string]types.AttributeValue
	for i, id := range pks {
		pk := id
		sk := sks[i]
		attr := map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: pk},
			"sk": &types.AttributeValueMemberS{Value: sk},
		}
		keys = append(keys, attr)
	}

	input := dynamodb.BatchGetItemInput{
		RequestItems:           map[string]types.KeysAndAttributes{ds.cfg.ProfileTable: {Keys: keys}},
		ReturnConsumedCapacity: "TOTAL",
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddbClient.BatchGetItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("BatchGetItemsExact"))
	setPkSkFromBatchQueryExp(&span, keys)
	results, err := ds.ddb.BatchGetItem(ctx, &input)
	span.Finish()

	if err != nil {
		log.Error().Msgf("BatchGetItem failed err=%v", err)
		return nil, errs.SanitizeDynamoDBException(err)
	}

	return results.Responses[ds.cfg.ProfileTable], nil
}

// DeleteItem delete item
func (ds *DataStore) DeleteItem(ctx context.Context, dsItem DataStoreItem) error {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	pk := dsItem.PK(tenant)
	sk := dsItem.SK(tenant)
	return ds.DeleteItemByPkSk(ctx, pk, sk)
}

// DeleteItemByPkSk remove item given pk and sk
func (ds *DataStore) DeleteItemByPkSk(ctx context.Context, pk string, sk string) error {
	log := logger.FromContext(ctx)
	key := map[string]types.AttributeValue{
		"pk": &types.AttributeValueMemberS{Value: pk},
		"sk": &types.AttributeValueMemberS{Value: sk},
	}

	input := &dynamodb.DeleteItemInput{
		TableName: &ds.cfg.ProfileTable,
		Key:       key,
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.DeleteItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("DeleteItemByPkSk"))
	span.SetTag("del_pk", pk)
	span.SetTag("del_sk", sk)
	_, err := ds.ddb.DeleteItem(ctx, input)
	span.Finish()
	if err != nil {
		log.Error().Str("pk", pk).Str("sk", sk).Msgf("DeleteItem failed err=%v", err)
		return errs.SanitizeDynamoDBException(err)
	}

	return nil
}

func (ds *DataStore) BatchWriteItems(ctx context.Context, requests *[]types.WriteRequest) error {
	log := logger.FromContext(ctx)
	if requests == nil || len(*requests) == 0 {
		return nil
	}

	count := len(*requests)
	start := 0
	end := 0
	var err error
	for end < count {
		// No more than 25 items in one batch request
		if start+25 < count {
			end = start + 25
		} else {
			end = count
		}

		batchWrite := &dynamodb.BatchWriteItemInput{
			RequestItems: map[string][]types.WriteRequest{
				ds.cfg.ProfileTable: (*requests)[start:end],
			},
		}

		span, _ := tracer.StartSpanFromContext(ctx, "ddb.BatchWriteItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("BatchWriteItems"))
		setPkSkFromWriteReq(&span, requests)
		_, err = ds.ddb.BatchWriteItem(ctx, batchWrite)
		span.Finish()
		if err != nil {
			log.Error().Msgf("BatchWriteItem failed err=%v", err)
		}

		start = end
	}

	return errs.SanitizeDynamoDBException(err)
}

// QueryItemCount get the item count
func (ds *DataStore) QueryItemCount(ctx context.Context, pk string, sk string) (*int32, error) {
	log := logger.FromContext(ctx)
	query := dynamodb.QueryInput{
		TableName:              &ds.cfg.ProfileTable,
		KeyConditionExpression: aws.String("#pk = :pk AND begins_with(#sk, :sk)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk": &types.AttributeValueMemberS{Value: pk},
			":sk": &types.AttributeValueMemberS{Value: sk},
		},
		ExpressionAttributeNames: map[string]string{
			"#pk": "pk",
			"#sk": "sk",
		},
		Select: "COUNT",
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddbClient.Query", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("QueryItemCount"))
	span.SetTag("query_pk", pk)
	span.SetTag("query_sk", sk)
	result, err := ds.ddb.Query(ctx, &query)
	span.Finish()
	if err != nil {
		log.Error().Str("pk", pk).Str("sk", sk).Msgf("Query failed err=%v", err)
		return nil, err
	}
	return &result.Count, nil
}

func (ds *DataStore) PutItemInProfileTable(ctx context.Context, dsItem DataStoreItem) error {
	return ds.PutItemInTable(ctx, ds.cfg.ProfileTable, dsItem)
}

// PutItemInTable store a data store item
func (ds *DataStore) PutItemInTable(ctx context.Context, table string, dsItem DataStoreItem) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	item, errMarshal := attributevalue.MarshalMap(dsItem)
	if errMarshal != nil {
		log.Error().Err(errMarshal).Msgf("failed to marshal datastore item")
		return errMarshal
	}
	item["pk"] = &types.AttributeValueMemberS{Value: dsItem.PK(tenant)}
	item["sk"] = &types.AttributeValueMemberS{Value: dsItem.SK(tenant)}

	input := dynamodb.PutItemInput{
		Item:      item,
		TableName: &table,
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.PutItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("PutItemInTable"))
	span.SetTag("put_pk", dsItem.PK(tenant))
	span.SetTag("put_sk", dsItem.SK(tenant))
	_, err := ds.ddb.PutItem(ctx, &input)
	span.Finish()

	if err != nil {
		log.Error().Err(err).Msgf("PutItemInTableWithPkSk() for %s failed", dsItem.PK(tenant))
		return errs.SanitizeDynamoDBException(err)
	}

	return nil
}

func (ds *DataStore) PutItems(ctx context.Context, dsItems []DataStoreItem) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	var ops []types.WriteRequest
	for _, dsItem := range dsItems {
		item, errMarshal := attributevalue.MarshalMap(dsItem)
		if errMarshal != nil {
			log.Error().Err(errMarshal).Msgf("failed to marshal datastore item")
			return errMarshal
		}

		item["pk"] = &types.AttributeValueMemberS{Value: dsItem.PK(tenant)}
		item["sk"] = &types.AttributeValueMemberS{Value: dsItem.SK(tenant)}

		ops = append(ops, types.WriteRequest{
			PutRequest: &types.PutRequest{
				Item: item,
			},
		})
	}

	input := &dynamodb.BatchWriteItemInput{
		RequestItems: map[string][]types.WriteRequest{
			ds.cfg.ProfileTable: ops,
		},
	}

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.BatchWriteItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("PutItems"))
	setPkSkFromWriteReq(&span, &ops)
	_, err := ds.ddb.BatchWriteItem(ctx, input)
	span.Finish()

	if err != nil {
		log.Error().Err(err).Msgf("BatchWriteItem failed err=%v", err)
		return errs.SanitizeDynamoDBException(err)
	}

	return nil
}

// GetItem retrieve a generic item from DyanmoDB
func (ds *DataStore) GetItem(ctx context.Context, pk string, sk string, item interface{}) (bool, error) {
	log := logger.FromContext(ctx)
	span, _ := tracer.StartSpanFromContext(ctx, "ddb.GetItem", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("GetItem"))
	span.SetTag("query_pk", pk)
	span.SetTag("query_sk", sk)
	result, err := ds.ddb.GetItem(ctx, &dynamodb.GetItemInput{
		Key: map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: pk},
			"sk": &types.AttributeValueMemberS{Value: sk},
		},
		TableName: &ds.cfg.ProfileTable,
	})
	span.Finish()
	if err != nil {
		log.Error().Err(err).Str("pk", pk).Str("sk", sk).Msgf("GetItem failed err=%v", err)
		return false, errs.SanitizeDynamoDBException(err)
	}
	if result == nil || result.Item == nil {
		return false, nil
	}

	err = attributevalue.UnmarshalMap(result.Item, &item)
	if err != nil {
		log.Error().Err(err).Msgf("UnmarshalMap failed to parse item %v", result.Item)
		return false, errs.New(http.StatusBadRequest, errs.EDynamodbUnmarshalFailed)
	}

	return true, nil
}

func NewTestDataStore(ctx context.Context, cfg *config.Config) *DataStore {
	// create dynamodb session & service interface
	ddb := NewDynamoDB(ctx, cfg)

	// create s3service session & service interface
	s3 := NewS3(ctx, cfg)

	return &DataStore{
		ddb: ddb,
		s3:  s3,
		cfg: cfg,
	}
}

// GetDynamoDB get the dynamodb object
func (ds *DataStore) GetDynamoDB() DynamoDBInterface {
	return ds.ddb
}

// GetDiscovery get discovery data from S3
func (ds *DataStore) GetDiscovery(ctx context.Context, appID string) (*[]apipub.DiscoveryResponse, error) {
	s3Key := fmt.Sprintf("discovery/%s/config.json", appID)
	content, err := getFileFromS3(ds, ctx, s3Key)
	if err != nil {
		return nil, err
	}

	var discoveryItems []apipub.DiscoveryResponse
	err = json.Unmarshal(*content, &discoveryItems)
	if err != nil {
		return nil, errs.New(http.StatusBadRequest, errs.EJsonParse)
	}
	if len(discoveryItems) <= 0 {
		return nil, errs.New(http.StatusNotFound, errs.EDiscoveryNotFound)
	}

	return &discoveryItems, nil
}

// SetDiscovery set discovery data in S3
func (ds *DataStore) SetDiscovery(ctx context.Context, appID string, items *[]apitrusted.DiscoveryResponse) error {
	if appID == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EInvalidProductID)
	}
	if items == nil {
		return errs.New(http.StatusUnprocessableEntity, errs.EInvalidRequest)
	}
	s3Key := fmt.Sprintf("discovery/%s/config.json", appID)

	b, err := json.Marshal(items)
	if err != nil {
		return errs.New(http.StatusBadRequest, errs.EJsonParse)
	}

	err = uploadToS3(ds, ctx, s3Key, &b)
	if err != nil {
		return err
	}

	return nil
}

func (ds *DataStore) DeleteDiscovery(ctx context.Context, appID string) error {
	s3Key := fmt.Sprintf("discovery/%s/config.json", appID)
	err := deleteFromS3(ds, ctx, s3Key)
	if err != nil {
		return err
	}

	return nil
}

// ReadSessionPolicyConfigs get session policy config from S3
func (ds *DataStore) ReadSessionPolicyConfigs(ctx context.Context) error {
	s3Key := "session-policy/session-policy-config.json"
	log := logger.FromContext(ctx)

	content, err := getFileFromS3(ds, ctx, s3Key)
	if err != nil || content == nil {
		log.Err(err).Msg("Failed to get session-policy-config.json from S3")
		return err
	}

	configMap := map[string]config.SessionPolicyConfig{}
	err = json.Unmarshal(*content, &configMap)
	if err != nil {
		log.Err(err).Msg("Failed to unmarshal config file raw content")
	}

	for k, c := range configMap {
		config.SetSessionPolicyConfigMap(k, c)
	}

	return err
}

func (ds *DataStore) GetSessionPolicyConfig(ctx context.Context, productId string) config.SessionPolicyConfig {
	configs := config.GetSessionPolicyConfigMap()
	c, ok := (*configs)[productId]
	if ok {
		return c
	}
	return config.GetDefaultSessionPolicyConfig()
}

func getFileFromS3(ds *DataStore, ctx context.Context, s3Key string) (*[]byte, error) {
	input := s3.GetObjectInput{
		Bucket: aws.String(ds.cfg.SocialConfigBucket),
		Key:    aws.String(s3Key),
	}

	output, err := ds.s3.GetObject(ctx, &input)
	if err != nil {
		zlog.Error().Err(err).Str("key", s3Key).Msgf("GetObject() failed")
		return nil, errs.New(http.StatusInternalServerError, errs.ES3GetFailed)
	}
	defer output.Body.Close()

	var content []byte
	content, err = io.ReadAll(output.Body)
	if err != nil {
		return nil, err
	}

	return &content, nil
}

func uploadToS3(ds *DataStore, ctx context.Context, s3Key string, buffer *[]byte) error {
	if buffer == nil {
		return errs.New(http.StatusUnprocessableEntity, errs.ES3PutFailed)
	}

	size := int64(len(*buffer))
	putObjInput := s3.PutObjectInput{
		Bucket:               aws.String(ds.cfg.SocialConfigBucket),
		Key:                  aws.String(s3Key),
		ACL:                  "private",
		Body:                 bytes.NewReader(*buffer),
		ContentLength:        aws.Int64(size),
		ContentType:          aws.String("application/json"),
		ContentDisposition:   aws.String("attachment"),
		ServerSideEncryption: "AES256",
		StorageClass:         "STANDARD",
	}

	output, err := ds.s3.PutObject(ctx, &putObjInput)
	if err != nil {
		zlog.Error().Err(err).Str("key", s3Key).Msgf("PutObject() failed")
		return errs.New(http.StatusInternalServerError, errs.ES3PutFailed)
	}

	zlog.Info().Msgf("OK s3 put output obj: %v", output)
	return nil
}

func deleteFromS3(ds *DataStore, ctx context.Context, s3Key string) error {
	delInput := s3.DeleteObjectInput{
		Bucket: aws.String(ds.cfg.SocialConfigBucket),
		Key:    aws.String(s3Key),
	}

	output, err := ds.s3.DeleteObject(ctx, &delInput)
	if err != nil {
		zlog.Error().Err(err).Str("key", s3Key).Msgf("DeleteObject() failed")
		return errs.New(http.StatusInternalServerError, errs.ES3DeleteFailed)
	}

	zlog.Info().Msgf("OK s3 delete output obj: %v", output)
	return nil
}

func setPkSkFromQueryExp(span *tracer.Span, query dynamodb.QueryInput) {
	if span == nil {
		return
	}
	setPkSkFromMap(span, "query", query.ExpressionAttributeValues)
}

func setPkSkFromScanExp(span *tracer.Span, query dynamodb.ScanInput) {
	if span == nil {
		return
	}
	setPkSkFromMap(span, "query", query.ExpressionAttributeValues)
}

func setPkSkFromBatchQueryExp(span *tracer.Span, keys []map[string]types.AttributeValue) {
	if span == nil {
		return
	}
	for i, k := range keys {
		setPkSkFromMap(span, fmt.Sprintf("query%v", i), k)
	}
}

func setPkSkFromWriteReq(span *tracer.Span, requests *[]types.WriteRequest) {
	if span == nil {
		return
	}
	if requests == nil {
		return
	}

	for i, req := range *requests {
		if req.PutRequest != nil {
			setPkSkFromMap(span, fmt.Sprintf("put%v", i), req.PutRequest.Item)
		}
		if req.DeleteRequest != nil {
			setPkSkFromMap(span, fmt.Sprintf("del%v", i), req.DeleteRequest.Key)
		}
	}
}

func setPkSkFromMap(span *tracer.Span, attType string, attMap map[string]types.AttributeValue) {
	if span == nil {
		return
	}
	for k, v := range attMap {
		if k == "pk" || k == ":pk" {
			(*span).SetTag(fmt.Sprintf("%s_pk", attType), v)
		} else if k == "sk" || k == ":sk" {
			(*span).SetTag(fmt.Sprintf("%s_sk", attType), v)
		}
	}
}
