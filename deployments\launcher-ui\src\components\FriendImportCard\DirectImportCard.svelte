<script lang="ts">
  import { onMount } from 'svelte';
  import { SVGAddFriend, SVGSteam } from '../../assets/icons';
  import {
    EVENT_FRIEND_REQUEST_MAKE,
    EVENT_FRIEND_REQUEST_MAKE_ERROR,
    EVENT_FRIEND_REQUEST_SENT,
  } from '../../constant';
  import {
    usePendingFriendsQuery,
    useTranslator,
    useTransportService,
  } from '../../hooks';
  import { isRequestSent } from '../../utils';
  import { Avatar } from '../Avatar';
  import { LoadingSpinner } from '../LoadingSpinner';
  import { Tooltip } from '../Tooltip';

  export let initials = '';
  export let avatar = '';
  export let displayName = '';
  export let userId = '';
  export let platform = '';
  export let loading = false;

  const t = useTranslator();
  const transportService = useTransportService();
  const pendingFriendsQueryResult = usePendingFriendsQuery();

  const onAddFriendClicked = () => {
    if (!requestSent) {
      loading = true;
      transportService.publishEvent(EVENT_FRIEND_REQUEST_MAKE, [userId]);
    }
  };

  onMount(() => {
    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_SENT, () => {
      loading = false;
    });

    transportService.subscribeEvent(EVENT_FRIEND_REQUEST_MAKE_ERROR, () => {
      loading = false;
    });
  });

  $: requestSent = isRequestSent($pendingFriendsQueryResult?.data, userId);
</script>

<style>
  .direct-import-card {
    background-color: var(
      --social-bg-color-import-friend-card,
      var(--default-bg-color-import-friend-card)
    );
    display: flex;
    align-items: center;
    padding: 0.625rem 1rem;
    box-sizing: border-box;
    border-radius: 0.25rem;
    margin-block-start: 0.5rem;
    min-width: 20.75rem;
    min-height: 3.75rem;
  }

  .direct-import-card .detail {
    color: rgba(255, 255, 255, 1);
    margin-left: 1rem;
    display: flex;
    flex-direction: column;
    line-height: 150%;
    text-transform: capitalize;
  }

  .detail .displayName {
    font-size: 1rem;
    font-weight: bold;
  }

  .detail .platform {
    font-size: 0.875rem;
    opacity: 0.6;
    display: flex;
    align-items: center;
  }

  .platform :global(svg) {
    width: 12px;
    height: 12px;
    margin-inline-end: 0.4rem;
  }

  .action {
    margin-inline-start: auto;
    margin-bottom: 0;
    border: none;
  }

  .action:hover {
    cursor: pointer;
  }

  .action.disabled {
    opacity: 0.4;
  }

  .action.disabled:hover {
    cursor: not-allowed;
  }
</style>

<div class="direct-import-card">
  <Avatar
    initials="{initials}"
    size="35px"
    src="{avatar}"
    showPresence="{false}"
  />
  <div class="detail">
    <span class="displayName">{displayName}</span>
    <span class="platform">
      {#if platform === 'steam'}
        <SVGSteam />
      {/if}
      {displayName}
    </span>
  </div>
  <div
    class="action"
    class:disabled="{requestSent}"
    on:click="{onAddFriendClicked}"
  >
    <Tooltip value="{requestSent ? $t('sent') : $t('add')}">
      {#if loading}
        <LoadingSpinner size="{24}" />
      {:else}
        <SVGAddFriend />
      {/if}
    </Tooltip>
  </div>
</div>
