{"eslint.alwaysShowStatus": true, "eslint.lintTask.enable": true, "eslint.workingDirectories": [{"directory": ".", "changeProcessCWD": true}], "eslint.validate": ["typescript", "svelte"], "typescript.tsdk": "node_modules/typescript/lib", "editor.formatOnSave": true, "eslint.format.enable": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.organizeImports": true, "source.fixAll.eslint": false}}