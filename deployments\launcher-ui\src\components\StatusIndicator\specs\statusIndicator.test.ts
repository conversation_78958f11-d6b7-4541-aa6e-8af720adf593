import { render } from '@testing-library/svelte';
import StatusIndicator from '../StatusIndicator.svelte';

describe('StatusIndicator', () => {
  it('should render online', () => {
    const { container } = render(StatusIndicator, {
      props: {
        online: true,
      },
    });
    expect(container.querySelector('.online')).not.toBeNull();
  });

  it('should render playing', () => {
    const { container } = render(StatusIndicator, {
      props: {
        playing: true,
      },
    });
    expect(container.querySelector('.playing')).not.toBeNull();
  });

  it('should render away', () => {
    const { container } = render(StatusIndicator, {
      props: {
        away: true,
      },
    });
    expect(container.querySelector('.away')).not.toBeNull();
  });

  it('should render offline', () => {
    const { container } = render(StatusIndicator, {
      props: {
        offline: true,
      },
    });
    expect(container.querySelector('.offline')).not.toBeNull();
  });

  it('should render custom classname', () => {
    const { container } = render(StatusIndicator, {
      props: {
        className: 'className',
      },
    });
    expect(container.querySelector('.className')).not.toBeNull();
  });
});
