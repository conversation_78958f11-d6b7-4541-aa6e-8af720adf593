import * as socialApi from '../../../lib/social-api';
import { config, TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

/**
 * NOTE
 *
 * setPresence.test.ts is about self-view of presence; whereas friendPresence.test.ts is about view from others
 *
 * friendship is not tied to app id (token with different app id would get you the same list of friends)
 * presence is tied to app id
 *
 * user should get friend presence update within his app/product (i.e. same product id as his token)
 */

describe('', () => {
  let usersTwok: TwokAccounts;
  const message: string = 'T2GP Social Automated Testing';

  // populate friend labels
  let friendCnt: number = 3;
  let friendLabels: string[] = [];
  for (let i = 1; i <= friendCnt; i++) {
    friendLabels.push("friend" + i.toString());
  }

  // all labels (user and all his friends)
  let allLabels: string[] = ["user"].concat(friendLabels);

  // array of all apps that user and his friends are in
  let appArray = [
    config.apps.alphaRace,
    config.apps.socialServiceProduct,
    config.apps.ghostPepper
  ];
  let appIdArray: string[] = [];

  for (let i = 0; i < appArray.length; i++) {
    appIdArray.push(appArray[i].appId);
  }

  beforeAll(async () => {
    usersTwok = new TwokAccounts(allLabels.length, allLabels);
    await usersTwok.loginAll({appIdArray: appIdArray});

    // create friendship between user and all friends
    for (let i = 0; i < friendLabels.length; i++) {
      let r = await socialApi.makeFriendsV1(
        usersTwok.acct["user"],
        { userid: usersTwok.acct[friendLabels[i]].publicId, message: message },
        appArray[0].appId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      r = await socialApi.makeFriendsV1(
        usersTwok.acct[friendLabels[i]],
        { userid: usersTwok.acct["user"].publicId, message: message },
        appArray[0].appId
      );
      socialApi.testStatus(StatusCodes.OK, r);
    }
  });

  afterAll(async () => {
    // remove all friendship
    for (let i = 0; i < friendLabels.length; i++) {
      await socialApi.deleteFriend(
        usersTwok.acct["user"],
        usersTwok.acct[friendLabels[i]].publicId,
        appArray[0].appId
      );
    }

    await usersTwok.logoutAll({appIdArray: appIdArray});
  });

  beforeEach(async () => {
    for (let i = 0; i < appArray.length; i++) {
      for (let j = 0; j < allLabels.length; j++) {
        await socialApi.clearPresence(
          usersTwok.acct[allLabels[j]],
          appArray[i].appId
        );
      }
    }
  });

  afterEach(async () => {
    for (let i = 0; i < appArray.length; i++) {
      for (let j = 0; j < allLabels.length; j++) {
        await socialApi.clearPresence(
          usersTwok.acct[allLabels[j]],
          appArray[i].appId
        );
      }
    }
  });

  it('friend update presence status in a product[public v1 happy]', async() => {
    let testCase = {
      description: `set presence status of friends in all products; one friend updates presence status with new status in a product;`,
      expected: "friends presence update within his product"
    }
    // the friend that updates status
    let statusChgFriendLabel = "friend2";
    // the product/app the friend updates status from
    let statusChgAppIdx = 1;
    // the friend that updates status - new status
    let statusChgStatus = "away";
    // initial presence status of everyone
    let iniStatus = "dnd";
    // game name
    let gameName = "Automated API Tests Game Name";

    // set presence status of everyone in all products
    for (let i = 0; i < appArray.length; i++) {
      for (let j = 0; j < allLabels.length; j++) {
        let r = await socialApi.setPresenceV1(
          usersTwok.acct[allLabels[j]],
          { status: iniStatus, gameName: gameName },
          appArray[i].appId
        );
        socialApi.testStatus(StatusCodes.OK, r);
      }
    }

    // set presence status of the friend that updates status
    let r = await socialApi.setPresenceV1(
      usersTwok.acct[statusChgFriendLabel],
      { status: statusChgStatus, gameName: gameName },
      appArray[statusChgAppIdx].appId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    for (let i = 0; i < appArray.length; i++) {
      // Note that different token from different app ID will get the same friends, but with presence objects of matching app ID.
      const actualFriendInfo = await socialApi.getFriends(
        usersTwok.acct["user"],
        {},
        appArray[i].appId
      );
      socialApi.testStatus(StatusCodes.OK, actualFriendInfo);

      // create an array of the expected friend objects
      let expFriendArray = [];
      for (let j = 0; j < friendLabels.length; j++) {
        let status;
        if ((friendLabels[j] == statusChgFriendLabel) && (i == statusChgAppIdx)) {
          status = statusChgStatus;
        } else {
          status = iniStatus;
        }

        expFriendArray.push(
          expect.objectContaining({
            friendid: usersTwok.acct[friendLabels[j]].publicId,
            // Note that this hardcoded array of 1 item implies presence objects of different product IDs should not be present.
            presence: [
              expect.objectContaining({
                productid: appArray[i].productId,
                status: status,
                userid: usersTwok.acct[friendLabels[j]].publicId,
              })
            ]
          })
        );
      }

      const expectedFriendInfo = {
        status: StatusCodes.OK,
        body: {
          items: expect.arrayContaining(expFriendArray),
        }
      }
      socialApi.expectMore(
        () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)}, 
        testCase, 
        {
          resp: actualFriendInfo,
          additionalInfo: {
            "fail reason": "unexpected friend presence status in the product"
          }
        }
      );
    }
  })
})