import type { PendingFriend } from '../services';

const STEAM_OPENID_CLAIM_ID_KEY = 'openid.claimed_id';
const STEAM_OPENID_ID_PREFIX = 'https://steamcommunity.com/openid/id/';

export const getSteamIdFromLocationSearch = (search: string) => {
  const params = new URLSearchParams(search);
  const claimId = params.get(STEAM_OPENID_CLAIM_ID_KEY);
  const steamId = claimId?.replace(STEAM_OPENID_ID_PREFIX, '');

  return steamId || '';
};

export const sortByName = (nameA: string, nameB: string) => {
  // ignore upper and lowercase
  if (nameA < nameB) {
    return -1;
  }
  if (nameA > nameB) {
    return 1;
  }

  // names must be equal
  return 0;
};

export const requestSentByUser = (userId: string) => (
  friend: PendingFriend
) => {
  return friend.invitee !== userId;
};

export const requestReceivedByUser = (userId: string) => (
  friend: PendingFriend
) => {
  return friend.invitee === userId;
};

export const isRequestSent = (
  pendingFriends: PendingFriend[],
  friendUserId
) => {
  if (pendingFriends && pendingFriends.length > 0) {
    return pendingFriends.some(friend => friend.userid === friendUserId);
  }

  return false;
};
