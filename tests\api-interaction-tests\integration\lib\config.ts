import * as socialApi from './social-api';
import { StatusCodes } from 'http-status-codes';
import request from 'supertest';
import AWS from 'aws-sdk';
import * as dotenv from 'dotenv';

dotenv.config({ path: '.env.common_config' });
dotenv.config({ path: '.env.twok_accounts' });
dotenv.config({ path: '.env.steam_accounts' });
dotenv.config({ path: '.env.epic_accounts' });
dotenv.config({ path: '.env.switch_accounts' });
dotenv.config({ path: '.env.xbx_accounts' });
dotenv.config({ path: '.env.xb1_accounts' });
dotenv.config({ path: '.env.ps4_accounts' });
dotenv.config({ path: '.env.ps5_accounts' });

process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
process.env.AWS_SDK_LOAD_CONFIG = '1';

/**
 * Read in configuration from .env.common_config
 */
// Please keep things in the same order as .env.common_config
const {
  /**
   * Apps, App IDs, and Product IDs
   */
  // default app
  DEFAULT_APP,

  // app IDs and product IDs
  T2GP_SAMPLE_GAME_STEAM_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_EPIC_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_SWITCH_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_XBX_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_XB1_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_PS4_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_PS5_TESTING_APP_ID,
  T2GP_SAMPLE_GAME_PRODUCT_ID,

  ALPHA_RACE_APP_ID,
  ALPHA_RACE_PRODUCT_ID,

  SOCIAL_SERVICE_PRODUCT_APP_ID,
  SOCIAL_SERVICE_PRODUCT_PRODUCT_ID,

  GHOST_PEPPER_APP_ID,
  GHOST_PEPPER_PRODUCT_ID,

  WINDOWS_APP_ID,
  WINDOWS_PRODUCT_ID,


  /**
   * Access Levels, Environments, API Versions and Endpoints
   */

  // social API access level
  SOCIAL_API_ACCESS_LEVEL,

  // environment name
  SOCIAL_SERVICE_ENVIRONMENT,

  // social API version
  SOCIAL_API_VERSION,

  // custom public service endpoints
  PUBLIC_SERVICE_API_URL,
  PUBLIC_SERVICE_MQTT_URL,

  // public service endpoints local table
  PUBLIC_SERVICE_DEVELOP_API_URL,
  PUBLIC_SERVICE_DEVELOP_MQTT_URL,

  PUBLIC_SERVICE_INTEGRATION_API_URL,
  PUBLIC_SERVICE_INTEGRATION_MQTT_URL,

  PUBLIC_SERVICE_STAGING_API_URL,
  PUBLIC_SERVICE_STAGING_MQTT_URL,

  PUBLIC_SERVICE_CERT_API_URL,
  PUBLIC_SERVICE_CERT_MQTT_URL,

  PUBLIC_SERVICE_PRODUCTION_API_URL,
  PUBLIC_SERVICE_PRODUCTION_MQTT_URL,


  // custom trusted server endpoints
  TRUSTED_SERVER_API_URL,
  TRUSTED_SERVER_MQTT_URL,

  // trusted server endpoints local table
  TRUSTED_SERVER_DEVELOP_API_URL,
  TRUSTED_SERVER_DEVELOP_MQTT_URL,

  TRUSTED_SERVER_INTEGRATION_API_URL,
  TRUSTED_SERVER_INTEGRATION_MQTT_URL,

  TRUSTED_SERVER_STAGING_API_URL,
  TRUSTED_SERVER_STAGING_MQTT_URL,

  TRUSTED_SERVER_CERT_API_URL,
  TRUSTED_SERVER_CERT_MQTT_URL,

  TRUSTED_SERVER_PRODUCTION_API_URL,
  TRUSTED_SERVER_PRODUCTION_MQTT_URL,

  // custom trusted server credential
  TRUSTED_CREDENTIAL,

  // custom trusted server credential type
  TRUSTED_CREDENTIAL_TYPE,

  // trusted server credentials local table
  TRUSTED_DEVELOP_V1_CREDENTIAL,

  TRUSTED_INTEGRATION_V1_CREDENTIAL,

  TRUSTED_STAGING_V1_CREDENTIAL,

  TRUSTED_CERT_V1_CREDENTIAL,

  TRUSTED_PRODUCTION_V1_CREDENTIAL,

  // trusted credential type local table
  TRUSTED_DEVELOP_V1_CREDENTIAL_TYPE,

  TRUSTED_INTEGRATION_V1_CREDENTIAL_TYPE,

  TRUSTED_STAGING_V1_CREDENTIAL_TYPE,

  TRUSTED_CERT_V1_CREDENTIAL_TYPE,

  TRUSTED_PRODUCTION_V1_CREDENTIAL_TYPE,

  TRUSTED_DEVELOP_V2_CREDENTIAL_TYPE,

  TRUSTED_INTEGRATION_V2_CREDENTIAL_TYPE,

  TRUSTED_STAGING_V2_CREDENTIAL_TYPE,

  TRUSTED_CERT_V2_CREDENTIAL_TYPE,

  TRUSTED_PRODUCTION_V2_CREDENTIAL_TYPE,

  // DNA SSO environment
  DNA_SSO_ENVIRONMENT,

  // DNA SSO endpoints
  DNA_SSO_DEVELOP_API_URL,
  DNA_SSO_STAGING_API_URL,
  DNA_SSO_PRODUCTION_API_URL,


  /**
   * Misc
   */
  // Datadog API key. For Sanity Test. Provided by repo secret.
  DD_API_KEY,

  //
  ROOM_PASSWORD,
  BAD_WORDS,

  AUTH_SECRET_PATH,
  SERVER_INSTANCE_ID,
} = process.env;


/**
 * config object
 */
interface AppInfo {
  appId: string;
  productId: string;
}

interface Apps {
  steam: AppInfo;
  epic: AppInfo;
  switch: AppInfo;
  xbx: AppInfo;
  xb1: AppInfo;
  ps4: AppInfo;
  ps5: AppInfo;
  alphaRace: AppInfo;
  socialServiceProduct: AppInfo;
  ghostPepper: AppInfo;
  windows: AppInfo;
  default: AppInfo;
}

interface SocialEndpointVersion {
  api: string;
  mqtt: string;
}

interface SocialEnvironment {
  v1: SocialEndpointVersion;
  v2: SocialEndpointVersion;
  currVer: SocialEndpointVersion;
}
interface SocialAccessLevel {
  develop: SocialEnvironment;
  integration: SocialEnvironment;
  staging: SocialEnvironment;
  cert: SocialEnvironment;
  production: SocialEnvironment;
  currEnv: SocialEnvironment;
}

interface SocialService {
  public: SocialAccessLevel;
  trusted: SocialAccessLevel;
  currAccessLevel: SocialAccessLevel;
}

interface CredentialInfo {
  credential: string | (() => Promise<string>);
  credentialType: string;
}

interface BasicAuthInfo {
  credential: string;
  credentialType: string;
}
interface DTLTokenInfo {
  credential: ((basicAuthParam?: string) => Promise<string>);
  credentialType: string;
}

interface TrustedEnvironment {
  v1: BasicAuthInfo;
  v2: DTLTokenInfo;
  currVer: CredentialInfo;
}

export interface TrustedCredentials {
  develop: TrustedEnvironment;
  integration: TrustedEnvironment;
  staging: TrustedEnvironment;
  cert: TrustedEnvironment;
  production: TrustedEnvironment;
  currEnv: TrustedEnvironment;
}

interface DnaEndpoint {
  api: string;
}

interface DnaEndpoints {
  develop: DnaEndpoint;
  staging: DnaEndpoint;
  production: DnaEndpoint;
  currEnv: DnaEndpoint;
}

//
interface IConfig {
  apps: Apps;

  socialAPIAccessLevel: string;

  socialServiceEnvironment: string;

  socialAPIVersion: string;

  socialService: SocialService;

  trustedCredentials: TrustedCredentials;

  dnaSsoEnvironment: string;

  dnaEndpoints: DnaEndpoints;

  ddApiKey: string;

  roomPassword: string;
  Bad_Words: string;

  authSecretPath: string;
  serverInstanceId: string;
}

// This is caching the trusted server DTL token, and is meant to be at the module level.
let tokenCache = "";

async function getTrustedToken(basicAuthParam?: string): Promise<string> {
  if (tokenCache != "") {
    return tokenCache;
  }

  let basicAuthCredential: string;

  if (basicAuthParam) {
    basicAuthCredential = basicAuthParam;
  } else {
    const secretsManager = new AWS.SecretsManager({ region: 'us-east-1' });
    const secretValueResponse = await secretsManager.getSecretValue({ SecretId: config.authSecretPath }).promise();
    if (!secretValueResponse.SecretString) {
      throw new Error('missing basic auth credential');
    }
    basicAuthCredential = secretValueResponse.SecretString;
  }

  const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
    .post('/auth/tokens')
    .set('Authorization', 'Basic ' + basicAuthCredential)
    .send({
      locale: 'en-US',
      accountType: '2',
      credentials: {
        type: 'server',
        instanceId: config.serverInstanceId
      },
    });

  if (resp.status == StatusCodes.OK) {
    tokenCache = resp.body.accessToken;
    return resp.body.accessToken;
  } else {
    throw new Error(`trusted login failed.
        status: [${resp.status}]
        body: [${JSON.stringify(resp.body, null, 4)}]`);
  }
}

//
export const config: IConfig = {
  apps: {
    steam: {
      appId: T2GP_SAMPLE_GAME_STEAM_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    epic: {
      appId: T2GP_SAMPLE_GAME_EPIC_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    switch: {
      appId: T2GP_SAMPLE_GAME_SWITCH_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    xbx: {
      appId: T2GP_SAMPLE_GAME_XBX_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    xb1: {
      appId: T2GP_SAMPLE_GAME_XB1_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    ps4: {
      appId: T2GP_SAMPLE_GAME_PS4_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    ps5: {
      appId: T2GP_SAMPLE_GAME_PS5_TESTING_APP_ID as string,
      productId: T2GP_SAMPLE_GAME_PRODUCT_ID as string
    },
    alphaRace: {
      appId: ALPHA_RACE_APP_ID as string,
      productId: ALPHA_RACE_PRODUCT_ID as string
    },
    socialServiceProduct: {
      appId: SOCIAL_SERVICE_PRODUCT_APP_ID as string,
      productId: SOCIAL_SERVICE_PRODUCT_PRODUCT_ID as string
    },
    ghostPepper: {
      appId: GHOST_PEPPER_APP_ID as string,
      productId: GHOST_PEPPER_PRODUCT_ID as string
    },
    windows: {
      appId: WINDOWS_APP_ID as string,
      productId: WINDOWS_PRODUCT_ID as string
    },
    default: {
      appId: "",
      productId: ""
    }
  },

  socialAPIAccessLevel: SOCIAL_API_ACCESS_LEVEL as string,

  socialServiceEnvironment: SOCIAL_SERVICE_ENVIRONMENT as string,

  socialAPIVersion: SOCIAL_API_VERSION as string,

  socialService: {
    public: {
      develop: {
        v1: {
          api: `${PUBLIC_SERVICE_DEVELOP_API_URL}/v1`,
          mqtt: PUBLIC_SERVICE_DEVELOP_MQTT_URL as string
        },
        v2: {
          api: `${PUBLIC_SERVICE_DEVELOP_API_URL}/v2`,
          mqtt: PUBLIC_SERVICE_DEVELOP_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      integration: {
        v1: {
          api: `${PUBLIC_SERVICE_INTEGRATION_API_URL}/v1`,
          mqtt: PUBLIC_SERVICE_INTEGRATION_MQTT_URL as string
        },
        v2: {
          api: `${PUBLIC_SERVICE_INTEGRATION_API_URL}/v2`,
          mqtt: PUBLIC_SERVICE_INTEGRATION_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      staging: {
        v1: {
          api: `${PUBLIC_SERVICE_STAGING_API_URL}/v1`,
          mqtt: PUBLIC_SERVICE_STAGING_MQTT_URL as string
        },
        v2: {
          api: `${PUBLIC_SERVICE_STAGING_API_URL}/v2`,
          mqtt: PUBLIC_SERVICE_STAGING_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      cert: {
        v1: {
          api: `${PUBLIC_SERVICE_CERT_API_URL}/v1`,
          mqtt: PUBLIC_SERVICE_CERT_MQTT_URL as string
        },
        v2: {
          api: `${PUBLIC_SERVICE_CERT_API_URL}/v2`,
          mqtt: PUBLIC_SERVICE_CERT_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      production: {
        v1: {
          api: `${PUBLIC_SERVICE_PRODUCTION_API_URL}/v1`,
          mqtt: PUBLIC_SERVICE_PRODUCTION_MQTT_URL as string
        },
        v2: {
          api: `${PUBLIC_SERVICE_PRODUCTION_API_URL}/v2`,
          mqtt: PUBLIC_SERVICE_PRODUCTION_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      currEnv: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
    },
    trusted: {
      develop: {
        v1: {
          api: `${TRUSTED_SERVER_DEVELOP_API_URL}/v1/server`,
          mqtt: TRUSTED_SERVER_DEVELOP_MQTT_URL as string
        },
        v2: {
          api: `${TRUSTED_SERVER_DEVELOP_API_URL}/v2/server`,
          mqtt: TRUSTED_SERVER_DEVELOP_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      integration: {
        v1: {
          api: `${TRUSTED_SERVER_INTEGRATION_API_URL}/v1/server`,
          mqtt: TRUSTED_SERVER_INTEGRATION_MQTT_URL as string
        },
        v2: {
          api: `${TRUSTED_SERVER_INTEGRATION_API_URL}/v2/server`,
          mqtt: TRUSTED_SERVER_INTEGRATION_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      staging: {
        v1: {
          api: `${TRUSTED_SERVER_STAGING_API_URL}/v1/server`,
          mqtt: TRUSTED_SERVER_STAGING_MQTT_URL as string
        },
        v2: {
          api: `${TRUSTED_SERVER_STAGING_API_URL}/v2/server`,
          mqtt: TRUSTED_SERVER_STAGING_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      cert: {
        v1: {
          api: `${TRUSTED_SERVER_CERT_API_URL}/v1/server`,
          mqtt: TRUSTED_SERVER_CERT_MQTT_URL as string
        },
        v2: {
          api: `${TRUSTED_SERVER_CERT_API_URL}/v2/server`,
          mqtt: TRUSTED_SERVER_CERT_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      production: {
        v1: {
          api: `${TRUSTED_SERVER_PRODUCTION_API_URL}/v1/server`,
          mqtt: TRUSTED_SERVER_PRODUCTION_MQTT_URL as string
        },
        v2: {
          api: `${TRUSTED_SERVER_PRODUCTION_API_URL}/v2/server`,
          mqtt: TRUSTED_SERVER_PRODUCTION_MQTT_URL as string
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      currEnv: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      }
    },
    currAccessLevel: {
      develop: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      integration: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      staging: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      cert: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      production: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      },
      currEnv: {
        v1: {
          api: "",
          mqtt: ""
        },
        v2: {
          api: "",
          mqtt: ""
        },
        currVer: {
          api: "",
          mqtt: ""
        }
      }
    }
  },

  trustedCredentials: {
    develop: {
      v1: {
        credential: TRUSTED_DEVELOP_V1_CREDENTIAL as string,
        credentialType: TRUSTED_DEVELOP_V1_CREDENTIAL_TYPE as string
      },
      v2: {
        credential: getTrustedToken,
        credentialType: TRUSTED_DEVELOP_V2_CREDENTIAL_TYPE as string
      },
      currVer: {
        credential: "",
        credentialType: ""
      }
    },
    integration: {
      v1: {
        credential: TRUSTED_INTEGRATION_V1_CREDENTIAL as string,
        credentialType: TRUSTED_INTEGRATION_V1_CREDENTIAL_TYPE as string
      },
      v2: {
        credential: getTrustedToken,
        credentialType: TRUSTED_INTEGRATION_V2_CREDENTIAL_TYPE as string
      },
      currVer: {
        credential: "",
        credentialType: ""
      }
    },
    staging: {
      v1: {
        credential: TRUSTED_STAGING_V1_CREDENTIAL as string,
        credentialType: TRUSTED_STAGING_V1_CREDENTIAL_TYPE as string
      },
      v2: {
        credential: getTrustedToken,
        credentialType: TRUSTED_STAGING_V2_CREDENTIAL_TYPE as string
      },
      currVer: {
        credential: "",
        credentialType: ""
      }
    },
    cert: {
      v1: {
        credential: TRUSTED_CERT_V1_CREDENTIAL as string,
        credentialType: TRUSTED_CERT_V1_CREDENTIAL_TYPE as string
      },
      v2: {
        credential: getTrustedToken,
        credentialType: TRUSTED_CERT_V2_CREDENTIAL_TYPE as string
      },
      currVer: {
        credential: "",
        credentialType: ""
      }
    },
    production: {
      v1: {
        credential: TRUSTED_PRODUCTION_V1_CREDENTIAL as string,
        credentialType: TRUSTED_PRODUCTION_V1_CREDENTIAL_TYPE as string
      },
      v2: {
        credential: getTrustedToken,
        credentialType: TRUSTED_PRODUCTION_V2_CREDENTIAL_TYPE as string
      },
      currVer: {
        credential: "",
        credentialType: ""
      }
    },
    currEnv: {
      v1: {
        credential: "",
        credentialType: ""
      },
      v2: {
        credential: async () => {return ""},
        credentialType: ""
      },
      currVer: {
        credential: "",
        credentialType: ""
      }
    }
  },

  dnaSsoEnvironment: DNA_SSO_ENVIRONMENT as string,

  dnaEndpoints: {
    develop: {
      api: DNA_SSO_DEVELOP_API_URL as string,
    },
    staging: {
      api: DNA_SSO_STAGING_API_URL as string,
    },
    production: {
      api: DNA_SSO_PRODUCTION_API_URL as string,
    },
    currEnv: {
      api: "",
    }
  },

  ddApiKey: DD_API_KEY as string,

  roomPassword: ROOM_PASSWORD as string,
  Bad_Words: BAD_WORDS as string,

  authSecretPath: AUTH_SECRET_PATH as string,
  serverInstanceId: SERVER_INSTANCE_ID as string,
};


/**
 * determine default app and product IDs
 */
switch (DEFAULT_APP) {
  case "steam":
    config.apps.default = config.apps.steam;
    break;
  case "epic":
    config.apps.default = config.apps.epic;
    break;
  case "switch":
    config.apps.default = config.apps.switch;
    break;
  case "xbx":
    config.apps.default = config.apps.xbx;
    break;
  case "xb1":
    config.apps.default = config.apps.xb1;
    break;
  case "ps4":
    config.apps.default = config.apps.ps4;
    break;
  case "ps5":
    config.apps.default = config.apps.ps5;
    break;
  case "alpha_race":
    config.apps.default = config.apps.alphaRace;
    break;
  case "social_service_product":
    config.apps.default = config.apps.socialServiceProduct;
    break;
  case "ghost_pepper":
    config.apps.default = config.apps.ghostPepper;
    break;
  case "windows":
    config.apps.default = config.apps.windows;
    break;
  default:
    throw new Error(`Unknown default app [${DEFAULT_APP}]`);
}


/**
 * determine current Social Service endpoints
 *
 * look up endpoints according to the API access level, environment and version.
 * use custom endpoints instead if they are supplied.
 * set the current version for the particular environment
 * set the current env for the particular access level first, then set the current access level to that.
 * for trusted server, also determine the current credentials.
 */
switch (SOCIAL_API_ACCESS_LEVEL) {
  case "public":
    // set the current env for public
    if ((PUBLIC_SERVICE_API_URL === "") || (PUBLIC_SERVICE_MQTT_URL === "")) {
      switch (SOCIAL_SERVICE_ENVIRONMENT) {
        case "develop":
          // set the current version for develop
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.public.develop.currVer = config.socialService.public.develop.v1;
              break;
            case "v2":
              config.socialService.public.develop.currVer = config.socialService.public.develop.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.public.currEnv = config.socialService.public.develop;
          break;
        case "integration":
          // set the current version for integration
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.public.integration.currVer = config.socialService.public.integration.v1;
              break;
            case "v2":
              config.socialService.public.integration.currVer = config.socialService.public.integration.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.public.currEnv = config.socialService.public.integration;
          break;
        case "staging":
          // set the current version for staging
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.public.staging.currVer = config.socialService.public.staging.v1;
              break;
            case "v2":
              config.socialService.public.staging.currVer = config.socialService.public.staging.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.public.currEnv = config.socialService.public.staging;
          break;
        case "cert":
          // set the current version for cert
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.public.cert.currVer = config.socialService.public.cert.v1;
              break;
            case "v2":
              config.socialService.public.cert.currVer = config.socialService.public.cert.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.public.currEnv = config.socialService.public.cert;
          break;
        case "production":
          // set the current version for production
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.public.production.currVer = config.socialService.public.production.v1;
              break;
            case "v2":
              config.socialService.public.production.currVer = config.socialService.public.production.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.public.currEnv = config.socialService.public.production;
          break;
        default:
          throw new Error(`Fail to look up public service endpoints from the local table for the [${SOCIAL_SERVICE_ENVIRONMENT}] environment`);
      }
    } else {
      console.log(`Using custom public service endpoints instead of the [${SOCIAL_SERVICE_ENVIRONMENT}] environment.  API: [${PUBLIC_SERVICE_API_URL}] MQTT: [${PUBLIC_SERVICE_MQTT_URL}].`);
      config.socialService.public.currEnv.currVer.api = PUBLIC_SERVICE_API_URL as string;
      config.socialService.public.currEnv.currVer.mqtt = PUBLIC_SERVICE_MQTT_URL as string;
    }
    // set the current access level
    config.socialService.currAccessLevel = config.socialService.public;
    break;
  case "trusted":
    // set the current env for trusted
    if ((TRUSTED_SERVER_API_URL === "") || (TRUSTED_SERVER_MQTT_URL === "") || (TRUSTED_CREDENTIAL_TYPE === "")) {
      switch (SOCIAL_SERVICE_ENVIRONMENT) {
        case "develop":
          // set the current version for develop
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.trusted.develop.currVer = config.socialService.trusted.develop.v1;
              config.trustedCredentials.develop.currVer = config.trustedCredentials.develop.v1;
              break;
            case "v2":
              config.socialService.trusted.develop.currVer = config.socialService.trusted.develop.v2;
              config.trustedCredentials.develop.currVer = config.trustedCredentials.develop.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.trusted.currEnv = config.socialService.trusted.develop;
          config.trustedCredentials.currEnv = config.trustedCredentials.develop;
          break;
        case "integration":
          // set the current version for integration
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.trusted.integration.currVer = config.socialService.trusted.integration.v1;
              config.trustedCredentials.integration.currVer = config.trustedCredentials.integration.v1;
              break;
            case "v2":
              config.socialService.trusted.integration.currVer = config.socialService.trusted.integration.v2;
              config.trustedCredentials.integration.currVer = config.trustedCredentials.integration.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.trusted.currEnv = config.socialService.trusted.integration;
          config.trustedCredentials.currEnv = config.trustedCredentials.integration;
          break;
        case "staging":
          // set the current version for staging
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.trusted.staging.currVer = config.socialService.trusted.staging.v1;
              config.trustedCredentials.staging.currVer = config.trustedCredentials.staging.v1;
              break;
            case "v2":
              config.socialService.trusted.staging.currVer = config.socialService.trusted.staging.v2;
              config.trustedCredentials.staging.currVer = config.trustedCredentials.staging.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.trusted.currEnv = config.socialService.trusted.staging;
          config.trustedCredentials.currEnv = config.trustedCredentials.staging;
          break;
        case "cert":
          // set the current version for cert
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.trusted.cert.currVer = config.socialService.trusted.cert.v1;
              config.trustedCredentials.cert.currVer = config.trustedCredentials.cert.v1;
              break;
            case "v2":
              config.socialService.trusted.cert.currVer = config.socialService.trusted.cert.v2;
              config.trustedCredentials.cert.currVer = config.trustedCredentials.cert.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.trusted.currEnv = config.socialService.trusted.cert;
          config.trustedCredentials.currEnv = config.trustedCredentials.cert;
          break;
        case "production":
          // set the current version for production
          switch (SOCIAL_API_VERSION) {
            case "v1":
              config.socialService.trusted.production.currVer = config.socialService.trusted.production.v1;
              config.trustedCredentials.production.currVer = config.trustedCredentials.production.v1;
              break;
            case "v2":
              config.socialService.trusted.production.currVer = config.socialService.trusted.production.v2;
              config.trustedCredentials.production.currVer = config.trustedCredentials.production.v2;
              break;
            default:
              throw new Error(`Unknown social API version [${SOCIAL_API_VERSION}]`);
          }
          config.socialService.trusted.currEnv = config.socialService.trusted.production;
          config.trustedCredentials.currEnv = config.trustedCredentials.production;
          break;
        default:
          throw new Error(`Fail to look up trusted server endpoints from the local table for the [${SOCIAL_SERVICE_ENVIRONMENT}] environment`);
      }
    } else {
      console.log(`Using custom trusted server endpoints instead of the [${SOCIAL_SERVICE_ENVIRONMENT}] environment.  API: [${TRUSTED_SERVER_API_URL}] MQTT: [${TRUSTED_SERVER_MQTT_URL}].`);
      config.socialService.trusted.currEnv.currVer.api = TRUSTED_SERVER_API_URL as string;
      config.socialService.trusted.currEnv.currVer.mqtt = TRUSTED_SERVER_MQTT_URL as string;
      config.trustedCredentials.currEnv.currVer.credentialType = TRUSTED_CREDENTIAL_TYPE as string;
      if (TRUSTED_CREDENTIAL_TYPE === "BasicAuth") {
        config.trustedCredentials.currEnv.currVer.credential = TRUSTED_CREDENTIAL as string;
      } else if (TRUSTED_CREDENTIAL_TYPE === 'DTLToken') {
        config.trustedCredentials.currEnv.currVer.credential = getTrustedToken;
      } else {
        throw new Error(`unsupported credentialType: ${TRUSTED_CREDENTIAL_TYPE}`);
      }
    }
    // set the current access level
    config.socialService.currAccessLevel = config.socialService.trusted;
    break;
  default:
    throw new Error(`Unknown social API access level [${SOCIAL_API_ACCESS_LEVEL}]`)
}


/**
 * determine DNA SSO API URL
 */
switch (DNA_SSO_ENVIRONMENT) {
  case "develop":
    config.dnaEndpoints.currEnv = config.dnaEndpoints.develop;
    break;
  case "staging":
    config.dnaEndpoints.currEnv = config.dnaEndpoints.staging;
    break;
  case "production":
    config.dnaEndpoints.currEnv = config.dnaEndpoints.production;
    break;
  default:
    throw new Error(`Unknown DNA SSO environment [${DNA_SSO_ENVIRONMENT}]`);
}


/**
 * A note on account inheritance:
 *
 * DNA account
 *   full account     |  platform account
 *     -> 2k account       -> steam, epic, etc.
 */


/**
 * Account info interfaces
 */

export interface DnaAccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
}

export interface FullAccountInfo extends DnaAccountInfo {
}

export interface TwokAccountInfo extends FullAccountInfo {
  email: string;
  password: string;
  displayName: string;
}

export interface PlatformAccountInfo extends DnaAccountInfo {
  platformId: string;
  alias: string;
}

export interface SteamAccountInfo extends PlatformAccountInfo {
}

export interface EpicAccountInfo extends PlatformAccountInfo {
}

export interface SwitchAccountInfo extends PlatformAccountInfo {
}

export interface XbxAccountInfo extends PlatformAccountInfo {
}

export interface Xb1AccountInfo extends PlatformAccountInfo {
}

export interface Ps4AccountInfo extends PlatformAccountInfo {
}

export interface Ps5AccountInfo extends PlatformAccountInfo {
}


/**
 * Account classes
 */

//
export abstract class DnaAccount implements DnaAccountInfo {
  abstract publicId: string;
  abstract accessToken: string;
  abstract accessTokenDict: {[key: string]: string};

  //
  async getLinkedAccounts(filterBy?: string) {
    const resp: request.Response = await DnaAccount.getLinkedAccounts(this, filterBy)
    return resp;
  }

  /**
   * static / utility functions
   */
  // valid filterBy values are: device, full, platform
  static async getLinkedAccounts(dnaAccount: DnaAccountInfo, filterBy?: string) {
    let queryString: string = '';
    if (typeof filterBy !== 'undefined') {
      queryString = `?filterBy=${filterBy}`;
    }

    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .get(`/user/accounts/me/links${queryString}`)
      .set({ Authorization: 'Bearer ' + dnaAccount.accessToken });
    return resp;
  }
}

//
export abstract class FullAccount extends DnaAccount implements FullAccountInfo {
  //
  async linkPlatformAccount(linkeeAccount: PlatformAccountInfo) {
    const resp: request.Response = await FullAccount.linkPlatformAccount(this, linkeeAccount);

    if (resp.status != StatusCodes.CREATED) {
      throw new Error(`linkPlatformAccount failed.
        status: [${resp.status}]
        body: [${JSON.stringify(resp.body, null, 4)}]
        linker: public ID [${this.publicId}]
        linkee: public ID [${linkeeAccount.publicId}] | platform ID [${linkeeAccount.platformId}] | alias [${linkeeAccount.alias}]`);
    }
  }

  //
  async unlinkPlatformAccount(linkType: string) {
    const resp: request.Response = await FullAccount.unlinkPlatformAccount(this, linkType);

    if (resp.status != StatusCodes.NO_CONTENT) {
      throw new Error(`unlinkPlatformAccount failed.
        status: [${resp.status}]
        body: [${JSON.stringify(resp.body, null, 4)}]
        unlinker: public ID [${this.publicId}]
        link type: [${linkType}]`);
    }
  }

  /**
   * static / utility functions
   */
  //
  static async linkPlatformAccount(linkerAccount: FullAccountInfo, linkeeAccount: PlatformAccountInfo) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .post('/user/accounts/me/links/platform')
      .set({ Authorization: 'Bearer ' + linkerAccount.accessToken })
      .send({
        accessToken: linkeeAccount.accessToken
      });
    return resp;
  }

  // NOTE: When the unlink occurs, this endpoint also revoke the active Spop session of the platform account.
  static async unlinkPlatformAccount(unlinkerAccount: FullAccountInfo, linkType: string) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .delete(`/user/accounts/me/links/${linkType}`)
      .set({ Authorization: 'Bearer ' + unlinkerAccount.accessToken });
    return resp;
  }


}

//
export class TwokAccount extends FullAccount implements TwokAccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  email: string;
  password: string;
  displayName: string;

  constructor(e: string, p: string, d: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};

    this.email = e;
    this.password = p;
    this.displayName = d;
  }

  async login(p: {appIdArray?: string[]}) {
    if (p.appIdArray != undefined) {
      for (let appId of p.appIdArray) {
        const resp: request.Response = await TwokAccount.loginDna({twokAccount: this, appId: appId});

        if (resp.status == StatusCodes.OK) {
          this.accessTokenDict[appId] = resp.body.accessToken;
        } else {
          throw new Error(`2K login failed.
            status: [${resp.status}]
            body: [${JSON.stringify(resp.body, null, 4)}]
            [${socialApi.maskEmailAddr(this.email)}]
            App ID: [${appId}]`);
        }
      }
    } else {
      const resp: request.Response = await TwokAccount.loginDna({twokAccount: this});

      if (resp.status == StatusCodes.OK) {
        this.accessTokenDict[config.apps.default.appId] = resp.body.accessToken;
        this.accessToken = resp.body.accessToken;
      } else {
        throw new Error(`2K login failed.
          status: [${resp.status}]
          body: [${JSON.stringify(resp.body, null, 4)}]
          [${socialApi.maskEmailAddr(this.email)}]`);
      }
    }
  }

  async logout(p: {appIdArray?: string[]}) {
    if (p.appIdArray != undefined) {
      for (let appId of p.appIdArray) {
        const resp: request.Response = await TwokAccount.logoutDna({twokAccount: this, appId: appId});

        if (resp.status == StatusCodes.OK) {
          this.accessTokenDict[appId] = "";
        } else {
          throw new Error(`2K logout failed.
            status: [${resp.status}]
            body: [${JSON.stringify(resp.body, null, 4)}]
            [${socialApi.maskEmailAddr(this.email)}]
            App ID: [${appId}]`);
        }
      }
    } else {
      const resp: request.Response = await TwokAccount.logoutDna({twokAccount: this});

      if (resp.status == StatusCodes.OK) {
        this.accessTokenDict[config.apps.default.appId] = "";
        this.accessToken = "";
      } else {
        throw new Error(`2K logout failed.
          status: [${resp.status}]
          body: [${JSON.stringify(resp.body, null, 4)}]
          [${socialApi.maskEmailAddr(this.email)}]`);
      }
    }
  }

  static async loginDna(p: {twokAccount: TwokAccountInfo, appId?: string}) {
    if (p.appId == undefined) {
      p.appId = config.apps.default.appId;
    }

    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .post('/auth/tokens')
      .set('Authorization', 'Application ' + p.appId)
      .send({
        locale: 'en-US',
        accountType: 'full',
        credentials: {
          type: 'emailPassword',
          email: p.twokAccount.email,
          password: p.twokAccount.password
        },
      });
    return resp;
  }

  static async logoutDna(p: {twokAccount: TwokAccountInfo, appId?: string}) {
    let token: string;

    if (p.appId == undefined) {
      token = p.twokAccount.accessToken;
    } else {
      token = p.twokAccount.accessTokenDict[p.appId];
    }

    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .post('/auth/tokens/logout')
      .set('Authorization', 'Bearer ' + token);
    return resp;
  }
}

//
export abstract class PlatformAccount extends DnaAccount implements PlatformAccountInfo {
  abstract platformId: string;
  abstract alias: string;
  abstract platformAppId: string;
  abstract loginCred: {type: string, [key: string]: string};
  abstract accessTokenDict: {[key: string]: string};

  //
  async login(p: {appIdArray?: string[]}) {
    if (p.appIdArray != undefined) {
      for (let appId of p.appIdArray) {
        const resp: request.Response = await PlatformAccount.loginPlatform(appId, this.loginCred);

        if (resp.status == StatusCodes.OK) {
          this.accessTokenDict[appId] = resp.body.accessToken;
        } else {
          throw new Error(`Platform login failed.
            status: [${resp.status}]
            body: [${JSON.stringify(resp.body, null, 4)}]
            [${this.platformId}]
            cred: [${JSON.stringify(this.loginCred, null, 4)}]
            App ID: [${appId}]`);
        }
      }
    } else {
      const resp: request.Response = await PlatformAccount.loginPlatform(this.platformAppId, this.loginCred);

      if (resp.status == StatusCodes.OK) {
        this.accessTokenDict[config.apps.default.appId] = resp.body.accessToken;
        this.accessToken = resp.body.accessToken;
      } else {
        throw new Error(`Platform login failed.
          status: [${resp.status}]
          body: [${JSON.stringify(resp.body, null, 4)}]
          [${this.platformId}]
          cred: [${JSON.stringify(this.loginCred, null, 4)}]`);
      }
    }
  }

  //
  async logout(p: {appIdArray?: string[]}) {
    if (p.appIdArray != undefined) {
      for (let appId of p.appIdArray) {
        const resp: request.Response = await PlatformAccount.logoutPlatform({platformAccount: this, appId: appId});

        if ((resp.status == StatusCodes.OK) ||
            ((resp.status == StatusCodes.UNAUTHORIZED) && (resp.body.message == "The access token has been revoked."))) {
          this.accessTokenDict[appId] = "";
        } else {
          throw new Error(`Platform logout failed.
            status: [${resp.status}]
            body: [${JSON.stringify(resp.body, null, 4)}]
            [${this.platformId}]
            cred: [${JSON.stringify(this.loginCred, null, 4)}]
            App ID: [${appId}]`);
        }
      }
    } else {
      const resp: request.Response = await PlatformAccount.logoutPlatform({platformAccount: this});

      if ((resp.status == StatusCodes.OK) ||
          ((resp.status == StatusCodes.UNAUTHORIZED) && (resp.body.message == "The access token has been revoked."))) {
        this.accessTokenDict[config.apps.default.appId] = "";
        this.accessToken = "";
      } else {
        throw new Error(`Platform logout failed.
          status: [${resp.status}]
          body: [${JSON.stringify(resp.body, null, 4)}]
          [${this.platformId}]
          cred: [${JSON.stringify(this.loginCred, null, 4)}]`);
      }
    }
  }

  //
  async linkParent(twokAccount: TwokAccount) {
    const resp: request.Response = await PlatformAccount.linkParent(this, twokAccount);

    if (resp.status != StatusCodes.CREATED) {
      throw new Error(`linkParent failed.
        status: [${resp.status}]
        body: [${JSON.stringify(resp.body, null, 4)}]
        linker: public ID [${this.publicId}] | platform ID [${this.platformId}] | alias [${this.alias}]
        parent: public ID [${twokAccount.publicId}] | email [${twokAccount.email}]`);
    }
  }

  //
  async unlinkParent() {
    const resp: request.Response = await PlatformAccount.unlinkParent(this);

    if (resp.status != StatusCodes.NO_CONTENT) {
      throw new Error(`unlinkParent failed.
        status: [${resp.status}]
        body: [${JSON.stringify(resp.body, null, 4)}]
        unlinker: public ID [${this.publicId}] | platform ID [${this.platformId}] | alias [${this.alias}]`);
    }
  }

  //
  async getLinkedParent() {
    const resp: request.Response = await PlatformAccount.getLinkedParent(this);
    return resp;
  }

  /**
   * static / utility functions
   */

  //
  static async loginPlatform(platformAppId: string, credentials: object) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .post('/auth/tokens')
      .set('Authorization', 'Application ' + platformAppId)
      .send({
        accountType: 'platform',
        instanceId: 't2gptestautomation',
        credentials: credentials,
      });
    return resp;
  }

  //
  static async logoutPlatform(p: {platformAccount: PlatformAccountInfo, appId?: string}) {
    let token: string;

    if (p.appId == undefined) {
      token = p.platformAccount.accessToken;
    } else {
      token = p.platformAccount.accessTokenDict[p.appId];
    }

    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .post('/auth/tokens/logout')
      .set('Authorization', 'Bearer ' + token);
    return resp;
  }

  //
  static async linkParent(platformAccount: PlatformAccountInfo, twokAccount: TwokAccount) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .post('/user/accounts/me/links')
      .set({ Authorization: 'Bearer ' + platformAccount.accessToken })
      .send({
        email: twokAccount.email,
        password: twokAccount.password,
        linkType: "parent",
      });
    return resp;
  }

  // NOTE: When the unlink occurs, this endpoint also revoke the active Spop session of the platform account.
  static async unlinkParent(platformAccount: PlatformAccountInfo) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .delete('/user/accounts/me/links/parent')
      .set({ Authorization: 'Bearer ' + platformAccount.accessToken });
    return resp;
  }

  // NOTE: the doc says this API is used in the context of a platform/device account, although it actually can be used 
  // in the context of a full account (undocumented).
  // This does the same thing as unlinkParent.
  static async unlink(unlinkerAccount: PlatformAccountInfo, unlinkeeAccount: TwokAccountInfo) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .delete(`/user/accounts/me/links/${unlinkeeAccount.publicId}`)
      .set({ Authorization: 'Bearer ' + unlinkerAccount.accessToken });
    return resp;
  }

  //
  static async getLinkedParent(platformAccount: PlatformAccountInfo) {
    const resp: request.Response = await request(config.dnaEndpoints.currEnv.api)
      .get('/user/accounts/me/parent')
      .set({ Authorization: 'Bearer ' + platformAccount.accessToken });
    return resp;
  }
}

//
class SteamAccount extends PlatformAccount implements SteamAccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(steam_id: string, steam_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = steam_id;
    this.aliasInternal = steam_alias;
    this.platformAppId = config.apps.steam.appId;
    this.loginCred = {
      type: 'steam',
      steamUserId: this.platformIdInternal,
      steamProfileName: this.aliasInternal,
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["steamUserId"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["steamProfileName"] = newVal;
  }
}

//
class EpicAccount extends PlatformAccount implements EpicAccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(epic_id: string, epic_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = epic_id;
    this.aliasInternal = epic_alias;
    this.platformAppId = config.apps.epic.appId;
    this.loginCred = {
      type: 'epic',
      epicUserId: this.platformIdInternal,
      epicUserName: this.aliasInternal,
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["epicUserId"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["epicUserName"] = newVal;
  }
}

//
class SwitchAccount extends PlatformAccount implements SwitchAccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(switch_id: string, switch_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = switch_id;
    this.aliasInternal = switch_alias;
    this.platformAppId = config.apps.switch.appId;
    this.loginCred = {
      type: 'nintendo',
      nsaId: this.platformIdInternal,
      nsaNickname: this.aliasInternal,
      nsaToken: ""
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["nsaId"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["nsaNickname"] = newVal;
  }
}

//
class XbxAccount extends PlatformAccount implements XbxAccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(xbx_id: string, xbx_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = xbx_id;
    this.aliasInternal = xbx_alias;
    this.platformAppId = config.apps.xbx.appId;
    this.loginCred = {
      type: 'xbl',
      xblXuid: this.platformIdInternal,
      xblGamertag: this.aliasInternal,
      ageGroup: "5"
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["xblXuid"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["xblGamertag"] = newVal;
  }
}

//
class Xb1Account extends PlatformAccount implements Xb1AccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(xb1_id: string, xb1_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = xb1_id;
    this.aliasInternal = xb1_alias;
    this.platformAppId = config.apps.xb1.appId;
    this.loginCred = {
      type: 'xbl',
      xblXuid: this.platformIdInternal,
      xblGamertag: this.aliasInternal,
      ageGroup: "5"
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["xblXuid"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["xblGamertag"] = newVal;
  }
}

//
class Ps4Account extends PlatformAccount implements Ps4AccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(ps4_id: string, ps4_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = ps4_id;
    this.aliasInternal = ps4_alias;
    this.platformAppId = config.apps.ps4.appId;
    this.loginCred = {
      type: 'psn',
      psnRegion: "scea",
      psnAccountId: this.platformIdInternal,
      psnOnlineId: this.aliasInternal,
      dob: "01/01/1950"
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["psnAccountId"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["psnOnlineId"] = newVal;
  }
}

//
class Ps5Account extends PlatformAccount implements Ps5AccountInfo {
  publicId: string;
  accessToken: string;
  accessTokenDict: {[key: string]: string};
  private platformIdInternal: string;
  private aliasInternal: string;
  platformAppId: string;
  loginCred: {type: string, [key: string]: string};

  constructor(ps5_id: string, ps5_alias: string) {
    super();

    this.publicId = "";
    this.accessToken = "";
    this.accessTokenDict = {};
    this.platformIdInternal = ps5_id;
    this.aliasInternal = ps5_alias;
    this.platformAppId = config.apps.ps5.appId;
    this.loginCred = {
      type: 'psn',
      psnRegion: "scea",
      psnAccountId: this.platformIdInternal,
      psnOnlineId: this.aliasInternal,
      dob: "01/01/1950"
    };
  }

  /**
   * platformId accessors
   */
  //
  get platformId(): string {
    return this.platformIdInternal;
  }

  //
  set platformId(newVal: string) {
    this.platformIdInternal = newVal;
    this.loginCred["psnAccountId"] = newVal;
  }

  /**
   * alias accessors
   */
  //
  get alias(): string {
    return this.aliasInternal;
  }

  //
  set alias(newVal: string) {
    this.aliasInternal = newVal;
    this.loginCred["psnOnlineId"] = newVal;
  }
}


/**
 * Classes processing multiple accounts
 */

//
abstract class DnaAccounts {
  static masterAcct: {[idx: string]: DnaAccount} = {};
  abstract acct: {[idx: string]: DnaAccount};

  //
  protected makeInsAcctDict(acctNum?: number, idxArray?: string[]) {
    if (Object.keys((this.constructor as typeof DnaAccounts).masterAcct).length == 0) {
      (this.constructor as typeof DnaAccounts).initMasterAcctDict();
    }

    if ((typeof acctNum !== 'undefined') &&
      (acctNum > Object.keys((this.constructor as typeof DnaAccounts).masterAcct).length)) {
      throw new Error(`number of accounts requested (${acctNum}) exceeds available account numbers (${Object.keys((this.constructor as typeof DnaAccounts).masterAcct).length})`);
    }

    if ((typeof acctNum !== 'undefined') &&
      (typeof idxArray !== 'undefined') &&
      (acctNum != idxArray.length)) {
      throw new Error(`number of accounts requested (${acctNum}) not equal to index array length (${idxArray.length})`);
    }

    let cnt = -1;
    for (let [idx, dnaAccount] of Object.entries((this.constructor as typeof DnaAccounts).masterAcct).sort()) {
      if (typeof acctNum !== 'undefined') {
        cnt += 1;
        if (cnt == acctNum) {
          break;
        }
      }

      this.acct[(typeof idxArray !== 'undefined') ? idxArray[cnt] : idx] = dnaAccount;
    }
  }

  //
  protected static initMasterAcctDict() {
  }
}

//
abstract class FullAccounts extends DnaAccounts {
}

//
export class TwokAccounts extends FullAccounts {
  static masterAcct: {[idx: string]: TwokAccount} = {};
  acct: {[idx: string]: TwokAccount} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const twokRegex = new RegExp('^TWOK_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = twokRegex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new TwokAccount("", "", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "EMAIL":
            this.masterAcct[key].email = process.env[k] as string;
            break;
          case "PASSWORD":
            this.masterAcct[key].password = process.env[k] as string;
            break;
          case "DISPLAY_NAME":
            this.masterAcct[key].displayName = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }

  //
  async loginAll(p: {appIdArray?: string[]}) {
    let promiseArray:Promise<void>[] = [];

    for (let twokAccount of Object.values(this.acct)) {
      promiseArray.push(twokAccount.login(p));
    }

    await Promise.all(promiseArray);
  }

  //
  async logoutAll(p: {appIdArray?: string[]}) {
    let promiseArray:Promise<void>[] = [];

    for (let twokAccount of Object.values(this.acct)) {
      promiseArray.push(twokAccount.logout(p));
    }

    await Promise.all(promiseArray);
  }
}

//
export abstract class PlatformAccounts extends DnaAccounts {
  abstract acct: {[idx: string]: PlatformAccount};

  //
  async loginAll(p: {appIdArray?: string[]}) {
    let promiseArray:Promise<void>[] = [];

    for (let platformAccount of Object.values(this.acct)) {
      promiseArray.push(platformAccount.login(p));
    }

    await Promise.all(promiseArray);
  }

  //
  async logoutAll() {
    for (let platformAccount of Object.values(this.acct)) {
      await platformAccount.logout({});
    }
  }

  //
  async linkParentAccts(ta: TwokAccounts) {
    if (Object.keys(this.acct).length != Object.keys(ta.acct).length) {
      throw new Error(`object lengths different! [${Object.keys(this.acct).length}] / [${Object.keys(ta.acct).length}] `);
    }

    for (let [idx, platformAccount] of Object.entries(this.acct)) {
      await platformAccount.linkParent(ta.acct[idx]);
    }
  }

  //
  async unlinkParentAccts() {
    for (let platformAccount of Object.values(this.acct)) {
      await platformAccount.unlinkParent();
    }
  }
}

//
export class SteamAccounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: SteamAccount} = {};
  acct: {[idx: string]: SteamAccount} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const steamRegex = new RegExp('^STEAM_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = steamRegex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new SteamAccount("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}

//
export class EpicAccounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: EpicAccount} = {};
  acct: {[idx: string]: EpicAccount} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const epicRegex = new RegExp('^EPIC_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = epicRegex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new EpicAccount("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}

//
export class SwitchAccounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: SwitchAccount} = {};
  acct: {[idx: string]: SwitchAccount} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const switchRegex = new RegExp('^SWITCH_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = switchRegex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new SwitchAccount("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}

//
export class XbxAccounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: XbxAccount} = {};
  acct: {[idx: string]: XbxAccount} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const xbxRegex = new RegExp('^XBX_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = xbxRegex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new XbxAccount("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}

//
export class Xb1Accounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: Xb1Account} = {};
  acct: {[idx: string]: Xb1Account} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const xb1Regex = new RegExp('^XB1_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = xb1Regex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new Xb1Account("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}

//
export class Ps4Accounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: Ps4Account} = {};
  acct: {[idx: string]: Ps4Account} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const ps4Regex = new RegExp('^PS4_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = ps4Regex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new Ps4Account("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}

//
export class Ps5Accounts extends PlatformAccounts {
  static masterAcct: {[idx: string]: Ps5Account} = {};
  acct: {[idx: string]: Ps5Account} = {};

  //
  constructor(acctNum?: number, idxArray?: string[]) {
    super();
    this.makeInsAcctDict(acctNum, idxArray);
  }

  //
  protected static initMasterAcctDict() {
    const ps5Regex = new RegExp('^PS5_(.*?)_(.*)');
    for (let k of Object.keys(process.env)) {
      let match = ps5Regex.exec(k);

      if (match != null) {
        let key = match[1];
        let propName = match[2];

        if (!(key in this.masterAcct)) {
          this.masterAcct[key] = new Ps5Account("", "");
        }

        switch (propName) {
          case "2K_PUBLIC_ID":
            this.masterAcct[key].publicId = process.env[k] as string;
            break;
          case "PLATFORM_ID":
            this.masterAcct[key].platformId = process.env[k] as string;
            break;
          case "ALIAS":
            this.masterAcct[key].alias = process.env[k] as string;
            break;
          default:
            console.log(`unexpected propName ${propName}`);
            break;
        }
      }
    }
  }
}