package store

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func TestMakeFriend(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	// id := identity.NewMockIdentityServiceInterface(mock.ctrl)
	pendingRequest := dynamodb.GetItemOutput{
		Item: map[string]types.AttributeValue{
			"userid":    &types.AttributeValueMemberS{Value: "b287e655461f4b3085c8f244e394ff7e"},
			"friendid":  &types.AttributeValueMemberS{Value: "e12c3df480984141b2f385646b2024fa"},
			"status":    &types.AttributeValueMemberS{Value: "pending"},
			"timestamp": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", time.Now().Unix())},
			"message":   &types.AttributeValueMemberS{Value: "hello there!"},
			"read":      &types.AttributeValueMemberBOOL{Value: false},
			"invitee":   &types.AttributeValueMemberS{Value: "e12c3df480984141b2f385646b2024fa"},
		},
	}

	g.Describe("MakeFriend", func() {
		g.It("should succeed make pending", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any())
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)
			mock.ddb.EXPECT().BatchWriteItem(ctx, gomock.Any())

			status, err := mock.db.MakeFriend(ctx, "b287e655461f4b3085c8f244e394ff7e", "e12c3df480984141b2f385646b2024fa", "hello there!", true, 0)
			g.Assert(err).IsNil()
			g.Assert(status).Equal("pending")
		})
		g.It("should succeed make friend", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(&pendingRequest, nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)

			mock.ddb.EXPECT().BatchWriteItem(ctx, gomock.Any())
			status, err := mock.db.MakeFriend(ctx, "e12c3df480984141b2f385646b2024fa", "b287e655461f4b3085c8f244e394ff7e", "", true, 0)
			g.Assert(err).IsNil()
			g.Assert(status).Equal("friend")

		})
		g.It("should succeed make unfriend", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)
			mock.ddb.EXPECT().DeleteItem(ctx, gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)
			mock.ddb.EXPECT().DeleteItem(ctx, gomock.Any()).Return(nil, nil)
			err := mock.db.MakeUnfriend(ctx, "e12c3df480984141b2f385646b2024fa", "b287e655461f4b3085c8f244e394ff7e")
			g.Assert(err).IsNil()
		})

		g.It("should fail with bad userids", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)

			status, err := mock.db.MakeFriend(ctx, "e12c3df480984141b2f385646b2024fa", "", "", true, 0)
			g.Assert(err).IsNotNil()
			g.Assert(status).Equal("")

			err = mock.db.MakeUnfriend(ctx, "e12c3df480984141b2f385646b2024fa", "")
			g.Assert(err).IsNotNil()
		})
	})
}

func TestGetFriendsCount(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	// id := identity.NewMockIdentityServiceInterface(mock.ctrl)

	g.Describe("GetFriendsCount", func() {
		g.It("should return 1", func() {

			queryOutput := dynamodb.QueryOutput{
				Items: []map[string]types.AttributeValue{
					{
						"pk":     &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
						"sk":     &types.AttributeValueMemberS{Value: "friend#e12c3df480984141b2f385646b2024fa"},
						"status": &types.AttributeValueMemberS{Value: "friend"},
					},
				},
				Count: 1,
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(ctx)

			mock.ddb.EXPECT().Query(ctx, gomock.Any()).Return(&queryOutput, nil)
			count, err := mock.db.GetFriendsCount(ctx, "user1")
			g.Assert(err).IsNil()
			g.Assert(count).IsNotNil()
			g.Assert(*count).Equal(int32(1))
		})
	})
}

func TestPatchDynamoUserItem(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("patchDynamoUserItem", func() {
		g.It("should succeed", func() {
			p := map[string]types.AttributeValue{
				"foo": &types.AttributeValueMemberS{Value: "bar"},
			}
			user := map[string]types.AttributeValue{
				"presence": &types.AttributeValueMemberM{Value: p},
			}
			result := patchDynamoUserItem(user)
			value, ok := result["presence"]
			g.Assert(ok).IsFalse()
			g.Assert(value).IsNil()
		})
	})
}

func TestGetFriends(t *testing.T) {
	g := goblin.Goblin(t)
	ddb := NewDynamoDB(context.TODO(), cfg)

	userId := utils.GenerateRandomDNAID()
	friend1 := apipub.FriendResponse{
		Userid:   userId,
		Friendid: "1",
		Invitee:  "1",
		Name:     aws.String("friend1"),
		Status:   "friend",
	}
	friend2 := apipub.FriendResponse{
		Userid:   userId,
		Friendid: "3",
		Invitee:  "3",
		Name:     aws.String("friend2"),
		Status:   "friend",
	}
	pending1 := apipub.FriendResponse{
		Userid:   userId,
		Friendid: "2",
		Invitee:  "2",
		Name:     aws.String("pending1"),
		Status:   "pending",
	}
	pending2 := apipub.FriendResponse{
		Userid:   userId,
		Friendid: "4",
		Invitee:  "4",
		Name:     aws.String("pending2"),
		Status:   "pending",
	}
	g.Describe("GetFriends", func() {
		createRequest := &dynamodb.CreateTableInput{
			TableName: &cfg.ProfileTable,
			AttributeDefinitions: []types.AttributeDefinition{
				{AttributeName: aws.String("pk"), AttributeType: "S"},
				{AttributeName: aws.String("sk"), AttributeType: "S"},
			},
			KeySchema: []types.KeySchemaElement{
				{AttributeName: aws.String("pk"), KeyType: "HASH"},
				{AttributeName: aws.String("sk"), KeyType: "RANGE"},
			},
			ProvisionedThroughput: &types.ProvisionedThroughput{
				ReadCapacityUnits:  aws.Int64(10),
				WriteCapacityUnits: aws.Int64(10),
			},
		}
		deleteRequest := &dynamodb.DeleteTableInput{
			TableName: &cfg.ProfileTable,
		}
		g.Before(func() {
			(*ddb).DeleteTable(context.TODO(), deleteRequest)
			(*ddb).CreateTable(context.TODO(), createRequest)
			ds.PutItemInProfileTable(ctx, &friend1)
			ds.PutItemInProfileTable(ctx, &friend2)
			ds.PutItemInProfileTable(ctx, &pending1)
			ds.PutItemInProfileTable(ctx, &pending2)
		})
		g.After(func() {
			(*ddb).DeleteTable(context.TODO(), deleteRequest)
		})
		g.It("GetFullFriendList - get a non-existing friend", func() {
			result, err := ds.GetFullFriendList(ctx, utils.GenerateRandomDNAID())
			g.Assert(err).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFullFriendList - get a full friend list", func() {
			result, err := ds.GetFullFriendList(ctx, userId)

			g.Assert(err).IsNil()
			g.Assert(len(*result)).Equal(4)
		})
		g.It("GetFriends - get all confirmed friends", func() {
			status := apipub.Friend
			result, next, _ := ds.GetFriends(ctx, userId, &status, cfg.MaxFriends, nil)
			g.Assert(next).IsNil()
			g.Assert(len(*result)).Equal(2)

			friend1 := (*result)[0]
			friend2 := (*result)[1]
			g.Assert(friend1.Status).Equal(apipub.Friend)
			g.Assert(friend2.Status).Equal(apipub.Friend)
		})
		g.It("GetFriends - get two confirmed friends with limit of 2", func() {
			status := apipub.Friend
			result, next, _ := ds.GetFriends(ctx, userId, &status, 2, nil)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(2)

			friend1 := (*result)[0]
			friend2 := (*result)[1]
			g.Assert(friend1.Status).Equal(apipub.Friend)
			g.Assert(friend2.Status).Equal(apipub.Friend)
		})
		g.It("GetFriends - get one confirmed friend", func() {
			status := apipub.Friend
			result, next, _ := ds.GetFriends(ctx, userId, &status, 1, nil)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(1)

			friend1 := (*result)[0]
			g.Assert(friend1.Status).Equal(apipub.Friend)
			g.Assert(*friend1.Name).Equal("friend1")
			g.Assert(*next).Equal("1")
		})
		g.It("GetFriends - get all pending friends", func() {
			status := apipub.Pending
			result, next, _ := ds.GetFriends(ctx, userId, &status, cfg.MaxFriends, nil)
			g.Assert(next).IsNil()
			g.Assert(len(*result)).Equal(2)

			friend1 := (*result)[0]
			friend2 := (*result)[1]
			g.Assert(friend1.Status).Equal(apipub.Pending)
			g.Assert(friend2.Status).Equal(apipub.Pending)
		})
		g.It("GetFriends - get one pending friend", func() {
			status := apipub.Pending
			result, next, _ := ds.GetFriends(ctx, userId, &status, 1, nil)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(1)

			friend1 := (*result)[0]
			g.Assert(friend1.Status).Equal(apipub.Pending)
			g.Assert(*friend1.Name).Equal("pending1")
			g.Assert(*next).Equal("2")
		})
		g.It("GetFriends - get first two friends", func() {
			result, next, _ := ds.GetFriends(ctx, userId, nil, 2, nil)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(2)
		})
		g.It("GetFriends - continuously get all friend with limit of 1", func() {
			result, next, _ := ds.GetFriends(ctx, userId, nil, 1, nil)
			g.Assert(*next).Equal("1")
			g.Assert(len(*result)).Equal(1)

			friend := (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Friend)
			g.Assert(*friend.Name).Equal("friend1")

			result, next, _ = ds.GetFriends(ctx, userId, nil, 1, next)
			g.Assert(*next).Equal("2")
			g.Assert(len(*result)).Equal(1)

			friend = (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Pending)
			g.Assert(*friend.Name).Equal("pending1")

			result, next, _ = ds.GetFriends(ctx, userId, nil, 1, next)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(1)

			friend = (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Friend)
			g.Assert(*friend.Name).Equal("friend2")

			result, next, _ = ds.GetFriends(ctx, userId, nil, 1, next)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(1)

			friend = (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Pending)
			g.Assert(*friend.Name).Equal("pending2")

			result, next, _ = ds.GetFriends(ctx, userId, nil, 1, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFriends - continuously get all friend with limit of 2", func() {
			result, next, _ := ds.GetFriends(ctx, userId, nil, 2, nil)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(2)

			result, next, _ = ds.GetFriends(ctx, userId, nil, 2, next)
			g.Assert(next).IsNotNil()
			g.Assert(len(*result)).Equal(2)

			result, next, _ = ds.GetFriends(ctx, userId, nil, 2, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFriends - continuously get all confirmed friends with limit of 1", func() {
			status := apipub.Friend
			result, next, _ := ds.GetFriends(ctx, userId, &status, 1, nil)
			g.Assert(*next).Equal("1")
			g.Assert(len(*result)).Equal(1)
			friend := (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Friend)

			result, next, _ = ds.GetFriends(ctx, userId, &status, 1, next)
			g.Assert(*next).Equal("3")
			g.Assert(len(*result)).Equal(1)
			friend = (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Friend)

			result, next, _ = ds.GetFriends(ctx, userId, &status, 1, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFriends - continuously get all confirmed friends with limit of 2", func() {
			status := apipub.Friend
			result, next, _ := ds.GetFriends(ctx, userId, &status, 2, nil)
			g.Assert(*next).Equal("3")
			g.Assert(len(*result)).Equal(2)
			friend1 := (*result)[0]
			friend2 := (*result)[1]
			g.Assert(friend1.Status).Equal(apipub.Friend)
			g.Assert(friend2.Status).Equal(apipub.Friend)

			result, next, _ = ds.GetFriends(ctx, userId, &status, 2, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFriends - continuously get all pending friends with limit of 1", func() {
			status := apipub.Pending
			result, next, _ := ds.GetFriends(ctx, userId, &status, 1, nil)
			g.Assert(*next).Equal("2")
			g.Assert(len(*result)).Equal(1)
			friend := (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Pending)

			result, next, _ = ds.GetFriends(ctx, userId, &status, 1, next)
			g.Assert(*next).Equal("4")
			g.Assert(len(*result)).Equal(1)
			friend = (*result)[0]
			g.Assert(friend.Status).Equal(apipub.Pending)

			result, next, _ = ds.GetFriends(ctx, userId, &status, 1, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFriends - continuously get all pending friends with limit of 2", func() {
			status := apipub.Pending
			result, next, _ := ds.GetFriends(ctx, userId, &status, 2, nil)
			g.Assert(*next).Equal("4")
			g.Assert(len(*result)).Equal(2)
			friend1 := (*result)[0]
			friend2 := (*result)[1]
			g.Assert(friend1.Status).Equal(apipub.Pending)
			g.Assert(friend2.Status).Equal(apipub.Pending)

			result, next, _ = ds.GetFriends(ctx, userId, &status, 2, next)
			g.Assert(next).IsNil()
			g.Assert(result).IsNil()
		})
		g.It("GetFriends - get all confirmed friends starting from the first pending friend", func() {
			status := apipub.Friend
			result, next, _ := ds.GetFriends(ctx, userId, &status, ds.cfg.ListFriendsLimit, aws.String("1"))
			g.Assert(next).IsNil()
			g.Assert(len(*result)).Equal(1)
			friend1 := (*result)[0]
			g.Assert(friend1.Status).Equal(apipub.Friend)
			g.Assert(*friend1.Name).Equal("friend2")
		})
	})
}
