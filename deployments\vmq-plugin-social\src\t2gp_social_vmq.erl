-module(t2gp_social_vmq).

-include_lib("vernemq_dev/include/vernemq_dev.hrl").
-include_lib("t2gp_social.hrl").

-export([
    publish/4,
    subscribe/2,
    unsubscribe/2,
    unsubscribe_all/1,
    list_subscriptions/1
]).

-export([
    reduce_results/1
]).

%% pulled from vmq_types_common.hrl
-type routing_key() :: [binary()].
-type msg_ref() :: binary().

-type msg_expiry_ts() :: {expire_after, non_neg_integer()} | {non_neg_integer(), non_neg_integer()}.

%% default timeout for erpc calls
-define(TIMEOUT, 60000).

-record(vmq_msg, {
    % OTP-12719
    msg_ref :: msg_ref() | 'undefined',
    routing_key :: routing_key() | 'undefined',
    payload :: payload() | 'undefined',
    retain = false :: flag(),
    dup = false :: flag(),
    qos :: qos(),
    mountpoint :: mountpoint(),
    persisted = false :: flag(),
    sg_policy = prefer_local :: shared_sub_policy(),
    properties = #{} :: properties(),
    expiry_ts :: undefined | msg_expiry_ts()
}).

-spec publish(username() | subscriber_id(), topic(), payload(), map()) -> ok | {error, term()}.
publish(UserId, Topic, Payload, Opts) when is_binary(UserId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => UserId, topic => lists:join(<<"/">>, Topic)}),
        case t2gp_social_db:get_subscriber_id(UserId) of
            undefined ->
                ok;
            SubscriberIdSet ->
                SubscriberIds = sets:to_list(SubscriberIdSet),
                Results = [publish_subid(SubId, Topic, Payload, Opts) || SubId <- SubscriberIds],
                reduce_results(Results)
        end,
        ok
    end);
publish(SubscriberId, Topic, Payload, Opts) when is_tuple(SubscriberId) ->
    publish_subid(SubscriberId, Topic, Payload, Opts).

-spec publish_subid(subscriber_id(), topic(), payload(), map()) -> ok | {error, term()}.
publish_subid({MountPoint, ClientId} = SubscriberId, Topic, Payload, _Opts) when is_tuple(SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId, topic => lists:join(<<"/">>, Topic)}),
        Msg = #vmq_msg{
            routing_key = Topic,
            mountpoint = MountPoint,
            payload = Payload,
            msg_ref = vmq_mqtt_fsm_util:msg_ref(),
            qos = 1,
            dup = false,
            retain = false,
            sg_policy = prefer_local
        },
        AllowPublishNetsplit = vmq_config:get_env(allow_publish_during_netsplit, false),
        case vmq_reg:publish(AllowPublishNetsplit, vmq_reg_trie, ClientId, Msg) of
            {ok, _} ->
                ok;
            {error, _} ->
                lager:error("can't subscribe to topic ~p due to unknown", [Topic]),
                {error, unknown}
        end
    end).

%% call sub/unsub erpc to the correct node
-spec erpc(node(), atom(), atom(), list(), non_neg_integer()) -> ok | {error, term()}.
erpc(Node, Module, Func, Args, Timeout) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{node => Node, module => Module, func => Func, args => Args, timeout => Timeout}),
        Local = node() == Node,
        lager:info("~p:~p(~p) on ~p with ~p", [Module, Func, Args, Node, Timeout]),
        Result =
            if
                Local == true ->
                    %% call locally
                    erlang:apply(Module, Func, Args);
                Local == false ->
                    %% call remotely
                    SleepBetweenRetries = 5000,
                    T0 = erlang:monotonic_time(),
                    try
                        erpc:call(Node, Module, Func, Args, Timeout)
                    catch
                        error:{erpc, noconnection} when
                            Timeout > SleepBetweenRetries
                        ->
                            lager:info("no connection to node `~ps`; retrying in ~p milliseconds", [Node, SleepBetweenRetries]),
                            timer:sleep(SleepBetweenRetries),
                            T1 = erlang:monotonic_time(),
                            TDiff = erlang:convert_time_unit(T1 - T0, native, millisecond),
                            Remaining = Timeout - TDiff,
                            Timeout1 = erlang:max(Remaining, 0),
                            case Timeout1 of
                                0 -> {error, {erpc, noconnection}};
                                _ -> erpc(Node, Module, Func, Args, Timeout1)
                            end
                    end
            end,
        case Result of
            ok ->
                lager:info("erpc ~p:~p(~p) on ~p local=~p succeeded", [Module, Func, Args, Node, Local]),
                ok;
            {ok, _} ->
                lager:info("erpc ~p:~p(~p) on ~p local=~p succeeded", [Module, Func, Args, Node, Local]),
                ok;
            {error, Reason} ->
                lager:info("erpc ~p:~p(~p) on ~p local=~p failed err=~p", [Module, Func, Args, Node, Local, Reason]),
                {error, Reason}
        end
    end).

-spec subscribe(username() | subscriber_id(), topic()) -> ok | {error, atom()}.
subscribe(UserId, Topic) when is_binary(UserId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => UserId, topic => lists:join(<<"/">>, Topic)}),
        case t2gp_social_db:list_subscriber_id(UserId) of
            undefined ->
                ok;
            SubscriberIds ->
                Results = [subscribe_subid(SubId, Topic) || SubId <- SubscriberIds],
                reduce_results(Results)
        end
    end);
subscribe(SubscriberId, Topic) when is_tuple(SubscriberId) ->
    subscribe_subid(SubscriberId, Topic).

-spec subscribe_subid(subscriber_id(), topic()) -> ok | {error, atom()}.
subscribe_subid({_, ClientId} = SubscriberId, Topic) when is_tuple(SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId, topic => lists:join(<<"/">>, Topic)}),
        case vmq_subscriber_db:read(SubscriberId) of
            undefined ->
                ok;
            NodeSubs ->
                AllowSubscribeNetsplit = vmq_config:get_env(allow_subscribe_during_netsplit, false),
                Results = [erpc(Node, vmq_reg, subscribe, [AllowSubscribeNetsplit, SubscriberId, [{Topic, 0}]], ?TIMEOUT) || {Node, _, _} <- NodeSubs],
                reduce_results(Results)
        end
    end).

-spec unsubscribe(username() | subscriber_id(), topic()) -> ok | {error, atom()}.
unsubscribe(UserId, Topic) when is_binary(UserId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => UserId, topic => lists:join(<<"/">>, Topic)}),
        case t2gp_social_db:list_subscriber_id(UserId) of
            [] ->
                ok;
            SubscriberIds ->
                Results = [unsubscribe_subid(SubId, Topic) || SubId <- SubscriberIds],
                reduce_results(Results)
        end
    end);
unsubscribe(SubscriberId, Topic) when is_tuple(SubscriberId) ->
    unsubscribe_subid(SubscriberId, Topic).

-spec unsubscribe_subid(subscriber_id(), topic()) -> ok | {error, atom()}.
unsubscribe_subid({_, ClientId} = SubscriberId, Topic) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId, topic => lists:join(<<"/">>, Topic)}),
        case vmq_subscriber_db:read(SubscriberId) of
            undefined ->
                ok;
            NodeSubs ->
                AllowUnsubscribeNetsplit = vmq_config:get_env(allow_unsubscribe_during_netsplit, false),
                Results = [erpc(Node, vmq_reg, unsubscribe, [AllowUnsubscribeNetsplit, SubscriberId, [Topic]], ?TIMEOUT) || {Node, _, _} <- NodeSubs],
                % in order to match the format of the non-rpc call, we must condense down to a single OK or error.
                reduce_results(Results)
        end
    end).

-spec unsubscribe_all(subscriber_id()) -> ok.
unsubscribe_all({_, ClientId} = SubscriberId) when is_tuple(SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        Subscriptions = list_subscriptions(SubscriberId),
        lager:info("unsubscribe_all subscriptions ~p", [Subscriptions]),
        Result = [unsubscribe(SubscriberId, Topic) || {_ClientId, Topic} <- Subscriptions],
        lager:info("unsubscribe_all result ~p", [Result]),
        ok
    end).

-spec list_subscriptions(username() | subscriber_id()) -> [{subscriber_id(), topic()}].
list_subscriptions(UserId) when is_binary(UserId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{userid => UserId}),
        case t2gp_social_db:list_subscriber_id(UserId) of
            [] ->
                [];
            SubscriberIds ->
                Topics = [list_subscriptions_subid(SubId) || SubId <- SubscriberIds],
                FlatTopics = lists:foldl(fun(X, Acc) -> X ++ Acc end, [], Topics),
                FlatTopics
        end
    end);
list_subscriptions(SubscriberId) when is_tuple(SubscriberId) ->
    list_subscriptions_subid(SubscriberId).

-spec list_subscriptions_subid(subscriber_id()) -> [{subscriber_id(), topic()}].
list_subscriptions_subid({_MountPoint, ClientId} = SubscriberId) when is_tuple(SubscriberId) ->
    t2gp_social_apm:span(?APM_NAME, ?curr_fname(), fun() ->
        t2gp_social_apm:tags(#{clientid => ClientId}),
        % Subscriptions = [ [{node, true, [{topic, 0}] },..], ..]
        Subscriptions = vmq_reg:subscriptions_for_subscriber_id(SubscriberId),
        Topics = [element(3, H) || H <- Subscriptions],
        FlatTopics = lists:foldl(fun(X, Acc) -> X ++ Acc end, [], Topics),
        [{ClientId, element(1, H)} || H <- FlatTopics]
    end).

%% utilities

% -spec wait_til_ready() -> ok.
% wait_til_ready() ->
%     case catch vmq_cluster:if_ready(fun() -> true end, []) of
%         true ->
%             ok;
%         _ ->
%             timer:sleep(100),
%             wait_til_ready()
%     end.

-spec reduce_results([ok | {error, any()}]) -> ok | {error, any()}.
reduce_results(Results) ->
    IsNotOkFunc = fun(X) ->
        case X of
            ok -> false;
            {ok, _} -> false;
            _ -> true
        end
    end,
    OksRemoved = lists:filter(IsNotOkFunc, Results),
    case OksRemoved of
        [] ->
            ok;
        [H | _] ->
            H
    end.
