// Package config implements loading .env and parsing env variables to config structure
package config

import (
	"encoding/base64"
	"flag"
	"fmt"
	"os"
	"strings"

	"github.com/joho/godotenv"
	"github.com/peterbourgon/ff/v3"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// StringSlice type define for a string array.
type StringSlice []string

// Set sets a string into the string array
func (ss *StringSlice) Set(s string) error {
	*ss = append(*ss, s)
	return nil
}

// String converts the StringSlice to a comma delimited string.
func (ss *StringSlice) String() string {
	if ss != nil && len(*ss) <= 0 {
		return "..."
	}
	return strings.Join(*ss, ", ")
}

// VersionStruct version structure
type VersionStruct struct {
	Version   string `json:"version"`
	GitHash   string `json:"gitHash"`
	BuildDate string `json:"buildDate"`
}

// Config configuration structure
type Config struct {
	// Server settings
	ServerMode          string        `json:"serverMode"`
	ServiceName         string        `json:"serviceName"`
	AllowAlgNone        bool          `json:"allowAlgNone"`
	CorsAllowedOrigins  StringSlice   `json:"corsAllowedOrigins"`
	DebugMode           bool          `json:"debugMode"`
	DnsCaching          bool          `json:"dnsCaching"`
	Hostname            string        `json:"hostname"`
	PrivatePort         int           `json:"privatePort"`
	PublicPort          int           `json:"publicPort"`
	MaxRequestBodyBytes int64         `json:"maxRequestBodyBytes"`
	SkipAuth            bool          `json:"skipAuth"`
	TSPort              int           `json:"tsPort"`
	Version             VersionStruct `json:"version"`

	// Service URLs
	HttpURL           string `json:"httpUrl"`
	MqttURL           string `json:"mqttUrl"`
	VoipPrivateApiURL string `json:"voipPrivateApiUrl"`
	ElasticacheUrl    string `json:"elasticacheUrl"`

	// Metrics
	DatadogAPMEnabled      bool   `json:"datadogAPMEnabled"`
	DatadogProfilerEnabled bool   `json:"datadogProfilerEnabled"`
	MetricsEnabled         bool   `json:"metricsEnabled"`
	RequestRecorderEnabled bool   `json:"requestRecorderEnabled"`
	RequestRecorderStream  string `json:"requestRecorderStream"`

	// DNA
	AppBasicAuth string `json:"appBasicAuth"`
	AppID        string `json:"appId"`
	AppSecret    string `json:"appSecret"`
	DiscoveryURL string `json:"discoveryUrl"`
	SsoURL       string `json:"ssoUrl"`  // retrieved from DiscoveryUrl
	SsoHost      string `json:"ssoHost"` // retrieved from DiscoveryUrl

	// Identity
	RSConfigURL string `json:"rsConfigUrl"`

	// DynamoDB
	AwsRegion         string `json:"AwsRegion"`
	DynamoDBURL       string `json:"dynamoDbUrl"`
	ProfileTable      string `json:"socialTable"`
	ChatMessagesTable string `json:"chatMessagesTable"`

	// S3
	S3BucketRegion     string `json:"s3BucketRegion"`
	SocialConfigBucket string `json:"socialConfigBucket"`

	// Data Firehose
	ShouldFirehose     bool   `json:"shouldFirehose"`
	FirehoseRegion     string `json:"firehoseRegion"`
	FirehoseStreamName string `json:"firehoseStreamName"`

	// Kinesis Datastream
	ShouldKinesis    bool   `json:"shouldKinesis"`
	KinesisRegion    string `json:"kinesisRegion"`
	KinesisStreamArn string `json:"kinesisStreamArn"`

	// VMQ
	VMQApiURL        string `json:"vmqApiUrl"`
	VMQApiKey        string `json:"vmqApiKey"`
	VMQHttpPubApiURL string `json:"VMQHttpPubApiUrl"`
	VMQHttpPubApiKey string `json:"vmqHttpPubApiKey"`
	VMQUseBinaryPub  bool   `json:"vmqUseBinaryPub"`
	VMQHttpPubUser   string `json:"vmqHttpPubUser"`

	// Social Settings
	FixPendingFriends      bool `json:"fixPendingFriends"`
	ListFriendsLimit       int  `json:"listFriendsLimit"`
	MaxBlocks              int  `json:"maxBlocks"`
	MaxControlMessgeLength int  `json:"maxControlMessage"`
	MaxFriends             int  `json:"maxFriends"`
	MaxGroups              int  `json:"maxGroups"`
	MaxGroupSize           int  `json:"maxGroupSize"`
	MaxChatMessageLength   int  `json:"maxMessageLength"`
	MaxMetaSize            int  `json:"maxMetaSize"`
	PresenceTimeoutMin     int  `json:"presenceTimeoutMin"`
	PresenceTimeoutMax     int  `json:"presenceTimeoutMax"`
	PresenceGameDataMax    int  `json:"presenceGameDataMax"`
	ShouldValidateYaml     bool `json:"shouldValidateYaml"`

	// Redis
	ElasticacheService       string `json:"elasticacheService"`
	RedisExpirationBatchSize int    `json:"redisExpirationBatchSize"`
	RedisExpirationHexMax    int    `json:"redisExpirationHexMax"`
	RedisExpirationHexChars  int    `json:"redisExpirationHexChars"`
	RedisLockDuration        int    `json:"redislockDuration"`
	RedisLeaderLockKey       string `json:"redislockKey"`
	RedisLockRefreshInterval int    `json:"redislockRefreshInterval"`
	RedisService             string `json:"redisService"`
	RedisTransactionMaxRetry int    `json:"redisTransactionMaxRetry"`

	// Redis TTL
	TtlBlocklist         int `json:"ttlBlocklist"`
	TtlChatMessage       int `json:"ttlChatMessage"`
	TtlDefault           int `json:"ttlDefault"`
	TtlFriend            int `json:"ttlFriend"`
	TtlGroup             int `json:"ttlGroup"`
	TtlIndex             int `json:"ttlIndex"`
	TtlMembership        int `json:"ttlMembership"`
	TtlPlayedDefault     int `json:"ttlPlayedDefault"`
	TtlPlayedMax         int `json:"ttlPlayedMax"`
	TtlPresence          int `json:"ttlPresence"`
	TtlProfile           int `json:"ttlProfile"`
	TtlFirstPartyToken   int `json:"ttlFirstPartyToken"`
	TtlFirstPartyRefresh int `json:"ttlFirstPartyRefresh"`

	// Abuse Report
	// S3 bucket uses envvar: SocialConfigBucket
	ReportSNSTopicArn string `json:"reportSNSTopicArn"`

	// Session
	// Remove it once we have a production customer
	IsSessionFeatureEnabled bool `json:"isSessionFeatureEnabled"`

	// AWS Retry
	AwsRequestMaxRetryAttempt int `json:"awsRequestMaxRetryAttempt"`
}

type SessionPolicy string

const (
	SingleSession   SessionPolicy = "Single"
	MultipleSession SessionPolicy = "Multiple"
)

type SessionPolicyConfig struct {
	Description   string        `json:"description"`
	SessionPolicy SessionPolicy `json:"sessionPolicy"`
}

// NewConfig create new config object
func NewConfig() *Config {
	version := VersionStruct{
		Version:   GetEnv("VERSION", "dev"),
		GitHash:   GetEnv("GIT_HASH", "dev"),
		BuildDate: GetEnv("BUILD_DATE", "dev"),
	}
	return &Config{
		AllowAlgNone:              false,                                                              // AllowAlgNone
		AppBasicAuth:              "",                                                                 // AppBasicAuth
		AppID:                     "",                                                                 // AppID
		AppSecret:                 "",                                                                 // AppSecret
		AwsRegion:                 "us-east-1",                                                        // AwsRegion
		ChatMessagesTable:         "",                                                                 // ChatMessagesTable
		CorsAllowedOrigins:        StringSlice{},                                                      // CorsAllowrdOrigins
		DatadogAPMEnabled:         true,                                                               // DatadogAPMEnabled
		DatadogProfilerEnabled:    false,                                                              // DatadogProfilerEnabled
		DebugMode:                 false,                                                              // DebugMode
		DiscoveryURL:              "https://discovery.api.2kcoretech.online",                          // DiscoveryURL
		DnsCaching:                true,                                                               // DnsCaching
		DynamoDBURL:               "",                                                                 // Dynamodburl
		ElasticacheUrl:            "localhost:6379",                                                   // ElasticacheUrl
		ElasticacheService:        "social-elasticache-service",                                       // ElasticacheService
		FixPendingFriends:         false,                                                              // Flag if we want to fix pending friends in dynamo
		FirehoseRegion:            "us-east-1",                                                        // Region for Data Firehose
		FirehoseStreamName:        "t2gp-social-telemetry-s3",                                         // Firehose stream name
		HttpURL:                   "http://localhost:8000",                                            // HttpURL external REST API url
		Hostname:                  "hostname",                                                         // Hostname - API Pod name.  Need unique strings per pod for redis consumer group name.
		KinesisRegion:             "us-east-1",                                                        // Region for Data Kinesis
		KinesisStreamArn:          "arn:aws:kinesis:us-east-1:354767525209:stream/t2gp-social-events", // Kinesis stream ARN
		ListFriendsLimit:          100,                                                                // Max friend page size
		MaxControlMessgeLength:    5120,                                                               // Max lenght of control message
		MaxBlocks:                 100,                                                                // Max users in a blocklist
		MaxFriends:                1000,                                                               // MaxFriends
		MaxGroups:                 50,                                                                 // MaxGroups
		MaxGroupSize:              100,                                                                // MaxGroupSize
		MaxChatMessageLength:      1024,                                                               // MaxMessageLength
		MaxMetaSize:               1024,                                                               // MaxMetaSize
		MaxRequestBodyBytes:       128 * 1024,                                                         // Max Request Body size in bytes
		MetricsEnabled:            true,                                                               // MetricsEnabled
		RequestRecorderEnabled:    false,                                                              // RequestRecorder
		RequestRecorderStream:     "",                                                                 // RequestRecorderStream
		MqttURL:                   "ws://localhost:8080/mqtt",                                         // MqttURL external MQTT url
		PresenceGameDataMax:       1024,                                                               // Max length presence game data
		PresenceTimeoutMax:        1800,                                                               // Max length auto presence group kick
		PresenceTimeoutMin:        35,                                                                 // Min length auto presence group kick
		PrivatePort:               8001,                                                               // PrivatePort
		ProfileTable:              "social-service-non-production-profile",                            // SocialTable
		PublicPort:                8000,                                                               // PublicPort
		RedisExpirationBatchSize:  5000,                                                               // RedisExpirationBatchSize Chose 5K to support horizon
		RedisExpirationHexMax:     0xf,                                                                // RedisExpirationHexMax - determines how many hash slots to use for Expirations.  MUST BE '0xF' or '0XFF' etc.
		RedisExpirationHexChars:   1,                                                                  // RedisExpirationHexChars - always change this if HEXMAX changes number of hex characters.
		RedisLockDuration:         60,                                                                 // Redis lock duration
		RedisLeaderLockKey:        "social-server-leader-lock",                                        // Redis lock key name
		RedisLockRefreshInterval:  45,                                                                 // Redis lock refresh interval
		RedisService:              "social-service-redis",                                             // RedisService
		RedisTransactionMaxRetry:  3,                                                                  // RedisTransactionMaxRetry
		RSConfigURL:               "s3://t2gp-pd-store-api/rs_auth_env/config.gz",                     // Rockstar S3 config
		S3BucketRegion:            "us-east-1",                                                        // S3Bucket Region
		SocialConfigBucket:        "t2gp-social",                                                      // SocialConfigBucket
		ServerMode:                "social",                                                           // ServerMode
		ServiceName:               "social-service-api",                                               // ServiceName
		ShouldValidateYaml:        true,                                                               // ShouldValidateYaml
		ShouldFirehose:            false,                                                              // Should send to firehose
		ShouldKinesis:             true,                                                               // Should send to kinesis
		SkipAuth:                  false,                                                              // SkipAuth
		SsoHost:                   "",                                                                 // SsoHost
		SsoURL:                    "",                                                                 // SsoURL
		TSPort:                    8005,                                                               // Trusted Server Port
		TtlBlocklist:              30 * 24 * 3600,                                                     // Blocklist message TTL in seconds
		TtlChatMessage:            30 * 24 * 3600,                                                     // Chat message TTL in seconds
		TtlDefault:                30 * 24 * 3600,                                                     // Default TTL in seconds
		TtlFriend:                 30 * 24 * 3600,                                                     // Friend TTL in seconds
		TtlProfile:                30 * 24 * 3600,                                                     // Profile message TTL in seconds
		TtlGroup:                  7 * 24 * 3600,                                                      // Group message TTL in seconds
		TtlIndex:                  30 * 24 * 3600,                                                     // Default TTL in seconds
		TtlMembership:             3600,                                                               // Membership message TTL in seconds
		TtlPlayedDefault:          3600,                                                               // Default TTL for recently played data in seconds
		TtlPlayedMax:              30 * 24 * 3600,                                                     // Max TTL for recently played data in seconds
		TtlPresence:               300,                                                                // Presence TTL in seconds (timeout for autokick)
		TtlFirstPartyToken:        3600,                                                               // TTL for first party tokens
		TtlFirstPartyRefresh:      24 * 3600,                                                          // Ttl for first party refresh
		Version:                   version,                                                            // Version
		VMQApiKey:                 "",                                                                 // VMQApiKey
		VMQApiURL:                 "",                                                                 // VMQApiURL
		VMQHttpPubApiURL:          "",                                                                 // VMQ API url for httppub calls
		VMQHttpPubApiKey:          "",                                                                 // VMQ API key for httppub calls
		VMQUseBinaryPub:           false,                                                              // Use httppub package for binary publish
		VMQHttpPubUser:            "",                                                                 // VMQ Http Pub User
		VoipPrivateApiURL:         "",                                                                 // VOIP private api endpoint
		ReportSNSTopicArn:         "arn:aws:sns:us-east-1:354767525209:t2gp-social-abuse-report-v2",   // SNS topic for all the abuse report
		IsSessionFeatureEnabled:   false,
		AwsRequestMaxRetryAttempt: 3,
	}
}

func ConfigForTests() *Config {
	cfg := NewConfig()
	cfg.setTestConfigOptions()

	return cfg
}

func (cfg *Config) setTestConfigOptions() {

	e := godotenv.Load("../../.env")
	if e != nil {
		log.Info().Msgf("Could not find .env file to load")
	}

	cfg.DynamoDBURL = os.Getenv("DYNAMODB_URL")
	cfg.VMQApiURL = os.Getenv("VMQ_API_URL")
	cfg.ElasticacheUrl = os.Getenv("ELASTICACHE_URL")
	cfg.VMQHttpPubApiURL = os.Getenv("VMQ_HTTPPUB_API_URL")
	// this set of hardcoded values is for when tests are run on github runners and we don't have .env vars set for them.

	cfg.SsoURL = "https://sso.api.2kcoretech.online/sso/v2.0"
	cfg.SsoHost = "sso.api.2kcoretech.online"
	cfg.AllowAlgNone = true
	cfg.DiscoveryURL = "https://discovery.api.2kcoretech.online"

	cfg.ProfileTable = "social_test_store"
	cfg.ChatMessagesTable = "social_chat_msg_test_store"

	cfg.AppID = os.Getenv("APP_ID")
	cfg.AppSecret = os.Getenv("APP_SECRET")
	cfg.AppBasicAuth = base64.RawURLEncoding.EncodeToString([]byte(cfg.AppID + ":" + cfg.AppSecret))

	cfg.RedisService = "social-service-redis-test"
	cfg.TtlDefault = 600

	cfg.SocialConfigBucket = "t2gp-social-discovery-test"
	cfg.MaxGroups = 2

	cfg.VMQApiKey = os.Getenv("VMQ_API_KEY")
	cfg.VMQHttpPubApiKey = os.Getenv("VMQ_HTTPPUB_API_KEY")
	if os.Getenv("VMQ_USE_BINARY_PUB") == "true" {
		cfg.VMQUseBinaryPub = true
	}
	cfg.VMQHttpPubUser = os.Getenv("VMQ_HTTPPUB_USER")
	cfg.ElasticacheUrl = "localhost:6379"
}

func (cfg *Config) SetLocalConfigOptions() {
	cfg.SsoURL = "https://sso.api.2kcoretech.online/sso/v2.0"
	cfg.SsoHost = "sso.api.2kcoretech.online"
	cfg.AllowAlgNone = true

	cfg.ProfileTable = "social_test_store"
	cfg.ChatMessagesTable = "social_chat_msg_test_store"

	//cfg.AppID = os.Getenv("APP_ID")
	//cfg.AppSecret = os.Getenv("APP_SECRET")
	cfg.AppBasicAuth = base64.RawURLEncoding.EncodeToString([]byte(cfg.AppID + ":" + cfg.AppSecret))

	cfg.RedisService = "social-service-redis-test"
	cfg.TtlDefault = 3600

	cfg.DatadogAPMEnabled = true

	//cfg.PDApiKey = os.Getenv("PD_API_KEY")
	//cfg.SocialConfigBucket = os.Getenv("SOCIAL_CONFIG_BUCKET")
}

// ParseConfigFlags parse flags from env variable
func ParseConfigFlags(cfg *Config) error {
	return ParseConfigFlagsWithOptions(cfg, os.Args[1:], flag.ExitOnError)
}

// ParseConfigFlagsWithOptions parse config with options
func ParseConfigFlagsWithOptions(cfg *Config, args []string, errorHandling flag.ErrorHandling) error {
	e := godotenv.Load()
	if e != nil && utils.IsLocal() {
		log.Info().Msgf("Could not find .env file to load")
	}

	fs := flag.NewFlagSet("social-service", errorHandling)
	fs.IntVar(&cfg.PublicPort, "public-port", cfg.PublicPort, "public port to listen on")
	fs.IntVar(&cfg.PrivatePort, "private-port", cfg.PrivatePort, "private port to listen on")
	fs.Var(&cfg.CorsAllowedOrigins, "cors-allowed-origins", "List of comma separated origins to allow for CORS")
	fs.BoolVar(&cfg.MetricsEnabled, "metrics-enabled", cfg.MetricsEnabled, "Enable metrics reporting through /metrics endpoint")
	fs.BoolVar(&cfg.DatadogAPMEnabled, "datadog-apm-enabled", cfg.DatadogAPMEnabled, "Enable go-tracer to forward APM traces to Datadog")
	fs.BoolVar(&cfg.DatadogProfilerEnabled, "datadog-profiler-enabled", cfg.DatadogProfilerEnabled, "Enable Datadog Profiler")
	fs.BoolVar(&cfg.RequestRecorderEnabled, "request-recorder-enabled", cfg.RequestRecorderEnabled, "Enable request recorder middleware")
	fs.StringVar(&cfg.RequestRecorderStream, "request-recorder-stream", cfg.RequestRecorderStream, "Request recorder kinesis stream name")
	// get app-id from 1password under "AppID"
	// https://my.1password.com/vaults/gbku3v3itxsxjeghn7oqauqxyq/allitems/wt2fbisegjnicrbhbnao4au2du
	fs.StringVar(&cfg.AppID, "app-id", cfg.AppID, "App id")
	fs.StringVar(&cfg.AppSecret, "app-secret", cfg.AppSecret, "App Secret")
	fs.BoolVar(&cfg.AllowAlgNone, "allow-alg-none", cfg.AllowAlgNone, "Allow Bearer JWT token to pass through alg=none")
	fs.StringVar(&cfg.DiscoveryURL, "discovery-url", cfg.DiscoveryURL, "Discovery Service URL")
	fs.StringVar(&cfg.DynamoDBURL, "dynamodb-url", cfg.DynamoDBURL, "DynamoDB endpoint URL")
	fs.StringVar(&cfg.AwsRegion, "aws-region", cfg.AwsRegion, "AWS Region")
	fs.StringVar(&cfg.ChatMessagesTable, "chat-messages-table", cfg.ChatMessagesTable, "DynamoDB table name for chat messages")
	fs.StringVar(&cfg.ProfileTable, "profile-table", cfg.ProfileTable, "DynamoDB table name for profile and friend services")
	fs.StringVar(&cfg.S3BucketRegion, "s3-bucket-region", cfg.S3BucketRegion, "S3 bucket region")
	fs.StringVar(&cfg.VMQApiURL, "vmq-api-url", cfg.VMQApiURL, "VerneMQ Admin API URL")
	fs.StringVar(&cfg.VMQApiKey, "vmq-api-key", cfg.VMQApiKey, "VerneMQ Admin API Key")
	fs.StringVar(&cfg.VMQHttpPubApiURL, "vmq-httppub-api-url", cfg.VMQHttpPubApiURL, "VerneMQ Httppub API URL")
	fs.StringVar(&cfg.VMQHttpPubApiKey, "vmq-httppub-api-key", cfg.VMQHttpPubApiKey, "VerneMQ Httppub API Key")
	fs.StringVar(&cfg.VMQHttpPubUser, "vmq-httppub-user", cfg.VMQHttpPubUser, "VerneMQ Httppub default user")
	fs.BoolVar(&cfg.VMQUseBinaryPub, "vmq-use-binary-pub", cfg.VMQUseBinaryPub, "Use httppub package for binary publish")
	fs.IntVar(&cfg.MaxFriends, "max-friends", cfg.MaxFriends, "Max friends")
	fs.IntVar(&cfg.MaxGroups, "max-groups", cfg.MaxGroups, "Max groups")
	fs.IntVar(&cfg.MaxGroupSize, "max-group-size", cfg.MaxGroupSize, "Max group size")
	fs.IntVar(&cfg.MaxBlocks, "max-blocks", cfg.MaxBlocks, "Max groups")
	fs.IntVar(&cfg.ListFriendsLimit, "list-friends-limit", cfg.ListFriendsLimit, "Default value for limit on getfriends")
	fs.IntVar(&cfg.MaxChatMessageLength, "max-message-length", cfg.MaxChatMessageLength, "Max message length")
	fs.IntVar(&cfg.MaxMetaSize, "max-meta-size", cfg.MaxMetaSize, "Max meta size")
	fs.BoolVar(&cfg.SkipAuth, "skip-auth", cfg.SkipAuth, "Skip JWT authentication")
	fs.BoolVar(&cfg.DebugMode, "debug-mode", cfg.DebugMode, "Enable/disable debug mode")
	fs.BoolVar(&cfg.DnsCaching, "dns-caching", cfg.DnsCaching, "Enable/disable DNS caching")
	fs.IntVar(&cfg.TtlDefault, "redis-item-default-ttl", cfg.TtlDefault, "redis item ttl in seconds")
	fs.StringVar(&cfg.HttpURL, "http-url", "http://localhost:8000", "external REST API url")
	fs.StringVar(&cfg.MqttURL, "mqtt-url", "ws://localhost:8080", "external MQTT url")
	fs.IntVar(&cfg.TtlPresence, "default-presence-timeout", 300, "time in seconds before presence auto logout")
	fs.StringVar(&cfg.VoipPrivateApiURL, "voip-private-api-url", cfg.VoipPrivateApiURL, "VOIP private api endpoint")
	fs.Int64Var(&cfg.MaxRequestBodyBytes, "max-request-body-bytes", cfg.MaxRequestBodyBytes, "Max request body length in bytes")
	fs.StringVar(&cfg.RSConfigURL, "rs-config", cfg.RSConfigURL, "Rockstar configur url")
	fs.IntVar(&cfg.TSPort, "ts-port", cfg.TSPort, "Trusted Server port to listen on")
	fs.StringVar(&cfg.ServerMode, "server-mode", cfg.ServerMode, "Server Mode (social, discovery)")
	fs.StringVar(&cfg.SocialConfigBucket, "discovery-bucket", cfg.SocialConfigBucket, "discovery bucket")
	fs.BoolVar(&cfg.FixPendingFriends, "fix-pending-friends", cfg.FixPendingFriends, "Fix pending friends")
	fs.StringVar(&cfg.ElasticacheUrl, "elasticache-url", cfg.ElasticacheUrl, "set elasticache url")
	fs.StringVar(&cfg.Hostname, "hostname", cfg.Hostname, "set hostname")
	fs.StringVar(&cfg.FirehoseRegion, "firehose-region", cfg.FirehoseRegion, "set data firehose region")
	fs.StringVar(&cfg.FirehoseStreamName, "firehose-stream-name", cfg.FirehoseStreamName, "set data firehose stream name")
	fs.BoolVar(&cfg.ShouldFirehose, "should-firehose", cfg.ShouldFirehose, "should firehose mode")
	fs.StringVar(&cfg.KinesisRegion, "kinesis-region", cfg.KinesisRegion, "set kinesis datastream region")
	fs.StringVar(&cfg.KinesisStreamArn, "kinesis-stream-name", cfg.KinesisStreamArn, "set kinesis data stream name")
	fs.BoolVar(&cfg.ShouldKinesis, "should-kinesis", cfg.ShouldKinesis, "should kinesis mode")

	if utils.IsNonProdCluster() {
		// always enable alg none for develop or local
		cfg.AllowAlgNone = true
	}

	// ff package loads environment variables into the config via fs command line arguments
	// More info at: https://github.com/peterbourgon/ff#flags-and-env-vars
	err := ff.Parse(fs, args, ff.WithEnvVarNoPrefix(), ff.WithEnvVarSplit(","))
	if err != nil {
		fmt.Fprintln(fs.Output(), "usage: server [flags ...]")
		fmt.Fprintln(fs.Output(), "Note: flags can also be passed through env vars, but flags take priority.")
		fs.PrintDefaults()
		fmt.Fprintln(fs.Output(), "error: "+err.Error())
		return err
	}

	// calc basic auth
	cfg.AppBasicAuth = base64.RawURLEncoding.EncodeToString([]byte(cfg.AppID + ":" + cfg.AppSecret))

	if len(cfg.CorsAllowedOrigins) == 0 {
		cfg.CorsAllowedOrigins = []string{"https://*.d2dragon.net"}
	}

	return nil
}

// GetEnv get env variable w/ default
func GetEnv(key, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}

var sessionPolicyConfig map[string]SessionPolicyConfig

func GetDefaultSessionPolicyConfig() SessionPolicyConfig {
	return SessionPolicyConfig{
		Description:   "Default session policy",
		SessionPolicy: SingleSession,
	}
}

func GetSessionPolicyConfigMap() *map[string]SessionPolicyConfig {
	return &sessionPolicyConfig
}

func SetSessionPolicyConfigMap(key string, config SessionPolicyConfig) {
	if sessionPolicyConfig == nil {
		sessionPolicyConfig = make(map[string]SessionPolicyConfig)
	}
	sessionPolicyConfig[key] = config
}
