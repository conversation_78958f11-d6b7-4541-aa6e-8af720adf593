<script lang="ts">
  import { createEventDispatcher } from 'svelte';
  import { SVGBell, SVGPersonAdd } from '../../assets/icons';
  import {
    EVENT_FRIENDS_ADD_WINDOW_OPEN,
    ROUTE_FRIEND_REQUESTS,
  } from '../../constant';
  import {
    usePendingFriendsQuery,
    useTranslator,
    useTransportService,
    useUserQuery,
  } from '../../hooks';
  import { Badge } from '../Badge';
  import type { CustomEventMap } from '../CustomInput';
  import { NavLink } from '../NavLink';
  import { SearchBar } from '../SearchBar';

  const pendingFriendsQueryResult = usePendingFriendsQuery();
  const t = useTranslator();
  const transportService = useTransportService();
  const userQueryResult = useUserQuery();
  const dispatch = createEventDispatcher<CustomEventMap>();

  const onSearchTextChange = ({
    detail,
  }: CustomEvent<CustomEventMap['inputChange']>) => {
    dispatch('inputChange', {
      text: detail.text,
    });
  };

  const onAddFriendsClicked = () => {
    transportService.publishEvent(EVENT_FRIENDS_ADD_WINDOW_OPEN);
  };

  $: pendingRequests = $pendingFriendsQueryResult?.data || [];
  $: userProfile = $userQueryResult?.data;
  $: pendingRequstCount = pendingRequests.filter(
    p => p.invitee === userProfile?.userid && !p.viewed
  ).length;
</script>

<style>
  .friends-list-action-bar {
    box-sizing: border-box;
    height: 3.75rem;
    background-color: var(
      --social-bg-color-action-bar,
      var(--default-bg-color-action-bar)
    );
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.75rem 0.875rem;
  }

  .friends-list-action-bar .link-button {
    margin-inline-start: 1rem;
    position: relative;
  }

  .friends-list-action-bar .link-button:hover {
    cursor: pointer;
  }
  .friends-list-action-bar :global(.request-badge) {
    position: absolute;
    right: -4px;
    top: -6px;
  }
</style>

<div class="friends-list-action-bar">
  <SearchBar
    on:inputChange="{onSearchTextChange}"
    placeholder="{$t('Search your fiends')}"
  />

  <div class="link-button">
    <NavLink to="{ROUTE_FRIEND_REQUESTS}">
      <SVGBell />
      {#if pendingRequstCount > 0}
        <Badge value="{pendingRequstCount}" className="request-badge" />
      {/if}
    </NavLink>
  </div>

  <div class="link-button" on:click="{onAddFriendsClicked}">
    <SVGPersonAdd />
  </div>
</div>
