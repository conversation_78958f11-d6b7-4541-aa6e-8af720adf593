package middleware

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/2kg-coretech/dna-common/pkg/identity"
	"github.com/franela/goblin"
	"github.com/rs/zerolog"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"go.uber.org/mock/gomock"
)

type TestHandler struct {
	CallCount int
	Request   *http.Request
	Callback  func(w http.ResponseWriter, r *http.Request)
}

type Identity interface {
	identity.Identity
}

func (h *TestHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
	h.CallCount++
	h.Request = r
	if h.Callback != nil {
		h.Callback(w, r)
	}
	utils.WriteJsonResponse(w, r, http.StatusOK, map[string]string{})
}

type mockObj struct {
	w        *httptest.ResponseRecorder
	r        *http.Request
	ctrl     *gomock.Controller
	identity *MockIdentity
}

func mockRequest(t *testing.T, path string) mockObj {
	ctx := context.Background()
	r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com"+path, nil)
	w := httptest.NewRecorder()
	mockCtrl := gomock.NewController(t)
	identity := NewMockIdentity(mockCtrl)

	return mockObj{
		w:        w,
		r:        r,
		ctrl:     mockCtrl,
		identity: identity,
	}
}

func TestBasicAuth(t *testing.T) {
	g := goblin.Goblin(t)
	zerolog.SetGlobalLevel(zerolog.FatalLevel)

	g.Describe("BasicAuth", func() {
		g.It("should fail basic auth with no header", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			handler := BasicAuth(mock.identity)

			nextHandler := &TestHandler{CallCount: 0}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.Assert(nextHandler.CallCount).Equal(0)
			g.Assert(mock.w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("should succeed for excepted paths", func() {
			paths := []string{
				"/health", "/favicon.ico",
			}
			for _, path := range paths {
				mock := mockRequest(t, path)
				defer mock.ctrl.Finish()

				handler := BasicAuth(mock.identity)

				nextHandler := &TestHandler{CallCount: 0}
				handler(nextHandler).ServeHTTP(mock.w, mock.r)
				g.Assert(nextHandler.CallCount).Equal(1)
				g.Assert(mock.w.Code).Equal(http.StatusOK)
			}
		})

		g.It("should fail basic auth with bad appid", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			mock.r.Header.Add("Authorization", "Basic Zm9vOmJhcg==")

			handler := BasicAuth(mock.identity)

			mock.identity.EXPECT().GetApplication("foo").Return(nil, fmt.Errorf("bad app id"))

			nextHandler := &TestHandler{CallCount: 0}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.Assert(nextHandler.CallCount).Equal(0)
			g.Assert(mock.w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("should fail basic auth incorrect password", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			mock.r.Header.Add("Authorization", "Basic Zm9vOmJhcg==")

			handler := BasicAuth(mock.identity)

			app := identity.Application{
				ID:     "foo",
				Secret: "baz",
			}

			mock.identity.EXPECT().GetApplication("foo").Return(&app, nil)

			nextHandler := &TestHandler{CallCount: 0}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.Assert(nextHandler.CallCount).Equal(0)
			g.Assert(mock.w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("should succeed w/ correct password", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			mock.r.Header.Add("Authorization", "Basic Zm9vOmJhcg==")

			handler := BasicAuth(mock.identity)

			app := identity.Application{
				ID:     "foo",
				Secret: "bar",
			}

			mock.identity.EXPECT().GetApplication("foo").Return(&app, nil)

			nextHandler := &TestHandler{CallCount: 0}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.Assert(nextHandler.CallCount).Equal(1)
			g.Assert(mock.w.Code).Equal(http.StatusOK)
		})
	})
}
