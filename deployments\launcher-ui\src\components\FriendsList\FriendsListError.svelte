<script lang="ts">
  import { SVGErrorFace, SVGRefresh } from '../../assets/icons';
  import { useTranslator } from '../../hooks';
  import { Button } from '../Button';

  const t = useTranslator();
</script>

<style>
  .container {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .container .svg {
    margin-top: 12rem;
  }

  .container p {
    font-family: Montserrat;
    font-style: normal;
    font-weight: 500;
    font-size: 1rem;
    line-height: 150%;
    text-align: center;
    color: var(--social-color, var(--default-color));
    margin-top: 1rem;
    margin-bottom: 1rem;
    opacity: 0.8;
  }
</style>

<div class="container">
  <div class="svg">
    <SVGErrorFace />
  </div>

  <p>{$t('Failed to load friends')}.</p>
  <Button outline showIcon>
    <span slot="icon"><SVGRefresh /></span>
    <span slot="label">{$t('Refresh')}</span>
  </Button>
</div>
