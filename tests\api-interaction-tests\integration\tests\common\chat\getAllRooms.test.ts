import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let tokenHost: string;
let roomId: string;
beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  roomId = await (await socialApi.createRoom(tokenHost, 2)).body.groupid;
});
afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.loginOut(tokenHost);
});
describe('', () => {
  /**
   * Checking get all rooms
   * - Create room
   * - Get room
   */
  it('get all rooms', async () => {
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .get('/chat/rooms')
      .set('Authorization', 'Bearer ' + tokenHost);

    expect(resp.status).toEqual(StatusCodes.OK);
  });
});
