name: Run unit test

on:
  push:
    branches:
      - develop
      - master
  pull_request:
    branches:
      - '*'
  release:
    types:
      - published

jobs:
  test:
    name: Run unit test
    runs-on: [t2gp-arc-linux]
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
      - name: cleanup
        run: |
          userid=$(id -u $(whoami))
          docker run -v ${PWD}:/var/clean node:12.22.4-alpine3.11 chown -R $userid /var/clean
          ls -al .
          docker network rm docker_devcontainer || true
        continue-on-error: true

      - name: Check out code
        uses: actions/checkout@v2
        with:
          fetch-depth: 0 # Sonarqube needs full depth to allocate issues to the original author
          submodules: recursive
          token: ${{ secrets.GH_PAT }}

      - name: Setup test
        run: |
          env > .env
          make version
          make test-down
        env:
          SOCIAL_DYNAMODB_ENDPOINT: http://dynamodb:8000
          SOCIAL_DISCOVERY_URL: https://discovery.api.2kcoretech.online
          SOCIAL_APP_BASIC_AUTH: ${{ secrets.APP_BASIC_AUTH }}
          SOCIAL_APP_ID: ${{ secrets.APP_ID }}
          SOCIAL_API_KEY: fakeapikey
          SOCIAL_PROFILE_TABLE: t2gp-social-develop-social

      - name: Run test
        run: |
          make test
        env:
          SOCIAL_DYNAMODB_ENDPOINT: http://dynamodb:8000
          SOCIAL_DISCOVERY_URL: https://discovery.api.2kcoretech.online
          SOCIAL_APP_BASIC_AUTH: ${{ secrets.APP_BASIC_AUTH }}
          SOCIAL_APP_ID: ${{ secrets.APP_ID }}
          SOCIAL_APP_SECRET: ${{ secrets.APP_ID }}
          SOCIAL_API_KEY: fakeapikey
          SOCIAL_PROFILE_TABLE: t2gp-social-develop-social

      # disable for now because vernemq docker image has a broken dialyzer
      # - name: Run dialyzer
      #   run: |
      #     make dialyzer
      #   continue-on-error: true

      # send coverage report to sonarqube
      - name: Inject slug/short variables
        uses: rlespinasse/github-slug-action@v3.x
      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONARQUBE_TOKEN }}
          SONAR_HOST_URL: https://sonarqube.take2games.com
        with:
          args: >
            -Dsonar.login=${{ secrets.SONARQUBE_TOKEN }}
            -Dsonar.host.url=https://sonarqube.take2games.com
            -Dsonar.projectKey=t2gp-${{ env.GITHUB_REPOSITORY_NAME_PART }}
            -Dsonar.branch.name=${{ env.GITHUB_REF_SLUG }}
            -Dsonar.projectVersion=${{ env.GITHUB_SHA_SHORT }}
            -Dsonar.erlang.ct.coverdata.filename=ct.coverdata
            -Dsonar.exclusions=_build
            -Dsonar.sources=src
            -Dsonar.exclusions=src/reloader.erl,src/t2gp_social_ver.erl
        continue-on-error: true
