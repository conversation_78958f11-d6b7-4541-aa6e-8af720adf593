# @baseUrl=https://social-service-develop.d2dragon.net
@baseUrl=http://localhost:8000
@accesstoken={{login.response.body.$.accessToken}}
@accesstokensteam={{loginSteam.response.body.$.accessToken}}

@groupid={{createGroup.response.body.$.groupid}}

@approverid={{login.response.body.$.accountId}}

### login 2k to create group
# @name login
POST {{baseUrl}}/v1/auth/login

{
  "email": "<EMAIL>",
  "password": "D2CTesting",
  "locale": "en-US",
  "appId": "955e2f6c7ce345a6ba0d4d4f77d9257d"
}


### login steam
# @name loginSteam
POST https://sso.api.2kcoretech.online/sso/v2.0/auth/tokens
Content-Type: application/json
Authorization: Application 955e2f6c7ce345a6ba0d4d4f77d9257d

{
	"locale": "en-GB",
	"accountType": "platform",
    "instanceId": "1",
	"credentials":
	{
        "type": "steam",
	    "steamUserId": "*****************",
	    "steamProfileName": "d2cfriendstest002"
	}
}

###
# @name createGroup
POST {{baseUrl}}/v1/groups
Authorization: Bearer {{accesstoken}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev

{
    "maxMembers": 5,
    "joinRequestAction": "auto-approve",
    "canMembersInvite": true
}

###
# @name sendFirstPartyInvite
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships
Authorization: Bearer {{accesstoken}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev

{
    "expiresIn":3200,
    "isFirstPartyInvite":true,
    "memberid":"*****************",
    "onlineServiceType":3,
    "status":"invited"
}

###
# @name send2kInvite
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships
Authorization: Bearer {{accesstoken}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev

{
    "expiresIn":7200,
    "memberid":"b60595c511e04d81b5dd95ee451794d8",
    "onlineServiceType":3,
    "status":"invited"
}

###
# @name kickMember
DELETE {{baseUrl}}/v1/groups/{{groupid}}/members/b60595c511e04d81b5dd95ee451794d8
Authorization: Bearer {{accesstoken}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev

###
# @name acceptFirstPartyInvite
POST {{baseUrl}}/v1/groups/{{groupid}}/memberships
Authorization: Bearer {{accesstokensteam}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev

{
    "approverid": "{{approverid}}",
    "canCrossPlay": true,
    "isFirstPartyInvite": true,
    "memberid": "*****************",
    "onlineServiceType": 3,
    "password": "",
    "status": "requested"
}

# ###
# # @name revokeFirstPartyInvite
# PATCH {{baseUrl}}/v1/groups/{{groupid}}/memberships/1234
# Authorization: Bearer {{accesstokensteam}}
# Content-Type: application/json
# t2gp-plugin-version: 0.0.0-dev

# {
#     "approverid": "4e22d7a70ba540758471a6d7bc8a9ed2",
#     "isFirstPartyInvite": true,
#     "memberid": "",
#     "onlineServiceType": 3,
#     "status": "revoked"
# }

### view group
GET {{baseUrl}}/v1/groups/{{groupid}}
Authorization: Bearer {{accesstoken}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev

###
# @name deleteGroup
DELETE {{baseUrl}}/v1/groups/{{groupid}}
Authorization: Bearer {{accesstoken}}
Content-Type: application/json
t2gp-plugin-version: 0.0.0-dev
