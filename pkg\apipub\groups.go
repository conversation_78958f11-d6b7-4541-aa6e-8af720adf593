package apipub

import (
	"fmt"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"net/http"
	"slices"
	"strings"
)

// RedisKey redis key
func (group *GroupResponse) RedisKey(tenant string) string {
	return fmt.Sprintf("%s:prod:%s:%s:group:{%s}", tenant, group.Productid, utils.GetEnvironment(), group.Groupid)
}

// BuildGroupRedisKey Build key from parts.
func BuildGroupRedisKey(tenant, productid, groupid string) string {
	return fmt.Sprintf("%s:prod:%s:%s:group:{%s}", tenant, productid, utils.GetEnvironment(), groupid)
}

func (group *GroupResponse) Topic(tenant string) string {
	return fmt.Sprintf("%s/prod/%s/%s/group/%s", tenant, group.Productid, strings.TrimSuffix(utils.GetEnvironment(), "-v2"), group.Groupid)
}

// GetMemberRole get the role of a member in the group
func (group *GroupResponse) GetMemberRole(userid string) GroupMemberRole {
	if group.Members != nil {
		for _, member := range *group.Members {
			if member.Userid == userid {
				return member.Role
			}
		}
	}
	return Nonmember
}

// GetMemberships get Memberships for a group given statuses
// Accepts "" as memberid for All Users
func (group *GroupResponse) GetMemberships(memberid string, statuses []string) *[]MembershipRequest {

	var requests []MembershipRequest

	if group.MembershipRequests == nil {
		group.MembershipRequests = &[]MembershipRequest{}
	}
	for _, membership := range *group.MembershipRequests {
		if (memberid == "" || membership.Memberid == memberid) && slices.Contains(statuses, string(membership.Status)) {
			requests = append(requests, membership)
		}
	}

	return &requests
}

// AddMembershipIfNotExist add member to group if not in group
func (group *GroupResponse) AddMembershipIfNotExist(membership *MembershipRequest) {
	found := false
	if group.MembershipRequests == nil {
		group.MembershipRequests = &[]MembershipRequest{}
	}
	for i, aMembership := range *group.MembershipRequests {
		if aMembership.Memberid == membership.Memberid &&
			aMembership.Approverid == membership.Approverid && membership.Status == aMembership.Status &&
			areFirstPartyFlagsSame(aMembership.IsFirstPartyInvite, membership.IsFirstPartyInvite) {
			(*group.MembershipRequests)[i] = *membership
			found = true
			break
		}
	}
	if !found {
		*group.MembershipRequests = append(*group.MembershipRequests, *membership)
	}
}

// ModifyMembershipStatus modify membership status for a user
func (group *GroupResponse) ModifyMembershipStatus(oldMembership *MembershipRequest, newStatus MembershipStatus) {
	//if no existing membershiprequests, or there already exists a membershiprequest of that status for that member, return without changes.
	if group.MembershipRequests == nil {
		return
	}
	for index, membership := range *group.MembershipRequests {
		if oldMembership.Status == membership.Status && oldMembership.Memberid == membership.Memberid && membership.Approverid == oldMembership.Approverid &&
			areFirstPartyFlagsSame(oldMembership.IsFirstPartyInvite, membership.IsFirstPartyInvite) {
			(*group.MembershipRequests)[index].Status = newStatus
		}
	}
}

// RemoveMembership remove a membership request from the group
// retuns nil if the request was correctly found and deleted
// returns an error for the incorrect value if not deleted
func (group *GroupResponse) RemoveMembership(membershipRequest MembershipRequest) error {
	var sErr errs.SocialError
	sErr = errs.EGroupsInvalidMemberID

	if group.MembershipRequests == nil {
		return nil // errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
	}
	//if no membership requests in group
	if len(*group.MembershipRequests) == 0 {
		return nil // errs.New(http.StatusNotFound, errs.EGroupsMembershipNotFound)
	}
	for i, membership := range *group.MembershipRequests {
		if membership.Memberid == membershipRequest.Memberid || (membership.FirstPartyid != nil && *membership.FirstPartyid == membershipRequest.Memberid) {
			if membership.Approverid == membershipRequest.Approverid && membership.Status == membershipRequest.Status {
				*group.MembershipRequests = append((*group.MembershipRequests)[:i], (*group.MembershipRequests)[i+1:]...)
				return nil
			} else if membership.Approverid != membershipRequest.Approverid {
				sErr = errs.EGroupsInvalidApproverID
			} else if membership.Status != membershipRequest.Status {
				sErr = errs.EGroupsInvalidMembershipStatus
			}
		}
	}
	err := errs.New(http.StatusUnprocessableEntity, sErr)
	return err
}

// ClearMemberships clear memberships for a group for a member.  status = "" will clear all status
func (group *GroupResponse) ClearMemberships(memberid, status string) {
	if group == nil || group.MembershipRequests == nil {
		return
	}
	memberships := []MembershipRequest{}
	for _, membership := range *group.MembershipRequests {
		if membership.Memberid == memberid && (status == "" || string(membership.Status) == status) {
			continue
		} else {
			memberships = append(memberships, membership)
		}
	}
	group.MembershipRequests = &memberships
}

// AddMemberIfNotExist add member to group if not in group
func (group *GroupResponse) AddMemberIfNotExist(member *GroupMemberResponse) {
	found := false
	if group.Members == nil {
		group.Members = &[]GroupMemberResponse{}
	}
	for i, aMember := range *group.Members {
		if aMember.Userid == member.Userid {
			(*group.Members)[i] = *member
			found = true
			break
		}
	}
	if !found {
		*group.Members = append(*group.Members, *member)
	}
}

// GetMember get group member
func (group *GroupResponse) GetMember(userid string) *GroupMemberResponse {
	if group.Members != nil {
		for _, member := range *group.Members {
			if member.Userid == userid {
				return &member
			}
		}
	}
	return nil
}

func (group *GroupResponse) GetLeader() *GroupMemberResponse {
	if group.Members != nil {
		for _, member := range *group.Members {
			if member.Role == Leader {
				return &member
			}
		}
	}
	return nil
}

// PromoteNextMember Will ensure there is a member that is the leader
// if the leader has left the group, a new one will be assigned
func (group *GroupResponse) PromoteNextMember() *GroupMemberResponse {
	foundLeader := false
	if group.Members == nil {
		group.Members = &[]GroupMemberResponse{}
	}

	// confirm there is no existing leader
	for _, i := range *group.Members {
		if i.Role == Leader {
			foundLeader = true
			break
		}
	}

	if !foundLeader && len(*group.Members) > 0 {
		newLeader := &(*group.Members)[0]
		newLeader.Role = Leader
		return newLeader
	}
	return nil
}

// CanMemberInvite determine if a member has invite permissions for a group.
func (group *GroupResponse) CanMemberInvite(memberid string) bool {
	role := group.GetMemberRole(memberid)
	if role == Leader {
		return true
	}
	if role == Member && group.CanMembersInvite != nil && *group.CanMembersInvite {
		return true
	}
	return false
}

// RemoveMember remove a group member
func (group *GroupResponse) RemoveMember(memberUserID string) *GroupMemberResponse {
	var retval *GroupMemberResponse = nil
	if group.Members != nil {
		for index, member := range *group.Members {
			if member.Userid == memberUserID {
				retval = &member
				*group.Members = append((*group.Members)[:index], (*group.Members)[index+1:]...)
				break
			}
		}
	}
	return retval
}

// IsMember checks if user id is in group array
func (group *GroupResponse) IsMember(userid string) bool {
	if group.Members == nil {
		return false
	}

	for _, a := range *group.Members {
		if a.Userid == userid {
			return true
		}
	}
	return false
}

// LeaderCount return the leader count
func (group *GroupResponse) LeaderCount() int {
	count := 0
	if group.Members == nil {
		return count
	}
	for _, member := range *group.Members {
		if member.Role == Leader {
			count++
		}
	}
	return count
}

func (group *GroupResponse) isFull() bool {
	if group.Members == nil {
		return false
	}
	return len(*group.Members) >= group.MaxMembers
}

// CanRequestJoin this function became just is group full in v2 since any group can be joined via password as long as it's not full.
func (group *GroupResponse) CanRequestJoin() bool {
	return !group.isFull()
}

func (group *GroupResponse) UpdateGroupMemberPresence(userid string, presence PresenceResponse) {
	if group != nil {
		var newMembers []GroupMemberResponse
		if group.Members != nil {
			for _, member := range *group.Members {
				if member.Userid == userid {
					member.Presence = &presence
				}
				newMembers = append(newMembers, member)
			}
			*group.Members = newMembers
		}
	}
}

func (cgr *CreateGroupRequestBody) GetMaxMembers() int {
	if cgr.MaxMembers != nil {
		return *cgr.MaxMembers
	}
	return 2
}

func (cgr *CreateGroupRequestBody) GetPassword() string {
	if cgr.Password != nil {
		return *cgr.Password
	}
	return ""
}

// areFirstPartyFlagsSame compare two isFirstParty bool pointers to see if the values are the same
func areFirstPartyFlagsSame(firstParty1, firstParty2 *bool) bool {
	if (firstParty1 == nil && firstParty2 == nil) ||
		(firstParty1 != nil && firstParty2 != nil && *firstParty1 == *firstParty2) ||
		(firstParty1 == nil && firstParty2 != nil && !*firstParty2) ||
		(firstParty2 == nil && firstParty1 != nil && !*firstParty1) {
		return true
	}
	return false
}
