-module(t2gp_social_cli).

-include("t2gp_social.hrl").

-export([
    register_cli/0,
    on_reload/0
]).

-export([
    keyspec/1,
    userid_keyspec/0,
    message_keyspec/0,
    topic_keyspec/0
]).

-spec register_cli() -> ok.
register_cli() ->
    register_config(),
    register_cli_usage(),
    subscribe_cmd(),
    unsubscribe_cmd(),
    publish_cmd(),
    topics_cmd(),
    clear_topics_cmd(),
    reload_cmd(),
    version_cmd().

-spec on_reload() -> ok.
on_reload() ->
    lager:info("t2gp_social_cli reloading..."),
    register_cli(),
    ok.

-spec register_config() -> ok.
register_config() ->
    ok.

-spec register_cli_usage() -> ok.
register_cli_usage() ->
    % lager:info("t2gp_social_cli:register_cli_usage() called"),
    clique:register_usage(["vmq-admin", "t2gp"], script_usage()),
    clique:register_usage(["vmq-admin", "t2gp", "subscribe"], subscribe_usage()),
    clique:register_usage(["vmq-admin", "t2gp", "unsubscribe"], unsubscribe_usage()),
    clique:register_usage(["vmq-admin", "t2gp", "publish"], publish_usage()),
    clique:register_usage(["vmq-admin", "t2gp", "topics"], topics_usage()),
    clique:register_usage(["vmq-admin", "t2gp", "clear-topics"], clear_topics_usage()).

-spec subscribe_cmd() -> ok | {error, atom()}.
subscribe_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "subscribe"],
    KeySpecs = [
        topic_keyspec(),
        userid_keyspec()
    ],
    Callback = fun
        (_, [{topic, Topic}, {userid, UserId}], _) ->
            t2gp_social_apm:span(?APM_NAME, <<"t2gp_social_cli:subscribe_cmd/0">>, fun() ->
                t2gp_social_apm:tags(#{userid => UserId, topic => lists:join(<<"/">>, Topic)}),
                utils:log_info_lowload(io_lib:format("vmq-admin t2gp subscribe topic ~p for userid ~p with rpc true", [Topic, UserId])),
                vmq_metrics:incr(mqtt5_subscribe_received),
                case t2gp_social_vmq:subscribe(UserId, Topic) of
                    ok ->
                        [clique_status:text("Done")];
                    {error, Reason} ->
                        vmq_metrics:incr(mqtt5_error_subscribe),
                        Text = io_lib:format("can't subscribe to topic ~p due to '~p'", [Topic, Reason]),
                        lager:warning(Text),
                        [clique_status:alert([clique_status:text(Text)])]
                end
            end);
        (_, _, _) ->
            Text = clique_status:text(subscribe_usage()),
            [clique_status:alert([Text])]
    end,
    clique:register_command(Cmd, KeySpecs, [], Callback).

-spec unsubscribe_cmd() -> ok | {error, atom()}.
unsubscribe_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "unsubscribe"],
    KeySpecs = [
        topic_keyspec(),
        userid_keyspec()
    ],
    Callback = fun
        (_, [{topic, Topic}, {userid, UserId}], _) ->
            t2gp_social_apm:span(?APM_NAME, <<"t2gp_social_cli:unsubscribe_cmd/0">>, fun() ->
                t2gp_social_apm:tags(#{userid => UserId, topic => lists:join(<<"/">>, Topic)}),
                utils:log_info_lowload(io_lib:format("vmq-admin t2gp unsubscribe topic ~p for userid ~p", [Topic, UserId])),
                vmq_metrics:incr(mqtt5_unsubscribe_received),
                case t2gp_social_vmq:unsubscribe(UserId, Topic) of
                    ok ->
                        [clique_status:text("Done")];
                    {error, Reason} ->
                        vmq_metrics:incr(mqtt5_error_unsubscribe),
                        Text = io_lib:format("can't subscribe to topic ~p due to '~p'", [Topic, Reason]),
                        lager:warning(Text),
                        [clique_status:alert([clique_status:text(Text)])]
                end
            end);
        (_, _, _) ->
            Text = clique_status:text(unsubscribe_usage()),
            [clique_status:alert([Text])]
    end,
    clique:register_command(Cmd, KeySpecs, [], Callback).

-spec publish_cmd() -> ok | {error, atom()}.
publish_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "publish"],
    KeySpecs = [
        topic_keyspec(),
        userid_keyspec(),
        message_keyspec()
    ],
    Callback = fun
        (_, [{topic, Topic}, {message, Msg}, {userid, UserId}], []) ->
            t2gp_social_apm:span(?APM_NAME, <<"t2gp_social_cli:publish_cmd/0">>, fun() ->
                TopicBinary = erlang:iolist_to_binary(lists:join(<<"/">>, Topic)),
                t2gp_social_apm:tags(#{topic => TopicBinary, userid => UserId}),
                {_, #{
                    publish_fun:= PublishFun
                }} = vmq_reg:direct_plugin_exports(t2gp_social,#{
                    wait_till_ready => not vmq_config:get_env(allow_publish_during_netsplit, false),
                    cap_publish => vmq_config:get_env(allow_publish_during_netsplit, false)
                }),
                utils:log_info_lowload(io_lib:format("vmq-admin t2gp publish topic ~s with message ~s from user ~s", [TopicBinary, Msg, UserId])),
                vmq_metrics:incr(mqtt5_publish_received),
                case
                    PublishFun(Topic, Msg, #{
                        qos => 1, retain => false, p_user_property => [{<<"sender">>, UserId}]
                    })
                of
                    {ok, _} ->
                        vmq_metrics:incr(mqtt5_publish_sent),
                        [clique_status:text("Done")];
                    {error, Reason} ->
                        vmq_metrics:incr(mqtt5_error_publish),
                        Text = io_lib:format("can't unsubscribe to topic ~p due to '~p'", [
                            Topic, Reason
                        ]),
                        lager:warning(Text),
                        [clique_status:alert([clique_status:text(Text)])]
                end
            end);
        (_, _, _) ->
            Text = clique_status:text(publish_usage()),
            [clique_status:alert([Text])]
    end,
    clique:register_command(Cmd, KeySpecs, [], Callback).

-spec topics_cmd() -> ok | {error, atom()}.
topics_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "topics"],
    KeySpecs = [
        userid_keyspec()
    ],
    Callback = fun
        (_, [{userid, UserId}], []) ->
            t2gp_social_apm:span(?APM_NAME, <<"t2gp_social_cli:topics_cmd/0">>, fun() ->
              utils:log_info_lowload(io_lib:format("vmq-admin t2gp topics for user ~p", [UserId])),
                TopicsRaw = t2gp_social_vmq:list_subscriptions(UserId),
                Topics = lists:map(
                    fun({SubscriberId, Topic}) ->
                        % join the topics array w/ "/"
                        [{client_id, SubscriberId}, {topic, lists:join(<<"/">>, Topic)}]
                    end,
                    TopicsRaw
                ),
                lager:info("result ~p", [Topics]),
                [clique_status:table(Topics)]
            end);
        (_, _, _) ->
            Text = clique_status:text(topics_usage()),
            [clique_status:alert([Text])]
    end,
    clique:register_command(Cmd, KeySpecs, [], Callback).

-spec clear_topics_cmd() -> ok | {error, atom()}.
clear_topics_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "clear-topics"],
    KeySpecs = [
        userid_keyspec()
    ],
    Callback = fun
        (_, [{userid, UserId}], []) ->
            t2gp_social_apm:span(?APM_NAME, <<"t2gp_social_cli:clear_topics_cmd/0">>, fun() ->
              utils:log_info_lowload(io_lib:format("vmq-admin t2gp clear-topics for user ~p", [UserId])),
                t2gp_social_db:del_all_subscriptions(UserId),
                [clique_status:text("Done")]
            end);
        (_, _, _) ->
            Text = clique_status:text(clear_topics_usage()),
            [clique_status:alert([Text])]
    end,
    clique:register_command(Cmd, KeySpecs, [], Callback).

-spec version_cmd() -> ok | {error, atom()}.
version_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "version"],
    Callback = fun(_, _, _) ->
        t2gp_social_apm:span(?APM_NAME, <<"t2gp_social_cli:version_cmd/0">>, fun() ->
            [clique_status:table([t2gp_social_ver:version()])]
        end)
    end,
    clique:register_command(Cmd, [], [], Callback).

-spec reload_cmd() -> ok | {error, atom()}.
reload_cmd() ->
    Cmd = ["vmq-admin", "t2gp", "reload"],
    Callback = fun(_, _, _) ->
        t2gp_social_app:reload(),
        [clique_status:text("Done")]
    end,
    clique:register_command(Cmd, [], [], Callback).

-spec script_usage() -> [string()].
script_usage() ->
    [
        "vmq-admin t2gp <sub-command>\n\n",
        "  Publish messages, subscribe and unsubscribe to topics for T2GP Social Service.\n\n",
        "  Sub-commands:\n",
        "    subscribe      Subscribe a client to topics\n",
        "    unsubscribe    Unsubscribe a client to topics\n",
        "    publish        Publish a message to a topic\n",
        "    topics         Print a list of topics for a user\n",
        "    clear-topics   Clear all topics topics for a user\n",
        "    version        Print module version\n",
        "  Use --help after a sub-command for more details.\n"
    ].

-spec subscribe_usage() -> [string()].
subscribe_usage() ->
    [
        "vmq-admin t2gp subscribe topic=<TopicToSubscribe> userid=<UserIdToSubscribeWith>\n\n",
        "  Subscribes to the topic.",
        "\n\n"
    ].

-spec unsubscribe_usage() -> [string()].
unsubscribe_usage() ->
    [
        "vmq-admin t2gp unsubscribe topic=<TopicToUnsubscribe> userid=<UserIdToUnsubscribeWith>\n\n",
        "  Unsubscribes to the topic.",
        "\n\n"
    ].

-spec publish_usage() -> [string()].
publish_usage() ->
    [
        "vmq-admin t2gp publish topic=<TopicToSubscribe message=<MessageToPublish> userid=<UserToSendAs>\n\n",
        "  Publishes a message to a topic.  If \"user\" is not specified, it will be published as \"admin\".",
        "\n\n"
    ].

-spec topics_usage() -> [string()].
topics_usage() ->
    [
        "vmq-admin t2gp topics userid=<UserToListTopics>\n\n",
        "  List topics that is subscribed by the user.",
        "\n\n"
    ].

-spec clear_topics_usage() -> [string()].
clear_topics_usage() ->
    [
        "vmq-admin t2gp clear-topics userid=<UserToListTopics>\n\n",
        "  Clear topics subscribed by user.",
        "\n\n"
    ].

-spec topic_keyspec() -> {atom(), list()}.
topic_keyspec() ->
    {topic, [
        {typecast, fun
            (E) when is_list(E) ->
                string:split(list_to_binary(E), "/", all);
            (E) ->
                {error, {invalid_value, E}}
        end}
    ]}.

-spec keyspec(atom()) -> {atom(), list()}.
keyspec(Name) ->
    % lager:info("keyspec ~p", [Name]),
    {Name, [
        {typecast, fun
            (E) when is_list(E) ->
                list_to_binary(E);
            (E) ->
                {error, {invalid_value, E}}
        end}
    ]}.

-spec userid_keyspec() -> {atom(), list()}.
userid_keyspec() ->
    keyspec(userid).

-spec message_keyspec() -> {atom(), list()}.
message_keyspec() ->
    {message, [
        {typecast, fun
            (E) when is_list(E) ->
                list_to_binary(string:replace(E, "+", " ", all));
            (E) ->
                {error, {invalid_value, E}}
        end}
    ]}.
