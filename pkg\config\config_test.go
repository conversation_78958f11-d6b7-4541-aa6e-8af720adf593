package config

import (
	"flag"
	"os"
	"testing"

	"github.com/joho/godotenv"
)

func TestConfig(t *testing.T) {
	godotenv.Load("../../.env")
	config := ConfigForTests()

	if config == nil {
		t.Fatal("config is nil")
	}

	err := ParseConfigFlagsWithOptions(config, []string{}, flag.ContinueOnError)
	if err != nil {
		t.Fatal(err)
	}

	err = ParseConfigFlagsWithOptions(config, []string{"-unknown-flag"}, flag.ContinueOnError)
	if err == nil {
		t.Fatalf("expected error but not nil")
	}
}

func TestGetEnv(t *testing.T) {
	os.Setenv("TEST", "test")
	value := GetEnv("TEST", "default")
	if value != "test" {
		t.Fatal("get env value is not test")
	}
}

func TestSlice(t *testing.T) {
	slice := StringSlice{
		"one",
		"two",
		"three",
	}
	err := slice.Set("four")
	if err != nil {
		t.Fatal(err)
	}
	value := slice.String()
	if value != "one, two, three, four" {
		t.Fatalf("string slice %s != one, two, three, four", value)
	}
}
