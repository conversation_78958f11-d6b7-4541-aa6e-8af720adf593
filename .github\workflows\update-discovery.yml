name: 'Update Discovery'

on:
  workflow_dispatch:
    inputs:
      action:
        description: "add or remove"
        required: true
        default: "add"
        type: choice
        options:
          - add
          - remove
      id:
        required: true
        type: string
  workflow_call:
    inputs:
      action:
        required: true
        type: string
      id:
        required: true
        type: string


permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

run-name: "Updating Discovery: ${{inputs.action}} ${{ inputs.id }}"

env:
  BUCKET_NAME: t2gp-social/discovery/0f5e1d57ea994a47ba593cbaad51d9f9
  CONFIG_FILE: config.json

jobs:
  quick:
    name: Update Discovery
    runs-on: t2gp-arc-linux
    environment: production
    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
          
      - name: Download ${{ env.CONFIG_FILE}} from S3
        run: aws s3 cp s3://$BUCKET_NAME/$CONFIG_FILE $CONFIG_FILE
    
      - name: Remove entry by ID
        if: ${{ github.event.inputs.action == 'remove' }}
        run: |
          INPUT_ID="${{ github.event.inputs.id }}"
          echo "Removing entry with id: $INPUT_ID"
          jq --arg input_id "$INPUT_ID" 'map(select(.id != $input_id))' $CONFIG_FILE > tmp.json
          mv tmp.json $CONFIG_FILE
          echo "******** Output of $CONFIG_FILE ********"
          cat config.json

      - name: Add entry by ID
        if: ${{ github.event.inputs.action == 'add' }}
        run: |
          INPUT_ID="${{ github.event.inputs.id }}"
          echo "Checking for existing entry with id: $INPUT_ID"

          EXISTS=$(jq --arg input_id "$INPUT_ID" 'any(.[]; .id == $input_id)' $CONFIG_FILE)
          if [ "$EXISTS" = "true" ]; then
            echo "Entry already exists. Skipping."
            exit 0
          fi

          echo "Adding new entry for id: $INPUT_ID"

          NEW_ENTRY=$(jq -n --arg id "$INPUT_ID" '
            def getFullVersion:
              if (. | match("(v[0-9]+(?:\\.[0-9]+)*)")) != null then
                (. | match("(v[0-9]+(?:\\.[0-9]+)*)").captures[0].string)
              else
                "v2"
              end;

            def getMajorVersion:
              getFullVersion | match("v[0-9]+").string;

            def getUrlId:
              if test("^rc-") then
                "release-" + (sub("^rc-"; "") | gsub("\\."; "-"))
              else
                (sub("-v[0-9]+(?:\\.[0-9]+)*$"; "") | gsub("\\."; "-"))
              end;

            def getEnvSuffix:
              if test("^(rc|pr)-") then ".dev" else "" end;

            def getDescription:
              if index("-") then
                .[:rindex("-")] + " " + .[rindex("-")+1:]
              else
                .
              end;

            {
              canList: true,
              description: ($id | getDescription),
              id: $id,
              urls: [
                {
                  type: "http",
                  url: "https://social-service-\($id | getUrlId)\($id | getEnvSuffix).d2dragon.net/\($id | getMajorVersion)"
                },
                {
                  type: "trusted",
                  url: "https://social-trusted-\($id | getUrlId)\($id | getEnvSuffix).d2dragon.net/\($id | getMajorVersion)"
                },
                {
                  type: "mqtt",
                  url: "wss://social-service-\($id | getUrlId)\($id | getEnvSuffix).d2dragon.net/mqtt"
                }
              ]
            }')

          jq --argjson new "$NEW_ENTRY" '. += [$new]' $CONFIG_FILE > tmp.json
          mv tmp.json $CONFIG_FILE
          echo "******** Output of $CONFIG_FILE ********"
          cat config.json
      - name: Upload updated ${{ env.CONFIG_FILE}} to S3
        run: aws s3 cp $CONFIG_FILE s3://$BUCKET_NAME/$CONFIG_FILE
