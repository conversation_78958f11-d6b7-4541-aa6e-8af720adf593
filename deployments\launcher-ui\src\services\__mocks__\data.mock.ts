import type { PendingFriend, SocialFriend } from '../friends';

export const MOCKED_FRIENDS: SocialFriend[] = [
  {
    avatar:
      'https://t2gp-social.s3-us-west-1.amazonaws.com/2017e9305ccc4e5781d076403c1b6725/avatar/original.png',
    invitee: '2017e9305ccc4e5781d076403c1b6725',
    message: 'test',
    name: '<PERSON>',
    presence: [
      {
        status: 'offline',
        timestamp: '2021-01-13T01:45:04Z',
      },
    ],
    status: 'friend',
    userid: '2017e9305ccc4e5781d076403c1b6725',
  },
  {
    invitee: 'ba297969998b41768765797f2dbc3e62',
    message: 'hi 195',
    name: '195',
    presence: [
      {
        status: 'offline',
        timestamp: '2021-01-13T01:56:26Z',
      },
    ],
    status: 'pending',
    userid: 'ba297969998b41768765797f2dbc3e62',
  },
  {
    invitee: 'd7056450997c488da69d997d2a8e9446',
    message: 'hi monkey',
    name: 'Monkey',
    presence: [
      {
        status: 'offline',
        timestamp: '2021-01-13T02:58:26Z',
      },
    ],
    status: 'friend',
    userid: 'd7056450997c488da69d997d2a8e9446',
  },
  {
    avatar:
      'https://t2gp-social.s3-us-west-1.amazonaws.com/e12c3df480984141b2f385646b2024fa/avatar/original.png',
    invitee: 'e12c3df480984141b2f385646b2024fa',
    message: '_',
    name: 'John Anderson',
    presence: [{
      status: 'offline',
      timestamp: '2021-01-13T07:04:02Z',
    }],
    status: 'friend',
    userid: 'e12c3df480984141b2f385646b2024fa',
  },
];

export const MOCKED_STEAM_FRIENDS = [
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 1,
      ProfileURL: 'https://steamcommunity.com/profiles/*****************/',
      ProfileState: 1,
      PersonaName: 'loongerdidu',
      LastLogoff: 0,
      PersonaState: 0,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_full.jpg',
    },
    profileDNA: {
      firstPartyId: '*****************',
      id: 'b989634db6954ce0a4d6406939cc1c8c',
      onlineServiceType: 3,
      parentAccountId: 'f9c1837ee5e34f0c8cd5ac77d5fcd5b6',
      type: 2,
    },
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 3,
      ProfileURL: 'https://steamcommunity.com/id/naedin/',
      ProfileState: 1,
      PersonaName: 'Naedin',
      LastLogoff: 0,
      PersonaState: 1,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/51/51200bb03b3412da9e2d90827918c85e46501e13.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/51/51200bb03b3412da9e2d90827918c85e46501e13_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/51/51200bb03b3412da9e2d90827918c85e46501e13_full.jpg',
      TimeCreated: **********,
      RealName: 'Jeff',
      PrimaryClanID: '103582791434672565',
    },
    profileDNA: {
      firstPartyAlias: 'naedin',
      firstPartyId: '*****************',
      id: '2cb443aef9d42b09a3bb6e888034f044',
      onlineServiceType: 3,
      parentAccountId: 'e6342a8aeb1f43f4822d50eaf63823de',
      type: 2,
    },
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 3,
      ProfileURL: 'https://steamcommunity.com/id/sgieng_vanity/',
      ProfileState: 1,
      PersonaName: 'steven.gieng',
      LastLogoff: 0,
      PersonaState: 1,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_full.jpg',
      TimeCreated: **********,
      PrimaryClanID: '103582791429521408',
    },
    profileDNA: null,
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 3,
      ProfileURL: 'https://steamcommunity.com/id/yulius/',
      ProfileState: 1,
      PersonaName: 'yulius',
      LastLogoff: 0,
      PersonaState: 1,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/16/16874aaf8d4977466678c1b8523b142a78a54467.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/16/16874aaf8d4977466678c1b8523b142a78a54467_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/16/16874aaf8d4977466678c1b8523b142a78a54467_full.jpg',
      TimeCreated: **********,
      PrimaryClanID: '103582791429521408',
    },
    profileDNA: {
      firstPartyAlias: 'yuliustjahjadi',
      firstPartyId: '*****************',
      id: '6b91ff7c6f2b440b8e372dfad4e34728',
      onlineServiceType: 3,
      parentAccountId: '8f9a40ee0aaa45938ac28fc829adf428',
      type: 2,
    },
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 1,
      ProfileURL: 'https://steamcommunity.com/profiles/*****************/',
      ProfileState: 1,
      PersonaName: 'rec0ded1',
      LastLogoff: 0,
      PersonaState: 0,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/34/344f7fd985f46f9579b51e25da41378b6232184a.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/34/344f7fd985f46f9579b51e25da41378b6232184a_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/34/344f7fd985f46f9579b51e25da41378b6232184a_full.jpg',
    },
    profileDNA: null,
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 3,
      ProfileURL: 'https://steamcommunity.com/id/-Santini-/',
      ProfileState: 1,
      PersonaName: 'Santini',
      LastLogoff: 0,
      PersonaState: 0,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/a8/a8091fa7e1c73cf1289ef49f74e105e0c0f5562f.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/a8/a8091fa7e1c73cf1289ef49f74e105e0c0f5562f_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/a8/a8091fa7e1c73cf1289ef49f74e105e0c0f5562f_full.jpg',
      TimeCreated: **********,
      PrimaryClanID: '103582791434494906',
    },
    profileDNA: {
      firstPartyAlias: 'santini',
      firstPartyId: '*****************',
      id: '06dfce0d36f63a8a5474956fba1f25e5',
      onlineServiceType: 3,
      parentAccountId: 'ddde177b51a7615e88193d1525c82a77',
      type: 2,
    },
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 3,
      ProfileURL: 'https://steamcommunity.com/profiles/*****************/',
      ProfileState: 1,
      PersonaName: 't2_neil',
      LastLogoff: 0,
      PersonaState: 4,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/fe/fef49e7fa7e1997310d705b2a6158ff8dc1cdfeb_full.jpg',
      TimeCreated: **********,
      PrimaryClanID: '103582791429521408',
    },
    profileDNA: {
      firstPartyAlias: 'neilgoossen',
      firstPartyId: '*****************',
      id: '95d252ff39894948a2fc5cfd87f763c0',
      onlineServiceType: 3,
      parentAccountId: '45c540d7c553460eb0cdebcb36caa92c',
      type: 2,
    },
  },
  {
    profileSteam: {
      SteamID: '*****************',
      CommunityVisibilityState: 3,
      ProfileURL: 'https://steamcommunity.com/id/zenjobo/',
      ProfileState: 1,
      PersonaName: 'zenmasterjobo',
      LastLogoff: 0,
      PersonaState: 0,
      avatar:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/70/7097810d94b6f24c0a56db466485046e5eb80871.jpg',
      avatarmedium:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/70/7097810d94b6f24c0a56db466485046e5eb80871_medium.jpg',
      avatarfull:
        'https://steamcdn-a.akamaihd.net/steamcommunity/public/images/avatars/70/7097810d94b6f24c0a56db466485046e5eb80871_full.jpg',
      TimeCreated: **********,
      RealName: 'Jordan',
      PrimaryClanID: '103582791429521408',
    },
    profileDNA: {
      firstPartyId: '*****************',
      id: '20042b49bf02476cbc600a3d13c18e17',
      onlineServiceType: 3,
      parentAccountId: 'b4d4c0f7b45a4f988a7e6f245b1ea7b2',
      type: 2,
    },
  },
];

export const MOCKED_PENDING_FRIENDS: PendingFriend[] = [
  {
    friendid: 'd65d13a34c9e4d27a8fc9344dad0fca4',
    invitee: 'd65d13a34c9e4d27a8fc9344dad0fca4',
    message: 'hello',
    name: 'Monkey',
    status: 'pending',
    userid: 'b287e655461f4b3085c8f244e394ff7e',
    presence: [{
      status: 'online',
      timestamp: '',
    }],
  },
];
