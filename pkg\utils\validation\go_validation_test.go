package validation

// import (
// 	"context"
// 	"testing"

// 	"github.com/franela/goblin"
// )

// const BearerAuthScopes = "bearerAuth.Scopes"

// func TestValidateUserID(t *testing.T) {
// 	g := goblin.Goblin(t)
// 	g.Describe("TestEscapeXSS", func() {
// 		g.It("should return expected values", func() {
// 			dnaid := "8f9a40ee0aaa45938ac28fc829adf428"
// 			pdid := "f5d62b46-dfa3-4255-8432-07777a6a91a4"
// 			rsid := "1238979487"
// 			ctx := context.Background()
// 			err := Validate.VarCtx(ctx, dnaid, KValidateUserID)
// 			g.Assert(err).IsNil()
// 			err = Validate.VarCtx(ctx, pdid, KValidateUserID)
// 			g.<PERSON>ser<PERSON>(err).IsNil()
// 			err = Validate.VarCtx(ctx, rsid, KValidateUserID)
// 			g.Assert(err).IsNil()
// 		})
// 	})
// }

// func TestValidateIsNintendoIdSame(t *testing.T) {
// 	g := goblin.Goblin(t)
// 	g.Describe("IsNintendoIdSame", func() {
// 		g.It("should return expected values", func() {
// 			naid1 := "dd1-1111111111111111-AAAAAAAAAAAAAAAAAAAAAAAAAAAA"
// 			naid2 := "dd1-1111111111111111-?"
// 			naidbad := "dd1-2222222222222222-AAAAAAAAAAAAAAAAAAAAAAAAAAAA"
// 			otherId := "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa"
// 			idType1 := 11
// 			idType2 := 11
// 			otherIdType := 3

// 			//true
// 			bRet := IsNintendoIdSame(&naid1, &idType1, &naid2, &idType2)
// 			g.Assert(bRet).IsTrue()

// 			//false cases
// 			//nils
// 			bRet = IsNintendoIdSame(nil, &idType1, &naid2, &idType2)
// 			g.Assert(bRet).IsFalse()
// 			bRet = IsNintendoIdSame(&naid1, nil, &naid2, &idType2)
// 			g.Assert(bRet).IsFalse()
// 			bRet = IsNintendoIdSame(&naid1, &idType1, nil, &idType2)
// 			g.Assert(bRet).IsFalse()
// 			bRet = IsNintendoIdSame(&naid1, &idType1, &naid2, nil)
// 			g.Assert(bRet).IsFalse()

// 			//wrong types
// 			bRet = IsNintendoIdSame(&naid1, &otherIdType, &naid2, &idType2)
// 			g.Assert(bRet).IsFalse()
// 			bRet = IsNintendoIdSame(&naid1, &idType1, &naid2, &otherIdType)
// 			g.Assert(bRet).IsFalse()

// 			//different ids
// 			bRet = IsNintendoIdSame(&naid1, &idType1, &otherId, &idType2)
// 			g.Assert(bRet).IsFalse()
// 			bRet = IsNintendoIdSame(&otherId, &idType1, &naid2, &idType2)
// 			g.Assert(bRet).IsFalse()
// 			bRet = IsNintendoIdSame(&naid1, &idType1, &naidbad, &idType2)
// 			g.Assert(bRet).IsFalse()
// 		})
// 	})
// }
