package cache

import (
	"context"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"math"
	"net/http"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"

	"github.com/redis/go-redis/v9"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

func (rc *RedisCache) GetPresence(ctx context.Context, userid, productid, sessonid string) (*apipub.PresenceResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	// It happens when attempting to get a group member's presence. Because we don't know that group member's session id,
	// just get that person's all presences and then pick an online one if there are multiple or just the only presence object.

	// TODO: [SPOP] - revert the commenting out
	// if (sessonid == "") {
	// 	return pickAPresence(rdb, ctx, userid, productid)
	// }
	key := apipub.BuildPresenceRedisKey(tenant, productid, userid, sessonid)
	return getCachedObject[apipub.PresenceResponse](ctx, rc, key)
}

// TODO: [SPOP] - revert the commenting out
// func pickAPresence(rdb *RedisCache, ctx context.Context, userid, productid string) (*apipub.Presence, error) {
// 	presences, err := rdb.GetUserPresences(ctx, userid, productid)
// 	if err != nil {
// 		return nil, err
// 	}

// 	if presences == nil || len(*presences) == 0 {
// 		return nil, nil
// 	}

// 	// get the first presence objet in case that's the only presence.
// 	result := (*presences)[0]
// 	lastTimeStamp := time.Now().AddDate(-1, 0, 0)
// 	for _, p := range *presences {
// 		if p == nil {
// 			continue
// 		}

// 		if (p.Timestamp.After(lastTimeStamp)) {
// 			result = p
// 			lastTimeStamp = p.Timestamp
// 		}
// 	}

// 	return result, nil
// }

func (rc *RedisCache) SetPresence(ctx context.Context, presence *apipub.PresenceResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	if presence == nil {
		return errs.New(http.StatusNotFound, errs.EPresenceNotFound)
	}

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	userPresenceKey := userSubject.PresencesSetForKey()
	if userPresenceKey == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	//TODO: [SPOP] - need to decide how to handle with trusted server not having a JWT.
	idx := index.NewSecondaryIndex(*userPresenceKey, apipub.BuildPresenceRedisKey(tenant, productid, userid, sessionid))
	idx.SetScore(float64(presence.Priority))

	err := rc.setSecondaryIndex(ctx, idx)
	if err != nil {
		log.Error().Err(err).Msg("failed to delete user group index")
		return err
	}

	//TODO: [SPOP] - Remove the IsSessionFeatureEnabled statement once we have a production customer.
	if rc.cfg.IsSessionFeatureEnabled && sessionid != "" {
		err = rc.SetSession(ctx, productid, appid, sessionid, userid, createdTime, expiresTime, tenant)
		if err != nil {
			log.Error().Err(err).Msg("failed to set Session")
			return err
		}
	}

	if presence.Timestamp.IsZero() {
		presence.Timestamp = time.Now().UTC()
	}

	if sessionid != "" {
		presence.ActiveSessionid = sessionid
	}

	//get unix now timestamp at time of writing key so the expiration index can be written
	unixNow := time.Now().Unix()
	err2 := setCachedObject(ctx, rc, presence, presence.RedisKey(tenant), ttl)
	if err2 != nil {
		return err2
	}
	expireAtUnix := unixNow + int64(ttl.Seconds())
	rc.writePresenceExpirationIdx(ctx, presence, expireAtUnix)

	return err2
}

func (rc *RedisCache) DeletePresence(ctx context.Context, presence *apipub.PresenceResponse) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := apipub.BuildPresenceRedisKey(tenant, presence.Productid, presence.Userid, presence.ActiveSessionid)
	return rc.DeleteCachedObj(ctx, key)
}

func (rc *RedisCache) RefreshPresenceKeepAlive(ctx context.Context, presence *apipub.PresenceResponse) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	if presence == nil || rc.exists(ctx, presence.RedisKey(tenant)).Val() == 0 {
		return errs.New(http.StatusNotFound, errs.EPresenceNotFound)
	}

	productid := presence.Productid
	userid := presence.Userid

	unixNow := time.Now().Unix()
	expireAtUnix := unixNow + int64(rc.cfg.TtlPresence)

	if presence.Ttl != nil && *presence.Ttl > 0 {
		expireAtUnix = unixNow + int64(*presence.Ttl)
	}

	err := rc.expireAt(ctx, presence.RedisKey(tenant), time.Unix(expireAtUnix, 0)).Err()
	if err != nil {
		return errs.New(http.StatusInternalServerError, errs.ERedisExpireFailed)
	}

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	userPresenceKey := userSubject.PresencesSetForKey()
	if userPresenceKey == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	err = rc.expireAt(ctx, *userPresenceKey, time.Unix(expireAtUnix, 0)).Err()
	if err != nil {
		log.Error().Err(err).Str("idxKey", *userPresenceKey).Msg("failed to expire presences index key")
	}

	rc.writePresenceExpirationIdx(ctx, presence, expireAtUnix)

	// presenceKey := apipub.BuildPresenceRedisKey(tenant, productid, userid, presence.ActiveSessionid);
	// err = rdb.ecWriteClient.expire(ctx, presenceKey, time.Duration(ttl)).Err()
	// if err != nil {
	// 	log.Error().Err(err).Str("sessionId", presence.ActiveSessionid).Msg("failed to expire presence index key")
	// }
	return nil
}

func (rc *RedisCache) GetLowestAvailablePriority(ctx context.Context, userid, productid string) (int, error) {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	zrange := &redis.ZRangeBy{
		Min:   fmt.Sprintf("%d", apipub.PresencePriorityGameSetStart),
		Max:   fmt.Sprintf("%d", apipub.PresencePriorityGameSetEnd),
		Count: 1,
	}

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return -1, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	userPresenceKey := userSubject.PresencesSetForKey()
	if userPresenceKey == nil {
		return -1, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	ret, err2 := rc.zRevRangeByScoreWithScores(ctx, *userPresenceKey, zrange).Result()
	if err2 != nil {
		log.Error().Err(err2).Msgf("failed to get user presence count")
		return apipub.PresencePriorityUnknown, nil
	}

	if len(ret) == 0 {
		return apipub.PresencePriorityGameSetStart, nil
	}
	vals := ret[0]
	priority := vals.Score
	currentMax := int(math.Floor(priority))

	return currentMax + 1, nil
}

func (rc *RedisCache) GetUserPresences(ctx context.Context, userid, productid string) (*[]*apipub.PresenceResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return nil, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	userPresenceKey := userSubject.PresencesSetForKey()
	if userPresenceKey == nil {
		return nil, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	idx := index.NewSecondaryIndex(*userPresenceKey, "")
	idx.SetScore(1)
	// TODO: [SPOP] - revert the commenting out
	/*
		presenceKeysCmd := rdb.ecReadClient.ZRange(ctx, idx.IdxKey(), 0, -1);
		if presenceKeysCmd == nil || len(presenceKeysCmd.Val()) == 0 {
			return nil, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}
	*/

	presences, _, err2 := getObjsFromSecondaryIndex[apipub.PresenceResponse](ctx, rc, idx, nil, nil, false)
	return presences, err2

	// TODO: [SPOP] - revert the commenting out
	/*
		presencesJson, err := rdb.jsonReadHandler.SetContext(ctx).JSONMGet(".", presenceKeysCmd.Val()...)
		if err != nil || presencesJson == nil {
			return nil, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
		}

		if presences, ok := presencesJson.([]interface{}); ok {
			result := make([]*apipub.Presence, 0)
			for _, p := range presences {
				bytes, pOk := p.([]byte)
				if !pOk {
					continue
				}
				var presence apipub.Presence
				err = json.Unmarshal(bytes, &presence)
				if err == nil {
					result = append(result, &presence)
				}
			}
			return &result, nil
		} else {
			return nil, errs.New(http.StatusInternalServerError, errs.EJsonParse)
		}
	*/
}

// writePresenceExpirationIdx writes to a sharded key based on the first character of userid do not cause a hotshard with jsut 1 list
func (rc *RedisCache) writePresenceExpirationIdx(ctx context.Context, presence *apipub.PresenceResponse, expireAtUnix int64) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	if presence == nil {
		return errs.New(http.StatusNotFound, errs.EPresenceNotFound)
	}
	if presence.Userid == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EInvalidUserID)
	}

	return rc.writeExpirationIdx(ctx, presence.RedisKey(tenant), presence.Userid, expireAtUnix)
}

// cleanupExpirationLock release lock nil out in rc array
func (rc *RedisCache) cleanupExpirationLock(ctx context.Context, pos int64) {
	lock := rc.expirationLocks[pos]
	rc.expirationLocks[pos] = nil
	lock.Release(ctx)
}

// SetActiveGroup helper for setActiveGroup so it can also be called in a group function with a different w r
// onlyModifySizes is a bool that determines if the whole active group record should be replaced, or if only the current size and max size should be updated.
func (rc *RedisCache) SetActiveGroup(ctx context.Context, group *apipub.GroupResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, onlyModifySizes bool) (*apipub.PresenceResponse, error) {
	log := logger.FromContext(ctx)

	presence, err := rc.GetPresence(ctx, userid, productid, sessionid)
	if err != nil {
		msg := "failed to get user presence"
		log.Error().Err(err).Str("productid", productid).Str("userid", userid).Str("event", msg).Msg(msg)
		return nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed)
	}
	if presence == nil {

		if onlyModifySizes {
			return nil, nil
		}

		var priority int
		priority, err = rc.GetLowestAvailablePriority(ctx, userid, productid)
		if err != nil {
			msg := "failed to get lowest available priority"
			log.Error().Err(err).Str("userid", userid).Str("event", msg).Msg(msg)
			return nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed)
		}
		presence = &apipub.PresenceResponse{
			Userid:    userid,
			Productid: productid,
			Status:    apipub.Online,
			Priority:  priority,
			Timestamp: time.Now().UTC(),
		}
	}

	memberCount := 0
	if group.Members != nil {
		memberCount = len(*group.Members)
	}

	shouldBroadcast := true

	// if directed to only modify sizes then just write sizes.
	if onlyModifySizes {
		// We don't broadcast presence if only group size modifies.
		shouldBroadcast = false

		if presence.ActiveGroup != nil {
			if presence.ActiveGroup.Groupid == group.Groupid {
				presence.ActiveGroup.CurrentMemberCount = memberCount
				presence.ActiveGroup.MaxMembers = group.MaxMembers

			} else {
				msg := "attempting to adjust sizes of non-active group"
				log.Debug().Str("fromGroupid", presence.ActiveGroup.Groupid).Str("toGroupid", group.Groupid).Str("event", msg).Msg(msg)
			}
		}
	} else {
		presence.ActiveGroup = &apipub.ActiveGroupResponse{
			Groupid:            group.Groupid,
			CanCrossPlay:       *group.CanCrossPlay,
			CanRequestJoin:     group.CanRequestJoin(),
			CurrentMemberCount: memberCount,
			MaxMembers:         group.MaxMembers,
		}
	}

	err = rc.SavePresence(ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, shouldBroadcast)
	if err != nil {
		format := "failed to save presence"
		log.Error().Err(err).Str("event", format).Str("group", group.Groupid).Str("productid", productid).Str("userid", userid).Str("sessionid", sessionid).Msg(format)
		return nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
	}

	return presence, nil
}

// SavePresence will save the presence with an optional broadcast boolean
func (rc *RedisCache) SavePresence(ctx context.Context, presence *apipub.PresenceResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, shouldBroadcast bool) error {
	log := logger.FromContext(ctx)

	if presence == nil {
		return nil
	}

	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	ttl := rc.cfg.TtlPresence
	if presence.Ttl != nil && *presence.Ttl > 0 {
		ttl = int(*presence.Ttl)
	}
	err := rc.SetPresence(ctx, presence, productid, appid, sessionid, userid, createdTime, expiresTime, time.Duration(ttl)*time.Second)
	if err != nil {
		return err
	}

	if shouldBroadcast {
		messenger.SendMqttMessage(ctx, rc.cfg, presence.Topic(tenant), messenger.MqttMessageTypePresence, presence)

		groups, _, err2 := rc.GetUserGroups(ctx, presence.Userid, presence.Productid, nil, nil)
		if err2 != nil {
			log.Err(err2).Str("userid", presence.Userid).Str("productid", presence.Productid).Msg("failed to get groups for user saving presence")
		} else {
			if groups != nil {
				for _, group := range *groups {

					groupPresence := apipub.MqttGroupMemberPresence{
						Groupid:           group.Groupid,
						Userid:            presence.Userid,
						Status:            presence.Status,
						CustomStatus:      presence.CustomStatus,
						RichPresence:      presence.RichPresence,
						GameName:          presence.GameName,
						GameData:          presence.GameData,
						ActiveGroup:       presence.ActiveGroup,
						Productid:         presence.Productid,
						Clientid:          presence.Clientid,
						Ttl:               aws.Int(ttl),
						Priority:          presence.Priority,
						OnlineServiceType: presence.OnlineServiceType,
						Meta:              presence.Meta,
						Timestamp:         presence.Timestamp,
						JoinContext:       presence.JoinContext,
						Platformid:        presence.Platformid,
					}

					messenger.SendMqttMessage(ctx, rc.cfg, group.Topic(tenant), messenger.MqttMessageTypePresence, groupPresence)
				}
			}
		}
	}
	return nil
}
