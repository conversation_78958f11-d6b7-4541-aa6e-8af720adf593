package apipub

import (
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func TestGroup(t *testing.T) {
	g := goblin.Goblin(t)
	tenant := "dna"

	groupid := utils.GenerateNewULID()
	productid := utils.GenerateRandomDNAID()
	group := &GroupResponse{
		Groupid:    groupid,
		Productid:  productid,
		MaxMembers: 10,
	}
	user1 := utils.GenerateRandomDNAID()
	user2 := utils.GenerateRandomDNAID()
	user3 := utils.GenerateRandomDNAID()
	members := &[]GroupMemberResponse{
		{
			Userid: user1,
			Role:   Leader,
		},
		{
			Userid: user2,
			Role:   Member,
		},
	}
	group.Members = members
	key := BuildGroupRedisKey(tenant, productid, groupid)

	g.Describe("Group", func() {
		g.It("should return expected values", func() {
			g.<PERSON><PERSON><PERSON>(group.RedisKey(tenant)).Equal(key)

			group.JoinRequestAction = AutoApprove
			g.<PERSON>ser<PERSON>(group.CanRequestJoin()).IsTrue()
			group.JoinRequestAction = Manual
			g.Assert(group.CanRequestJoin()).IsTrue()
			group.MaxMembers = 1
			g.Assert(group.CanRequestJoin()).IsFalse()
			group.MaxMembers = 10
		})

		g.It("roles should return expected values", func() {
			g.Assert(group.GetMemberRole(user1)).Equal(Leader)
			g.Assert(group.GetMemberRole(user2)).Equal(Member)
			g.Assert(group.GetMemberRole(user3)).Equal(Nonmember)
			leader := group.GetLeader()
			g.Assert(leader).IsNotNil()
			g.Assert(leader.Userid).Equal(user1)

			group.CanMembersInvite = aws.Bool(true)
			g.Assert(group.CanMemberInvite(user1)).IsTrue()
			g.Assert(group.CanMemberInvite(user2)).IsTrue()
			g.Assert(group.CanMemberInvite(user3)).IsFalse()

			g.Assert(group.IsMember(user1)).IsTrue()
			g.Assert(group.IsMember(user2)).IsTrue()
			g.Assert(group.IsMember(user3)).IsFalse()

			g.Assert(group.GetMember(user1).Userid).Equal(user1)
			g.Assert(group.GetMember(user2).Userid).Equal(user2)
			g.Assert(group.GetMember(user3)).IsNil()

			group.Members = nil
			g.Assert(group.IsMember(user3)).IsFalse()
			group.Members = members

			g.Assert(group.LeaderCount()).Equal(1)

			group.Members = nil
			g.Assert(group.isFull()).IsFalse()
			group.Members = members
			g.Assert(group.isFull()).IsFalse()
			group.MaxMembers = 1
			g.Assert(group.isFull()).IsTrue()
			group.MaxMembers = 10
		})

		g.It("should promote properly", func() {
			group.Members = nil
			leader := group.PromoteNextMember()
			g.Assert(leader).IsNil()
			g.Assert(group.Members).IsNotNil()

			group.Members = &[]GroupMemberResponse{
				{
					Userid: user1,
					Role:   Leader,
				},
				{
					Userid: user2,
					Role:   Member,
				},
			}
			// leader exists
			leader = group.PromoteNextMember()
			g.Assert(leader).IsNil()

			// remove leader
			u := group.RemoveMember(user1)
			g.Assert(u).IsNotNil()
			g.Assert(group.RemoveMember(user1)).IsNil()
			g.Assert(group.GetLeader()).IsNil()

			// promote next leader
			leader = group.PromoteNextMember()
			g.Assert(leader).IsNotNil()
			g.Assert(leader.Userid).Equal(user2)

			// add member
			gm := &GroupMemberResponse{
				Userid: user3, Role: Member,
			}
			group.AddMemberIfNotExist(gm)
			g.Assert(len(*group.Members)).Equal(2)
			group.AddMemberIfNotExist(gm)
			g.Assert(len(*group.Members)).Equal(2)
			group.Members = nil
			group.AddMemberIfNotExist(gm)
			g.Assert(len(*group.Members)).Equal(1)

			group.Members = members
		})
	})
}

func TestGroupMembers(t *testing.T) {
	g := goblin.Goblin(t)

	groupid := utils.GenerateNewULID()
	productid := utils.GenerateRandomDNAID()
	approverid := utils.GenerateRandomDNAID()
	group := &GroupResponse{
		Groupid:    groupid,
		Productid:  productid,
		MaxMembers: 10,
	}
	user1 := utils.GenerateRandomDNAID()
	user2 := utils.GenerateRandomDNAID()
	user3 := utils.GenerateRandomDNAID()
	mr := &[]MembershipRequest{
		{
			Memberid:   user1,
			Status:     Approved,
			Approverid: approverid,
		},
		{
			Memberid:   user2,
			Status:     Invited,
			Approverid: approverid,
		},
	}

	g.Describe("GroupMemberships", func() {
		g.It("should return expected values", func() {
			group.MembershipRequests = nil
			r := group.GetMemberships(user1, []string{string(Approved)})
			g.Assert(r).IsNotNil()
			g.Assert(len(*r)).IsZero()

			group.MembershipRequests = mr
			r = group.GetMemberships(user1, []string{string(Approved)})
			g.Assert(r).IsNotNil()
			g.Assert(len(*r)).Equal(1)
			g.Assert((*r)[0].Memberid).Equal(user1)

			r = group.GetMemberships("", []string{string(Approved), string(Invited)})
			g.Assert(r).IsNotNil()
			g.Assert(len(*r)).Equal(2)
			g.Assert((*r)[0].Memberid).Equal(user1)
			g.Assert((*r)[1].Memberid).Equal(user2)
		})

		g.It("adding membership should return expected values", func() {
			m := &MembershipRequest{
				Memberid:   user3,
				Status:     Rejected,
				Approverid: approverid,
			}
			group.MembershipRequests = nil
			group.AddMembershipIfNotExist(m)
			g.Assert(group.MembershipRequests).IsNotNil()
			g.Assert(len(*group.MembershipRequests)).Equal(1)

			group.AddMembershipIfNotExist(m)
			g.Assert(len(*group.MembershipRequests)).Equal(1)
		})

		g.It("modifying membership should return expected values", func() {
			m := &MembershipRequest{
				Memberid:   user3,
				Status:     Rejected,
				Approverid: approverid,
			}
			group.MembershipRequests = nil
			group.ModifyMembershipStatus(m, Approved)
			g.Assert(group.MembershipRequests).IsNil()

			group.AddMembershipIfNotExist(m)
			group.ModifyMembershipStatus(m, Approved)
			g.Assert((*group.MembershipRequests)[0].Status).Equal(Approved)

			// modify w/ same value should succeed
			group.MembershipRequests = &[]MembershipRequest{
				{Memberid: user3, Status: Rejected, Approverid: approverid},
				{Memberid: user3, Status: Approved, Approverid: approverid},
			}
			group.ModifyMembershipStatus(m, Approved)
			g.Assert(len(*group.MembershipRequests)).Equal(2)
			g.Assert((*group.MembershipRequests)[0].Status).Equal(Approved)
		})

		g.It("remove membership should return expected values", func() {
			m := MembershipRequest{
				Memberid:   user3,
				Status:     Rejected,
				Approverid: approverid,
			}
			group.MembershipRequests = nil
			group.RemoveMembership(m)
		})
	})
}
