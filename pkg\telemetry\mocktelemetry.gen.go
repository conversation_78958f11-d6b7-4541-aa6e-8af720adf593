// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/telemetry/telemetry.go

// Package telemetry is a generated GoMock package.
package telemetry

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockTelemetryInterface is a mock of TelemetryInterface interface.
type MockTelemetryInterface struct {
	ctrl     *gomock.Controller
	recorder *MockTelemetryInterfaceMockRecorder
}

// MockTelemetryInterfaceMockRecorder is the mock recorder for MockTelemetryInterface.
type MockTelemetryInterfaceMockRecorder struct {
	mock *MockTelemetryInterface
}

// NewMockTelemetryInterface creates a new mock instance.
func NewMockTelemetryInterface(ctrl *gomock.Controller) *MockTelemetryInterface {
	mock := &MockTelemetryInterface{ctrl: ctrl}
	mock.recorder = &MockTelemetryInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTelemetryInterface) EXPECT() *MockTelemetryInterfaceMockRecorder {
	return m.recorder
}

// SendBlocklistEvent mocks base method.
func (m *MockTelemetryInterface) SendBlocklistEvent(ctx context.Context, meta *BlocklistTelemetryMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendBlocklistEvent", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendBlocklistEvent indicates an expected call of SendBlocklistEvent.
func (mr *MockTelemetryInterfaceMockRecorder) SendBlocklistEvent(ctx, meta interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendBlocklistEvent", reflect.TypeOf((*MockTelemetryInterface)(nil).SendBlocklistEvent), ctx, meta)
}

// SendEndorsementEvent mocks base method.
func (m *MockTelemetryInterface) SendEndorsementEvent(ctx context.Context, meta *EndorsementTelemetryMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEndorsementEvent", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendEndorsementEvent indicates an expected call of SendEndorsementEvent.
func (mr *MockTelemetryInterfaceMockRecorder) SendEndorsementEvent(ctx, meta interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEndorsementEvent", reflect.TypeOf((*MockTelemetryInterface)(nil).SendEndorsementEvent), ctx, meta)
}

// SendFriendEvent mocks base method.
func (m *MockTelemetryInterface) SendFriendEvent(ctx context.Context, meta *FriendTelemetryMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendFriendEvent", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendFriendEvent indicates an expected call of SendFriendEvent.
func (mr *MockTelemetryInterfaceMockRecorder) SendFriendEvent(ctx, meta interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendFriendEvent", reflect.TypeOf((*MockTelemetryInterface)(nil).SendFriendEvent), ctx, meta)
}

// SendGenericEvent mocks base method.
func (m *MockTelemetryInterface) SendGenericEvent(ctx context.Context, meta *GenericTelemetryMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGenericEvent", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendGenericEvent indicates an expected call of SendGenericEvent.
func (mr *MockTelemetryInterfaceMockRecorder) SendGenericEvent(ctx, meta interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGenericEvent", reflect.TypeOf((*MockTelemetryInterface)(nil).SendGenericEvent), ctx, meta)
}

// SendGroupEvent mocks base method.
func (m *MockTelemetryInterface) SendGroupEvent(ctx context.Context, meta *GroupTelemetryMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendGroupEvent", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendGroupEvent indicates an expected call of SendGroupEvent.
func (mr *MockTelemetryInterfaceMockRecorder) SendGroupEvent(ctx, meta interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendGroupEvent", reflect.TypeOf((*MockTelemetryInterface)(nil).SendGroupEvent), ctx, meta)
}

// SendReportEvent mocks base method.
func (m *MockTelemetryInterface) SendReportEvent(ctx context.Context, meta *ReportTelemetryMeta) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendReportEvent", ctx, meta)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendReportEvent indicates an expected call of SendReportEvent.
func (mr *MockTelemetryInterfaceMockRecorder) SendReportEvent(ctx, meta interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendReportEvent", reflect.TypeOf((*MockTelemetryInterface)(nil).SendReportEvent), ctx, meta)
}
