const { DynamoDBClient, ScanCommand, BatchWriteItemCommand } = require("@aws-sdk/client-dynamodb");
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

let ddbClient;

async function batchDeleteItems(tableName, params) {
    const deleteCmd = {
        RequestItems: {
            [tableName]: params.map(item => {
                return {
                    DeleteRequest: {
                        Key: item
                    }
                }
            }),
        }
    };

    // console.log(JSON.stringify(deleteCmd, null, 2));
    while (true) {
        try {
            const response = await ddbClient.send(new BatchWriteItemCommand(deleteCmd));
            break;
        } catch {
            await sleep(2000);
        }
    }
    // console.log(JSON.stringify(response, null, 2));
}

function sleep(t) {
    return new Promise(function(resolve) {
        setTimeout(resolve, t);
    });
}

async function main() {
    const args = yargs(hideBin(process.argv))
        .option('table-name', {
            describe: 'table name',
            type: 'string',
            required: true,
        })
        .option('region', {
            type: 'string',
            describe: 'AWS region',
            type: 'string',
            default: 'us-east-1',
        })
        .option('force', {
            type: 'string',
            describe: 'force production truncate',
            type: 'boolean',
            default: false,
        })
        .help().argv; 

    if (!args.force && args.tableName.match(/(prod|prd)/)) {
        throw new Error(`Cannot truncate table ${args.tableName} without --force`);
    }

    ddbClient = new DynamoDBClient({ region: args.region });

    const scan = {
        TableName: args.tableName,
        ProjectionExpression: "#pk,#sk",
        ExpressionAttributeNames: {"#pk": "pk", "#sk": "sk"},
        Limit: 25, // can only delete 25 at a time
    }
    let done = false;
    let iterCounter = 1;
    let startKey = undefined;
    console.log(`Begin deletion of table: ${args.tableName}`);
    while (!done) {
        console.log(`Iteration ${iterCounter}`);
        scan.ExclusiveStartKey = startKey;
        const response = await ddbClient.send(new ScanCommand(scan));

        console.log(`  Table: ${args.tableName} Delete count: ${response.Items.length}`);

        if (response.Items.length > 0) {
            batchDeleteItems(args.tableName, response.Items);
        }

        startKey = response.LastEvaluatedKey;
        done = startKey == undefined;
        iterCounter++;
    }
}
main();
