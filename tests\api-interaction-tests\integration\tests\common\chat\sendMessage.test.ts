import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let tokenHost: string;
let tokenInvited: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  tokenInvited = await socialApi.loginIn(
    config.invitedUsername,
    config.invitedPassword
  );
});
// eslint-disable-next-line max-lines-per-function
describe('', () => {
  afterEach(async () => {
    await socialApi.deleteFriend(tokenHost, config.invitedUserId);
    await socialApi.loginOut(tokenHost);
    await socialApi.loginOut(tokenInvited);
  });

  /**
   * Checking send Message
   * - Create room
   * - Get roomMember=1
   * - joinRoon
   * - Get roomMember=2
   * - B getMessage length
   * - A send message B
   */
  it('send message', async () => {
    let resp: request.Response = await socialApi.makeFriends(
      tokenHost,
      config.invitedUserId
    );
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({ status: 'pending' });
    resp = await socialApi.makeFriends(tokenInvited, config.inviteUserId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({ status: 'friend' });
    resp = await socialApi.sendMessage(tokenHost, config.invitedUserId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({});
  });

  it('send message if A and B is not friend', async () => {
    const resp: request.Response = await socialApi.sendMessage(
      tokenHost,
      config.invitedUserId
    );
    expect(resp.status).toEqual(StatusCodes.FORBIDDEN);
    expect(resp.body).toEqual({
      code: 403,
      message: 'not friends with that user',
    });
  });

  it('send message with profanity check', async () => {
    let resp: request.Response = await socialApi.makeFriends(
      tokenHost,
      config.invitedUserId
    );
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({ status: 'pending' });
    resp = await socialApi.makeFriends(tokenInvited, config.inviteUserId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({ status: 'friend' });
    resp = await request(config.socialEndpoints.current.api)
      .post(`/chat/dms/${config.invitedUserId}`)
      .set({
        Authorization: 'Bearer ' + tokenHost,
        'Content-Type': 'application/json',
      })
      .send({
        message: config.Bad_Words,
      });
    expect(resp.status).toEqual(StatusCodes.NOT_ACCEPTABLE);
    expect(resp.body).toEqual({
      code: 406,
      message: 'Message is unacceptable',
    });
  });
});
