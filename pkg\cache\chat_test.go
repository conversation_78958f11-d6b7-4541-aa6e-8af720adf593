package cache

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/redis/go-redis/v9"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	gomock "go.uber.org/mock/gomock"
)

func Test_SetChatMessage(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	ctx := context.Background()
	tenant := "tenant1"
	message := &apipub.ChatMessage{
		SubjectId:  "user1",
		TargetId:   "user2",
		PostedTime: time.Now(),
		ProductId:  "product1",
		Type:       apipub.N1on1,
		Body: []apipub.MessageBodySegement{
			{
				Type: apipub.Text,
				Text: "Hello",
			},
		},
	}

	subjectKey := message.RedisKey(tenant, message.SubjectId)
	targetKey := message.RedisKey(tenant, message.TargetId)
	var err error

	g.Describe("RedisCache SetChatMessage", func() {
		g.It("should store message successfully", func() {
			err = rc.SetChatMessage(ctx, tenant, message, 10*time.Second)
			g.Assert(err).IsNil()

			members1, err1 := rc.zRangeByScore(ctx, subjectKey, &redis.ZRangeBy{
				Min: "-inf",
				Max: "+inf",
			}).Result()
			members2, err2 := rc.zRangeByScore(ctx, targetKey, &redis.ZRangeBy{
				Min: "-inf",
				Max: "+inf",
			}).Result()

			g.Assert(err1).IsNil()
			g.Assert(err2).IsNil()

			message1 := &apipub.ChatMessage{}
			message2 := &apipub.ChatMessage{}
			err1 = json.Unmarshal([]byte(members1[0]), message1)
			err2 = json.Unmarshal([]byte(members2[0]), message2)
			g.Assert(err1).IsNil()
			g.Assert(err2).IsNil()

			g.Assert(message1).Equal(message2)
		})

		g.It("should store empty message successfully", func() {
			emptyMessage := &apipub.ChatMessage{
				SubjectId:  "user1",
				TargetId:   "user2",
				PostedTime: time.Now(),
				ProductId:  "product1",
				Type:       apipub.N1on1,
				Body:       []apipub.MessageBodySegement{},
			}
			err = rc.SetChatMessage(ctx, tenant, emptyMessage, 10*time.Second)
			g.Assert(err).IsNil()

			members1, err1 := rc.zRangeByScore(ctx, subjectKey, &redis.ZRangeBy{
				Min: "-inf",
				Max: "+inf",
			}).Result()
			members2, err2 := rc.zRangeByScore(ctx, targetKey, &redis.ZRangeBy{
				Min: "-inf",
				Max: "+inf",
			}).Result()
			g.Assert(err1).IsNil()
			g.Assert(err2).IsNil()

			message1 := &apipub.ChatMessage{}
			message2 := &apipub.ChatMessage{}
			err1 = json.Unmarshal([]byte(members1[0]), message1)
			err2 = json.Unmarshal([]byte(members2[0]), message2)
			g.Assert(err1).IsNil()
			g.Assert(err2).IsNil()
			g.Assert(message1).Equal(message2)
		})
		g.After(func() {
			err = rc.DeleteCachedObj(ctx, subjectKey)
			g.Assert(err).IsNil()
			err = rc.DeleteCachedObj(ctx, targetKey)
			g.Assert(err).IsNil()
		})
	})
}
