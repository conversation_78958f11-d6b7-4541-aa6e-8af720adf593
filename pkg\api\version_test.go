package api

import (
	"net/http"
	"testing"

	"github.com/franela/goblin"
)

func TestVersion(t *testing.T) {
	g := goblin.Goblin(t)
	g.Describe("Version", func() {
		g.It("should return correct version", func() {
			mock := NewMockAPI(t)
			defer mock.ctrl.Finish()
			w, r := Login("")
			mock.api.GetVersion(w, r)

			g.Assert(w.Code).Equal(http.StatusOK)
			expected := "{\"buildDate\":\"dev\",\"gitHash\":\"dev\",\"version\":\"dev\"}\n"
			g.<PERSON>ser<PERSON>(w.Body.String()).Equal(expected)
		})
	})
}
