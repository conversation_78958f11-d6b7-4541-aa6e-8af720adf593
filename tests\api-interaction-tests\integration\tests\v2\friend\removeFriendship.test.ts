import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
const message: string = 'T2GP Social Automated Testing';

beforeEach(async () => {
  usersTwok = new TwokAccounts(2, ["invitee", "inviter"]);
  await usersTwok.loginAll({});
});

afterEach(async () => {
  await usersTwok.logoutAll({});
});

// eslint-disable-next-line max-lines-per-function
describe('[public v2]', () => {
  afterEach(async () => {
    await socialApi.deleteFriend(usersTwok.acct["inviter"], usersTwok.acct["invitee"].publicId);
  });

  async function declineInvitation() {
    let r = await socialApi.deleteFriend(usersTwok.acct["invitee"], usersTwok.acct["inviter"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function revokeInvitation() {
    let r = await socialApi.deleteFriend(usersTwok.acct["inviter"], usersTwok.acct["invitee"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each`
    testSubjectFunc      | scenario                        
    ${declineInvitation} | ${"invitee declines friend invitation[happy]"}
    ${revokeInvitation}  | ${"invitor revokes friend invitation[happy]"}
  `('$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `send user friend invitation; the user then ${testSubjectFunc.name == 'declineInvitation' ? 'declines' : 'revokes'} the invitation`,
      expected: "the friend invitation is removed"
    };

    // inviter sends invitee friend invitation
    let r = await socialApi.makeFriends(
      usersTwok.acct["inviter"],
      usersTwok.acct["invitee"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // the pending invite is present in inviter and invitee friend list
    // the invitee gets friend list
    let actualFriendInfo = await socialApi.getFriends(
      usersTwok.acct["invitee"],
      {}
    );

    // expect inviter is present in invitee friend list
    let expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.arrayContaining([
          expect.objectContaining({
            friendid: usersTwok.acct["inviter"].publicId,
            invitee: usersTwok.acct["invitee"].publicId,
            status: 'pending',
            userid: usersTwok.acct["invitee"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "get unexpected friend info"
        }
      }
    );

    // the inviter gets friend list
    actualFriendInfo = await socialApi.getFriends(
      usersTwok.acct["inviter"],
      {}
    );

    // expect invitee is present in inviter friend list
    expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.arrayContaining([
          expect.objectContaining({
            friendid: usersTwok.acct["invitee"].publicId,
            invitee: usersTwok.acct["invitee"].publicId,
            status: 'pending',
            userid: usersTwok.acct["inviter"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "get unexpected friend info"
        }
      }
    );

    // main test subject
    await testSubjectFunc();

    actualFriendInfo = await socialApi.getFriends(
      usersTwok.acct["invitee"],
      {}
    );

    // expect inviter is removed from invitee friend list
    expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.not.arrayContaining([
          expect.objectContaining({
            friendid: usersTwok.acct["inviter"].publicId,
            invitee: usersTwok.acct["invitee"].publicId,
            status: 'pending',
            userid: usersTwok.acct["invitee"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "the friend invitation is not removed from invitee friend list"
        }
      }
    );

    actualFriendInfo = await socialApi.getFriends(usersTwok.acct["inviter"], {});

    // expect invitee is removed from inviter friend list
    expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.not.arrayContaining([
          expect.objectContaining({
            friendid: usersTwok.acct["invitee"].publicId,
            invitee: usersTwok.acct["invitee"].publicId,
            status: 'pending',
            userid: usersTwok.acct["inviter"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "the friend invitation is not removed from the inviter friend list"
        }
      }
    );
  });
});

describe('[public v2]', () => {
  it('Inviter removes friendship with invitee[happy]', async () => {
    let testCase = {
      description: "the user as inviter delete his/her friend",
      expected: "the friendship relationship is broken"
    };

    // inviter sends invitee friend invitation
    let r: request.Response = await socialApi.makeFriends(
      usersTwok.acct["inviter"],
      usersTwok.acct["invitee"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // invitee accepts friend invitation from inviter
    r = await socialApi.makeFriends(
      usersTwok.acct["invitee"],
      usersTwok.acct["inviter"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // inviter deletes friendship with invitee
    r = await socialApi.deleteFriend(usersTwok.acct["inviter"], usersTwok.acct["invitee"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualFriendInfo = await socialApi.getFriends(usersTwok.acct["inviter"], {});

    // expect the friendship is removed
    const expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.not.arrayContaining([
          expect.objectContaining({
            friendid: usersTwok.acct["invitee"].publicId,
            invitee: usersTwok.acct["invitee"].publicId,
            status: 'friend',
            userid: usersTwok.acct["inviter"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "the friend is not deleted from the friend list"
        }
      }
    );
  });

  it('Invitee removes friendship with inviter[happy]', async () => {
    let testCase = {
      description: "the user as invitee deletes his/her friend",
      expected: "the friendship relationship is broken"
    };

    // inviter sends invitee friend invitation
    let r: request.Response = await socialApi.makeFriends(
      usersTwok.acct["inviter"],
      usersTwok.acct["invitee"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // invitee accepts friend invitation from inviter
    r = await socialApi.makeFriends(
      usersTwok.acct["invitee"],
      usersTwok.acct["inviter"].publicId,
      { message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // invitee deletes friendship with inviter
    r = await socialApi.deleteFriend(usersTwok.acct["invitee"], usersTwok.acct["inviter"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualFriendInfo: request.Response = await socialApi.getFriends(
      usersTwok.acct["invitee"],
      {}
    );

    // expect the friendship is removed
    const expectedFriendInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.not.arrayContaining([
          expect.objectContaining({
            friendid: usersTwok.acct["inviter"].publicId,
            invitee: usersTwok.acct["invitee"].publicId,
            status: 'friend',
            userid: usersTwok.acct["invitee"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualFriendInfo).toMatchObject(expectedFriendInfo)},
      testCase,
      {
        resp: actualFriendInfo,
        additionalInfo: {
          "fail reason": "the friend is not deleted from the friend list"
        }
      }
    );
  });
});