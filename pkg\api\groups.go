package api

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"slices"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"

	"github.com/segmentio/encoding/json"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

// CreateGroup create a group
func (api *SocialPublicAPI) CreateGroup(w http.ResponseWriter, r *http.Request) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime
	sessionid := token.Claims.SessionID

	// decode request
	var createGroup apipub.CreateGroupRequestBody
	if !DecodeBody(w, r, &createGroup) {
		return
	}

	if createGroup.CanCrossPlay != nil && *createGroup.CanCrossPlay && !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	group := api.CreateAndGetGroup(w, r, productid, appid, userid, sessionid, ost, &createGroup, createdTime, expiresTime)
	if group != nil {
		ReturnCreated(w, r, group)
		return
	}
}

// CreateAndGetGroup create and return the group. Only throw HTTP errors.
func (api *SocialPublicAPI) CreateAndGetGroup(w http.ResponseWriter, r *http.Request, productid, appid, userid, sessionid string, ost apipub.OnlineServiceType, createGroup *apipub.CreateGroupRequestBody, createdTime, expiresTime int64) *apipub.GroupResponse {
	log := logger.Get(r)
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)

	maxGroupSize := 100
	if int64(api.Cfg.MaxGroupSize) > 0 {
		maxGroupSize = api.Cfg.MaxGroupSize
	}

	if !slices.Contains([]string{string(apipub.Manual), string(apipub.AutoApprove), string(apipub.AutoReject)}, string(createGroup.JoinRequestAction)) {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidJoinRequestAction))
		return nil
	}

	maxMembers := createGroup.GetMaxMembers()
	if maxMembers < 2 || maxMembers > maxGroupSize {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidMaxMembers))
		return nil
	}

	// verify max group hasn't been reached
	count, err := api.Cache.CountUserGroups(r.Context(), userid, productid)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisGeneric))
		return nil
	}
	if count >= int64(api.Cfg.MaxGroups) {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMaxReached))
		return nil
	}

	log.Info().Str("event", fmt.Sprintf("CreateGroup - ProductID: %s", productid)).Msgf("CreateGroup - ProductID: %s", productid)
	if productid == "" {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidProductID))
		return nil
	}
	if createGroup.Meta != nil {
		ja, _ := json.Marshal(createGroup.Meta)
		if len(ja) > api.Cfg.MaxMetaSize {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMetaTooLong))
			return nil
		}
	}

	groupid := utils.GenerateNewULID()

	//default boolean values
	canMembersInvite := false
	if createGroup.CanMembersInvite != nil {
		canMembersInvite = *createGroup.CanMembersInvite
	}
	canCrossPlay := true
	if createGroup.CanCrossPlay != nil {
		canCrossPlay = *createGroup.CanCrossPlay
	}

	group := &apipub.GroupResponse{
		Groupid:           groupid,
		Productid:         productid,
		MaxMembers:        createGroup.GetMaxMembers(),
		JoinRequestAction: createGroup.JoinRequestAction,
		Password:          utils.HashPassword(createGroup.Password),
		CanMembersInvite:  &canMembersInvite,
		CanCrossPlay:      &canCrossPlay,
		OnlineServiceType: &ost,
		Meta:              createGroup.Meta,
	}

	api.Cache.SetGroup(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)

	additionalInfo := make(map[string]string)
	if createGroup.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*createGroup.TeleMeta, &additionalInfo)
	}

	// add group leader
	groupLeader := apipub.GroupMemberResponse{
		Userid:    userid,
		Role:      apipub.Leader,
		Productid: group.Productid,
		Name:      aws.String(""),
	}
	// add group member details if found
	profile, err := api.getUserProfile(r.Context(), groupLeader.Userid, true)
	if profile != nil {
		groupLeader.Name = profile.DisplayName
		// get links after write
	} else {
		log.Error().Err(err).Msg("Failed to get user profile for group leader on create")
	}

	// set user group index redis
	err = api.Cache.AddGroupMember(r.Context(), group, &groupLeader)
	if err != nil {
		log.Error().Err(err).Msg("Failed to set user groups")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed))
		return nil
	}

	// add group member details if found
	if profile != nil {
		if profile.Links != nil {
			groupLeader.Links = profile.Links
			if appid != constants.TrustedServer {
				identity.FilterLinksByOST(groupLeader.Links, ost)
			}
		}
	} else {
		log.Error().Err(err).Msg("Failed to get user profile for group leader on create")
	}

	presence, err := api.Cache.GetPresence(r.Context(), groupLeader.Userid, groupLeader.Productid, "")
	if err != nil {
		log.Error().Err(err).Msgf("GetPresence error in create group %v", err)
	}
	groupLeader.Presence = presence
	group.Members = &[]apipub.GroupMemberResponse{groupLeader}

	// send telemetry after group leader add so group_size and is_group_host get reported
	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupCreate, userid, ost, []string{groupid}, &appid, &additionalInfo))

	// subscribe to group topic
	topic := group.Topic(tenant)
	subErr := messenger.Subscribe(r.Context(), api.Cfg, userid, topic)
	if subErr != nil {
		log.Error().Err(subErr).Msgf("failed to subscribe to %s for %s", topic, userid)
	}

	// Send mqtt message on group create
	msgType := messenger.MqttMessageTypeGroupModified
	data := apipub.MqttGroupModified{
		Action:  "create",
		Groupid: groupid,
	}
	messenger.SendMqttMessage(r.Context(), api.Cfg, topic, msgType, &data)

	// send mqtt message
	userModified := apipub.MqttGroupMemberModified{
		Action:   "create",
		Userid:   userid,
		Reason:   "created",
		PreRole:  apipub.Nonmember,
		PostRole: apipub.Leader,
		Groupid:  groupid,
	}
	messenger.SendMqttMessage(r.Context(), api.Cfg, topic, messenger.MqttMessageTypeGroupMembersModified, userModified)

	//set password nil so it doesn't get returned on create
	group.Password = nil

	return group
}

// DeleteGroup delete a group item
func (api *SocialPublicAPI) DeleteGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer
	api.DeleteGroupHelper(w, r, g, userid, productid, appid, ost)
}

// DeleteGroupHelper delete a group item
func (api *SocialPublicAPI) DeleteGroupHelper(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, userid, productid, appid string, ost apipub.OnlineServiceType) {
	log := logger.Get(r)
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)

	groupid := g
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	if userid != constants.TrustedServer {
		err = api.CanDelete(r.Context(), group, userid, productid)
		if err != nil {
			errs.Return(w, r, errs.ToError(err))
			return
		}
	}
	topic := group.Topic(tenant)

	// Send mqtt message on group disband
	msgType := messenger.MqttMessageTypeGroupModified
	data := apipub.MqttGroupModified{
		Action:  "disband",
		Groupid: groupid,
	}
	messenger.SendMqttMessage(r.Context(), api.Cfg, topic, msgType, &data)

	// remove all member subscriptions
	for _, member := range *group.Members {
		messenger.Unsubscribe(r.Context(), api.Cfg, member.Userid, topic)
		api.clearActiveGroup(r.Context(), userid, productid, &groupid)
	}

	// group delete
	err = api.Cache.DeleteGroup(r.Context(), group)
	if err != nil {
		log.Error().Err(err).Msgf("DeleteGroup() failed")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheDeleteFailed))
		return
	}

	// set group ost to token ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = &ost

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupDisband, userid, ost, []string{groupid}, &appid, nil))

	group.OnlineServiceType = originalOST

	// everything ok
	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) CanDelete(ctx context.Context, group *apipub.GroupResponse, userId, productId string) error {
	log := logger.FromContext(ctx)
	if group.GetLeader() == nil {
		log.Warn().Str("group Id", group.Groupid).Msg("group has no leader.")
		return nil
	}

	leaderId := group.GetLeader().Userid
	limit := aws.Int64(int64(api.Cfg.MaxGroups))
	if leaderId != userId {
		groups, _, err := api.Cache.GetUserGroups(ctx, leaderId, productId, limit, nil)
		if groups == nil {
			log.Warn().Str("group leader Id", leaderId).Msg("group leader doesn't own any group.")
			return nil
		}
		if err != nil {
			log.Error().Err(err).Str("user Id", leaderId).Msg("failed to get groups owned by group leader.")
			return err
		}

		for _, g := range *groups {
			if g.Groupid == group.Groupid {
				// Group member is not allowed to deleted a group they don't own
				log.Error().Err(err).Str("user Id", leaderId).Msg("you are not allowed to delete the group.")
				return errs.New(http.StatusForbidden, errs.EAuthorizationFailed)
			}
		}

		// Group leader doesn't own the group, so any member can delete the group.
		return nil
	}
	return nil
}

// UpdateGroup edit group
func (api *SocialPublicAPI) UpdateGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime

	api.UpdateGroupHelper(w, r, g, userid, productid, appid, ost, sessionid, createdTime, expiresTime)
}

// UpdateGroupHelper edit group settings
func (api *SocialPublicAPI) UpdateGroupHelper(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, userid, productid, appid string, ost apipub.OnlineServiceType, sessionid string, createdTime, expiresTime int64) {
	log := logger.Get(r)
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	groupid := g
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	// decode request
	var updateRequest apipub.UpdateGroupRequestBody
	if !DecodeBody(w, r, &updateRequest) {
		return
	}

	if updateRequest.JoinRequestAction == nil && updateRequest.Meta == nil && updateRequest.MaxMembers == nil && updateRequest.Password == nil && updateRequest.CanMembersInvite == nil {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.ENoValidParameters))
		return
	}

	oldJoinRequestAction := group.JoinRequestAction
	oldMeta := group.Meta
	oldMaxMember := group.MaxMembers
	oldPassword := group.Password
	oldCanMbmbersInvite := group.CanMembersInvite

	role := group.GetMemberRole(userid)
	propertyChanged := ""
	isLeader := false
	isLeaderOrMember := false
	didMetaChange := false
	if role == apipub.Nonmember && userid != constants.TrustedServer {
		log.Error().Err(err).Msgf("member not in group")
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
		return
	} else {
		isLeaderOrMember = true
	}
	// trusted server has leader level permissions
	if role == apipub.Leader || userid == constants.TrustedServer {
		isLeader = true
	}

	//Meta update should be allowed for all members but only if it's the only change being made
	if isLeaderOrMember && updateRequest.Meta != nil && group.Meta != updateRequest.Meta {
		if (updateRequest.Password != nil || updateRequest.JoinRequestAction != nil || updateRequest.MaxMembers != nil || updateRequest.CanMembersInvite != nil) && !isLeader {
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsOnlyMetaUpdateForMembers))
			return
		}

		mergedMeta := make(map[string]interface{})

		if group.Meta != nil {
			jb, _ := json.Marshal(*group.Meta)
			json.Unmarshal(jb, &mergedMeta)
		}

		ja, _ := json.Marshal(updateRequest.Meta)
		if len(ja) > api.Cfg.MaxMetaSize {
			api.resetUpdateGroupVals(r.Context(), group, oldMeta, oldMaxMember, oldJoinRequestAction, oldPassword, oldCanMbmbersInvite)
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMetaTooLong))
			return
		}

		if string(ja) != "{}" {

			json.Unmarshal(ja, &mergedMeta)

			jm, _ := json.Marshal(mergedMeta)
			if len(jm) > api.Cfg.MaxMetaSize {
				api.resetUpdateGroupVals(r.Context(), group, oldMeta, oldMaxMember, oldJoinRequestAction, oldPassword, oldCanMbmbersInvite)
				errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMergedMetaTooLong))
				return
			}
		} else {
			mergedMeta = make(map[string]interface{})
		}

		group.Meta = &mergedMeta

		propertyChanged += "meta "
		metaErr := api.Cache.SetGroupMeta(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
		if metaErr != nil {
			api.resetUpdateGroupVals(r.Context(), group, oldMeta, oldMaxMember, oldJoinRequestAction, oldPassword, oldCanMbmbersInvite)
			log.Error().Err(metaErr).Str("event", "failed to set group meta").Msgf("failed to set group meta")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidMeta))
			return
		} else {
			didMetaChange = true
		}

	}

	// any non meta change requires leader
	if role == apipub.Member && !didMetaChange {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsNotLeader))
		return
	}

	// Leader or trusted server only for all other changes
	if isLeader {
		maxGroupSize := 100
		if api.Cfg.MaxGroupSize > 0 {
			maxGroupSize = api.Cfg.MaxGroupSize
		}

		// update max members.  must be > 0.  smaller than global max group size setting.  only allow increase in group size.
		maxMembers := group.MaxMembers
		if updateRequest.MaxMembers != nil && maxMembers != *updateRequest.MaxMembers {
			maxMembers = *updateRequest.MaxMembers

			if maxMembers > 1 && maxMembers <= maxGroupSize && group.Members != nil && maxMembers >= len(*group.Members) {
				if group.MaxMembers != maxMembers {
					group.MaxMembers = maxMembers
					api.Cache.SetGroupMaxMembers(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
					propertyChanged += "maxMembers "
				}
			} else {
				log.Error().Err(err).Msgf("invalid max members")
				api.resetUpdateGroupVals(r.Context(), group, oldMeta, oldMaxMember, oldJoinRequestAction, oldPassword, oldCanMbmbersInvite)
				errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidMaxMembers))
				return
			}
		}

		if updateRequest.Password != nil && group.Password != updateRequest.Password {
			group.Password = utils.HashPassword(updateRequest.Password)
			api.Cache.SetGroupPassword(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
			propertyChanged += "password "
		}

		if updateRequest.CanMembersInvite != nil && group.CanMembersInvite != updateRequest.CanMembersInvite {
			group.CanMembersInvite = updateRequest.CanMembersInvite
			api.Cache.SetGroupCanMembersInvite(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
			propertyChanged += "canMembersInvite "
		}

		if updateRequest.JoinRequestAction != nil && group.JoinRequestAction != *updateRequest.JoinRequestAction {
			group.JoinRequestAction = *updateRequest.JoinRequestAction
			api.Cache.SetGroupJoinRequestAction(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
			propertyChanged += "joinRequestAction "
		}
	}

	if propertyChanged != "" {
		topic := group.Topic(tenant)
		msgType := messenger.MqttMessageTypeGroupModified
		data := apipub.MqttGroupModified{
			Action:   "modify",
			Groupid:  groupid,
			Property: &propertyChanged,
		}
		messenger.SendMqttMessage(r.Context(), api.Cfg, topic, msgType, &data)
	}

	// iterate members to get presence and filter linked accounts
	if group.Members != nil {
		var members []apipub.GroupMemberResponse
		memberids := make([]string, 0, len(*group.Members))
		for _, member := range *group.Members {
			memberids = append(memberids, member.Userid)
		}
		memberProfiles, _ := api.GetUserProfiles(r.Context(), memberids, true)
		var profile *apipub.UserProfileResponse

		for _, member := range *group.Members {
			if member.Name == nil || *member.Name == "" || member.Links == nil {
				profile = nil
				if memberProfiles != nil {
					for _, p := range *memberProfiles {
						if p.Userid == member.Userid {
							profile = p
							break
						}
					}
				}

				if profile != nil && profile.DisplayName != nil {
					member.Name = profile.DisplayName
				}
				if profile != nil && profile.Links != nil {
					member.Links = profile.Links
					if ost > -1 && userid != constants.TrustedServer {
						identity.FilterLinksByOST(member.Links, ost)
					}
				}
			}

			// prior to fetching presence records, update active group for records for member with current groupo state
			api.Cache.SetActiveGroup(r.Context(), group, productid, appid, sessionid, member.Userid, createdTime, expiresTime, true)

			memberSessionid := ""
			if member.Presence != nil {
				memberSessionid = member.Presence.ActiveSessionid
			}
			memPresence, _ := api.Cache.GetPresence(r.Context(), member.Userid, productid, memberSessionid)

			//we always want to set the member presence to the current presence
			//user could have cleared presence since the last group save
			member.Presence = memPresence
			members = append(members, member)
		}
		group.Members = &members
	}

	additionalInfo := make(map[string]string)
	if updateRequest.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*updateRequest.TeleMeta, &additionalInfo)
	}
	// set group ost to token ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = &ost

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupModify, userid, ost, []string{groupid}, &appid, &additionalInfo))

	group.OnlineServiceType = originalOST

	ReturnEmptyOK(w, r)
}

// resetUpdateGroupVals if there's an error when updating group, rollback all changes
func (api *SocialPublicAPI) resetUpdateGroupVals(ctx context.Context, group *apipub.GroupResponse, meta *map[string]interface{}, maxMembers int, joinRequestAction apipub.JoinRequestAction, password *string, canMembersInvite *bool) {

	group.Meta = meta
	api.Cache.SetGroupMeta(ctx, group, time.Duration(api.Cfg.TtlGroup)*time.Second)

	group.MaxMembers = maxMembers
	api.Cache.SetGroupMaxMembers(ctx, group, time.Duration(api.Cfg.TtlGroup)*time.Second)

	group.Password = utils.HashPassword(password)
	api.Cache.SetGroupPassword(ctx, group, time.Duration(api.Cfg.TtlGroup)*time.Second)

	group.CanMembersInvite = canMembersInvite
	api.Cache.SetGroupCanMembersInvite(ctx, group, time.Duration(api.Cfg.TtlGroup)*time.Second)

	group.JoinRequestAction = joinRequestAction
	api.Cache.SetGroupJoinRequestAction(ctx, group, time.Duration(api.Cfg.TtlGroup)*time.Second)

}

// GetGroup get a specific group
func (api *SocialPublicAPI) GetGroup(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	userid := token.Claims.Subject
	api.GetGroupHelper(w, r, g, userid, productid, ost)
}

// GetGroupHelper get a specific group
func (api *SocialPublicAPI) GetGroupHelper(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, userid string, productid string, ost apipub.OnlineServiceType) {
	log := logger.Get(r)
	groupid := g
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	if userid != constants.TrustedServer {
		role := group.GetMemberRole(userid)
		if role == apipub.Nonmember {
			log.Error().Err(err).Msgf("member %s is not in group %s", userid, groupid)
			errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
			return
		}
	}

	// don't send back any passwords
	group.Password = nil

	// iterate members to get presence and filter linked accounts
	if group.Members != nil {
		var members []apipub.GroupMemberResponse
		memberids := make([]string, 0, len(*group.Members))
		for _, member := range *group.Members {
			memberids = append(memberids, member.Userid)
		}
		memberProfiles, _ := api.GetUserProfiles(r.Context(), memberids, true)
		var profile *apipub.UserProfileResponse

		for _, member := range *group.Members {
			if member.Name == nil || *member.Name == "" || member.Links == nil {
				profile = nil
				if memberProfiles != nil {
					for _, p := range *memberProfiles {
						if p.Userid == member.Userid {
							profile = p
							break
						}
					}
				}

				if profile != nil && profile.DisplayName != nil {
					member.Name = profile.DisplayName
				}
				if profile != nil && profile.Links != nil {
					member.Links = profile.Links
					if userid != constants.TrustedServer {
						identity.FilterLinksByOST(member.Links, ost)
					}
				}
			}

			sessionid := ""
			if member.Presence != nil {
				sessionid = member.Presence.ActiveSessionid
			}
			memPresence, _ := api.Cache.GetPresence(r.Context(), member.Userid, productid, sessionid)

			//we always want to set the member presence to the current presence
			//user could have cleared presence since the last group save
			member.Presence = memPresence
			members = append(members, member)
		}
		group.Members = &members
	}

	if group.Members != nil && len(*group.Members) == 0 {
		group.Members = nil
	}
	if group.MembershipRequests != nil && len(*group.MembershipRequests) == 0 {
		group.MembershipRequests = nil
	}

	ReturnOK(w, r, group)
}

// GetGroups get a list of groups user is in
func (api *SocialPublicAPI) GetGroups(w http.ResponseWriter, r *http.Request, params apipub.GetGroupsParams) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	limit := 100
	limit64 := int64(limit)
	if params.Limit != nil && *params.Limit >= 1 && *params.Limit <= 100 {
		limit = *params.Limit
		limit64 = int64(limit)
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	nextVal := ""
	if params.Next != nil {
		nextVal = *params.Next
	}

	var ctx context.Context
	if r == nil || r.Context() == nil {
		ctx = context.Background()
	} else {
		ctx = r.Context()
	}

	groups, next, err := api.Cache.GetUserGroups(ctx, userid, productid, &limit64, &nextVal)
	if err != nil {
		log.Error().Err(err).Msgf("failed to read groups from Cache")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if groups == nil {
		log.Info().Err(err).Msgf("user has no groups")
	}

	var groupsResponse apipub.GroupsNext
	groupsResponse.Items = []apipub.GroupResponse{}
	if groups != nil {
		for _, group := range *groups {
			// don't send back any passwords
			group.Password = nil

			// iterate members to get presence and filter linked accounts
			if group.Members != nil {
				var members []apipub.GroupMemberResponse
				memberids := make([]string, 0, len(*group.Members))
				for _, member := range *group.Members {
					memberids = append(memberids, member.Userid)
				}
				memberProfiles, _ := api.GetUserProfiles(r.Context(), memberids, true)
				var profile *apipub.UserProfileResponse

				for _, member := range *group.Members {
					if member.Name == nil || *member.Name == "" || member.Links == nil {
						profile = nil
						if memberProfiles != nil {
							for _, p := range *memberProfiles {
								if p.Userid == member.Userid {
									profile = p
									break
								}
							}
						}

						if profile != nil && profile.DisplayName != nil {
							member.Name = profile.DisplayName
						}
						if profile != nil && profile.Links != nil {
							member.Links = profile.Links
						}
					}
					if member.Links != nil {
						identity.FilterLinksByOST(member.Links, ost)
					}

					sessionid := ""
					if member.Presence != nil {
						sessionid = member.Presence.ActiveSessionid
					}
					memPresence, _ := api.Cache.GetPresence(r.Context(), member.Userid, productid, sessionid)
					member.Presence = memPresence
					members = append(members, member)
				}
				group.Members = &members
			}
			if group.Members != nil && len(*group.Members) == 0 {
				group.Members = nil
			}
			if group.MembershipRequests != nil && len(*group.MembershipRequests) == 0 {
				group.MembershipRequests = nil
			}
			groupsResponse.Items = append(groupsResponse.Items, *group)
		}
	}
	if next != "" {
		groupsResponse.Nextid = &next
	}
	ReturnOK(w, r, groupsResponse)
}

// KickMemberFromGroup kick member from group
func (api *SocialPublicAPI) KickMemberFromGroup(w http.ResponseWriter, r *http.Request, groupId, userId string, params apipub.KickMemberFromGroupParams) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	productid := token.Claims.ProductID
	requestorid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime

	api.KickOrLeaveGroup(w, r, groupId, requestorid, userId, params.Reason, productid, appid, sessionid, ost, createdTime, expiresTime)
}

// LeaveGroup leave the group
func (api *SocialPublicAPI) LeaveGroup(w http.ResponseWriter, r *http.Request, groupId string) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	productid := token.Claims.ProductID
	requestorid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	sessionid := token.Claims.SessionID
	createdTime := token.Claims.CreatedTime
	expiresTime := token.Claims.ExpiresTime

	api.KickOrLeaveGroup(w, r, groupId, requestorid, token.Claims.Subject, nil, productid, appid, sessionid, ost, createdTime, expiresTime)
}

// KickOrLeaveGroup kick or leave a group
func (api *SocialPublicAPI) KickOrLeaveGroup(w http.ResponseWriter, r *http.Request, groupid, requestorid, userid string, reason *string, productid, appid, sessionid string, ost apipub.OnlineServiceType, createdTime, expiresTime int64) {
	log := logger.Get(r)
	targetUserId := userid

	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("groupid", groupid).Str("productid", productid).Msg("failed to get group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EGroupsGeneric))
		return
	}

	if group == nil {
		log.Error().Str("productid", productid).Str("groupid", groupid).Msg("could not find a group")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}
	originalLeader := group.GetLeader()

	eventType, wasDisbanded, err := api.Cache.KickOrLeaveHelper(r.Context(), group, requestorid, targetUserId, reason)
	if err != nil {
		log.Error().Err(err).Msgf("requester %s failed to leave or kick %s from %s", requestorid, targetUserId, groupid)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsLeaveOrKickFailed))
		return
	}
	if wasDisbanded {
		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupDisband, userid, ost, []string{groupid}, &appid, nil))
	}
	//clear the active group if this group is marked as active
	api.clearActiveGroup(r.Context(), targetUserId, productid, &group.Groupid)

	// update group sizes in other group members active group records if this group is their active group
	if group.Members != nil && len(*group.Members) > 0 {
		for _, checkMemberActive := range *group.Members {
			if checkMemberActive.Userid != userid {
				api.Cache.SetActiveGroup(r.Context(), group, productid, appid, sessionid, checkMemberActive.Userid, createdTime, expiresTime, true)
			}
		}
	}

	additionalInfo := make(map[string]string)
	// set group ost to token ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = &ost

	switch eventType {
	case apipub.ChatMessageEventTypeKicked:
		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupKick, requestorid, ost, []string{targetUserId}, &appid, &additionalInfo))
	case apipub.ChatMessageEventTypeLeft:
		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupLeave, requestorid, ost, []string{targetUserId}, &appid, &additionalInfo))
	}

	// if target was group leader send group leader change telemetry
	if group.Members != nil && len(*group.Members) > 0 && group.GetLeader().Userid != originalLeader.Userid {
		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupLeaderChange, requestorid, ost, []string{targetUserId}, &appid, &additionalInfo))
	}
	group.OnlineServiceType = originalOST
	ReturnEmptyOK(w, r)
}

// GetGroupMembers list group members
func (api *SocialPublicAPI) GetGroupMembers(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	groupid := g
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	// get the group item
	var group *apipub.GroupResponse
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	if !group.IsMember(userid) {
		log.Error().Err(err).Msgf("member %s is not in group %s", userid, groupid)
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
		return
	}

	var members apipub.MembersNext
	memberids := make([]string, 0, len(*group.Members))
	for _, member := range *group.Members {
		memberids = append(memberids, member.Userid)
	}
	memberProfiles, _ := api.GetUserProfiles(r.Context(), memberids, true)
	var profile *apipub.UserProfileResponse

	for _, member := range *group.Members {
		if member.Name == nil || *member.Name == "" || member.Links == nil {
			profile = nil
			if memberProfiles != nil {
				for _, p := range *memberProfiles {
					if p.Userid == member.Userid {
						profile = p
						break
					}
				}
			}
			if profile != nil && profile.DisplayName != nil {
				member.Name = profile.DisplayName
			}
			if profile != nil && profile.Links != nil {
				member.Links = profile.Links
			}
		}
		if member.Links != nil {
			identity.FilterLinksByOST(member.Links, ost)
		}

		sessionid := ""
		if member.Presence != nil {
			sessionid = member.Presence.ActiveSessionid
		}
		memPresence, _ := api.Cache.GetPresence(r.Context(), member.Userid, productid, sessionid)
		member.Presence = memPresence
		members.Items = append(members.Items, member)
	}
	ReturnOK(w, r, members)
}

// GetGroupMember get group member
func (api *SocialPublicAPI) GetGroupMember(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, memberid apipub.PUserid) {
	groupid := g
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	// get the group item
	var group *apipub.GroupResponse
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	if !group.IsMember(userid) {
		log.Error().Err(err).Str("groupid", groupid).Str("userid", userid).Msg("cannot request if not a member of group")
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
		return
	}

	if !group.IsMember(memberid) {
		log.Error().Err(err).Str("groupid", groupid).Str("userid", memberid).Msgf("target user not in group")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsMemberNotInGroup))
		return
	}

	member := group.GetMember(memberid)
	if member == nil {
		log.Error().Err(err).Str("memberid", memberid).Str("groupid", groupid).Msg("member not in group")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsMemberNotInGroup))
		return
	}

	if member.Name == nil || *member.Name == "" || member.Links == nil {

		profile, _ := api.getUserProfile(r.Context(), member.Userid, true)
		if profile != nil && profile.DisplayName != nil {
			member.Name = profile.DisplayName
		}
		if profile != nil && profile.Links != nil {
			member.Links = profile.Links
		}
	}

	if member.Links != nil {
		identity.FilterLinksByOST(member.Links, ost)
	}

	sessionid := ""
	if member.Presence != nil {
		sessionid = member.Presence.ActiveSessionid
	}
	memPresence, _ := api.Cache.GetPresence(r.Context(), member.Userid, productid, sessionid)
	member.Presence = memPresence

	ReturnOK(w, r, member)
}

// UpdateGroupMember update group member
func (api *SocialPublicAPI) UpdateGroupMember(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, memberid apipub.PUserid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	api.UpdateGroupMemberHelper(w, r, g, memberid, userid, productid, appid, ost)
}

// UpdateGroupMemberHelper update group member
func (api *SocialPublicAPI) UpdateGroupMemberHelper(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, memberid apipub.PUserid, userid, productid, appid string, ost apipub.OnlineServiceType) {
	log := logger.Get(r)
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	groupid := g

	// get the group item
	var group *apipub.GroupResponse
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	// get the request
	var updateRequest apipub.UpdateGroupMemberRequest
	if !DecodeBody(w, r, &updateRequest) {
		return
	}

	// check that the requested role is valid
	if updateRequest.Role != apipub.Leader &&
		updateRequest.Role != apipub.Member {
		log.Error().Msgf("invalid role, valid roles are %s and %s", apipub.Leader, apipub.Member)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidRole))
		return
	}

	if !group.IsMember(userid) && userid != constants.TrustedServer {
		log.Error().Err(err).Msgf("user %s is not in group %s", userid, groupid)
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
		return
	}

	// only the leader can edit the group member
	if group.GetMemberRole(userid) != apipub.Leader && userid != constants.TrustedServer {
		log.Error().Err(err).Msgf("user %s is not group leader", userid)
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsNotLeader))
		return
	}

	// update the member
	member := group.GetMember(memberid)
	if member == nil {
		log.Error().Err(err).Msgf("member %s is nil", memberid)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsMemberNotInGroup))
		return
	}

	//change role if requested
	currentRole := member.Role
	if currentRole != updateRequest.Role {
		currentLeader := group.GetLeader()

		// cannot demote self leader
		if currentLeader.Userid == memberid && updateRequest.Role != apipub.Leader {
			log.Error().Err(err).Msgf("cannot demote self leader")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidRole))
			return
		}

		//If the request is changing role to group leader.  Remove group leader from current Leader.
		if updateRequest.Role == apipub.Leader {
			group.RemoveMember(memberid)
			member.Role = apipub.Leader
			group.RemoveMember(currentLeader.Userid)
			currentLeader.Role = apipub.Member
			group.AddMemberIfNotExist(member)
			group.AddMemberIfNotExist(currentLeader)
			err = api.Cache.SetGroup(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
			if err != nil {
				log.Error().Err(err).Msgf("failed to set group leader %s for group %s", memberid, groupid)
				errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsGroupMemberModifyFailed))
				return
			}
			additionalInfo := make(map[string]string)
			if updateRequest.TeleMeta != nil {
				utils.ConvertMapInterfaceToMapString(*updateRequest.TeleMeta, &additionalInfo)
			}
			// set group ost to token ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
			originalOST := group.OnlineServiceType
			group.OnlineServiceType = &ost

			api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupLeaderChange, currentLeader.Userid, ost, []string{memberid}, &appid, &additionalInfo))

			group.OnlineServiceType = originalOST

		}
	}

	if group.LeaderCount() < 1 {
		log.Error().Err(err).Msgf("group %s does not have a leader", groupid)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMissingLeader))
		return
	}

	err = api.Cache.SetGroup(r.Context(), group, time.Duration(api.Cfg.TtlGroup)*time.Second)
	if err != nil {
		log.Error().Err(err).Str("groupid", groupid).Str("productid", productid).Msg("failed to set group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed))
		return
	}

	// send MQTT message
	msgType := messenger.MqttMessageTypeGroupMembersModified
	data := apipub.MqttGroupMemberModified{
		Action:   "roleChanged",
		Userid:   member.Userid,
		Reason:   "modified",
		PreRole:  currentRole,
		PostRole: member.Role,
		Groupid:  groupid,
	}

	messenger.SendMqttMessage(r.Context(), api.Cfg, group.Topic(tenant), msgType, &data)

	// get member diosplay name and links
	profile, err := api.getUserProfile(r.Context(), member.Userid, true)
	if profile != nil {
		member.Name = profile.DisplayName
		if profile.Links != nil {
			member.Links = profile.Links
			if userid != constants.TrustedServer {
				identity.FilterLinksByOST(member.Links, ost)
			}
		}
	} else {
		log.Error().Err(err).Msg("Failed to get user profile for group leader on create")
	}

	ReturnEmptyOK(w, r)
}

// SendControlMessage send control message
func (api *SocialPublicAPI) SendControlMessage(w http.ResponseWriter, r *http.Request, g apipub.PGroupid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productid := token.Claims.ProductID
	userid := token.Claims.Subject
	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	if utils.ArrayStrContainsString(r.Header[constants.KContentType], constants.KApplicationOctetStream) {
		api.SendBinControlMessageHelper(w, r, g, userid, productid, appid, ost)
		return
	}

	// decode request
	var controlMessage apipub.SendControlMessageRequestBody
	if !DecodeBody(w, r, &controlMessage) {
		return
	}

	api.SendControlMessageHelper(w, r, g, userid, productid, appid, ost, &controlMessage)
}

func (api *SocialPublicAPI) SendControlMessageHelper(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, userid, productid, appid string, ost apipub.OnlineServiceType, controlMessage *apipub.SendControlMessageRequestBody) {
	log := logger.Get(r)
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	groupid := g

	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Msgf("could not find group for %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	// verify user is a member of the group
	if userid != constants.TrustedServer {
		role := group.GetMemberRole(userid)
		if role == apipub.Nonmember {
			log.Error().Msgf("user %s can't send a control message if you aren't in the group", userid)
			errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
			return
		}
	}

	if len(controlMessage.Payload) > api.Cfg.MaxControlMessgeLength {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsControlMessageTooLong))
		return
	}

	topic := group.Topic(tenant)
	msgType := messenger.MqttMessageTypeGroupControlMessage
	var mqttCtrlMsg apipub.MqttControlMessage
	mqttCtrlMsg.Payload = controlMessage.Payload
	mqttCtrlMsg.Senderid = &userid
	mqttCtrlMsg.Groupid = &groupid
	mqttCtrlMsg.Event = controlMessage.Event
	messenger.SendMqttMessage(r.Context(), api.Cfg, topic, msgType, &mqttCtrlMsg)

	additionalInfo := make(map[string]string)
	if controlMessage.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*controlMessage.TeleMeta, &additionalInfo)
	}
	// set group ost to token ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = &ost

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupControlMsg, userid, ost, []string{groupid}, &appid, &additionalInfo))

	group.OnlineServiceType = originalOST

	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) SendBinControlMessageHelper(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, userid, productid, appid string, ost apipub.OnlineServiceType) {
	log := logger.Get(r)
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	groupid := g

	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}
	if group == nil {
		log.Error().Err(err).Msgf("could not find group for %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	// verify user is a member of the group
	if userid != constants.TrustedServer {
		role := group.GetMemberRole(userid)
		if role == apipub.Nonmember {
			log.Error().Msgf("user %s can't send a control message if you aren't in the group", userid)
			errs.Return(w, r, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup))
			return
		}
	}

	bodyBytes, _ := io.ReadAll(r.Body)
	r.Body.Close()

	if len(bodyBytes) > api.Cfg.MaxControlMessgeLength {
		errs.Return(w, r, errs.New(http.StatusRequestEntityTooLarge, errs.EGroupsControlMessageTooLong))
	}

	topic := group.Topic(tenant)
	msgType := messenger.MqttMessageTypeGroupControlMessage

	// msgType not used. do we need it?
	err = messenger.SendBinMqttMessage(r.Context(), api.Cfg, topic, msgType, &bodyBytes)
	if err != nil {
		var sErr errs.SocialErrorInterface
		if errors.As(err, &sErr) {
			errs.Return(w, r, errs.New(sErr.GetHttpErrorCode(), sErr.GetSocialErrorCode()))
		}
		return
	}
	additionalInfo := make(map[string]string)
	// set group ost to token ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	originalOST := group.OnlineServiceType
	group.OnlineServiceType = &ost

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupControlMsg, userid, ost, []string{groupid}, &appid, &additionalInfo))

	group.OnlineServiceType = originalOST

	ReturnEmptyOK(w, r)
}

// UpdateGroupMemberMeta updates group member's metadata
func (api *SocialPublicAPI) UpdateGroupMemberMeta(w http.ResponseWriter, r *http.Request, groupid apipub.Groupid, memberid apipub.Dnaid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	productId := token.Claims.ProductID
	group, err := api.Cache.GetGroup(r.Context(), productId, groupid)
	log := logger.Get(r)
	if err != nil {
		log.Error().Err(err).Str("productId", productId).Str("groupId", groupid).Msg("failed to read group")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}

	if group == nil {
		log.Error().Err(err).Str("productId", productId).Str("groupId", groupid).Msg("group does not exist")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	for _, member := range *group.Members {
		if member.Userid != memberid {
			continue
		}

		userid := token.Claims.Subject

		// Member can only update their own meta
		if member.Userid != userid {
			log.Error().Err(err).Str("memberId", memberid).Str("groupId", groupid).Msg("member can only update their own meta")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsMemberUpdateFailed))
			return
		}

		var updateRequest apipub.UpdateGroupMemberMetaRequestBody
		if !DecodeBody(w, r, &updateRequest) {
			log.Error().Err(err).Str("memberId", memberid).Str("groupId", groupid).Msg("failed to decode the request body")
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EGroupsGeneric))
			return
		}

		// If the member has no metadata, just save the metadata included the request.
		//if (*group.Members)[index].Meta == nil {
		if member.Meta == nil {
			member.Meta = &updateRequest.Meta
		} else {
			if *member.MetaLastUpdated < updateRequest.Timestamp {
				*member.Meta = updateRequest.Meta
			}
		}

		member.MetaLastUpdated = &updateRequest.Timestamp
		err = api.Cache.UpdateGroupMember(r.Context(), group, &member)
		if err != nil {
			log.Error().Err(err).Str("memberId", memberid).Str("groupId", groupid).Msg("failed to update the group member")
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EGroupsMemberUpdateFailed))
			return
		}

		appid := token.Claims.Issuer
		ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupModify, userid, ost, []string{groupid}, &appid, nil))

		// send MQTT message to notify all members
		msgType := messenger.MqttMessageTypeGroupMembersModified
		metaInBytes, err := json.Marshal(*member.Meta)
		if err == nil {
			data := apipub.MqttGroupMemberModified{
				Action:          "memberMetaUpdated",
				Userid:          member.Userid,
				Reason:          string(metaInBytes),
				PreRole:         member.Role,
				PostRole:        member.Role,
				Groupid:         groupid,
				MetaLastUpdated: &updateRequest.Timestamp,
			}

			tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
			messenger.SendMqttMessage(r.Context(), api.Cfg, group.Topic(tenant), msgType, &data)
		}

		ReturnEmptyOK(w, r)
		return
	}

	// Member doesn't exist in the group.
	log.Error().Err(err).Str("memberId", memberid).Str("groupId", groupid).Msg("member not in group")
	errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsMemberNotInGroup))
}

// SyncSessionToGroup requests a sync from first party session to t2 group
func (api *SocialPublicAPI) SyncSessionToGroup(w http.ResponseWriter, r *http.Request, pOnlineServiceType apipub.POnlineServiceType, pGroupid apipub.PGroupid) {

	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := pOnlineServiceType
	groupid := pGroupid

	log.Debug().Str("userid", userid).Str("productid", productid).Int("ost", int(ost)).Str("groupid", groupid).Str("event", "sync_session_to_group").Msg("sync_session_to_group")

	ReturnEmptyOK(w, r)
}
