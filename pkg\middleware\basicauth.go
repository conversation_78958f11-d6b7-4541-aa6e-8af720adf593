package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"

	"github.com/2kg-coretech/dna-common/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// BasicAuth implements a simple middleware handler for adding basic http auth to a route.
func BasicAuth(client identity.Identity) func(next http.Handler) http.Handler {
	realmName := "Social Admin Web Page"
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// we allow urls for health checks
			if utils.IgnoreRequest(r) {
				next.ServeHTTP(w, r)
				return
			}

			ctx := r.Context()

			appID, appSecret, ok := r.BasicAuth()
			if !ok {
				BasicAuthFailed(w, r, realmName)
				return
			}

			// validate
			app, err := client.GetApplication(appID)
			if err != nil || app == nil {
				logger.Get(r).Error().Err(err).Msgf("failed to get app from dna %s, app %v", appID, app)
				BasicAuthFailed(w, r, realmName)
				return
			}

			if appID != app.ID || appSecret != app.Secret {
				BasicAuthFailed(w, r, realmName)
				return
			}

			label := r.Header.Get(constants.KT2GPLabel)
			if strings.ToLower(label) == "pdi" {
				label = "pdi"
			} else {
				label = "dna"
			}
			ctx = context.WithValue(ctx, constants.T2GPCtxTenant, label)

			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}
}

func BasicAuthFailed(w http.ResponseWriter, r *http.Request, realm string) {
	w.Header().Add("WWW-Authenticate", fmt.Sprintf(`Basic realm="%s"`, realm))
	errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EHttp+http.StatusUnauthorized))
}
