import i18next from 'i18next';
import { getContext, setContext } from 'svelte';
import type { Readable, Writable } from 'svelte/store';
import { CONTEXT_KEY_I18N } from '../constant';
import { I18nService } from '../services';
import {
  createLocal,
  createTranslate,
  LocaleStore,
  TranslateStore,
} from '../stores';

export type I18nContext = {
  t: Readable<TType>;
  locale: Writable<string>;
};

export type TType = (
  text: string,
  replacements?: Record<string, unknown>
) => string;

export interface TranslationService {
  locale: LocaleStore;
  translate: TranslateStore<TType>;
}

export class Translator implements TranslationService {
  public locale: LocaleStore;
  public translate: TranslateStore<TType>;

  constructor(i18nService: I18nService) {
    i18nService.initialize();
    this.locale = createLocal(i18nService);
    this.translate = createTranslate(this.locale, i18nService);
  }
}

export const initI18nContext = (initialLanguage: string) => {
  // Initialize our services
  const i18n = new I18nService(i18next, initialLanguage);
  const tranlator = new Translator(i18n);

  // Setting the Svelte context
  setI18nContext({
    t: tranlator.translate,
    locale: tranlator.locale,
  });

  return {
    i18n,
  };
};

export const setI18nContext = (context: I18nContext) => {
  return setContext<I18nContext>(CONTEXT_KEY_I18N, context);
};

// To make retrieving the t function easier.
export const getI18nContext = () => {
  return getContext<I18nContext>(CONTEXT_KEY_I18N);
};
