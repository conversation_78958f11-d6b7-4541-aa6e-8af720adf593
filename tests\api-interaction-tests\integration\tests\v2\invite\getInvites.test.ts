import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

// eslint-disable-next-line max-lines-per-function
describe('', () =>{
  let usersTwok: TwokAccounts;
  let groupIdA: string;
  let groupIdB: string;
  let groupIdC: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(3, ["leader1", "leader2", "member1"]);
    await usersTwok.loginAll({});

    let r = await socialApi.createGroup(
      usersTwok.acct["leader1"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupIdA = socialApi.getGroupId(r);

    r = await socialApi.createGroup(
      usersTwok.acct["leader1"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupIdB = socialApi.getGroupId(r);

    r = await socialApi.createGroup(
      usersTwok.acct["leader2"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupIdC = socialApi.getGroupId(r);
  });

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["leader1"], groupIdA);
    await socialApi.deleteGroup(usersTwok.acct["leader1"], groupIdB);
    await socialApi.deleteGroup(usersTwok.acct["leader2"], groupIdC);
    await usersTwok.logoutAll({});
    done();
  });

  it('User get all invited[public v2 happy]', async () => {
    let testCase = {
      description: "user receives invitations from multiple groups",
      expected: "the user gets all the invitations"
    };

    // leader1 sends a groupIdA invitation to member1
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader1"],
      groupIdA,
      {},
      usersTwok.acct["member1"].publicId
    );

    // Leader1 sends a groupIdB invitation to member1
    r = await socialApi.invite(
      usersTwok.acct["leader1"],
      groupIdB,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Leader2 sends a groupIdC invitation to member1
    r = await socialApi.invite(
      usersTwok.acct["leader2"],
      groupIdC,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    const actualInviteInfo: request.Response = await socialApi.getInvite(
      usersTwok.acct["member1"]
    );

    // expect member1 gets all the invitations
    const expectedInviteInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.arrayContaining([
          expect.objectContaining({
            approverid: usersTwok.acct["leader1"].publicId,
            groupid: groupIdA,
            memberid: usersTwok.acct["member1"].publicId,
            // status: 'invited',
          }),
          expect.objectContaining({
            approverid: usersTwok.acct["leader1"].publicId,
            groupid: groupIdB,
            memberid: usersTwok.acct["member1"].publicId,
            // status: 'invited',
          }),
          expect.objectContaining({
            approverid: usersTwok.acct["leader2"].publicId,
            groupid: groupIdC,
            memberid: usersTwok.acct["member1"].publicId,
            // status: 'invited',
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualInviteInfo).toMatchObject(expectedInviteInfo)},
      testCase,
      {
        resp: actualInviteInfo,
        additionalInfo: {
          "fail reason": "some invitations are not received"
        }
      }
    );
  });
});
