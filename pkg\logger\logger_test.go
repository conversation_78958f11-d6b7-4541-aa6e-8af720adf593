package logger

import (
	"context"
	"crypto/tls"
	"net/http"
	"runtime/debug"
	"testing"

	"github.com/franela/goblin"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func TestSetupLogger(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("SetupLogger", func() {
		g.It("should be setup correctly", func() {
			SetupLogger(true)
			g.Assert(zerolog.GlobalLevel()).Equal(zerolog.DebugLevel)
		})
	})
}

type logWriter struct {
	Body []byte
}

func (l *logWriter) Write(p []byte) (n int, err error) {
	l.Body = append(l.Body, p...)
	return len(l.Body), nil
}

func (l *logWriter) WriteLevel(level zerolog.Level, p []byte) (n int, err error) {
	return l.Write(p)
}

func createLogger() (*StructuredLogger, *logWriter) {
	writer := &logWriter{}
	zl := zerolog.New(writer)
	l := &StructuredLogger{
		Logger: &zl,
	}
	return l, writer
}

func TestNewLogger(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("Logger", func() {
		g.It("should create a logger successfully", func() {
			l := NewLogger()
			g.Assert(l).IsNotNil()
		})

		g.It("should create a logger successfully", func() {
			l, writer := createLogger()
			ctx := context.Background()
			ctx = context.WithValue(ctx, middleware.RequestIDKey, "request_id_value")
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
			r.RequestURI = "/foo"
			InvalidSigJWT := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUwNzQ3NzMzLCJydGkiOiIwMmQ4ZjNlZmI0MGI0NTliOWMzMzExMzY4Y2Y5YmFiMyIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiZWZlNjYxMzA4MjY1NDM4NWI4MTUwYTE2ZDZjOTEzMmMiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6OTk5OTk5OTk5OSwiaWF0IjoxNjUwNzQwNTMzLCJqdGkiOiI3Y2VhYTFmN2FiYjU0MmQxYjI3ZmQ2NTk3ZTVlNzk0NSIsImxhdCI6MzkuMDQ2OX0.FHd5r4KbS61gvbAZ_EWCbpBuYi2IYJjfBNgPZG_jAm8"
			r.Header.Add("Authorization", "Bearer "+InvalidSigJWT)
			ctx = context.WithValue(ctx, BearerAuthJWT, InvalidSigJWT)
			r = r.WithContext(ctx)
			r.TLS = &tls.ConnectionState{}
			e := l.NewLogEntry(r)
			g.Assert(e).IsNotNil()

			e.Write(200, 123, r.Header, 100, &map[string]string{"foo": "bar"})
			g.Assert(len(writer.Body) > 0).IsTrue()
			// can't do expected anymore because of timestamp
			// expected := "{\"level\":\"info\",\"request_id\":\"request_id_value\",\"git_revision\":\"\",\"jwt\":{\"jti\":\"7ceaa1f7abb542d1b27fd6597e5e7945\",\"iss\":\"3bb92115af724e509f639113b0d521f8\",\"sub\":\"b287e655461f4b3085c8f244e394ff7e\",\"iat\":1650740533,\"exp\":9999999999,\"gid\":\"7b02a7e17589467f88b365ad5bb1c2b7\",\"pid\":\"0f5e1d57ea994a47ba593cbaad51d9f9\",\"loc\":\"en-US\",\"cty\":\"Ashburn\",\"ctr\":\"US\",\"lat\":39.0469,\"lon\":-77.4903,\"rti\":\"02d8f3efb40b459b9c3311368cf9bab3\",\"rex\":1650747733,\"aty\":3,\"agp\":5,\"sid\":\"efe6613082654385b8150a16d6c9132c\",\"ver\":true,\"dob\":\"+UynUrmf7JZ+2weQLWM26A==\"},\"dd.span_id\":0,\"dd.trace_id\":0,\"method\":\"GET\",\"proto\":\"HTTP/1.1\",\"remote_ip\":\"\",\"scheme\":\"https\",\"uri\":\"https://example.com/foo\",\"user_agent\":\"\",\"bytes_out\":123,\"elapsed_ms\":0.0001,\"status\":200,\"ts\":**********,\"caller\":\"C:/Repos/t2gp-social-service/pkg/logger/logger.go:134\",\"message\":\"GET /foo\"}\n"
			// g.Assert(string(writer.Body)).Equal(expected)
		})

		g.It("should skip certain paths or user agents", func() {
			l, writer := createLogger()
			ctx := context.Background()
			paths := []string{"/health", "/favicon.ico"}
			for _, path := range paths {
				r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com"+path, nil)
				e := l.NewLogEntry(r)
				g.Assert(e).IsNotNil()

				e.Write(200, 123, r.Header, 100, nil)
				g.Assert(len(writer.Body)).IsZero()
			}

			userAgents := []string{"ELB-HealthChecker", "kube-probe"}
			for _, ua := range userAgents {
				r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)
				r.Header.Set("User-Agent", ua)
				e := l.NewLogEntry(r)
				g.Assert(e).IsNotNil()

				e.Write(200, 123, r.Header, 100, nil)
				g.Assert(len(writer.Body)).IsZero()
			}
		})

		g.It("should a panic log successfully", func() {
			l, writer := createLogger()
			ctx := context.Background()
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/panic", nil)
			e := l.NewLogEntry(r)
			g.Assert(e).IsNotNil()
			e.Panic(map[string]string{}, debug.Stack())
			g.Assert(len(writer.Body)).IsNotZero()
			g.Assert(utils.StringContainsSubstr(string(writer.Body), "!!! Panic !!!", "runtime/debug.Stack()")).IsTrue()
		})

		g.It("should get logger from context or request", func() {
			l, _ := createLogger()
			ctx := context.Background()
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)

			newLogger := Get(r)
			g.Assert(newLogger).IsNotNil()
			g.Assert(newLogger).Equal(&log.Logger)

			newLogger = FromContext(r.Context())
			g.Assert(newLogger).IsNotNil()
			g.Assert(newLogger).Equal(&log.Logger)

			e := l.NewLogEntry(r)
			ctx = context.WithValue(ctx, middleware.LogEntryCtxKey, e)
			r = r.WithContext(ctx)

			newLogger = Get(r)
			g.Assert(newLogger).IsNotNil()

			newLogger = FromContext(r.Context())
			g.Assert(newLogger).IsNotNil()
		})
	})
}
