import { EventService } from '../event';
import type { SubscriberFunction, TransportService } from '../transport';
import { LogServiceMock } from './logService.mock';

export const transportServiceMock: TransportService = {
  logService: LogServiceMock,
  publishEvent: (eventName: string, data?: unknown) => {
    EventService.send(eventName, data);
  },
  subscribeEvent: (eventName: string, subscriber: SubscriberFunction) => {
    return EventService.on(eventName, subscriber);
  },
  subscribeEventOnce: (eventName: string, subscriber: SubscriberFunction) => {
    EventService.once(eventName, subscriber);
  },
  unsubscribe: (eventName: string) => {
    EventService.off(eventName);
  },
  unbind: token => {
    EventService.unbind(token);
  },
};
