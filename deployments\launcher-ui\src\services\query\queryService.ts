import type { QueryObserverResult } from '@sveltestack/svelte-query';
import {
  DefaultOptions,
  notifyManager,
  QueryClient,
  QueryFunction,
  QueryKey,
  QueryObserver,
  UseQueryOptions,
  UseQueryStoreResult,
} from '@sveltestack/svelte-query';
import { readable } from 'svelte/store';
import { parseQueryArgs, setBatchCalls } from './utils';

export declare type QueryResult<TData = unknown, TError = unknown> = Omit<
  QueryObserverResult<TData, TError>,
  'refetch' | 'remove'
>;

export declare type QueryStoreResult<
  TQueryFnData = unknown,
  TError = unknown,
  TData = TQueryFnData
> = {
  refetch: () => void;
} & UseQueryStoreResult<TQueryFnData, TError, TData>;

export interface IQueryService<
  TQueryFnData = unknown,
  TError = unknown,
  TData = TQueryFnData
> {
  setQuery: (
    arg1: QueryKey | UseQueryOptions<TQueryFnData, TError, TData>,
    arg2?:
      | QueryFunction<TQueryFnData>
      | UseQueryOptions<TQueryFnData, TError, TData>,
    arg3?: UseQueryOptions<TQueryFnData, TError, TData>
  ) => QueryStoreResult<TQueryFnData, TError, TData>;
  mount: () => void;
  unmount: () => void;
}

export class QueryService implements IQueryService {
  private queryClient: QueryClient;

  constructor(queryOptions?: DefaultOptions) {
    this.queryClient = new QueryClient({
      defaultOptions: queryOptions,
    });
  }

  mount(): void {
    this.queryClient.mount();
  }

  setQuery<TQueryFnData = unknown, TError = unknown, TData = TQueryFnData>(
    arg1: QueryKey | UseQueryOptions<TQueryFnData, TError, TData>,
    arg2?:
      | QueryFunction<TQueryFnData>
      | UseQueryOptions<TQueryFnData, TError, TData>,
    arg3?: UseQueryOptions<TQueryFnData, TError, TData>
  ): { refetch: () => void } & UseQueryStoreResult<
    TQueryFnData,
    TError,
    TData
  > {
    const options = parseQueryArgs(arg1, arg2, arg3);
    let defaultedOptions = this.queryClient.defaultQueryObserverOptions(
      options
    );
    // Include callbacks in batch renders
    defaultedOptions = setBatchCalls<
      UseQueryOptions<TQueryFnData, TError, TData>
    >(defaultedOptions);
    const observer = new QueryObserver<TQueryFnData, TError, TData>(
      this.queryClient,
      defaultedOptions
    );

    const { subscribe } = readable(observer.getCurrentResult(), set => {
      return observer.subscribe(notifyManager.batchCalls(set));
    });

    const client = this.queryClient;

    function setOptions(options: UseQueryOptions<TQueryFnData, TError, TData>);
    function setOptions(
      queryKey: QueryKey,
      options?: UseQueryOptions<TQueryFnData, TError, TData>
    );
    function setOptions(
      queryKey: QueryKey,
      queryFn: QueryFunction<TQueryFnData>,
      options?: UseQueryOptions<TQueryFnData, TError, TData>
    );
    function setOptions(
      arg1: QueryKey | UseQueryOptions<TQueryFnData, TError, TData>,
      arg2?:
        | QueryFunction<TQueryFnData>
        | UseQueryOptions<TQueryFnData, TError, TData>,
      arg3?: UseQueryOptions<TQueryFnData, TError, TData>
    ) {
      const options = parseQueryArgs(arg1, arg2, arg3);
      let defaultedOptions = client.defaultQueryObserverOptions(options);
      // Include callbacks in batch renders
      defaultedOptions = setBatchCalls<
        UseQueryOptions<TQueryFnData, TError, TData>
      >(defaultedOptions);
      if (observer.hasListeners()) {
        observer.setOptions(defaultedOptions);
      }
    }

    return { subscribe, setOptions, refetch: observer.refetch };
  }

  unmount(): void {
    this.queryClient.unmount();
  }
}
