export interface socialTheme {
  color: string;
  bgColorTopBar: string;
  bgColorActionBar: string;
  bgColorButton: string;
}

const toSnakeCaseWithDash = str =>
  str && str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`);

export const themefunc: (theme: socialTheme) => string = theme => {
  const themename = 'social';
  let stylex = '';

  if (theme) {
    Object.keys(theme).forEach(key => {
      const value = theme[key];
      if (value) {
        stylex += `--${themename}-${toSnakeCaseWithDash(key)}: ${value};`;
      }
    });
  }

  return stylex;
};
