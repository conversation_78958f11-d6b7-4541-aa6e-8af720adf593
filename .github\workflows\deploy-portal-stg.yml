name: Deploy Portal Staging

on:
  workflow_dispatch:
    inputs:
      version:
        required: true
        description: 'Release image tag (https://go.aws/37MtoXh) with githash ex: 1.0.0-0a1b2c3d'

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  run-helm:
    name: 'Run Helm'
    runs-on: [t2gp-arc-linux]
    environment: integration
    env:
      AWS_DEFAULT_REGION: us-east-1
      TARGET_ENV: integration
      CLUSTER: t2gp-non-production
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables (Manual)
        if: github.event.inputs.version != ''
        run: |
          ver=${{github.event.inputs.version}}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
          echo "VERSION=$(echo '${{ github.event.inputs.version }}' | sed 's/-.*//')" >> $GITHUB_ENV
      - name: Helm Deploy (portal-staging)
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.3.1
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          environment: portal-staging
          helm-values: 'social-api.groupsApi.image.tag=${{ env.IMAGE_TAG }},social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "portal-staging", "version": "${{env.IMAGE_TAG}}", "api_url":"https://social-service-portal-staging.d2dragon.net/v1", "api_private_url":"https://social-service-portal-staging-private.d2dragon.net", "mqtt_url":"wss://social-service-integration.d2dragon.net/mqtt" }'
      - name: Output Info
        id: output_info
        run: |
          echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT
  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: portal-staging
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      skip_notification: false
      api_test_note: 'deploy portal-staging'
    secrets: inherit
