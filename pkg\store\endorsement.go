package store

import (
	"context"
	"fmt"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

func (ds *DataStore) GetEndorsements(ctx context.Context, userid, productid string) (*[]*apipub.EndorsementResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	pk := tenant + "#user#" + userid
	sk := productid + "#endorsement#"

	query := dynamodb.QueryInput{
		TableName:              &ds.cfg.ProfileTable,
		KeyConditionExpression: aws.String("#pk = :pk AND begins_with(#sk, :sk)"),
		ExpressionAttributeNames: map[string]string{
			"#pk": "pk",
			"#sk": "sk",
		},
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk": &types.AttributeValueMemberS{Value: pk},
			":sk": &types.AttributeValueMemberS{Value: sk},
		},
	}

	results, _, err := ds.QueryByPkSkWithLimitAndNext(ctx, query)

	var endorsements []*apipub.EndorsementResponse
	for _, item := range results {
		anEndorsement := &apipub.EndorsementResponse{}
		err2 := attributevalue.UnmarshalMap(item, anEndorsement)
		if err2 == nil {
			endorsements = append(endorsements, anEndorsement)
		} else {
			logger.FromContext(ctx).Error().Err(err2).Msgf("UnmarshalMap failed to parse endorsement %v", item)
		}
	}
	return &endorsements, err
}

func (ds *DataStore) IncrementEndorsement(ctx context.Context, userid, productid string, endorsement *apipub.EndorsementResponse, incrementValue int) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	key := map[string]types.AttributeValue{
		"pk": &types.AttributeValueMemberS{Value: endorsement.PK(tenant, userid)},
		"sk": &types.AttributeValueMemberS{Value: endorsement.SK(productid)},
	}
	currentCountAttribute := "currentEndorsementCount"
	totalCountAttribute := "totalEndorsementCount"
	expresssion := fmt.Sprintf("ADD %s :val, %s :val", totalCountAttribute, currentCountAttribute)

	input := &dynamodb.UpdateItemInput{
		TableName:                 &ds.cfg.ProfileTable,
		Key:                       key,
		UpdateExpression:          &expresssion,
		ExpressionAttributeValues: map[string]types.AttributeValue{":val": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", incrementValue)}},
		ReturnValues:              types.ReturnValueAllNew,
	}

	_, err := ds.ddb.UpdateItem(ctx, input)

	if err != nil {
		log.Error().Err(err).Msgf("BatchWriteItem failed err=%v", err)
		return errs.SanitizeDynamoDBException(err)
	}

	return nil
}

func (ds *DataStore) ResetEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	endorsement := &apipub.EndorsementResponse{
		EndorsementName: endorsementName,
	}

	key := map[string]types.AttributeValue{
		"pk": &types.AttributeValueMemberS{Value: endorsement.PK(tenant, userid)},
		"sk": &types.AttributeValueMemberS{Value: endorsement.SK(productid)},
	}
	counterAttribute := "currentEndorsementCount"
	expression := fmt.Sprintf("SET %s = :val", counterAttribute)

	input := &dynamodb.UpdateItemInput{
		TableName:                 &ds.cfg.ProfileTable,
		Key:                       key,
		UpdateExpression:          &expression,
		ConditionExpression:       aws.String("attribute_exists(pk)"),
		ExpressionAttributeValues: map[string]types.AttributeValue{":val": &types.AttributeValueMemberN{Value: fmt.Sprintf("%d", 0)}},
		ReturnValues:              types.ReturnValueAllNew,
	}

	_, err := ds.ddb.UpdateItem(ctx, input)

	if err != nil {
		log.Error().Err(err).Msgf("ResetEndorsement failed err=%v", err)
		return errs.SanitizeDynamoDBException(err)
	}

	return nil
}

func (ds *DataStore) RemoveEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)

	endorsement := &apipub.EndorsementResponse{
		EndorsementName: endorsementName,
	}

	pk := endorsement.PK(tenant, userid)
	sk := endorsement.SK(productid)

	err := ds.DeleteItemByPkSk(ctx, pk, sk)

	if err != nil {
		log.Error().Err(err).Msgf("ResetEndorsement failed err=%v", err)
		return errs.SanitizeDynamoDBException(err)
	}

	return nil
}
