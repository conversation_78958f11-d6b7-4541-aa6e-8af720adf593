openapi: 3.0.3
info:
  title: T2GP Social Service
  version: 'REPLACE_ME'
servers:
  - url: https://social-service-staging.d2dragon.net/v2
  - url: https://social-service-cert.d2dragon.net/v2
  - url: https://social-service-integration.d2dragon.net/v2
  - url: https://social-service-develop.dev.d2dragon.net/v2
  - url: https://social-service-release-1-6-3.d2dragon.net/v2
  - url: /v2
tags:
  - name: Authentication
    description: DNA full account authentication - Not available in production environment. Recommended that game teams use DNA identity systems directly.
  - name: Profile
    description: Fetch user profile
  - name: Search
    description: Search 2K users by display name
  - name: Friends
    description: Invite, search, import friends
  - name: Groups
    description: Create and manage groups of users
  - name: Invite
    description: Manage group invites
  - name: JoinRequest
    description: Request to Join a group
  - name: Presence
    description: Manage user presence
  - name: Chat
    description: Chat with friends
  - name: Abuse
    description: Manage user block list & report users
  - name: Status
    description: Retrieve server stauts information
  - name: Discovery
    description: Get endpoints that game teams can override
paths:
  $ref: '../paths/public-paths.yaml'
components:
  $ref: '../securityschemas/bearerAuth.yaml'
security:
  - bearerAuth: []

