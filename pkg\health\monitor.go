// Package health provides server health status
package health

import (
	"bytes"
	"encoding/json"
	"strings"
	"sync/atomic"
	"time"

	"github.com/rs/zerolog/log"
)

const CheckPollInterval = time.Second * 30
const idPrefix = "social-service-id-"

type HealthReportType int

const (
	HealthReportPublic = iota
	HealthReportPrivate
)

type DependentService interface {
	IsCritical() bool
	CheckHealth() bool
	LastStatus() *ServiceStatus
}

type ServiceStatus struct {
	Instances []*InstanceInfo `json:"instance-details"`
	Status    Health          `json:"status"`
}

type Health uint8

const (
	UNKNOWN Health = iota
	OK
	FAIL
)

var healthText = []string{"UNKNOWN", "OK", "FAIL"}

func HealthText(h Health) string {
	return healthText[int(h)]
}

func (h Health) MarshalJSON() ([]byte, error) {
	return json.Marshal(HealthText(h))
}

type InstanceInfo struct {
	Id                string      `json:"id"`
	Status            Health      `json:"status"`
	LastCheck         time.Time   `json:"last-check"`
	LastCheckDuration int64       `json:"last-check-duration-ms"`
	LastKnownWorking  time.Time   `json:"last-working"`
	RunningFailures   int         `json:"running-failures"`
	ExtraInfo         interface{} `json:"extra-info,omitempty"`
}

func SetInstance(i *InstanceInfo, status Health, lastCheck time.Time, duration int64, extra interface{}) {
	i.Status = status
	i.LastCheck = lastCheck
	i.LastCheckDuration = duration
	if extra != nil {
		if extraInfo, ok := extra.(string); ok {
			if len(extraInfo) > 0 {
				i.ExtraInfo = extraInfo
			}
		} else {
			i.ExtraInfo = extra
		}
	}
	if status == OK {
		i.RunningFailures = 0
		i.LastKnownWorking = lastCheck
	} else {
		i.RunningFailures++
	}
}

type ServiceMonitorInterface interface {
	AddDependentService(name string, service DependentService)
	GetOverallHealth(idProvider *string) Health
	Start()
	Stop()
	GetHealthReport(reportType HealthReportType, idProvider *string) *bytes.Buffer
}

type ServiceMonitor struct {
	Name     string
	Version  string
	Registry map[string]DependentService

	//-- internal implementation stuff
	reportIdx            int32
	pollInterval         time.Duration
	healthReportsPublic  []*bytes.Buffer
	healthReportsPrivate []*bytes.Buffer
	overallHealth        *int32
	ticker               *time.Ticker
}

type PulicReport struct {
	Name          string            `json:"name"`
	Version       string            `json:"version"`
	OverallHealth Health            `json:"overall-status"`
	Generated     time.Time         `json:"generated"`
	Dependencies  map[string]string `json:"dependencies"`
}

type PrivateReport struct {
	Name          string                    `json:"name"`
	Version       string                    `json:"version"`
	OverallHealth Health                    `json:"overall-status"`
	Generated     time.Time                 `json:"generated"`
	Dependencies  map[string]*ServiceStatus `json:"dependencies"`
}

func NewServiceMonitor(name string, version string) *ServiceMonitor {
	sm := &ServiceMonitor{
		Name:          name,
		Version:       version,
		Registry:      make(map[string]DependentService),
		reportIdx:     0,
		pollInterval:  CheckPollInterval,
		overallHealth: new(int32),
	}
	sm.setOverallHealth(UNKNOWN)
	length := 2
	bufsize := 8192
	sm.healthReportsPublic = make([]*bytes.Buffer, length)
	for i := 0; i < length; i++ {
		buf := new(bytes.Buffer)
		buf.Grow(bufsize)
		buf.Write([]byte(`{"overall-health-status": "updating..."}`))
		sm.healthReportsPublic[i] = buf
	}

	sm.healthReportsPrivate = make([]*bytes.Buffer, length)
	for i := 0; i < length; i++ {
		buf := new(bytes.Buffer)
		buf.Grow(bufsize)
		buf.Write([]byte(`{"overall-health-status": "updating..."}`))
		sm.healthReportsPrivate[i] = buf
	}

	return sm
}

func (sm *ServiceMonitor) CreatePrivateReport() ([]byte, error) {
	s := &PrivateReport{
		Name:          sm.Name,
		Version:       sm.Version,
		OverallHealth: sm.GetOverallHealth(nil),
		Generated:     time.Now(),
		Dependencies:  make(map[string]*ServiceStatus),
	}
	for name, ds := range sm.Registry {
		s.Dependencies[name] = ds.LastStatus()
	}
	return json.Marshal(s)
}

func (sm *ServiceMonitor) CreatePublicReport(idProvider *string) ([]byte, error) {
	s := &PulicReport{
		Name:          sm.Name,
		Version:       sm.Version,
		OverallHealth: sm.GetOverallHealth(idProvider),
		Generated:     time.Now(),
		Dependencies:  make(map[string]string),
	}

	for name, ds := range sm.Registry {
		//if not identity provider, or the provider we want
		if !strings.HasPrefix(name, idPrefix) ||
			(idProvider != nil && strings.ToLower(name) == idPrefix+strings.ToLower(*idProvider)) {

			lastStatus := ds.LastStatus()
			if lastStatus == nil {
				s.Dependencies[name] = HealthText(UNKNOWN)
			} else {
				s.Dependencies[name] = HealthText(lastStatus.Status)
			}
		}
	}
	return json.Marshal(s)
}

func (sm *ServiceMonitor) AddDependentService(name string, service DependentService) {
	sm.Registry[name] = service
}

func (sm *ServiceMonitor) GetOverallHealth(idProvider *string) Health {
	overall := atomic.LoadInt32(sm.overallHealth)
	if idProvider != nil {
		for name, ds := range sm.Registry {
			if strings.ToLower(name) == idPrefix+strings.ToLower(*idProvider) {
				idStatus := ds.LastStatus().Status
				//if either is fail, return fail
				if idStatus == 2 || overall == 2 {
					return Health(2)
					//if either is unknown, return unknown
				} else if idStatus == 0 || overall == 0 {
					return Health(0)
					// OK
				} else {
					return Health(1)
				}
			}
		}
	}

	return Health(overall)
}

func (sm *ServiceMonitor) Start() {
	sm.ticker = time.NewTicker(sm.pollInterval)
	go func() {
		probe := func() {
			healthy := true
			for _, svc := range sm.Registry {
				svcHealth := svc.CheckHealth()
				if svc.IsCritical() {
					healthy = healthy && svcHealth
				}
			}

			overall := OK
			if !healthy {
				overall = FAIL
			}

			sm.setOverallHealth(overall)
			sm.updateReport()
		}

		probe()
		for range sm.ticker.C {
			probe()
		}
	}()
}

func (sm *ServiceMonitor) Stop() {
	if sm.ticker != nil {
		sm.ticker.Stop()
	}
}

func (sm *ServiceMonitor) GetHealthReport(reportType HealthReportType, idProvider *string) *bytes.Buffer {
	idx := atomic.LoadInt32(&sm.reportIdx)
	switch reportType {
	case HealthReportPrivate:
		return sm.healthReportsPrivate[idx]
	case HealthReportPublic:
		//get report w/ id provider passed in
		publishIdx := sm.updateReportForWeb(idProvider)
		return sm.healthReportsPublic[publishIdx]
	}
	return nil
}

func (sm *ServiceMonitor) setOverallHealth(h Health) {
	atomic.StoreInt32(sm.overallHealth, int32(h))
}

func (sm *ServiceMonitor) nextReport() int32 {
	idx := atomic.LoadInt32(&sm.reportIdx)
	return idx ^ 1
}

func (sm *ServiceMonitor) publishReport(idx int32) {
	atomic.StoreInt32(&sm.reportIdx, idx)
}

func (sm *ServiceMonitor) updateReport() {
	publishIdx := sm.nextReport()
	sm.updateReportPublic(publishIdx, nil)
	sm.updateReportPrivate(publishIdx)
	sm.publishReport(publishIdx)
}

func (sm *ServiceMonitor) updateReportForWeb(idProvider *string) int32 {
	publishIdx := sm.nextReport()
	sm.updateReportPublic(publishIdx, idProvider)
	sm.updateReportPrivate(publishIdx)
	sm.publishReport(publishIdx)
	return publishIdx
}

func (sm *ServiceMonitor) updateReportPublic(publishIdx int32, idProvider *string) {
	buf := sm.healthReportsPublic[publishIdx]
	buf.Reset()
	reportBytes, err := sm.CreatePublicReport(idProvider)
	if err != nil {
		log.Err(err).Msg("Generate Health Report Error")
	} else {
		buf.Write(reportBytes)
	}
}

func (sm *ServiceMonitor) updateReportPrivate(publishIdx int32) {
	buf := sm.healthReportsPrivate[publishIdx]
	buf.Reset()
	reportBytes, err := sm.CreatePrivateReport()
	if err != nil {
		log.Err(err).Msg("Generate Health Report Error")
	} else {
		buf.Write(reportBytes)
	}
}
