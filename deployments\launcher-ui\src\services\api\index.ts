import type { PendingFriend } from '../friends';

export const SOCIAL_REST_API_BASE =
  'https://social-service-develop.d2dragon.net';
export interface ListResult<T> {
  items: T[];
}
export interface UserCredential {
  locale: string;
  email: string;
  password: string;
  appId?: string;
}

export interface AuthResponse {
  accessToken: string;
  accessTokenExpiresIn: number;
  accountId: string;
  accountType: number;
  legalManifest: string[];
  refreshToken: string;
  refreshTokenExpiresIn: number;
  sessionId: string;
}

export interface IAPIService {
  setAccessToken: (accessToken: string) => void;
  LoginAsync: (credentail: UserCredential) => Promise<Response>;
  getFriendsAsync: () => Promise<Response>;
  getPendingFriendsAsync: () => Promise<Response>;
  getUserAsync: () => Promise<Response>;
  getSteamFriendsAsync: (steamId: string) => Promise<Response>;
  postMakeFriendAsync: (userid: string) => Promise<Response>;
  deleteFriendAsync: (friendid: string) => Promise<Response>;
  searchFriendsAsync: (displayName: string) => Promise<Response>;
  patchPendingFriendAsync: (
    friendid: string,
    payload: Partial<PendingFriend>
  ) => Promise<Response>;
}

export async function handleListResponse<T>(response: Response): Promise<T[]> {
  if (response.ok) {
    const data: ListResult<T> = await response.json();
    return data.items || [];
  } else {
    throw new Error(response.statusText);
  }
}

export async function handleResponse<T>(response: Response): Promise<T> {
  if (response.ok) {
    const data: T = await response.json();
    return data;
  } else {
    throw new Error(response.statusText);
  }
}

export class APIService implements IAPIService {
  private accessToken: string;
  constructor(accessToken?: string) {
    this.accessToken = accessToken;
  }

  setAccessToken(accessToken: string) {
    this.accessToken = accessToken;
  }

  async LoginAsync(credentail: UserCredential) {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentail),
    });
  }

  async getFriendsAsync() {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/friends?status=friend`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async getPendingFriendsAsync() {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/friends?status=pending`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async getUserAsync() {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/user/profile`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async getSteamFriendsAsync(steamId: string) {
    return await fetch(
      `${SOCIAL_REST_API_BASE}/v1/friends/import/steam?steamid=${steamId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );
  }

  async postMakeFriendAsync(userid: string) {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/friends`, {
      method: 'POST',
      body: JSON.stringify({
        userid,
      }),
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async deleteFriendAsync(friendId: string) {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/friends/${friendId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  }

  async searchFriendsAsync(displayName: string) {
    return await fetch(
      `${SOCIAL_REST_API_BASE}/v1/friends/search?q=${encodeURIComponent(
        displayName
      )}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
      }
    );
  }

  async patchPendingFriendAsync(
    friendid: string,
    payload: Partial<PendingFriend>
  ) {
    return await fetch(`${SOCIAL_REST_API_BASE}/v1/friends/${friendid}`, {
      method: 'PATCH',
      body: JSON.stringify(payload),
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    });
  }
}
