################################################
# Apps, App IDs, and Product IDs
################################################
# default app
# choices are: steam, epic, switch, xbx, xb1, ps4, ps5, alpha_race, social_service_product, ghost_pepper, windows
DEFAULT_APP=steam

# app IDs and product IDs
# T2GP Sample Game / Steam / Testing
T2GP_SAMPLE_GAME_STEAM_TESTING_APP_ID=955e2f6c7ce345a6ba0d4d4f77d9257d

# T2GP Sample Game / Epic / Testing
T2GP_SAMPLE_GAME_EPIC_TESTING_APP_ID=4ad332975aae4c21bd9a411ad9d8982a

# T2GP Sample Game / Switch / Testing
T2GP_SAMPLE_GAME_SWITCH_TESTING_APP_ID=3148ef615a7f4622865f8f21049b9a59

# T2GP Sample Game / Xbox Series X / Testing
T2GP_SAMPLE_GAME_XBX_TESTING_APP_ID=5aded947e1ee4c1789596970991b64a1

# T2GP Sample Game / Xbox One / Testing
T2GP_SAMPLE_GAME_XB1_TESTING_APP_ID=50d5e2d887044d6c98d5cace31c38c5a

# T2GP Sample Game / PS4 / Testing
T2GP_SAMPLE_GAME_PS4_TESTING_APP_ID=fa8242e0f37548edb657cc37be6e74bd

# T2GP Sample Game / PS5 / Testing
T2GP_SAMPLE_GAME_PS5_TESTING_APP_ID=b128397365ea46ccbeac03ff48e72b2a

# This product ID is the same for all T2GP_SAMPLE_GAME_XXX_TESTING_APP_IDs
T2GP_SAMPLE_GAME_PRODUCT_ID=0f5e1d57ea994a47ba593cbaad51d9f9

# The following apps are kept here for reference only.

# These apps are for the Social team, but not for testing.
ALPHA_RACE_APP_ID=3bb92115af724e509f639113b0d521f8
ALPHA_RACE_PRODUCT_ID=0f5e1d57ea994a47ba593cbaad51d9f9

SOCIAL_SERVICE_PRODUCT_APP_ID=e3c64ee90b8044d2ba35f12ea161fae4
SOCIAL_SERVICE_PRODUCT_PRODUCT_ID=4029a6ffe9924f969955aa2e1c0782aa

# These apps are NOT for T2GP.  They are for DNA internal use.
GHOST_PEPPER_APP_ID=b90e3283f22eae98a529241255e8dc62
GHOST_PEPPER_PRODUCT_ID=c71f50c3533c462785a2fc22f24c9fad

WINDOWS_APP_ID=da477150a0974fc3bd4015421fd61832
WINDOWS_PRODUCT_ID=c71f50c3533c462785a2fc22f24c9fad


################################################
# Access Levels, Environments, API Versions and Endpoints
################################################
# social API access level
# choices are: public or trusted
SOCIAL_API_ACCESS_LEVEL=public

# environment name. also used as key to look up URLs from the local table if they aren't available.
# choices are: develop, integration, staging, cert, or production
SOCIAL_SERVICE_ENVIRONMENT=develop

# social API version
# choices are: v1, v2
SOCIAL_API_VERSION=v2

# custom public service endpoints
PUBLIC_SERVICE_API_URL=
PUBLIC_SERVICE_MQTT_URL=

# public service endpoints local table
PUBLIC_SERVICE_DEVELOP_API_URL=https://social-service-develop.dev.d2dragon.net
PUBLIC_SERVICE_DEVELOP_MQTT_URL=wss://social-service-develop.dev.d2dragon.net/mqtt

PUBLIC_SERVICE_INTEGRATION_API_URL=https://social-service-integration.d2dragon.net
PUBLIC_SERVICE_INTEGRATION_MQTT_URL=wss://social-service-integration.d2dragon.net/mqtt

PUBLIC_SERVICE_STAGING_API_URL=https://social-service-staging.d2dragon.net
PUBLIC_SERVICE_STAGING_MQTT_URL=wss://social-service-staging.d2dragon.net/mqtt

PUBLIC_SERVICE_CERT_API_URL=https://social-service-cert.d2dragon.net
PUBLIC_SERVICE_CERT_MQTT_URL=wss://social-service-cert.d2dragon.net/mqtt

PUBLIC_SERVICE_PRODUCTION_API_URL=https://social-service-production.d2dragon.net
PUBLIC_SERVICE_PRODUCTION_MQTT_URL=wss://social-service-production.d2dragon.net/mqtt


# custom trusted server endpoints
TRUSTED_SERVER_API_URL=
TRUSTED_SERVER_MQTT_URL=

# trusted server endpoints local table
TRUSTED_SERVER_DEVELOP_API_URL=https://social-trusted-develop.dev.d2dragon.net
TRUSTED_SERVER_DEVELOP_MQTT_URL=wss://social-service-develop.dev.d2dragon.net/mqtt

TRUSTED_SERVER_INTEGRATION_API_URL=https://social-trusted-integration.d2dragon.net
TRUSTED_SERVER_INTEGRATION_MQTT_URL=wss://social-service-integration.d2dragon.net/mqtt

TRUSTED_SERVER_STAGING_API_URL=https://social-trusted-staging.d2dragon.net
TRUSTED_SERVER_STAGING_MQTT_URL=wss://social-service-staging.d2dragon.net/mqtt

TRUSTED_SERVER_CERT_API_URL=https://social-trusted-cert.d2dragon.net
TRUSTED_SERVER_CERT_MQTT_URL=wss://social-service-cert.d2dragon.net/mqtt

TRUSTED_SERVER_PRODUCTION_API_URL=https://social-trusted-production.d2dragon.net
TRUSTED_SERVER_PRODUCTION_MQTT_URL=wss://social-service-production.d2dragon.net/mqtt


# custom trusted server credential
TRUSTED_CREDENTIAL=

# custom trusted credential type
# choices are: BasicAuth, DTLToken
TRUSTED_CREDENTIAL_TYPE=

# trusted server credential local table
TRUSTED_DEVELOP_V1_CREDENTIAL=

TRUSTED_INTEGRATION_V1_CREDENTIAL=

TRUSTED_STAGING_V1_CREDENTIAL=

TRUSTED_CERT_V1_CREDENTIAL=

TRUSTED_PRODUCTION_V1_CREDENTIAL=

# trusted credential type local table
TRUSTED_DEVELOP_V1_CREDENTIAL_TYPE=BasicAuth
TRUSTED_INTEGRATION_V1_CREDENTIAL_TYPE=BasicAuth
TRUSTED_STAGING_V1_CREDENTIAL_TYPE=BasicAuth
TRUSTED_CERT_V1_CREDENTIAL_TYPE=BasicAuth
TRUSTED_PRODUCTION_V1_CREDENTIAL_TYPE=BasicAuth

TRUSTED_DEVELOP_V2_CREDENTIAL_TYPE=DTLToken
TRUSTED_INTEGRATION_V2_CREDENTIAL_TYPE=DTLToken
TRUSTED_STAGING_V2_CREDENTIAL_TYPE=DTLToken
TRUSTED_CERT_V2_CREDENTIAL_TYPE=DTLToken
TRUSTED_PRODUCTION_V2_CREDENTIAL_TYPE=DTLToken

# DNA SSO environment
# choices are: develop, staging, or production
DNA_SSO_ENVIRONMENT=production

# DNA SSO endpoints
DNA_SSO_DEVELOP_API_URL=https://dev.sso.api.2kcoretech.online/sso/v2.0
DNA_SSO_STAGING_API_URL=https://stg.sso.api.2kcoretech.online/sso/v2.0
DNA_SSO_PRODUCTION_API_URL=https://sso.api.2kcoretech.online/sso/v2.0


################################################
# Misc
################################################
#
ROOM_PASSWORD=1qaz2wsx
BAD_WORDS=FUCK

AUTH_SECRET_PATH=/secrets/t2gp/DTL_BASIC_AUTH
SERVER_INSTANCE_ID=4022a2a20b864d068d037694f5bba9fc