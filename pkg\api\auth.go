package api

import (
	"context"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// Login login to 2K account
func (api *SocialPublicAPI) Login(w http.ResponseWriter, r *http.Request) {
	log := logger.Get(r)
	var loginRequest apipub.LoginRequestBody
	if !DecodeBody(w, r, &loginRequest) {
		return
	}

	appID := api.Cfg.AppID
	if loginRequest.AppId != nil {
		appID = *loginRequest.AppId
	}
	ctx := r.Context()

	loginResp, err := api.Id.Login(r.Context(), loginRequest.Email, loginRequest.Password, loginRequest.Locale, appID)

	// telemetry
	t := api.Tele
	if err == nil {
		loginjwt, err := jwt.ParseJWTTokenWithoutValidation(loginResp.AccessToken)
		ost := apipub.OnlineServiceType(loginjwt.Claims.OnlinePlatformType)
		if err == nil && loginjwt != nil {
			// update the context to log event
			ctx = context.WithValue(ctx, constants.BearerAuthJWT, loginjwt)
			t.SendGenericEvent(ctx, telemetry.BuildGenericTeleMeta(telemetry.KAuthLoginSuccess, loginjwt.Claims.ProductID, loginjwt.Claims.Subject, ost, &loginjwt.Claims.Issuer, nil))
		} else if loginjwt != nil {
			t.SendGenericEvent(ctx, telemetry.BuildGenericTeleMeta(telemetry.KAuthLoginFail, loginjwt.Claims.ProductID, loginjwt.Claims.Subject, ost, &loginjwt.Claims.Issuer, nil))
		}
	} else {
		t.SendGenericEvent(ctx, telemetry.BuildGenericTeleMeta(telemetry.KAuthLoginFail, "", string(loginRequest.Email), 0, aws.String(""), nil))
	}

	if err != nil {
		log.Error().Err(err).Msgf("login failed")
		errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EInvalidLogin))
		return
	}

	token, err := jwt.ParseJWTToken(ctx, loginResp.AccessToken, false)
	if err == nil {
		if token.Claims != nil {

			if validation.IsFullSocialAccount(ctx, token) && token.Claims.DnaFullAccountID != "" {
				//make sure this is done befor the platformid is promoted.
				api.replacePlatformIdsWith2k(ctx, token)
			}

			if token.Claims.DnaFullAccountID != "" {
				token.Claims.Subject = token.Claims.DnaFullAccountID
			}

			priority := apipub.PresencePriorityUnknown
			priority, _ = api.Cache.GetLowestAvailablePriority(r.Context(), token.Claims.Subject, token.Claims.ProductID)

			loginPresence := &apipub.PresenceResponse{
				Userid:    token.Claims.Subject,
				Productid: token.Claims.ProductID,
				Status:    "online",
				Priority:  priority,
			}
			userid := token.Claims.Subject
			productid := token.Claims.ProductID
			appid := token.Claims.GetApplicationID()
			sessionid := token.Claims.SessionID
			createdTime := token.Claims.CreatedTime
			expiresTime := token.Claims.ExpiresTime

			api.Cache.SetPresence(r.Context(), loginPresence, productid, appid, sessionid, userid, createdTime, expiresTime, time.Duration(api.Cfg.TtlPresence)*time.Second)
		}
	}

	ReturnOK(w, r, loginResp)
}

// Logout account logout
func (api *SocialPublicAPI) Logout(w http.ResponseWriter, r *http.Request) {
	log := logger.Get(r)
	ctx := r.Context()
	tokenStr := ""
	bearer := ctx.Value(constants.BearerAuthString)
	productid := ""
	if bearer != nil {
		tokenStr = bearer.(string)
		token, _ := jwt.ParseJWTToken(ctx, bearer.(string), true)
		if token != nil && token.Claims != nil {
			productid = token.Claims.ProductID
		}
	}

	err := api.Id.Logout(ctx, tokenStr)

	// telemetry
	additionalInfo := make(map[string]string)
	if err == nil {
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KAuthLogoutSuccess, productid, tokenStr, 0, aws.String(""), &additionalInfo))
	} else {
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KAuthLogoutFail, productid, tokenStr, 0, aws.String(""), &additionalInfo))
		log.Error().Err(err).Msgf("logout failed")
	}

	ReturnEmptyOK(w, r)
}

// RefreshToken refresh JWT token
func (api *SocialPublicAPI) RefreshToken(w http.ResponseWriter, r *http.Request) {
	log := logger.Get(r)
	// decode body
	var refreshTokenRequest apipub.RefreshRequestBody
	if !DecodeBody(w, r, &refreshTokenRequest) {
		return
	}

	ctx := r.Context()
	bearer := ctx.Value(constants.BearerAuthString)

	productid := ""
	if bearer != nil {
		token, _ := jwt.ParseJWTToken(ctx, bearer.(string), true)
		if token != nil && token.Claims != nil {
			productid = token.Claims.ProductID
		}
	}

	refreshResp, err := api.Id.RefreshToken(ctx, refreshTokenRequest.RefreshToken, refreshTokenRequest.Locale)

	// telemetry
	additionalInfo := make(map[string]string)
	if err == nil {
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KAuthTokenRefreshSuccess, productid, bearer.(string), 0, aws.String(""), &additionalInfo))
	} else {
		api.Tele.SendGenericEvent(r.Context(), telemetry.BuildGenericTeleMeta(telemetry.KAuthTokenRefreshFail, productid, bearer.(string), 0, aws.String(""), &additionalInfo))
		log.Error().Err(err).Msgf("refresh token failed")
		errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EInvalidLogin))
		return
	}

	ReturnOK(w, r, refreshResp)
}
