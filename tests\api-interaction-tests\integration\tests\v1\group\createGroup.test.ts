import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
const groupMaxMembersMax: number = 100;
const groupMaxMembersMin: number = 2;
const groupMaxMembersDefault: number = 2;

beforeEach(async () => {
  usersTwok = new TwokAccounts(1, ["leader"]);
  await usersTwok.loginAll({});
});

afterEach(async () => {
  await usersTwok.logoutAll({});
});

// NOTE: test cases here don't have clean-up in case test cases fail and groups
// are actually created.
describe('[public v1]', () => {
  it('max members = negative number', async () => {
    let testCase = {
      description: "create a group with negative max members",
      expected: "the group is not created"
    };

    const negMaxMember: number = -1;
    // Create a group with negative members
    const actualStatuses = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: negMaxMember,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.BAD_REQUEST, actualStatuses);

    const actualGroupsInfo = await socialApi.getGroupsInfo(usersTwok.acct["leader"]);

    // Expect the leader doesn't get the groups info
    const expectedGroupsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupsInfo).toMatchObject(expectedGroupsInfo)},
      testCase,
      {
        resp: actualGroupsInfo,
        additionalInfo: {
          "fail reason": "group was unexpectedly created"
        }
      }
    );
  });

  it('max members = valid lower bound - 1', async () => {
    let testCase = {
      description: "create a group with max members being 1 less than the lowest allowed number",
      expected: "the group is not created"
    };

    const inMinMembers: number = groupMaxMembersMin - 1;
    // Create a group with "number of members = minimum boundary-1"
    const actualStatuses = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: inMinMembers,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.BAD_REQUEST, actualStatuses);

    const actualGroupsInfo = await socialApi.getGroupsInfo(usersTwok.acct["leader"]);

    // Expect the leader doesn't get the groups info
    const expectedGroupsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupsInfo).toMatchObject(expectedGroupsInfo)},
      testCase,
      {
        resp: actualGroupsInfo,
        additionalInfo: {
          "fail reason": "group was unexpectedly created"
        }
      }
    );
  });

  it('max members = valid upper bound + 1', async () => {
    let testCase = {
      description: "create a group with max members being 1 more than the highest allowed number",
      expected: "the group is not created"
    };

    const inMaxMembers: number = groupMaxMembersMax + 1;
    // Create a group with "number of members = maximum boundary + 1"
    const actualStatuses = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: inMaxMembers,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.BAD_REQUEST, actualStatuses);

    const actualGroupsInfo = await socialApi.getGroupsInfo(usersTwok.acct["leader"]);

    // Expect the leader doesn't get the groups info
    const expectedGroupsInfo = {
      status: StatusCodes.OK,
      body: {
        items: [],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupsInfo).toMatchObject(expectedGroupsInfo)},
      testCase,
      {
        resp: actualGroupsInfo,
        additionalInfo: {
          "fail reason": "group was unexpectedly created"
        }
      }
    );
  });
});

describe('[public v1]', () => {
  let mgroupId: string;

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], mgroupId);
  });

  it('max members without value[trusted]', async () => {
    let testCase = {
      description: "create a group with max members having no value",
      expected: "the group is created with the default max members"
    };

    // Create a group that maxMembers without value
    const actualStatuses = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, actualStatuses);
    mgroupId = socialApi.getGroupId(actualStatuses);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mgroupId
    );

    // Expect the group is created with the groupMaxMembersDefault
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: { groupid: mgroupId, maxMembers: groupMaxMembersDefault },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "group was not created, or the created group contains unexpected default value of maxMembers"
        }
      }
    );
  });

  it('max members = valid number[happy trusted]', async () => {
    let testCase = {
      description: "create a group with valid max members",
      expected: "the group is created"
    };

    const valMaxMember: number = 50;
    // Create a group of valid members(50)
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: valMaxMember,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mgroupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mgroupId
    );

    // Expect group information includes: the number of members is 50.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: { groupid: mgroupId, maxMembers: valMaxMember },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "group was not created, or unexpected groupid or maxMembers"
        }
      }
    );
  });

  it('max members = valid lower bound[happy trusted]', async () => {
    let testCase = {
      description: "create a group with max members being the lowest allowed number",
      expected: "the group is created"
    };

    // Create group with minimum number of members(2)
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: groupMaxMembersMin,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mgroupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mgroupId
    );

    // Expect Group information includes: the number of members is 2.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: { groupid: mgroupId, maxMembers: groupMaxMembersMin },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the group not created, or the created group contains unexpected minimum value of maxMembers"
        }
      }
    );
  });

  it('max members = valid upper bound[happy trusted]', async () => {
    let testCase = {
      description: "create a group with max members being the highest allowed number",
      expected: "the group is created"
    };

    // Create the group with the largest number of members(100)
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: groupMaxMembersMax,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    mgroupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      mgroupId
    );

    // Expect group information includes: the number of members is 100.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: { groupid: mgroupId, maxMembers: groupMaxMembersMax },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the group not created, or the created group contains unexpected maximum value of maxMembers"
        }
      }
    );
  });
});

// eslint-disable-next-line max-lines-per-function
describe('[public v1]', () => {
  let groupId: string;

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
  });

  it('joinRequestAction = authenticate, with password[happy trusted]', async () => {
    let testCase = {
      description: "create a group with join request action being authenticate",
      expected: "the group is created"
    };

    // Create a group that requires an authentication password
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'authenticate',
        password: '123',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect group:
    // - join request action is "authenticate".
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        groupid: groupId,
        joinRequestAction: 'authenticate',
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the group was not created, or the created group contains unexpected joinRequestAction value"
        }
      }
    );
  });

  it('joinRequestAction = auto-approve, without password[happy trusted]', async () => {
    let testCase = {
      description: "create a group with join request action being auto-approve",
      expected: "the group is created"
    };

    // Create a group whose members are automatically approved to join
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'auto-approve',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect group:
    // - join request action is "auto-approve".
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        groupid: groupId,
        joinRequestAction: 'auto-approve',
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "group was not created, or the created group contains unexpected joinRequestAction value"
        }
      }
    );
  });

  it('joinRequestAction = auto-reject, without password[happy trusted]', async () => {
    let testCase = {
      description: "create a group with join request action being auto-reject",
      expected: "the group is created"
    };

    // Create a group whose members are automatically rejected
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'auto-reject',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect group:
    // - join request action is "auto-reject".
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        groupid: groupId,
        joinRequestAction: 'auto-reject',
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "group was not created, or the created group contains unexpected joinRequestAction value"
        }
      }
    );
  });

  it('joinRequestAction = manual[happy trusted]', async () => {
    let testCase = {
      description: "create a group with join request action being manual",
      expected: "the group is created"
    };

    // Create a manual verification member to join the group
    const r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    // Expect group:
    // - join request action is "manual".
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        groupid: groupId,
        joinRequestAction: 'manual',
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "group was not created, or the created group contains unexpected joinRequestAction value"
        }
      }
    );
  });
});
