package store

import (
	"context"
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"

	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// GetTsClientId fetches the TsClientIdInfo object from the database
func (ds *DataStore) GetTsClientId(ctx context.Context, clientid string) (*apitrusted.TsClientIdInfo, error) {
	log := logger.FromContext(ctx)

	var tsCid = &apitrusted.TsClientIdInfo{
		ClientId: clientid,
	}

	pk := tsCid.PK("")
	sk := tsCid.SK("")

	span, _ := tracer.StartSpanFromContext(ctx, "ddb.GetItemWithContext", tracer.ServiceName("aws.Dynamodb"), tracer.ResourceName("GetTsClientId"))
	span.SetTag("query_pk", pk)
	span.SetTag("query_sk", sk)
	item, err := ds.ddb.GetItem(ctx, &dynamodb.GetItemInput{
		Key: map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: pk},
			"sk": &types.AttributeValueMemberS{Value: sk},
		},
		TableName: &ds.cfg.ProfileTable,
	})
	span.Finish()
	if err != nil {
		logger.FromContext(ctx).Error().Err(err).Str("pk", pk).Str("sk", sk).Msg("ddb.GetItem() failed")
		return nil, err
	}
	if item == nil || item.Item == nil {
		return nil, nil
	}

	err = attributevalue.UnmarshalMap(item.Item, &tsCid)
	if err != nil {
		log.Error().Err(err).Caller().Interface("item", item).Msg("UnmarshalMap failed to parse profile")
		return nil, errs.New(http.StatusInternalServerError, errs.EDynamodbUnmarshalFailed)
	}

	return tsCid, nil
}

// PutTsClientId puts the TsClientIdInfo object in the database
func (ds *DataStore) PutTsClientId(ctx context.Context, tsCid *apitrusted.TsClientIdInfo) error {
	if tsCid == nil {
		return errs.New(http.StatusUnauthorized, errs.ERequestEmpty)
	}
	if tsCid.ClientId == "" {
		return errs.New(http.StatusUnauthorized, errs.EInvalidUserID)
	}

	items := []DataStoreItem{
		tsCid,
	}

	err := ds.PutItems(ctx, items)
	if err != nil {
		return err
	}

	return nil
}

// DelTsClientId delete the TsClientIdInfo object from the database
func (ds *DataStore) DelTsClientId(ctx context.Context, clientid string) error {
	pk := apitrusted.BuildTsClientIdPK(clientid)
	sk := pk
	err := ds.DeleteItemByPkSk(ctx, pk, sk)
	if err != nil {
		return err
	}

	return nil
}
