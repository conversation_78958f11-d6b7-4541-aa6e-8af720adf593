{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Social Service Server",
      "type": "go",
      "request": "launch",
      "mode": "debug",
      "program": "${workspaceFolder}/cmd/social/main.go",
      "cwd": "${workspaceFolder}",
      "envFile": "${workspaceFolder}/.env"
    },
    {
      "name": "Debug Backfill Script",
      "program": "${workspaceFolder}/scripts/backfill-social-table.js",
      "request": "launch",
      "skipFiles": ["<node_internals>/**"],
      "type": "node",
      "args": [
        "--source-table-name",
        "t2gp-social-develop-social",
        "--destination-table-name",
        "t2gp-social-develop-social-v1",
        "--region",
        "us-east-1"
      ]
    },
    {
      "name": "Debug Backfill Profile Script",
      "program": "${workspaceFolder}/scripts/backfill-item.js",
      "request": "launch",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "type": "node",
      "args": [
        "--source-table-name",
        "t2gp-social-production-social",
        "--destination-table-name",
        "social-service-production-profile",
        "--type",
        "profile",
        "--region",
        "us-east-1"
      ]
    },
    {
      "name": "Launch Iteraction Tests",
      "request": "launch",
      "runtimeArgs": ["run-script", "test"],
      "runtimeExecutable": "npm",
      "skipFiles": ["<node_internals>/**"],
      "type": "node",
      "cwd": "${workspaceFolder}/tests/api-interaction-tests"
    }
  ]
}
