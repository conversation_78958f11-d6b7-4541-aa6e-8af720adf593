import { render, waitFor } from '@testing-library/svelte';
import FriendsSearchMock from '../../../components/FriendsSearch/__mock__/FriendsSearch.svelte';
import SteamImportMock from '../../../components/SteamImport/__mock__/SteamImport.svelte';
import {
  TabListMock,
  TabMock,
  TabPanelMock,
  TabsMock,
} from '../../../components/Tabs/__mock__';
import TaskBarMock from '../../../components/TaskBar/__mock__/TaskBar.svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  transportServiceMock,
} from '../../../services/__mocks__';
import FriendsAddWrapper from './FriendsAddWrapper.svelte';

jest.mock('../../../components', () => ({
  FriendsSearch: FriendsSearchMock,
  SteamImport: SteamImportMock,
  Tab: Tab<PERSON>ock,
  TabList: <PERSON>b<PERSON>ist<PERSON>ock,
  TabPanel: TabPanelMock,
  Tabs: TabsMock,
  TaskBar: TaskBarMock,
}));

const socialServicesMock = new SocialServices({
  transportService: transportServiceMock,
  friendsService: friendsServiceMock,
});

describe('FriendsAdd', () => {
  it('should render friends add page', async () => {
    const { getByText } = render(FriendsAddWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    await waitFor(() => {
      expect(getByText('add friends')).not.toBeNull();
    });
  });
});
