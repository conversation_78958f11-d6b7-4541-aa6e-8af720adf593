import request from 'supertest';
import { config } from '../../integration/lib/config';
import * as socialApi from '../../integration/lib/social-api';
import { StatusCodes } from 'http-status-codes';
import jwt_decode, { JwtPayload } from "jwt-decode";

describe('group-sendAcceptInviteConcurrent', () => {
  let groupId: string;

  /* PLEASE NOTE *********
   * Before running the script, fill in 2k account credentials for group leader
   * and group members below. Fill in as many group members as needed.
   * Adjust sleepMs as needed; 0 means no delay between calls.
   * 
   * TODO: input these params from command line or a data file.
   */
  // Time in ms to sleep between the async invite accept calls.
  const sleepMs: number = 0;

  // login/pass for the group leader.
  const groupLeaderAccount = {
      "name" : "",
      "pass" : ""
  };
  const groupLeaderCred = {uid:"", token:""};

  // NOTE: in this investigation, only 1 member is needed.
  // login/pass for group members. Add more if needed.
  const groupMemberAccountList = [
    {
      "name" : "",
      "pass" : ""
    },
  ];
  const groupMemberCredList: {uid: string, token: string}[] = [];

  beforeEach(async () => {
    // Login and get credential for group leader
    groupLeaderCred.token = await socialApi.loginIn(
      groupLeaderAccount.name,
      groupLeaderAccount.pass
    );
    const dLeader: JwtPayload = jwt_decode(groupLeaderCred.token);
    groupLeaderCred.uid = dLeader.sub as string;

    // create group
    const r = await socialApi.createGroup(groupLeaderCred.token, null, 'manual', null, null, true);
    groupId = socialApi.getGroupId(r);

    // Login and get credentials for group members
    for (const m of groupMemberAccountList) {
      const t = await socialApi.loginIn(
          m["name"], m["pass"]
      );
      const d: JwtPayload = jwt_decode(t);
      groupMemberCredList.push({uid:d.sub as string, token:t});
    }
  });

  afterEach(async () => {
    // delete group
    await socialApi.deleteGroup(groupLeaderCred.token, groupId);
    // logout for group leader
    await socialApi.loginOut(groupLeaderCred.token);
    // logout for group members
    for (const c of groupMemberCredList) {
      await socialApi.loginOut(c.token);
    }
  });

  /**
   *
   */
  it('send invite then concurrently get invite or accept invite', async () => {
    // for time stamps
    const start_time = Date.now();

    // sanity check
    expect(groupMemberCredList.length).toEqual(groupMemberAccountList.length);

    console.log(`groupId: ${groupId}`);

    const promiseList = [];
    console.log(`time stamp: ${Date.now() - start_time}ms`);
    promiseList.push(
      socialApi.inviteOld(groupLeaderCred.token, groupId, groupMemberCredList[0].uid)
    );

    console.log(`(s) time stamp: ${Date.now() - start_time}ms`);
    // sleep between calls, to see the interval that would cause issue
    await new Promise(resolve => setTimeout(resolve, sleepMs));

    console.log(`(s) time stamp: ${Date.now() - start_time}ms`);
    promiseList.push(

      // There were 2 scenarios investigated:
      // send invite then get invite
      // send invite then accept invite

      //socialApi.getInvite(groupMemberCredList[0].token)
      socialApi.acceptInvite(groupMemberCredList[0].token, groupId, groupMemberCredList[0].uid)
    );

    for (const p of promiseList) {
      p.then(resp => {
        console.log(resp.status);
        console.log(resp.body);
      }).catch(err => console.log(err));
    }
  });
});
