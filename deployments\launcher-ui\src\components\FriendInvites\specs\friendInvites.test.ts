import { render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { AccordionMock, AccordionSectionMock } from '../../Accordion/__mock__';
import FriendRequestsActionBarMock from '../../FriendRequestsActionBar/__mock__/FriendRequestsActionBar.svelte';
import NavLinkMock from '../../NavLink/__mock__/NavLink.svelte';
import { SteamImportMock } from '../../SteamImport/__mock__';
import FriendInvitesWrapper from './FriendInvitesWrapper.svelte';

jest.mock('../../../assets/icons', () => ({
  SVGArrowLeft: SVGIconMock,
  SVGPersonAdd: SVGIconMock,
}));

jest.mock('../../Accordion', () => ({
  Accordion: AccordionMock,
  AccordionSection: AccordionSectionMock,
}));

jest.mock('../../NavLink', () => ({
  NavLink: NavLinkMock,
}));

jest.mock('../../FriendRequestsActionBar', () => ({
  FriendRequestsActionBar: FriendRequestsActionBarMock,
}));

jest.mock('../../SteamImport', () => ({
  SteamImport: SteamImportMock,
}));

describe('FriendInvites', () => {
  it('should render UI', () => {
    expect(() => render(FriendInvitesWrapper)).not.toThrow();
  });
});
