package store

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/franela/goblin"
	"github.com/go-redis/redismock/v9"
	"github.com/rs/zerolog"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	gomock "go.uber.org/mock/gomock"
)

var cfg *config.Config
var ctx context.Context = context.Background()
var sm *health.ServiceMonitor
var id *identity.Identity
var ds *DataStore

type MockStore struct {
	ctrl        *gomock.Controller
	rc          *cache.MockRedisCacheInterface
	ddb         *MockDynamoDBInterface
	db          *DataStore
	s3          *MockS3Interface
	id          *identity.MockIdentityInterface
	redisMock   redismock.ClientMock
	testTimeout time.Duration
}

func NewMockDS(t *testing.T) *MockStore {
	mockCtrl := gomock.NewController(t)
	_, redisMock := redismock.NewClientMock()

	rc := cache.NewMockRedisCacheInterface(mockCtrl)
	ddb := NewMockDynamoDBInterface(mockCtrl)
	s3 := NewMockS3Interface(mockCtrl)
	id := identity.NewMockIdentityInterface(mockCtrl)
	db := NewMockStore(cfg, ddb, s3, id)

	return &MockStore{
		ctrl:        mockCtrl,
		redisMock:   redisMock,
		rc:          rc,
		ddb:         ddb,
		s3:          s3,
		db:          db,
		id:          id,
		testTimeout: time.Second * 45,
	}
}

func NewMockStore(cfg *config.Config, ddb *MockDynamoDBInterface, s3 *MockS3Interface, id *identity.MockIdentityInterface) *DataStore {
	return &DataStore{
		ddb: ddb,
		s3:  s3,
		cfg: cfg,
		id:  id,
	}
}

func (m *MockStore) Marshal(ctx context.Context, obj DataStoreItem) map[string]types.AttributeValue {
	tenant := "dna"

	item, err := attributevalue.MarshalMap(obj)
	if err != nil {
		return map[string]types.AttributeValue{}
	}
	item["pk"] = &types.AttributeValueMemberS{Value: obj.PK(tenant)}
	item["sk"] = &types.AttributeValueMemberS{Value: obj.SK(tenant)}
	return item
}

func TestMain(m *testing.M) {
	setup()
	code := m.Run()
	teardown()
	os.Exit(code)
}

func setup() {
	cfg = config.ConfigForTests()
	sm = health.NewServiceMonitor("test-social", cfg.Version.Version)
	id = identity.NewIdentity(context.Background(), cfg, sm)
	ddb := NewDynamoDB(context.TODO(), cfg)
	s3 := NewS3(context.TODO(), cfg)
	ds = NewDataStore(context.TODO(), cfg, ddb, s3, id)
	// disable log
	zerolog.SetGlobalLevel(zerolog.FatalLevel)

	createSocialTables(ds.ddb, context.TODO(), cfg)
}

func teardown() {
	ddb := NewDynamoDB(context.TODO(), cfg)
	(*ddb).DeleteTable(context.TODO(), &dynamodb.DeleteTableInput{TableName: aws.String(cfg.ProfileTable)})
	(*ddb).DeleteTable(context.TODO(), &dynamodb.DeleteTableInput{TableName: aws.String(cfg.ChatMessagesTable)})
}

func TestNewV2DynamoDB(t *testing.T) {
	g := goblin.Goblin(t)
	g.Describe("NewDynamoDB", func() {
		g.It("should succeed creating and checking health", func() {
			c := *cfg
			c.DynamoDBURL = "https://dynamodb.us-east-1.amazonaws.com"
			ddb := NewDynamoDB(context.TODO(), &c)
			g.Assert(ddb).IsNotNil()
			g.Assert(ddb.IsCritical()).IsTrue()
			g.Assert(ddb.CheckHealth()).IsTrue()
			g.Assert(ddb.LastStatus()).IsNotNil()
		})

		g.It("should succeed with localhost", func() {
			c := *cfg
			c.DynamoDBURL = "http://*************:8000"
			ddb := NewDynamoDB(context.TODO(), &c)
			g.Assert(ddb).IsNotNil()
			g.Assert(ddb.IsCritical()).IsTrue()
			g.Assert(ddb.CheckHealth()).IsTrue()
			g.Assert(ddb.LastStatus()).IsNotNil()
		})

		g.It("should fail with bad status code", func() {
			g.Timeout(45 * time.Second)
			c := *cfg
			c.DynamoDBURL = "https://httpbin.org/status/500"
			os.Setenv("DYNAMODB_URL", "https://httpbin.org/status/500")
			ddb := NewDynamoDB(context.TODO(), &c)
			g.Assert(ddb).IsNotNil()
			g.Assert(ddb.IsCritical()).IsTrue()
			g.Assert(ddb.CheckHealth()).IsFalse()
			g.Assert(ddb.LastStatus()).IsNotNil()
		})

		g.It("should fail with 200 but missing healthy", func() {
			g.Timeout(45 * time.Second)
			c := *cfg
			c.DynamoDBURL = "https://httpbin.org/status/200"
			os.Setenv("DYNAMODB_URL", "https://httpbin.org/status/200")
			ddb := NewDynamoDB(context.TODO(), &c)
			g.Assert(ddb).IsNotNil()
			g.Assert(ddb.IsCritical()).IsTrue()
			g.Assert(ddb.CheckHealth()).IsFalse()
			g.Assert(ddb.LastStatus()).IsNotNil()
		})

		g.It("should fail bad url", func() {
			g.Timeout(45 * time.Second)
			c := *cfg
			c.DynamoDBURL = "http://bad-url"
			ddb := NewDynamoDB(context.TODO(), &c)
			g.Assert(ddb).IsNotNil()
			g.Assert(ddb.IsCritical()).IsTrue()
			g.Assert(ddb.CheckHealth()).IsFalse()
			g.Assert(ddb.LastStatus()).IsNotNil()
		})
	})
}

func TestNewS3(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	s3 := NewS3(context.TODO(), cfg)
	g.Describe("NewS3", func() {
		g.It("should succeed", func() {
			g.Assert(s3).IsNotNil()
			g.Assert(s3.IsCritical()).IsTrue()
		})

		g.It("should return valid last status", func() {
			g.Assert(s3.LastStatus()).IsNotNil()
		})

	})
}

func TestPutItem(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	item1 := apipub.UserProfileResponse{
		Userid: "b287e655461f4b3085c8f244e394ff7e",
	}
	i := mock.Marshal(context.Background(), &item1)
	result := &dynamodb.PutItemOutput{
		Attributes: i,
	}

	g.Describe("PutItem", func() {
		g.It("should fail when ddb returns error", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Times(2)
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(nil, fmt.Errorf("error"))

			err := mock.db.PutItemInProfileTable(context.Background(), &item1)
			g.Assert(err).IsNotNil()
		})

		g.It("should succeed", func() {
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(result, nil)

			err := mock.db.PutItemInProfileTable(context.Background(), &item1)
			g.Assert(err).IsNil()
		})
	})
}

func TestQueryItemCount(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	g.Describe("QueryItemCount", func() {
		g.It("should fail with database error", func() {

			mock.ddb.EXPECT().Query(ctx, gomock.Any()).Return(nil, fmt.Errorf("error"))

			_, err := mock.db.QueryItemCount(context.Background(), "user#b287e655461f4b3085c8f244e394ff7e", "group#")
			g.Assert(err).IsNotNil()
		})

		g.It("should fail succeed", func() {
			input := &dynamodb.QueryInput{
				ExpressionAttributeNames: map[string]string{
					"#pk": "pk",
					"#sk": "sk",
				},
				ExpressionAttributeValues: map[string]types.AttributeValue{
					":pk": &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
					":sk": &types.AttributeValueMemberS{Value: "group#"},
				},
				KeyConditionExpression: aws.String("#pk = :pk AND begins_with(#sk, :sk)"),
				TableName:              &cfg.ProfileTable,
				Select:                 "COUNT",
			}

			output := &dynamodb.QueryOutput{
				Count: 2,
			}

			mock.ddb.EXPECT().Query(ctx, gomock.Eq(input)).Return(output, nil)

			count, err := mock.db.QueryItemCount(context.Background(), "user#b287e655461f4b3085c8f244e394ff7e", "group#")
			g.Assert(err).IsNil()
			g.Assert(count).IsNotNil()
			g.Assert(*count).Equal(int32(2))
		})
	})
}

func TestQueryItemCountLocalDDB(t *testing.T) {
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()
	g := goblin.Goblin(t)
	item1 := apipub.UserProfileResponse{
		Userid: "b287e655461f4b3085c8f244e394ff7e",
	}

	var err error
	var count *int32

	g.Describe("QueryItemCountLocalDDB", func() {
		g.It("should fail with database error", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(nil, fmt.Errorf("error"))
			err = mock.db.PutItemInProfileTable(context.Background(), &item1)
			g.Assert(err).IsNotNil()
		})

		g.It("should succeed", func() {
			queryOutput := dynamodb.QueryOutput{
				Items: []map[string]types.AttributeValue{
					{
						"pk": &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
						"sk": &types.AttributeValueMemberS{Value: "group#group1"},
					},
					{
						"pk": &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
						"sk": &types.AttributeValueMemberS{Value: "group#group2"},
					},
				},
				Count: 2,
			}
			mock.ddb.EXPECT().Query(ctx, gomock.Any()).Return(&queryOutput, nil)
			count, err = mock.db.QueryItemCount(context.Background(), "user#b287e655461f4b3085c8f244e394ff7e", "group#")
			g.Assert(err).IsNil()
			g.Assert(count).IsNotNil()
			g.Assert(*count).Equal(int32(2))
		})

		mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
		mock.ddb.EXPECT().DeleteItem(ctx, gomock.Any()).Return(nil, nil)
		mock.db.DeleteItem(context.Background(), &item1)
	})
}

func TestGetPutItem(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	item := apipub.UserProfileResponse{
		Userid: "b287e655461f4b3085c8f244e394ff7e",
	}

	g.Describe("PutItem", func() {
		g.It("should succeed", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(nil, nil)
			err := mock.db.PutItemInProfileTable(context.Background(), &item)
			g.Assert(err).IsNil()

			itemGet := apipub.UserProfileResponse{}
			found := false

			getItem := &dynamodb.GetItemOutput{
				Item: map[string]types.AttributeValue{
					"pk":     &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
					"sk":     &types.AttributeValueMemberS{Value: "user#"},
					"userid": &types.AttributeValueMemberS{Value: "b287e655461f4b3085c8f244e394ff7e"},
				},
			}

			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(getItem, nil)
			found, err = mock.db.GetItem(context.Background(), "user#b287e655461f4b3085c8f244e394ff7e", "user#b287e655461f4b3085c8f244e394ff7e", &itemGet)
			g.Assert(err).IsNil()
			g.Assert(found).IsTrue()
			g.Assert(itemGet.Userid).Equal("b287e655461f4b3085c8f244e394ff7e")
		})
	})
}

func TestGetDiscovery(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	g.Describe("GetDiscovery", func() {
		g.It("should succeed", func() {
			s3key := fmt.Sprintf("discovery/%s/config.json", cfg.AppID)
			input := &s3.GetObjectInput{
				Bucket: aws.String(cfg.SocialConfigBucket),
				Key:    aws.String(s3key),
			}

			output := &s3.GetObjectOutput{
				Body: io.NopCloser(bytes.NewBuffer([]byte("[{}]"))),
			}

			mock.s3.EXPECT().GetObject(ctx, input).Return(output, nil)

			emptyDisc := []apipub.DiscoveryResponse{
				{
					Description: "",
					Id:          "",
					Urls:        []apipub.DiscoveryURLResponse(nil),
				},
			}

			result, err := mock.db.GetDiscovery(ctx, cfg.AppID)
			g.Assert(err).IsNil()
			g.Assert(result).Equal(&emptyDisc)
		})
	})
}

func TestBatchGetItems(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	g.Describe("BatchGetItemsExact", func() {
		g.It("should succeed", func() {
			item1 := &apipub.UserProfileResponse{
				Userid: "b287e655461f4b3085c8f244e394ff7e",
			}
			item2 := &apipub.UserProfileResponse{
				Userid: "2017e9305ccc4e5781d076403c1b6725",
			}

			pksk := []string{"user#b287e655461f4b3085c8f244e394ff7e", "user#2017e9305ccc4e5781d076403c1b6725"}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Times(2)
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(nil, nil)
			mock.db.PutItemInProfileTable(ctx, item1)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Times(2)
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(nil, nil)
			mock.db.PutItemInProfileTable(ctx, item2)

			batchItems := dynamodb.BatchGetItemOutput{
				Responses: map[string][]map[string]types.AttributeValue{
					mock.db.cfg.ProfileTable: {
						{
							"pk":     &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
							"sk":     &types.AttributeValueMemberS{Value: "profile#b287e655461f4b3085c8f244e394ff7e"},
							"userid": &types.AttributeValueMemberS{Value: "b287e655461f4b3085c8f244e394ff7e"},
						},
						{
							"pk":     &types.AttributeValueMemberS{Value: "user#2017e9305ccc4e5781d076403c1b6725"},
							"sk":     &types.AttributeValueMemberS{Value: "profile#2017e9305ccc4e5781d076403c1b6725"},
							"userid": &types.AttributeValueMemberS{Value: "2017e9305ccc4e5781d076403c1b6725"},
						},
					},
				},
			}

			mock.ddb.EXPECT().BatchGetItem(ctx, gomock.Any()).Return(&batchItems, nil)
			result, err := mock.db.BatchGetItemsExact(ctx, pksk, pksk)
			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(result)).Equal(2)
			userid1 := (result[0]["userid"].(*types.AttributeValueMemberS)).Value
			userid2 := (result[1]["userid"].(*types.AttributeValueMemberS)).Value

			g.Assert(userid1).Equal("b287e655461f4b3085c8f244e394ff7e")
			g.Assert(userid2).Equal("2017e9305ccc4e5781d076403c1b6725")

			mock.ddb.EXPECT().DeleteItem(ctx, gomock.Any()).Return(nil, nil)
			mock.db.DeleteItem(ctx, item1)
			mock.ddb.EXPECT().DeleteItem(ctx, gomock.Any()).Return(nil, nil)
			mock.db.DeleteItem(ctx, item2)
		})
	})
}

func TestQuery(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	item := apipub.UserProfileResponse{
		Userid: "b287e655461f4b3085c8f244e394ff7e",
	}

	g.Describe("Query", func() {
		g.It("should succeed", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().PutItem(ctx, gomock.Any()).Return(nil, nil)
			err := mock.db.PutItemInProfileTable(context.Background(), &item)
			g.Assert(err).IsNil()

			itemGet := apipub.UserProfileResponse{}
			found := false

			getItem := &dynamodb.GetItemOutput{
				Item: map[string]types.AttributeValue{
					"pk":     &types.AttributeValueMemberS{Value: "user#b287e655461f4b3085c8f244e394ff7e"},
					"sk":     &types.AttributeValueMemberS{Value: "user#"},
					"userid": &types.AttributeValueMemberS{Value: "b287e655461f4b3085c8f244e394ff7e"},
				},
			}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(getItem, nil)
			found, err = mock.db.GetItem(context.Background(), "user#b287e655461f4b3085c8f244e394ff7e", "user#b287e655461f4b3085c8f244e394ff7e", &itemGet)
			g.Assert(err).IsNil()
			g.Assert(found).IsTrue()
			g.Assert(itemGet.Userid).Equal("b287e655461f4b3085c8f244e394ff7e")

			mock.ddb.EXPECT().DeleteItem(ctx, gomock.Any()).Return(nil, nil)
			mock.db.DeleteItem(context.Background(), &item)
		})
	})
}
