import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccounts, SteamAccounts, EpicAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
let usersSteam: SteamAccounts;
let usersEpic: EpicAccounts;

describe(`search friends[public v1]${_ds}`, () => {
  describe(`happy cases${_ds}`, () => {
    beforeAll(async () => {
      usersTwok = new TwokAccounts(4, ["leader1", "user1", "leader2", "user2"]);
      await usersTwok.loginAll({});

      usersSteam = new SteamAccounts(2, ["steamLeader", "user1"]);
      await usersSteam.loginAll({});

      usersEpic = new EpicAccounts(2, ["epicLeader", "user1"]);
      await usersEpic.loginAll({});


      // add links
      await usersSteam.acct["steamLeader"].linkParent(usersTwok.acct["leader1"]);
      await usersSteam.acct["user1"].linkParent(usersTwok.acct["user1"]);
      await usersEpic.acct["epicLeader"].linkParent(usersTwok.acct["leader2"]);
      await usersEpic.acct["user1"].linkParent(usersTwok.acct["user1"]);

      // platform accounts login again
      await usersSteam.loginAll({});
      await usersEpic.loginAll({});
    });

    afterAll(async () => {
      await usersSteam.acct["steamLeader"].unlinkParent();
      await usersSteam.acct["user1"].unlinkParent();
      await usersEpic.acct["epicLeader"].unlinkParent();
      await usersEpic.acct["user1"].unlinkParent();

      await usersTwok.logoutAll({});
      await usersSteam.logoutAll();
      await usersEpic.logoutAll();
    });

    it.each`
      scenario                                                                          | leader           | friend     | linkExist
      ${"can search for another user without steam links; show links being empty list"} | ${"steamLeader"} | ${"user2"} | ${false}
      ${"can search for another user with steam links; only show steam links"}          | ${"steamLeader"} | ${"user1"} | ${true}
      ${"can search for another user with epic links; only show epic links"}            | ${"epicLeader"}  | ${"user1"} | ${true}
    `('$scenario', async ({leader, friend, linkExist}) => {
      let testCase = {
        description: 'search friend which in the same platform with the logged-in user; check the links info',
        expected: "the matched friend is present in the result list"
      }

      let linkItem;
      let actualSearchResult: request.Response;

      switch (linkExist) {
        case true:
          if (leader == 'steamLeader') {
            linkItem = [
              {
                accountId: usersSteam.acct[friend].publicId,
                firstPartyId: usersSteam.acct[friend].platformId,
                linkType: 'steam'
              }
            ];
          } else {
            linkItem = [
              {
                accountId: usersEpic.acct[friend].publicId,
                firstPartyId: usersEpic.acct[friend].platformId,
                linkType: 'epic'
              }
            ];
          }
          break;
        case false:
          linkItem = [];
          break;
      }

      if (leader == 'steamLeader') {
        actualSearchResult = await socialApi.searchFriendV1(
          usersSteam.acct[leader],
          { q: usersTwok.acct[friend].displayName }
        );
      } else {
        actualSearchResult = await socialApi.searchFriendV1(
          usersEpic.acct[leader],
          { q: usersTwok.acct[friend].displayName }
        );
      }

      /**
       * friend search result checks
       * - without steam links: show links being [];
       * - with steam links: only show steam link list;
       * - with epic links: only show epic link list.
      */
      const expectedSearchResult = {
        status: StatusCodes.OK,
        body: {
          items: [
            {
              links: linkItem,
              name: usersTwok.acct[friend].displayName,
              userid: usersTwok.acct[friend].publicId,
            },
          ],
        },
      };
      socialApi.expectMore(
        () => {expect(actualSearchResult).toMatchObject(expectedSearchResult)},
        testCase,
        {
          resp: actualSearchResult,
          additionalInfo: {
            "fail reason": "expected friend did not appear in the search result"
          }
        }
      );
    });

    it('search with no params', async () => {
      //
      const r = await socialApi.searchFriendV1(
        usersTwok.acct["user1"],
        {}
      );

      socialApi.testStatus(StatusCodes.BAD_REQUEST, r);
    });
  });
});