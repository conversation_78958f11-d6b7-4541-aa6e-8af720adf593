loginRequestBody:
  description: Login request body
  required: true
  content:
    application/json:
      schema:
        required:
          - locale
          - email
          - password
        properties:
          email:
            $ref: "../schemas/fields.yaml#/email"
          password:
            $ref: "../schemas/fields.yaml#/password"
          locale:
            $ref: "../schemas/fields.yaml#/locale"
          appId:
            $ref: "../schemas/fields.yaml#/dnaid"
logoutRequestBody:
  description: Logout token request body
  required: false
  content:
    application/json:
      schema:
        type: object
        properties:
          refreshToken:
            description: Refresh token
            type: string
            example: eyJhbGci...xUpOD5qlY\
refreshRequestBody:
  description: Refresh token request body
  required: true
  content:
    application/json:
      schema:
        required:
          - locale
          - refreshToken
        properties:
          locale:
            $ref: "../schemas/fields.yaml#/locale"
          refreshToken:
            description: Refresh token
            type: string
            example: eyJhbGci...xUpOD5qlY\
setPlayedRequestBody:
  description: Update recently played list
  required: true
  content:
    application/json:
      schema:
        $ref: "#/setPlayedRequest"
setPresenceRequestBody:
  description: Sets Users Presence
  required: true
  content:
    application/json:
      schema:
        required:
          - status
          - gameName
        properties:
          status:
            $ref: "../schemas/fields.yaml#/presenceStatus"
          customStatus:
            $ref: "../schemas/fields.yaml#/customStatus"
          richPresence:
            $ref: "../schemas/fields.yaml#/richPresence"
          gameData:
            $ref: "../schemas/fields.yaml#/gameData"
          gameName:
            $ref: "../schemas/fields.yaml#/gameName"
          ttl:
            allOf:
              - $ref: "../schemas/fields.yaml#/ttl"
            minimum: 35
            maximum: 1800
            description:
              How long in seconds before this connection will be considered offline if no futher presence update is made.   |
              NOTE - this value MUST be at MINIMUM several seconds longer than the rate of your heartbeat timer, or your presence will always expire. |
              We recommend having your keep alive allow for at least 3 heartbeats to occur in the window (espepecially on mobile), |
              but it's entirely up to you how much of a grace period you want.  And for how a long droppped connection can be recovered. |
              The minimum is set to 35s because our plugin heartbeat is 30s.  We do NOT recommend using 35s.  Default is 5m.
          meta:
            allOf:
              - $ref: "../schemas/schemas.yaml#/meta"
            description: Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
          joinContext:
            $ref: "../schemas/schemas.yaml#/joinContext"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
setActiveGroupRequestBody:
  description: Sets Active Group for a user.  This determines which group should be used for automated actions. Also subscribes to the group presence object
  required: true
  content:
    application/json:
      schema:
        required:
          - activeGroupid
        properties:
          activeGroupid:
            $ref: "../schemas/fields.yaml#/groupid"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
importBlocklistRequestBody:
  description: Batch import blocklist request body. Must submit 20 or fewer records per request.
  required: true
  content:
    application/json:
      schema:
        required:
          - userids
        properties:
          isFirstParty:
            type: boolean
            example: false
            description: optional flag to add the platform blocklist users to the main blocklist.
          userids:
            type: array
            description: an array of user ids to add to blocklist.  set isFirstParty = true if adding first party ids.  must all be same ost as requestor.
            items:
              allOf:
                - $ref: "../schemas/fields.yaml#/userid"
            example:
              [
                "b287e655461f4b3085c8f244e394ff7e",
                "effe28b27efc6594e43bfc0879b40085",
              ]
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Add users to the blocklist":
          value:
            userids:
              [
                "b287e655461f4b3085c8f244e394ff7e",
                "effe28b27efc6594e43bfc0879b40085",
              ]
        "Add first party users to the blocklist":
          value:
            isFirstParty: true
            userids: ["76543210123456789", "76543210234567890"]
addBlocklistRequestBody:
  description: Add user to blocklist
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
setFriendsRequestBody:
  description: Make friend request body
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          message:
            type: string
            example: Hello there!
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
setFriendViewedRequestBody:
  description: Update friend status
  required: true
  content:
    application/json:
      schema:
        type: object
        properties:
          viewed:
            type: boolean
            example: true
            default: true
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Mark pending friendship as viewed":
          value:
            viewed: true
setReportRequestBody:
  required: true
  description: Required and optional fields for a report.
  content:
    application/json:
      schema:
        required:
          - reportingUserId
          - reportingCategory
          - reportingUserLocale
          - reportMessage
          - platform
          - subjectTitle
        properties:
          reportingUserId:
            type: string
            description: The reporting user's full account id.
          reportingUserLocale:
            type: string
            description: The reporting user's locale.
            example: zh-CN
          reportingCategory:
            $ref: "../schemas/fields.yaml#/reportingCategory"
            example: Player_Boosting_Cheating
          reportMessage:
            type: string
            description: The user's specified report message. Limit the size to 5 KB.
            example: Player has said some inflammatory remarks.
          platform:
            type: string
            example: PlayStation 5
          subjectTitle:
            type: string
            example: Notice of Update on your Reported Content
            description: The subject title of a report.
          versionNumber:
            type: string
            description: The version number of a game.
          reportingContentType:
            type: string
            description: The type of reporting content.
            example: Abuse of Gameplay Mechanics
          os:
            type: string
            example: Windows 11
          gameSessionInfo:
            type: object
            description: The additional information for a report in a key-value pair format.
            example:
              key1: onlineSessionId
              key2: session id
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
createGroupRequestBody:
  required: true
  content:
    application/json:
      schema:
        required:
          - joinRequestAction
        properties:
          maxMembers:
            $ref: "../schemas/fields.yaml#/maxMembers"
          joinRequestAction:
            $ref: "../schemas/fields.yaml#/joinRequestAction"
          password:
            $ref: "../schemas/fields.yaml#/password"
          canMembersInvite:
            $ref: "../schemas/fields.yaml#/canMembersInvite"
          canCrossPlay:
            $ref: "../schemas/fields.yaml#/canCrossPlay"
          meta:
            $ref: "../schemas/schemas.yaml#/meta"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Create public group that anyone can join":
          value:
            maxMembers: 5
            joinRequestAction: auto-approve
        "Create manual group with a password":
          value:
            joinRequestAction: manual
            password: "********"
        "Create invite only group":
          value:
            joinRequestAction: auto-reject
            canMembersInvite: true
        "Create a group where inviter has to accept invite":
          value:
            joinRequestAction: manual
            canMembersInvite: true
        "Create no crossplay group":
          value:
            joinRequestAction: manual
            canCrossPlay: false
        "Create a group with meta data":
          value:
            joinRequestAction: manual
            meta:
              key1: value1
              key2: value2
updateGroupRequestBody:
  required: true
  content:
    application/json:
      schema:
        $ref: "#/updateGroupRequest"
updateGroupMemberRequestBody:
  description: Update group member role body
  required: true
  content:
    application/json:
      schema:
        $ref: "#/updateGroupMemberRequest"
updateGroupMemberMetaRequestBody:
  description: Update group member's metadata.  Keep in mind.  All data from a metadata write is taken as a full record each time.  Any fields missing will be removed, any fields added or updated will be added or updated.  We do not keep any history of previous meta data records.  Data will always be clobbered by most recent data.
  required: true
  content:
    application/json:
      schema:
        type: object
        required: [meta, timestamp]
        properties:
          meta:
            type: object
            description: A group member's latest metadata.
            example:
              key1: value1
              key2: value2
          timestamp:
            description: Represents the client time when the meta update is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
            type: integer
            format: uint64
            example: 1737484653930
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
sendControlMessageRequestBody:
  description: Control message request body. Max size for binary is 5120 bytes
  required: true
  content:
    application/json:
      schema:
        required:
          - payload
        properties:
          payload:
            type: string
            example: "ZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFHRUFBQUJ4Q0FZQUFBREYwTTA0QUFBQUFYTlNSMElBcnM0YzZRQUFBQVJuUVUxQkFBQ3hqd3Y4WVFVQUFBQUpjRWhaY3dBQURzUUFBQTdFQVpVckRoc0FBQUt0U1VSQlZIaGU3WnRiVGdOQkRBU3puQ3hIejgwQ1NIelRJejg2SGFoSWZIbkg5bFF4bTNnRDErMTJlMzc5OEhvaGdZOFgxcWIwRHdFa0JQd3FJQUVKQVFRQ1d1QWtJQ0dBUUVBTG5BUWtCQkFJYUlHVGdJUUFBZ0V0Y0JJQ0pGeU9aMGZQNTNzL25ycXViMHg3TDA3Q0h0dmp6RWc0UnJWM0lSTDIyQjVuUnNJeHFyMExrYkRIOWpnekVvNVI3VjJJaEQyMng1bmJjOExKRExEOU9mdDR0OFVMMVI2NysrTWtGTVZNTGtQQ0pNMWlMaVFVd1UwdVE4SWt6V0l1SkJUQlRTNUR3aVROWWk0a0ZNRk5Ma1BDSk0xaUxpUVV3VTB1UThJa3pXSXVKQlRCVFM1RHdpVE5ZaTRrRk1GTkxrUENKTTFpTGlRVXdVMHVpL2crWWZ0NWZUZC9kNzBTeGtsUWhBeHhKQmdncXhKSVVJUU1jU1FZSUtzU1NGQ0VESEVrR0NDckVraFFoQXp4OXB6d2VEeGttL2Y3WFY2VGZJSGFZM2QvbklRQSswaEFRZ0NCZ0JZNENVZ0lJQkRRQWljQkNRRUVBbHF3ekFrQisxeHRnVGxoRmE4bk9lOEpIczYvVmtFQ0VnSUlCTFRBU1VCQ0FJR0FGamdKQVJJaTVnVDFPVnY5M1kvaXFQN1BXSDFmb1BLci90VjZUb0lpWklnandRQlpsVUNDSW1TSUk4RUFXWlZBZ2lKa2lDUEJBRm1WUUlJaVpJaEh6QW1HZmE2V1lFNVl4ZXRKenUzSXc1bnZFd0k0SXdFSjZRUUMrdU05QVFrQkJBSmFhTThKSjN2b1BxOC9xYkY1VFhjT1VMMXhPMUtFREhFa0dDQ3JFa2hRaEF4eEpCZ2dxeEpJVUlRTWNTUVlJS3NTU0ZDRURISExuTkRkeC9hY3NUMEhxUDF6RWhRaFF4d0pCc2lxQkJJVUlVTWNDUWJJcWdRU0ZDRkRIQWtHeUtvRUVoUWhRL3d0NWdURm9UdEhNQ2Nvd3Y4Z3p1MG9RRElTa0JCQUlLQUZUZ0lTQWdnRXRNQkpRRUlBZ1lBVy9zU3dGc0N4MVFLM294YSttY1ZJbU9IWXlvS0VGcjZaeFVpWTRkaktnb1FXdnBuRlNKamgyTXFDaEJhK21jVkltT0hZeW9LRUZyNlp4VWlZNGRqS2dvUVd2cG5GbjBlVFA4dGMwOTkvQUFBQUFFbEZUa1N1UW1DQw"
          senderid:
            allOf:
              - $ref: "../schemas/fields.yaml#/dnaid"
            description: sender should be a dna userid or a dna server instance id.
          event:
            type: string
            example: pingLocation
            description: a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
          timestamp:
            $ref: "../schemas/fields.yaml#/timestamp"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
    application/octet-stream:
      schema:
        type: string
        format: binary
sendInviteRequestBody:
  description: membership request body
  required: true
  content:
    application/json:
      schema:
        type: object
        description: schema for sending an invite
        properties:
          ttl:
            allOf:
              - $ref: "../schemas/fields.yaml#/ttl"
            default: 3600
            minimum: 1
            maximum: 2628288
          isFirstPartyInvite:
            $ref: "../schemas/fields.yaml#/isFirstPartyInvite"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Invite a user via first party platform id":
          value:
            isFirstPartyInvite: true
        "Invite a user with non-default expiration":
          value:
            ttl: 300
acceptInviteRequestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        description: schema for accepting invites
        required:
          - canCrossPlay
        properties:
          canCrossPlay:
            $ref: "../schemas/fields.yaml#/canCrossPlay"
          isFirstPartyInvite:
            $ref: "../schemas/fields.yaml#/isFirstPartyInvite"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Accepting an invite":
          value:
            canCrossPlay: true
        "Accept first party account invitation":
          value:
            isFirstPartyInvite: true
            canCrossPlay: true
        "Accept with crossplay off":
          value:
            canCrossPlay: false
            isFirstPartyInvite: true
requestJoinRequestBody:
  description: join request body
  required: true
  content:
    application/json:
      schema:
        type: object
        description: schema for sending join requests
        required:
          - canCrossPlay
        properties:
          canCrossPlay:
            $ref: "../schemas/fields.yaml#/canCrossPlay"
          password:
            $ref: "../schemas/fields.yaml#/password"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Request to join group with crossplay on":
          value:
            canCrossPlay: true
        "Request to join group with crossplay off":
          value:
            canCrossPlay: false
        "Request to join group with a password":
          value:
            canCrossPlay: true
            password: p@ssw0rd
approveJoinRequestBody:
  description: join request body
  required: true
  content:
    application/json:
      schema:
        type: object
        description: schema for approving join requests
        properties:
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
      examples:
        "Approve join request":
          value: {}
sendChatMessageRequestBody:
  description: chat message
  required: true
  content:
    application/json:
      schema:
        type: object
        required: [message]
        properties:
          message:
            $ref: "../schemas/schemas.yaml#/chatMessage"
searchAccountCriteria:
  type: object
  description: object used for criteria when making a DNA user search
  required: true
  properties:
    accountId:
      description: "Account Identifier. Mandatory if the type is accountsById"
      type: "string"
    displayName:
      description: "Display Name. Mandatory if the type is fullAccountByDisplayName"
      type: "string"
    firstPartyId:
      allOf:
        - $ref: "../schemas/fields.yaml#/firstPartyid"
      description: "First Party Identifier. Mandatory if the type is equal to accountsByFirstPartyId or fullAccountIdByFirstPartyId"
      type: string
    firstPartyAlias:
      description: "First Party Alias. Mandatory if the type is equal to accountsByFirstPartyAlias"
      type: string
    onlineServiceType:
      allOf:
        - $ref: "../schemas/fields.yaml#/onlineServiceType"
      description: "The online service platform of the account. Mandatory if the type is equal to accountsByFirstPartyId or fullAccountIdByFirstPartyId"
updateGroupRequest:
  type: object
  description: request schema for update group
  properties:
    maxMembers:
      $ref: "../schemas/fields.yaml#/maxMembers"
    meta:
      $ref: "../schemas/schemas.yaml#/meta"
    joinRequestAction:
      $ref: "../schemas/fields.yaml#/joinRequestAction"
    password:
      $ref: "../schemas/fields.yaml#/password"
    canMembersInvite:
      $ref: "../schemas/fields.yaml#/canMembersInvite"
    teleMeta:
      $ref: "../schemas/schemas.yaml#/telemetryMetaData"
updateGroupMemberRequest:
  type: object
  description: request schema for update group member
  required:
    - role
  properties:
    role:
      $ref: "../schemas/fields.yaml#/groupMemberRole"
    teleMeta:
      $ref: "../schemas/schemas.yaml#/telemetryMetaData"
setPlayedRequest:
  type: object
  description: request schema for set recetnly played users
  required:
    - users
  properties:
    ttl:
      allOf:
        - $ref: "../schemas/fields.yaml#/ttl"
      default: 3600
      minimum: 1
      maximum: 2628288
    users:
      description: Array of recently played users
      type: array
      items:
        $ref: "#/recentlyPlayedUsers"
    teleMeta:
      $ref: "../schemas/schemas.yaml#/telemetryMetaData"
recentlyPlayedUsers:
  type: object
  description: schema for sending recently played users in requests
  required:
    - userid
  properties:
    userid:
      $ref: "../schemas/fields.yaml#/dnaid"
    weight:
      description: Sorting is done by last played unix time stamp in milliseconds + weight descending.  Weight can be used to make sorting not strictly by datetime.
      type: integer
      example: 100
    context:
      description: Used by the game to store any context for the recenly played user.
      type: object
upsertSessionAuthRequestBody:
  content:
    application/json:
      schema:
        type: object
        description: send auth code for user/ost combo for session syncing auth that requires user first party token
        required:
          - authCode
        properties:
          authCode:
            $ref: "../schemas/fields.yaml#/authCode"
acceptInviteBySessionRequestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        description: schema for accepting invites by session information
        properties:
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
incrementEndorsementRequestBody:
  required: true
  content:
    application/json:
      schema:
        type: object
        description: schema for accepting invites by session information
        required:
          - incrementValue
        properties:
          endorsementName:
            allOf:
              - $ref: "../schemas/fields.yaml#/endorsementName"
            description: only include a name if you would like to increment a custom endorsement.  if no endorsement name is included, the count will be incremented to the default endorsement/commend bucket
          incrementValue:
            $ref: "../schemas/fields.yaml#/incrementValue"
          isPositive:
            $ref: "../schemas/fields.yaml#/isPositive"
          isPrivate:
            $ref: "../schemas/fields.yaml#/isPrivate"
          teleMeta:
            $ref: "../schemas/schemas.yaml#/telemetryMetaData"
