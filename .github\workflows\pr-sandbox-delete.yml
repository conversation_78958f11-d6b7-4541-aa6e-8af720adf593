name: Destroy Sandbox Env

on:
  pull_request:
    types: [closed]
    branches:
      - develop

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

concurrency: pr-${{ github.head_ref }}
jobs:
  build:
    name: 'Destroy existing infrastructure'
    env:
      CLUSTER: t2gp-non-production
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
    runs-on: [t2gp-arc-linux]

    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - uses: actions/checkout@v4
        with:
          ref: ${{github.sha}}
      - name: Environment Variables
        run: |
          echo BRANCH_NAME=$(echo "${{ github.head_ref }}" | awk '{print tolower($0)}') >> $GITHUB_ENV
          echo RELEASE_NAME="pr-${{github.event.pull_request.number}}" >> $GITHUB_ENV
      - name: He<PERSON> Uninstall
        uses: take-two-t2gp/app-charts-commit@v0.7
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          uninstall: true
          sandbox: true
          service: social-service
          environment: ${{env.RELEASE_NAME}}
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: delete
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          key: '{ "env_label": "${{env.RELEASE_NAME}}" }'
