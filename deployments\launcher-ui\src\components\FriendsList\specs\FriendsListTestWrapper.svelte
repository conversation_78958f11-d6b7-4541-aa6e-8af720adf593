<script lang="ts">
  import { setContext } from 'svelte';
  import {
    CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT,
    EVENT_FRIENDS_FETCH,
    EVENT_FRIENDS_FETCH_RESULT,
    INITIAL_LANGUAGE,
  } from '../../../constant';
  import { initI18nContext, SocialServicesContext } from '../../../context';
  import { useFriendsQuery } from '../../../hooks';
  import type { QueryResult, SocialFriend } from '../../../services';
  import FriendsList from '../FriendsList.svelte';

  export let context: SocialServicesContext;

  const transportService = context.getTransportService();
  const friendsQueryResult = useFriendsQuery();
  setContext(CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT, context);
  initI18nContext(INITIAL_LANGUAGE);

  transportService.subscribeEvent(
    EVENT_FRIENDS_FETCH_RESULT,
    (_, result: QueryResult<SocialFriend[]>) => {
      friendsQueryResult.set(result);
    }
  );

  transportService.publishEvent(EVENT_FRIENDS_FETCH);
</script>

<FriendsList />
