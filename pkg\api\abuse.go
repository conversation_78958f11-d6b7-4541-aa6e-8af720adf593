package api

import (
	"encoding/json"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sns"
	"github.com/aws/aws-sdk-go-v2/service/sns/types"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

func (api *SocialPublicAPI) ReportUserAbuse(w http.ResponseWriter, r *http.Request, pUserid apipub.PUserid) {
	log := logger.Get(r)
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	// decode request
	var request apipub.SetReportRequestBody
	if !DecodeBody(w, r, &request) {
		return
	}

	if request.ReportingUserId == pUserid {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsCannotReportYourself))
		return
	}

	// Limit the size of ReportMessage to 5 KB.
	if len(request.ReportMessage) > 5000 {
		errs.Return(w, r, errs.New(http.StatusRequestEntityTooLarge, errs.EReportMessageTooLarge))
		return
	}

	reportingUserId := request.ReportingUserId
	productId := token.Claims.ProductID
	telemetryEventid := utils.GenerateNewULID()
	report := apipub.AbuseReport{
		ReportingCategory:      request.ReportingCategory,
		ReportingUserId:        reportingUserId,
		ReportingContentType:   request.ReportingContentType,
		ReportedUserId:         pUserid,
		ReportingUserIpAddress: r.RemoteAddr,
		ReportingUserLocale:    request.ReportingUserLocale,
		RequestId:              middleware.GetReqID(r.Context()),
		ReportingUserAgent:     r.UserAgent(),
		ReportingUserProductId: productId,
		ReportingUserPlatform:  request.Platform,
		TelemetryEventId:       telemetryEventid,
		ReportMessage:          request.ReportMessage,
		Os:                     request.Os,
		SubmissionTime:         time.Now().Format(time.RFC3339),
		SubjectTitle:           request.SubjectTitle,
		GameVersionNumber:      request.VersionNumber,
		TeleMeta:               request.TeleMeta,
	}

	reportedUser, err := api.getUserProfile(r.Context(), report.ReportedUserId, true)
	if reportedUser == nil {
		log.Error().Err(err).Str("reported user", report.ReportedUserId).Msg("no profile found")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EInvalidUserID))
		return
	}

	if err != nil {
		log.Error().Err(err).Str("reported user", report.ReportedUserId).Msg("could not get profile")
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EProfileFetchFailed))
		return
	}

	if reportedUser.DisplayName != nil {
		report.ReportedUserDisplayName = *reportedUser.DisplayName
	}

	reportingUser, err := api.getUserProfile(r.Context(), report.ReportingUserId, true)
	if reportingUser == nil {
		log.Error().Err(err).Str("reporting user", report.ReportingUserId).Msg("no profile found")
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EInvalidUserID))
		return
	}

	if err != nil {
		log.Error().Err(err).Str("reporting user", report.ReportingUserId).Msg("could not get profile")
	}

	if reportingUser.DisplayName != nil {
		report.ReportingUserDisplayName = *reportingUser.DisplayName
	}

	if request.GameSessionInfo != nil {
		report.GameSessionInfo = &map[string]interface{}{}
		*report.GameSessionInfo = *request.GameSessionInfo
	}

	jsonData, err := json.Marshal(report)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EFriendsReportingFailed))
		return
	}

	messageAttributes := map[string]types.MessageAttributeValue{
		"ProductId": {
			DataType:    aws.String("String"),
			StringValue: aws.String(productId),
		},
	}

	snsInput := sns.PublishInput{
		Message:           aws.String(string(jsonData)),
		TopicArn:          &api.Cfg.ReportSNSTopicArn,
		MessageAttributes: messageAttributes,
	}

	output, err := api.Sns.SNSClient.Publish(r.Context(), &snsInput)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EFriendsReportingFailed))
		return
	}

	response := make(map[string]string)
	if output != nil {
		log.Info().
			Str("request Id", report.RequestId).
			Str("message Id", *output.MessageId).
			Str("telemetryEventid", telemetryEventid).
			Str("event", "abuse report published").
			Msg("abuse report published")

		response["MessageId"] = *output.MessageId
	}

	// process telemeta if exists for telemetry
	additionalInfo := make(map[string]string)
	if request.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*request.TeleMeta, &additionalInfo)
	}

	appid := token.Claims.Issuer
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	api.Tele.SendReportEvent(r.Context(), telemetry.BuildReportTeleMeta(&report, telemetry.KReportPlayer, telemetryEventid, productId, ost, &appid, &additionalInfo))

	ReturnOK(w, r, response)
}
