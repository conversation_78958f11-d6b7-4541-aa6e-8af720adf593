# Test Account Management Tool

## Setup Running Environment
The following steps are for setting up the environment to run the Test Account Management Tool.

### Common
Access permission to [d2c-automation](https://github.com/take-two-t2gp/d2c-automation) is required since the tool uses libraries in that repo.

### Linux
#### 0. You might need to install the venv package before step 1. For example:
```
apt install python3.10-venv
```
#### 1. Specify your PAT in the environment variables
```
export PAT=xxxxx
```
#### 2. Setup virtual environment and install required Python modules
```
./setup.sh
```
#### 3. Provide basic auth for the DNA server.
<PERSON>ript would still work without it. Only the generate account functionality requires this. [1password](https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/vaj3w3pvqxbjy52dfo55ocwgwy)
```
export DNA_BASIC_AUTH=xxxxx
```
#### 4. Run the script
```
./account_mgmt_cli.py
```

### Windows

#### 1. Specify your PAT in the environment variables
```
set PAT=xxxxx
```
#### 2. Setup virtual environment and install required Python modules
```
setup.bat
```
#### 3. Provide basic auth for the DNA server.
Script would still work without it. Only the generate account functionality requires this. [1password](https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/allitems/vaj3w3pvqxbjy52dfo55ocwgwy)
```
set DNA_BASIC_AUTH=xxxxx
```
#### 4. Run the script with either of the following:
(activate virtual environment and run the script inside it.)
```
account_mgmt_cli_env\Scripts\activate
python account_mgmt_cli.py
```
-or-
```
account_mgmt_cli_env\Scripts\python.exe account_mgmt_cli.py
```
If you see the the help text then the script was successfully run.

## Update Running Environment
Run `update_account_mgmt.sh` to update account management related libraries only. Or run `setup.sh` / `setup.bat` again.

## Setup [aws sso login](https://paper.dropbox.com/doc/How-to-New-Okta-Tile-for-AWS-SSO-Login--B5yqE71D0LdzpZSpKy1Jfr6lAg-hRoeiMoX37acpAeVQC3NG)
AWS access is required since account management data are stored in a S3 bucket.

## Cheat Sheet
The following is a cheat sheet for moving 100 good accounts from the pool to the user, and generate the `.env.twok_accounts` file to run tests. Please read the documentation for more detail.
```
./account_mgmt_cli.py -at twok -l user_name
./account_mgmt_cli.py -at twok -m 100 g None user_name
./account_mgmt_cli.py -at twok -p
./account_mgmt_cli.py -at twok -u
./account_mgmt_cli.py -at twok -s owner user_name ci_file
```

## Manually Replace Bad Accounts for CI
```
cd ../account_health_check
./replace_bad_account.sh CI <2K Public ID> "input remark here"
```

## Documentation
Please read: [Test Account Management Tool](https://paper.dropbox.com/doc/Test-Account-Management-Tool--B52FXpWGosSAaIVb8Xc03Ya0Ag-3cxVBOprQy8WrfqhldMPU)