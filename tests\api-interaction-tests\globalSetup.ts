import { config } from './integration/lib/config';
import {
  allTwokAcctHealthCheck
} from './utils/account_health_check/account_health_check';

module.exports = async () => {
  console.log();
  console.log(`API Access Level: ${config.socialAPIAccessLevel}`);
  console.log(`Environment: ${config.socialServiceEnvironment}`);
  console.log(`Version: ${config.socialAPIVersion}`);
  console.log(`API: ${config.socialService.currAccessLevel.currEnv.currVer.api}`);
  console.log(`MQTT: ${config.socialService.currAccessLevel.currEnv.currVer.mqtt}`);
  console.log();

  if (config.socialAPIAccessLevel == "public") {
    await allTwokAcctHealthCheck(
      // list of health checks to be performed
      [
        "block",
        "friend",
        "group",
        "childAccount",
        "twokInfo"
      ],
      // replace bad accounts
      true,
      // account owner
      "CI",
      // dump log to console
      true
    );
  } else {
    console.log("Skipping account health check for trusted server tests.\n");
  }
};