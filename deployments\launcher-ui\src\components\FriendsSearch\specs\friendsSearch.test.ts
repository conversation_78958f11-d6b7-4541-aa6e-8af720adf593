import { render, waitFor } from '@testing-library/svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  MOCKED_FRIENDS,
  transportServiceMock,
} from '../../../services/__mocks__';
import { DirectImportCardMock } from '../../FriendImportCard/__mock__';
import SearchBarMock from '../../SearchBar/__mock__/SearchBar.svelte';
import FriendsSearchWrapper from './FriendsSearchWrapper.svelte';

jest.mock('../../SearchBar', () => ({
  SearchBar: SearchBarMock,
}));

jest.mock('../../FriendImportCard', () => ({
  DirectImportCard: DirectImportCardMock,
}));

const socialServicesMock = new SocialServices({
  transportService: transportServiceMock,
  friendsService: friendsServiceMock,
});

describe('FriendsSearch', () => {
  it('should render friends search', async () => {
    const { getByTestId } = render(FriendsSearchWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    expect(getByTestId('search-bar-mock')).not.toBeNull();
  });

  it('should render friends search with searched results', async () => {
    const { getAllByTestId } = render(FriendsSearchWrapper, {
      props: {
        context: socialServicesMock,
        initSearchTerm: MOCKED_FRIENDS[0].name,
      },
    });

    await waitFor(() => {
      expect(getAllByTestId('direct-import-card-mock').length).toBe(1);
    });
  });
});
