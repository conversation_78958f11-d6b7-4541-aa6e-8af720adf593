const { markdown, danger, fail } = require('danger');
const filesize = require('filesize');
const { existsSync, readFileSync } = require('fs');
const { exec } = require('child_process');
const lint = require('@commitlint/lint').default;
const commitLintConfig = require('@commitlint/config-conventional');
const STATS_JSON = './.size-snapshot.json';
const S3_LOCATION =
  'http://t2gp-web-bundlesize.s3-website-us-west-1.amazonaws.com/t2gp-social-frontend/.size-snapshot.json';
const GZIP_SIZE_SIGNIFICANT_MIN = 100;
const BUNDLE_SIZE_SIGNIFICANT_MIN = 300;

/**
 * Helper to figure out the percentage
 * @param {number} prev the previous value
 * @param {number} current the current value
 */
const fractionalChange = (prev, current) => (current - prev) / prev;

/**
 * Function takes current and previous results and generates bundle difference data to populate in a table
 * @param {object} current the current bundle size results built from the local bundles
 * @param {object} prevResults the previous results on master stored on S3
 */
const generateResultsArray = (currentResults, prevResults) => {
  const keys = Object.keys(currentResults);
  return keys.map(result => {
    const cur = currentResults[result];
    const prev = prevResults[result];

    if (cur === prev) {
      // We didn't rebuild this bundle.
      return;
    }

    const size = cur.minified;
    const gzip = cur.gzipped || 0;
    let prevSize = prev ? prev.minified : 0;
    let prevGzip = prev ? prev.gzipped || 0 : 0;

    return {
      filename: result,
      packageName: result,
      mode: cur.treeshaked ? 'esm' : 'cjs',
      prevSize: filesize(prevSize),
      prevFileSize: filesize(size),
      prevFileSizeChange: fractionalChange(prevSize, size),
      prevFileSizeAbsoluteChange: size - prevSize,
      prevGzip: filesize(prevGzip),
      prevGzipSize: filesize(gzip),
      prevGzipSizeChange: fractionalChange(prevGzip, gzip),
      prevGzipSizeAbsoluteChange: gzip - prevGzip,
    };
  });
};

/**
 * Utility function to run git as a child process
 * @param {string} args
 */
const git = args =>
  new Promise(res => {
    exec('git ' + args, (err, stdout, stderr) => {
      if (err) {
        throw err;
      } else {
        res(stdout.trim());
      }
    });
  });

/**
 * Format the decimal and add a red triangle if the percentage went up
 * @param {number} change the percentage change
 */
const formatWithPercentage = change => {
  if (!isFinite(change)) {
    // When a new package is created
    return 'n/a';
  }
  const formatted = (change * 100).toFixed(1);
  if (/^-|^0(?:\.0+)$/.test(formatted)) {
    return `${formatted}%`;
  } else {
    return `:small_red_triangle:+${formatted}%`;
  }
};

/**
 * Generates a markdown table used to display the bundle size in the GitHub PR
 * @param {string[]} headers headers for the table
 * @param {string[]} body body for the table
 */
const generateMDTable = (headers, body) => {
  const tableHeaders = [
    headers.join(' | '),
    headers.map(() => ' --- ').join(' | '),
  ];

  const tablebody = body.map(r => r.join(' | '));
  return tableHeaders.join('\n') + '\n' + tablebody.join('\n');
};

/**
 * main function that grabs the bundle size information from master
 * and compares it to the local bundle sizes of the branch
 */
const runBundleComparison = async () => {
  if (!existsSync(STATS_JSON)) {
    // This indicates the build failed previously.
    // In that case, there's nothing for the Dangerfile to do.
    // Exit early to avoid leaving a redundant (and potentially confusing) PR comment.
    warn(
      'No bundle size information found. This indicates the build ' +
        'job failed.'
    );
    process.exit(0);
  }

  const currentBuildResults = JSON.parse(readFileSync(STATS_JSON));

  // Use git locally to grab the commit which represents the place
  // where the branches differ

  const upstreamRepo = danger.github.pr.base.repo.full_name;

  if (upstreamRepo !== 'take-two-t2gp/t2gp-social-frontend') {
    // Exit unless we're running in the main repo
    return;
  }

  const baseCommit = await git(
    `merge-base HEAD origin/${danger.github.pr.base.ref}`
  );

  let previousBuildResults = null;
  try {
    const statusesResponse = await fetch(
      `https://api.github.com/repos/take-two-t2gp/t2gp-social-frontend/commits/${baseCommit}/status`
    );
    const { state } = await statusesResponse.json();
    if (state === 'failure') {
      warn(`Base commit is broken: ${baseCommit}`);
      return;
    }

    const baseArtifactsInfoResponse = await fetch(S3_LOCATION, {
      mode: 'cors', // no-cors, *cors, same-origin
    });
    previousBuildResults = await baseArtifactsInfoResponse.json();
  } catch (error) {
    warn(`Failed to fetch build artifacts for base commit: ${baseCommit}`);
    return;
  }

  if (previousBuildResults === null) {
    warn(`Could not find build artifacts for base commit: ${baseCommit}`);
    return;
  }

  const results = generateResultsArray(
    currentBuildResults,
    previousBuildResults
  );

  const packagesToShow = results.filter(
    r =>
      Math.abs(r.prevFileSizeAbsoluteChange) >= GZIP_SIZE_SIGNIFICANT_MIN ||
      Math.abs(r.prevGzipSizeAbsoluteChange) >= BUNDLE_SIZE_SIGNIFICANT_MIN // bytes // bytes
  );

  if (packagesToShow.length) {
    let allTables = [];

    const mdHeaders = [
      'File',
      'Format',
      'Filesize Diff',
      'Gzip Diff',
      'Prev Size',
      'Current Size',
      'Prev Gzip',
      'Current Gzip',
    ];

    const mdRows = packagesToShow.map(r => [
      r.filename,
      r.mode,
      formatWithPercentage(r.prevFileSizeChange),
      formatWithPercentage(r.prevGzipSizeChange),
      r.prevSize,
      r.prevFileSize,
      r.prevGzip,
      r.prevGzipSize,
    ]);

    allTables.push(`\n## T2GP SOCIAL FRONTEND sizes`);
    allTables.push(generateMDTable(mdHeaders, mdRows));

    const summary = `
 
  <p>Comparing: ${baseCommit}...${danger.github.pr.head.sha}</p>
  ${allTables.join('\n')}
 
  `;
    markdown(summary);
  } else {
    markdown('No significant bundle size changes to report.');
  }
};

//print run bundle
runBundleComparison();

//check PR title
lint(danger.github.pr.title, commitLintConfig.rules).then(({ errors }) => {
  if (errors.length) {
    errors.forEach(item => {
      fail(`PR title (${danger.github.pr.title}): ${item.message}`);
    });
  }
});
