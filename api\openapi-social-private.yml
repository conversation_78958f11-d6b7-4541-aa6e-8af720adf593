openapi: 3.0.0
info:
  title: T2GP Social Service
  version: 'REPLACE_ME'
  description: '"X-T2GP-Label" header is used to change identity provider. Defaults to "dna" if not set. Acceptable values are "dna" and "pdi".'
servers:
  - url: https://social-service-develop-private.d2dragon.net/v2
  - url: https://social-admin.d2dragon.net/v2
  - url: http://localhost:8000/v2
paths:
  /dev/make-friend:
    get:
      operationId: makeFriend
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/id1'
        - $ref: '#/components/parameters/id2'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/make-unfriend:
    get:
      operationId: makeUnfriend
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/id1'
        - $ref: '#/components/parameters/id2'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/subscriptions/{userid}:
    get:
      operationId: getVmqSubscriptions
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/userid'
      responses:
        200:
          description: List MQTT Subscriptions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listResponse'
                properties:
                  items:
                    type: array
                    items:
                      $ref: '#/components/schemas/subscription'

        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
    delete:
      operationId: deleteVmqSubscriptions
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/userid'
      responses:
        200:
          description: Clear all MQTT Subscriptions
          content:
            application/json:
              schema:
                type: object
                example:
                  text: Done
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
  /dev/error/{errorid}:
    get:
      operationId: getDevError
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/errorid'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/ulids/{ulid}:
    get:
      operationId: decodeUlid
      parameters:
        - $ref: '#/components/parameters/ulid'
      tags:
        - Development
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                example:
                  createDate: Mon Jan 04 2021 19:20:57 GMT-0800 (Pacific Standard Time)

  /dev/vmq/sessions:
    get:
      operationId: getVmqSessions
      tags:
        - VMQ
      responses:
        200:
          description: VMQ connected client sessions
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  example:
                    client_id: socialweb-b287e655461f4b3085c8f244e394ff7e-b7ed280c
                    is_online: true
                    mountpoint: ''
                    peer_host: **********
                    peer_port: 40140
                    user: b287e655461f4b3085c8f244e394ff7e
  /dev/vmq/version:
    get:
      operationId: getVmqVersion
      tags:
        - VMQ
      responses:
        200:
          description: VMQ Plugin Version
          content:
            application/json:
              schema:
                type: object
                example:
                  git_hash: d53fb03
                  build_date: Mon Jan 04 2021 19:20:57 GMT-0800 (Pacific Standard Time)
                  version: 0.1.21-d53fb03
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
  /dev/vmq/status:
    get:
      operationId: getVmqStatus
      tags:
        - VMQ
      responses:
        200:
          description: VMQ Server Info
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  example:
                    <EMAIL>:
                      num_online: 1
                      num_offline: 0
                      msg_in: 27
                      msg_out: 0
                      queue_in: 2506
                      queue_out: 2506
                      queue_drop: 0
                      queue_unhandled: 0
                      num_subscriptions: 5
                      num_retained: 3
                      matches_local: 21
                      matches_remote: 18
                      mystatus:
                        - <EMAIL>: true
                          <EMAIL>: true
                      listeners:
                        - type: http
                          status: running
                          ip: 0.0.0.0
                          port: 8100
                          mountpoint: []
                          max_conns: 10000
                        - type: http
                          status: running
                          ip: 0.0.0.0
                          port: 8888
                          mountpoint: []
                          max_conns: 10000
                        - type: vmq
                          status: running
                          ip: *************
                          port: 44053
                          mountpoint: []
                          max_conns: 10000
                        - type: mqttws
                          status: running
                          ip: 0.0.0.0
                          port: 8080
                          mountpoint: []
                          max_conns: 10000
                        - type: mqtt
                          status: running
                          ip: 0.0.0.0
                          port: 1883
                          mountpoint: []
                          max_conns: 10000
                      version: 1.12.3
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
  /dev/dynamodb/{userid}:
    get:
      operationId: getDynamoDBUserData
      tags:
        - DynamoDB
      parameters:
        - $ref: '#/components/parameters/userid'
      responses:
        200:
          description: DynamoDB items for the user
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
  /dev/sync/firstParty/{firstPartyid}/platform/{platform}/token:
    get:
      operationId: getFirstPartyToken
      tags:
        - Sync
      parameters:
        - $ref: '#/components/parameters/firstPartyid'
        - $ref: '#/components/parameters/platform'
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/firstPartyTokenResponse'
        '404':
          $ref: '#/components/responses/404'
    post:
      operationId: setFirstPartyToken
      tags:
        - Sync
      parameters:
        - $ref: '#/components/parameters/firstPartyid'
        - $ref: '#/components/parameters/platform'
      requestBody:
        description: First Party Token With Optional Refresh
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/firstPartyTokenRequest'
      responses:
        200:
          description: successful write of tokens
  /dev/sync/firstParty/{firstPartyid}/platform/{platform}/refresh:
    get:
      operationId: getFirstPartyRefreshToken
      tags:
        - Sync
      parameters:
        - $ref: '#/components/parameters/firstPartyid'
        - $ref: '#/components/parameters/platform'
      responses:
        '200':
          description: Successful authentication
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/firstPartyRefreshResponse'
        '404':
          $ref: '#/components/responses/404'
  /dev/config:
    get:
      tags:
        - Config
      operationId: getConfig
      responses:
        200:
          description: Server Config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/config'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
    patch:
      operationId: updateConfig
      tags:
        - Development
      requestBody:
        description: modifies config
        required: true
        content:
          application/json:
            schema:
              type: object
      responses:
        200:
          description: Server Config
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/config'
        default:
          description: unexpected error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
  /dev/user/presence:
    patch:
      operationId: updateUserPresence
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/clearPresence'
      requestBody:
        description: modifies existing presence from system side
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserPresenceStatusRequest'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/user/presence/self:
    post:
      operationId: setUserDefinedPresence
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/clearPresence'
      requestBody:
        description: user defined presence
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/userPresenceRequest'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/user/dna/search:
    get:
      operationId: userSearchDNA
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/searchQuery'
        - $ref: '#/components/parameters/searchPlatform'
        - $ref: '#/components/parameters/searchType'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/user/dna/{userid}/links:
    get:
      operationId: getUserLinksDNA
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/userid'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/user/{userid}/friends:
    get:
      operationId: getFriendsForUser
      description: get friends for a user
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/userid'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/user/{userid}/presence:
    get:
      operationId: getPresenceForUser
      description: get presence for a user
      tags:
        - Development
      parameters:
        - $ref: '#/components/parameters/userid'
      responses:
        200:
          description: Successful response
          content:
            application/json:
              schema:
                type: object
  /dev/user/{userid}/groups:
    get:
      operationId: getAllGroupsForUser
      description: DEPRECIATED. This is no longer implemented. Previously got all groups a user is in regardles of platform
      parameters:
        - $ref: '#/components/parameters/userid'
      tags:
        - Development
      responses:
        200:
          description: OK - Here are all the groups
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listResponse'
  /dev/user/{userid}/voip:
    get:
      operationId: getVoipRoomsForUser
      description: get the voip rooms for a user
      parameters:
        - $ref: '#/components/parameters/userid'
      tags:
        - Development
      responses:
        200:
          description: OK - Here are all the groups
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/listResponse'
  /dev/user/{userid}/sync:
    get:
      operationId: syncUserProfile
      description: synchronize data from DNA like display name
      parameters:
        - $ref: '#/components/parameters/userid'
      tags:
        - Development
      responses:
        200:
          description: Successful sync
          content:
            application/json:
              schema:
                type: object
  /dev/health.json:
    get:
      tags:
        - Server
      summary: Returns the details of the server's health
      operationId: getHealthDetails
      responses:
        200:
          description: Server is healthy
          content:
            application/json:
              schema:
                type: object
        503:
          description: Server is unhealthy
          content:
            application/json:
              schema:
                type: object
  /dev/stats:
    get:
      tags:
        - Stats
      summary: Get all stats
      operationId: getAllStats
      responses:
        200:
          description: Stats
          content:
            application/json:
              schema:
                type: object
  /dev/trustedlogin:
    post:
      tags:
        - Social Trusted API
      summary: Create a Social Trusted API login for the product
      description: Create a Social Trusted API login for the product
      operationId: trustedCreate
      requestBody:
        description: Application request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/trustedLoginCreateRequest'
      responses:
        201:
          description: Successful creation
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/trustedLoginResponse'
        401:
          description: Failed to create
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
              example:
                code: 4006320
                message: Failed to create login
  /dev/trustedlogin/{clientid}:
    patch:
      tags:
        - Social Trusted API
      parameters:
        - $ref: '#/components/parameters/clientid'
      summary: Update the password for the Social Trusted API login
      description: Update the password for the Social Trusted API login
      operationId: trustedUpdate
      requestBody:
        description: Application request body
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/trustedLoginUpdateRequest'
      responses:
        200:
          description: Successful update
        401:
          description: Failed to update
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
              example:
                code: 4006320
                message: Failed to update login
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
              example:
                code: 100012
                message: User not found
    delete:
      tags:
        - Social Trusted API
      parameters:
        - $ref: '#/components/parameters/clientid'
      summary: Delete the Social Trusted API login
      description: Delete the Social Trusted API login
      operationId: trustedDel
      responses:
        200:
          description: Successful delete
        401:
          description: Failed to delete
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
              example:
                code: 4006320
                message: Failed to update login
        404:
          description: Not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/error'
              example:
                code: 100012
                message: User not found
components:
  parameters:
    id1:
      name: id1
      in: query
      description: Userid1
      required: true
      schema:
        type: string
    id2:
      name: id2
      in: query
      description: Userid2
      required: true
      schema:
        type: string
    userid:
      name: userid
      in: path
      description: Userid
      required: true
      schema:
        type: string
    firstPartyid:
      name: firstPartyid
      in: path
      description: First Party ID
      required: true
      schema:
        type: string
    clientid:
      name: clientid
      in: path
      description: clientid
      required: true
      schema:
        type: string
    errorid:
      name: errorid
      in: path
      description: ErrorId
      required: true
      schema:
        type: string
    ulid:
      name: ulid
      in: path
      description: ulid for group friend ids
      required: true
      schema:
        type: string
    priority:
      name: priority
      in: path
      description: Presence priority
      required: true
      schema:
        type: integer
    clearPresence:
      name: clearPresence
      in: query
      description: Flag to clear user defined presence record. if set to true, will clear the presence ignoring presence in body except userid.
      required: false
      schema:
        type: boolean
    searchQuery:
      name: q
      in: query
      description: Search query
      required: true
      schema:
        type: string
    searchPlatform:
      name: searchPlatform
      in: query
      description: Search platform
      required: false
      schema:
        $ref: '#/components/schemas/onlineServiceType'
    searchType:
      name: type
      in: query
      description: Search type
      required: false
      schema:
        type: string
    xT2gpLabel:
      name: X-T2GP-Label
      in: header
      description: Optional header for identity provider. Defaults to "dna" if not set. Acceptable values are "dna" and "pdi".
      required: false
      schema:
        type: string
      example: pdi
    platform:
      name: platform
      in: path
      description: platform
      required: true
      schema:
        $ref: '#/components/schemas/onlineServiceType'
  schemas:
    error:
      type: object
      required:
        - code
        - message
      properties:
        code:
          description: A code representing the error that occurred
          type: integer
          format: uint32
          example: 500
        message:
          description: A string describing the error that occurred
          type: string
          example: Internal Server Error
    groupCleanupResponse:
      type: object
      properties:
        count:
          type: integer
          description: the number of group deleted
        executionTime:
          type: integer
          description: the number of secords clean up job running
        cleanuped:
          type: array
          description: a list of group id have been removed
          items:
            type: string
        errors:
          type: array
          description: list of errors that might have occurred
          items:
            type: string
    listResponse:
      type: object
      required: [items]
      properties:
        items:
          type: array
          items:
            oneOf:
              - $ref: '#/components/schemas/group'
        next:
          type: string
          example: /v1/api?next=f9ba143d26e86d5f9479dc9267177aae
    subscription:
      type: object
      properties:
        clientId:
          type: string
          example: socialweb-d7056450997c488da69d997d2a8e9446-2c8eddb8
        topic:
          type: string
          example: presence/4a85fad26a9747ab8c45868b1cb7317a
    group:
      required:
        - groupid
        - maxMembers
        - joinRequestAction
      description: 'ProductID only set as required due to codegen issues.'
      type: 'object'
      properties:
        type:
          type: string
          example: group
        created:
          $ref: '#/components/schemas/dateTime'
          example: '2020-09-14T20:30:35.129Z'
        controlMessages:
          type: 'array'
          items:
            $ref: '#/components/schemas/controlMessage'
        groupid:
          type: string
          example: '01EYRSXN4DCFF1AV128Y5A211J'
        productid:
          type: string
          example: '01EYRSXN4DCFF1AV128Y5A211J'
        membershipRequests:
          type: 'array'
          items:
            $ref: '#/components/schemas/membershipRequest'
        meta:
          type: 'object'
          example:
            key1: value1
            key2: value2
          description: Maximum size of 1024 bytes.
        members:
          type: array
          items:
            $ref: '#/components/schemas/groupMember'
        maxMembers:
          type: integer
          example: 6
        joinRequestAction:
          $ref: '#/components/schemas/joinRequestAction'
        password:
          type: string
          example: '********'
          description: NOT required.  needed to put required for codegen issues.
    membershipRequest:
      type: object
      required:
        - memberid
        - groupid
        - status
      properties:
        expires:
          $ref: '#/components/schemas/dateTime'
          description: for response only, when the invite expires
        memberid:
          type: string
          example: b287e655461f4b3085c8f244e394ff7e
          description: userid or first party id
        approverid:
          type: string
          example: b287e655461f4b3085c8f244e394ff7e
          description: for join group request when need approval
        groupid:
          type: string
          example: 01EYRSXN4DCFF1AV128Y5A211J
          description: the group id
        productid:
          type: string
          example: 01EYRSXN4DCFF1AV128Y5A211J
          description: in reponse body, it extracts from JWT token
        password:
          type: string
          format: password
          description: password for join the group.
        onlineServiceType:
          $ref: '#/components/schemas/onlineServiceType'
        status:
          $ref: '#/components/schemas/membershipStatus'
    onlineServiceType:
      type: integer
      enum:
        - 0
        - 1
        - 2
        - 3
        - 4
        - 5
        - 6
        - 9
        - 10
        - 11
        - 12
        - 13
        - 14
        - 15
        - 16
        - 17
        - 18
        - 19
        - 20
        - 21
        - 22
        - 23
        - 24
        - 99
      x-enumNames:
        - UNKNOWN
        - XBOX LIVE
        - SONY ENTERTAINMENT NETWORK
        - STEAM
        - WEB
        - LEGACY GAME CENTER
        - GOOGLE PLAY
        - WINDOWS PHONE
        - CALICO
        - NINTENDO
        - GAME CENTER
        - WEGAME
        - VORTEX
        - EPIC
        - STADIA
        - FACEBOOK
        - GOOGLE
        - TWITTER
        - TWITCH
        - DEVICE
        - APPLE
        - ZENDESK
        - T2GP
        - WINDOWS DEVELOPER
      example: 3
      description: The online service type of the specified Platform Account.
      x-oapi-codegen-extra-tags:
        dynamodbav: "onlineServiceType"
    membershipStatus:
      type: string
      enum: ['requested', 'approved', 'rejected', 'authenticated', 'invited', 'joined', 'declined', 'revoked']
    dateTime:
      type: string
      format: date-time
      example: '2020-09-14T20:30:35.129Z'
    groupMemberRole:
      type: string
      enum: [leader, member, nonmember]
    groupMember:
      required:
        - userid
        - groupid
        - role
        - productid
      description: 'ProductID only set as required due to codegen issues.'
      type: object
      properties:
        userid:
          type: string
        groupid:
          type: string
          example: '01EYRSXN4DCFF1AV128Y5A211J'
        productid:
          type: string
          example: '01EYRSXN4DCFF1AV128Y5A211J'
        role:
          $ref: '#/components/schemas/groupMemberRole'
    controlMessage:
      type: object
      properties:
        payload:
          type: string
          example: 'ZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SdzBLR2dvQUFBQU5TVWhFVWdBQUFHRUFBQUJ4Q0FZQUFBREYwTTA0QUFBQUFYTlNSMElBcnM0YzZRQUFBQVJuUVUxQkFBQ3hqd3Y4WVFVQUFBQUpjRWhaY3dBQURzUUFBQTdFQVpVckRoc0FBQUt0U1VSQlZIaGU3WnRiVGdOQkRBU3puQ3hIejgwQ1NIelRJejg2SGFoSWZIbkg5bFF4bTNnRDErMTJlMzc5OEhvaGdZOFgxcWIwRHdFa0JQd3FJQUVKQVFRQ1d1QWtJQ0dBUUVBTG5BUWtCQkFJYUlHVGdJUUFBZ0V0Y0JJQ0pGeU9aMGZQNTNzL25ycXViMHg3TDA3Q0h0dmp6RWc0UnJWM0lSTDIyQjVuUnNJeHFyMExrYkRIOWpnekVvNVI3VjJJaEQyMng1bmJjOExKRExEOU9mdDR0OFVMMVI2NysrTWtGTVZNTGtQQ0pNMWlMaVFVd1UwdVE4SWt6V0l1SkJUQlRTNUR3aVROWWk0a0ZNRk5Ma1BDSk0xaUxpUVV3VTB1UThJa3pXSXVKQlRCVFM1RHdpVE5ZaTRrRk1GTkxrUENKTTFpTGlRVXdVMHVpL2crWWZ0NWZUZC9kNzBTeGtsUWhBeHhKQmdncXhKSVVJUU1jU1FZSUtzU1NGQ0VESEVrR0NDckVraFFoQXp4OXB6d2VEeGttL2Y3WFY2VGZJSGFZM2QvbklRQSswaEFRZ0NCZ0JZNENVZ0lJQkRRQWljQkNRRUVBbHF3ekFrQisxeHRnVGxoRmE4bk9lOEpIczYvVmtFQ0VnSUlCTFRBU1VCQ0FJR0FGamdKQVJJaTVnVDFPVnY5M1kvaXFQN1BXSDFmb1BLci90VjZUb0lpWklnandRQlpsVUNDSW1TSUk4RUFXWlZBZ2lKa2lDUEJBRm1WUUlJaVpJaEh6QW1HZmE2V1lFNVl4ZXRKenUzSXc1bnZFd0k0SXdFSjZRUUMrdU05QVFrQkJBSmFhTThKSjN2b1BxOC9xYkY1VFhjT1VMMXhPMUtFREhFa0dDQ3JFa2hRaEF4eEpCZ2dxeEpJVUlRTWNTUVlJS3NTU0ZDRURISExuTkRkeC9hY3NUMEhxUDF6RWhRaFF4d0pCc2lxQkJJVUlVTWNDUWJJcWdRU0ZDRkRIQWtHeUtvRUVoUWhRL3d0NWdURm9UdEhNQ2Nvd3Y4Z3p1MG9RRElTa0JCQUlLQUZUZ0lTQWdnRXRNQkpRRUlBZ1lBVy9zU3dGc0N4MVFLM294YSttY1ZJbU9IWXlvS0VGcjZaeFVpWTRkaktnb1FXdnBuRlNKamgyTXFDaEJhK21jVkltT0hZeW9LRUZyNlp4VWlZNGRqS2dvUVd2cG5GbjBlVFA4dGMwOTkvQUFBQUFFbEZUa1N1UW1DQw'
        timestamp:
          $ref: '#/components/schemas/dateTime'
    joinRequestAction:
      type: string
      enum: ['manual', 'auto-approve', 'auto-reject', 'authenticate']
      example: 'manual'
    UpdateUserPresenceStatusRequest:
      type: object
      required:
        - userid
        - status
        - timestamp
        - productid
        - gameName
        - priority
      properties:
        userid:
          type: string
          example: effe28b27efc6594e43bfc0879b40085
        status:
          $ref: '#/components/schemas/presenceStatus'
          example: online
        richPresence:
          type: string
          description: string to be displayed for rich presence.  eventually will support templating for localization.
        gameName:
          type: string
          example: Sample Game
          description: Possibly pull this from somewhere for localization in the future.
        gameData:
          type: string
          description: structure for games to send additional presence information for internal use.
          maxLength: 1024
        activeGroup:
          type: object
          $ref: '#/components/schemas/activeGroup'
        productid:
          type: string
          example: effe28b27efc6594e43bfc0879b40085
          description: pulled from JWT
        Ttl:
          type: integer
          minimum: 35
          maximum: 1800
          description: How long in seconds before this presence will be considered offline if no presence Heartbeat is made.
        priority:
          type: integer
          description: Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc). offline will remove from list.
        meta:
          type: object
          example:
            key1: value1
            key2: value2
          description: Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
        timestamp:
          $ref: '#/components/schemas/dateTime'
          description: timestamp set by server.
    presenceStatus:
      type: string
      enum: [online, offline, playing, custom, away, dnd, chat, authenticating]
    activeGroup:
      type: object
      required:
        - groupid
        - maxMembers
        - currentMemberCount
        - canRequestJoin
        - canCrossPlay
      properties:
        groupid:
          type: string
          example: 01FHCPK9SF5RYQBKATX08WG4ZR
        canRequestJoin:
          type: boolean
          default: true
        canCrossPlay:
          type: boolean
          default: true
        maxMembers:
          type: integer
        currentMemberCount:
          type: integer
    userPresenceRequest:
      type: object
      required:
        - status
        - userid
      properties:
        status:
          $ref: '#/components/schemas/presenceStatus'
          example: online
        richPresence:
          type: string
          description: string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
        userid:
          type: string
    config:
      type: object
      example:
        version:
          version: 1.0.40-f577736
          gitHash: f5777369be12a550fb44c93b052c8221221d5882
          buildDate: Mon Jan 04 2021 22:59:47 GMT-0800 (Pacific Standard Time)
        debugMode: false
        publicPort: 8000
        privatePort: 8001
        corsAllowedOrigins: []
        metricsEnabled: false
        appId: APP_ID
        appSecret: APP_SECRET
        allowAlgNone: true
        discoveryUrl: https://discovery.api.2kcoretech.online
        ssoUrl: https://sso.api.2kcoretech.online/sso/v2.0
        ssoHost: sso.api.2kcoretech.online
        dynamoDbUrl: https://dynamodb.us-west-1.amazonaws.com
        awsAccessKey: AWS_ACCESS_KEY
        awsSecret: AWS_SECRET
        AwsRegion: us-west-1
        socialTable: t2gp-social
        s3Bucket: t2gp-social
        vmqApiUrl: http://vmq0:8100
        vmqApiKey: VMQ_API_KEY
    trustedLoginCreateRequest:
      type: object
      required:
        - tenantId
        - productId
      properties:
        tenantId:
          description: Tenant id
          type: string
          example: [dna, pdi]
        productId:
          description: Product id
          type: string
          example: b156ad250e7d4b6f81830e8a1812aae9
        password:
          description: Password. If not set, it will be randomly generated.
          type: string
          example: password1
    trustedLoginUpdateRequest:
      type: object
      properties:
        password:
          description: Password. If not set, it will be randomly generated.
          type: string
          example: password1
    trustedLoginResponse:
      type: object
      required:
        - clientId
        - password
      properties:
        clientId:
          description: Client id
          type: string
          example: b156ad250e7d4b6f81830e8a1812aae9
        password:
          description: Password
          type: string
          example: '********'
    firstPartyTokenRequest:
      type: object
      required:
        - firstPartyId
        - token
        - ttl
      properties:
        firstPartyid:
          type: string
          example: 9234015234
        token:
          type: string
        ttl:
          type: integer
          description: ttl of token in seconds
        refreshToken:
          type: string
        refreshTokenTtl:
          type: integer
          description: ttl of optional refresh token in seconds
    firstPartyTokenResponse:
      type: object
      properties:
        firstPartyid:
          type: string
        token:
          type: string
    firstPartyRefreshResponse:
      type: object
      properties:
        firstPartyid:
          type: string
        refreshToken:
          type: string
  responses:
    400:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 400
            message: bad request
    401:
      description: Unauthorized - The request did not include the required authorization information
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 401
            message: Invalid bearer token
    403:
      description: Forbidden - The requestor is not authorized to make the request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 403
            message: Forbidden
    404:
      description: Not Found - The requested entity could not be found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 404
            message: Not found
    406:
      description: Unacceptable - The message is unacceptable
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
          example:
            code: 406
            message: Message is unacceptable
    500:
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/error'
