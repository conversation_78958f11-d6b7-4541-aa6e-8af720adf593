import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

// TODO: need a way to manage shorter time on develop envrionment and longer time on production.
//       this applies to some other features too.
let keepalive = 35;
// Picking a number that's away from the "edges" would avoid false positives. 10sec is a good choice.
let timeDelta = 10;
// Timeout in ms for each test. Adjust according to keepalive and timeDelta.
let testTimeout = 115000;
// extra time in seconds allowed for presence to become expired.
let maxSlack = 15;
// game name
let gameName = "Automated API Tests Game Name";

describe('[public v1]', () => {
  let usersTwok: TwokAccounts;
  let iniStatus = 'online';

  beforeAll(async () => {
    usersTwok = new TwokAccounts(1, ["user"]);
    await usersTwok.loginAll({});
  });

  afterAll(async () => {
    await usersTwok.logoutAll({});
  });

  beforeEach(async () => {
    let r = await socialApi.clearPresence(
      usersTwok.acct['user']
    );
    socialApi.testStatus(StatusCodes.OK, r);
  });

  afterEach(async () => {
    let r = await socialApi.clearPresence(
      usersTwok.acct['user']
    );
    socialApi.testStatus(StatusCodes.OK, r);
  });

  it.each`
  scenario                                                                                         | isCallingHeartbeat | timeBeforeHeartbeat        | timeBeforeGetPresence      | expPresExist
  ${"get presence before keepalive elapsed"}                                                       | ${false}           | ${0}                       | ${(keepalive - timeDelta)} | ${true}
  ${"get presence after keepalive elapsed"}                                                        | ${false}           | ${0}                       | ${(keepalive + timeDelta)} | ${false}
  ${"heartbeat presence before keepalive elapsed; then get presence before 2nd keepalive elapsed"} | ${true}            | ${(keepalive - timeDelta)} | ${(keepalive - timeDelta)} | ${true}
  ${"heartbeat presence before keepalive elapsed; then get presence after 2nd keepalive elapsed"}  | ${true}            | ${(keepalive - timeDelta)} | ${(keepalive + timeDelta)} | ${false}
  ${"heartbeat presence after keepalive elapsed; then get presence before 2nd keepalive elapsed"}  | ${true}            | ${(keepalive + timeDelta)} | ${(keepalive - timeDelta)} | ${false}
  ${"heartbeat presence after keepalive elapsed; then get presence after 2nd keepalive elapsed"}   | ${true}            | ${(keepalive + timeDelta)} | ${(keepalive + timeDelta)} | ${false}
  `('$scenario', async ({isCallingHeartbeat, timeBeforeHeartbeat, timeBeforeGetPresence, expPresExist}) => {
    let testCase = {
      description: `set presence with keepalive ${keepalive}sec; hearbeat: ${isCallingHeartbeat}; time before heartbeat: ${timeBeforeHeartbeat}sec; time before get presence: ${timeBeforeGetPresence}sec`,
      expected: `expected presence existence: ${expPresExist}`
    };

    let presenceItem;

    switch (expPresExist) {
      case true:
        presenceItem = [{status:iniStatus, userid: usersTwok.acct['user'].publicId}];
        break;
      case false:
        presenceItem = [];
        break;
    }

    // set presence with keepalive
    let r = await socialApi.setPresenceV1(
      usersTwok.acct['user'],
      { status: iniStatus, keepAliveFor: keepalive, gameName: gameName }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    if (isCallingHeartbeat == true) {
        // wait for a moment
        await new Promise(r => setTimeout(r, timeBeforeHeartbeat * 1000));

        // heartbeat presence
        const r = await socialApi.heartbeatPresence(usersTwok.acct['user']);
        socialApi.testStatus(StatusCodes.OK, r);
    }

    // wait for a moment
    await new Promise(r => setTimeout(r, timeBeforeGetPresence * 1000));

    // some extra amount of time is allowed before presence info is gone when it expires
    // NOTE that this extra time DOES NOT take into account of time already elapsed since expiry in each scenario
    if (expPresExist == false) {
      await socialApi.waitWhile(async () => {
        let p = await socialApi.getPresence(
          usersTwok.acct['user'], {}
        );
        return (p.body.items.length != 0);
      }, maxSlack, 1000);
    }

    // get presence as expected result
    let actualPresence = await socialApi.getPresence(
      usersTwok.acct['user'], {}
    );

    let expectedPresence = {
      status: StatusCodes.OK,
      body: {
        items: presenceItem
      },
    };
    socialApi.expectMore(
      () => {expect(actualPresence).toMatchObject(expectedPresence)},
      testCase,
      {
        resp: actualPresence,
        additionalInfo: {
          "fail reason": "unexpected user presence"
        }
      }
    );
  }, testTimeout);
});