package apipub

type AbuseReport struct {
	Os                       *string                 `json:"os,omitempty"`
	ReportedUserDisplayName  string                  `json:"reportedUserDisplayName"`
	ReportedUserId           string                  `json:"reportedUserId"`
	ReportingCategory        ReportingCategory       `json:"reportingCategory"`
	ReportingContentType     *string                 `json:"reportingContentType,omitempty"`
	ReportingUserDisplayName string                  `json:"reportingUserDisplayName"`
	ReportingUserId          string                  `json:"reportingUserId"`
	ReportingUserLocale      string                  `json:"reportingUserLocale"`
	ReportingUserIpAddress   string                  `json:"reportingUserIpAddress"`
	ReportingUserAgent       string                  `json:"reportingUserAgent"`
	ReportingUserProductId   string                  `json:"reportingUserProductId"`
	ReportingUserPlatform    string                  `json:"reportingUserPlatformIdentifier"`
	TelemetryEventId         string                  `json:"telemetryEventId"`
	SubjectTitle             string                  `json:"subjectTitle"`
	SubmissionTime           string                  `json:"submissionTime"`
	RequestId                string                  `json:"requestId"`
	ReportMessage            string                  `json:"reportMessage"`
	GameVersionNumber        *string                 `json:"gameVersionNumber,omitempty"`
	GameSessionInfo          *map[string]interface{} `json:"gameSessionInfo,omitempty"`
	TeleMeta                 *TelemetryMetaData      `json:"teleMeta"`
}
