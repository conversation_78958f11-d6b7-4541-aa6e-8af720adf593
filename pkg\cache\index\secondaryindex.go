// Package index explicitly created for secondaryIndex struct to force use of getters for default values.
package index

import (
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
)

type SecondaryIndex struct {
	idxKey string         //`json:"idxKey"`
	valKey string         //`json:"valKey"`
	score  *float64       //`json:"score,omitempty"`
	ttl    *time.Duration //`json:"ttl,omitempty"`
}

// NewSecondaryIndex constructor secondary index
func NewSecondaryIndex(idxKey, valKey string) *SecondaryIndex {
	if idxKey == "" {
		return nil
	}
	tmpCfg := config.NewConfig()
	//default ttl set to 30 days.
	ttl := time.Duration(tmpCfg.TtlIndex) * time.Second
	return &SecondaryIndex{
		idxKey: idxKey,
		valKey: valKey,
		ttl:    &ttl,
	}
}

// Idx<PERSON><PERSON> returns the index key of the secondary index
func (idx *SecondaryIndex) IdxKey() string {
	if idx != nil {
		return idx.idxKey
	}
	return ""
}

// ValKey returns the value key of the secondary index
func (idx *SecondaryIndex) ValKey() string {
	if idx != nil {
		return idx.valKey
	}
	return ""
}

// Score returns the score of the secondary index.  defaults to 0
func (idx *SecondaryIndex) Score() *float64 {
	if idx == nil || idx.score == nil {
		return aws.Float64(0)
	}
	return idx.score
}

// Ttl returns the ttl of the secondary index.  defaults to config default ttl
func (idx *SecondaryIndex) Ttl(cfg *config.Config) *time.Duration {
	ttl := time.Duration(30*24) * time.Hour
	if cfg != nil {
		ttl = time.Duration(cfg.TtlDefault) * time.Second
	}
	if idx == nil || idx.ttl == nil {
		return &ttl
	}
	return idx.ttl
}

// SetIdxKey sets the index key of the secondary index
func (idx *SecondaryIndex) SetIdxKey(idxKey string) {
	idx.idxKey = idxKey
}

// SetValKey sets the value key of the secondary index
func (idx *SecondaryIndex) SetValKey(valKey string) {
	idx.valKey = valKey
}

// SetKeys sets the index and value key of the secondary index
func (idx *SecondaryIndex) SetKeys(idxKey, valKey string) {
	idx.idxKey = idxKey
	idx.valKey = valKey
}

// SetScore sets the score of the secondary index
func (idx *SecondaryIndex) SetScore(score float64) {
	idx.score = &score
}

// SetTtl sets the ttl of the secondary index
func (idx *SecondaryIndex) SetTtl(ttl time.Duration) {
	idx.ttl = &ttl
}
