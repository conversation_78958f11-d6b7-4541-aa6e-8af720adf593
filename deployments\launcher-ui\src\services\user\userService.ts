import {
  LINK_WINDOW_FEATURES,
  STEAM_LINK_URL,
  STEAM_LINK_WINDOW_NAME,
} from '../../constant';
import { handleResponse, IAPIService } from '../api';
import type { UserProfile } from '../friends';

export interface IUserService {
  getUserProfileAsync: () => Promise<UserProfile>;
  getUserIdAsync: () => Promise<string>;
  openSteamLinkWindow: (callback: any) => Window;
  closeLinkWindow: () => void;
  watchingLinkWindow: (onWindowClosed: () => void) => void;
  clearMessageListener: () => void;
}

export class UserService implements IUserService {
  private windowObjectReference: Window;
  private previousLinkUrl: string;
  private host: string;
  private redirectUrl: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  private onMessageReceived: (event: any) => void;
  private apiService: IAPIService;

  constructor(
    apiService: IAPIService,
    {
      host,
      redirectUrl,
    }: {
      host?: string;
      redirectUrl?: string;
    }
  ) {
    this.apiService = apiService;
    this.windowObjectReference = null;
    this.previousLinkUrl = '';
    this.host = host;
    this.redirectUrl = redirectUrl;
  }

  async getUserProfileAsync(): Promise<UserProfile> {
    const response = await this.apiService.getUserAsync();
    return handleResponse<UserProfile>(response);
  }

  async getUserIdAsync(): Promise<string> {
    const { userid } = await this.getUserProfileAsync();
    return userid;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  openSteamLinkWindow(callback): Window {
    this.onMessageReceived = callback;

    // remove any existing event listeners
    this.clearMessageListener();

    const linkUrl = `${STEAM_LINK_URL}&openid.realm=${this.host}&openid.return_to=${this.redirectUrl}`;
    if (
      this.windowObjectReference === null ||
      this.windowObjectReference.closed
    ) {
      /* if the pointer to the window object in memory does not exist
            or if such pointer exists but the window was closed */
      this.windowObjectReference = window.open(
        linkUrl,
        STEAM_LINK_WINDOW_NAME,
        LINK_WINDOW_FEATURES
      );
    } else if (this.previousLinkUrl !== linkUrl) {
      /* if the resource to load is different,
            then we load it in the already opened secondary window and then
            we bring such window back on top/in front of its parent window. */
      this.windowObjectReference = window.open(
        linkUrl,
        STEAM_LINK_WINDOW_NAME,
        LINK_WINDOW_FEATURES
      );
      this.windowObjectReference.focus();
    } else {
      /* else the window reference must exist and the window
            is not closed; therefore, we can bring it back on top of any other
            window with the focus() method. There would be no need to re-create
            the window or to reload the referenced resource. */
      this.windowObjectReference.focus();
    }

    // add the listener for receiving a message from the popup
    window.addEventListener('message', this.onMessageReceived, false);
    // assign the previous URL
    this.previousLinkUrl = linkUrl;

    return this.windowObjectReference;
  }

  closeLinkWindow(): void {
    if (this.windowObjectReference && !this.windowObjectReference.closed) {
      this.windowObjectReference.close();
    }
  }

  watchingLinkWindow(onWindowClosed: () => void): void {
    let timer = null;
    const polling = () => {
      if (this.windowObjectReference && this.windowObjectReference.closed) {
        clearInterval(timer);
        onWindowClosed();
      }
    };

    timer = setInterval(polling, 1000);
  }

  clearMessageListener(): void {
    // remove any existing event listeners
    window.removeEventListener('message', this.onMessageReceived, false);
  }
}
