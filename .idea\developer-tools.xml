<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DeveloperToolsToolWindowSettingsV1" lastSelectedContentNodeId="url-encoding-encoder-decoder">
    <developerToolsConfigurations>
      <developerToolConfiguration developerToolId="url-encoding-encoder-decoder" id="b68ac422-c753-4b6a-bfb2-c15f71b2ba4d" name="Workbench">
        <properties>
          <property key="sourceText" type="INPUT" value="kotlin.String|&lt;&gt;" />
          <property key="targetText" type="INPUT" value="kotlin.String|%3C%3E" />
        </properties>
      </developerToolConfiguration>
      <developerToolConfiguration developerToolId="time-conversion" id="a7be0559-01e5-496f-b0c0-928ffd69ad0e" name="Workbench">
        <properties>
          <property key="milliseconds" type="INPUT" value="java.math.BigDecimal|1.23460E+8" />
          <property key="centuries" type="INPUT" value="java.math.BigDecimal|0.0000391489" />
          <property key="hours" type="INPUT" value="java.math.BigDecimal|34.2944" />
          <property key="weeks" type="INPUT" value="java.math.BigDecimal|0.204134" />
          <property key="months" type="INPUT" value="java.math.BigDecimal|0.0469787" />
          <property key="minutes" type="INPUT" value="java.math.BigDecimal|2057.67" />
          <property key="years" type="INPUT" value="java.math.BigDecimal|0.00391489" />
          <property key="seconds" type="INPUT" value="java.math.BigDecimal|123460" />
          <property key="days" type="INPUT" value="java.math.BigDecimal|1.42894" />
          <property key="decades" type="INPUT" value="java.math.BigDecimal|0.000391489" />
          <property key="millenniums" type="INPUT" value="java.math.BigDecimal|0.00000391489" />
        </properties>
      </developerToolConfiguration>
      <developerToolConfiguration developerToolId="jwt-encoder-decoder" id="05d8f2ab-732b-40c4-9c40-5aec60825f54" name="Workbench">
        <properties>
          <property key="headerText" type="INPUT" value="kotlin.String|{&#13;&#10;  &quot;exp&quot;:1635758886,&#13;&#10;  &quot;iat&quot;:1635755286,&#13;&#10;  &quot;jti&quot;:&quot;35616470b90c43709dee35028fa2f835&quot;,&#13;&#10;  &quot;tty&quot;:0,&#13;&#10;  &quot;pid&quot;:&quot;4029a6ffe9924f969955aa2e1c0782aa&quot;,&#13;&#10;  &quot;gid&quot;:&quot;c7dcd622c2a64b68823cc53f49bb13b9&quot;,&#13;&#10;  &quot;loc&quot;:&quot;en-US&quot;,&#13;&#10;  &quot;cty&quot;:&quot;Ashburn&quot;,&#13;&#10;  &quot;ctr&quot;:&quot;US&quot;,&#13;&#10;  &quot;lat&quot;:39.0469,&#13;&#10;  &quot;lon&quot;:-77.4903,&#13;&#10;  &quot;rti&quot;:&quot;d9d10ac8d08a44d08fb823a987075145&quot;,&#13;&#10;  &quot;rex&quot;:1635762486,&#13;&#10;  &quot;iss&quot;:&quot;e3c64ee90b8044d2ba35f12ea161fae4&quot;,&#13;&#10;  &quot;sub&quot;:&quot;b287e655461f4b3085c8f244e394ff7e&quot;,&#13;&#10;  &quot;aty&quot;:3,&#13;&#10;  &quot;agp&quot;:5,&#13;&#10;  &quot;sid&quot;:&quot;2242a1903fe04a96887c6bba1b474d32&quot;,&#13;&#10;  &quot;ver&quot;:true,&#13;&#10;  &quot;agr&quot;:1070,&#13;&#10;  &quot;dob&quot;:&quot;3k0xlp+aYQkHVPoriLOuyg==&quot;&#13;&#10;}" />
          <property key="payloadText" type="INPUT" value="kotlin.String|{&#10;  &quot;jti&quot;:&quot;96492d59-0ad5-4c00-892d-590ad5ac00f3&quot;,&#10;  &quot;sub&quot;:&quot;0123456789&quot;,&#10;  &quot;name&quot;:&quot;John Doe&quot;,&#10;  &quot;iat&quot;:1681040515&#10;}" />
          <property key="secret" type="SECRET" value="kotlin.String|SAVED_IN_KEYSTORE" />
          <property key="privateKey" type="SECRET" value="kotlin.String|SAVED_IN_KEYSTORE" />
        </properties>
      </developerToolConfiguration>
    </developerToolsConfigurations>
    <expandedGroupNodeIds />
  </component>
</project>