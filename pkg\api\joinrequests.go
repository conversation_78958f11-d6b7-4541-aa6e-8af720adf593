package api

import (
	"errors"
	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
	"net/http"
)

// SendJoinRequest membership request for group
func (api *SocialPublicAPI) SendJoinRequest(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, pApproverid apipub.PApproverid) {
	log := logger.Get(r)
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if g == "" || pApproverid == "" {
		msg := "cant have empty group or approver"
		log.Error().Str("groupid", g).Str("approverid", pApproverid).Msg(msg)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidGroupID))
	}

	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer

	//	decode request
	var sendJoinRequest apipub.RequestJoinRequestBody
	if !DecodeBody(w, r, &sendJoinRequest) {
		return
	}

	memberid := token.Claims.Subject
	approverid := pApproverid

	api.SendJoinRequestByStrings(w, r, g, productid, appid, ost, token, memberid, approverid, sendJoinRequest)
}

func (api *SocialPublicAPI) SendJoinRequestByStrings(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, productid, appid string, ost apipub.OnlineServiceType, token *jwt.Token, memberid, approverid string, sendJoinRequest apipub.RequestJoinRequestBody) {
	e := api.SendJoinRequestWithHttpReturn(w, r, g, productid, appid, ost, token, true, memberid, approverid, sendJoinRequest)
	if e != nil {
		errs.Return(w, r, e)
		return
	}
}

// SendJoinRequestWithHttpReturn returns errors
// if we get a 20x return, bHttpReturnOk = true will send the HTTP messages
// bHttpReturnOk is only false when adding multiple users to a group at once via trusted server calls
func (api *SocialPublicAPI) SendJoinRequestWithHttpReturn(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, productid, appid string, ost apipub.OnlineServiceType, token *jwt.Token, bHttpReturnOk bool, memberid, approverid string, sendJoinRequest apipub.RequestJoinRequestBody) *errs.Error {
	log := logger.Get(r)
	groupid := g

	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msg("failed to get group")
		return errs.ToError(err)
	}
	if group == nil {
		log.Error().Err(err).Str("productid", productid).Str("groupid", groupid).Msgf("no group found for %s#%s", productid, groupid)
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}

	if group.Members != nil && len(*group.Members) >= group.MaxMembers {
		log.Error().Err(err).Int("maxMembers", group.MaxMembers).Msg("group is at max members")
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsFull)
	}

	//allow sending join requests to the leader if you don't know the id of the leader
	leaderid := ""
	if group.GetLeader() != nil {
		leaderid = group.GetLeader().Userid
		if approverid == "leader" {
			approverid = leaderid
		}
	}

	bTSForce := false
	if token == nil {
		bTSForce = true
	}

	// for security reasons below.
	var membershipRequest apipub.MembershipRequest
	membershipRequest.Groupid = groupid
	membershipRequest.Approverid = approverid
	membershipRequest.Status = apipub.Requested
	membershipRequest.Memberid = memberid
	membershipRequest.CanCrossPlay = &sendJoinRequest.CanCrossPlay
	membershipRequest.Productid = &productid
	membershipRequest.OnlineServiceType = &ost
	membershipRequest.TeleMeta = sendJoinRequest.TeleMeta

	// process telemeta if exists for telemetry
	additionalInfo := make(map[string]string)
	if membershipRequest.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*membershipRequest.TeleMeta, &additionalInfo)
	}

	if membershipRequest.CanCrossPlay == nil {
		membershipRequest.CanCrossPlay = aws.Bool(true)
	}

	ttlMembership := apipub.Ttl(api.Cfg.TtlMembership)
	if membershipRequest.Ttl == nil {
		membershipRequest.Ttl = &ttlMembership
	}

	//if group is cross play but current user does not have a full account return error
	if group.CanCrossPlay != nil && *group.CanCrossPlay && !api.IsUserIdFullAccount(r.Context(), token, memberid) {
		return errs.New(http.StatusForbidden, errs.EFullAccountRequired)
	}

	//if group JoinRequestAction is manual and , approverid must be group leader
	if group.JoinRequestAction == apipub.Manual && (leaderid != membershipRequest.Approverid) {
		//throw error on request
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID)
	}

	//if join request for this approver/group/member combo exists already, just return
	if api.Cache.JoinRequestExistsInCache(r.Context(), membershipRequest.Memberid, *membershipRequest.Productid, membershipRequest.Groupid, membershipRequest.Approverid) {
		if bHttpReturnOk {
			ReturnEmptyOK(w, r)
		}
		return nil
	}

	//For joinRequests, check if approverid has blocked current user and eat request if so
	isRequestorBlocked, _ := api.doesBlockerBlockBlockee(r.Context(), approverid, memberid)
	if isRequestorBlocked {
		if bHttpReturnOk {
			ReturnEmptyOK(w, r)
		}
		return nil
	}

	//On requesting to join a group, we can already determine if the cross play settings don't allow it.
	//No mqtt message is sent in this case.  Only in the case of invites do we send an mqtt message to the inviter that it failed.
	if !api.validCrossPlayRequest(*group, membershipRequest) {
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsCrossplayValidationFailed)
	}

	//If member is already in the group, then return error
	if group.IsMember(membershipRequest.Memberid) {
		if bHttpReturnOk {
			ReturnEmptyOK(w, r)
		}
		return nil
	}

	//Check if an submitted status already exists and ignore if it does.
	requests := group.GetMemberships(membershipRequest.Memberid, []string{string(membershipRequest.Status)})
	if requests != nil {
		for _, memshp := range *requests {
			//If one exists. just return ok because the invite you're trying to create already exists and is not expired.
			if memshp.Status == membershipRequest.Status && memshp.Approverid == membershipRequest.Approverid {
				if bHttpReturnOk {
					membershipRequest.TeleMeta = nil
					ReturnOK(w, r, membershipRequest)
				}
				return nil
			}
		}
	}

	// If a join request or invite and group's max members reached, return err
	if group.Members != nil && len(*group.Members) >= group.MaxMembers {
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsFull)
	}

	// get user profile for inviter in user to get display name.
	displayName := ""
	profile, _ := api.getUserProfile(r.Context(), membershipRequest.Approverid, true)
	if profile != nil && profile.DisplayName != nil {
		displayName = *profile.DisplayName
	}

	password := ""
	if sendJoinRequest.Password != nil {
		password = *sendJoinRequest.Password
	}

	var teleMeta apipub.TelemetryMetaData
	if sendJoinRequest.TeleMeta != nil {
		teleMeta = *sendJoinRequest.TeleMeta
	}

	// adds invites and manual join requests to group
	err = api.Cache.AddMembershipRequestToGroup(r.Context(), group, membershipRequest)
	if err != nil {
		var socialError *errs.Error
		if errors.As(err, &socialError) {
			errs.Return(w, r, socialError)
			return nil
		}
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EGroupsGeneric))
		return nil
	}

	//proces various join request actions.  telemetry for join requests sent in processJoinRequestActions
	joinErr := api.processJoinRequestActions(r, group, bTSForce, appid, ost, token, memberid, approverid, password, displayName, teleMeta)
	if joinErr != nil {
		return joinErr
	} else if group.JoinRequestAction == apipub.AutoApprove || bTSForce {
		if bHttpReturnOk {
			membershipRequest.TeleMeta = nil
			ReturnOK(w, r, membershipRequest)
		}
		return nil
	}

	if bHttpReturnOk {
		membershipRequest.TeleMeta = nil
		ReturnOK(w, r, membershipRequest)
	}
	return nil
}

// ApproveJoinRequest approves invite for the memberid
func (api *SocialPublicAPI) ApproveJoinRequest(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, m apipub.Dnaid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	approverid := token.Claims.Subject

	// decode request
	var approveJoinRequest apipub.ApproveJoinRequestBody
	if !DecodeBody(w, r, &approveJoinRequest) {
		return
	}

	var teleMeta apipub.TelemetryMetaData
	if approveJoinRequest.TeleMeta != nil {
		teleMeta = *approveJoinRequest.TeleMeta
	}

	api.modifyJoinRequest(w, r, g, m, token, approverid, apipub.Approved, teleMeta)
}

// RejectJoinRequest rejects the join request for the memberid
func (api *SocialPublicAPI) RejectJoinRequest(w http.ResponseWriter, r *http.Request, g apipub.PGroupid, m apipub.Dnaid) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	approverid := token.Claims.Subject
	api.modifyJoinRequest(w, r, g, m, token, approverid, apipub.Rejected, nil)
}

// modifyJoinRequest modifies join request for group for member
func (api *SocialPublicAPI) modifyJoinRequest(w http.ResponseWriter, r *http.Request, groupid apipub.Groupid, memberid apipub.Dnaid, token *jwt.Token, approverid string, status apipub.MembershipStatus, teleMeta apipub.TelemetryMetaData) {
	log := logger.FromContext(r.Context())

	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer

	// process telemeta if exists for telemetry
	additionalInfo := make(map[string]string)
	if teleMeta != nil {
		utils.ConvertMapInterfaceToMapString(teleMeta, &additionalInfo)
	}

	var group *apipub.GroupResponse
	group, err := api.Cache.GetGroup(r.Context(), productid, groupid)
	if err != nil {
		log.Error().Err(err).Str("status", string(status)).Msgf("failed to get group %s#%s", productid, groupid)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EDynamodbReadFailed))
		return
	}
	if group == nil {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsNotFound))
		return
	}

	//Check if user requested to join group
	requests := group.GetMemberships(memberid, []string{string(apipub.Requested)})
	if requests == nil || len(*requests) == 0 {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EGroupsMembershipNotFound))
		return
	}

	// do not allow modification of a cross play group membership unless full account
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	if approverid == memberid {
		log.Error().Err(err).Str("status", string(status)).Msgf("cannot approve/reject yourself")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotAllowed))
		return
	}

	leaderid := ""
	leader := group.GetLeader()
	if leader != nil {
		leaderid = leader.Userid
	}
	if (approverid == "" || approverid == "leader") && leaderid != "" {
		approverid = leaderid
	}
	//always error if memberid == approverid
	//if JoinRequestAction == manual AND CanMemberInvite == false you also need to be leader to accept
	if group.JoinRequestAction == apipub.Manual && (group.CanMembersInvite == nil || !*group.CanMembersInvite) && leaderid != approverid {
		log.Error().Err(err).Str("status", string(status)).Str("status", string(status)).Str("approverid", approverid).Str("groupid", groupid).Msg("invalid approverid status")
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidApproverID))
		return
	}

	//create new request
	var membershipRequest apipub.MembershipRequest
	membershipRequest.Status = status
	membershipRequest.Memberid = memberid
	membershipRequest.Approverid = approverid
	membershipRequest.Groupid = groupid
	membershipRequest.TeleMeta = &teleMeta
	membershipRequest.OnlineServiceType = &ost
	if membershipRequest.Productid == nil {
		membershipRequest.Productid = &productid
	}

	if (membershipRequest.Status == apipub.Approved) && (group.Members != nil && len(*group.Members) >= group.MaxMembers) {
		log.Error().Err(err).Str("status", string(status)).Msgf("group is at max members %d", group.MaxMembers)
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EGroupsFull))
		return
	}

	//delete old request if request details match
	if err = api.deletePreviousMembershipRequestFull(r.Context(), group, membershipRequest); err != nil {
		log.Error().Err(err).Str("status", string(status)).Msgf("did not delete previous group membership")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	//If membership was approved, do all the join groupy stuff.  Revoked and Rejected have nothing to do.
	if membershipRequest.Status == apipub.Approved {

		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupApprove, membershipRequest.Approverid, ost, []string{memberid}, &appid, &additionalInfo))

		_, err = api.JoinGroupFinalizeHelper(r, group, appid, ost, token, memberid, approverid, teleMeta)
		if err != nil {
			log.Error().Err(err).Str("status", string(status)).Msgf("finalize helper failed for groups")
			errs.Return(w, r, err.(*errs.Error))
			return
		}
	} else if membershipRequest.Status == apipub.Rejected {
		// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
		originalOST := group.OnlineServiceType
		group.OnlineServiceType = membershipRequest.OnlineServiceType

		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupReject, membershipRequest.Approverid, ost, []string{memberid}, &appid, &additionalInfo))

		group.OnlineServiceType = originalOST
	}

	membershipRequest.TeleMeta = nil
	ReturnEmptyOK(w, r)
}
