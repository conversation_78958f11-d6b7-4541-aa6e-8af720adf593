package cache

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/go-redis/redismock/v9"
	"github.com/joho/godotenv"
	"github.com/rs/zerolog"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	gomock "go.uber.org/mock/gomock"
)

var cfg *config.Config
var ctx context.Context = context.Background()
var sm *health.ServiceMonitor
var id *identity.Identity
var t *telemetry.Telemetry
var rc *RedisCache

type MockCache struct {
	ctrl        *gomock.Controller
	mockRC      *MockRedisCacheInterface
	redisMock   redismock.ClientMock
	rc          *RedisCache
	testTimeout time.Duration
}

func NewMockRC(t *testing.T) *MockCache {
	mockCtrl := gomock.NewController(t)
	_, redisMock := redismock.NewClientMock()

	return &MockCache{
		ctrl:        mockCtrl,
		redisMock:   redisMock,
		mockRC:      NewMockRedisCacheInterface(mockCtrl),
		testTimeout: time.Second * 30,
		rc:          rc,
	}
}

func TestMain(m *testing.M) {
	setup()
	code := m.Run()
	teardown()
	os.Exit(code)
}

func setup() {
	godotenv.Load("../../.env")
	cfg = config.ConfigForTests()
	sm = health.NewServiceMonitor("test-social", cfg.Version.Version)
	id = identity.NewIdentity(ctx, cfg, sm)
	t = telemetry.NewTelemetry(cfg, nil)
	rc = NewRedisCache(ctx, cfg, t, id)

	// disable log
	zerolog.SetGlobalLevel(zerolog.FatalLevel)
}

func teardown() {
}

func TestNewRedisCache(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("new cache", func() {
		g.It("should be created successfully", func() {
			g.Assert(rc).IsNotNil()
		})
		g.It("is healthy", func() {
			g.Assert(rc.CheckHealth()).IsTrue()
		})
		g.It(" last status ok", func() {
			g.Assert(rc.LastStatus().Status).Equal(health.OK)
		})
		// g.It("has leadership lock", func() {
		// 	g.Assert(rdb.isLeader(ctx)).IsTrue()
		// })
		// g.It("turn on keyspace notifications", func() {
		// 	g.Assert(TurnRedisKeyspaceNotificationsOn(ctx, rdb.client)).IsNil()
		// })
	})
}

func TestRedisLock(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("Obtain a Redis lock", func() {
		g.It("Obtain a lock successfully", func() {
			lock := rc.GetSyncLock(ctx, "Lock1", time.Duration(5*time.Second))
			g.Assert(lock).IsNotNil()
		})
		g.It("Fail to obtain a lock if it already exists", func() {
			lock := rc.GetSyncLock(ctx, "Lock2", time.Duration(5*time.Second))
			anotherLock := rc.GetSyncLock(ctx, "Lock2", time.Duration(5*time.Second))
			g.Assert(lock).IsNotNil()
			g.Assert(anotherLock).IsNil()
		})
		g.It("Obtain a lock if it has already been released", func() {
			lock := rc.GetSyncLock(ctx, "Lock3", time.Duration(5*time.Second))
			g.Assert(lock).IsNotNil()
			lock.Release(ctx)
			anotherLock := rc.GetSyncLock(ctx, "Lock3", time.Duration(5*time.Second))
			g.Assert(anotherLock).IsNotNil()
		})
	})
}
