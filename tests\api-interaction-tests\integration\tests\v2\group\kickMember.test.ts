/* eslint-disable max-lines-per-function */
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

/**
 * TODO: see if the invite/accept invite parts of the tests can be placed in beforeEach.
 */

let usersTwok: TwokAccounts;
let groupId: string;

beforeEach(async () => {
  usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
  await usersTwok.loginAll({});

  const r = await socialApi.createGroup(
    usersTwok.acct["leader"],
    {
      maxMembers: 6,
      joinRequestAction: 'auto-approve',
      canCrossPlay: true
    }
  );
  socialApi.testStatus(StatusCodes.CREATED, r);
  groupId = socialApi.getGroupId(r);
});

afterEach(async () => {
  await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
  await usersTwok.logoutAll({});
});
// eslint-disable-next-line max-lines-per-function
describe('[public v2]', () => {
  beforeEach(async () => {
    const resp = await socialApi.requestToJoin(
      usersTwok.acct["member1"],
      groupId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, resp);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["member1"], groupId);
  });

  it('Leader kicks a member[happy trusted]', async () => {
    let testCase = {
      description: "leader kicks a member",
      expected: "the member is not in the group"
    };

    // The leader kicks member1.
    const r = await socialApi.kickGroupMember(usersTwok.acct["leader"], groupId, usersTwok.acct["member1"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );

    //Expect member1 is not in group
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.not.arrayContaining([
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the member is not kicked from the group"
        }
      }
    );
  });

  it('leader kicks him/herself, member is promoted to leader[happy trusted]', async () => {
    let testCase = {
      description: "leader kicks him/herself",
      expected: "the leader is not in the group; a member is promoted to leader"
    };
    // The leader kicks him/herself
    const r = await socialApi.kickGroupMember(usersTwok.acct["leader"], groupId, usersTwok.acct["leader"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["member1"],
      groupId
    );

    //Expect member1 role becomes leader
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'leader',
            // TODO: have more members in group rather than promoted by default; the promoted member needs to join the group after the leader.
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected group role or userid in the group"
        }
      }
    );
  });

  it('A member cannot kick another member', async () => {
    let testCase = {
      description: "a member kicks another member",
      expected: "all members are in the group"
    };

    // The leader sends an invitation to member2.
    let r = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member2"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member2 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member2"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 cannot kick member2.
    r = await socialApi.kickGroupMember(usersTwok.acct["member1"], groupId, usersTwok.acct["member2"].publicId);
    socialApi.testStatus(StatusCodes.UNPROCESSABLE_ENTITY, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["member1"],
      groupId
    );

    // Expect:
    // leaderUser role:leader
    // member1 role:member
    // member2 role:member
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member2"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the group does not contain all the original group members"
        }
      }
    );
  });
});

// eslint-disable-next-line max-lines-per-function
describe('', () => {
  it('Leader kicks himself from a group of only himself[public v2 happy trusted]', async () => {
    // The leader kicks him/herself
    const resp: request.Response = await socialApi.kickGroupMember(
      usersTwok.acct["leader"],
      groupId,
      usersTwok.acct["leader"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, resp);

    // Expect check the group become empty
    const actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["leader"], groupId);
    socialApi.testStatus(StatusCodes.NOT_FOUND, actualGroupInfo);
  });
});