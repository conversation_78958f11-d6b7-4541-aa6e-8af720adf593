package api

import (
	"errors"
	"github.com/segmentio/encoding/json"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"net/http"
)

func DecodeBody(w http.ResponseWriter, r *http.Request, obj interface{}) bool {
	return DecodeBodyReturnHttpErr(w, r, obj, true)
}

func DecodeOptionalBody(w http.ResponseWriter, r *http.Request, obj interface{}) bool {
	return DecodeBodyReturnHttpErr(w, r, obj, false)
}

func DecodeBodyReturnHttpErr(w http.ResponseWriter, r *http.Request, obj interface{}, bHttpErr bool) bool {

	log := logger.FromContext(r.Context())

	if r.Body == nil || r.Body == http.NoBody {
		log.Error().Msgf("json body nil or http.NoBody")
		if bHttpErr {
			errs.Return(w, r, errs.New(http.StatusBadRequest, errs.ERequestEmpty))
		}
		return false
	}
	decoder := json.NewDecoder(r.Body)
	err := decoder.Decode(obj)
	if err != nil {
		if err.Error() != "EOF" {
			log.Error().Err(err).Msgf("json body decode error")
		}
		if bHttpErr {
			var t *json.UnmarshalTypeError
			switch {
			case errors.As(err, &t):
				//return less internal code info in message per 2K Red
				errs.Return(w, r, errs.New(http.StatusBadRequest, errs.EJsonParse))
			default:
				errs.Return(w, r, errs.New(http.StatusBadRequest, errs.EJsonParse))
			}
		}
		return false
	}

	// escape fields in the struct for XSS injection
	// disabled for now requested by Steven Wyckoff. enable when the client can unescape.
	// utils.EscapeXSS(obj)

	return true
}
