package cache

import (
	"context"
	"fmt"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// buildExpirationLockKey take a hex str and return the expiration lock key for it
func buildExpirationLockKey(suffix string) string {
	return fmt.Sprintf("locks:expiration:%s:%s", utils.GetEnvironment(), suffix)
}

// StartExpirationChecker goroutine to check expirations
func (rc *RedisCache) StartExpirationChecker(ctx context.Context) {
	log := logger.FromContext(ctx)
	log.Debug().Msg("starting expiration checker")

	for {
		//sleep random milliseconds so not every pod tries for lock at same time
		time.Sleep(time.Duration(rand.Intn(1000)) * time.Millisecond)

		for i := 0; i <= rc.cfg.RedisExpirationHexMax; i++ {
			if rc.expirationLocks[i] == nil {
				//try to get lock and then expire keys if get lock
				suffix := fmt.Sprintf("%x", i)
				expirationLockKey := buildExpirationLockKey(suffix)
				expLock := rc.getLockCluster(ctx, expirationLockKey, time.Duration(rc.cfg.RedisLockDuration)*time.Second)
				if expLock != nil {
					rc.expirationLocks[i] = expLock
					expirationWg.Add(1)
					//log.Debug().Str("expirationLock", expirationLockKey).Str("event", "expirationLock obtained").Msg("expirationLock obtained")
					go rc.expireKeysInExpirationIdx(ctx, suffix)
				}
			}
		}
		expirationWg.Wait()
	}
}

// writeExpirationIdx	writes a key to be tracked in the expiration idx
func (rc *RedisCache) writeExpirationIdx(ctx context.Context, key, id string, expireAtUnix int64) error {
	log := logger.FromContext(ctx)

	firstChars := id[0:rc.cfg.RedisExpirationHexChars]
	expirationIdxKey := apipub.BuildExpirationIdxKey(firstChars)

	err := rc.zAdd(ctx, expirationIdxKey, redis.Z{
		Score:  float64(expireAtUnix),
		Member: key,
	}).Err()

	if err != nil {
		log.Error().Err(err).Str("id", id).Str("key", key).Int64("expireAt", expireAtUnix).Str("event", "presence expiration idx write failed").Msg("presence expiration idx write failed")
		return errs.New(http.StatusInternalServerError, errs.ERedisZAddFailed)
	}

	return nil
}

// expireKeysInExpirationIdx finds expired keys in index and "touches" them to make sure they expire quickly. also handles expiration actions.
func (rc *RedisCache) expireKeysInExpirationIdx(ctx context.Context, suffix string) {
	log := logger.FromContext(ctx)
	defer expirationWg.Done()

	pos, _ := strconv.ParseInt(suffix, 16, 64)
	if rc.expirationLocks[pos] != nil {
		defer rc.cleanupExpirationLock(ctx, pos)
	}

	if suffix == "" {
		return
	}

	expirationKey := apipub.BuildExpirationIdxKey(suffix)
	//-1 second because i think it's losing keys that have microseconds left to expire.
	unixTimeStr := strconv.FormatInt(time.Now().Add(-1*time.Second).Unix(), 10)

	minScore := "-inf"
	maxScore := unixTimeStr

	rangeBy := &redis.ZRangeBy{
		Min: minScore,
		Max: maxScore,
	}

	res, err := rc.zRangeByScore(ctx, expirationKey, rangeBy).Result()
	if err != nil {
		log.Error().Err(err).Str("expirationKey", expirationKey).Str("unixTimeStr", unixTimeStr).Str("event", "expiration zrange failed").Msg("expiration zrange failed")
	}
	if len(res) > 0 {
		if utils.IsDevOrLocal() {
			log.Debug().Str("expirationKey", expirationKey).Str("unixTimeStr", unixTimeStr).Int("count", len(res)).Str("event", "checked expiration").Msg("checked expiration")
		}
		// Touch each of the keys.  exists has a smaller return value than MGet.
		errors := rc.existsPrimary(ctx, res...).Err()
		if err != nil {
			log.Error().Err(errors).Str("expirationKey", expirationKey).Str("unixTimeStr", unixTimeStr).Str("event", "expiration existsPrimary failed").Msg("expiration existsPrimary failed")
		}
		var interfaceSlice = make([]interface{}, len(res))
		for i, key := range res {
			//create slice to batch zrem after actions completed
			interfaceSlice[i] = key

			pieces := strings.Split(key, ":")
			if len(pieces) >= 7 && pieces[6] == "presence" {
				rc.expirePresence(ctx, pieces)
				// played fmt.Sprintf("%s:prod:%s:%s:user:{%s}:played:%s", tenant, *played.Productid, utils.GetEnvironment(), *played.ForUserid, played.Userid)
			} else if len(pieces) >= 8 && pieces[6] == "played" {
				span, spanCtx := tracer.StartSpanFromContext(ctx, "ListenKeyeventNotificationExpired", tracer.ServiceName(rc.cfg.ServiceName), tracer.ResourceName("playedExpired"), tracer.SpanType("redis"))
				// delete 2nd index for recently played when obj expires
				tenant := pieces[0]
				spanCtx = context.WithValue(spanCtx, constants.T2GPCtxTenant, tenant)
				userid := utils.RemoveCurlyBrackets(pieces[5])
				productid := pieces[2]
				playedWithid := pieces[7]
				log.Debug().Str("tenant", tenant).Str("productid", productid).Str("userid", userid).Str("playedWithId", playedWithid).Msg("Played expiration")

				userSubject := index.NewUserSubject(tenant, productid, userid)
				if userSubject == nil {
					continue
				}
				playedWithKey := userSubject.PlayedWithKey()
				if playedWithKey == nil {
					continue
				}

				idx := index.NewSecondaryIndex(*playedWithKey, apipub.BuildRecentlyPlayedRedisKey(tenant, productid, userid, playedWithid))
				err2 := rc.delSecondaryIndex(spanCtx, idx)
				if err2 != nil {
					log.Error().Err(err2).Str("idxKey", idx.IdxKey()).Msg("Unable to delete secondary index for presence")
				}
				span.Finish()
				//invite fmt.Sprintf("%s:prod:%s:%s:group:{%s}:memberships:member:%s:approver:%s%s", tenant, *membership.Productid, utils.GetEnvironment(), membership.Groupid, *membership.Memberid, *membership.Approverid, firstParty)
			} else if len(pieces) >= 11 && pieces[4] == "group" && pieces[6] == "memberships" && pieces[7] == "member" && pieces[9] == "approver" {
				rc.expireMembership(ctx, pieces)
			}

		}
		errors = rc.zRem(ctx, expirationKey, interfaceSlice...).Err()
		if err != nil {
			log.Error().Err(errors).Str("expirationKey", expirationKey).Str("unixTimeStr", unixTimeStr).Str("event", "expiration zrem failed").Msg("expiration zrem failed")
		}
	}
}

// expirePresence handles the actions that happen after a presence expires.  namely auto kick from group.
func (rc *RedisCache) expirePresence(ctx context.Context, pieces []string) {
	span, spanCtx := tracer.StartSpanFromContext(ctx, "ListenKeyeventNotificationExpired", tracer.ServiceName(rc.cfg.ServiceName), tracer.ResourceName("presenceExpired"), tracer.SpanType("redis"))
	tenant := pieces[0]
	spanCtx = context.WithValue(spanCtx, constants.T2GPCtxTenant, tenant)
	productid := pieces[2]
	userid := utils.RemoveCurlyBrackets(pieces[5])
	sessionid := ""
	// TODO: [SPOP] - revert the commenting out
	// if len(pieces) >= 8 {
	// 	sessionid = utils.RemoveCurlyBrackets(pieces[7])
	// }
	log.Debug().Str("tenant", tenant).Str("productid", productid).Str("userid", userid).Msg("Presence expiration")

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return
	}
	memberOf := userSubject.MemberOfKey()
	if memberOf == nil {
		return
	}
	userGroupIdx := index.NewSecondaryIndex(*memberOf, "")
	offlinePresence := &apipub.PresenceResponse{
		Userid:    userid,
		Productid: productid,
		Status:    apipub.Offline,
	}

	bRemoveMissingGroups := false
	//get groups that the user is a member of and leave them if the presence for that productid expires.
	groups, _, err2 := getObjsFromSecondaryIndex[apipub.GroupResponse](spanCtx, rc, userGroupIdx, nil, nil, false)
	//if group related redis object is missing, ignore error
	if err2 != nil && errs.IsEqual(err2, errs.ERedisObjectMissing) {
		err2 = nil
		bRemoveMissingGroups = true
	}
	if err2 != nil {
		log.Error().Err(err2).Msg("Unable to get members group from expiring presence")
	}
	if groups != nil {
		for _, group := range *groups {
			if group != nil && productid == group.Productid {
				reason := "Kicking from Group for presence expiry"
				log.Info().Str("groupid", group.Groupid).Str("event", reason).Msg(reason)
				originalLeader := group.GetLeader()

				_, wasDisbanded, err := rc.KickOrLeaveHelper(spanCtx, group, rc.cfg.AppID, userid, &reason)
				if err != nil {
					log.Error().Err(err).Str("groupid", group.Groupid).Str("userid", userid).Str("productid", productid).Str("event", "err autokick from group on expiry").Msg("err autokick from group on expiry")
					continue
				} else {

					if wasDisbanded {
						rc.tele.SendGroupEvent(spanCtx, telemetry.BuildGroupTeleMeta(group, telemetry.KGroupDisband, userid, 0, []string{group.Groupid}, nil, nil))
					}

					// update group sizes in other group members active group records if this group is their active group
					if group.Members != nil && len(*group.Members) > 0 {
						for _, checkMemberActive := range *group.Members {
							if checkMemberActive.Userid != userid {
								rc.SetActiveGroup(ctx, group, productid, "", sessionid, checkMemberActive.Userid, 0, 0, true)
							}
						}
					}

					errTel := rc.tele.SendGroupEvent(spanCtx, telemetry.BuildGroupTeleMeta(group, telemetry.KGroupAutoKick, userid, 0, []string{group.Groupid}, nil, nil))
					if errTel != nil {
						val := "err send telemetry for autokick"
						log.Error().Err(err).Str("groupid", group.Groupid).Str("userid", userid).Str("productid", productid).Str("event", val).Msg(val)
						continue
					}
					log.Debug().Str("event", "sent telemetry for autokick").Str("userid", userid).Str("groupid", group.Groupid).Str("productid", productid).Msg("sent telemetry for autokick")

					// if new group leader send change leader telemetry
					if group.Members != nil && len(*group.Members) > 0 && originalLeader != nil && group.GetLeader().Userid != originalLeader.Userid {
						errTel = rc.tele.SendGroupEvent(spanCtx, telemetry.BuildGroupTeleMeta(group, telemetry.KGroupLeaderChange, userid, 0, []string{group.Groupid}, nil, nil))
						if errTel != nil {
							val := "err send telemetry for change leader from autokick"
							log.Error().Err(err).Str("groupid", group.Groupid).Str("userid", userid).Str("productid", productid).Str("event", val).Msg(val)
							continue
						}
					}

					messenger.SendMqttMessage(spanCtx, rc.cfg, group.Topic(tenant), messenger.MqttMessageTypePresence, offlinePresence)
					log.Debug().Str("userid", userid).Str("groupid", group.Groupid).Str("productid", productid).Str("event", "sent offline presence autokick").Msg("sent offline presencse autokick")
				}
			}
		}
	}

	if bRemoveMissingGroups {
		//remove any groups that expired for this product but are still in user memberOf list
		valKeys, _, err := getKeysFromSecondaryIndex(spanCtx, rc, userGroupIdx, nil, nil, false)
		if err == nil && valKeys != nil {
			for _, groupKey := range *valKeys {
				keyParts := strings.Split(groupKey, ":")
				if len(keyParts) >= 6 && keyParts[2] == productid && keyParts[4] == "group" {

					//check for existing object in redis. if not found, delete
					_, err2 := getCachedObjects[apipub.GroupResponse](spanCtx, rc, &[]string{groupKey})
					if err2 != nil && errs.IsEqual(err2, errs.ERedisObjectMissing) {
						missingGroup := &apipub.GroupResponse{
							Productid: productid,
							Groupid:   utils.RemoveCurlyBrackets(keyParts[5]),
						}
						rc.DeleteUserGroupIdxs(spanCtx, missingGroup.Productid, missingGroup.Groupid, userid)
						log.Debug().Str("groupKey", groupKey).Str("productid", productid).Str("userid", userid).Msg("Remove old expired groups")
					}
				}
			}
		}
	}

	userPresenceKey := userSubject.PresencesSetForKey()
	if userPresenceKey == nil {
		return
	}

	idx := index.NewSecondaryIndex(*userPresenceKey, apipub.BuildPresenceRedisKey(tenant, productid, userid, sessionid))
	//Also delete secondary index for presence since obj is expired
	err2 = rc.delSecondaryIndex(spanCtx, idx)
	if err2 != nil {
		log.Error().Err(err2).Str("idxKey", idx.IdxKey()).Msg("Unable to delete secondary index for presence")
	}

	messenger.SendMqttMessage(spanCtx, rc.cfg, offlinePresence.Topic(tenant), messenger.MqttMessageTypePresence, offlinePresence)

	rc.tele.SendGenericEvent(spanCtx, telemetry.BuildGenericTeleMeta(telemetry.KPresenceOffline, productid, userid, 0, nil, nil))

	span.Finish()
}

// expireMembership handles the actions after a membershipRequest expires.  Primarily for Invite expirations.
func (rc *RedisCache) expireMembership(ctx context.Context, pieces []string) {
	span, spanCtx := tracer.StartSpanFromContext(ctx, "ListenKeyeventNotificationExpired", tracer.ServiceName(rc.cfg.ServiceName), tracer.ResourceName("invitedExpired"), tracer.SpanType("redis"))
	//when invite expires, remove it from group as well
	tenant := pieces[0]
	spanCtx = context.WithValue(spanCtx, constants.T2GPCtxTenant, tenant)
	memberid := pieces[8]
	productid := pieces[2]
	groupid := utils.RemoveCurlyBrackets(pieces[5])
	approverid := pieces[10]

	firstParty := false
	if len(pieces) > 11 {
		firstParty = true
	}
	log.Debug().Str("groupid", groupid).Str("productid", productid).Str("memberid", memberid).Str("approverid", approverid).Bool("firstParty", firstParty).Msg("Invite expiration")

	invite := apipub.MembershipRequest{
		Groupid:            groupid,
		Memberid:           memberid,
		Approverid:         approverid,
		IsFirstPartyInvite: aws.Bool(firstParty),
		Status:             apipub.Invited,
		Productid:          &productid,
	}

	var group *apipub.GroupResponse
	group, err := rc.GetGroup(ctx, productid, groupid)
	if err != nil || group == nil {
		return
	}

	err = rc.RemoveMembershipRequestFromGroup(spanCtx, group, invite)
	if err != nil && errs.IsEqual(err, errs.EGroupsInvalidMembershipStatus) {
		//if invalid status try request.  only invites should expire but it's not forbidden to ttl join requests.
		invite.Status = apipub.Requested
		rc.RemoveMembershipRequestFromGroup(spanCtx, group, invite)
	}
	span.Finish()
}
