name: Run interaction API tests
run-name: Run test on ${{ github.event.inputs.socialServiceEnvLabel || github.ref  }}


on:
  schedule:
    # 6/7AM PST/PDT | 2PM UTC
    - cron: '0 14 * * *'
    # 7/8AM PST/PDT | 3PM UTC
    - cron: '0 15 * * *'
  workflow_dispatch:
    inputs:
      note:
        description: 'Additional info for this test run'
        default: 'none'
        type: string
      mandatoryTag:
        description: 'Mandatory Tag'
        default: 'public-v2'
        type: choice
        options:
        - public-v1
        - public-v2
        - trusted-v1
        - trusted-v2
      optionalInclusionTag:
        description: 'Optional Inclusion Tags. Choices are: happy,broken,lowprio,known; multiple tags with comma are allowed'
        default: ''
        type: string
      optionalExclusionTag:
        description: 'Optional Exclusion Tags. Choices are: happy,broken,lowprio,known; multiple tags with comma are allowed'
        default: ''
        type: string
      testSuite:
        description: 'Test Suite'
        default: ''
        type: choice
        options:
        -
        - autokick
        - blockList
        - controlMessage
        - createFriendship
        - createGroup
        - deleteGroup
        - discovery
        - friendPresence
        - getGroups
        - getInvites
        - getMembers
        - getProfile
        - heartbeatPresence
        - inviteUser
        - kickMember
        - leaveGroup
        - listFriends
        - membershipMgmt
        - mqttFriend
        - mqttGroup
        - recentlyPlayedList
        - removeFriendship
        - reportUser
        - search2KUsers
        - searchFriends
        - setPresence
        - updateGroup
        - updateMember
        - crossPlay
        - groupCapacity
      testCase:
        description: 'Test Case'
        default: ''
        type: string
      loopType:
        description: 'Loop Type'
        default: ''
        type: choice
        options:
        - runs back-to-back
        - retries if any tests fail
      maxNumber:
        description: 'Max Number'
        default: 1
        type: number
      socialServiceEnvLabel:
        description: 'Social Service Environment Label.Enter env_label from https://us-east-1.console.aws.amazon.com/dynamodbv2/home?region=us-east-1#item-explorer?table=social-env-ver-mapping and the corresponding endpoints will be used. Trusted server endpoints are derived from those.'
        default: 'develop'
        type: string
  # inputs of workflow_call should mirror workflow_dispatch
  workflow_call:
    inputs:
      note:
        description: 'Additional info for this test run'
        default: 'none'
        type: string
      mandatoryTag:
        description: 'Mandatory Tag'
        default: 'public-v2'
        type: string
      optionalInclusionTag:
        description: 'Optional Inclusion Tag'
        default: ''
        type: string
      optionalExclusionTag:
        description: 'Optional Exclusion Tag'
        default: ''
        type: string
      testSuite:
        description: 'Test Suite'
        default: ''
        type: string
      testCase:
        description: 'Test Case'
        default: ''
        type: string
      loopType:
        description: 'Loop Type'
        default: ''
        type: string
      maxNumber:
        description: 'Max Number'
        default: 1
        type: number
      socialServiceEnvLabel:
        description: 'Social Service Environment Label'
        default: 'develop'
        type: string
concurrency:
  group: ${{ github.ref }}
  cancel-in-progress: false

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  test:
    name: Run interaction API tests
    runs-on: [t2gp-arc-linux]
    env:
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      # environment label
      # for "schedule" event, set to "develop"
      SOCIAL_SERVICE_ENVIRONMENT_LABEL: ${{ inputs.socialServiceEnvLabel || 'develop' }}
    steps:
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1

      - name: Fetch Deployment Info
        id: fetch_deployment_info
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: get
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          key: "{ \"env_label\": \"${{ env.SOCIAL_SERVICE_ENVIRONMENT_LABEL }}\" }"
      - name: Check out code
        uses: actions/checkout@v3
      # - name: Version map to correct sha
      #   run: |
      #     ver=${{ fromJson(steps.fetch_deployment_info.outputs.item).version }}
      #     full_sha=$(git rev-parse ${ver: -8})
      #     echo "REF_TO_CHECKOUT=${full_sha}" >> $GITHUB_ENV

      - name: cleanup
        run: |
          docker run -v ${PWD}:/var/clean node:12.22.4-alpine3.11 rm -rf /var/clean/deployments/vmq-plugin-social/ /var/clean/.scannerwork /var/clean/cp.out
        continue-on-error: true


      # TODO: Fix slack notif 3rd party action failing to be pulled from ghcr registry
      # - name: Slack Notification
      #   if: github.event_name == 'workflow_dispatch'
      #   continue-on-error: true
      #   uses: rtCamp/action-slack-notify@v2
      #   env:
      #     SLACK_TITLE: "Dispatch received"
      #     SLACK_MESSAGE: "A manual run was requested by ${{ github.triggering_actor }}"
      #     SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK_URL_QE }}

      - name: Update branch API endpoint
        run: echo "SOCIAL_API_URL=https://social-service-pr-${{github.event.pull_request.number}}.d2dragon.net/" >> $GITHUB_ENV
        if: github.ref != 'refs/heads/develop'

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12.0"
      - name: Use Node.js 
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          registry-url: https://npm.pkg.github.com
          scope: take-two

      - name: Install required Python packages
        run: |
          python3 -m venv venv
          venv/bin/python3 -m pip install -r requirements.txt
        working-directory: ./tests/api-interaction-tests

      - name: Install required node modules
        run: |
          npm install
          mkdir export
        working-directory: ./tests/api-interaction-tests

      - name: Run Test
        env:
          ## env variables for the downstream report processing
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_QE }}

          AWS_SECRET_NAME: "/secrets/t2gp/DTL_BASIC_AUTH"
          AWS_REGION: us-east-1

          SERVER_INSTANCE_ID: "4022a2a20b864d068d037694f5bba9fc"

          DD_API_KEY: ${{ secrets.DD_API_KEY }}
          GH_PAT: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}

          HEAD_COMMIT_ID: unknown
          HEAD_COMMIT_URL: unknown
          HEAD_COMMIT_MESSAGE: unknown

          PR_TITLE: unknown
          PR_HEAD_SHA: unknown
          PR_HTML_URL: unknown

          EVENT_NAME: ${{ github.event_name }}
          ACTOR: ${{ github.actor }}
          TRIGGERING_ACTOR: ${{ github.triggering_actor }}

          SOCIAL_API_ACCESS_LEVEL: unknown

          SOCIAL_SERVICE_ENVIRONMENT: unknown

          SOCIAL_API_VERSION: unknown

          PUBLIC_SERVICE_API_URL: ${{ fromJson(steps.fetch_deployment_info.outputs.item).api_url }}
          PUBLIC_SERVICE_MQTT_URL: ${{ fromJson(steps.fetch_deployment_info.outputs.item).mqtt_url }}

          TRUSTED_SERVER_API_URL: unknown
          TRUSTED_SERVER_MQTT_URL: ${{ fromJson(steps.fetch_deployment_info.outputs.item).mqtt_url }}        

          TRUSTED_CREDENTIAL: unknown
          TRUSTED_CREDENTIAL_TYPE: unknown
          TRUSTED_DEVELOP_V1_CREDENTIAL: ${{ secrets.TRUSTED_DEVELOP_CREDENTIAL }}
          TRUSTED_INTEGRATION_V1_CREDENTIAL: ${{ secrets.TRUSTED_INTEGRATION_CREDENTIAL }}
          TRUSTED_STAGING_V1_CREDENTIAL: ${{ secrets.TRUSTED_STAGING_CREDENTIAL }}
          TRUSTED_CERT_V1_CREDENTIAL: ${{ secrets.TRUSTED_CERT_CREDENTIAL }}
          TRUSTED_PRODUCTION_V1_CREDENTIAL: ${{ secrets.TRUSTED_PRODUCTION_CREDENTIAL }}

          NOTE: ${{ inputs.note }}

          MANDATORY_TAG: ${{ inputs.mandatoryTag }}
          OPTIONAL_INCLUSION_TAG: ${{ inputs.optionalInclusionTag }}
          OPTIONAL_EXCLUSION_TAG: ${{ inputs.optionalExclusionTag }}

          TEST_SUITE: ${{ inputs.testSuite }}
          TEST_CASE: ${{ inputs.testCase }}

          LOOP_TYPE: ${{ inputs.loopType }}

          RUN_MAX: ${{ inputs.maxNumber }}

          RUN_CNT: 1

        run: |
          ## process based on event types
          # schedule
          if [[ "${{ github.event_name }}" == "schedule" ]]; then
            if [[ "${{ github.event.schedule }}" == "0 14 * * *" ]]; then
              MANDATORY_TAG="public-v2"
            elif [[ "${{ github.event.schedule }}" == "0 15 * * *" ]]; then
              MANDATORY_TAG="trusted-v2"
            fi

            OPTIONAL_EXCLUSION_TAG="broken|lowprio"
            LOOP_TYPE="retries if any tests fail"
            RUN_MAX=2

          # workflow_dispatch (manual trigger)
          elif [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            # nothing to do
            :

          # repository_dispatch (triggered by push)
          elif [[ "${{ github.event_name }}" == "repository_dispatch" ]]; then
            # override with the info from the workflow triggered by push
            EVENT_NAME="${{ github.event.client_payload.origin_event_name }}"
            ACTOR="${{ github.event.client_payload.origin_actor }}"

            HEAD_COMMIT_ID="${{ github.event.client_payload.origin_head_commit_id }}"
            HEAD_COMMIT_URL="${{ github.event.client_payload.origin_head_commit_url }}"
            HEAD_COMMIT_MESSAGE="${{ github.event.client_payload.origin_head_commit_message }}"

          # pull_request
          elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
            PR_TITLE="${{ github.event.pull_request.title }}"
            PR_HEAD_SHA="${{ github.event.pull_request.head.sha }}"
            PR_HTML_URL="${{ github.event.pull_request.html_url }}"

          # push
          elif [[ "${{ github.event_name }}" == "push" ]]; then
            echo "Push event. Don't set extra envvars"

          # unknown event
          else
            echo "unknown event [${{ github.event_name }}]"
            exit 1
          fi

          # determine SOCIAL_API_ACCESS_LEVEL based on the coverage
          [[ "$MANDATORY_TAG" == *"trusted"* ]] && SOCIAL_API_ACCESS_LEVEL="trusted" || SOCIAL_API_ACCESS_LEVEL="public"

          # check if SOCIAL_SERVICE_ENVIRONMENT_LABEL ends with -v2 and remove it if it does
          ENVIRONMENT_LABEL="${{ env.SOCIAL_SERVICE_ENVIRONMENT_LABEL }}"
          if [[ "$ENVIRONMENT_LABEL" == *"-v2" ]]; then
            SOCIAL_SERVICE_ENVIRONMENT="${ENVIRONMENT_LABEL%-v2}"
          else
            SOCIAL_SERVICE_ENVIRONMENT="$ENVIRONMENT_LABEL"
          fi

          # determine TRUSTED_SERVER_API_URL
          TRUSTED_SERVER_API_URL="${PUBLIC_SERVICE_API_URL/service/trusted}/server"

          # separate mandatory tag by '-' delimiter
          arrMandatoryTags=(${MANDATORY_TAG//-/ })

          # set the API version
          SOCIAL_API_VERSION=${arrMandatoryTags[1]}

          # Determine the TRUSTED_CREDENTIAL_TYPE and TRUSTED_CREDENTIAL based on:
          # - SOCIAL_API_ACCESS_LEVEL
          # - SOCIAL_API_VERSION 
          # - SOCIAL_SERVICE_ENVIRONMENT
          #
          # For API v1: Use Basic Auth, with different credential strings for each environment
          # For API v2: Use DTL token authentication
          if [[ "$SOCIAL_API_ACCESS_LEVEL" == "trusted" ]]; then
            if [[ "$SOCIAL_API_VERSION" == "v1" ]]; then
              TRUSTED_CREDENTIAL_TYPE="BasicAuth"
              case $SOCIAL_SERVICE_ENVIRONMENT in
                develop) 
                  TRUSTED_CREDENTIAL=$TRUSTED_DEVELOP_V1_CREDENTIAL
                  ;;
                integration)
                  TRUSTED_CREDENTIAL=$TRUSTED_INTEGRATION_V1_CREDENTIAL
                  ;;
                staging)
                  TRUSTED_CREDENTIAL=$TRUSTED_STAGING_V1_CREDENTIAL
                  ;;
                cert)
                  TRUSTED_CREDENTIAL=$TRUSTED_CERT_V1_CREDENTIAL
                  ;;
                production)
                  TRUSTED_CREDENTIAL=$TRUSTED_PRODUCTION_V1_CREDENTIAL
                  ;;
                *)
                  echo "for environment [$SOCIAL_SERVICE_ENVIRONMENT], using trusted server credential for the develop environment"
                  TRUSTED_CREDENTIAL=$TRUSTED_DEVELOP_V1_CREDENTIAL
                  ;;
              esac
            elif [[ "$SOCIAL_API_VERSION" == "v2" ]]; then
              TRUSTED_CREDENTIAL_TYPE="DTLToken"
              TRUSTED_CREDENTIAL=''
            else
              echo "unknown social API version [$SOCIAL_API_VERSION]"
              exit 1             
            fi          
          fi

          ## process the input of tag
          ## remove extra whitespace between optional tags; replace ',' to be '|' delimiter
          OPTIONAL_INCLUSION_TAG=$(echo "$OPTIONAL_INCLUSION_TAG" | tr -d ' ' | tr ',' '|')
          OPTIONAL_EXCLUSION_TAG=$(echo "$OPTIONAL_EXCLUSION_TAG" | tr -d ' ' | tr ',' '|')

          ## process jest test name pattern expression
          # if the optional tags exist: mandatory tag and optional tag are in 'and' relationship
          # if the optional tags are not existing: it will not contain the optional tag expression
          mandatory_tag_exp=""
          for tag in "${arrMandatoryTags[@]}"; do
            mandatory_tag_exp+="(?=.*\[.*\b$tag\b.*\])"
          done
          inclusion_tag_exp="(?=.*\[.*\b($OPTIONAL_INCLUSION_TAG)\b.*\])"
          exclusion_tag_exp="(?!.*\[.*\b($OPTIONAL_EXCLUSION_TAG)\b.*\])"

          tags=""
          if [[ -n "$MANDATORY_TAG" ]]; then
            tags+="$mandatory_tag_exp"
            if [[ -n "$OPTIONAL_INCLUSION_TAG" ]]; then
              tags+="$inclusion_tag_exp"
            fi
            if [[ -n "$OPTIONAL_EXCLUSION_TAG" ]]; then
              tags+="$exclusion_tag_exp"
            fi
          fi

          ## get the test account env files
          aws s3 cp s3://t2gp-social-develop/ci-test-account-envs/.env.twok_accounts_FOR_CI_DO_NOT_USE ./.env.twok_accounts
          aws s3 cp s3://t2gp-social-develop/ci-test-account-envs/.env.steam_accounts_FOR_CI_DO_NOT_USE ./.env.steam_accounts
          aws s3 cp s3://t2gp-social-develop/ci-test-account-envs/.env.epic_accounts_FOR_CI_DO_NOT_USE ./.env.epic_accounts

          ## run sanity test before a full set of tests
          ## except for sanity test, since we don't need to run sanity test for sanity test :)
          # TODO: sanity test for trusted server tests
          if [[ "$SOCIAL_API_ACCESS_LEVEL" == "public" ]]; then
            # run test
            npm run test:exportJson -- --testPathPattern='sanity' \
                                      && RC=$?
            # sanity test failed
            if [[ $RC -ne 0 ]]; then
              # parse result and report
              venv/bin/python3 ./parse_test_results.py export/reporter.json

              exit 1
            fi
          fi

          ## run tests
          # Option to specify which loop type to run:
          # LOOP_TYPE="runs back-to-back" will run max number of times no matter if not all pass
          #
          # LOOP_TYPE="retries if any tests fail", retry if not all pass

          # set RUN_CNT based on the loop type
          [[ "$LOOP_TYPE" == "runs back-to-back" ]] && RUN_CNT=1 || RUN_CNT=0

          RC=1
          while ([[ "$LOOP_TYPE" == "runs back-to-back" ]] && [ $RUN_CNT -le $RUN_MAX ]) ||
            ([[ "$LOOP_TYPE" == "retries if any tests fail" ]] && [ $RC -ne 0 ] && [ $RUN_CNT -le $RUN_MAX ])
          do 

            if [[ "$MANDATORY_TAG" == *"public"* || "$MANDATORY_TAG" == *"trusted"* ]]; then
              # Options to specify which paths to be included or excluded. For example:
              # --testPathPattern='integration/friend/|integration/group/'
              # --testPathIgnorePatterns='other/|local/|integration/chat/|sanity/'
              #
              # (The following is a subset of testPathPattern and testPathIgnorePatterns)
              # Control test coverage through labels. For example:
              # full coverage:
              # -t='^(?!.*\[.*\b(broken|lowprio)\b.*\]).*'
              #
              # happy path coverage:
              # -t='^(?!.*\[.*\b(broken|lowprio)\b.*\])(?=.*\[.*\bhappy\b.*\]).*'
              #
              # See https://github.com/take-two-t2gp/t2gp-social-service/tree/develop/tests/api-interaction-tests#tag-filters for details about tags.
              #
              # Always return success so the job doesn't stop here for failed tests. The "continue-on-error" doesn't work because although the job
              # doesn't fail, the following commands are skipped.
              npm run test:exportJson --  --testPathPattern="$TEST_SUITE" --testPathIgnorePatterns='other/|local/|integration/tests/common/chat/|sanity/' \
                                        -t="$tags.*$TEST_CASE.*" \
                                        && RC=$?
              # parse full test result:
              # ./parse_test_results.py [path]
              #
              # parse "happy path" test result:
              # ./parse_test_results.py [path] --runtags happy
              #
              # parse test result with 2 labels:
              # ./parse_test_results.py [path] --runtags happy label1
              #
              # parse test result with specificSuite and specific test Case
              # ./parse_test_results.py [path] --specificSuite "$TEST_SUITE" --specificTest "$TEST_CASE"
              #
              # parse test result with happy label, specificSuite and specificTest
              # ./parse_test_results.py [path] --runtags happy --specificSuite "$TEST_SUITE" --specificTest "$TEST_CASE"
              MANDATORY_TAG_SPACE_DELIMITER=$(echo "$MANDATORY_TAG"  | tr '-' ' ')
              OPTIONAL_INCLUSION_TAG_SPACE_DELIMITER=$(echo "$OPTIONAL_INCLUSION_TAG"  | tr '|' ' ')
              venv/bin/python3 ./parse_test_results.py export/reporter.json --runMandatorytags $MANDATORY_TAG_SPACE_DELIMITER --runOptionaltags $OPTIONAL_INCLUSION_TAG_SPACE_DELIMITER --specificSuite "$TEST_SUITE" --specificTest "$TEST_CASE"
      

            else
              echo "unknown mandatory tag [$MANDATORY_TAG]"
              exit 1
            fi

            RUN_CNT=$(( $RUN_CNT + 1 ))

          done


        working-directory: ./tests/api-interaction-tests
