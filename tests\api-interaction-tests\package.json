{"name": "@t2gp/social-service", "version": "1.0.0", "description": "Social service API test", "main": "", "devDependencies": {"@types/jest": "^26.0.20", "@types/node-fetch": "^2.5.8", "@types/supertest": "^2.0.10", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "4.5.0", "@typescript-eslint/parser": "4.5.0", "dotenv": "^8.2.0", "eslint": "^7.18.0", "eslint-config-prettier": "^6.13.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-wdio": "^6.6.0", "http-status-codes": "^2.1.4", "jest": "^26.6.3", "jest-github-actions-reporter": "^1.0.3", "jest-html-reporter": "^3.3.0", "jest-json-cumulative-reporter": "^1.2.5", "mqtt": "^4.2.6", "node-fetch": "^2.6.7", "prettier": "^2.2.1", "supertest": "^6.0.1", "ts-jest": "^26.4.4", "ts-node": "^9.0.0", "typescript": "^3.9.7"}, "scripts": {"lint:all": "npm run lint:eslint:all && npm run lint:styles:all", "lint:eslint:all": "eslint --max-warnings 0 \"integration/**/*.{ts,tsx}\"", "lint:eslint:fix": "npm run lint:eslint:all -- --fix", "lint:styles:all": "stylelint \"integration/**/*.{ts,tsx}\"", "lint:styles:fix": "npm run lint:styles:all -- --fix", "lint:tests": "eslint  --max-warnings 0 \"integration/**/*.test.{ts,tsx}\" && tsc --noEmit", "test": "jest -i", "test:coverage": "jest -i --coverage", "test:exportJson": "jest -i --json --outputFile=./export/reporter.json", "sanity": "jest -i sanity/sanity.test.ts"}, "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-sqs": "^3.309.0", "aws-sdk": "^2.1692.0", "jwt-decode": "^3.1.2"}}