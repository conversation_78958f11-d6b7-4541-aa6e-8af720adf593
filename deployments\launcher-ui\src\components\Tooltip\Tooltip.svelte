<script lang="ts">
  export let value = '';
</script>

<style>
  [data-tooltip] {
    position: relative;
    z-index: 2;
    display: block;
  }

  [data-tooltip]:before,
  [data-tooltip]:after {
    visibility: hidden;
    opacity: 0;
    pointer-events: none;
    transition: 0.2s ease-out;
    transform: translate(-50%, 5px);
  }

  [data-tooltip]:before {
    position: absolute;
    bottom: 100%;
    margin-bottom: 0.825rem;
    padding: 1rem;
    border-radius: 0.25rem;
    background-color: rgba(64, 64, 64, 1);
    color: rgba(255, 255, 255, 0.6);
    content: attr(data-tooltip);
    text-align: center;
    font-size: 0.625rem;
    line-height: 1.2;
    transition: 0.2s ease-out;
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-transform: capitalize;
    box-sizing: border-box;
    min-width: 5rem;
    letter-spacing: 0.05rem;
  }

  [data-tooltip]:after {
    position: absolute;
    bottom: 100%;
    left: 50%;
    width: 0;
    border-top: 0.3125rem solid rgba(255, 255, 255, 0.5);
    border-right: 0.3125rem solid transparent;
    border-left: 0.3125rem solid transparent;
    content: ' ';
    font-size: 0;
    line-height: 0;
    margin-bottom: 0.525rem;
  }

  [data-tooltip]:hover:before,
  [data-tooltip]:hover:after {
    visibility: visible;
    opacity: 1;
  }
  [data-tooltip='false']:hover:before,
  [data-tooltip='false']:hover:after {
    visibility: hidden;
    opacity: 0;
  }
</style>

<div data-tooltip="{value}">
  <slot />
</div>
