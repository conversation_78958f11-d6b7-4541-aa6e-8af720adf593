package middleware

import (
	"context"
	"io"
	"net/http"
	"strings"

	jwtgo "github.com/golang-jwt/jwt/v4"
	"github.com/segmentio/encoding/json"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

// BearerJWT parse the store the jwt in the request context
func BearerJWT(cfg *config.Config, rc *cache.RedisCache) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			auth := r.Header.Get("Authorization")
			if auth != "" {
				authSplit := strings.Split(auth, " ")
				if len(authSplit) == 2 && authSplit[0] == "Bearer" {
					bearer := authSplit[1]
					ctx = context.WithValue(ctx, constants.BearerAuthString, bearer)
					token2k, err := jwt.ParseJWTTokenWithoutValidation(bearer)
					if err == nil {
						ctx = context.WithValue(ctx, constants.BearerAuthJWT, token2k)

						// add tenant type
						// see identity.go (cannot import because of cycle)
						tenant := "dna"
						if utils.StringContainsSubstr(token2k.Claims.Issuer, "rockstargames") {
							tenant = "rsg"
							claims := authheader.RSClaims{}
							if _, _, err = jwtgo.NewParser(jwtgo.WithoutClaimsValidation()).ParseUnverified(bearer, &claims); err == nil {
								token2k.Claims.Subject = claims.NameID
							}
							// TODO: figure out how to get a product id / ost
							token2k.Claims.ProductID = "default"
							token2k.Claims.OnlinePlatformType = 0
						}
						ctx = context.WithValue(ctx, constants.T2GPCtxTenant, tenant)
						//add productid to span
						span, ok := tracer.SpanFromContext(ctx)
						if ok {
							span.SetTag("id.product", GetProductNameFromProductID(ctx, cfg, rc, tenant, token2k.Claims.ProductID))
							span.SetTag("id.pid", token2k.Claims.ProductID)
							span.SetTag("id.iss", token2k.Claims.Issuer)
							span.SetTag("id.userid", token2k.Claims.Subject)
						}
					}
				}
			}

			tenant := r.Header.Get(constants.KT2GPLabel)
			if tenant != "" {
				ctx = context.WithValue(ctx, constants.T2GPCtxTenant, tenant)
			}
			next.ServeHTTP(w, r.WithContext(ctx))
		}
		return http.HandlerFunc(fn)
	}
}

// getProdNameFromIdentity gets human readable product name from productid from identity.  This unfortunately cant be part of the identity packge where it should be because of cyclical references so it has to be handled here
func getProdNameFromIdentity(ctx context.Context, cfg *config.Config, tenant, productid string) string {
	log := logger.FromContext(ctx)

	if tenant == "dna" {
		url := cfg.SsoURL + "/app/products/" + productid
		body, err := httpGetFromDna(ctx, cfg, url)

		if err != nil {
			return ""
		}

		var response map[string]interface{}
		err = json.Unmarshal(body, &response)
		if err != nil {
			log.Error().Err(err).Str("body", string(body)).Msgf("unmarshal failed in dna product name")
			return ""
		}

		var productId string
		if response != nil && response["name"] != nil {
			productId = response["name"].(string)
		}
		return productId
	}
	return ""
}

// GetProductNameFromProductID gets human readable product name from productid from cache -> identity.  This unfortunately cant be part of the cache packge where it should be because of cyclical references so it has to be handled here
func GetProductNameFromProductID(ctx context.Context, cfg *config.Config, rc *cache.RedisCache, tenant, productid string) string {
	log := logger.FromContext(ctx)

	//get value from cache
	name, err := rc.GetProductIdToName(ctx, tenant, productid)
	if err != nil {
		log.Warn().Err(err).Str("event", "err reading productid/name from redis").Str("productid", productid).Msg("err reading productid/name from redis")
	}
	if err == nil {
		return name
	}

	//if not in cache get from provider
	name = getProdNameFromIdentity(ctx, cfg, tenant, productid)
	if name != "" {
		err = rc.SetProductIdToName(ctx, tenant, productid, name)
		if err != nil {
			log.Warn().Err(err).Str("event", "err writing productid/name to redis").Str("productid", productid).Str("name", name).Msg("err writing productid/name to redis")
		}
	} else {
		name = productid
	}
	return name
}

// HTTP helper to get from DNA.  Copied over from Identity package because of cyclical references
func httpGetFromDna(ctx context.Context, cfg *config.Config, url string) ([]byte, error) {
	log := logger.FromContext(ctx)

	var err error
	var req *http.Request
	req, err = http.NewRequest("GET", url, nil)
	if err != nil {
		log.Error().Err(err).Msgf("create failed failed url='%s'", url)
		return nil, err
	}

	req.Header.Add(constants.KContentType, constants.KApplicationJson)
	req.Header.Add("Authorization", "Basic "+cfg.AppBasicAuth)

	span, _ := tracer.StartSpanFromContext(ctx, "dna.getLinks", tracer.ResourceName(req.URL.Path))
	var resp *http.Response
	resp, err = http.DefaultClient.Do(req)
	if err != nil {
		span.Finish()
		log.Error().Err(err).Msgf("GET failed %s %v", url, err)
		return nil, err
	}
	span.Finish()

	// forward body to client
	defer resp.Body.Close()
	var body []byte
	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}
	if resp.StatusCode != http.StatusOK {
		dnaError := errs.New(http.StatusInternalServerError, errs.EDnaGeneric)
		err = json.Unmarshal(body, &dnaError)
		if err != nil {
			return nil, err
		}
		return nil, dnaError
	}

	return body, nil
}
