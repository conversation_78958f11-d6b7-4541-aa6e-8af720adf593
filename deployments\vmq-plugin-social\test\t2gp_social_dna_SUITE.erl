-module(t2gp_social_dna_SUITE).

-compile(nowarn_export_all).
-compile(export_all).

-include_lib("eunit/include/eunit.hrl").

%% before running rebar3, run
%% export $(cat .env | grep -v '#' | xargs)
init_per_suite(Config) ->
  cover:start(),
  t2gp_social_test:configure(),
  application:ensure_all_started(t2gp_social),
  Config.

end_per_suite(_Config) ->
  application:stop(t2gp_social),
  ok.

init_per_testcase(_Case, Config) ->
  t2gp_social_test:configure(),
  ssl:start(),
  t2gp_social_dna:start_link(),
  Config.

end_per_testcase(_Case, _Config) ->
  ok.

all() ->
  [test_validate_jwt,
   test_clear_jwks,
   test_clear_applications,
   test_clear_all,
   test_invalid_call,
   test_terminate,
   test_code_change,
   test_handle_cast,
   test_handle_info,
   test_get_key_from_jwks,
   test_get_application,
   test_discover_sso,
   test_parse_discovery].

test_validate_jwt(_) ->
  User = <<"<EMAIL>">>,
  Pass = <<"D2CTesting">>,
  {ok, JWT_RS56} = t2gp_social_test:login_rs256(User, Pass),
  % ?debugVal(JWT_RS56),
  {ok, JWT_HS256} = t2gp_social_test:login_hs256(User, Pass),
  % ?debugVal(JWT_HS256),
  {ok, Claims_RS256} = gen_server:call(t2gp_social_dna, {validate_jwt, JWT_RS56}),
  <<"b287e655461f4b3085c8f244e394ff7e">> = maps:get(<<"sub">>, Claims_RS256, <<>>),
  % run again to test jwks cache
  {ok, Claims_RS256} = gen_server:call(t2gp_social_dna, {validate_jwt, JWT_RS56}),
  <<"b287e655461f4b3085c8f244e394ff7e">> = maps:get(<<"sub">>, Claims_RS256, <<>>),

  {ok, Claims_HS256} = gen_server:call(t2gp_social_dna, {validate_jwt, JWT_HS256}),
  <<"b287e655461f4b3085c8f244e394ff7e">> = maps:get(<<"sub">>, Claims_HS256, <<>>),
  % run again to test application secret cache
  {ok, Claims_HS256} = gen_server:call(t2gp_social_dna, {validate_jwt, JWT_HS256}),
  <<"b287e655461f4b3085c8f244e394ff7e">> = maps:get(<<"sub">>, Claims_HS256, <<>>),

  AlgNoneJWT =
    <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVT"
      "Iiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRy"
      "dWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4Ijox"
      "NjUyMzAyMTYyLCJydGkiOiJiYzNjNzQ3YzAxM2M0YTYxOWE4MzgxYzc4ODljMmZmYSIs"
      "ImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJj"
      "dHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5"
      "ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiOGEzZWU4"
      "MDVmMWNkNDI3YTlmNzQ1MTQyNmMyZTkzZDEiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VR"
      "TFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6MTY1MjI5ODU2MiwiaWF0IjoxNjUyMjk0OTYy"
      "LCJqdGkiOiIxNzQxNzVjY2FhNjg0MmY3ODQ0OTM5N2EzOTQ3MzY1ZCIsImxhdCI6Mzku"
      "MDQ2OX0.">>,
  {error, invalid_alg} = gen_server:call(t2gp_social_dna, {validate_jwt, AlgNoneJWT}),
  ok.

test_clear_jwks(_) ->
  ok = gen_server:call(t2gp_social_dna, {clear_jwks}),
  ok.

test_clear_applications(_) ->
  ok = gen_server:call(t2gp_social_dna, {clear_applications}),
  ok.

test_clear_all(_) ->
  ok = gen_server:call(t2gp_social_dna, {clear_all}),
  ok.

test_invalid_call(_) ->
  {error, invalid_msg} = gen_server:call(t2gp_social_dna, {invalid_call}),
  ok.

test_terminate(_) ->
  ok = t2gp_social_dna:terminate(shutdown, #{}),
  ok.

test_code_change(_) ->
  {ok, _} = t2gp_social_dna:code_change(old_version, new_version, []),
  ok.

test_handle_cast(_) ->
  {noreply, _} = t2gp_social_dna:handle_cast(cast, []),
  ok.

test_handle_info(_) ->
  {noreply, _} = t2gp_social_dna:handle_info(info, []),
  ok.

test_get_key_from_jwks(_) ->
  GoodURL = "https://sso.api.2kcoretech.online/sso/v2.0",
  GoodKid = <<"03a61152-ecd9-4936-80d2-4536c9e64556">>,
  BadKid = <<"bad key id">>,
  {ok, _Key, Jwks} = t2gp_social_dna:get_key_from_jwks(GoodURL, GoodKid, undefined),
  {error, _} = t2gp_social_dna:get_key_from_jwks(GoodURL, BadKid, undefined),
  {ok, _, _} = t2gp_social_dna:get_key_from_jwks(GoodURL, GoodKid, Jwks),
  {error, _} = t2gp_social_dna:get_key_from_jwks(GoodURL, BadKid, Jwks),
  NotFoundURL = "https://httpbin.org",
  {error, {http_error, 404, _, _}} =
    t2gp_social_dna:get_key_from_jwks(NotFoundURL, BadKid, undefined),
  BadURL = "bad url",
  {error, _Err} = t2gp_social_dna:get_key_from_jwks(BadURL, BadKid, undefined),
  ok.

test_get_application(_) ->
  GoodURL = "https://sso.api.2kcoretech.online/sso/v2.0",
  NotFoundURL = "https://httpbin.org",
  BadURL = "bad url",
  AppID = "e3c64ee90b8044d2ba35f12ea161fae4",
  BasicAuth = os:getenv("SOCIAL_APP_BASIC_AUTH"),
  {ok, App, Apps} = t2gp_social_dna:get_application(GoodURL, AppID, BasicAuth, #{}),
  {ok, App, Apps} = t2gp_social_dna:get_application(GoodURL, AppID, BasicAuth, Apps),
  {error, {http_error, 404, _, _}} =
    t2gp_social_dna:get_application(NotFoundURL, AppID, BasicAuth, #{}),
  {error, _} = t2gp_social_dna:get_application(BadURL, AppID, BasicAuth, #{}),
  ok.

test_discover_sso(_) ->
  GoodURL = "https://discovery.api.2kcoretech.online",
  GoodAppID = "e3c64ee90b8044d2ba35f12ea161fae4",
  BadAppID = "bad app id",
  NotFoundURL = "https://httpbin.org",
  BadURL = "badurl",
  {ok, _JSON} = t2gp_social_dna:discover_sso(GoodURL, GoodAppID),
  {error,
   {http_error,
    401,
    _,
    <<"{\"code\":10008,\"message\":\"The authorization context is "
      "not valid.\"}">>}} =
    t2gp_social_dna:discover_sso(GoodURL, BadAppID),
  {error, {http_error, 404, _, _}} = t2gp_social_dna:discover_sso(NotFoundURL, BadAppID),
  {error, nxdomain} = t2gp_social_dna:discover_sso(BadURL, BadAppID),
  ok.

test_parse_discovery(_) ->
  BadJSON = <<"badjson">>,
  GoodJSON =
    <<"[\r\n  {\r\n    \"serviceId\": \"3d839688a8654d2aa86951834363bfc0\",\r\n "
      "   \"name\": \"sso\",\r\n    \"baseUrl\": \"https://sso.api.2kcorete"
      "ch.online/sso/v2.0\",\r\n    \"tags\": [\r\n      \"public\"\r\n "
      "   ],\r\n    \"scheme\": \"https\",\r\n    \"host\": \"sso.api.2kcor"
      "etech.online\",\r\n    \"contextPath\": \"/sso/v2.0\"\r\n  "
      "}\r\n]">>,
  NoNameJSON = <<"[{\"foo\":\"bar\"}]">>,
  #{} = t2gp_social_dna:parse_discovery(BadJSON),
  #{} = t2gp_social_dna:parse_discovery(NoNameJSON),
  Result = t2gp_social_dna:parse_discovery(GoodJSON),
  Result =
    #{<<"sso">> =>
        #{<<"baseUrl">> => <<"https://sso.api.2kcoretech.online/sso/v2.0">>,
          <<"contextPath">> => <<"/sso/v2.0">>,
          <<"host">> => <<"sso.api.2kcoretech.online">>,
          <<"name">> => <<"sso">>,
          <<"scheme">> => <<"https">>,
          <<"serviceId">> => <<"3d839688a8654d2aa86951834363bfc0">>,
          <<"tags">> => [<<"public">>]}},
  ok.
