import request from 'supertest';
import { DnaAccount, config, TrustedCredentials } from './config';

/**
 * TODO:
 * 1. For POST (e.g. createGroup) and PATCH, send parameters in an object, like updateRecentlyPlayedList.
 */

/*
 * Utilities
 */

// TODO: factor with expectMore; need to modify all instances
// Test the status code of a request response. Piggy-back the response body to
// the Error object.
export function testStatus(expectedStatusCode: number, r: request.Response) {
  try {
    expect(r.status).toEqual(expectedStatusCode);
  } catch (err) {
    if (err instanceof Error) {
      err.message = `${err.message}\n\n
[response info]
x-request-id: ${r.header["x-request-id"]}
status code: ${r.status}
response body:
${JSON.stringify(r.body, null, 4)}
`;
    } else {
      console.log(`unexpected error: ${err}`);
    }
    throw err;
  }
}

//
export function expectMore(
  f: () => void,
  testCase: {
    description: string,
    expected: string
  },
  debugInfo: {
    resp?: request.Response,
    additionalInfo?: object
  }
) {
  try {
    f();
  } catch (err) {
    if (err instanceof Error) {
      // add test case info
      err.message = `${err.message}\n\n
[test case]
description:
${testCase.description}
expected:
${testCase.expected}
`;

      // add response info
      if (debugInfo.resp != undefined) {
        err.message = `${err.message}\n\n
[response info]
x-request-id: ${debugInfo.resp.header["x-request-id"]}
status code: ${debugInfo.resp.status}
response body:
${JSON.stringify(debugInfo.resp.body, null, 4)}
`;
      }

      // add additional info
      if (debugInfo.additionalInfo != undefined) {
        err.message = `${err.message}\n\n
[additional info]
${JSON.stringify(debugInfo.additionalInfo, null, 4)}
`;
      }
    } else {
      console.log(`unexpected error: ${err}`);
    }
    throw err;
  }
}

// Compose a query string for a GET API, from a list of parameters.
function makeQueryString(p: {}) {
  let queryString: string = '';
  if (Object.keys(p).length > 0) {
    queryString = '?';

    for (const [k, v] of Object.entries(p)) {
      queryString += `${k}=${encodeURIComponent(`${v}`)}&`;
    }

    // remove the trailing &
    queryString = queryString.slice(0, -1);
  }

  return queryString;
}

/**
 * Asynchronous function to retrieve trusted credentials
 * 
 * This function handles two types of authentication credentials:
 * 1. BasicAuth: Returns the credential string directly
 * 2. DTLToken: Executes the credential function to get the token
 */
async function getTrustedCredential(
  trustedCreds: TrustedCredentials
): Promise<string> {
  const { credential, credentialType } = trustedCreds.currEnv.currVer;

  if (credentialType == 'BasicAuth') {
    return credential as string;
  } else if (credentialType == 'DTLToken') {
    return await (credential as () => Promise<string>)();
  } else {
    throw new Error(`unsupported credentialType: ${credentialType}`);
  }
}

// wait while the condition is true, or until timeout (maxCnt * interval; interval in ms)
export async function waitWhile(condition: () => Promise<boolean>, maxCnt: number, interval: number) {
  let currentCnt = 0;
  while (await condition() && (currentCnt < maxCnt)) {
    await new Promise(r => setTimeout(r, interval));
    currentCnt += 1;
  }
}

// create a listener function to verify MQTT message contents
export function makeVmc(
  filter: RegExp,
  expectedPayload: object | null,
  expectedTopic: string | null,
  occurCnt: {cnt: number},
  msg?: {topic: string, payload: Buffer}
) {
  return (topic: string, payload: Buffer) => {
    let payloadStr = payload.toString();
    let actualPayload = JSON.parse(payloadStr);

    if (filter.test(payloadStr)) {
      occurCnt.cnt += 1;

      if (msg != undefined) {
        msg.topic = topic;
        msg.payload = payload;
      }

      if (expectedPayload != null) {
        expect(actualPayload).toMatchObject(expectedPayload);
      }

      if (expectedTopic != null) {
        expect(topic).toBe(expectedTopic);
      }
    }
  };
}

// get user topic given user ID
export function getUserTopic(userId: string) {
  return `dna/user/${userId}`;
}

// get group topic given group ID
export function getGroupTopic(groupId: string) {
  let productId: string;
  switch (config.socialAPIAccessLevel) {
    case 'public':
      productId = config.apps.default.productId;
      break;
    case 'trusted':
      if (config.socialAPIVersion == 'v1') {
        productId = config.apps.default.productId;
      } else if (config.socialAPIVersion == 'v2') {
        productId = config.apps.socialServiceProduct?.productId;
      } else {
        throw new Error(`Unknown API version ${config.socialAPIVersion}`);
      }
      break;
    default:
      throw new Error(`Unknown social API access level ${config.socialAPIAccessLevel}`);
  }

  return `dna/prod/${productId}/${config.socialServiceEnvironment}/group/${groupId}`;
}

// get presence topic given user ID
export function getUserPresenceTopic(userId: string) {
  return `dna/prod/${config.apps.default.productId}/${config.socialServiceEnvironment}/user/${userId}/presence`
}

// mask email address
export function maskEmailAddr(em: string) {
  let maskedEm: string;

  const regex = new RegExp('.{4}@.{4}');
  let match = regex.exec(em);
  if (match != null) {
    maskedEm = match[0];
  } else {
    // if match fails, make a wild guess.
    maskedEm = em.substring(25, 32);
  }

  return maskedEm
}

// separator for describe blocks
export const describeSep: string = "//";

///////////////////////////////////////////////////////////////////////////////

/*
 * Profile
 */

export async function getProfile(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get('/user/profile')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);
  return resp;
}

// TODO: POST /user/profile/sync

export async function getRecentlyPlayedList(
  user: DnaAccount,
  p: { limit?: number, next?: string },
  appId = config.apps.default.appId
) {
  let queryString = makeQueryString(p);

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/user/played${queryString}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

export async function updateRecentlyPlayedListV1(
  user: DnaAccount,
  rb: {
    expiresIn?: number,
    users?: {
      userid?: string,
      weight?: number,
      context?: {[key: string]: any}
    }[]
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/played')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

// v2-update recently played list
export async function updateRecentlyPlayedList(
  user: DnaAccount,
  rb: {
    ttl?: number,
    users?: {
      userid?: string,
      weight?: number,
      context?: {[key: string]: any}
    }[]
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/played')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

export async function deleteRecentlyPlayed(
  user: DnaAccount,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete('/user/played')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

///////////////////////////////////////////////////////////////////////////////

/*
 * Friends
 */

export async function getFriends(
  user: DnaAccount,
  p: { status?: string, priority?: number, productid?: string, limit?: number, next?: string },
  appId = config.apps.default.appId
) {
  let queryString = makeQueryString(p);

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/friends${queryString}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

export async function getFriend(
  user: DnaAccount,
  friendId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/friends/${friendId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

export async function makeFriendsV1(
  user: DnaAccount,
  rb: {
    userid?: string,
    message?: string,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/friends')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);
  return resp;
}

// v2-make friend
export async function makeFriends(
  user: DnaAccount,
  userid: string,
  rb: {
    message?: string,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(`/friends/${userid}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);
  return resp;
}

export async function searchFriendV1(
  user: DnaAccount,
  p: { q?: string, platform?: string, type?: string },
  appId = config.apps.default.appId
) {
  let queryString = makeQueryString(p);

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/friends/search${queryString}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// TODO: GET /friends/import

// TODO: PATCH /friends/{friendID}

export async function deleteFriend(user: DnaAccount, friendId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/friends/${friendId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

///////////////////////////////////////////////////////////////////////////////

/*
 * Groups
 */

// ---  /groups  --------------------------------------------------------------

//
export async function createGroupV1(
  user: DnaAccount,
  groupRequest: {
    maxMembers?: number,
    joinRequestAction?: string,
    password?: string,
    canMembersInvite?: boolean,
    canCrossPlay?: boolean,
    meta?: {[key: string]: any},
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  let rb = config.socialAPIAccessLevel == 'trusted' ? {
    ...{ groupLeader: user.publicId, onlineServiceType: 3 }, groupRequest: { ...groupRequest }
  } : {
    ...groupRequest
  };

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/groups')
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        'Basic ' + config.trustedCredentials.currEnv.currVer.credential :
        'Bearer ' + user.accessTokenDict[appId]
    })
    .send(rb);

  return resp;
}

// v2-create group
export async function createGroup(
  user: DnaAccount,
  rb: {
    maxMembers?: number,
    joinRequestAction?: string,
    password?: string,
    canMembersInvite?: boolean,
    canCrossPlay?: boolean,
    meta?: {[key: string]: any},
    groupLeader?: string,
    onlineServiceType?: number,
    groupMember?: {
      memberid: string,
      canCrossPlay: boolean,
      onlineServiceType: number
    }[],
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  let token: string;

  if (config.socialAPIAccessLevel == 'trusted') {
    if (rb.groupLeader == undefined) rb = { ...rb, groupLeader: user.publicId };
    if (rb.onlineServiceType == undefined) rb = { ...rb, onlineServiceType: 3 };

    token = await getTrustedCredential(config.trustedCredentials);
  } else {
    token = user.accessTokenDict[appId];
  }

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/groups')
    .set({ Authorization: 'Bearer ' + token })
    .send(rb);

  return resp;
}

// get the group ID from the response of createGroup
export function getGroupId(respCreateGroup: request.Response) {
  return respCreateGroup.body.groupid;
}

export async function getGroupsInfo(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get('/groups')
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// ---  /groups/{groupid}  ----------------------------------------------------

export async function getGroupInfo(user: DnaAccount, groupId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/groups/${groupId}`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        (config.socialAPIVersion == 'v1' ? 'Basic ' : 'Bearer ') + await getTrustedCredential(config.trustedCredentials) :
        'Bearer ' + user.accessTokenDict[appId]
    });
  return resp;
}

export async function updateGroup(
  user: DnaAccount,
  groupId: string,
  rb: {
    maxMembers?: number,
    meta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch(`/groups/${groupId}`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        (config.socialAPIVersion == 'v1' ? 'Basic ' : 'Bearer ') + await getTrustedCredential(config.trustedCredentials) :
        'Bearer ' + user.accessTokenDict[appId]
    })
    .send(rb);
  return resp;
}

export async function deleteGroup(user: DnaAccount, groupId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/groups/${groupId}`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        (config.socialAPIVersion == 'v1' ? 'Basic ' : 'Bearer ') + await getTrustedCredential(config.trustedCredentials) :
        'Bearer ' + user.accessTokenDict[appId]
    });
  return resp;
}

export async function sendControlMessage(
  user: DnaAccount,
  groupId: string,
  rb: {
    payload?: string,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(`/groups/${groupId}/control`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        (config.socialAPIVersion == 'v1' ? 'Basic ' : 'Bearer ') + await getTrustedCredential(config.trustedCredentials) :
        'Bearer ' + user.accessTokenDict[appId]
    })
    .send(rb);
  return resp;
}

// ---  /user/invites  --------------------------------------------------------

export async function getInviteV1(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get('/user/invites')
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// v2-get invite
export async function getInvite(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get('/memberships/invites/me')
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// ---  /user/invites/{groupid}  ----------------------------------------------

export async function acceptInviteV1(
  user: DnaAccount,
  groupId: string,
  rb: {
    memberid?: string,
    status?: string,
    canCrossPlay?: boolean,
    isFirstPartyInvite?: boolean,
    approverid?: string,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  rb.status = rb.status || 'joined';

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch(`/user/invites/${groupId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] })
    .send(rb);
  return resp;
}

// v2-accept invite
export async function acceptInvite(
  user: DnaAccount,
  groupId: string,
  approverid: string,
  rb: {
    canCrossPlay?: boolean,
    isFirstPartyInvite?: boolean,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch(`/memberships/invites/groups/${groupId}/approvers/${approverid}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] })
    .send(rb);
  return resp;
}

export async function declineInviteV1(user: DnaAccount, groupId: string, approverId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/user/invites/${groupId}/${approverId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// v2-decline invite
export async function declineInvite(
  user: DnaAccount,
  groupId: string,
  approverId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/memberships/invites/groups/${groupId}/approvers/${approverId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// ---  /groups/{groupid}/memberships  ----------------------------------------

export async function inviteV1(
  user: DnaAccount,
  groupId: string,
  groupMembershipRequest: {
    memberid?: string,
    isFirstPartyInvite?: boolean,
    status?: string,
    password?: string,
    expiresIn?: number,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  let rb = config.socialAPIAccessLevel == 'trusted' ? {
    ...{ userId: user.publicId, onlineServiceType: 3 }, groupMembershipRequest: { ...groupMembershipRequest }
  } : {
    ...groupMembershipRequest
  };

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(config.socialAPIAccessLevel == 'trusted' ? `/groups/${groupId}/memberships` : `/groups/${groupId}/memberships/invite`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        'Basic ' + config.trustedCredentials.currEnv.currVer.credential :
        'Bearer ' + user.accessTokenDict[appId]
    })
    .send(rb);

  return resp;
}

// v2-invite
export async function invite(
  user: DnaAccount,
  groupId: string,
  rb: {
    isFirstPartyInvite?: boolean,
    ttl?: number,
    teleMeta?: {[key: string]: any},
    members?: {
      memberid?: string,
      isFirstPartyInvite?: boolean
    }[],
    inviterid?: string,
    onlineServiceType?: number
  },
  memberid?: string,
  appId = config.apps.default.appId
) {
  let token: string;

  if (config.socialAPIAccessLevel == 'trusted') {
    if (rb.members == undefined) rb = { ...rb, members: [{ memberid: memberid }] };
    if (rb.inviterid == undefined) rb = { ...rb, inviterid: user.publicId };
    if (rb.onlineServiceType == undefined) rb = { ...rb, onlineServiceType: 3 };

    token = await getTrustedCredential(config.trustedCredentials);
  } else {
    token = user.accessTokenDict[appId];
  }

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(config.socialAPIAccessLevel == 'trusted' ? `/memberships/invites/groups/${groupId}` : `/memberships/invites/groups/${groupId}/members/${memberid}`)
    .set({ Authorization: 'Bearer ' + token })
    .send(rb);

  return resp;
}

export async function requestToJoinV1(
  user: DnaAccount,
  groupId: string,
  groupMembershipRequest: {
    memberid?: string,
    password?: string,
    status?: string,
    canCrossPlay?: boolean,
    isFirstPartyInvite?: boolean,
    expiresIn?: number,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  let rb = config.socialAPIAccessLevel == 'trusted' ? {
    ...{ userId: user.publicId, onlineServiceType: 3 }, groupMembershipRequest: { ...groupMembershipRequest }
  } : {
    ...groupMembershipRequest
  };

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(config.socialAPIAccessLevel == 'trusted' ? `/groups/${groupId}/memberships` : `/groups/${groupId}/memberships/request`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        'Basic ' + config.trustedCredentials.currEnv.currVer.credential :
        'Bearer ' + user.accessTokenDict[appId]
    })
    .send(rb);

  return resp;
}

// v2-request to join
export async function requestToJoin(
  user: DnaAccount,
  groupId: string,
  rb: {
    password?: string,
    canCrossPlay?: boolean,
    teleMeta?: {[key: string]: any},
    members?: {
      memberid: string,
      canCrossPlay: boolean,
      onlineServiceType: number
    }[]
  },
  approverId = 'leader', // v2 feature
  appId = config.apps.default.appId
) {
  let token: string;

  if (config.socialAPIAccessLevel == 'trusted') {
    if (rb.members == undefined) rb = { ...rb, members: [{ memberid: user.publicId, onlineServiceType: 3, canCrossPlay: true }] };

    token = await getTrustedCredential(config.trustedCredentials);
  } else {
    token = user.accessTokenDict[appId];
  }
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(config.socialAPIAccessLevel == 'trusted' ? `/memberships/requests/groups/${groupId}` : `/memberships/requests/groups/${groupId}/approvers/${approverId}`)
    .set({ Authorization: 'Bearer ' + token })
    .send(rb);

  return resp;
}

// ---  /groups/{groupid}/memberships/{memberid}  -----------------------------

export async function approveRequestV1(
  user: DnaAccount,
  groupId: string,
  memberId: string,
  rb: {
    isFirstPartyInvite?: boolean,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch(`/groups/${groupId}/memberships/request/${memberId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] })
    .send(rb);

  return resp;
}

// v2-approve request
export async function approveRequest(
  user: DnaAccount,
  groupId: string,
  memberId: string,
  rb: {
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch(`/memberships/requests/groups/${groupId}/members/${memberId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] })
    .send(rb);

  return resp;
}

export async function rejectRequestV1(
  user: DnaAccount,
  groupId: string,
  memberId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/groups/${groupId}/memberships/request/${memberId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });

  return resp;
}

// v2-reject request
export async function rejectRequest(
  user: DnaAccount,
  groupId: string,
  memberId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/memberships/requests/groups/${groupId}/members/${memberId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });

  return resp;
}

export async function revokeInviteV1(
  user: DnaAccount,
  groupId: string,
  memberId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/groups/${groupId}/memberships/invite/${memberId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });

  return resp;
}

// v2-revoke invite
export async function revokeInvite(
  user: DnaAccount,
  groupId: string,
  memberId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/memberships/invites/groups/${groupId}/members/${memberId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });

  return resp;
}

// ---  /groups/{groupid}/members  --------------------------------------------

export async function getMembers(user: DnaAccount, groupId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/groups/${groupId}/members`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);
  return resp;
}

// ---  /groups/{groupid}/members/{userid}  -----------------------------------

export async function getGroupMember(
  user: DnaAccount,
  userId: string,
  groupId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/groups/${groupId}/members/${userId}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);
  return resp;
}

export async function updateGroupMember(
  user: DnaAccount,
  userId: string,
  groupId: string,
  rb: {
    role?: string,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch(`/groups/${groupId}/members/${userId}`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        (config.socialAPIVersion == 'v1' ? 'Basic ' : 'Bearer ') + await getTrustedCredential(config.trustedCredentials) :
        'Bearer ' + user.accessTokenDict[appId]
    })
    .send(rb);
  return resp;
}

//
export async function kickGroupMember(
  user: DnaAccount,
  groupId: string,
  userId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/groups/${groupId}/members/${userId}`)
    .set({
      Authorization: config.socialAPIAccessLevel == 'trusted' ?
        (config.socialAPIVersion == 'v1' ? 'Basic ' : 'Bearer ') + await getTrustedCredential(config.trustedCredentials) :
        'Bearer ' + user.accessTokenDict[appId]
    })
  return resp;
}

// ---  /groups/{groupid}/members/me  -----------------------------------------

//
export async function leaveGroup(user: DnaAccount, groupId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/groups/${groupId}/members/me`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

// ---  /groups/{groupid}/control  --------------------------------------------
// TODO: /groups/{groupid}/control

///////////////////////////////////////////////////////////////////////////////

/*
 * Presence
 */

export async function getPresence(
  user: DnaAccount,
  p: { priority?: number, productid?: string },
  appId = config.apps.default.appId
) {
  let queryString = makeQueryString(p);

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/user/presence${queryString}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

export async function setPresenceV1(
  user: DnaAccount,
  rb: {
    status?: string,
    customStatus?: string,
    richPresence?: string,
    gameData?: string,
    gameName?: string,
    keepAliveFor?: number,
    meta?: {[key: string]: any},
    joinContext?: {
      sessionid?: string,
      launchGameArgs?: string
    }
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/presence')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

// v2-set presence
export async function setPresence(
  user: DnaAccount,
  rb: {
    status?: string,
    customStatus?: string,
    richPresence?: string,
    gameData?: string,
    gameName?: string,
    ttl?: number,
    meta?: {[key: string]: any},
    joinContext?: {
      sessionid?: string,
      launchGameArgs?: string
    }
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/presence')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

export async function setActiveGroupV1(
  user: DnaAccount,
  rb: {
    activeGroupid?: string
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .patch('/user/presence')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

// v2-set active group
export async function setActiveGroup(
  user: DnaAccount,
  rb: {
    activeGroupid?: string
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/presence/active')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

export async function clearPresence(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete('/user/presence')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

export async function heartbeatPresence(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/presence/heartbeat')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

///////////////////////////////////////////////////////////////////////////////

/*
 * Abuse
 */

export async function reportUser(user: DnaAccount, userId: string, report: object, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(`/user/${userId}/report`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(report);

  return resp;
}

export async function getBlockList(
  user: DnaAccount,
  p: { limit?: number, next?: string },
  appId = config.apps.default.appId
) {
  let queryString = makeQueryString(p);

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/user/blocklist${queryString}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

export async function updateBlockListV1(
  user: DnaAccount,
  rb: {
    add?: string[],
    remove?: string[],
    isFirstParty?: boolean,
    teleMeta?: {[key: string]: any}
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/blocklist')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

// v2-update block list
export async function updateBlockList(
  user: DnaAccount,
  rb: {
    userids?: string[],
    teleMeta?: {[key: string]: any},
    isFirstParty?: boolean
  },
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/user/blocklist')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send(rb);

  return resp;
}

export async function delBlockList(
  user: DnaAccount,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete('/user/blocklist')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

// v2 remove user from blocklist
export async function removeUserFromBlocklist(
  user: DnaAccount,
  blockeeId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
  .delete(`/user/blocklist/${blockeeId}`)
  .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

///////////////////////////////////////////////////////////////////////////////

/*
 * Discovery
 */

export async function getDiscovery() {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get('/discovery');

  return resp;
}

///////////////////////////////////////////////////////////////////////////////

/*
 * Chat
 */

export async function createRoom(user: DnaAccount, maxMembers: number = 6, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post('/chat/rooms')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send({
      maxMembers: maxMembers,
      public: true,
      password: config.roomPassword,
    });

  return resp;
}

export async function getRoomMembers(user: DnaAccount, roomId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/chat/rooms/${roomId}/members`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

export async function joinRoom(user: DnaAccount, roomId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(`/chat/rooms/${roomId}/members`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send({
      password: config.roomPassword,
    });

  return resp;
}
export async function deleteRoom(
  user: DnaAccount,
  roomId: string,
  userId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .delete(`/chat/rooms/${roomId}/members/${userId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

export async function getRoom(user: DnaAccount, roomId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/chat/rooms/${roomId}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

export async function sendRoomMessage(
  user: DnaAccount,
  roomId: string,
  message: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(`/chat/rooms/${roomId}/messages`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send({
      message: message,
      type: 'group',
    });

  return resp;
}

export async function getRoomMessage(user: DnaAccount, roomId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/chat/rooms/${roomId}/messages`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId])
    .send({ maxMembers: 7 });

  return resp;
}
export async function getMemberInformation(
  user: DnaAccount,
  roomId: string,
  userId: string,
  appId = config.apps.default.appId
) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/chat/rooms/${roomId}/members/${userId}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}

export async function sendMessage(user: DnaAccount, userId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .post(`/chat/dms/${userId}`)
    .set({
      Authorization: 'Bearer ' + user.accessTokenDict[appId],
      'Content-Type': 'application/json',
    })
    .send({
      message: 'Hello there!',
    });
  return resp;
}

export async function getMessage(user: DnaAccount, userId: string, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/chat/dms/${userId}`)
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

export async function getRooms(user: DnaAccount, appId = config.apps.default.appId) {
  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get('/chat/rooms')
    .set('Authorization', 'Bearer ' + user.accessTokenDict[appId]);

  return resp;
}

///////////////////////////////////////////////////////////////////////////////

/*
 * Search
 */

export async function search2Kusers(
  user: DnaAccount,
  p: { displayName?: string },
  appId = config.apps.default.appId
) {
  let queryString = makeQueryString(p);

  const resp: request.Response = await request(config.socialService.currAccessLevel.currEnv.currVer.api)
    .get(`/search/2KUsers${queryString}`)
    .set({ Authorization: 'Bearer ' + user.accessTokenDict[appId] });
  return resp;
}
///////////////////////////////////////////////////////////////////////////////