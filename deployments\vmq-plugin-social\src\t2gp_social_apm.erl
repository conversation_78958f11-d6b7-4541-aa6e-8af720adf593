-module(t2gp_social_apm).

-include_lib("opencensus/include/opencensus.hrl").
-include_lib("t2gp_social.hrl").

-export([
    init/0,
    span/3,
    span_start/2,
    span_finish/0,
    tags/1,
    on_reload/0,
    http_client/3,
    update_lager_meta/1,
    stack_trace/0
]).

-behaviour(oc_reporter).
% -behaviour(oc_stat_exporter).

%% OC report exports
-export([
    init/1,
    report/2
]).

%% OC stats exports
-export([export/2]).

%% for tests
-ifdef(TEST).
-export([
    group/1,
    to_meta/1, to_meta/2,
    to_tag/1, to_tag/2,
    span_field/2,
    build_packet/1,
    build_tags/1,
    to_key/1,
    sanitize/1,
    build_rows/5,
    build_row/3,
    tags_append/2,
    format_num/1
]).
-endif.

-define(TRACER_VERSION, "OC/0.2.0").

-define(DEFAULT_HOST, "localhost").
-define(DEFAULT_PORT, 8126).
-define(DEFAULT_STATS_HOST, "localhost").
-define(DEFAULT_STATS_PORT, 8125).
-define(DEFAULT_SERVICE, <<"social-service-mqtt">>).
-define(DEFAULT_TYPE, <<"custom">>).
-define(DEFAULT_NAME, <<"vmq.t2gp">>).

-spec init() -> ok.
init() ->
    case os:getenv("DD_AGENT_HOST") of
        false ->
            lager:info("no DD_AGENT_HOST"),
            ok;
        Host ->
            lager:info("starting t2gp_social_apm with DD_AGENT_HOST=~p", [Host]),
            application:set_env(opencensus, reporters, [
                {t2gp_social_apm, [
                        {host, Host},
                        {service, <<"social-service-mqtt">>},
                        {type, <<"mqtt">>},
                        {http_client, fun http_client/3}
                    ]}
                % {oc_reporter_stdout, []}
            ]),
            Exporters = [{t2gp_social_apm, [{host, Host}]}],
            application:set_env(opencensus, stat, [
                [{exporters, Exporters}]
            ])
    end,
    %% restart opencensus to pick up new configration
    application:stop(opencensus),
    application:ensure_all_started(opencensus),
    ok.

-spec on_reload() -> ok.
on_reload() ->
    lager:info("reloading t2gp_social_apm", []),
    init().

-spec update_lager_meta(opencensus:span_ctx()) -> ok.
update_lager_meta(SpanCtx) ->
    % span conversion doc - https://docs.datadoghq.com/tracing/connect_logs_and_traces/opentelemetry/
    % oc_datadog uses 32bits instead of 64bits https://github.com/opencensus-beam/opencensus_datadog/blob/master/src/oc_reporter_datadog.erl#L117
    lager:md([{'dd.span_id', SpanCtx#span_ctx.span_id}, {'dd.trace_id', SpanCtx#span_ctx.trace_id rem 16#ffffffff}]).

-spec span(unicode:unicode_binary(), map() | unicode:unicode_binary(), fun()) -> maybe(opencensus:span_ctx()).
span(Name, Resource, Fun) when is_binary(Name) ->
    case os:getenv("DD_AGENT_HOST") of
        false ->
            Fun();
        _ ->
            CurrentSpanCtx = ocp:current_span_ctx(),
            Attributes2 = maps:put(env, utils:get_env(), #{name => Name}),
            NewSpanCtx = oc_trace:start_span(Resource, CurrentSpanCtx, #{
                attributes => Attributes2,
                kind => ?SPAN_KIND_SERVER
            }),
            update_lager_meta(NewSpanCtx),
            ocp:with_span_ctx(NewSpanCtx),
            try Fun()
            after
                oc_trace:finish_span(ocp:current_span_ctx()),
                ocp:with_span_ctx(CurrentSpanCtx)
            end
    end.

-spec span_start(binary(), binary()) -> maybe(opencensus:span_ctx()).
span_start(Name, Resource) ->
    case os:getenv("DD_AGENT_HOST") of
        false -> undefined;
        _ ->
            PrevSpanCtx = ocp:with_child_span(Resource, #{name => Name}),
            update_lager_meta(ocp:current_span_ctx()),
            PrevSpanCtx
    end.

-spec span_finish() -> boolean().
span_finish() ->
    case os:getenv("DD_AGENT_HOST") of
        false -> true;
        _ -> ocp:finish_span()
    end.


-spec tags(#{atom() => opencensus:attribute_value()}) ->  boolean() | {error, invalid_attribute}.
tags(Tags) ->
    maps:map(fun (Key, Value) -> ocp:put_attribute(atom_to_binary(Key), Value) end, Tags).

-spec http_client(string(), [string()], binary()) -> ok |{error, {http_error, integer(), binary()}} | {error, any()}.
http_client(Address, Headers, JSON) ->
    case Address of
        "" -> ok;
        _ ->
            % lager:info("~s: request ~s ~s", [?curr_fname(), Address, JSON]),
            case hackney:request(put, Address, Headers, JSON, [with_body]) of
                {ok,Code, _, _} when Code >= 200, Code =< 299 ->
                    ok;
                {ok,Code, Header, Body} ->
                    lager:error("~s: response ~p ~p ~p", [?curr_fname(), Code, Body, JSON]),
                    {error, {http_error, Code, Header, Body}};
                {error, Reason} ->
                    lager:error("~s: error ~p", [?curr_fname(), Reason]),
                    {error, Reason}
            end
    end.

-spec stack_trace() -> [tuple()].
stack_trace() ->
    try erlang:error("throw")
    catch _:_:Stack ->
        {_, R} = lists:split(2,Stack),
        R
    end.

%% oc_reporter implementation
-spec init(term()) -> oc_reporter:opts().
init(Options) ->
    Host = proplists:get_value(host, Options, ?DEFAULT_HOST),
    Port = proplists:get_value(port, Options, ?DEFAULT_PORT),
    Service = proplists:get_value(service, Options, ?DEFAULT_SERVICE),
    Type = proplists:get_value(type, Options, ?DEFAULT_TYPE),
    Client = proplists:get_value(http_client, Options, fun http_client/3),
    #{
        host => Host,
        port => Port,
        service => Service,
        type => Type,
        client => Client
    }.

-spec report(nonempty_list(opencensus:span()), oc_reporter:opts()) -> ok.
report(Spans, #{
        service := Service,
        host := Host,
        port := Port,
        type := Type,
        client := Client}) ->
    Sorted = lists:sort(fun(A, B) ->
                                A#span.trace_id =< B#span.trace_id
                        end, Spans),
    Grouped = group(Sorted),
    DSpans = [[build_span(S, Service, Type) || S <- Trace] || Trace <- Grouped],

    try jsx:encode(DSpans) of
        JSON ->
            Address = io_lib:format('http://~s:~B/v0.3/traces', [Host, Port]),
            Headers = [
                {"Datadog-Meta-Lang", "erlang"},
                {"Datadog-Meta-Lang-Version", lang_version()},
                {"Datadog-Meta-Lang-Interpreter", interpreter_version()},
                {"Datadog-Meta-Tracer-Version", ?TRACER_VERSION}
            ],
            Client(Address, Headers, JSON),
            ok
    catch
        error:Error ->
            lager:error("DD: Can't spans encode to json: ~p", [Error])
    end.

-spec lang_version() -> string().
lang_version() ->
    erlang:system_info(otp_release).

-spec interpreter_version() -> string().
interpreter_version() ->
    io_lib:format('~s-~s', [erlang:system_info(version),
                            erlang:system_info(system_architecture)]).

-spec group([opencensus:span()]) -> [opencensus:span()].
group([]) -> [];
group([First | Spans]) -> group(Spans, [[First]]).

-spec group([opencensus:span()], [opencensus:span()]) -> [opencensus:span()].
group([], Acc) -> Acc;
group([#span{trace_id = Id} = Span | Spans],
        [[#span{trace_id = Id} | _] = Curr | Acc]) ->
    group(Spans, [[Span | Curr] | Acc]);
group([Span | Spans], Acc) -> group(Spans, [[Span] | Acc]).

-spec build_span(opencensus:span(), binary(), binary()) -> map().
build_span(Span, Service, Type) ->
    (optional_fields(Span))#{
        <<"trace_id">> => Span#span.trace_id rem 16#ffffffff,
        <<"span_id">> => Span#span.span_id,
        <<"name">> => maps:get(name, Span#span.attributes, ?DEFAULT_NAME),
        <<"resource">> => Span#span.name,
        <<"service">> => to_tag(Service),
        <<"start">> => wts:to_absolute(Span#span.start_time) * 1000,
        <<"duration">> => wts:duration(Span#span.start_time, Span#span.end_time) * 1000,
        <<"type">> => to_tag(Type),
        <<"meta">> => to_meta(Span#span.attributes)}.

%% remove name from attributes
-spec to_meta(map()) -> map().
to_meta(Attributes) -> maps:map(fun to_meta/2, maps:without([name], Attributes)).

-spec to_meta(atom(), any()) -> any().
to_meta(_Name, Value) when is_integer(Value) -> integer_to_binary(Value);
to_meta(_Name, Value) when is_float(Value) ->
    float_to_binary(Value, [compact, {decimals, 253}]);
to_meta(Name, true) -> to_tag(Name, <<"true">>);
to_meta(Name, false) -> to_tag(Name, <<"false">>);
to_meta(Name, Value) -> to_tag(Name, Value).

-spec to_tag(any()) -> binary().
to_tag(Value) -> to_tag(nil, Value).

-spec to_tag(any(), any()) -> binary().
to_tag(_Name, Value) when is_function(Value) -> Value();
to_tag(_Name, Value) when is_list(Value) -> list_to_binary(Value);
to_tag(_Name, Value) -> Value.

-spec optional_fields(opencensus:span()) -> map().
optional_fields(Span) ->
    lists:foldl(fun(Field, Acc) ->
        case span_field(Field, Span) of
            undefined -> Acc;
            Value -> maps:put(Field, Value, Acc)
        end
    end, #{}, [<<"error">>, <<"parent_id">>]).

-spec span_field(binary(), opencensus:span()) -> undefined | integer().
span_field(<<"error">>, #span{status = undefined}) -> undefined;
span_field(<<"error">>, #span{status = #status{code = 0}}) -> undefined;
span_field(<<"error">>, _) -> 1;
span_field(<<"parent_id">>, #span{parent_span_id = ParentId}) -> ParentId.

%% oc_stats_exporter implementation
-spec export(any(), any()) -> ok.
export(ViewData, Options) ->
    Host = proplists:get_value(host, Options, ?DEFAULT_STATS_HOST),
    Port = proplists:get_value(port, Options, ?DEFAULT_STATS_PORT),
    {ok, Socket} = gen_udp:open(0, [{active, false}]),
    Packet = build_packet(ViewData),
    ok = gen_udp:send(Socket, Host, Port, Packet),
    ok = gen_udp:close(Socket),
    ok.

-spec build_packet(any()) -> any().
build_packet(Data) when is_list(Data) ->
    List = [build_packet_item(Entry) || Entry <- Data],
    lists:join($\n, List).

-spec build_packet_item(map()) -> any().
build_packet_item(#{name := Name,
        ctags := CTags,
        tags := Tags,
        data := #{type := Type,
        rows := Rows}}) ->
    Key = to_key(Name),
    List = [build_rows(Key, Type, CTags, Tags, Row) || Row <- Rows],
    lists:join($\n, List).

-spec build_tags(list(), list(), map()) -> list().
build_tags(Tags, TagsV, CTags) ->
    TagsMap = maps:merge(CTags, maps:from_list(lists:zip(Tags, TagsV))),
    TagsList = maps:to_list(TagsMap),
    Cleaned = [{Key, Value} || {Key, Value} <- TagsList, Value =/= undefined],
    build_tags(Cleaned).

-spec build_tags(list()) -> list().
build_tags([]) -> [];
build_tags(Tags) ->
    List = [[to_key(Key), $:, Value]
        || {Key, Value} <- Tags],
    ["|#", lists:join($,, List)].

-spec to_key(list()) -> string().
to_key(Atom) when is_atom(Atom) ->
    sanitize(erlang:atom_to_list(Atom));
to_key(Value) -> sanitize(Value).

-spec sanitize(string()) -> string().
sanitize(Name) ->
    re:replace(Name, "[^[:alpha:][:digit:]_]+", ".", [global]).

-spec build_rows(string(), atom(), map(), list(), map()) -> list().
build_rows(Name, Type, CTags, Tags, #{tags := TagsV, value := Value}) ->
    TagList = build_tags(Tags, TagsV, CTags),
    Metrics = [[Name, Stat] || Stat <- build_row(Type, Value, TagList) ],
    lists:join($\n, Metrics).

-spec build_row(atom(), map(), list()) -> list().
build_row(sum, Data, Tags) ->
    build_row_sum(sum, Data, Tags);
build_row(distribution, #{buckets := Buckets} = Data, Tags) ->
    BucketRows = [ bucket_row(Bucket, Tags) || Bucket <- Buckets ],
    build_row_sum(sum, Data, Tags) ++ BucketRows;
build_row(_Type, Value, Tags) ->
    [[$:, format_num(Value), "|g", Tags]].

-spec build_row_sum(atom(), map(), list()) -> list().
build_row_sum(sum, #{count := Count, mean := Mean, sum := Sum}, Tags) ->
    [
        [".count:", format_num(Count), "|g", Tags],
        [".mean:", format_num(Mean), "|g", Tags],
        [".sum:", format_num(Sum), "|g", Tags]
    ].

-spec bucket_row({integer(), integer()}, list()) -> list().
bucket_row({Bound, Count}, Tags) when is_integer(Count) ->
    [
        $:, format_num(Count),
        "|g",
        tags_append(Tags, ["le:", format_num(Bound)])
    ].

-spec tags_append(list(), any()) -> list().
tags_append([], Value) -> ["|#", Value];
tags_append(List, Value) -> [List, $,, Value].

-spec format_num(any()) -> binary().
format_num(infinity) -> <<"infinity">>;
format_num(Integer) when is_integer(Integer) ->
    erlang:integer_to_binary(Integer);
format_num(Float) when is_float(Float) ->
    erlang:float_to_binary(Float, [{decimals, 5}, compact]).
