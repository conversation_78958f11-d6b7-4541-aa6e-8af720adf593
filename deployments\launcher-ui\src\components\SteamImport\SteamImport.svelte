<script lang="ts">
  import { onDestroy, onMount } from 'svelte';
  import { EVENT_STEAM_ACCOUNT_LINKED } from '../../constant';
  import {
    useLinkedSteamId,
    useTranslator,
    useTransportService,
  } from '../../hooks';
  import ConnectSteam from './ConnectSteam.svelte';
  import SteamFriends from './SteamFriends.svelte';

  export let steamAccountLinked = false;

  const t = useTranslator();
  const transportService = useTransportService();
  const linkedSteamId = useLinkedSteamId();

  onMount(() => {
    transportService.subscribeEvent(
      EVENT_STEAM_ACCOUNT_LINKED,
      (_, steamId: string) => {
        steamAccountLinked = true;
        if (steamId) {
          linkedSteamId.set(steamId);
        }
      }
    );
  });

  onDestroy(() => {
    transportService.unsubscribe(EVENT_STEAM_ACCOUNT_LINKED);
  });
</script>

<style>
  .steam-import {
    height: 100%;
    width: 100%;
    padding: 0 0 0 2rem;
  }
  h2 {
    margin: 0 0 1rem 0;
    color: var(--social-color, var(--default-color));
    font-weight: bold;
    font-size: 1rem;
    line-height: 125%;
    text-transform: capitalize;
  }
  .container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
  }
</style>

<div class="steam-import">
  <h2>{$t('import steam friends to 2K')}</h2>
  <div class="container">
    {#if steamAccountLinked}
      <SteamFriends />
    {:else}
      <ConnectSteam />
    {/if}
  </div>
</div>
