package cache

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
)

// SetGroup set group to redis
func (rc *RedisCache) SetGroup(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group.Created == nil || group.Created.IsZero() {
		now := time.Now().UTC()
		group.Created = &now
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObject(ctx, rc, group, group.RedisKey(tenant), ttl)
}

// GetGroup get group from redis
func (rc *RedisCache) GetGroup(ctx context.Context, productid, groupid string) (*apipub.GroupResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return getCachedObject[apipub.GroupResponse](ctx, rc, apipub.BuildGroupRedisKey(tenant, productid, groupid))
}

// DeleteGroup delete group and clean up members/requests
func (rc *RedisCache) DeleteGroup(ctx context.Context, group *apipub.GroupResponse) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := group.RedisKey(tenant)
	err := rc.DeleteCachedObj(ctx, key)
	if err == nil {
		//Delete membershipRequests before deleting user groups to prevent group nil
		if group.MembershipRequests != nil {
			for _, membership := range *group.MembershipRequests {
				//only need to delete indexes since group is being deleted.
				err2 := rc.DeleteMembership(ctx, &membership)
				if err2 != nil {
					log.Error().Err(err).Msgf("Error deleting membership index in delete group %s", group.Groupid)
				}
			}
		}
		// clear secondary indexes for members.
		if group.Members != nil {
			for _, member := range *group.Members {
				err2 := rc.DeleteUserGroupIdxs(ctx, group.Productid, group.Groupid, member.Userid)
				if err2 != nil {
					log.Error().Err(err).Msgf("Error deleting group member in delete group %s", group.Groupid)

				}
			}
		}
	}
	return err
}

// GetUserGroups get user's groups from redis
func (rc *RedisCache) GetUserGroups(ctx context.Context, userid, productid string, limit *int64, next *string) (*[]*apipub.GroupResponse, string, error) {
	//log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	user := index.NewUserSubject(tenant, productid, userid)
	if user == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	key := user.MemberOfKey()
	if key == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	idx := index.NewSecondaryIndex(*key, "")

	groups, retNext, err2 := getObjsFromSecondaryIndex[apipub.GroupResponse](ctx, rc, idx, limit, next, false)
	//if group related redis object is missing, ignore error
	if err2 != nil && errs.IsEqual(err2, errs.ERedisObjectMissing) {
		err2 = nil
	}

	//if groups != nil {
	//	for _, group := range *groups {
	//		if group != nil {
	//			ttl, err := rc.GetGroupTTL(ctx, group.Productid, group.Groupid)
	//			if err != nil {
	//				log.Error().Err(err).Msgf("failed to get group ttl: %v", err)
	//			} else {
	//				group.Ttl = aws.Int64(ttl.Milliseconds())
	//			}
	//		}
	//	}
	//}

	return groups, retNext, err2
}

// SetUserGroupIdxs set users group indexes to redis
func (rc *RedisCache) SetUserGroupIdxs(ctx context.Context, userid string, group *apipub.GroupResponse) error {
	log := logger.FromContext(ctx)
	if group == nil {
		return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	productid := group.Productid
	groupid := group.Groupid

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		log.Warn().Str("groupid", groupid).Str("event", "failed to create secondary index for usersub").Msg("failed to create secondary index for usersub")
		return errs.New(http.StatusInternalServerError, errs.ESubjectNotFound)
	}

	//Write Member Of Idx
	userMemberOfkey := userSubject.MemberOfKey()
	if userMemberOfkey == nil {
		log.Warn().Str("groupid", groupid).Str("event", "failed to create secondary index for memberof").Msg("failed to create secondary index for memberof")
	} else {
		userGroupIdx := index.NewSecondaryIndex(*userMemberOfkey, apipub.BuildGroupRedisKey(tenant, productid, groupid))

		err := rc.setSecondaryIndex(ctx, userGroupIdx)
		if err != nil {
			log.Error().Str("idxKey", userGroupIdx.IdxKey()).Str("userid", userid).Str("groupid", group.Groupid).Msg("failed to set secondary index for usergroup")
		}
	}

	//Write Members Idx
	groupSubject := index.NewGroupSubject(tenant, productid, groupid)
	if groupSubject == nil {
		log.Warn().Str("groupid", groupid).Str("event", "failed to create secondary index for groupsub").Msg("failed to create secondary index for groupsub")
	} else {
		groupMembersKey := groupSubject.GroupMembersKey()
		if groupMembersKey == nil {
			log.Warn().Str("groupid", groupid).Str("event", "failed to create secondary index for groupmembers").Msg("failed to create secondary index for groupmembers")
		} else {

			groupMembersIdx := index.NewSecondaryIndex(*groupMembersKey, apipub.BuildUserRedisKey(tenant, userid))

			err := rc.setSecondaryIndex(ctx, groupMembersIdx)
			if err != nil {
				log.Error().Str("idxKey", groupMembersIdx.IdxKey()).Str("userid", userid).Str("groupid", group.Groupid).Msg("failed to set secondary index for usergroup")
			}
		}
	}
	return nil
}

// DeleteUserGroupIdxs delete user group from redis.  Do not delete cached object since other users can reference it.
func (rc *RedisCache) DeleteUserGroupIdxs(ctx context.Context, productid, groupid, userid string) error {
	log := logger.FromContext(ctx)

	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	//productid := groupid.Productid
	//groupid := groupid.Groupid

	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		log.Warn().Str("userid", userid).Str("groupid", groupid).Str("event", "failed to create secondary index for usersub").Msg("failed to create secondary index for usersub")
	} else {
		// Delete member of index
		userMemberOfkey := userSubject.MemberOfKey()
		if userMemberOfkey == nil {
			log.Warn().Str("userid", userid).Str("groupid", groupid).Str("event", "failed to create secondary index for membersof").Msg("failed to create secondary index for membersof")
		} else {

			userGroupIdx := index.NewSecondaryIndex(*userMemberOfkey, apipub.BuildGroupRedisKey(tenant, productid, groupid))
			err2 := rc.delSecondaryIndex(ctx, userGroupIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", userGroupIdx.IdxKey()).Msg("failed to del secondary index for usergroup")
			}
		}
	}

	// Delete members index
	groupSubject := index.NewGroupSubject(tenant, productid, groupid)
	if groupSubject == nil {
		log.Warn().Str("userid", userid).Str("groupid", groupid).Str("event", "failed to create secondary index for groupsub").Msg("failed to create secondary index for groupsub")
	} else {
		groupMembersKey := groupSubject.GroupMembersKey()
		if groupMembersKey == nil {
			log.Warn().Str("userid", userid).Str("groupid", groupid).Str("event", "failed to create secondary index for groupmembers").Msg("failed to create secondary index for groupembers")
		} else {

			groupMembersIdx := index.NewSecondaryIndex(*groupMembersKey, apipub.BuildUserRedisKey(tenant, userid))

			err2 := rc.delSecondaryIndex(ctx, groupMembersIdx)
			if err2 != nil {
				log.Error().Err(err2).Str("idxKey", groupMembersIdx.IdxKey()).Msg("failed to del secondary index for groupmembers")
			}
		}
	}

	return nil
}

func (rc *RedisCache) GetGroupMembers(ctx context.Context, productid, groupid string) (*[]apipub.GroupMemberResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	groupKey := apipub.BuildGroupRedisKey(tenant, productid, groupid)

	if !rc.CachedObjExists(ctx, groupKey) {
		return nil, errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}

	idx := index.NewSecondaryIndex(groupKey, "")

	limit := int64(100)
	next := ""

	groups, _, err2 := getObjsFromSecondaryIndex[apipub.GroupResponse](ctx, rc, idx, &limit, &next, false)
	if err2 != nil {
		return nil, err2
	}

	if groups != nil && len(*groups) > 0 {
		return (*groups)[0].Members, nil
	}

	return nil, nil
}

// RemoveGroupMember removes group member from group. Boolean value indicates if the group is also disbanded.
func (rc *RedisCache) RemoveGroupMember(ctx context.Context, group *apipub.GroupResponse, memberid string) (bool, error) {
	log := logger.FromContext(ctx)
	if group == nil {
		return false, errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotFound)
	}
	if !group.IsMember(memberid) {
		return false, errs.New(http.StatusForbidden, errs.EGroupsMemberNotInGroup)
	}

	group.RemoveMember(memberid)
	//increment compositionid counter for telemetry
	compid := aws.Int64(0)
	if group.GroupCompositionId != nil {
		compid = group.GroupCompositionId
	}
	group.GroupCompositionId = aws.Int64(*compid + 1)
	rc.SetGroupCompositionId(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)

	// delete indexes before delete group
	rc.DeleteUserGroupIdxs(ctx, group.Productid, group.Groupid, memberid)

	//delete group if empty
	if group.Members != nil && len(*group.Members) == 0 {
		err := rc.DeleteGroup(ctx, group)
		if err != nil {
			log.Error().Err(err).Str("groupid", group.Groupid).Str("event", "failed to delete group in cache in RemoveGroupMember").Msg("failed to delete group in cache in RemoveGroupMember")
		}
		return true, nil
	}
	return false, rc.setGroupMembers(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)
}

// AddGroupMember adds a group  member.  Does NOT pass by reference because nil-ing out links before saving to cache.
func (rc *RedisCache) AddGroupMember(ctx context.Context, group *apipub.GroupResponse, member *apipub.GroupMemberResponse) error {

	if group == nil {
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotFound)
	}

	// don't write links or preesence to redis
	member.Links = nil
	member.Presence = nil

	group.AddMemberIfNotExist(member)

	//increment compositionid counter for telemetry
	compid := aws.Int64(0)
	if group.GroupCompositionId != nil {
		compid = group.GroupCompositionId
	}
	group.GroupCompositionId = aws.Int64(*compid + 1)
	rc.SetGroupCompositionId(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)

	rc.SetUserGroupIdxs(ctx, member.Userid, group)
	return rc.setGroupMembers(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)
}

// UpdateGroupMember updates a group member.
func (rc *RedisCache) UpdateGroupMember(ctx context.Context, group *apipub.GroupResponse, member *apipub.GroupMemberResponse) error {
	log := logger.FromContext(ctx)
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}

	if member == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsMemberNotInGroup)
	}

	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := group.RedisKey(tenant)
	ttl := time.Duration(rc.cfg.TtlGroup) * time.Second

	path := fmt.Sprintf("$.members[?(@.userid == '%s')]", member.Userid)
	err := setCachedObjectAtPath(ctx, rc, member, key, path, ttl)
	if err != nil {
		log.Error().Err(err).Str("groupid", group.Groupid).Str("memberid", member.Userid).Msg("failed to update the group in cache")
		return err
	}

	return nil
}

// CountUserGroups count groups for user per product
func (rc *RedisCache) CountUserGroups(ctx context.Context, userid, productid string) (int64, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	user := index.NewUserSubject(tenant, productid, userid)
	if user == nil {
		return 0, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	key := user.MemberOfKey()
	if key == nil {
		return 0, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	return rc.zCard(ctx, *key).Result()
}

// RemoveMembershipRequestFromGroup removes a membership request from a group in cache and writes the group.
// uses groupid instead of *group because of cache invalidation on expired membershiprequest.  uses pattern because productid not available at the time.
func (rc *RedisCache) RemoveMembershipRequestFromGroup(ctx context.Context, group *apipub.GroupResponse, membership apipub.MembershipRequest) error {
	log := logger.FromContext(ctx)
	if group == nil || group.Groupid == "" || group.Productid == "" {
		return errs.New(http.StatusUnprocessableEntity, errs.EGroupsNotFound)
	}

	if mErr := group.RemoveMembership(membership); mErr == nil {
		//only continue if the membership was sucessfully deleted from the group
		err := rc.setGroupMemberships(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)
		if err == nil {
			//delete index
			err = rc.DeleteMembership(ctx, &membership)
			if err != nil {
				log.Error().Err(err).Msgf("Unable to delete membership index for group %s", group.Groupid)
			}
			return nil
		}
	} else {
		return mErr
	}

	return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
}

// AddMembershipRequestToGroup adds a membership request to cache and adds it to group as well
func (rc *RedisCache) AddMembershipRequestToGroup(ctx context.Context, group *apipub.GroupResponse, membership apipub.MembershipRequest) error {
	log := logger.FromContext(ctx)

	err := rc.setMembership(ctx, &membership)
	if err != nil {
		log.Error().Err(err).Str("groupid", group.Groupid).Interface("membership", membership).Str("event", "Unable to add membership for group").Msg("Unable to add membership for group")
		return err
	}
	group.AddMembershipIfNotExist(&membership)
	return rc.setGroupMemberships(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)
}

// KickOrLeaveHelper helper to leave or kick group so it can be used by presence auto offline kick.  reason is passed by ref to return the reason for the kick.   returns chat event type.   and a boolean for if the group was disbanded.
func (rc *RedisCache) KickOrLeaveHelper(ctx context.Context, group *apipub.GroupResponse, requestorid, targetuserid string, reason *string) (apipub.ChatMessageEventType, bool, error) {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	if group == nil {
		return apipub.ChatMessageEventTypeUnknown, false, errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}

	productid := group.Productid
	role := group.GetMemberRole(requestorid)
	currentRole := group.GetMemberRole(targetuserid)
	//if role is not leader, or not self-kick, or not auto presence timeout kick, or Trusted Server, return error
	if !(role == apipub.Leader || requestorid == targetuserid || requestorid == rc.cfg.AppID || requestorid == constants.TrustedServer) {
		return apipub.ChatMessageEventTypeUnknown, false, errs.New(http.StatusForbidden, errs.EGroupsNotAllowedToKick)
	}

	isDisbanded, err := rc.RemoveGroupMember(ctx, group, targetuserid)
	if err != nil {
		log.Error().Err(err).Str("group", group.Groupid).Str("targetuserid", targetuserid).Msg("Error removing user from group")
	}

	// remove all existing membershiprequests for this user in this group
	groupid := group.Groupid
	rc.ClearAllMemberships(ctx, targetuserid, productid, groupid)
	group.ClearMemberships(targetuserid, "")

	reasonString := "left"
	retChatEventType := apipub.ChatMessageEventTypeLeft
	wasKicked := requestorid != targetuserid
	if wasKicked {
		reasonString = "kicked"
		if reason != nil {
			reasonString = *reason
		}
		retChatEventType = apipub.ChatMessageEventTypeKicked
	}

	// send mqtt message
	userModified := apipub.MqttGroupMemberModified{
		Action:   "left",
		Userid:   targetuserid,
		Reason:   reasonString,
		PreRole:  currentRole,
		PostRole: group.GetMemberRole(targetuserid),
		Groupid:  groupid,
	}

	groupTopic := group.Topic(tenant)
	log.Debug().Str("topic", groupTopic).Str("mqttmesagetype", string(apipub.MqttMessageTypeGroupMembersModified)).Interface("msg", userModified).Msg("kick or leave helper mqtt mesage")
	messenger.SendMqttMessage(ctx, rc.cfg, groupTopic, messenger.MqttMessageTypeGroupMembersModified, userModified)

	// unsubscribe to group
	messenger.Unsubscribe(ctx, rc.cfg, targetuserid, groupTopic)

	// Promote a team member to be the group leader.
	if !isDisbanded && currentRole == apipub.Leader {
		newLeader := group.PromoteNextMember()
		if newLeader != nil {
			userModified = apipub.MqttGroupMemberModified{
				Action:   "roleChanged",
				Userid:   newLeader.Userid,
				Reason:   "leaderLeft",
				PreRole:  apipub.Member,
				PostRole: apipub.Leader,
				Groupid:  groupid,
			}

			err = rc.setGroupMembers(ctx, group, time.Duration(rc.cfg.TtlGroup)*time.Second)
			if err != nil {
				log.Error().Err(err).Msgf("Updating Group Leader failed: %v", group)
				return apipub.ChatMessageEventTypeUnknown, isDisbanded, err
			}

			messenger.SendMqttMessage(ctx, rc.cfg, groupTopic, messenger.MqttMessageTypeGroupMembersModified, userModified)
		}
	}

	return retChatEventType, isDisbanded, nil
}

func (rc *RedisCache) GetGroupTTL(ctx context.Context, productid, groupid string) (time.Duration, error) {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := apipub.BuildGroupRedisKey(tenant, productid, groupid)

	ttl, err := rc.tTL(ctx, key).Result()
	if err != nil {
		log.Error().Err(err).Msgf("Error getting group ttl %s", groupid)
		return 0, err
	}
	//if ttl is -2 then the key was not found so return 0
	if ttl == -2 {
		return 0, errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	//if ttl is -1 then the key has no ttl but groups always should have one so set it to default
	if ttl == -1 {
		rc.expire(ctx, key, time.Duration(rc.cfg.TtlGroup)*time.Second)
	}

	return ttl, nil
}

// setGroupMembers set group Members to redis
func (rc *RedisCache) setGroupMembers(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectArrayAtPath(ctx, rc, group.Members, group.RedisKey(tenant), ".members", ttl)
}

// SetGroupMemberships set group Memberships to redis
func (rc *RedisCache) setGroupMemberships(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectArrayAtPath(ctx, rc, group.MembershipRequests, group.RedisKey(tenant), ".membershipRequests", ttl)
}

// SetGroupMeta set group Meta to redis
func (rc *RedisCache) SetGroupMeta(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectAtPath(ctx, rc, group.Meta, group.RedisKey(tenant), ".meta", ttl)
}

// SetGroupMaxMembers set group MaxMembers to redis
func (rc *RedisCache) SetGroupMaxMembers(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	if group.MaxMembers < 2 || (group.Members != nil && group.MaxMembers < len(*group.Members)) {
		return errs.New(http.StatusNotFound, errs.EGroupsInvalidMaxMembers)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectAtPath(ctx, rc, &group.MaxMembers, group.RedisKey(tenant), ".maxMembers", ttl)
}

// SetGroupPassword set group Password to redis
func (rc *RedisCache) SetGroupPassword(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectAtPath(ctx, rc, group.Password, group.RedisKey(tenant), ".password", ttl)
}

// SetGroupCanMembersInvite set group CanMembersInvite to redis
func (rc *RedisCache) SetGroupCanMembersInvite(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectAtPath(ctx, rc, group.CanMembersInvite, group.RedisKey(tenant), ".canMembersInvite", ttl)
}

// SetGroupJoinRequestAction set group JoinRequestAction to redis
func (rc *RedisCache) SetGroupJoinRequestAction(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil || group.Members == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsInvalidMaxMembers)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectAtPath(ctx, rc, &group.JoinRequestAction, group.RedisKey(tenant), ".joinRequestAction", ttl)
}

// buildMembershipJSONPath finds a membershipqurest based on
func buildMembershipJSONPath(productid, groupid, memberid, approverid string) string {
	return fmt.Sprintf(`membershipRequests[?(@.groupid=="%s"&&@.productid=="%s"&&@.memberid=="%s"&&@.approverid=="%s")]`, groupid, productid, memberid, approverid)
}

// SetGroupCompositionId set group composition id to redis
func (rc *RedisCache) SetGroupCompositionId(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error {
	if group == nil || group.Members == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsInvalidMaxMembers)
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	return setCachedObjectAtPath(ctx, rc, group.GroupCompositionId, group.RedisKey(tenant), ".groupCompositionId", ttl)
}
