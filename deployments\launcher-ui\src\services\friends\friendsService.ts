import type { IAPIService } from '../api';
import { handleListResponse, handleResponse } from '../api';

export interface Presence {
  status: string;
  timestamp: string;
  activeGroup?: string;
  gameName?: string;
  gameData?: string;
  joinContext?: string;
  richPresence?: string;
  meta?: {
    gameID: string;
    planet: string;
  };
}

export interface SocialFriend {
  avatar?: string;
  invitee: string;
  message: string;
  name: string;
  presence: [Presence];
  status: string;
  userid: string;
}

export interface PendingFriend {
  friendid: string;
  invitee: string;
  message: string;
  name: string;
  status: string;
  userid: string;
  presence: [Presence];
  viewed?: boolean;
}

export interface UserProfile {
  avatar: string;
  created: string;
  email: string;
  last_login: string;
  locale: string;
  name: string;
  presence: Presence;
  userid: string;
}

export interface SteamFriendProfile {
  SteamID: string;
  CommunityVisibilityState: number;
  ProfileURL: string;
  ProfileState: number;
  PersonaName: string;
  LastLogoff: number;
  PersonaState: number;
  avatar: string;
  avatarmedium: string;
  avatarfull: string;
}

export interface SteamFriendProfileDNA {
  firstPartyId: string;
  id: string;
  onlineServiceType: number;
  parentAccountId: string;
  type: number;
}
export interface SteamFriend {
  profileSteam: SteamFriendProfile;
  profileDNA: SteamFriendProfileDNA;
}

export interface FriendRequestResponse {
  status: string;
}
export interface IFriendsService {
  getFriendsAsync: () => Promise<SocialFriend[]>;
  getPendingFriendsAsync(): Promise<PendingFriend[]>;
  getSteamFriendsAsync(steamId: string): Promise<SteamFriend[]>;
  MakeFriendAsync(userId: string): Promise<FriendRequestResponse>;
  DeleteFriendAsync(friendId: string): Promise<FriendRequestResponse>;
  searchFriendsAsync(displayName: string): Promise<SocialFriend[]>;
  markFriendRequestViewed(friendId: string): Promise<PendingFriend>;
}

export class FriendsService implements IFriendsService {
  private apiService: IAPIService;

  constructor(apiService: IAPIService) {
    this.apiService = apiService;
  }

  async getFriendsAsync(): Promise<SocialFriend[]> {
    const response = await this.apiService.getFriendsAsync();
    return handleListResponse<SocialFriend>(response);
  }

  async getPendingFriendsAsync(): Promise<PendingFriend[]> {
    const response = await this.apiService.getPendingFriendsAsync();
    return handleListResponse<PendingFriend>(response);
  }

  async getSteamFriendsAsync(steamId: string): Promise<SteamFriend[]> {
    const response = await this.apiService.getSteamFriendsAsync(steamId);
    return handleListResponse<SteamFriend>(response);
  }

  async MakeFriendAsync(userId: string): Promise<FriendRequestResponse> {
    const response = await this.apiService.postMakeFriendAsync(userId);
    return handleResponse<FriendRequestResponse>(response);
  }

  async DeleteFriendAsync(friendId: string): Promise<FriendRequestResponse> {
    const response = await this.apiService.deleteFriendAsync(friendId);
    return handleResponse<FriendRequestResponse>(response);
  }

  async searchFriendsAsync(displayName: string): Promise<SocialFriend[]> {
    const response = await this.apiService.searchFriendsAsync(displayName);
    return handleListResponse<SocialFriend>(response);
  }

  async markFriendRequestViewed(friendId: string): Promise<PendingFriend> {
    const response = await this.apiService.patchPendingFriendAsync(friendId, {
      viewed: true,
    });
    return handleResponse<PendingFriend>(response);
  }
}
