package telemetry

//
//import (
//	"context"
//	"encoding/json"
//	"fmt"
//	"github.com/aws/aws-sdk-go-v2/service/firehose/types"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
//	"net/http"
//	"time"
//
//	"github.com/aws/aws-sdk-go-v2/aws"
//	"github.com/aws/aws-sdk-go-v2/service/firehose"
//	"github.com/aws/smithy-go/middleware"
//	zlog "github.com/rs/zerolog/log"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
//)
//
//// FirehoseObj Type constraints for Telemetry using generics
//type FirehoseObj interface {
//	GroupTelemetryMeta | FriendTelemetryMeta | BlocklistTelemetryMeta | ReportTelemetryMeta | GenericTelemetryMeta
//}
//
//var _ health.DependentService = &FirehoseWithMonitoring{}
//
//type FirehoseInterface interface {
//	CreateDeliveryStream(ctx context.Context, params *firehose.CreateDeliveryStreamInput, optFns ...func(*firehose.Options)) (*firehose.CreateDeliveryStreamOutput, error)
//	DeleteDeliveryStream(ctx context.Context, params *firehose.DeleteDeliveryStreamInput, optFns ...func(*firehose.Options)) (*firehose.DeleteDeliveryStreamOutput, error)
//	DescribeDeliveryStream(ctx context.Context, params *firehose.DescribeDeliveryStreamInput, optFns ...func(*firehose.Options)) (*firehose.DescribeDeliveryStreamOutput, error)
//	ListDeliveryStreams(ctx context.Context, params *firehose.ListDeliveryStreamsInput, optFns ...func(*firehose.Options)) (*firehose.ListDeliveryStreamsOutput, error)
//	ListTagsForDeliveryStream(ctx context.Context, params *firehose.ListTagsForDeliveryStreamInput, optFns ...func(*firehose.Options)) (*firehose.ListTagsForDeliveryStreamOutput, error)
//	PutRecord(ctx context.Context, params *firehose.PutRecordInput, optFns ...func(*firehose.Options)) (*firehose.PutRecordOutput, error)
//	PutRecordBatch(ctx context.Context, params *firehose.PutRecordBatchInput, optFns ...func(*firehose.Options)) (*firehose.PutRecordBatchOutput, error)
//	StartDeliveryStreamEncryption(ctx context.Context, params *firehose.StartDeliveryStreamEncryptionInput, optFns ...func(*firehose.Options)) (*firehose.StartDeliveryStreamEncryptionOutput, error)
//	StopDeliveryStreamEncryption(ctx context.Context, params *firehose.StopDeliveryStreamEncryptionInput, optFns ...func(*firehose.Options)) (*firehose.StopDeliveryStreamEncryptionOutput, error)
//	TagDeliveryStream(ctx context.Context, params *firehose.TagDeliveryStreamInput, optFns ...func(*firehose.Options)) (*firehose.TagDeliveryStreamOutput, error)
//	UntagDeliveryStream(ctx context.Context, params *firehose.UntagDeliveryStreamInput, optFns ...func(*firehose.Options)) (*firehose.UntagDeliveryStreamOutput, error)
//	UpdateDestination(ctx context.Context, params *firehose.UpdateDestinationInput, optFns ...func(*firehose.Options)) (*firehose.UpdateDestinationOutput, error)
//}
//
//type FirehoseWithMonitoring struct {
//	FirehoseInterface
//	serviceStatus *health.ServiceStatus
//}
//
//func NewFirehoseClient(ctx context.Context, cfg *config.Config) *firehose.Client {
//	log := logger.FromContext(ctx)
//
//	awsconfig := aws.Config{
//		Region:      cfg.FirehoseRegion,
//		Credentials: utils.GetV2CredentialProvider(ctx),
//	}
//
//	awsconfig.APIOptions = append(awsconfig.APIOptions, func(s *middleware.Stack) error {
//		return s.Serialize.Add(&OnBuild{}, middleware.After)
//	})
//
//	awsconfig.APIOptions = append(awsconfig.APIOptions, func(s *middleware.Stack) error {
//		return s.Finalize.Add(&OnComplete{}, middleware.After)
//	})
//
//	var client *firehose.Client
//	for attempt := 1; attempt <= cfg.AwsRequestMaxRetryAttempt; attempt++ {
//		client = firehose.NewFromConfig(awsconfig, func(o *firehose.Options) {
//			o.Retryer = utils.GetDefaultRetryStandard(cfg.AwsRequestMaxRetryAttempt)
//		})
//
//		if client != nil {
//			streamName := aws.String(cfg.FirehoseStreamName)
//
//			// check we have access to the stream
//			_, err := client.DescribeDeliveryStream(ctx, &firehose.DescribeDeliveryStreamInput{DeliveryStreamName: streamName})
//			if err != nil {
//				log.Error().Err(err).Msgf("Missing firehose stream: %s", cfg.KinesisStreamArn)
//				return nil
//			}
//
//			return client
//		}
//
//		zlog.Error().Msg("failed to create a firehose client")
//		time.Sleep(time.Second * time.Duration(attempt))
//	}
//	return client
//}
//
//func NewFirehose(ctx context.Context, cfg *config.Config) *FirehoseWithMonitoring {
//	firehoseClient := NewFirehoseClient(ctx, cfg)
//	endpoint := ""
//
//	status := &health.ServiceStatus{
//		Instances: []*health.InstanceInfo{
//			{
//				Id:     endpoint,
//				Status: health.UNKNOWN,
//			},
//		},
//		Status: health.UNKNOWN,
//	}
//	return &FirehoseWithMonitoring{
//		FirehoseInterface: firehoseClient,
//		serviceStatus:     status,
//	}
//}
//
//func (fh *FirehoseWithMonitoring) IsCritical() bool {
//	return true
//}
//
//func (fh *FirehoseWithMonitoring) CheckHealth() bool {
//	svc := fh.serviceStatus.Instances[0]
//	status := health.FAIL
//	start := time.Now()
//
//	deliveryStreamInput := &firehose.ListDeliveryStreamsInput{
//		ExclusiveStartDeliveryStreamName: aws.String("a"),
//		Limit:                            aws.Int32(1),
//	}
//	_, err := fh.FirehoseInterface.ListDeliveryStreams(context.Background(), deliveryStreamInput)
//	elapsed := time.Since(start).Milliseconds()
//	if err != nil {
//		health.SetInstance(svc, status, start, elapsed, err.Error())
//		zlog.Err(err).Msg("Firehose Service unreachable")
//	} else {
//		status = health.OK
//		health.SetInstance(svc, status, start, elapsed, "all firehose streams")
//	}
//
//	fh.serviceStatus.Status = status
//	return status == health.OK
//}
//
//func (fh *FirehoseWithMonitoring) LastStatus() *health.ServiceStatus {
//	return fh.serviceStatus
//}
//
//// BuildFirehoseStreamName builds fire hose stream name to write to
//func BuildFirehoseStreamName() string {
//	env := utils.GetEnvironment()
//	if utils.IsReleaseCantidate() || utils.IsPullRequest() {
//		env = "develop"
//	}
//	return fmt.Sprintf("t2gp-social-telemetry-%s", env)
//}
//
//// sendToFirehose send tele event to firehose
//func sendToFirehose[fhObj FirehoseObj](ctx context.Context, t *Telemetry, item *fhObj) error {
//	log := logger.FromContext(ctx)
//	log.Debug().Bool("shouldFirehose", t.cfg.ShouldFirehose).Str("event", "should firehose").Msg("should firehose")
//	if t.cfg.ShouldFirehose {
//		if item == nil {
//			log.Error().Str("event", "nil firehose record").Msg("nil firehose record")
//			return errs.New(http.StatusNotFound, errs.EFirehosePutFailed)
//		}
//
//		data, err := json.Marshal(item)
//		if err != nil {
//			log.Error().Err(err).Str("event", "failed to marshal firehose record").Msg("failed to marshal firehose record")
//			return errs.New(http.StatusUnprocessableEntity, errs.EFirehoseMarshalFailed)
//		}
//		if data == nil {
//			log.Error().Err(err).Str("event", "firehose record marshal empty").Msg("firehose record marshal empty")
//			return errs.New(http.StatusNotFound, errs.EFirehoseMarshalFailed)
//		}
//
//		itemStr := string(data)
//		log.Info().Str("item", itemStr).Str("event", "sending item to firehose").Msg("sending item to firehose")
//
//		pr := &firehose.PutRecordInput{
//			DeliveryStreamName: aws.String(t.cfg.FirehoseStreamName),
//			Record:             &types.Record{Data: data},
//		}
//
//		out, err := t.fh.PutRecord(ctx, pr)
//		if err != nil {
//			log.Error().Err(err).Str("deliveryStream", t.cfg.FirehoseStreamName).Str("item", itemStr).Str("event", "failed to put firehose record").Msg("failed to put firehose record")
//			return errs.New(http.StatusInternalServerError, errs.EFirehosePutFailed)
//		}
//
//		outStr := ""
//		if out != nil {
//			outStr = *out.RecordId
//		}
//		log.Debug().Str("deliveryStream", t.cfg.FirehoseStreamName).Str("output", outStr).Str("event", "firehose put record").Msg("firehose put record")
//
//	}
//	return nil
//}
