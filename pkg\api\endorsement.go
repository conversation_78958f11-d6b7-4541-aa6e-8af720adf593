package api

import (
	"context"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
)

func (api *SocialPublicAPI) GetEndorsementsForSelf(w http.ResponseWriter, r *http.Request) {

	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID

	var ctx context.Context
	if r == nil || r.Context() == nil {
		ctx = context.Background()
	} else {
		ctx = r.Context()
	}

	response, err := api.getEndorsementsHelper(ctx, userid, productid, true)
	if err != nil {
		errs.Return(w, r, err)
		return
	}
	ReturnOK(w, r, response)
}

func (api *SocialPublicAPI) GetEndorsementsForUser(w http.ResponseWriter, r *http.Request, pUserid apipub.PUserid) {

	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := pUserid
	productid := token.Claims.ProductID

	var ctx context.Context
	if r == nil || r.Context() == nil {
		ctx = context.Background()
	} else {
		ctx = r.Context()
	}

	response, err := api.getEndorsementsHelper(ctx, userid, productid, false)
	if err != nil {
		errs.Return(w, r, err)
		return
	}
	ReturnOK(w, r, response)

}

func (api *SocialPublicAPI) IncrementEndorsement(w http.ResponseWriter, r *http.Request, pUserid apipub.PUserid) {
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	fromUserid := token.Claims.Subject

	if fromUserid == pUserid {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EEndorseCannotEndorseSelf))
		return
	}

	toUserid := pUserid
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer

	// decode request
	var endorsementReq apipub.IncrementEndorsementRequestBody
	if !DecodeBody(w, r, &endorsementReq) {
		return
	}

	isPositive := true
	if endorsementReq.IsPositive != nil && !*endorsementReq.IsPositive {
		isPositive = false
	}
	isPrivate := false
	if endorsementReq.IsPrivate != nil && *endorsementReq.IsPrivate {
		isPrivate = true
	}
	endorsementName := "default"
	if endorsementReq.EndorsementName != nil && *endorsementReq.EndorsementName != "" {
		endorsementName = *endorsementReq.EndorsementName
	}

	endorsement, _ := api.Cache.GetEndorsement(r.Context(), toUserid, productid, endorsementName)

	if endorsement == nil {
		endorsement = &apipub.EndorsementResponse{
			CurrentEndorsementCount: 0,
			TotalEndorsementCount:   0,
			EndorsementName:         endorsementName,
			IsPositive:              isPositive,
			IsPrivate:               isPrivate,
		}
	}

	api.Cache.IncrementEndorsement(r.Context(), toUserid, productid, endorsement, endorsementReq.IncrementValue)

	// dynamo is source of truth so return error if can't reset it
	err := api.Ds.IncrementEndorsement(r.Context(), toUserid, productid, endorsement, endorsementReq.IncrementValue)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
		return
	}

	mqttEndorse := apipub.MqttEndorsementReceived{
		EndorsementName:         endorsement.EndorsementName,
		CurrentEndorsementCount: endorsement.CurrentEndorsementCount,
		TotalEndorsementCount:   endorsement.TotalEndorsementCount,
		IsPositive:              endorsement.IsPositive,
		IsPrivate:               endorsement.IsPrivate,
	}

	messenger.SendMqttMessage(r.Context(), api.Cfg, apipub.BuildUserTopic(tenant, toUserid), messenger.MqttMessageTypeEndorseIncrement, mqttEndorse)

	// send telemetry for increment
	additionalInfo := make(map[string]string)
	if endorsementReq.TeleMeta != nil {
		utils.ConvertMapInterfaceToMapString(*endorsementReq.TeleMeta, &additionalInfo)
	}
	api.Tele.SendEndorsementEvent(r.Context(), telemetry.BuildEndorsementTeleMeta(telemetry.KEndorsementIncrement, productid, token.Claims.Subject, ost, []string{pUserid}, *endorsement, &appid, &additionalInfo))

	ReturnEmptyOK(w, r)
}

// getEndorsementsHelper - helper function to get endrosements.  flag shouldReturnPrivate is used so game teams can disallow querying certain endorsements for user.
func (api *SocialPublicAPI) getEndorsementsHelper(ctx context.Context, userid, productid string, shouldReturnPrivate bool) (*apipub.EndorsementListResponse, *errs.Error) {
	log := logger.FromContext(ctx)

	response := apipub.EndorsementListResponse{
		Items: []apipub.EndorsementResponse{},
	}

	endorsements, err := api.Cache.GetEndorsements(ctx, userid, productid)
	if err != nil || endorsements == nil || len(*endorsements) == 0 {
		endorsements, err = api.Ds.GetEndorsements(ctx, userid, productid)
		if err != nil {
			log.Error().Err(err).Msgf("failed to read groups from Cache")
			return nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed)
		} else {
			for _, endorsement := range *endorsements {
				// add 0 to count because we're just adding the records to the cache, not actually incrementing it
				api.Cache.IncrementEndorsement(ctx, userid, productid, endorsement, 0)
			}
		}
	}

	for _, endorsement := range *endorsements {
		if shouldReturnPrivate || !endorsement.IsPrivate {
			response.Items = append(response.Items, *endorsement)
		}
	}

	if len(response.Items) == 0 {
		response.Items = nil
	}

	return &response, nil
}
