-module(t2gp_social_cli_SUITE).

-include_lib("eunit/include/eunit.hrl").

-compile(nowarn_export_all).
-compile(export_all).

init_per_suite(Config) ->
    t2gp_social_test:configure(),
    t2gp_social_test:start_vmq(),
    cover:start(),
    t2gp_social_cli:register_cli(),
    Config.

end_per_suite(_Config) ->
    t2gp_social_test:stop_vmq(),
    ok.

all() ->[
    test_on_reload,
    test_keyspec,
    test_version,
    test_topics,
    test_clear_topics,
    test_publish,
    test_unsubscribe,
    test_subscribe
].

test_on_reload(_) ->
    ok = t2gp_social_cli:on_reload(),
    ok.

test_keyspec(_) ->
    Name = foobar,
    Result = t2gp_social_cli:keyspec(Name),
    {foobar,[{typecast,NameFunc}]} = Result,
    <<"test">> = NameFunc("test"),
    {error,{invalid_value,test}} = NameFunc(test),
    
    {userid,[{typecast,_}]} = t2gp_social_cli:userid_keyspec(),
    {message,[{typecast,MessageFunc}]} = t2gp_social_cli:message_keyspec(),
    <<"test  test">> = MessageFunc("test++test"),
    {error, {invalid_value, test}} = MessageFunc(test),

    {topic,[{typecast,TopicFunc}]} = t2gp_social_cli:topic_keyspec(),
    [<<"foo">>, <<"bar">>] = TopicFunc("foo/bar"),
    {error, {invalid_value, test}} = TopicFunc(test),
    ok.


cli(Cmd) ->
    M0 = clique_command:match(Cmd),
    M1 = clique_parser:parse(M0),
    M2 = clique_parser:extract_global_flags(M1),
    M3 = clique_parser:validate(M2),
    clique_command:run(M3).

test_version(_) ->
    Output = cli(["vmq-admin", "t2gp", "version"]),
    {[{table,[[{version,_},
                    {git_hash,_},
                    {build_date,_}]]}],
          0,"human"} = Output,
    ok.

test_topics(_) ->
    Output = cli(["vmq-admin", "t2gp", "topics", "userid=foobar"]),
    {[{table,[]}],0,"human"} = Output,
    OutputErr = cli(["vmq-admin", "t2gp", "topics", "wrong=arg"]),
    {error,{invalid_key,"wrong"}} = OutputErr,
    OutputHelp = cli(["vmq-admin", "t2gp", "topics"]),
    {[{alert,[{text,_}]}], 0,"human"} = OutputHelp,
    ok.


test_clear_topics(_) ->
    Output = cli(["vmq-admin", "t2gp", "clear-topics", "userid=foobar"]),
    {[{text,"Done"}],0,"human"} = Output,
    OutputErr = cli(["vmq-admin", "t2gp", "clear-topics", "wrong=arg"]),
    {error,{invalid_key,"wrong"}} = OutputErr,
    OutputHelp = cli(["vmq-admin", "t2gp", "clear-topics"]),
    {[{alert,[{text,_}]}], 0,"human"} = OutputHelp,
    ok.

test_publish(_) ->
    Output = cli(["vmq-admin", "t2gp", "publish", "topic=foo/bar", "message=hello", "userid=foobar"]),
    {[{text,"Done"}],0,"human"} = Output,
    OutputErr = cli(["vmq-admin", "t2gp", "publish", "wrong=arg"]),
    {error,{invalid_key,"wrong"}} = OutputErr,
    OutputHelp = cli(["vmq-admin", "t2gp", "publish"]),
    {[{alert,[{text,_}]}], 0,"human"} = OutputHelp,
    ok.

test_unsubscribe(_) ->
    Output = cli(["vmq-admin", "t2gp", "unsubscribe", "topic=foo/bar", "userid=foobar"]),
    {[{text,"Done"}],0,"human"} = Output,
    OutputErr = cli(["vmq-admin", "t2gp", "unsubscribe", "wrong=arg"]),
    {error,{invalid_key,"wrong"}} = OutputErr,
    OutputHelp = cli(["vmq-admin", "t2gp", "unsubscribe"]),
    {[{alert,[{text,_}]}], 0,"human"} = OutputHelp,
    ok.

test_subscribe(_) ->
    Output = cli(["vmq-admin", "t2gp", "subscribe", "topic=foo/bar", "userid=foobar"]),
    {[{text,"Done"}],0,"human"} = Output,
    OutputErr = cli(["vmq-admin", "t2gp", "subscribe", "wrong=arg"]),
    {error,{invalid_key,"wrong"}} = OutputErr,
    OutputHelp = cli(["vmq-admin", "t2gp", "subscribe"]),
    {[{alert,[{text,_}]}], 0,"human"} = OutputHelp,
    ok.

% we can't test this because it will screw up the coverage report
% test_reload(_) ->
%     Output = cli(["vmq-admin", "t2gp", "reload"]),
%     {[{text,"Done"}],0,"human"} = Output,
%     ok.
