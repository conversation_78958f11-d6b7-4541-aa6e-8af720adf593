package cache

import (
	"context"
	"fmt"
	"net/http"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/redis/go-redis/v9"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
)

// Use sorted sets for secondary indexing and pagination
/////////////////////////////////////////////////////////////////////////////////////////////////
// Sorted set By Lex Index Key formats:
// User Groups - user:<userid>:groups:product:<productid>	 									group:<productid>:<groupid>
// Join Requests - requested:<approverid>:<productid>:<groupid>:<memberid>	group:<productid>:<groupid>
// Invites - invited:<memberid>:<productid>:<groupid>:<approverid>					group:<productid>:<groupid>
// UserBlocklist - user:<userid>:blocklist 																	blocklist:<productid>:<groupid>
// Friendlist - user:<userid>:friends:<status>															friend:<userid>:<friendid>
/////////////////////////////////////////////////////////////////////////////////////////////////
// Sorted by Score:
// Presence - user:<userid>:presence 							  <priority> 	presence:<userid>:<productid>
// Recently Played - user:<userid>:played         	<weight> 		recentlyPlayed:<userid>
/////////////////////////////////////////////////////////////////////////////////////////////////
// Primary Key formats:
// Group - group:<productid>:<groupid>													*apipub.Group
// Friend - friend:<userid>:<friendid>  												*apipub.Friend
// Profile - profile:<userid> 																	*apipub.Profile
// Presence - presence:<userid>:<productid>                 		*apipub.Presence
// Recently Played - recentlyPlayed:<userid>:<productid>    		*apipub.RecentlyPlayed
// Blocklist item - blocklist:<userid>:<blockedid>          		*apipub.Blocklist
/////////////////////////////////////////////////////////////////////////////////////////////////

func (rc *RedisCache) setSecondaryIndex(ctx context.Context, idx *index.SecondaryIndex) error {

	if idx == nil {
		return errs.New(http.StatusInternalServerError, errs.ERedisNilIndex)
	}
	if idx.IdxKey() == "" || idx.ValKey() == "" {
		return errs.New(http.StatusInternalServerError, errs.ERedisInvalidKey)
	}

	err := rc.zAdd(ctx, idx.IdxKey(), redis.Z{
		Score:  *idx.Score(),
		Member: idx.ValKey(),
	}).Err()

	if err != nil {
		log.Error().Err(err).Str("idxKey", idx.IdxKey()).Str("valKey", idx.ValKey()).Float64("score", *idx.Score()).Dur("ttl", *idx.Ttl(rc.cfg)).Msg("Cache setSecondaryIndex failed")
		return errs.New(http.StatusInternalServerError, errs.ERedisZAddFailed)
	}

	// set expiration
	err = rc.expire(ctx, idx.IdxKey(), *idx.Ttl(rc.cfg)).Err()

	if err != nil {
		log.Error().Err(err).Msgf("Cache setSecondaryIndex expire failed for idx %s", idx.IdxKey())
	}
	return nil
}

func (rc *RedisCache) setSecondaryIndexPipe(ctx context.Context, idx *index.SecondaryIndex, pipe *redis.Pipeliner) error {
	err := (*pipe).ZAdd(ctx, idx.IdxKey(), redis.Z{
		Score:  *idx.Score(),
		Member: idx.ValKey(),
	}).Err()
	if err != nil {
		log.Error().Err(err).Str("idxKey", idx.IdxKey()).Msg("failed to set zadd pipe")
		return errs.New(http.StatusInternalServerError, errs.ERedisZAddFailed)
	}

	err = (*pipe).Expire(ctx, idx.IdxKey(), *idx.Ttl(rc.cfg)).Err()
	if err != nil {
		log.Error().Err(err).Str("idxKey", idx.IdxKey()).Msg("failed to set expire pipe")
		return errs.New(http.StatusInternalServerError, errs.ERedisExpireFailed)
	}

	return nil
}

func (rc *RedisCache) delSecondaryIndex(ctx context.Context, idx *index.SecondaryIndex) error {
	if idx == nil {
		return errs.New(http.StatusInternalServerError, errs.ERedisNilIndex)
	}
	if idx.IdxKey() == "" || idx.ValKey() == "" {
		return errs.New(http.StatusInternalServerError, errs.ERedisInvalidKey)
	}

	var err error
	if idx.Score() != aws.Float64(0) {
		// scoreStr := fmt.Sprintf("%f", *idx.Score())
		err = rc.zRem(ctx, idx.IdxKey(), idx.Score(), idx.ValKey()).Err()
	} else {
		err = rc.zRem(ctx, idx.IdxKey(), idx.ValKey()).Err()
	}

	if err != nil {
		log.Error().Err(err).Msgf("Cache DelMembershipIndex failed for idx %s", idx.IdxKey())
		return errs.New(http.StatusInternalServerError, errs.ERedisZRemFailed)
	}

	return nil
}

func getKeysFromSecondaryIndex(ctx context.Context, rc *RedisCache, idx *index.SecondaryIndex, limit *int64, next *string, reverse bool) (*[]string, string, error) {

	var count int64
	if limit == nil || *limit <= 0 {
		limit = aws.Int64(0)
		count = 0
	} else {
		//count = limit + 1 to get next value
		count = *limit + 1
	}

	//get all index valKeys from sorted set
	var valKeys []string
	retNext := ""
	if idx.Score() != nil && *idx.Score() > float64(0) {
		var resWithScores []redis.Z
		var err error
		var minRange, maxRange string

		if reverse {
			if next == nil || *next == "" {
				minRange = "-inf"
				maxRange = "+inf"
			} else {
				minRange = "-inf"
				maxRange = *next
			}
			rangeBy := &redis.ZRangeBy{
				Min:   minRange,
				Max:   maxRange,
				Count: count,
			}

			resWithScores, err = rc.zRevRangeByScoreWithScores(ctx, idx.IdxKey(), rangeBy).Result()
		} else {
			if next == nil || *next == "" {
				minRange = "-inf"
				maxRange = "+inf"
			} else {
				minRange = *next
				maxRange = "+inf"
			}
			rangeBy := &redis.ZRangeBy{
				Min:   minRange,
				Max:   maxRange,
				Count: count,
			}

			resWithScores, err = rc.zRangeByScoreWithScores(ctx, idx.IdxKey(), rangeBy).Result()
		}
		if err != nil {
			log.Error().Err(err).Str("key", idx.IdxKey()).Str("minRange", minRange).Str("maxRange", maxRange).Int64("count", count).Msg("failed to get range by score")
			return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisZRangeByScoreFailed)
		}
		for i := int64(0); i < int64(len(resWithScores)); i++ {
			if *limit == 0 || (*limit > 0 && i < *limit) {
				valKeys = append(valKeys, resWithScores[i].Member.(string))
			}
		}
		if *limit > 0 && int64(len(resWithScores)) == *limit+1 {
			retNext = fmt.Sprintf("%f", resWithScores[*limit].Score)
		}
	} else {

		var minRange, maxRange string
		var resByLex []string
		var err error
		if reverse {
			if next == nil || *next == "" {
				minRange = "-"
				maxRange = "+"
			} else {
				minRange = "-"
				maxRange = "[" + *next
			}
			rangeBy := &redis.ZRangeBy{
				Min:   minRange,
				Max:   maxRange,
				Count: count,
			}
			resByLex, err = rc.zRangeByLex(ctx, idx.IdxKey(), rangeBy).Result()
		} else {
			if next == nil || *next == "" {
				minRange = "-"
				maxRange = "+"
			} else {
				minRange = "[" + *next
				maxRange = "+"
			}
			rangeBy := &redis.ZRangeBy{
				Min:   minRange,
				Max:   maxRange,
				Count: count,
			}
			resByLex, err = rc.zRangeByLex(ctx, idx.IdxKey(), rangeBy).Result()
		}
		if err != nil {
			log.Error().Err(err).Msg("Redis getKeysFromSecondaryIndex failed")
			return nil, "", err
		}
		for i := int64(0); i < int64(len(resByLex)); i++ {
			if *limit == 0 || (*limit > 0 && i < *limit) {
				valKeys = append(valKeys, resByLex[i])
			}
		}

		if *limit > 0 && int64(len(resByLex)) == *limit+1 {
			retNext = resByLex[*limit]
		}
	}
	return &valKeys, retNext, nil
}

func getObjsFromSecondaryIndex[cachedObj RedisCacheObj](ctx context.Context, rc *RedisCache, idx *index.SecondaryIndex, limit *int64, next *string, reverse bool) (*[]*cachedObj, string, error) {
	if rc == nil {
		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisNilCache)
	}
	if idx == nil {
		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisNilIndex)
	}
	if idx.IdxKey() == "" {
		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisInvalidKey)
	}

	valKeys, retNext, err := getKeysFromSecondaryIndex(ctx, rc, idx, limit, next, reverse)
	if err != nil {
		return nil, "", err
	}

	if valKeys != nil && len(*valKeys) > 0 {
		//get actual objects from redis
		retObjs, err := getCachedObjects[cachedObj](ctx, rc, valKeys)
		//if we have an error that is not ERedisObjectMissing, log then return nil and error
		if err != nil && !errs.IsEqual(err, errs.ERedisObjectMissing) {
			log.Error().Err(err).Msg("Redis getObjsFromSecondaryIndex failed")
			return nil, "", err
			//if retObjs length is > 0, return objects and error (nil or ObjectMissing)
		} else if retObjs != nil && len(*retObjs) > 0 {
			return retObjs, retNext, err
		} else { //else we have no return objects, so return nil and error (nil or ObjectMissing)
			return nil, "", err
		}
	}
	return nil, "", nil
}

// func getObjsFromMultiSortedSets[cachedObj RedisCacheObj](ctx context.Context, rdb *RedisCache, indexes *[]index.SecondaryIndex, limit *int64, next *string, reverse bool) (*[]*cachedObj, string, error) {
// 	if rdb == nil {
// 		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisNilCache)
// 	}
// 	if indexes == nil || len(*indexes) == 0 {
// 		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisNilIndex)
// 	}

// 	if len(*indexes) == 1 {
// 		return getObjsFromSecondaryIndex[cachedObj](ctx, rdb, &(*indexes)[0], limit, next, reverse)
// 	}

// 	// Union the sets and save the result to a temporary key
// 	idxKeys := make([]string, len(*indexes))
// 	var hashTag string
// 	for i, idx := range *indexes {
// 		idxKeys[i] = idx.IdxKey()
// 		if hashTag == "" {
// 			hashTag = GetHashTag(idx.IdxKey())
// 		}
// 	}

// 	if hashTag != "" {
// 		hashTag = "{" + hashTag + "}"
// 	}

// 	//if we found a hash tag, prepend it with brackets to use the same hash
// 	tmpKey := hashTag + utils.GenerateNewULID()
// 	defer rdb.ecWriteClient.del(ctx, tmpKey)

// 	unionStore := &redis.ZStore{
// 		Keys: idxKeys,
// 	}
// 	err := rdb.ecWriteClient.ZUnionStore(ctx, tmpKey, unionStore).Err()
// 	if err != nil {
// 		log.Error().Err(err).Str("tempKey", tmpKey).Msgf("Redis getObjsFromMultiSortedSets ZUnionStore failed %v", err)
// 		return nil, "", errs.New(http.StatusInternalServerError, errs.ERedisZUnionStoreFailed)
// 	}
// 	idx := index.NewSecondaryIndex(tmpKey, "")

// 	retArr, retNext, retErr := getObjsFromSecondaryIndex[cachedObj](ctx, rdb, idx, limit, next, reverse)
// 	if retErr != nil {
// 		log.Error().Err(retErr).Msgf("Redis getObjsFromMultiSortedSets getObjsFromSecondaryIndex failed %v", retErr)
// 		return nil, "", retErr
// 	}

// 	return retArr, retNext, nil
// }
