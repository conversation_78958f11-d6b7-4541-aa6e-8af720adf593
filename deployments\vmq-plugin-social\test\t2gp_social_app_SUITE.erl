-module(t2gp_social_app_SUITE).

-compile(nowarn_export_all).
-compile(export_all).

init_per_suite(Config) ->
    t2gp_social_test:configure(),
    cover:start(),
    Config.

end_per_suite(_Config) ->
    erlcloud_ddb2:delete_table(t2gp_social_test:table()),
    ok.

all() ->
    [
        test_case,
        test_get_config,
        test_get_sm_secrets,
        test_get_sm_env_name
    ].

test_case(_) ->
    t2gp_social_test:configure(),
    application:start(t2gp_social),
    application:stop(t2gp_social),
    ok = t2gp_social_app:stop(#{}),
    ok.

test_get_config(_) ->
    OldEnv = binary_to_list(utils:get_env()),
    os:putenv("DD_ENV", "test"),
    Secrets = [{"key1", "value1"}],
    "value1" = t2gp_social_app:get_config("key1", Secrets, ""),
    "default" = t2gp_social_app:get_config("key2", Secrets, "default"),

    %% test env var overriding
    os:putenv("key1", "value2"),
    "value2" = t2gp_social_app:get_config("key1", Secrets, "default"),

    os:putenv("DD_ENV", "local"),
    os:putenv("key1", "envvalue1"),
    "envvalue1" = t2gp_social_app:get_config("key1", Secrets, ""),
    "default" = t2gp_social_app:get_config("key2", Secrets, "default"),
    os:putenv("DD_ENV", OldEnv),
    ok.

test_get_sm_secrets(_) ->
    % SDev = t2gp_social_app:get_sm_secrets("develop"),
    % "t2gp-social-develop-social" = proplists:get_value("SOCIAL_DYNAMODB_SOCIAL_TABLE", SDev),
    % SBadEnv = t2gp_social_app:get_sm_secrets("bad env"),
    % "t2gp-social-develop-social" = proplists:get_value("SOCIAL_DYNAMODB_SOCIAL_TABLE", SBadEnv),
    % SInt = t2gp_social_app:get_sm_secrets("integration"),
    % "t2gp-social-integration-social" = proplists:get_value("SOCIAL_DYNAMODB_SOCIAL_TABLE", SInt),
    % SStage = t2gp_social_app:get_sm_secrets("staging"),
    % "t2gp-social-staging-social" = proplists:get_value("SOCIAL_DYNAMODB_SOCIAL_TABLE", SStage),
    % SProd = t2gp_social_app:get_sm_secrets("production"),
    % "t2gp-social-production-social" = proplists:get_value("SOCIAL_DYNAMODB_SOCIAL_TABLE", SProd),
    % SCert = t2gp_social_app:get_sm_secrets("cert"),
    % "t2gp-social-staging-social" = proplists:get_value("SOCIAL_DYNAMODB_SOCIAL_TABLE", SCert),
    ok.

test_get_sm_env_name(_) ->
    <<"social/mqtt/develop">> = t2gp_social_app:get_sm_env_name(<<"develop">>),
    <<"social/mqtt/integration">> = t2gp_social_app:get_sm_env_name(<<"integration">>),
    <<"social/mqtt/loadtesting">> = t2gp_social_app:get_sm_env_name(<<"loadtesting">>),
    <<"social/mqtt/staging">> = t2gp_social_app:get_sm_env_name(<<"staging">>),
    <<"social/mqtt/cert">> = t2gp_social_app:get_sm_env_name(<<"cert">>),
    <<"social/mqtt/production">> = t2gp_social_app:get_sm_env_name(<<"production">>),
    os:putenv("DEFAULT_SM_TARGET", "sm-test"),
    <<"social/mqtt/sm-test">> = t2gp_social_app:get_sm_env_name(<<"cert3">>),
    ok.
