<script lang="ts">
  import { onMount } from 'svelte';
  import { SVGArrowDown } from '../../assets/icons';
  import {
    EVENT_PRESENCE_CHANGED,
    EVENT_USER_PROFILE_FETCH_REQUEST,
    PRESENCE_STATUS,
  } from '../../constant';
  import {
    useTranslator,
    useTransportService,
    useUserPresence,
    useUserQuery,
  } from '../../hooks';
  import { isQueryLoading } from '../../utils';
  import { Avatar } from '../Avatar';
  import { LoadingSpinner } from '../LoadingSpinner';
  import StatusIndicator from '../StatusIndicator/StatusIndicator.svelte';

  export let showmenuitems = false;

  const userPresence = useUserPresence();
  const userQueryResult = useUserQuery();
  const transportService = useTransportService();
  const t = useTranslator();
  const toggleDropdownMenu = () => {
    showmenuitems = !showmenuitems;
  };

  const onPresenceClicked = (status: string) => () => {
    const payload = {
      status,
      timestamp: new Date().toISOString(),
    };
    userPresence.set(payload);
    transportService.publishEvent(EVENT_PRESENCE_CHANGED, payload);
    toggleDropdownMenu();
  };

  $: queryLoading = isQueryLoading($userQueryResult);
  $: user = $userQueryResult?.data;
  $: initials = (user?.name && user?.name[0]) || '';
  $: username = user?.name || '';
  $: displayname = username.split('#');
  $: status = $userPresence.status || '';

  onMount(() => {
    transportService.publishEvent(EVENT_USER_PROFILE_FETCH_REQUEST);
  });
</script>

<style>
  .user-account {
    position: relative;
  }
  .user-account .user-info {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .user-info > *:not(:first-child) {
    margin-inline-start: 0.375rem;
  }

  .user-account .user-info:hover {
    cursor: pointer;
  }
  .user-account .title {
    font-size: 0.875rem;
    line-height: 150%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    text-transform: uppercase;
  }

  .user-account .subtitle {
    font-size: 0.75rem;
    font-weight: 700;
    letter-spacing: 0.05rem;
    opacity: 0.6;
    margin-inline-start: 0.125rem;
  }

  .user-menu .title {
    margin-inline-start: 1rem;
    text-transform: none;
  }

  .user .user-account .icon {
    display: flex;
  }

  .user-account .user-menu {
    position: absolute;
    min-height: 7.5rem;
    min-width: 12.5rem;
    max-width: 15.5rem;
    background-color: rgba(45, 45, 45, 1);
    top: 2rem;
    right: 0;
    z-index: 100;
    display: none;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.125rem;
  }

  .user-account .user-menu:hover {
    cursor: default;
  }

  .user-account .user-menu.showmenuitems {
    display: block;
  }

  .user-account .user-menu .menuitem {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.6rem 1rem;
    font-size: 1rem;
    font-weight: 700;
    line-height: 150%;
    letter-spacing: 0.05rem;
  }

  .user-account .user-menu .menuitem.header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    justify-content: center;
  }

  .user-account .user-menu .menuitem.status {
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 150%;
    color: rgba(255, 255, 255, 0.8);
    padding-left: 1.5rem;
    text-transform: capitalize;
  }

  .user-account .user-menu .menuitem.status:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 1);
    cursor: pointer;
  }
  .user-account .user-menu .menuitem.status span {
    margin-inline-end: 1rem;
  }
</style>

<div class="user-account">
  {#if queryLoading}
    <LoadingSpinner size="{18}" />
  {:else if user}
    <div class="user-info" on:click="{toggleDropdownMenu}">
      <Avatar initials="{initials}" name="{displayname[0]}" status="{status}" />
      <span class="title">{displayname[0]}</span>
      <span class="icon">
        <SVGArrowDown />
      </span>
    </div>
    <div class="user-menu" class:showmenuitems>
      <div class="menuitem header">
        <span class="title">{displayname[0]}</span>
        {#if displayname[1]}
          <span class="subtitle"> #{displayname[1]}</span>
        {/if}
      </div>
      <div
        class="menuitem status"
        on:click="{onPresenceClicked(PRESENCE_STATUS.online)}"
      >
        <span><StatusIndicator online /></span>
        <span>{$t('online')}</span>
      </div>
      <div
        class="menuitem status"
        on:click="{onPresenceClicked(PRESENCE_STATUS.away)}"
      >
        <span><StatusIndicator away /></span>
        <span>{$t('away')}</span>
      </div>

      <div
        class="menuitem status"
        on:click="{onPresenceClicked(PRESENCE_STATUS.offline)}"
      >
        <span><StatusIndicator offline /></span>
        <span>{$t('Appear Offline')}</span>
      </div>
    </div>
  {/if}
</div>
