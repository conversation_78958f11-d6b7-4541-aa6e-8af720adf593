package apipub

import (
	"testing"

	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

func TestPresence(t *testing.T) {
	g := goblin.Goblin(t)

	userid := utils.GenerateRandomDNAID()
	productid := utils.GenerateRandomDNAID()
	p := PresenceResponse{
		Userid:          userid,
		Priority:        PresencePriorityGameSetStart,
		Productid:       productid,
		ActiveSessionid: "1",
	}
	key := BuildPresenceRedisKey("test", productid, userid, "1")

	g.Describe("Presence", func() {
		g.It("should return expected values", func() {
			g.<PERSON>(p.<PERSON>("test")).Equal(key)
		})

		g.It("should return valid presence", func() {
			statuses := []PresenceStatus{
				Authenticating,
				Away,
				Chat,
				Custom,
				Dnd,
				Offline,
				Online,
				Playing,
			}
			for _, s := range statuses {
				g.<PERSON>(IsValidPresenceStatus(string(s))).IsTrue()
			}
			g.<PERSON>(IsValidPresenceStatus("foobar")).IsFalse()
		})

	})
}
