package store

import (
	"context"
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	gomock "go.uber.org/mock/gomock"
)

func TestCreateGetUserProfile(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	var err error
	email := "<EMAIL>"
	profile := &apipub.UserProfileResponse{
		Userid: "foobar",
		Email:  &email,
	}

	g.Describe("GetUserProfile", func() {
		g.It("should get user profile foobar successfully", func() {
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().BatchWriteItem(gomock.Any(), gomock.Any()).Return(nil, nil)
			err = mock.db.PutUserProfile(context.Background(), profile)
			g.Assert(err).IsNil()

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(nil, nil)
			_, err = mock.db.GetUserProfile(context.Background(), "foobar")
			g.Assert(err).IsNil()
		})

		g.It("should get user updated user profile name123 successfully", func() {
			profile.DisplayName = aws.String("name123")
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().BatchWriteItem(gomock.Any(), gomock.Any())
			err = mock.db.PutUserProfile(context.Background(), profile)
			g.Assert(err).IsNil()

			var result *apipub.UserProfileResponse
			pItem := &dynamodb.GetItemOutput{
				Item: map[string]types.AttributeValue{
					"userid": &types.AttributeValueMemberS{
						Value: profile.Userid,
					},
					"displayName": &types.AttributeValueMemberS{
						Value: *profile.DisplayName,
					},
				},
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(pItem, nil)
			result, err = mock.db.GetUserProfile(context.Background(), "foobar")
			g.Assert(err).IsNil()
			g.Assert(result).Equal(profile)
		})

		g.It("should fail on missing profile foo", func() {
			var result *apipub.UserProfileResponse
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().GetItem(gomock.Any(), gomock.Any()).Return(nil, nil)
			result, err = mock.db.GetUserProfile(context.Background(), "foo")
			g.Assert(err).IsNil()
			g.Assert(result).IsNil()
		})
	})
}

func TestGetUserProfiles(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockDS(t)
	defer mock.ctrl.Finish()

	g.Describe("GetUserProfiles", func() {
		g.It("return empty", func() {

			result, err := mock.db.GetUserProfiles(ctx, []string{})

			g.Assert(err).IsNil()
			g.Assert(result).IsNil()
		})

		g.It("should preserve order", func() {

			profile1 := &apipub.UserProfileResponse{
				Userid: "user1",
			}
			profile2 := &apipub.UserProfileResponse{
				Userid: "user2",
			}
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().BatchWriteItem(gomock.Any(), gomock.Any())
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.db.PutUserProfile(context.Background(), profile1)
			mock.ddb.EXPECT().BatchWriteItem(gomock.Any(), gomock.Any())
			mock.db.PutUserProfile(context.Background(), profile2)

			bItem := &dynamodb.BatchGetItemOutput{
				Responses: map[string][]map[string]types.AttributeValue{
					cfg.ProfileTable: {
						{
							"userid": &types.AttributeValueMemberS{
								Value: profile1.Userid,
							},
						},
						{
							"userid": &types.AttributeValueMemberS{
								Value: profile2.Userid,
							},
						},
					},
				},
			}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().BatchGetItem(gomock.Any(), gomock.Any()).Return(bItem, nil)
			result, err := mock.db.GetUserProfiles(ctx, []string{"user1", "user2"})

			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result)).Equal(2)
			g.Assert((*(*result)[0]).Userid).Equal("user1")
			g.Assert((*(*result)[1]).Userid).Equal("user2")
			g.Assert(true).Equal(true)

			bItem2 := &dynamodb.BatchGetItemOutput{
				Responses: map[string][]map[string]types.AttributeValue{
					cfg.ProfileTable: {
						{
							"userid": &types.AttributeValueMemberS{
								Value: profile2.Userid,
							},
						},
						{
							"userid": &types.AttributeValueMemberS{
								Value: profile1.Userid,
							},
						},
					},
				},
			}

			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
			mock.ddb.EXPECT().BatchGetItem(gomock.Any(), gomock.Any()).Return(bItem2, nil)
			result, err = mock.db.GetUserProfiles(ctx, []string{"user2", "user1"})

			g.Assert(err).IsNil()
			g.Assert(result).IsNotNil()
			g.Assert(len(*result)).Equal(2)
			g.Assert((*(*result)[0]).Userid).Equal("user2")
			g.Assert((*(*result)[1]).Userid).Equal("user1")
			g.Assert(true).Equal(true)
		})
	})
}
