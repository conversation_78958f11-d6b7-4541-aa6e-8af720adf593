name: Stale RC branches

on:
  schedule:
    - cron: '0 0 1 * 1-5'
  workflow_dispatch:

permissions:
  issues: write
  contents: write

jobs:
  stale_branches:
    runs-on: ubuntu-latest
    steps:
    - name: Stale Branches
      uses: crs-k/stale-branches@v3.0.0
      with:
        repo-token: '${{ secrets.SERVICE_ACCOUNT_GH_PAT }}'
        comment-updates: false
        max-issues: 3
        tag-committer: false
        stale-branch-label: 'stale branch 🗑️'
        compare-branches: 'info'
        branches-filter-regex: '^release\/v.*$'