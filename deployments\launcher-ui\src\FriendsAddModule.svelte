<script lang="ts">
  import { SocialServiceProvider } from './components/SocialServiceProvider';
  import { INITIAL_LANGUAGE } from './constant';
  import { FriendsAdd } from './pages';
  import type {
    FriendsService,
    SocialServices,
    TransportService,
  } from './services';
  import { UserService } from './services/user';

  export let lang = INITIAL_LANGUAGE;

  export let services: SocialServices<
    TransportService,
    FriendsService,
    UserService
  >;
</script>

<SocialServiceProvider lang="{lang}" services="{services}">
  <FriendsAdd />
</SocialServiceProvider>
