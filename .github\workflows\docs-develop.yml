name: Build and publish documentation for changes to <PERSON>elop

on:
  push:
    branches:
      - develop
  workflow_dispatch:

permissions:
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  build:
    environment: develop
    name: Build
    runs-on: [t2gp-arc-linux]

    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          fetch-depth: 0
      - name: Replace Version Placeholder
        run: |
          sed -i "s/REPLACE_ME/${GITHUB_SHA::8}/g" api/openapi-social-private.yml
          sed -i "s/REPLACE_ME/${GITHUB_SHA::8}/g" api/openapi-social-public.yml
          sed -i "s/REPLACE_ME/${GITHUB_SHA::8}/g" api/openapi-social-trusted-server.yml
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Set up Go 1.21
        uses: actions/setup-go@v3
        with:
          go-version: '1.21'
        id: go

      # enable npx
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          registry-url: https://npm.pkg.github.com/
          scope: take-two-t2gp

      - name: Gen Error codes
        run: |
          git config --global url.https://${{secrets.SERVICE_ACCOUNT_GH_PAT}}:<EMAIL>/.insteadOf https://github.com/
          GOPRIVATE=github.com/take-two-t2gp,github.com/2kg-coretech make errors
          aws s3 cp docs/api-error-codes.md s3://t2gp-docs/social-service/latest/

      # Use Docs upload tool to upload relevant docs, and publish dev.take2games.com
      # -s set latest
      # -h publish the docsite when finished
      # -a Set the PAT for the tool to use to publish
      # -v set the version
      - run: npx @take-two-t2gp/t2gp-docs-upload@v1.6.8 -v develop -s -h -a ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
