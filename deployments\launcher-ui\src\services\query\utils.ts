import {
  notifyManager,
  QueryFunction,
  QueryKey,
  QueryObserverOptions,
  QueryOptions,
} from '@sveltestack/svelte-query';

export function setBatchCalls<Options extends QueryObserverOptions>(
  options: Options
): Options {
  if (options.onError) {
    options.onError = notifyManager.batchCalls(options.onError);
  }

  if (options.onSuccess) {
    options.onSuccess = notifyManager.batchCalls(options.onSuccess);
  }

  if (options.onSettled) {
    options.onSettled = notifyManager.batchCalls(options.onSettled);
  }
  return options;
}

export function isQueryKey(value: any): value is QueryKey {
  return typeof value === 'string' || Array.isArray(value);
}

export function parseQueryArgs<TOptions extends QueryOptions<any, any, any>>(
  arg1: QueryKey | TOptions,
  arg2?: QueryFunction<any> | TOptions,
  arg3?: TOptions
): TOptions {
  if (!isQuery<PERSON>ey(arg1)) {
    return arg1 as TOptions;
  }

  if (typeof arg2 === 'function') {
    return { ...arg3, queryKey: arg1, queryFn: arg2 } as TOptions;
  }

  return { ...arg2, queryKey: arg1 } as TOptions;
}
