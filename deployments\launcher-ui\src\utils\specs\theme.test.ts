import { themefunc } from '../theme';

describe('themefunc', () => {
  it('should return style string with theme properties', () => {
    const theme = {
      color: '#501818',
      bgColorTopBar: 'rgba(255, 255, 255, 1)',
      bgColorActionBar: 'rgba(26, 26, 26, 1)',
      bgColorButton: 'rgba(221,7,0,1)',
    };

    const stylex = themefunc(theme);

    expect(stylex).toEqual(
      '--social-color: #501818;--social-bg-color-top-bar: rgba(255, 255, 255, 1);--social-bg-color-action-bar: rgba(26, 26, 26, 1);--social-bg-color-button: rgba(221,7,0,1);'
    );
  });

  it('should return empty string when no property defined in theme', () => {
    const theme = {
      color: '',
      bgColorTopBar: '',
      bgColorActionBar: '',
      bgColorButton: '',
    };

    const stylex = themefunc(theme);

    expect(stylex).toBe('');
  });

  it('should return empty string when theme is undefined', () => {
    const theme = undefined;

    const stylex = themefunc(theme);

    expect(stylex).toBe('');
  });
});
