// Package cache caching functionality
package cache

import (
	"context"

	zlog "github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"

	"time"

	"github.com/bsm/redislock"
	"github.com/redis/go-redis/v9"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"

	ddredisv9 "gopkg.in/DataDog/dd-trace-go.v1/contrib/redis/go-redis.v9"
	// "gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

var _ health.DependentService = &RedisCache{}

type RedisCacheInterface interface {

	//This file
	IsCritical() bool
	CheckHealth() bool
	LastStatus() *health.ServiceStatus

	//tsclientid.go
	GetTsClientId(ctx context.Context, clientid string) (*apitrusted.TsClientIdInfo, error)
	SetTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error
	DelTsClientId(ctx context.Context, clientid string) error
	SetRangeTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error
	DelRangeTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error

	//endorsements.go
	GetEndorsement(ctx context.Context, userid, productid, endorsementName string) (*apipub.EndorsementResponse, error)
	GetEndorsements(ctx context.Context, userid, productid string) (*[]*apipub.EndorsementResponse, error)
	IncrementEndorsement(ctx context.Context, userid, productid string, endorsement *apipub.EndorsementResponse, incrementValue int) error
	ResetEndorsement(ctx context.Context, userid, productid, endorsementName string) error
	RemoveEndorsement(ctx context.Context, userid, productid, endorsementName string) error

	//friends.go
	SetFriend(ctx context.Context, friend *apipub.FriendResponse, ttl time.Duration) error
	GetFriend(ctx context.Context, userid string, friendid string) (*apipub.FriendResponse, error)
	DeleteFriend(ctx context.Context, friend *apipub.FriendResponse) error
	MakeFriend(ctx context.Context, userid, friendid, message string, isUserBlocked bool, userOST apipub.OnlineServiceType, ttl time.Duration) error
	MakeUnfriend(ctx context.Context, userid string, friendid string) error
	GetFriends(ctx context.Context, userid string, status *apipub.FriendStatus, limit *int64, next *string) (*[]*apipub.FriendResponse, string, error)
	CountFriendlistMembers(ctx context.Context, userid string) (int64, error)
	FriendlistExistsInCache(ctx context.Context, userid string) bool
	ClearFriendlist(ctx context.Context, userid string) error
	SetFriends(ctx context.Context, friends *[]*apipub.FriendResponse, ttl time.Duration)

	//groups.go
	GetGroup(ctx context.Context, groupid string, productid string) (*apipub.GroupResponse, error)
	SetGroup(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	DeleteGroup(ctx context.Context, group *apipub.GroupResponse) error
	GetUserGroups(ctx context.Context, userid, productid string, limit *int64, next *string) (*[]*apipub.GroupResponse, string, error)
	GetGroupMembers(ctx context.Context, productid, groupid string) (*[]apipub.GroupMemberResponse, error)
	AddGroupMember(ctx context.Context, group *apipub.GroupResponse, member *apipub.GroupMemberResponse) error
	UpdateGroupMember(ctx context.Context, group *apipub.GroupResponse, member *apipub.GroupMemberResponse) error
	RemoveMembershipRequestFromGroup(ctx context.Context, group *apipub.GroupResponse, membership apipub.MembershipRequest) error
	AddMembershipRequestToGroup(ctx context.Context, group *apipub.GroupResponse, membership apipub.MembershipRequest) error
	KickOrLeaveHelper(ctx context.Context, group *apipub.GroupResponse, requestorid, targetuserid string, reason *string) (apipub.ChatMessageEventType, bool, error)
	RemoveGroupMember(ctx context.Context, group *apipub.GroupResponse, memberid string) (bool, error)
	CountUserGroups(ctx context.Context, userid, productid string) (int64, error)
	GetGroupTTL(ctx context.Context, productid, groupid string) (time.Duration, error)
	SetGroupMeta(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	SetGroupMaxMembers(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	SetGroupPassword(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	SetGroupCanMembersInvite(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	SetGroupCompositionId(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	SetGroupJoinRequestAction(ctx context.Context, group *apipub.GroupResponse, ttl time.Duration) error
	SetUserGroupIdxs(ctx context.Context, userid string, group *apipub.GroupResponse) error

	//redislock.go
	GetSyncLock(ctx context.Context, key string, ttl time.Duration) *redislock.Lock

	//membershiprequest.go
	GetInvitesForUser(ctx context.Context, memberid, productid string, limit *int64, next *string) (*[]*apipub.MembershipRequest, string, error)
	GetJoinRequestsByApproverId(ctx context.Context, approverid, productid string, limit *int64, next *string) (*[]*apipub.MembershipRequest, string, error)
	DeleteMembership(ctx context.Context, request *apipub.MembershipRequest) error
	setMembership(ctx context.Context, request *apipub.MembershipRequest) error
	JoinRequestExistsInCache(ctx context.Context, approverid, productid, groupid, memberid string) bool
	InviteExistsInCache(ctx context.Context, memberid, productid, groupid, approverid string, isFirstParty *bool) bool
	ClearAllMemberships(ctx context.Context, memberid, productid, groupid string)
	GetInvite(ctx context.Context, memberid, productid, groupid, approverid string, isFirstParty *bool) (*apipub.MembershipRequest, error)

	//profile.go
	GetUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error)
	SetUserProfile(ctx context.Context, profile *apipub.UserProfileResponse, ttl time.Duration) error
	GetUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error)
	SetUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse, ttl time.Duration) error
	DeleteUserProfile(ctx context.Context, userid string) error
	GetRecentlyPlayed(ctx context.Context, userid string, productid string, limit *int64, next *string) (*[]*apipub.RecentlyPlayedUserResponse, string, error)
	SetRecentlyPlayedUser(ctx context.Context, productid string, recentlyPlayed *apipub.RecentlyPlayedUserResponse, ttl time.Duration) error
	SetRecentlyPlayedUsers(ctx context.Context, productid string, recentlyPlayedUsers *[]*apipub.RecentlyPlayedUserResponse, ttl time.Duration)
	DelRecentlyPlayedUsers(ctx context.Context, productid, userid string)
	RefreshPresenceKeepAlive(ctx context.Context, presence *apipub.PresenceResponse) error
	UpdateUserTtls(ctx context.Context, userid string, ttl time.Duration) ([]bool, error)
	GetUserFirstPartyToken(ctx context.Context, firstPartyid string, ost int) (string, error)
	GetUserFirstPartyRefresh(ctx context.Context, firstPartyid string, ost int) (string, error)
	SetUserFirstPartyToken(ctx context.Context, firstPartyid string, ost int, token string, ttl time.Duration, refreshToken string, refreshTokenTtl time.Duration) error

	//presence.go
	GetPresence(ctx context.Context, userid, productid, sessionid string) (*apipub.PresenceResponse, error)
	SetPresence(ctx context.Context, presence *apipub.PresenceResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, ttl time.Duration) error
	DeletePresence(ctx context.Context, presence *apipub.PresenceResponse) error
	GetUserPresences(ctx context.Context, userid, product string) (*[]*apipub.PresenceResponse, error)
	GetLowestAvailablePriority(ctx context.Context, userid, productid string) (int, error)
	SavePresence(ctx context.Context, presence *apipub.PresenceResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, shouldBroadcast bool) error
	SetActiveGroup(ctx context.Context, group *apipub.GroupResponse, productid, appid, sessionid, userid string, createdTime, expiresTime int64, onlyModifySizes bool) (*apipub.PresenceResponse, error)

	//rediscachedobj.go
	DeleteCachedObj(ctx context.Context, key string) error
	CachedObjExists(ctx context.Context, key string) bool

	//redisstream.go
	//ProcessKeyspaceExpiredStream(ctx context.Context)

	//rediswrapper.go
	del(ctx context.Context, key string) *redis.IntCmd
	expire(ctx context.Context, key string, ttl time.Duration) *redis.BoolCmd
	get(ctx context.Context, key string) *redis.StringCmd
	exists(ctx context.Context, keys ...string) *redis.IntCmd
	existsInSortedSet(ctx context.Context, key string, member string) bool
	jSONGet(ctx context.Context, key string, paths ...string) *redis.JSONCmd
	jSONMGet(ctx context.Context, path string, keys ...string) *redis.JSONSliceCmd
	jSONSet(ctx context.Context, key string, path string, value interface{}) *redis.StatusCmd
	ping(ctx context.Context) *redis.StatusCmd
	pipeline() redis.Pipeliner
	pipelined(ctx context.Context, fn func(redis.Pipeliner) error) ([]redis.Cmder, error)
	set(ctx context.Context, key string, name interface{}, ttl time.Duration) *redis.StatusCmd
	tTL(ctx context.Context, key string) *redis.DurationCmd
	zAdd(ctx context.Context, key string, members ...redis.Z) *redis.IntCmd
	zCard(ctx context.Context, key string) *redis.IntCmd
	zCount(ctx context.Context, key string, min string, max string) *redis.IntCmd
	zRangeByLex(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.StringSliceCmd
	zRangeByScore(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.StringSliceCmd
	zRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.ZSliceCmd
	zRem(ctx context.Context, key string, members ...interface{}) *redis.IntCmd
	zRevRangeByScoreWithScores(ctx context.Context, key string, opt *redis.ZRangeBy) *redis.ZSliceCmd
	zScore(ctx context.Context, key string, member string) *redis.FloatCmd

	//userblocklist.go
	GetUserBlocklist(ctx context.Context, userid string, limit *int64, next *string) (*[]*apipub.BlocklistResponse, string, error)
	AddToUserBlockList(ctx context.Context, blocklist *[]*apipub.BlocklistResponse, ttl time.Duration) error
	RemoveFromUserBlockList(ctx context.Context, blocklist *[]*apipub.BlocklistResponse) error
	UserBlocklistExistsInCache(ctx context.Context, userid string) bool
	ClearUserBlocklist(ctx context.Context, userid string) error
	DoesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error)

	//usercachemeta.go
	GetUserCacheMeta(ctx context.Context, userid string) (*apipub.UserCacheMeta, error)
	SetUserCacheMeta(ctx context.Context, userid string, meta *apipub.UserCacheMeta, ttl time.Duration) error
	UpdateUserCacheMetaTtl(ctx context.Context, userid string, ttl time.Duration) (bool, error)
	GetFirstPartyLookup(ctx context.Context, fpid string, ost apipub.OnlineServiceType) (*string, error)
	SetFirstPartyLookup(ctx context.Context, fpid string, ost apipub.OnlineServiceType, parentid string, ttl time.Duration) (string, error)
	UpdateFirstPartyLookupTtl(ctx context.Context, key string, ttl time.Duration) (bool, error)

	//appidproductid.go
	GetProductIdFromAppId(ctx context.Context, appid string) (*string, error)
	SetAppIdProductId(ctx context.Context, appid string, productid string, ttl time.Duration) error
	GetProductIdToName(ctx context.Context, tenantid string, productid string) (string, error)
	SetProductIdToName(ctx context.Context, tenant string, productid string, name string) error

	// chat.go
	//GetChatMessages(ctx context.Context, tenant string, userid string, limit *int64, next *string) (*[]*apipub.ChatMessage, string, error)
	SetChatMessage(ctx context.Context, tenant string, message *apipub.ChatMessage, ttl time.Duration) error
}

type RedisCache struct {
	serviceStatus   *health.ServiceStatus
	ecWriteClient   *redis.ClusterClient
	ecReadClient    *redis.ClusterClient
	lockClient      *redislock.Client
	expirationLocks []*redislock.Lock
	cfg             *config.Config
	tele            *telemetry.Telemetry
	id              identity.IdentityInterface
}

func NewRedisCache(ctx context.Context, cfg *config.Config, tele *telemetry.Telemetry, id identity.IdentityInterface) *RedisCache {

	var elasticacheWriteClient *redis.ClusterClient
	var elasticacheReadClient *redis.ClusterClient

	log := logger.FromContext(ctx)
	status := &health.ServiceStatus{
		Instances: nil,
		Status:    health.UNKNOWN,
	}

	elasticacheWriteOptions := &redis.ClusterOptions{
		Addrs: []string{cfg.ElasticacheUrl},
	}
	elasticacheReadOptions := &redis.ClusterOptions{
		Addrs: []string{cfg.ElasticacheUrl},
	}

	if utils.IsLocal() {
		elasticacheWriteOptions = &redis.ClusterOptions{
			Addrs: []string{"127.0.0.1:6379", "127.0.0.1:6380", "127.0.0.1:6381"},
		}
		elasticacheReadOptions = &redis.ClusterOptions{
			Addrs:    []string{"127.0.0.1:6379", "127.0.0.1:6380", "127.0.0.1:6381"},
			ReadOnly: true,
		}
		//this is to support CI/CD tests and devcontainer
		if utils.StringContainsSubstr(cfg.DynamoDBURL, "192.168.123.") {
			elasticacheWriteOptions.Addrs = []string{"192.168.123.4:6379", "192.168.123.5:6380", "192.168.123.6:6381"}
			elasticacheReadOptions.Addrs = []string{"192.168.123.4:6379", "192.168.123.5:6380", "192.168.123.6:6381"}
		}
	}

	if len(cfg.ElasticacheUrl) > 0 {
		elasticacheWriteClient = redis.NewClusterClient(elasticacheWriteOptions)
		elasticacheReadClient = redis.NewClusterClient(elasticacheReadOptions)
	} else {
		log.Fatal().Msgf("Redis is required")
	}

	if cfg.DatadogAPMEnabled {
		ddredisv9.WrapClient(elasticacheWriteClient, ddredisv9.WithServiceName(cfg.ElasticacheService))
		ddredisv9.WrapClient(elasticacheReadClient, ddredisv9.WithServiceName(cfg.ElasticacheService))
	}

	if err := elasticacheWriteClient.Ping(ctx).Err(); err != nil {
		log.Error().Err(err).Interface("redisOpts", elasticacheWriteOptions).Msg("Unable to connect to redis node with write permissions")
	}
	if err := elasticacheReadClient.Ping(ctx).Err(); err != nil {
		log.Error().Err(err).Interface("redisOpts", elasticacheWriteOptions).Msg("Unable to connect to redis node with read permissions")
	}

	status.Instances = []*health.InstanceInfo{
		{
			Id:     cfg.ElasticacheUrl,
			Status: health.UNKNOWN,
		},
	}

	locker := redislock.New(elasticacheWriteClient)

	expirationLocks := make([]*redislock.Lock, cfg.RedisExpirationHexMax+1)

	rc := &RedisCache{
		serviceStatus:   status,
		ecWriteClient:   elasticacheWriteClient,
		ecReadClient:    elasticacheReadClient,
		cfg:             cfg,
		tele:            tele,
		id:              id,
		lockClient:      locker,
		expirationLocks: expirationLocks,
	}

	return rc
}

func (rc *RedisCache) IsCritical() bool {
	return false
}

func (rc *RedisCache) CheckHealth() bool {
	svcs := rc.serviceStatus.Instances
	if svcs == nil {
		return false
	}
	primarySvc := svcs[0]
	var replicaSvc *health.InstanceInfo
	if len(svcs) > 1 {
		replicaSvc = svcs[1]
	} else {
		replicaSvc = svcs[0]
	}
	status := health.FAIL
	start := time.Now()

	primaryRes := rc.ping(context.Background())
	replicaRes := rc.ping(context.Background())
	primaryErr := primaryRes.Err()
	replicaErr := replicaRes.Err()
	elapsed := time.Since(start).Milliseconds()

	if primaryErr != nil {
		health.SetInstance(primarySvc, status, start, elapsed, primaryErr.Error())
		zlog.Err(primaryErr).Str("setInstance", "health-check").Msg("failed to ping to redis primary node")
	} else if replicaErr != nil {
		health.SetInstance(replicaSvc, status, start, elapsed, replicaErr.Error())
		zlog.Err(replicaErr).Str("setInstance", "health-check").Msg("failed to ping to redis replica node")
	} else {
		status = health.OK
		health.SetInstance(primarySvc, status, start, elapsed, primaryRes.Val())
		health.SetInstance(replicaSvc, status, start, elapsed, replicaRes.Val())
	}

	rc.serviceStatus.Status = status
	return status == health.OK
}

func (rc *RedisCache) LastStatus() *health.ServiceStatus {
	return rc.serviceStatus
}
