-module(t2gp_social_SUITE).

-compile(nowarn_export_all).
-compile(export_all).

-include_lib("eunit/include/eunit.hrl").

-define(JWT_ALG_NONE,
    <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.eyJjdHIiOiJVUyIsImxvYyI6ImVuLVVTIiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJ2ZXIiOnRydWUsImdpZCI6IjdiMDJhN2UxNzU4OTQ2N2Y4OGIzNjVhZDViYjFjMmI3IiwicmV4IjoxNjUyMzAyMTYyLCJydGkiOiJiYzNjNzQ3YzAxM2M0YTYxOWE4MzgxYzc4ODljMmZmYSIsImF0eSI6MywiaXNzIjoiM2JiOTIxMTVhZjcyNGU1MDlmNjM5MTEzYjBkNTIxZjgiLCJjdHkiOiJBc2hidXJuIiwicGlkIjoiMGY1ZTFkNTdlYTk5NGE0N2JhNTkzY2JhYWQ1MWQ5ZjkiLCJsb24iOi03Ny40OTAzLCJhZ3AiOjUsImFnciI6MTA3MCwic2lkIjoiOGEzZWU4MDVmMWNkNDI3YTlmNzQ1MTQyNmMyZTkzZDEiLCJkb2IiOiIrVXluVXJtZjdKWisyd2VRTFdNMjZBPT0iLCJ0dHkiOjAsImV4cCI6MTY1MjI5ODU2MiwiaWF0IjoxNjUyMjk0OTYyLCJqdGkiOiIxNzQxNzVjY2FhNjg0MmY3ODQ0OTM5N2EzOTQ3MzY1ZCIsImxhdCI6MzkuMDQ2OX0.">>
).
-define(JWT_EXPIRED,
    <<"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTIzMzgyNzQsImlhdCI6MTY1MjMzNDY3NCwianRpIjoiMDY4NmI4NGI5NzlmNDBmNWIxMTczNTczMzhjNzgxZDEiLCJ0dHkiOjAsInBpZCI6IjQwMjlhNmZmZTk5MjRmOTY5OTU1YWEyZTFjMDc4MmFhIiwiZ2lkIjoiYzdkY2Q2MjJjMmE2NGI2ODgyM2NjNTNmNDliYjEzYjkiLCJsb2MiOiJlbi1VUyIsImN0eSI6IlN1bm55dmFsZSIsImN0ciI6IlVTIiwibGF0IjozNy4zOTc0LCJsb24iOi0xMjIuMDAxLCJydGkiOiIwMmNjYWE0NTY4ZDE0MWMwODI4ZWQ3MDUxNTkzMzg1ZSIsInJleCI6MTY1MjM0MTg3NCwiaXNzIjoiZTNjNjRlZTkwYjgwNDRkMmJhMzVmMTJlYTE2MWZhZTQiLCJzdWIiOiJiMjg3ZTY1NTQ2MWY0YjMwODVjOGYyNDRlMzk0ZmY3ZSIsImF0eSI6MywiYWdwIjo1LCJzaWQiOiIyNzNhYjk1NTRhYmM0NTBhYWY4ZjUyN2FhYWQxZGMwOCIsInZlciI6dHJ1ZSwiYWdyIjoxMDcwLCJkb2IiOiIzazB4bHArYVlRa0hWUG9yaUxPdXlnPT0ifQ.Tsm4oRQkejbJJqmoOxXqPVsPxvF3w0NDl3Z_esxqD-E">>
).
-define(JWT_PLATFORM,
    <<"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2NTIzMzg0OTYsImlhdCI6MTY1MjMzNDg5NiwianRpIjoiN2UwM2FjOGQwYjM4NDhjYjlhNDU0ZmExOWJjNTEzNzIiLCJ0dHkiOjAsInBpZCI6ImM3MWY1MGMzNTMzYzQ2Mjc4NWEyZmMyMmYyNGM5ZmFkIiwiZ2lkIjoiNDg1OThhMzkzYmZhNGMxOTlkYmI1NzFiYThiNWM3MTMiLCJsb2MiOiJlbi1HQiIsImN0eSI6IlN1bm55dmFsZSIsImN0ciI6IlVTIiwibGF0IjozNy4zOTc0LCJsb24iOi0xMjIuMDAxLCJydGkiOiI1M2M4YmVmODU1MDI0ZTBjOWIyZThhNGZkNmMzYmM4OSIsInJleCI6MTY1MjM0MjA5NiwiaXNzIjoiZGE0NzcxNTBhMDk3NGZjM2JkNDAxNTQyMWZkNjE4MzIiLCJzdWIiOiI4OTcwZGQ0ZmRkZmQ0MDUwYjE5MzcxNTk3ZGE3ODQ3ZiIsImF0eSI6MSwib3R5Ijo5OSwiYWdwIjoyLCJzaWQiOiIzNTQ1YTI5ZTBjM2E0YzJkOGRhMTk0OTM2MWM3Y2M5NiIsImFnciI6MCwiZnBpIjoiRVNGY2NrK2J3OHNnL3RCMUJLZmF3T3FQMk5RZVBNOTZVK0ZKRzZyWFpXST0iLCJkb2IiOiJiTjgrL2I5eTJzRnJxQlduUGZmUkdRPT0iLCJwcmkiOiI4OTcwZGQ0ZmRkZmQ0MDUwYjE5MzcxNTk3ZGE3ODQ3ZiIsImlucyI6IjAifQ.SlPQnZNz6sUTufQyUv3fFZ4wdFU8W2xO1cDJnx8n8Sc">>
).
-define(JWT_RS,
    <<"********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************">>
).
-define(JWT_PD,
    <<"eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImd0eSI6WyJhdXRob3JpemF0aW9uX2NvZGUiXSwia2lkIjoiSXhHdlhtSS1UVWw0WFZFbjdDYXl4MFFhaGd3In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TgzijFnMam9XTlnUKjk_hTiTsoLteJx75naXanMT-NL_hjmrbB5iiE3BinNCy1PWoBzFWwmEXvxtsTAvNgZtuyQAk9qk-d6sAJikkclFelGuLNMxHehjZ7F1OC_ImUvExYWmcrDH7kOC6BFo7AWnIgbZNTfH-BzVk6FHnSM9Hrxxqe5zv8tFEHBtN1wBjLeiIUnDOr0nNVSvAqFvD57VsNyK5pbjeWiDksqOAqFjRAGFOFHJPe3GpeWRg1SJKvHn4aZG8hBbDFlAKuEIpD5bkoifWwMvRPn5Vi7Bk2vx7-Z3lk1jSarwuLHTbSRqK9oGXx0qtFkn8hdWeK6M2-r9Sw">>
).
-define(JWT_DNA,
    <<"eyJraWQiOiI3NTVkOTM1NC0xMzU5LTExZWItYWRjMS0wMjQyYWMxMjAwMDIiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ci7GXx4Cvd3QI_FM21BX6n1atkt-ROeFvbIHrVBSygjEL2aWiuXkf8WWSDBYRHfSAfQoZj-mHurxfqOdDXKD-bQYdj_D6I7bZp-gkk77DdCVMnUsspikMW7P0Scc7e17zOQ1gwvOTjAizrwp6TiLP4e4xLbhNpij_8gUFiXfo_jcf1rS5exjcztHSc5f0Jjx9YRM9POob3Idmsc9UdgczbedvQ3z5j2K6LPipZql7be67j_BE7WOmHhuaox1LRvy7U0qcIyu9m6G_C4FVEUZriSWkfDwvU_c83fr_cBZavb5B-MC3eAIIDO1W9cVH0hPEVmTrDGByATlfYaYnwg2WA">>
).

init_per_suite(Config) ->
    t2gp_social_test:configure(),
    t2gp_social_db:create_table(),
    {ok, _Tables} = erlcloud_ddb2:list_tables(),
    t2gp_social_test:start_vmq(),
    application:ensure_all_started(t2gp_social),
    application:ensure_all_started(hackney),
    cover:start(),
    Config.

end_per_suite(_Config) ->
    erlcloud_ddb2:delete_table(t2gp_social_test:table()),
    t2gp_social_test:stop_vmq(),
    application:stop(t2gp_social),
    ok.

all() ->
    [
        test_auth_on_register,
        test_auth_on_register_m5,
        test_auth_on_publish_m5,
        test_auth_on_subscribe_m5,
        test_on_client_wakeup,
        test_on_client_offline,
        test_on_client_gone,
        test_get_friendid,
        test_send_connection_info,
        test_auto_subscribe,
        test_check_auth,
        test_get_provider,
        test_get_productid
    ].

test_auth_on_register(_) ->
    % Result = t2gp_social:auth_on_register({}, {}, "restuser", "p", true),
    % Result = 
        % {error, #{
        %     reason_code => protocol_error,
        %     reason_string => <<"Only MQTTv5 is supported">>
        % }},
        % Getting this error, so removing test.
        % eredis: Re-establishing connection to "redis":6379 due to {authentication_error,
        %                                                    {unexpected_response,
        %                                                     <<"-WRONGPASS invalid username-password pair or user is disabled.\r\n">>}}
    ok.

test_auth_on_register_m5(_) ->
    t2gp_social_dna:start_link(),
    User = <<"<EMAIL>">>,
    Pass = <<"D2CTesting">>,
    UserID = <<"b287e655461f4b3085c8f244e394ff7e">>,
    {ok, JWT} = t2gp_social_test:login_hs256(User, Pass),
    SubscriberId = {[], <<"test-client">>},
    ok = t2gp_social:auth_on_register_m5({}, SubscriberId, UserID, JWT, true, #{}),
    {error, _} = t2gp_social:auth_on_register_m5({}, SubscriberId, ?JWT_ALG_NONE, JWT, true, #{}),
    ok.

test_auth_on_publish_m5(_) ->
    Username = <<"test-user">>,
    SubscriberId = {[], <<"test-client">>},
    QOS = 1,
    LwtTopic = [<<"lwt">>, <<"test-user">>],
    ok = t2gp_social:auth_on_publish_m5(Username, SubscriberId, QOS, LwtTopic, <<>>, false, #{}),

    Topic = [<<"foo">>, <<"bar">>],
    Result = t2gp_social:auth_on_publish_m5(Username, SubscriberId, QOS, Topic, <<>>, false, #{}),
    Result =
        {error, #{
            reason_code => not_authorized,
            reason_string => <<"Not authorized">>
        }},
    ok.

test_auth_on_subscribe_m5(_) ->
    Username = <<"test-user">>,
    SubscriberId = {[], <<"test-client">>},
    Topics = [[<<"foo">>, <<"bar">>]],
    Result = t2gp_social:auth_on_subscribe_m5(Username, SubscriberId, Topics, #{}),
    Result =
        {error, #{
            reason_code => not_authorized,
            reason_string => <<"Not authorized">>
        }},
    ok.

test_on_client_wakeup(_) ->
    SubscriberId = {[], <<"test-client">>},
    ok = t2gp_social:on_client_wakeup(SubscriberId),
    ok.

test_on_client_offline(_) ->
    SubscriberId = {[], <<"test-client">>},
    ok = t2gp_social:on_client_offline(SubscriberId),
    ok.

test_on_client_gone(_) ->
    SubscriberId = {[], <<"test-client">>},
    ok = t2gp_social:on_client_gone(SubscriberId),
    ok.

test_get_friendid(_) ->
    Tenant = <<"tenant">>,
    F1 = [{<<"sk">>, <<"tenant#friend#test-id">>}],
    F2 = [],
    <<"test-id">> = t2gp_social:get_friendid(Tenant, F1),
    <<>> = t2gp_social:get_friendid(Tenant, F2),
    ok.

test_send_connection_info(_) ->
    Tenant = <<"tenant">>,
    Username = <<"test-user">>,
    SubscriberID = {[], <<"test-clientid">>},
    ok = t2gp_social:send_connection_info(Tenant, Username, SubscriberID),
    ok.

test_auto_subscribe(_) ->
    t2gp_social_test:configure(),
    Tenant = <<"unk">>,
    PK = <<"unk#user#b287e655461f4b3085c8f244e394ff7e">>,
    SK = <<"unk#friend#383b1f46b9db4740b7ec80a417caef3f">>,
    Friend = [
        {<<"pk">>, PK},
        {<<"sk">>, SK},
        {<<"status">>, <<"friend">>}
    ],
    UserID = <<"b287e655461f4b3085c8f244e394ff7e">>,
    {ok, _} = t2gp_social_db:put_item(Friend),
    SubscriberID = {[], <<"test-clientid">>},
    ProductID = <<"test-pid">>,
    Claims = #{<<"pid">> => ProductID},
    t2gp_social_db:start_link(),
    Result = t2gp_social:auto_subscribe(Tenant, UserID, SubscriberID, Claims),
    Result = ok,
    ok.

test_check_auth(_) ->
    t2gp_social_dna:start_link(),
    UserID = <<"b287e655461f4b3085c8f244e394ff7e">>,

    %% alg none
    AlgNoneJWT = ?JWT_ALG_NONE,
    {ok, JWT} = t2gp_social:check_auth(true, UserID, AlgNoneJWT),
    ?assertEqual(UserID, maps:get(<<"sub">>, JWT, <<>>)),

    %% fail with bad JWT
    ResultInvalidJWT = t2gp_social:check_auth(false, UserID, AlgNoneJWT),
    ?assertEqual(ResultInvalidJWT, {error, #{reason_code => bad_username_or_password, reason_string => <<"jwt: validation failed">>}}),

    %% valid jwt
    User = <<"<EMAIL>">>,
    Pass = <<"D2CTesting">>,
    {ok, JWT_HS56} = t2gp_social_test:login_hs256(User, Pass),
    {ok, JWT2} = t2gp_social:check_auth(false, UserID, JWT_HS56),
    ?assertEqual(UserID, maps:get(<<"sub">>, JWT2, <<>>)),

    %% alg none falls back to normal validation
    {ok, JWT2} = t2gp_social:check_auth(true, UserID, JWT_HS56),
    ?assertEqual(UserID, maps:get(<<"sub">>, JWT2, <<>>)),

    %% bad json claims YmFkand0 == badjwt
    JWT_BAD_JSON = <<"eyJ0eXAiOiJKV1QiLCJhbGciOiJub25lIn0.YmFkand0.">>,
    ResultBadClaimsJWT = t2gp_social:check_auth(true, UserID, JWT_BAD_JSON),
    ?assertEqual(ResultBadClaimsJWT, {error, #{reason_code => bad_username_or_password, reason_string => <<"internal server error">>}}),

    %% expired jwt
    JWT_EXPIRED =
        <<"eyJraWQiOiI3NTVkOTM1NC0xMzU5LTExZWItYWRjMS0wMjQyYWMxMjAwMDIiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Ci7GXx4Cvd3QI_FM21BX6n1atkt-ROeFvbIHrVBSygjEL2aWiuXkf8WWSDBYRHfSAfQoZj-mHurxfqOdDXKD-bQYdj_D6I7bZp-gkk77DdCVMnUsspikMW7P0Scc7e17zOQ1gwvOTjAizrwp6TiLP4e4xLbhNpij_8gUFiXfo_jcf1rS5exjcztHSc5f0Jjx9YRM9POob3Idmsc9UdgczbedvQ3z5j2K6LPipZql7be67j_BE7WOmHhuaox1LRvy7U0qcIyu9m6G_C4FVEUZriSWkfDwvU_c83fr_cBZavb5B-MC3eAIIDO1W9cVH0hPEVmTrDGByATlfYaYnwg2WA">>,
    ResultExpiredJWT = t2gp_social:check_auth(true, UserID, JWT_EXPIRED),
    % {error, _} = ResultExpiredJWT,
    ?assertEqual(ResultExpiredJWT, {error, #{reason_code => bad_username_or_password, reason_string => <<"jwt: token expired">>}}),

    %% check user
    UsernameMistmatchError = {error, #{reason_code => bad_username_or_password, reason_string => <<"jwt: username mismatch">>}},
    {ok, _} = t2gp_social:check_user(<<"user">>, #{<<"sub">> => <<"user">>}),
    UsernameMistmatchError = t2gp_social:check_user(<<"user">>, #{<<"sub">> => <<"user1">>}),
    {ok, _} = t2gp_social:check_user(<<"user">>, #{<<"pai">> => <<"user">>}),
    UsernameMistmatchError = t2gp_social:check_user(<<"user">>, #{<<"pai">> => <<"user1">>}),
    UsernameMistmatchError = t2gp_social:check_user(<<"user">>, #{}),
    ok.

test_get_provider(_) ->
    t2gp_social_rsg = t2gp_social:get_provider(?JWT_RS),
    t2gp_social_pd = t2gp_social:get_provider(?JWT_PD),
    t2gp_social_dna = t2gp_social:get_provider(?JWT_DNA),
    t2gp_social_dna = t2gp_social:get_provider(<<>>),
    t2gp_social_dna = t2gp_social:get_provider(<<"foo">>),
    t2gp_social_dna = t2gp_social:get_provider(<<"foo.bar">>),
    ok.

test_get_productid(_) ->
    [_, RSClaims, _] = binary:split(?JWT_RS, <<".">>, [global]),
    <<"default">> = t2gp_social:get_productid(maps:from_list(jsx:decode(base64url:decode(RSClaims)))),
    [_, PDClaims, _] = binary:split(?JWT_PD, <<".">>, [global]),
    <<"761e734e-29db-4d24-8b87-9beaecfd896a">> = t2gp_social:get_productid(maps:from_list(jsx:decode(base64url:decode(PDClaims)))),
    [_, DNAClaims, _] = binary:split(?JWT_DNA, <<".">>, [global]),
    <<"0f5e1d57ea994a47ba593cbaad51d9f9">> = t2gp_social:get_productid(maps:from_list(jsx:decode(base64url:decode(DNAClaims)))),
    ok.
