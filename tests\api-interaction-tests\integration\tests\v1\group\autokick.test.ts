import * as socialApi from '../../../lib/social-api';
import { config, TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

/**
 * NOTE
 *
 * When token expiry works correctly it should expire properly within 15s of the scheduled time.
 * The minimum supported presence expiry is 35s + 15s (50s)
 *
 * Presence expiry remove a user from all groups for a product.
 * What happens when that user leaves a group (as a member, as a leader, promotion, etc.) are the same as the case when leaving group with API calls.
 *
 * user should get groups update within his app/product (i.e. same product id as his token)
 *
 */

let usersTwok: TwokAccounts;
// populate member labels
let memberCnt: number = 2;
let memberLabels: string[] = ["leader"];
for (let i = 1; i <= memberCnt; i++) {
  memberLabels.push("member" + i.toString());
}

// user updates keepalive time to be
let keepalive = 35;
// extra time in seconds allowed for presence to become expired
let maxSlack = 15;
// timeout in ms for each test. Adjust according to keepalive
let testTimeout = 100000;

// game name for presence
let gameName = "Automated API Tests Game Name";

// array of AppInfo of apps/products without presence changes
let appInfoArrayNoPc = [
  config.apps.ghostPepper,
  config.apps.socialServiceProduct
];

// AppInfo of the app/product with presence changes
let appInfoPc = config.apps.steam;

// map of app ID to group ID
let groupIdMap: {[key: string]: string[]} = {};

// array of all app IDs: both apps with and without presence changes.
// namely: appInfoPc and apps in appInfoArrayNoPc
let appIdArray: string[] = [];

appIdArray.push(appInfoPc.appId);

for (let appInfo of appInfoArrayNoPc) {
  appIdArray.push(appInfo.appId);
}

// number of groups per app
let groupCnt: number = 3;


beforeAll(async () => {
  usersTwok = new TwokAccounts(memberLabels.length, memberLabels);
  await usersTwok.loginAll({ appIdArray: appIdArray });
});

afterAll(async () => {
  await usersTwok.logoutAll({ appIdArray: appIdArray });
});

beforeEach(async () => {
  // create groups for each app
  for (let appId of appIdArray) {
    groupIdMap[appId] = [];
    for (let i = 0; i < groupCnt; i++) {
      const r = await socialApi.createGroupV1(
        usersTwok.acct["leader"],
        {
          maxMembers: 6,
          joinRequestAction: 'manual',
          canCrossPlay: true
        },
        appId
      );
      socialApi.testStatus(StatusCodes.CREATED, r);

      groupIdMap[appId].push(socialApi.getGroupId(r));
    }
  }
});

afterEach(async () => {
  // disband each group and clean up groupIdMap
  for (let [appId, groupIdArray] of Object.entries(groupIdMap)) {
    for (let groupId of groupIdArray) {
      await socialApi.deleteGroup(
        usersTwok.acct["leader"],
        groupId,
        appId
      );
    }

    delete groupIdMap[appId];
  }
});

describe('', () => {
  it('leader is autokicked from groups of only himself[public v1]', async () => {
    let testCase = {
      description: `create groups in multiple products; leader is the only group member;
leader updates presence in a product; verify group member after leader presence expiry`,
      expected: `the groups of the product that leader's presence expired are empty;
the groups of the other products remain`,
    };

    // leader sets presence in the product with presence changes
    const r = await socialApi.setPresenceV1(
      usersTwok.acct["leader"],
      {
        keepAliveFor: keepalive,
        status: "chat",
        gameName: gameName
      },
      appInfoPc.appId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // let presence keepalive expire
    await new Promise(r => setTimeout(r, keepalive * 1000));

    // some extra amount of time is allowed before group member is autokicked when presence expires
    await socialApi.waitWhile(async () => {
      let g = await socialApi.getGroupsInfo(
        usersTwok.acct["leader"],
        appInfoPc.appId
      );
      return (g.body.items.length != 0);
    }, maxSlack, 1000);

    // check the groups of the product with presence changes
    for (let groupId of groupIdMap[appInfoPc.appId]) {
      const actualGroupInfo = await socialApi.getGroupInfo(
        usersTwok.acct["leader"],
        groupId,
        appInfoPc.appId
      );

      // expect the groups to be disbanded since the leader is autokicked from a group of himself.
      socialApi.testStatus(StatusCodes.NOT_FOUND, actualGroupInfo);
    }

    // check the groups of the products without presence changes
    for (let appInfoNoPc of appInfoArrayNoPc) {
      for (let groupId of groupIdMap[appInfoNoPc.appId]) {
        const actualGroupInfo = await socialApi.getGroupInfo(
          usersTwok.acct["leader"],
          groupId,
          appInfoNoPc.appId
        );
        socialApi.testStatus(StatusCodes.OK, actualGroupInfo);

        let expMemberArray = [
          expect.objectContaining({
            groupid: groupId,
            role: "leader",
            userid: usersTwok.acct["leader"].publicId,
            productid: appInfoNoPc.productId,
          })
        ];

        const expectedGroupInfo = {
          status: StatusCodes.OK,
          body: {
            members: expMemberArray
          },
        };

        socialApi.expectMore(
          () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
          testCase,
          {
            resp: actualGroupInfo,
            additionalInfo: {
              "fail reason": "unexpected group member info",
            },
          }
        );
      }
    }
  }, testTimeout);
});

describe('[public v1]', () => {
  // add a member to a group
  async function inviteAndAccept(
    gid: string,
    invitedLabel: string,
    appId: string
  ) {
    let r = await socialApi.inviteV1(
      usersTwok.acct["leader"],
      gid,
      { memberid: usersTwok.acct[invitedLabel].publicId, status: 'invited' },
      appId
    );
    socialApi.testStatus(StatusCodes.CREATED, r);

    r = await socialApi.acceptInviteV1(
      usersTwok.acct[invitedLabel],
      gid,
      {
        approverid: usersTwok.acct["leader"].publicId,
        memberid: usersTwok.acct[invitedLabel].publicId
      },
      appId
    );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  beforeEach(async () => {
    for (let [appId, groupIdArray] of Object.entries(groupIdMap)) {
      for (let groupId of groupIdArray) {
        for (let i = 1; i <= memberCnt; i++) {
          await inviteAndAccept(
            groupId,
            memberLabels[i],
            appId
          );
        }
      }
    }
  }, 25000);  // This beforeEach takes extra long and needs longer timeout

  describe('', () => {
    afterEach(async () => {
      // for the test case with member promotion
      for (let [appId, groupIdArray] of Object.entries(groupIdMap)) {
        for (let groupId of groupIdArray) {
          await socialApi.deleteGroup(
            usersTwok.acct["member1"],
            groupId,
            appId
          );
        }
      }
    });

    it('leader is autokicked, member is promoted to be leader', async () => {
      let testCase = {
        description: `leader updates his/her presence in a product;
  verify group members after leader presence expiry`,
        expected: `the leader is removed from the groups of the product and the member
  is promoted to be leader;
  group members remain at original roles in the groups of the other products`,
      };

      // leader sets presence in the product with presence changes
      const r = await socialApi.setPresenceV1(
        usersTwok.acct["leader"],
        {
          keepAliveFor: keepalive,
          status: "chat",
          gameName: gameName
        },
        appInfoPc.appId
      );
      socialApi.testStatus(StatusCodes.OK, r);

      // let presence keepalive expire
      await new Promise(r => setTimeout(r, keepalive * 1000));

      // some extra amount of time is allowed before group member is autokicked when presence expires
      await socialApi.waitWhile(async () => {
        let g = await socialApi.getGroupsInfo(
          usersTwok.acct["leader"],
          appInfoPc.appId
        );
        return (g.body.items.length != 0);
      }, maxSlack, 1000);

      // check the groups of the product with presence changes
      for (let groupId of groupIdMap[appInfoPc.appId]) {
        // use member1 since leader is already autokicked
        let actualGroupInfo = await socialApi.getGroupInfo(
          usersTwok.acct["member1"],
          groupId,
          appInfoPc.appId
        );
        socialApi.testStatus(StatusCodes.OK, actualGroupInfo);

        // expect member1 to be promoted to the leader
        let expMemberArray = [
          expect.objectContaining({
            groupid: groupId,
            role: "leader",
            userid: usersTwok.acct["member1"].publicId,
            productid: appInfoPc.productId,
          })
        ];

        const expectedMemberInfo = {
          status: StatusCodes.OK,
          body: {
            members: expect.arrayContaining(expMemberArray),
          },
        };

        socialApi.expectMore(
          () => {expect(actualGroupInfo).toMatchObject(expectedMemberInfo)},
          testCase,
          {
            resp: actualGroupInfo,
            additionalInfo: {
              "fail reason": "unexpected group member info",
            },
          }
        );
      }

      // check the groups of the products without presence changes
      for (let appInfoNoPc of appInfoArrayNoPc) {
        for (let groupId of groupIdMap[appInfoNoPc.appId]) {
          // use member1 since leader is already autokicked
          let actualGroupInfo = await socialApi.getGroupInfo(
            usersTwok.acct["member1"],
            groupId,
            appInfoNoPc.appId
          );
          socialApi.testStatus(StatusCodes.OK, actualGroupInfo);

          // expect member1 to remain in the original role
          let expMemberArray = [
            expect.objectContaining({
              groupid: groupId,
              role: "member",
              userid: usersTwok.acct["member1"].publicId,
              productid: appInfoNoPc.productId,
            })
          ];

          const expectedMemberInfo = {
            status: StatusCodes.OK,
            body: {
              members: expect.arrayContaining(expMemberArray),
            },
          };

          socialApi.expectMore(
            () => {expect(actualGroupInfo).toMatchObject(expectedMemberInfo)},
            testCase,
            {
              resp: actualGroupInfo,
              additionalInfo: {
                "fail reason": "unexpected group member info",
              },
            }
          );
        }
      }
    }, testTimeout);
  });

  it('member is autokicked from groups', async () => {
    let testCase = {
      description: `member updates his/her presence in a product;
verify group members after the member presence expiry`,
      expected: `the member is removed from the groups of the product;
member remains in the groups of the other products`,
    };

    // member sets presence in the product with presence changes
    const r = await socialApi.setPresenceV1(
      usersTwok.acct["member2"],
      {
        keepAliveFor: keepalive,
        status: "chat",
        gameName: gameName
      },
      appInfoPc.appId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // let presence keepalive expire
    await new Promise(r => setTimeout(r, keepalive * 1000));

    // some extra amount of time is allowed before group member is autokicked when presence expires
    await socialApi.waitWhile(async () => {
      let g = await socialApi.getGroupsInfo(
        usersTwok.acct["member2"],
        appInfoPc.appId
      );
      return (g.body.items.length != 0);
    }, maxSlack, 1000);

    // check the groups of the product with presence changes
    for (let groupId of groupIdMap[appInfoPc.appId]) {
      let actualGroupInfo = await socialApi.getGroupInfo(
        usersTwok.acct["leader"],
        groupId,
        appInfoPc.appId
      );
      socialApi.testStatus(StatusCodes.OK, actualGroupInfo);

      let expMemberArray = [
        expect.objectContaining({
          groupid: groupId,
          role: 'member',
          userid: usersTwok.acct["member2"].publicId,
          productid: appInfoPc.productId,
        })
      ];

      // expect member is removed from the groups
      const expectedMemberInfo = {
        status: StatusCodes.OK,
        body: {
          members: expect.not.arrayContaining(expMemberArray),
        },
      };
      socialApi.expectMore(
        () => {expect(actualGroupInfo).toMatchObject(expectedMemberInfo)},
        testCase,
        {
          resp: actualGroupInfo,
          additionalInfo: {
            "fail reason": "group member not autokicked when his/her presence expired",
          },
        }
      );
    }

    // check the groups of the products without presence changes
    for (let appInfoNoPc of appInfoArrayNoPc) {
      for (let groupId of groupIdMap[appInfoNoPc.appId]) {

        let actualGroupInfo = await socialApi.getGroupInfo(
          usersTwok.acct["leader"],
          groupId,
          appInfoNoPc.appId
        );
        socialApi.testStatus(StatusCodes.OK, actualGroupInfo);

        let expMemberArray = [
          expect.objectContaining({
            groupid: groupId,
            role: 'member',
            userid: usersTwok.acct["member2"].publicId,
            productid: appInfoNoPc.productId,
          })
        ];

        // expect member to remain in the groups
        const expectedMemberInfo = {
          status: StatusCodes.OK,
          body: {
            members: expect.arrayContaining(expMemberArray),
          },
        };

        socialApi.expectMore(
          () => {expect(actualGroupInfo).toMatchObject(expectedMemberInfo)},
          testCase,
          {
            resp: actualGroupInfo,
            additionalInfo: {
              "fail reason": "unexpected group member info",
            },
          }
        );
      }
    }
  }, testTimeout);
});