<script lang="ts">
  export let disabled = false;
  export let outline = false;
  export let className = '';
  export let loading = false;
  export let showIcon = false;

  import { createEventDispatcher } from 'svelte';
  import LoadingSpinner from '../LoadingSpinner/LoadingSpinner.svelte';

  const dispatch = createEventDispatcher();

  const onButtonClick: svelte.JSX.EventHandler<
    MouseEvent,
    HTMLButtonElement
  > = event => {
    dispatch('buttonClick', {
      event,
    });
  };
</script>

<style>
  .button {
    display: inline-flex;
    position: relative;
    padding: 0.5rem 1rem;
    margin: 0;
    text-align: center;
    color: var(--social-color, var(--default-color));
    border-radius: 0.125rem;
    overflow: hidden;
    vertical-align: middle;
    cursor: pointer;
    transition: background-color 0.1s ease-out;
    background-color: var(
      --social-bg-color-button,
      var(--default-bg-color-button)
    );
    border-color: var(--social-bg-color-button, var(--default-bg-color-button));
    color: var(--social-color, var(--default-color));
    backdrop-filter: blur(4px);
    min-width: unset;
    min-height: unset;
  }

  .button::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    pointer-events: none;
    background-color: #ffffff;
    opacity: 0;
    transition: opacity 0.1s ease-out;
  }

  .button .content {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .button:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }

  .button:enabled:hover {
    cursor: pointer;
  }

  .button:enabled:hover::after {
    opacity: 0.2;
  }

  .button:enabled:active::after {
    opacity: 0.25;
  }

  .button.outline {
    background-color: transparent;
    border-color: #ffffff;
    border-width: 1px;
    border-style: solid;
  }

  .button .content .label {
    font-family: Montserrat;
    font-style: normal;
    font-weight: 700;
    font-size: 0.875rem;
    line-height: 150%;
    text-transform: none;
  }

  .button .content > *:nth-child(2) {
    margin-inline-start: auto;
  }

  .icon {
    width: 1rem;
    height: 1rem;
    margin-inline-end: 0.5rem;
  }

  .icon :global(svg) {
    width: 1rem;
    height: 1rem;
  }

  .loading-container {
    position: absolute;
  }

  .icon.loading,
  .label.loading {
    visibility: hidden;
  }
</style>

<button
  class="button {className}"
  class:outline
  type="button"
  disabled="{disabled}"
  on:click="{onButtonClick}"
>
  <div class="content">
    {#if loading}
      <span class="loading-container">
        <LoadingSpinner size="{18}" />
      </span>
    {/if}
    {#if showIcon}
      <span class="icon" class:loading>
        <slot name="icon" />
      </span>
    {/if}

    <span class="label" class:loading>
      <slot name="label" />
    </span>
    <slot />
  </div>
</button>
