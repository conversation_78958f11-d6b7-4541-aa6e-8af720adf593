package api

//
//import (
//	"errors"
//	"github.com/DataDog/datadog-go/v5/statsd"
//	"github.com/aws/aws-sdk-go-v2/aws"
//	"github.com/franela/goblin"
//	. "github.com/onsi/gomega"
//	"github.com/segmentio/encoding/json"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
//	"go.uber.org/mock/gomock"
//	"net/http"
//	"testing"
//	"time"
//)
//
//func TestCreateGroupServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	//groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
//	groupID := utils.GenerateNewULID()
//	productID := "c71f50c3533c462785a2fc22f24c9fad"
//	//userID := "b287e655461f4b3085c8f244e394ff7e"
//	userID := utils.GenerateRandomDNAID()
//
//	groupReq := apitrusted.CreateGroupRequest{
//		MaxMembers:        aws.Int(30),
//		JoinRequestAction: apitrusted.AutoApprove,
//		CanCrossPlay:      aws.Bool(true),
//		CanMembersInvite:  aws.Bool(false),
//		Password:          aws.String(""),
//	}
//	canXplay := true
//	var groupMember apipub.GroupMember
//	groupMember.Userid = userID
//	var groupMembers []apipub.GroupMember
//	groupMembers = append(groupMembers, groupMember)
//	expectedBody := &apipub.Group{
//		Groupid:      groupID,
//		Productid:    productID,
//		MaxMembers:   30,
//		Created:      nil,
//		CanCrossPlay: &canXplay,
//	}
//	expectedBody.Members = &groupMembers
//
//	reqBody := apitrusted.ServerCreateGroupRequest{
//		GroupLeader:       userID,
//		GroupRequest:      groupReq,
//		OnlineServiceType: 24,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	profile := apipub.UserProfile{Userid: userID}
//
//	g.Describe("ServerCreateGroup", func() {
//		g.BeforeEach(func() {
//			g.Timeout(45 * time.Second)
//			rc.ClearAllMemberships(ctx, userID, productID, groupID)
//			rc.DeleteUserGroupIdxs(ctx, productID, groupID, userID)
//		})
//		g.AfterEach(func() {
//			group := &apipub.Group{
//				Groupid:   groupID,
//				Productid: productID,
//			}
//			rc.DeleteGroup(ctx, group)
//			rc.DeleteUserGroupIdxs(ctx, productID, groupID, userID)
//		})
//		g.It("should successfully create a group", func() {
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			// mock.Ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
//			// mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).AnyTimes()
//			mock.rc.EXPECT().SetGroup(gomock.Any(), gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//			mock.apiTrusted.SocialApi.Cfg.MaxGroups = 3
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var group apipub.Group
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &group)
//			g.Assert(group.MaxMembers).Equal(30)
//			g.Assert(group.Productid).Equal(productID)
//		})
//
//		g.It("failed to create a group because Count User Groups returned an error", func() {
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
//			w2, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w2, req)
//			g.Assert(w2.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("failed to create a group empty groupLeader error", func() {
//			requestEmptyUser := apitrusted.ServerCreateGroupRequest{
//				GroupRequest:      groupReq,
//				OnlineServiceType: 24,
//			}
//
//			w, req := AddBodyToRequestBasic(requestEmptyUser, productID, "pass")
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, req, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//	})
//}
//
//func TestCreateGroupServerWithMembers(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
//	productID := "c71f50c3533c462785a2fc22f24c9fad"
//	userID := "b287e655461f4b3085c8f244e394ff7e"
//	userID2 := "c287e655461f4b3085c8f244e394ff7e"
//	userID3 := "d287e655461f4b3085c8f244e394ff7e"
//
//	groupReq := apitrusted.CreateGroupRequest{
//		MaxMembers:        aws.Int(30),
//		JoinRequestAction: apitrusted.AutoApprove,
//		CanCrossPlay:      aws.Bool(true),
//		CanMembersInvite:  aws.Bool(false),
//		Password:          aws.String(""),
//	}
//	canXplay := true
//	var groupMember apipub.GroupMember
//	groupMember.Userid = userID
//	groupMember.Role = apipub.Leader
//	var groupMembers []apipub.GroupMember
//	groupMembers = append(groupMembers, groupMember)
//	expectedBody := &apipub.Group{
//		Groupid:      groupID,
//		Productid:    productID,
//		MaxMembers:   3,
//		Created:      nil,
//		CanCrossPlay: &canXplay,
//	}
//	expectedBody.Members = &groupMembers
//
//	var addMembers []apitrusted.GroupMemberAddObject
//	addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//		Memberid:          userID2,
//		OnlineServiceType: 2,
//		CanCrossPlay:      canXplay,
//	})
//	addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//		Memberid:          userID3,
//		OnlineServiceType: 3,
//		CanCrossPlay:      canXplay,
//	})
//
//	reqBody := apitrusted.ServerCreateGroupRequest{
//		GroupLeader:       userID,
//		GroupRequest:      groupReq,
//		OnlineServiceType: 24,
//		GroupMembers:      &addMembers,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	profile := apipub.UserProfile{
//		Userid:      userID,
//		DisplayName: aws.String("User1"),
//	}
//
//	profile2 := apipub.UserProfile{
//		Userid:      userID2,
//		DisplayName: aws.String("User2"),
//	}
//
//	profile3 := apipub.UserProfile{
//		Userid:      userID3,
//		DisplayName: aws.String("User3"),
//	}
//
//	group := apipub.Group{
//		Groupid:      groupID,
//		Productid:    productID,
//		Members:      &groupMembers,
//		MaxMembers:   3,
//		CanCrossPlay: &canXplay,
//	}
//
//	g.Describe("ServerCreateGroup with members", func() {
//		g.It("should successfully create a crossplay group", func() {
//			g.Timeout(30 * time.Second)
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user3
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID3).Return(&profile3, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID3, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID3, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile3, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apipub.Group
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//		})
//
//		g.It("should create a crossplay group bad CanCrossPlay on member - no error returned", func() {
//			g.Timeout(20 * time.Second)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 2,
//				CanCrossPlay:      false,
//			})
//
//			reqBody = apitrusted.ServerCreateGroupRequest{
//				GroupLeader:       userID,
//				GroupRequest:      groupReq,
//				OnlineServiceType: 24,
//				GroupMembers:      &addMembers,
//			}
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert(len(*groupCheck.Members)).Equal(1)
//			g.Assert(groupCheck.Errors).IsNil()
//		})
//
//		g.It("should create a crossplay group bad CanCrossPlay on member - error returned", func() {
//			g.Timeout(20 * time.Second)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 2,
//				CanCrossPlay:      false,
//			})
//
//			reqBody = apitrusted.ServerCreateGroupRequest{
//				GroupLeader:            userID,
//				GroupRequest:           groupReq,
//				OnlineServiceType:      24,
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert(len(*groupCheck.Members)).Equal(1)
//			g.Assert(groupCheck.Errors).IsNotNil()
//			g.Assert((*groupCheck.Errors)[0].Memberid).Equal(userID2)
//			//these both need to get converted to uint, otherwise the tests complain
//			g.Assert(uint((*groupCheck.Errors)[0].Error.Code)).Equal(uint(http.StatusForbidden))
//			g.Assert(uint((*groupCheck.Errors)[0].Error.ErrorCode)).Equal(uint(errs.EGroupsCrossplayValidationFailed))
//		})
//
//		g.It("should successfully create a non-crossplay group", func() {
//			g.Timeout(20 * time.Second)
//
//			groupReq = apitrusted.CreateGroupRequest{
//				MaxMembers:        aws.Int(30),
//				JoinRequestAction: apitrusted.AutoApprove,
//				CanCrossPlay:      aws.Bool(false),
//				CanMembersInvite:  aws.Bool(false),
//				Password:          aws.String(""),
//			}
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			reqBody = apitrusted.ServerCreateGroupRequest{
//				GroupLeader:            userID,
//				GroupRequest:           groupReq,
//				OnlineServiceType:      24,
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//		})
//
//		g.It("should successfully create a non-crossplay group with error for one member", func() {
//			g.Timeout(20 * time.Second)
//
//			groupReq = apitrusted.CreateGroupRequest{
//				MaxMembers:        aws.Int(30),
//				JoinRequestAction: apitrusted.AutoApprove,
//				CanCrossPlay:      aws.Bool(false),
//				CanMembersInvite:  aws.Bool(false),
//				Password:          aws.String(""),
//			}
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID3,
//				OnlineServiceType: 3,
//				CanCrossPlay:      false,
//			})
//
//			reqBody = apitrusted.ServerCreateGroupRequest{
//				GroupLeader:            userID,
//				GroupRequest:           groupReq,
//				OnlineServiceType:      24,
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user3
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID3).Return(&profile3, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID3, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert((*groupCheck.Errors)[0].Memberid).Equal(userID3)
//			//these both need to get converted to uint, otherwise the tests complain
//			g.Assert(uint((*groupCheck.Errors)[0].Error.Code)).Equal(uint(http.StatusForbidden))
//			g.Assert(uint((*groupCheck.Errors)[0].Error.ErrorCode)).Equal(uint(errs.EGroupsCrossplayValidationFailed))
//		})
//
//		g.It("should successfully create a crossplay group with error for platform only member", func() {
//			g.Timeout(20 * time.Second)
//
//			groupReq = apitrusted.CreateGroupRequest{
//				MaxMembers:        aws.Int(30),
//				JoinRequestAction: apitrusted.AutoApprove,
//				CanCrossPlay:      aws.Bool(true),
//				CanMembersInvite:  aws.Bool(false),
//				Password:          aws.String(""),
//			}
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID3,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			reqBody = apitrusted.ServerCreateGroupRequest{
//				GroupLeader:            userID,
//				GroupRequest:           groupReq,
//				OnlineServiceType:      24,
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			profile3 = apipub.UserProfile{
//				Userid: userID3,
//				//no display name returned = platform account
//				//DisplayName: aws.String("User3"),
//			}
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user3
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID3).Return(&profile3, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert((*groupCheck.Errors)[0].Memberid).Equal(userID3)
//			//these both need to get converted to uint, otherwise the tests complain
//			g.Assert(uint((*groupCheck.Errors)[0].Error.Code)).Equal(uint(http.StatusForbidden))
//			g.Assert(uint((*groupCheck.Errors)[0].Error.ErrorCode)).Equal(uint(errs.EFullAccountRequired))
//		})
//
//		g.It("should successfully create a group with first party memberid", func() {
//			g.Timeout(20 * time.Second)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID3,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//				IsFirstParty:      aws.Bool(true),
//			})
//
//			reqBody := apitrusted.ServerCreateGroupRequest{
//				GroupLeader:            userID,
//				GroupRequest:           groupReq,
//				OnlineServiceType:      24,
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			var count int64 = 0
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().CountUserGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(count, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupCreate, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetFirstPartyLookup(gomock.Any(), userID3, 24).Return(aws.String(userID2), nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), aws.Bool(true)).Return(nil, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerCreateGroup(w, req)
//
//			g.Assert(w.Code).Equal(http.StatusCreated)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//		})
//
//	})
//}
//
//func TestAddGroupMembers(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
//	productID := "c71f50c3533c462785a2fc22f24c9fad"
//	userID := "b287e655461f4b3085c8f244e394ff7e"
//	userID2 := "c287e655461f4b3085c8f244e394ff7e"
//	userID3 := "d287e655461f4b3085c8f244e394ff7e"
//
//	canXplay := true
//	var groupMember apipub.GroupMember
//	groupMember.Userid = userID
//	groupMember.Role = apipub.Leader
//	var groupMembers []apipub.GroupMember
//	groupMembers = append(groupMembers, groupMember)
//	expectedBody := &apipub.Group{
//		Groupid:      groupID,
//		Productid:    productID,
//		MaxMembers:   3,
//		Created:      nil,
//		CanCrossPlay: &canXplay,
//	}
//	expectedBody.Members = &groupMembers
//
//	var addMembers []apitrusted.GroupMemberAddObject
//	addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//		Memberid:          userID2,
//		OnlineServiceType: 2,
//		CanCrossPlay:      canXplay,
//	})
//	addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//		Memberid:          userID3,
//		OnlineServiceType: 3,
//		CanCrossPlay:      canXplay,
//	})
//
//	reqBody := apitrusted.GroupMembersAdd{
//		GroupMembers: &addMembers,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	profile := apipub.UserProfile{
//		Userid:      userID,
//		DisplayName: aws.String("User1"),
//	}
//
//	profile2 := apipub.UserProfile{
//		Userid:      userID2,
//		DisplayName: aws.String("User2"),
//	}
//
//	profile3 := apipub.UserProfile{
//		Userid:      userID3,
//		DisplayName: aws.String("User3"),
//	}
//
//	group := apipub.Group{
//		Groupid:           groupID,
//		Productid:         productID,
//		Members:           &groupMembers,
//		MaxMembers:        3,
//		CanCrossPlay:      &canXplay,
//		OnlineServiceType: aws.Int(24),
//	}
//
//	var groupMembers2 []apipub.GroupMember
//
//	g.Describe("ServerAddGroupMembers", func() {
//		g.It("should successfully add members to a crossplay group", func() {
//			g.Timeout(20 * time.Second)
//
//			groupMembers2 = append(groupMembers, apipub.GroupMember{
//				Userid: userID2,
//				Role:   apipub.Member,
//			})
//			groupMembers2 = append(groupMembers2, apipub.GroupMember{
//				Userid: userID3,
//				Role:   apipub.Member,
//			})
//
//			groupRet := apipub.Group{
//				Groupid:      groupID,
//				Productid:    productID,
//				Members:      &groupMembers2,
//				MaxMembers:   3,
//				CanCrossPlay: &canXplay,
//			}
//
//			//initial queries
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user3
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID3).Return(&profile3, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID3, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID3, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile3, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&groupRet, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerAddGroupMembers(w, req, groupID)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			var groupCheck apipub.Group
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(len(*groupCheck.Members)).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//		})
//
//		g.It("should add member to a crossplay group bad CanCrossPlay on member - no error returned", func() {
//			g.Timeout(20 * time.Second)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 2,
//				CanCrossPlay:      false,
//			})
//
//			reqBody := apitrusted.GroupMembersAdd{
//				GroupMembers: &addMembers,
//			}
//
//			//initial queries
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerAddGroupMembers(w, req, groupID)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert(len(*groupCheck.Members)).Equal(1)
//			g.Assert(groupCheck.Errors).IsNil()
//		})
//
//		g.It("should add member to a crossplay group bad CanCrossPlay on member - error returned", func() {
//			g.Timeout(20 * time.Second)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 2,
//				CanCrossPlay:      false,
//			})
//
//			reqBody := apitrusted.GroupMembersAdd{
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			//initial queries
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerAddGroupMembers(w, req, groupID)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert(len(*groupCheck.Members)).Equal(1)
//			g.Assert(groupCheck.Errors).IsNotNil()
//			g.Assert((*groupCheck.Errors)[0].Memberid).Equal(userID2)
//			//these both need to get converted to uint, otherwise the tests complain
//			g.Assert(uint((*groupCheck.Errors)[0].Error.Code)).Equal(uint(http.StatusForbidden))
//			g.Assert(uint((*groupCheck.Errors)[0].Error.ErrorCode)).Equal(uint(errs.EGroupsCrossplayValidationFailed))
//		})
//
//		g.It("should successfully create a non-crossplay group with error for one member", func() {
//			g.Timeout(20 * time.Minute)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID3,
//				OnlineServiceType: 3,
//				CanCrossPlay:      false,
//			})
//
//			reqBody := apitrusted.GroupMembersAdd{
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			group.CanCrossPlay = aws.Bool(false)
//
//			//initial queries
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user3
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID3).Return(&profile3, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID3, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerAddGroupMembers(w, req, groupID)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert((*groupCheck.Errors)[0].Memberid).Equal(userID3)
//			//these both need to get converted to uint, otherwise the tests complain
//			g.Assert(uint((*groupCheck.Errors)[0].Error.Code)).Equal(uint(http.StatusForbidden))
//			g.Assert(uint((*groupCheck.Errors)[0].Error.ErrorCode)).Equal(uint(errs.EGroupsCrossplayValidationFailed))
//		})
//
//		g.It("should successfully create a crossplay group with error for platform only member", func() {
//			g.Timeout(20 * time.Minute)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID2,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID3,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//			})
//
//			reqBody := apitrusted.GroupMembersAdd{
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			group.CanCrossPlay = aws.Bool(true)
//
//			profile3 = apipub.UserProfile{
//				Userid: userID3,
//				//no display name returned = platform account
//				//DisplayName: aws.String("User3"),
//			}
//
//			//initial queries
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//then this for user3
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID3).Return(&profile3, nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerAddGroupMembers(w, req, groupID)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(groupCheck.MaxMembers).Equal(3)
//			g.Assert(groupCheck.Productid).Equal(productID)
//			g.Assert((*groupCheck.Errors)[0].Memberid).Equal(userID3)
//			//these both need to get converted to uint, otherwise the tests complain
//			g.Assert(uint((*groupCheck.Errors)[0].Error.Code)).Equal(uint(http.StatusForbidden))
//			g.Assert(uint((*groupCheck.Errors)[0].Error.ErrorCode)).Equal(uint(errs.EFullAccountRequired))
//		})
//
//		g.It("should successfully add first party member", func() {
//			g.Timeout(20 * time.Second)
//
//			addMembers = nil
//			addMembers = append(addMembers, apitrusted.GroupMemberAddObject{
//				Memberid:          userID3,
//				OnlineServiceType: 24,
//				CanCrossPlay:      true,
//				IsFirstParty:      aws.Bool(true),
//			})
//
//			reqBody := apitrusted.GroupMembersAdd{
//				GroupMembers:           &addMembers,
//				ReturnMembershipErrors: aws.Bool(true),
//			}
//
//			groupMembers2 = nil
//			groupMembers2 = append(groupMembers, apipub.GroupMember{
//				Userid: userID2,
//				Role:   apipub.Member,
//			})
//
//			groupRet := apipub.Group{
//				Groupid:      groupID,
//				Productid:    productID,
//				Members:      &groupMembers2,
//				MaxMembers:   3,
//				CanCrossPlay: &canXplay,
//			}
//
//			//initial queries
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			//then this for user2
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetFirstPartyLookup(gomock.Any(), userID3, 24).Return(aws.String(userID2), nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID2).Return(&profile2, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), userID2, gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), userID).Return(&profile, nil)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), aws.Bool(true)).Return(nil, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID2, productID, groupID)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&profile2, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any()).Return(nil)
//			//final get group
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&groupRet, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			mock.apiTrusted.ServerAddGroupMembers(w, req, groupID)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			var groupCheck apitrusted.GroupWithErrors
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &groupCheck)
//			g.Assert(len(*groupCheck.Members)).Equal(2)
//			g.Assert(groupCheck.Productid).Equal(productID)
//		})
//	})
//}
//
//func TestGetGroupServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
//	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"
//
//	now := time.Now().UTC()
//	var groupMember apipub.GroupMember
//	userID := "b287e655461f4b3085c8f244e394ff7e"
//	groupMember.Userid = userID
//	groupMember.Name = aws.String("Test User")
//
//	var groupMembers []apipub.GroupMember
//	groupMembers = append(groupMembers, groupMember)
//
//	cgReq := apitrusted.CreateGroupRequest{
//		MaxMembers:        aws.Int(30),
//		JoinRequestAction: apitrusted.Manual,
//		CanCrossPlay:      aws.Bool(true),
//		CanMembersInvite:  aws.Bool(false),
//		Password:          aws.String(""),
//	}
//	reqBody := apitrusted.ServerCreateGroupRequest{
//		GroupLeader:       userID,
//		OnlineServiceType: 24,
//		GroupRequest:      cgReq,
//	}
//
//	expectedBody := &apipub.Group{
//		Groupid:    groupID,
//		Productid:  productID,
//		MaxMembers: 30,
//		Created:    &now,
//		Members:    &groupMembers,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("ServerGetGroup", func() {
//		g.It("should return correct groupid", func() {
//			profile := apipub.UserProfile{Userid: userID}
//			profileAr := []*apipub.UserProfile{&profile}
//
//			// Mock the GetGroup call
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(expectedBody, nil)
//			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(&profileAr, nil)
//			mock.ds.EXPECT().PutUserProfiles(gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().SetUserProfiles(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//
//			// get the group
//			mock.apiTrusted.ServerGetGroup(w, req, apipub.Groupid(expectedBody.Groupid))
//
//			var newGroup apipub.Group
//			body := w.Body.String()
//			json.Unmarshal([]byte(body), &newGroup)
//			if newGroup.Groupid != expectedBody.Groupid {
//				t.Errorf("group ids don't match %s != %s", newGroup.Groupid, expectedBody.Groupid)
//			}
//			if newGroup.MaxMembers != expectedBody.MaxMembers {
//				t.Errorf("group max members don't match %d != %d", newGroup.MaxMembers, expectedBody.MaxMembers)
//			}
//		})
//
//		g.It("should return 404 for non existing group", func() {
//			// Group doesn't exist
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, req := AddBodyToRequestBasic(reqBody, productID, "pass")
//			mock.apiTrusted.ServerGetGroup(w, req, apipub.Groupid(expectedBody.Groupid))
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//	})
//}
//
//func TestDeleteGroupServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
//	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"
//
//	var group apipub.Group
//
//	// Test User is not group leader and tries to delete group
//	// Make user1 a member of the group but not leader
//	member := apipub.GroupMember{
//		Role:   apipub.Member,
//		Userid: "b287e655461f4b3085c8f244e394ff7e",
//	}
//	members := []apipub.GroupMember{
//		{
//			Role:   apipub.Leader,
//			Userid: "leader",
//		},
//		member,
//	}
//
//	now := time.Now().UTC()
//	group = apipub.Group{
//		Groupid:    groupID,
//		Productid:  productID,
//		Created:    &now,
//		Members:    &members,
//		MaxMembers: 20,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("ServerDeleteGroup", func() {
//		g.It("should get not found with nil group", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, req := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerDeleteGroup(w, req, "coolGroup")
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("should get bad request with error from get group", func() {
//			// Test GetGroup errors
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
//			w, req := LoginBasic(productID, "pass")
//
//			mock.apiTrusted.ServerDeleteGroup(w, req, "coolGroup")
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should get bad request when leader deletes a group with get group returning  error", func() {
//			g.Timeout(mock.testTimeout)
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().DeleteGroup(gomock.Any(), gomock.Any()).Return(errors.New("errored while deleting group"))
//
//			w, req := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerDeleteGroup(w, req, "fails to delete")
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should succeed", func() {
//			g.Timeout(mock.testTimeout)
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().DeleteGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupDisband, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//
//			w, req := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerDeleteGroup(w, req, "successful delete")
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestUpdateGroupServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//
//	// create mock data store
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01E5V475QFRCEHXKJAS3BRS6BV"
//	productID := "01E5V5Z9J0GX72VFSENBCKMHF0"
//
//	var members []apipub.GroupMember
//	members = append(members, apipub.GroupMember{
//		Userid: "1",
//		Role:   apipub.Nonmember,
//	})
//	now := time.Now().UTC()
//	group := apipub.Group{
//		Groupid:           groupID,
//		Productid:         productID,
//		MaxMembers:        10,
//		JoinRequestAction: apipub.AutoApprove,
//		Members:           &members,
//		Created:           &now,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("ServerUpdateGroup", func() {
//		g.It("should get bad request with db error", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
//
//			w, req := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerUpdateGroup(w, req, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should get not found", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, req := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerUpdateGroup(w, req, "group1")
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("should succeed editing group", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().SetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			groupLeader := apipub.GroupMember{
//				Userid:    "b287e655461f4b3085c8f244e394ff7e",
//				Role:      apipub.Leader,
//				Groupid:   &groupID,
//				Productid: productID,
//			}
//			group.AddMemberIfNotExist(&groupLeader)
//
//			meta := map[string]interface{}{
//				"name": "TestGroup",
//			}
//			editGroupRequest := apipub.UpdateGroupRequest{
//				MaxMembers: aws.Int(100),
//				Meta:       &meta,
//			}
//
//			rr, req := AddBodyToRequestBasic(editGroupRequest, productID, "pass")
//			mock.apiTrusted.ServerUpdateGroup(rr, req, groupID)
//
//			g.Assert(rr.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestKickOrLeaveGroupServer(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := utils.GenerateNewULID()
//	userid := "e12c3df480984141b2f385646b2024fa" //user2
//	productid := "4029a6ffe9924f969955aa2e1c0782aa"
//	reason := "Time out"
//
//	kickParams := apitrusted.ServerKickMemberFromGroupParams{
//		Reason: &reason,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productid,
//		ProductId: productid,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("ServerKickOrLeaveGroup", func() {
//		g.It("1. FAIL - failed to get group", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EGroupsNotFound))
//
//			w, r := AddBodyToRequestBasic(kickParams, productid, "pass")
//			mock.apiTrusted.ServerKickMemberFromGroup(w, r, groupID, apipub.Userid(userid), kickParams)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("2. FAIL - kick helper return error", func() {
//			members := []apipub.GroupMember{
//				{
//					Userid: "e12c3df480984141b2f385646b2024fa",
//					Role:   "member",
//				},
//			}
//
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(apipub.ChatMessageEventTypeUnknown, errs.New(http.StatusUnprocessableEntity, errs.EGroupsLeaveOrKickFailed))
//
//			w, r := AddBodyToRequestBasic(kickParams, productid, "pass")
//			mock.apiTrusted.ServerKickMemberFromGroup(w, r, groupID, apipub.Userid(userid), kickParams)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("3. OK - kick a member with a customized reason", func() {
//			members := []apipub.GroupMember{
//				{
//					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
//					Role:   "leader",
//				},
//				{
//					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
//					Role:   "member",
//				},
//			}
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), &reason).Return(apipub.ChatMessageEventTypeLeft, nil)
//			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupLeave, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any())
//			w, r := AddBodyToRequestBasic(kickParams, productid, "pass")
//			mock.apiTrusted.ServerKickMemberFromGroup(w, r, groupID, apipub.Userid(userid), kickParams)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			g.Assert(w.Body.String()).Equal("{}\n")
//		})
//
//		g.It("4. OK - kick leader with a customized reason", func() {
//			members := []apipub.GroupMember{
//				{
//					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
//					Role:   "member",
//				},
//				{
//					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
//					Role:   "leader",
//				},
//			}
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), &reason).Return(apipub.ChatMessageEventTypeLeft, nil)
//			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupLeave, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any())
//			w, r := AddBodyToRequestBasic(kickParams, productid, "pass")
//			mock.apiTrusted.ServerKickMemberFromGroup(w, r, groupID, apipub.Userid(userid), kickParams)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			g.Assert(w.Body.String()).Equal("{}\n")
//		})
//
//		g.It("5. OK - kick a member with this as the active group in presence", func() {
//			members := []apipub.GroupMember{
//				{
//					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
//					Role:   "leader",
//				},
//				{
//					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
//					Role:   "member",
//				},
//			}
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//			activeGroup := apipub.ActiveGroup{
//				CanCrossPlay:       false,
//				CanRequestJoin:     false,
//				CurrentMemberCount: 2,
//				Groupid:            groupID,
//				MaxMembers:         2,
//			}
//			var presences []*apipub.Presence
//			presence := apipub.Presence{
//				ActiveGroup: &activeGroup,
//				Userid:      userid,
//				Productid:   productid,
//			}
//			presences = append(presences, &presence)
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().KickOrLeaveHelper(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), &reason).Return(apipub.ChatMessageEventTypeLeft, nil)
//			mock.rc.EXPECT().GetUserPresences(gomock.Any(), userid, productid).Return(&presences, nil)
//			mock.rc.EXPECT().SetPresence(gomock.Any(), &presence, nil, gomock.Any())
//			mock.rc.EXPECT().GetUserGroups(gomock.Any(), userid, productid, nil, nil).Return(nil, "", nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupLeave, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any())
//			w, r := AddBodyToRequestBasic(kickParams, productid, "pass")
//			mock.apiTrusted.ServerKickMemberFromGroup(w, r, groupID, apipub.Userid(userid), kickParams)
//
//			g.Assert(w.Code).Equal(http.StatusOK)
//			g.Assert(w.Body.String()).Equal("{}\n")
//		})
//	})
//}
//
//func TestUpdateGroupMemberServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//
//	// create mock data store
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01FN83SSZZ26H316YA4HQ6KX88"
//	userid := "e12c3df480984141b2f385646b2024fa" //user2
//	productID := "4029a6ffe9924f969955aa2e1c0782aa"
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("ServerUpdateGroupMember", func() {
//
//		g.It("1. FAIL - failed to get group, group is nil", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, r := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusNotFound, errs.EGroupsNotFound).Error()))
//		})
//
//		g.It("2. FAIL - failed to get group, got the error", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
//			w, r := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed).Error()))
//		})
//
//		g.It("3. FAIL - no patch body is provided", func() {
//			group := apipub.Group{}
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			w, r := LoginBasic(productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.ERequestEmpty).Error()))
//		})
//
//		g.It("4. FAIL - invalid role in body", func() {
//			updateRoleRequest := apipub.UpdateGroupMemberRequest{
//				Role: "admin",
//			}
//
//			//request for friends fails
//			group := apipub.Group{}
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, r := AddBodyToRequestBasic(updateRoleRequest, productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidRole).Error()))
//		})
//
//		g.It("5. FAIL - member is not in group", func() {
//			updateRoleRequest := apipub.UpdateGroupMemberRequest{
//				Role: "leader",
//			}
//
//			members := []apipub.GroupMember{
//				{
//					Userid: "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
//					Role:   "leader",
//				},
//			}
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, r := AddBodyToRequestBasic(updateRoleRequest, productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusNotFound, errs.EGroupsMemberNotInGroup).Error()))
//		})
//
//		g.It("6. FAIL - group requires at lease 1 leader", func() {
//
//			updateRoleRequest := apipub.UpdateGroupMemberRequest{
//				Role: "member",
//			}
//
//			members := []apipub.GroupMember{
//				{
//					Userid: "e12c3df480984141b2f385646b2024fa", //user2JWT subject
//					Role:   "leader",
//				},
//			}
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			w, r := AddBodyToRequestBasic(updateRoleRequest, productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusForbidden)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidRole).Error()))
//		})
//
//		g.It("7. OK - user1 update to leader", func() {
//
//			updateRoleRequest := apipub.UpdateGroupMemberRequest{
//				Role: "leader",
//			}
//
//			user1Name := "User1"
//			user2Name := "User2"
//			members := []apipub.GroupMember{
//				{
//					Userid:  "b287e655461f4b3085c8f244e394ff7e", //user1JWT subject
//					Role:    "leader",
//					Groupid: &groupID,
//					Name:    &user1Name,
//					Presence: &apipub.Presence{
//						Status:    "online",
//						Timestamp: time.Now().UTC(),
//					},
//				},
//				{
//					Userid:  "e12c3df480984141b2f385646b2024fa", //user2JWT subject
//					Role:    "member",
//					Groupid: &groupID,
//					Name:    &user2Name,
//					Presence: &apipub.Presence{
//						Status:    "online",
//						Timestamp: time.Now().UTC(),
//					},
//				},
//			}
//			group := apipub.Group{
//				Groupid: groupID,
//				Members: &members,
//			}
//			//mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any())
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupLeaderChange, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//			mock.rc.EXPECT().SetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
//
//			w, r := AddBodyToRequestBasic(updateRoleRequest, productID, "pass")
//			mock.apiTrusted.ServerUpdateGroupMember(w, r, groupID, apipub.Userid(userid))
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestServerSendControlMessage(t *testing.T) {
//	g := goblin.Goblin(t)
//	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//
//	// create mock data store
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := "01FN83SSZZ26H316YA4HQ6KX88"
//	productID := "4029a6ffe9924f969955aa2e1c0782aa"
//
//	group := apipub.Group{
//		Groupid: groupID,
//	}
//
//	controlMessageOk := apitrusted.ControlMessage{
//		Payload: "payload",
//
//		Event: aws.String("event"),
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	//6000 chars
//	longPayload := "1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890" +
//		"1234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890"
//
//	controlMessageLong := apitrusted.ControlMessage{
//		Payload: longPayload,
//
//		Event: aws.String("event"),
//	}
//
//	g.Describe("TestServerSendControlMessage", func() {
//
//		g.It("1. FAIL - failed to get group, group is nil", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, r := AddBodyToRequestBasic(controlMessageOk, productID, "pass")
//			mock.apiTrusted.ServerSendControlMessage(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusNotFound, errs.EGroupsNotFound).Error()))
//		})
//
//		g.It("2. FAIL - failed to get group, got the error", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
//			w, r := AddBodyToRequestBasic(controlMessageOk, productID, "pass")
//			mock.apiTrusted.ServerSendControlMessage(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed).Error()))
//		})
//
//		g.It("3. FAIL - no body is provided", func() {
//			w, r := LoginBasic(productID, "pass")
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.apiTrusted.ServerSendControlMessage(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.ERequestEmpty).Error()))
//		})
//
//		g.It("4. FAIL - message too long", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			w, r := AddBodyToRequestBasic(controlMessageLong, productID, "pass")
//			mock.apiTrusted.ServerSendControlMessage(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnprocessableEntity, errs.EGroupsControlMessageTooLong).Error()))
//		})
//
//		g.It("5. OK - sends message", func() {
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupControlMsg, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//			w, r := AddBodyToRequestBasic(controlMessageOk, productID, "pass")
//			mock.apiTrusted.ServerSendControlMessage(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
