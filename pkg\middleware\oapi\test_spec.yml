openapi: "3.0.0"
info:
  version: 1.0.0
  title: TestServer
servers:
  - url: http://deepmap.ai/
paths:
  /resource:
    get:
      operationId: getResource
      parameters:
        - name: id
          in: query
          schema:
            type: integer
            minimum: 10
            maximum: 100
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                properties:
                  name:
                    type: string
                  id:
                    type: integer
    post:
      operationId: createResource
      responses:
        '204':
          description: No content
      requestBody:
        required: true
        content:
          application/json:
            schema:
              properties:
                name:
                  type: string
  /protected_resource:
    get:
      operationId: getProtectedResource
      security:
        - BearerAuth:
            - someScope
      responses:
        '204':
          description: no content
  /protected_resource2:
    get:
      operationId: getProtectedResource
      security:
        - BearerAuth:
            - otherScope
      responses:
        '204':
          description: no content
  /protected_resource_401:
    get:
      operationId: getProtectedResource
      security:
        - BearerAuth:
            - unauthorized
      responses:
        '401':
          description: no content
  /multiparamresource:
    get:
      operationId: getResource
      parameters:
        - name: id
          in: query
          required: true
          schema:
            type: integer
            minimum: 10
            maximum: 100
        - name: id2
          required: true
          in: query
          schema:
            type: integer
            minimum: 10
            maximum: 100
      responses:
        '200':
          description: success
          content:
            application/json:
              schema:
                properties:
                  name:
                    type: string
                  id:
                    type: integer
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
