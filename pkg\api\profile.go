package api

import (
	"context"
	"errors"
	"net/http"
	"time"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// GetUserProfile get a user profile
func (api *SocialPublicAPI) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	userid := token.Claims.Subject

	profile, err := api.getUserProfile(r.Context(), userid, false)
	if err != nil {
		errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EProfileFetchFailed))
		return
	}

	if profile == nil {

		//call identity service here because profile has more fields set from token
		profile, err = api.Id.SyncUserProfile(r.Context(), userid)
		if err == nil {
			api.saveUserProfile(r.Context(), profile)
		}
	}

	if profile != nil {
		profile.Dob = nil
		profile.Email = nil
		if token.Claims.DnaFullAccountID != "" {
			profile.ParentAccountId = &token.Claims.DnaFullAccountID
		}
	}

	ReturnOK(w, r, profile)
}

// getUserProfile try to get profile from Cache > db > dna
func (api *SocialPublicAPI) getUserProfile(ctx context.Context, userid string, bIdentityServiceFallback bool) (*apipub.UserProfileResponse, error) {
	log := logger.FromContext(ctx)

	profile, err := api.Cache.GetUserProfile(ctx, userid)
	if err != nil {
		log.Err(err).Str("user", userid).Msg("GetUserProfile Cache failed")
		return nil, err
	}

	if profile == nil {
		profile, err = api.Ds.GetUserProfile(ctx, userid)
		if err != nil {
			log.Err(err).Str("user", userid).Msg("GetUserProfile DB failed")
			return nil, err
		}
		if profile != nil {
			api.Cache.SetUserProfile(ctx, profile, time.Duration(api.Cfg.TtlProfile)*time.Second)
		}
	}

	if profile == nil && bIdentityServiceFallback {
		profile, err = api.Id.SyncUserProfile(ctx, userid)
		if err == nil {
			api.saveUserProfile(ctx, profile)
		} else {
			return nil, err
		}
	}

	if profile != nil {
		profile.Dob = nil
		profile.Email = nil
	}

	return profile, nil
}

// GetUserProfiles try to get profile from Cache > db > dna
func (api *SocialPublicAPI) GetUserProfiles(ctx context.Context, userids []string, bIdentityServiceFallback bool) (*[]*apipub.UserProfileResponse, error) {
	log := logger.FromContext(ctx)

	profiles := make([]*apipub.UserProfileResponse, 0, len(userids))
	var missing []string
	found := make(map[string]struct{}, len(userids))

	cacheProfiles, err := api.Cache.GetUserProfiles(ctx, userids)
	if err != nil {
		log.Err(err).Strs("userids", userids).Msg("GetUserProfiles Cache failed")
	}
	if cacheProfiles != nil && len(*cacheProfiles) > 0 {
		profiles = foundIds(found, profiles, cacheProfiles)
	}

	missing = missingIds(found, userids)

	if len(missing) > 0 {
		dsProfiles, err := api.Ds.GetUserProfiles(ctx, missing)
		if err != nil {
			log.Err(err).Strs("missing userids", missing).Msg("GetUserProfiles DB failed")
		}
		if dsProfiles != nil && len(*dsProfiles) > 0 {
			api.Cache.SetUserProfiles(ctx, dsProfiles, time.Duration(api.Cfg.TtlProfile)*time.Second)
			profiles = foundIds(found, profiles, dsProfiles)
		}

		missing = missingIds(found, userids)
	}

	if len(missing) > 0 && bIdentityServiceFallback {
		idProfiles, err := api.Id.SyncUserProfiles(ctx, missing)
		if err != nil {
			log.Err(err).Strs("missing userids", missing).Msg("SyncUserProfiles failed")
		}
		if idProfiles != nil && len(*idProfiles) > 0 {
			api.saveUserProfiles(ctx, idProfiles)
			profiles = append(profiles, *idProfiles...)
		}
	}

	if len(profiles) <= 0 {
		err = errors.New("no users found")
		return nil, err
	}

	profiles = reorderAndSanitizeProfiles(profiles, userids)

	return &profiles, nil
}

// SyncUserProfile direct call to identity service to get a user profile
func (api *SocialPublicAPI) SyncUserProfile(w http.ResponseWriter, r *http.Request) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}
	userid := token.Claims.Subject

	profile, err := api.Id.SyncUserProfile(r.Context(), userid)
	if err == nil {
		api.saveUserProfile(r.Context(), profile)
	}

	fullid := ""
	platformid := ""
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	if tenant == "dna" {
		//if full account get original token from auth header to get platformid and full2kid (without our login promotion)
		auth, err := authheader.GetAuthHeaderArr(r)
		if err != nil {
			ogToken, _ := jwt.ParseJWTTokenWithoutValidation(auth[1])
			fullid = userid
			platformid = ogToken.Claims.Subject
			if fullid != platformid && fullid != "" && platformid != "" {
				api.replacePlatformIdsWith2k(r.Context(), token)
			}
		}
	}

	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) saveUserProfile(ctx context.Context, profile *apipub.UserProfileResponse) {
	if profile != nil {
		// save the user profile to dynamo.
		api.Ds.PutUserProfile(ctx, profile)

		// save the user profile to redis.
		api.Cache.SetUserProfile(ctx, profile, time.Duration(api.Cfg.TtlProfile)*time.Second)
	}
}

func (api *SocialPublicAPI) saveUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse) {
	if profiles != nil {
		// save the user profiles to dynamo.
		api.Ds.PutUserProfiles(ctx, profiles)

		// save the user profiles to redis.
		api.Cache.SetUserProfiles(ctx, profiles, time.Duration(api.Cfg.TtlProfile)*time.Second)
	}
}

func (api *SocialPublicAPI) replacePlatformIdsWith2k(ctx context.Context, token *jwt.Token) {
	log := logger.FromContext(ctx)

	productid := token.Claims.ProductID
	platformid := token.Claims.Subject
	fullid := token.Claims.DnaFullAccountID

	groups, _, err := api.Cache.GetUserGroups(ctx, platformid, productid, nil, nil)
	if err != nil {
		log.Error().Err(err).Str("platformid", platformid).Str("fullid", fullid).Str("productid", productid).Msg("get groups failed in platformid replace")
	}
	if groups != nil {
		for _, group := range *groups {
			if group.Members != nil {
				for _, member := range *group.Members {
					if member.Userid == platformid {
						api.Cache.RemoveGroupMember(ctx, group, member.Userid)
						member.Userid = fullid
						api.Cache.AddGroupMember(ctx, group, &member)
						// no need to call set active group helper since it's not actually removing or adding to group
					}
				}
			}
			if group.MembershipRequests != nil {
				for _, membershipRequest := range *group.MembershipRequests {
					if (membershipRequest.Memberid == platformid) && (membershipRequest.IsFirstPartyInvite == nil || membershipRequest.IsFirstPartyInvite == aws.Bool(false)) {
						err = api.Cache.RemoveMembershipRequestFromGroup(ctx, group, membershipRequest)
						if err != nil {
							log.Error().Err(err).Interface("membershipRequest", membershipRequest).Msgf("delete membership idx failed in platformid replace")
						}
						membershipRequest.Memberid = fullid

						ttlMembership := apipub.Ttl(api.Cfg.TtlMembership)
						if membershipRequest.Ttl == nil {
							membershipRequest.Ttl = &ttlMembership
						}
						api.Cache.AddMembershipRequestToGroup(ctx, group, membershipRequest)
					}
				}
			}
		}
	}
}

func (api *SocialPublicAPI) GetRecentlyPlayed(w http.ResponseWriter, r *http.Request, params apipub.GetRecentlyPlayedParams) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	limit := 100
	limit64 := int64(limit)
	if params.Limit != nil && *params.Limit >= 1 && *params.Limit <= 100 {
		limit = *params.Limit
		limit64 = int64(limit)
	}

	nextVal := ""
	if params.Next != nil {
		nextVal = *params.Next
	}

	items, next, err := api.Cache.GetRecentlyPlayed(r.Context(), userid, productid, &limit64, &nextVal)
	if err != nil {
		log.Error().Err(err).Str("userid", userid).Str("productid", productid).Msgf("error getting recently played")
	}

	var response apipub.PlayedPlayersNext
	response.Items = []apipub.RecentlyPlayedUserResponse{}
	if items != nil {
		recentids := make([]string, 0, len(*items))
		for _, recent := range *items {
			if recent != nil {
				recentids = append(recentids, recent.Userid)
			}
		}
		recentProfiles, _ := api.GetUserProfiles(r.Context(), recentids, false)
		var profile *apipub.UserProfileResponse

		for _, item := range *items {
			if item != nil {
				profile = nil
				if recentProfiles != nil {
					for _, p := range *recentProfiles {
						if p.Userid == item.Userid {
							profile = p
							break
						}
					}
				}
				if profile != nil && profile.DisplayName != nil {
					item.Name = profile.DisplayName
				}
				if profile != nil && profile.Links != nil {
					item.Links = profile.Links
				}
				if item.Links != nil {
					identity.FilterLinksByOST(item.Links, ost)
				}

				var simplePresence []apipub.SimplePresenceResponse
				presences, err2 := api.Cache.GetUserPresences(r.Context(), item.Userid, productid)
				if err2 != nil {
					log.Error().Err(err2).Str("event", "failed to get user presence").Str("item userid", item.Userid).Msg("failed to get user presence")
				}
				if presences != nil && len(*presences) > 0 {
					//iterate through all user presnces and filter priority params and always also include user defined and launcher set
					for _, presence := range *presences {
						if presence != nil {
							if presence.Priority == apipub.PresesencePriorityUserSet || presence.Priority == apipub.PresencePriorityLauncherAutomated || (presence.Priority >= apipub.PresencePriorityGameSetStart && presence.Priority <= apipub.PresencePriorityGameSetEnd) {
								simpPresence := &apipub.SimplePresenceResponse{
									Userid:            presence.Userid,
									Status:            presence.Status,
									GameName:          &presence.GameName,
									OnlineServiceType: &(presence.OnlineServiceType),
								}

								simplePresence = append(simplePresence, *simpPresence)
							}
						}
					}
					item.SimplePresence = &simplePresence
				}

				response.Items = append(response.Items, *item)
			}
		}
	}

	if next != "" {
		response.Nextid = &next
	}

	ReturnOK(w, r, response)
}

func (api *SocialPublicAPI) UpdateRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	//ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	// parse post request
	var request apipub.SetPlayedRequestBody
	if !DecodeBody(w, r, &request) {
		return
	}

	var userids []string
	userMap := map[string]apipub.RecentlyPlayedUsers{}
	for _, user := range request.Users {
		//can not add self to recently played
		if user.Userid == userid {
			log.Error().Str("event", "cannot add self to recently played").Str("userid", userid).Msg("cannot add self to recently played")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.ERecentlyPlayedCannotAddSelf))
			return
		}

		err := validation.Validate.VarCtx(r.Context(), user.Userid, validation.KValidateUserID)
		if err != nil {
			log.Error().Str("event", "invalid id").Str("userid", userid).Err(err).Msg("invalid id")
			errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EInvalidUserID))
			return
		}

		userids = append(userids, user.Userid)
		userMap[user.Userid] = user
	}
	userids = utils.UniqueStringArray(userids)

	profiles, _ := api.GetUserProfiles(r.Context(), userids, false)

	// add new recently played
	var recentlyPlayerUsers []*apipub.RecentlyPlayedUserResponse
	for _, id := range userids {
		user := &apipub.RecentlyPlayedUserResponse{
			Userid: id,
		}

		if requestUser, ok := userMap[id]; ok {
			user.Weight = requestUser.Weight
			user.Context = requestUser.Context
		}

		if profiles != nil {
			for _, profile := range *profiles {
				if profile != nil && profile.Userid == id {
					user.Name = profile.DisplayName
					if profile.Links != nil && len(*profile.Links) > 0 {
						links := make([]apipub.AccountLinkDNA, len(*profile.Links))
						user.Links = &links
						copy(*user.Links, *profile.Links)
					}
				}
			}
		}

		if user.Productid == nil || *user.Productid == "" {
			user.Productid = &productid
		}

		user.ForUserid = &userid
		recentlyPlayerUsers = append(recentlyPlayerUsers, user)
	}

	ttl := int64(api.Cfg.TtlPlayedDefault)
	if request.Ttl != nil {
		value := *request.Ttl
		// maximum of 30 days
		var maxValue = int64(api.Cfg.TtlPlayedMax)
		if value > maxValue {
			value = maxValue
		}

		// set minimum of 1 second.
		// zero means no TTL so the record will be in redis indefinitely
		if value < 1 {
			value = 1
		}
		ttl = value
	}

	api.Cache.SetRecentlyPlayedUsers(r.Context(), productid, &recentlyPlayerUsers, time.Duration(ttl)*time.Second)
	count := int64(len(recentlyPlayerUsers))

	items, _, err := api.Cache.GetRecentlyPlayed(r.Context(), userid, productid, &count, nil)
	if err != nil {
		log.Error().Err(err).Str("userid", userid).Str("productid", productid).Msg("error getting recently played")
	}

	if items != nil && count != int64(len(*items)) {
		log.Error().Msgf("recently played items set count=%d get count=%d recentPlayerUsers=%v items=%v tll=%d", count, int64(len(*items)), recentlyPlayerUsers, items, ttl)
	}

	//ret := []apipub.RecentlyPlayedUser{}
	//if items != nil {
	//	for _, item := range *items {
	//		if item != nil {
	//			if item.Links != nil {
	//				identity.FilterLinksByOST(item.Links, ost)
	//			}
	//			ret = append(ret, *item)
	//		}
	//	}
	//}
	//response := apipub.PlayedPlayersNext{
	//	Items:  ret,
	//	Nextid: &next,
	//}
	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) DeleteRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID

	api.Cache.DelRecentlyPlayedUsers(r.Context(), productid, userid)
}

// UpsertSessionAuth function to upsert session auth for 1p session syncing.
func (api *SocialPublicAPI) UpsertSessionAuth(w http.ResponseWriter, r *http.Request, pOnlineServiceType apipub.POnlineServiceType, pFirstPartyid apipub.PFirstPartyid) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := pOnlineServiceType
	firstPartyid := pFirstPartyid

	log.Debug().Str("userid", userid).Str("productid", productid).Int("ost", int(ost)).Str("firstPartyid", firstPartyid).Str("event", "upsert_session_auth").Msg("upsert_session_auth")

	ReturnEmptyOK(w, r)
}

func foundIds(found map[string]struct{}, profiles []*apipub.UserProfileResponse, newProfiles *[]*apipub.UserProfileResponse) []*apipub.UserProfileResponse {
	if found == nil || profiles == nil || newProfiles == nil {
		return profiles
	}
	for _, profile := range *newProfiles {
		if profile != nil {
			found[profile.Userid] = struct{}{}
			profiles = append(profiles, profile)
		}
	}
	return profiles
}

func missingIds(found map[string]struct{}, userids []string) []string {
	//clear out missing
	missing := make([]string, 0, len(userids))

	//if not found: append to missing
	for _, id := range userids {
		if _, ok := found[id]; !ok {
			missing = append(missing, id)
		}
	}
	return missing
}

func reorderAndSanitizeProfiles(profiles []*apipub.UserProfileResponse, userids []string) []*apipub.UserProfileResponse {
	orderedProfiles := make([]*apipub.UserProfileResponse, 0, len(profiles))
	//reorder array to return in the order requested
	for _, id := range userids {
		//sanitize email and dob for return
		for _, profile := range profiles {
			if id == profile.Userid {
				profile.Dob = nil
				profile.Email = nil
				orderedProfiles = append(orderedProfiles, profile)
				break
			}
		}
	}
	return orderedProfiles
}
