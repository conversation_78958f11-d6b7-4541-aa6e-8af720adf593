import { render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import NavLinkMock from '../../NavLink/__mock__/NavLink.svelte';
import FriendRequestsActionBarWrapper from './FriendRequestsActionBarWrapper.svelte';

jest.mock('../../NavLink', () => ({
  NavLink: NavLinkMock,
}));

jest.mock('../../../assets/icons', () => ({
  SVGArrowLeft: SVGIconMock,
  SVGBell: SVGIconMock,
}));

describe('FriendRequestsActionBar', () => {
  it('should render UI', () => {
    const { container } = render(FriendRequestsActionBarWrapper);
    expect(container).not.toBeNull();
  });
});
