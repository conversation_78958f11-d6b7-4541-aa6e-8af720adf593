openapi: '3.0.0'
info:
  title: SSO Service - Authenticated Server
  description: SSO Service endpoints for Authenticated Servers.  All IDs are assumed to be for DNA services unless otherwise noted.  Properties denoted with a red asterisk in the models are required to send the specified request.  Non-denoted properties are either optional or read-only.
  version: 'Current Release Version: 1.50.0'
 
servers:
- url: https://sso.api.2kcoretech.online/sso/v2.0
 
paths:
  '/server/user/accounts':
    get:
      description: "Retrieves details for the specified Account based on the provided user access token.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclAuthServerUserAccountsGet</i>"
      tags:
        - User Accounts
      security:
        - accessToken: []
      parameters:
        - $ref: '#/components/parameters/scope'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/server/user/accounts/search':
    post:
      description: "Retrieves details for a list of specified Accounts.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclAuthServerUserAccountsGet</i>"
      tags:
        - User Accounts
      security:
        - accessToken: []
      requestBody:
        description: The Account search request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchAccountRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchAccountResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/server/user/accounts/batch/upsert-first-party':
    post:
      description: "Creates or updates Accounts for a specified list of First Party ID. If the Account with the provided First Party ID does exist, Legal Document acceptance of that Account will be updated by any Legal Document specified in the request. If the Account with the provided First Party ID does not exist, a new Anonymous or Undisclosed Account will be created.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclAuthServerAccountsBatchUpsert</i>"
      tags:
        - User Accounts
      security:
        - accessToken: []
      requestBody:
        description: The Account upsert request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpsertAccountRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UpsertAccountResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens':
    post:
      description: "Issues unique Access and Refresh Tokens once valid credentials have been provided for the current Server Instance.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Authenticated Server Tokens
      security:
        - appCredentials: []
      requestBody:
        description: The Server login request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ServerLoginRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerLoginResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/logout':
    post:
      description: "Logs the current user out of the session and revokes the Access and Refresh Tokens associated with that session.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Authenticated Server Tokens
      security:
        - accessToken: []
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ServerLogoutResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/validate':
    post:
      description: "Checks whether the provided Access or Refresh Token is valid.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>aclAuthTokensValidate</i>"
      tags:
        - Authenticated Server Tokens
      security:
        - appId: []
      requestBody:
        description: The token validation check request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TokenValidationRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenValidationResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/jwks.json':
    get:
      description: "A JSON object that represents a set of JWKs (JSON Web Key Set). Returns a list of SSO public keys for validating the token signature.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - JSON Web Key Sets (JWKs)
      parameters:
        - $ref: '#/components/parameters/ifModifiedSince'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetJwksResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/openid-configuration':
    get:
      description: "Returns the OpenID configuration.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - JSON Web Key Sets (JWKs)
      parameters:
        - $ref: '#/components/parameters/ifModifiedSince'
      responses:
        200:
          description: OK
 
components:
  securitySchemes:
    appCredentials:
      type: http
      scheme: basic
    appId:
      type: apiKey
      name: Authorization
      in: header
    accessToken:
      type: oauth2
      flows:
        implicit:
           authorizationUrl: https://tbd.2k.com
           scopes: {}
 
  schemas:
    GetAccountResponse:
      type: object
      properties:
        id:
          description: The Account ID, which was generated when the specified Account was created.
          type: string
          format: ID
        accountType:
          description: "The type of Account.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - ANONYMOUS\n\n
            2 - PLATFORM\n\n
            3 - FULL\n\n
            4 - TOOLS\n\n
            5 - NEWSLETTER\n\n
            6 - UNDISCLOSED\n\n
            7 - PRIVACY POLICY ACCEPTED ONLY"
          type: integer
          format: uint32
        dob:
          description: The date of birth for the specified user.
          type: string
          format: mm/dd/yyyy
        isDobVerified:
          description: Whether the date of birth for the specified user has been verified.
          type: boolean
          enum: [true, false]
        locale:
          description: The locale for the specified user.
          type: string
          format: xx-XX
          example: 'en-US'
        createdOn:
          description: The UNIX timestamp that indicates when the specified Account was created.
          type: integer
          format: uint64
        email:
          description: The email address of the specified user.
          type: string
          format: email
        isEmailVerified:
          description: Whether the email address for the specified user has been verified.
          type: boolean
          enum: [true, false]
        passwordVersion:
          description: The current version of the password stored on the Account of the current user.  The version is incremented by one each time the password is changed.
          type: integer
          format: uint32
        firstName:
          description: The first name of the specified user.
          type: string
        lastName:
          description: The last name of the specified user.
          type: string
        displayName:
          description: The display name for the specified user.
          type: string
          pattern: '^[a-zA-Z]{3,25}$'
        subscribedNewsletters:
          description: The list of newsletters the specified user is subscribed to.
          type: array
          items:
            type: string
        defaultTwoFaType:
          description: The default Two-Factor Authentication method for the specified user.
          type: string
          example: 'sms'
        twoFaCredentials:
          description: The Two-Factor Authentication credentials associated with the Two-Factor Authentication type being used for the Account of the specified user.
          type: object
          properties:
            twoFaType:
              type: object
              properties:
                credential:
                  description: The Two-Factor Authentication credentials.
                  type: string
                isVerified:
                  description: Whether the credentials have been verified.
                  type: boolean
                  enum: [true, false]
        country:
          description: The country for the specified user.
          type: string
          format: XX
          example: 'US'
        onlineServiceType:
          description: "The online service type of the specified Platform Account.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - XBOX LIVE\n\n
            2 - SONY ENTERTAINMENT NETWORK\n\n
            3 - STEAM\n\n
            4 - WEB\n\n
            5 - LEGACY GAME CENTER\n\n
            6 - GOOGLE PLAY\n\n
            9 - WINDOWS PHONE\n\n
            10 - CALICO\n\n
            11 - NINTENDO\n\n
            12 - GAME CENTER\n\n
            13 - WEGAME\n\n
            14 - VORTEX\n\n
            15 - EPIC\n\n
            16 - STADIA\n\n
            17 - FACEBOOK\n\n
            18 - GOOGLE\n\n
            19 - TWITTER\n\n
            20 - TWITCH\n\n
            21 - DEVICE\n\n
            22 - APPLE\n\n
            23 - ZENDESK\n\n
            24 - T2GP\n\n
            99 - WINDOWS DEVELOPER"
          type: integer
          format: uint32
    SearchAccountRequest:
      type: object
      required:
        - type
        - criterias
      properties:
        type:
          description: "The type of criteria to be searched.\n\n\n
            List of types for reference:\n\n
            - accountsById - Retrieves a list of Accounts by Account ID.\n\n
            - accountsByFirstPartyId - Retrieves a list of Accounts by First-Party ID.\n\n
            - fullAccountIdByFirstPartyId - Retrieves a list of Full Accounts that are linked to the provided First-Party IDs.\n\n
            - accountsByParentAccountId - Retrieves a list of Full Accounts and all Platform Account that are linked to the provided Parent Account IDs.\n\n
            - accountsByFirstPartyAlias - Retrieves a list of Accounts by First-Party Alias."
          type: string
          enum: [accountsById, accountsByFirstPartyId, fullAccountIdByFirstPartyId, accountsByParentAccountId, accountsByFirstPartyAlias]
        criterias:
          description: The criteria to be searched.
          type: array
          items:
            type: object
            properties:
              accountId:
                description: The Account ID to search with, which can be obtained from either the Account Login response body or the 'Location' response header when the Account was created and required when using the 'accountsById' search type.
                type: string
              firstPartyId:
                description: The First-Party ID to search with, which can be obtained from the Account details if the Account Type is 2 and required when using the 'accountsByFirstPartyId' or 'fullAccountIdByFirstPartyId'search type.
                type: string
              onlineServiceType:
                description: The Online Service Type to search with, which can be obtained from the Account details, and required when using the 'accountsByFirstPartyId', 'accountsByFirstPartyAlias' or 'fullAccountIdByFirstPartyId'search type.
                type: integer
              parentAccountId:
                description: The Parent Account ID to search with, which can be obtained from the Account details and required when using the 'accountsByParentAccountId'.
                type: string
              firstPartyAlias:
                description: The First-Party Alias to search with, which can be obtained from the Account details if the Account Type is 2.
                type: string
    SearchAccountResponse:
      type: object
      properties:
        accountList:
          description: The list of Accounts when the 'accountsById', 'accountsByFirstPartyId', 'accountsByParentAccountId',  or  'accountsByFirstPartyAlias' search type is requested.
          type: array
          items:
            type: object
            properties:
              id:
                description: The Account ID, which was generated when the listed Account was created.
                type: string
                format: ID
              type:
                description: "The type of Account.\n\n\n
                  List of types for reference:\n\n
                  0 - UNKNOWN\n\n
                  1 - ANONYMOUS\n\n
                  2 - PLATFORM\n\
                  3 - FULL\n\n
                  4 - TOOLS\n\n
                  5 - NEWSLETTER\n\n
                  6 - UNDISCLOSED\n\n
                  7 - PRIVACY POLICY ACCEPTED ONLY"
                type: integer
                format: uint32
              email:
                description: The email address of the listed Account.
                type: string
                format: email
              dob:
                description: The date of birth for the listed Account.
                type: string
                format: mm/dd/yyyy
              locale:
                description: The locale for the listed Account.
                type: string
                format: xx-XX
                example: 'en-US'
              createdOn:
                description: The UNIX timestamp that indicates when the listed Account was created.
                type: integer
                format: uint64
              firstPartyId:
                description: The First Party ID of the listed Platform Account. For Device Accounts, the Device ID will be displayed.
                type: string
              firstPartyAlias:
                description: The First Party alias of the listed Platform Account. For Device Accounts, the Device Name will be displayed. Full Accounts will not have an alias.
                type: string
              displayName:
                description: The Display Name of the listed Full Account.
                type: string
              parentAccountId:
                description: The Parent Account ID of the listed Platform Account.
                type: string
                format: ID
              onlineServiceType:
                description: "The online service type of the listed Platform Account.\n\n\n
                  List of types for reference:\n\n
                  0 - UNKNOWN\n\n
                  1 - XBOX LIVE\n\n
                  2 - SONY ENTERTAINMENT NETWORK\n\n
                  3 - STEAM\n\n
                  4 - WEB\n\n
                  5 - LEGACY GAME CENTER\n\n
                  6 - GOOGLE PLAY\n\n
                  9 - WINDOWS PHONE\n\n
                  10 - CALICO\n\n
                  11 - NINTENDO\n\n
                  12 - GAME CENTER\n\n
                  13 - WEGAME\n\n
                  14 - VORTEX\n\n
                  15 - EPIC\n\n
                  16 - STADIA\n\n
                  17 - FACEBOOK\n\n
                  18 - GOOGLE\n\n
                  19 - TWITTER\n\n
                  20 - TWITCH\n\n
                  21 - DEVICE\n\n
                  22 - APPLE\n\n
                  23 - ZENDESK\n\n
                  24 - T2GP\n\n
                  99 - WINDOWS DEVELOPER"
                type: integer
                format: uint32
        fullAccountIdMap:
          description: An object map of First-Party IDs and the Full Account each one is linked to, and this response is given when the 'fullAccountIdByFirstPartyId' search type is requested.
          type: object
          additionalProperties:
            type: string
          example:
            'firstPatyId12345': '00000000000000000000000000000001'
    UpsertAccountRequest:
      type: array
      items:
        type: object
        required:
          - onlineServiceType
          - firstPartyId
        properties:
          onlineServiceType:
            description: "The online service type of the listed Platform Account.\n\n\n
              List of types for reference:\n\n
              0 - UNKNOWN\n\n
              1 - XBOX LIVE\n\n
              2 - SONY ENTERTAINMENT NETWORK\n\n
              3 - STEAM\n\n
              4 - WEB\n\n
              5 - LEGACY GAME CENTER\n\n
              6 - GOOGLE PLAY\n\n
              9 - WINDOWS PHONE\n\n
              10 - CALICO\n\n
              11 - NINTENDO\n\n
              12 - GAME CENTER\n\n
              13 - WEGAME\n\n
              14 - VORTEX\n\n
              15 - EPIC\n\n
              16 - STADIA\n\n
              17 - FACEBOOK\n\n
              18 - GOOGLE\n\n
              19 - TWITTER\n\n
              20 - TWITCH\n\n
              21 - DEVICE\n\n
              22 - APPLE\n\n
              23 - ZENDESK\n\n
              24 - T2GP\n\n
              99 - WINDOWS DEVELOPER"
            type: integer
            format: uint32
            example: 2
          firstPartyId:
            description: The First Party ID of the listed Platform Account. For Device Accounts, the Device ID will be displayed.
            type: string
          firstPartyAlias:
            description: The First Party alias of the listed Platform Account. For Device Accounts, the Device Name will be displayed.
            type: string
          legalDocuments:
            description: A list of legal document ID. The upserted Accounts will be updated with the provided document IDs.
    UpsertAccountResponse:
      type: array
      items:
        type: object
        properties:
          id:
            description: The Account ID, which was generated when the specified Account was created.
            type: string
            format: ID
          firstPartyId:
            description: The First Party ID of the listed Platform Account. For Device Accounts, the Device ID will be displayed.
            type: string
          createdOn:
            description: The UNIX timestamp that indicates when the specified Account was created.
            type: integer
            format: uint64
          updatedOn:
            description: The UNIX timestamp that indicates when the specified Account was last modified.
            type: integer
            format: uint64
          status:
            description: The HTTP status for each Account upsert response.
            type: string
            example: 'Bad Request'
          message:
            description: The error message for each Account upsert response.
            type: string
            example: 'Could not find any legal documents with that ID.'
    ServerLoginRequest:
      type: object
      required:
      - accountType
      - credentials(server)
      - credentials(refresh)
      properties:
        locale:
          description: The locale for the current user.
          type: string
          format: xx-XX
          example: 'en-US'
        accountType:
          description: The type of Account the current user is authenticating against.
          type: string
          format: platform
        credentials(server):
          $ref: '#/components/schemas/serverCredentials'
        credentials(refresh):
          $ref: '#/components/schemas/refreshCredentials'
    serverCredentials:
      description: The credentials to be used for Server authentication.
      type: object
      required:
        - type
        - instanceId
      properties:
        type:
          description: The Server credential type being supplied for the login request.
          type: string
          example: 'server'
        instanceId:
          description: A 32-character no-dash GUID that uniquely identifies the current server instance.
          type: string
          format: ID
    refreshCredentials:
      description: The credentials to be used for Refresh Token authentication.
      type: object
      required:
        - type
        - refreshToken
      properties:
        type:
          description: The Refresh Token credential type being supplied to refresh the access token for the current user.
          type: string
          example: 'refresh'
        refreshToken:
          description: The Refresh Token value, which was issued to the current user upon successful login.
          type: string
        psnRegion:
          description: The PSN Region the current user is logging in from.  This field is required if the 'psnRegion' is not present in the request headers.  Note that this is only for PSN-based refresh tokens requests.
          type: string
          example: 'scea|scee|scej'
    ServerLoginResponse:
      type: object
      properties:
        accessToken:
          description: The JWT (JSON Web Token) that is issued upon successful login.
          type: string
          format: jwt
        accessTokenExpiresIn:
          description: The number of seconds (TTL) until the issued access token expires.
          type: integer
          format: uint32
        refreshToken:
          description: The JWT (JSON Web Token) that is issued upon successful login.  This token is used to refresh a current session.
          type: string
          format: jwt
        refreshTokenExpiresIn:
          description: The number of seconds (TTL) until the issued refresh token expires.
          type: integer
          format: uint32
    ServerLogoutResponse:
      type: array
      items:
        type: object
        properties:
          state:
            description: The current state of the Access Token or Refresh Token.
            type: string
            example: 'revoked'
          jti:
            description: The Access Token or Refresh Token ID that was generated when the token was created.
            type: string
            format: ID
    TokenValidationRequest:
      type: object
      properties:
        token:
          description: The Access or Refresh Token to be validated.
          type: string
          format: jwt
    TokenValidationResponse:
        type: object
        properties:
          revoked:
            description: The valid state of the specified token.
            type: boolean
            enum: [true, false]
    GetJwksResponse:
      type: object
      properties:
        keys:
          description: The supported JWKS key.
          type: array
          items:
            $ref: '#/components/schemas/JwksItem'
          example:
            - kty: RSA
              e: AQAB
              use: sig
              kid: 755d9354-1359-11eb-adc1-0242ac120002
              alg: RS256
              'n': l_BSeK2DcWDrG6dqNlEsHJWk5ZEObC3k_9cCr5jjiBaNSim-XO4eY96BK_QA44eJTORwo_Z8CnhTNU_H0MWQ-_dnYaY7RKaK67v_g6JqPVY8o7gcYSOwCZ-ppYg0ZXuEl8hSDlC99Dw5eSbRbVA90Ef4OHx22kPSQsxVVWX0frINrnV3h2gbxeMa-gjzTWNUmTBXGQK5Mu4XfT67ubBY5Hm0KC291ONQBZ7sLnflMMW0FgnPj_v9-FMN-QpOkeZGmDbLKWg4w3XrCLuXIPVStodv_zwANoFNdc3hUhdxEnz_oK1pUpGeazbr-MGukJ3sbVtm1clFJ5mnjCQ7yeHBjw
            - kty: RSA
              e: AQAB
              use: sig
              kid: 03a61152-ecd9-4936-80d2-4536c9e64556
              alg: RS256
              'n': q2IUn5RQ6kP9U_JRcKxeTTE9fqP4Rx6tsHP4yFYoTQHTD07AdUI1uDzNcC5A3vvujZJ2mzerJGfC6u9ueLzN62_jQ6RBE-zlB4jzpe2XEMvB01MpdJwt_TQGCIJVwkoltoKr8A8x3s0EfkViRgKaA8VuIHwyV8XolYtS9_h-UoVm9KCNuAbJJLPG_zuHjHBTpeJlsY6vzdgjuM5pyaHUUHbzrIEV69-Slh6h68TS8iZH7MS4OMvOruNfkytM_cCa85N3gtNUgh2P_7eyqP3k6XX1Y-MHiKQF_iDbglKawKQObXXTNKK_waUraKRWUFchbjN0lF7MO0cKBaHX8qXtnQ
    JwksItem:
      type: object
      properties:
        kty:
          description: The Key Type parameter identifies the cryptographic algorithm family used with the key, such as 'RSA' or 'EC'.
          type: string
        e:
          description: The Exponent parameter contains the exponent value for the RSA public key. It is represented as a Base64urlUInt-encoded value.
          type: string
        use:
          description: The Public Key Use Parameter identifies the intended use of the public key. Values defined by this specification are 'sig' (signature) 'enc' (encryption).
          type: string
        kid:
          description: The Key ID parameter is used to match a specific key. This is used, for instance, to choose among a set of keys within a JWK Set during key rollover.
          type: string
        alg:
          description: The Algorithm parameter identifies the algorithm intended for use with the key.
          type: string
        n:
          description: The 'n', or Modulus parameter contains the modulus value for the RSA public key. It is represented as a Base64urlUInt-encoded value.
          type: string
 
    ErrorResponse:
      type: object
      required:
        - code
        - message
      properties:
        code:
          description: A code representing the error that occurred.
          type: integer
          format: uint32
        message:
          description: A string describing the error that occurred.
          type: string
 
  parameters:
    scope:
      name: X-2k-User-Scope
      in: header
      description: The JWT access token associated with the Account to search with, which can be obtained from the Account Login response body.
      required: true
      schema:
        type: string
        format: jwt
    ifModifiedSince:
      name: If-Modified-Since
      in: header
      description: Conditional request based on the last modification.
      required: false
      schema:
        type: string
        format: date-time
 
  responses:
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '409':
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '415':
      description: Unsupported Media Type
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '429':
      description: Too Many Requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
