package apipub

import (
	"fmt"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

//var memberhipStatuses = []string{
//	string(Approved),
//	string(Declined),
//	string(Invited),
//	string(Accepted),
//	string(Rejected),
//	string(Requested),
//	string(Revoked),
//}

//var validStatus = fmt.Sprintf("oneof=%s", strings.Join(memberhipStatuses, " "))

func (membership *MembershipRequest) RedisKey(tenant string) string {
	firstParty := ""
	if membership.IsFirstPartyInvite != nil && *membership.IsFirstPartyInvite {
		firstParty = ":firstParty"
	}

	return fmt.Sprintf("%s:prod:%s:%s:group:{%s}:memberships:member:%s:approver:%s%s", tenant, *membership.Productid, utils.GetEnvironment(), membership.Groupid, membership.Memberid, membership.Approverid, firstParty)
}

func BuildMembershipRequestKey(tenant, productid, groupid, memberid, approverid string, isFirstParty *bool) string {
	firstParty := ""
	if isFirstParty != nil && *isFirstParty {
		firstParty = ":firstParty"
	}
	return fmt.Sprintf("%s:prod:%s:%s:group:{%s}:memberships:member:%s:approver:%s%s", tenant, productid, utils.GetEnvironment(), groupid, memberid, approverid, firstParty)
}
