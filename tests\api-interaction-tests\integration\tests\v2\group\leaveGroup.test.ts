import request from 'supertest';
import { TwokAccounts } from '../../../lib/config';
import * as socialApi from '../../../lib/social-api';
import { StatusCodes } from 'http-status-codes';

/**
 * TODO: see if the invite/accept invite parts of the tests can be placed in beforeEach.
 */

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 2,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    await usersTwok.logoutAll({});
  });

  it('Member leaves the group[public v2 happy]', async () => {
    let testCase = {
      description: "member leaves the group",
      expected: "the member is not in the group"
    };

    // The leader invites member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    let actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );
    // Member1 joins the group.
    let expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: expect.arrayContaining([
          expect.objectContaining({
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the member does not join the group"
        }
      }
    );

    // Member1 leaves the group.
    r = await socialApi.leaveGroup(usersTwok.acct["member1"], groupId);
    socialApi.testStatus(StatusCodes.OK, r);

    actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["leader"], groupId);

    // Expect member1 leaves the group.
    expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: [
          {
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the member is still in the group unexpectedly"
        }
      }
    );
  });
});

describe('', () => {
  let usersTwok: TwokAccounts;
  let groupId: string;

  beforeEach(async () => {
    usersTwok = new TwokAccounts(2, ["leader", "member1"]);
    await usersTwok.loginAll({});

    const r = await socialApi.createGroup(
      usersTwok.acct["leader"],
      {
        maxMembers: 2,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["member1"], groupId);
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId); // in case the leader does not leave the group.
    await usersTwok.logoutAll({});
  });

  it('The leader leaves the group[public v2 happy]', async () => {
    let testCase = {
      description: "the leader leaves the group",
      expected: "the leader is not in the group; a member is promoted to be the leader"
    };

    // The leader invites member1.
    let r: request.Response = await socialApi.invite(
      usersTwok.acct["leader"],
      groupId,
      {},
      usersTwok.acct["member1"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // Member1 accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["member1"],
      groupId,
      usersTwok.acct["leader"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    // The leader leaves the group.
    r = await socialApi.leaveGroup(usersTwok.acct["leader"], groupId);
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["member1"],
      groupId
    );
    // Expect
    // - the leader leaves the group.
    // - member1 is promoted to be the leader.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        members: [
          {
            role: 'leader',
            // TODO: have more members in group rather than promoted by default; the promoted member needs to join the group after the leader.
            userid: usersTwok.acct["member1"].publicId,
          },
        ],
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected group role or userid in the group"
        }
      }
    );
  });
});
