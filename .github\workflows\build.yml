name: Build docker images

on:
  push:
    branches:
      - develop
      - loadtesting
  release:
    types:
      - published

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  pre-deploy:
    name: Determine target environment
    runs-on: [t2gp-arc-linux]
    outputs:
      env: ${{ steps.env-fetch.outputs.env }}
      ref: ${{ steps.env-fetch.outputs.ref }}
    steps:
      - id: env-fetch
        name: Determine target environment
        run: |
          REF=$(echo "${{ github.ref }}" | awk -F'/' '{print $3}')
          ENV=develop
          if [[ "$REF" == release* ]]; then
            ENV=release
          else
            ENV=develop
          fi
          echo "env=$ENV" >> $GITHUB_OUTPUT
          echo "ref=$REF" >> $GITHUB_OUTPUT

  build-social-service:
    needs: [pre-deploy]
    name: Build social service docker images
    runs-on: [t2gp-arc-linux]
    env:
      WORK_DIR: ./
    outputs:
      image_tag: ${{ steps.build-docker.outputs.image_tag }}

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          fetch-depth: 0

      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ vars.GH_AWS_ROLE }}
          role-session-name: ${{ vars.GH_AWS_SESSION }}
          aws-region: us-east-1          
      - name: Get version if release
        if: ${{github.event_name == 'release'}}
        run: echo VER=${{ github.event.release.tag_name }}-${GITHUB_SHA::8} >> $GITHUB_ENV
      - name: Get commit if push to develop
        if: ${{github.event_name == 'push'}}
        id: declare-envs
        run: |
          echo VER=${GITHUB_SHA::8} >> $GITHUB_ENV
      - name: Get release candidate version from branch name
        if: ${{ startsWith(github.ref,'refs/heads/release/') }}
        run: |
          RC=$(echo "${{ github.ref }}" | awk -F'/' '{print $4}')
          echo VER=${RC}-${GITHUB_SHA::8} >> $GITHUB_ENV
      - name: socialCopy launcher ui from S3
        run: |
          make sync-launcher-ui
        continue-on-error: true
      - name: Build docker images
        id: build-docker
        run: |
          echo "image_tag=$VER" >> $GITHUB_OUTPUT
          DATE=$(date "+%Y-%m-%d-%H-%M-%S")
          docker build --progress plain -t t2gp-social-api . \
            --build-arg VERSION="${{env.VER}}" \
            --build-arg GIT_HASH="${{ github.sha }}" \
            --build-arg BUILD_DATE="$DATE" \
            --build-arg NODE_AUTH_TOKEN="${{ secrets.SERVICE_ACCOUNT_GH_PAT }}" \
            --build-arg git_token=${{secrets.SERVICE_ACCOUNT_GH_PAT}} \
            --label "org.opencontainers.image.revision=${{ github.sha }}" \
            --label "org.opencontainers.image.source=https://github.com/${{ github.repository }}"
        env:
          NODE_AUTH_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
      - name: Push api image
        uses: jwalton/gh-ecr-push@v1
        with:
          access-key-id: ${{ env.AWS_ACCESS_KEY_ID }}
          secret-access-key: ${{ env.AWS_SECRET_ACCESS_KEY }}
          region: us-east-1
          local-image: 't2gp-social-api'
          image: 't2gp-social-api:${{env.VER}}, t2gp-social-api:${{ needs.pre-deploy.outputs.env }}'
      - name: Upload git metadata to datadog
        run: |
          curl --insecure -vv -L --fail "https://github.com/DataDog/datadog-ci/releases/latest/download/datadog-ci_linux-x64" --output "datadog-ci"
          chmod +x ./datadog-ci
          ./datadog-ci git-metadata upload
        env:
          DATADOG_API_KEY: ${{ secrets.DATADOG_API_KEY }}

  trigger-deploy:
    needs: [pre-deploy, build-social-service]
    name: Trigger social service deploy to develop
    runs-on: [t2gp-arc-linux]

    steps:
      - name: Repository Dispatch Develop
        if: ${{ github.ref == 'refs/heads/develop' }}
        uses: peter-evans/repository-dispatch@v2.0.1
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          event-type: 'Deploy to ${{needs.pre-deploy.outputs.env}}'
          client-payload: |
            {
              "ref": "${{ github.ref }}",
              "env": "${{ needs.pre-deploy.outputs.env }}",
              "image_tag":"${{ needs.build-social-service.outputs.image_tag }}",
              "origin_event_name":"${{ github.event_name }}",
              "origin_actor":"${{ github.actor }}",
              "origin_triggering_actor":"${{ github.triggering_actor }}",
              "origin_head_commit_id": "${{ github.event.head_commit.id }}",
              "origin_head_commit_url": "${{ github.event.head_commit.url }}",
              "origin_head_commit_message": ${{ toJSON(github.event.head_commit.message) }}
            }
      - name: Repository Dispatch Release
        if: ${{ startsWith(github.ref,'refs/heads/release/') }}
        uses: peter-evans/repository-dispatch@v2.0.1
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          event-type: 'Deploy to ${{needs.pre-deploy.outputs.env}}'
          client-payload: |
            {
              "ref": "${{ github.ref }}",
              "env": "${{ needs.pre-deploy.outputs.env }}",
              "image_tag":"${{ needs.build-social-service.outputs.image_tag }}",
              "origin_event_name":"${{ github.event_name }}"
            }
      - name: Repository Dispatch LoadTesting
        if: ${{ github.ref == 'refs/heads/loadtesting' }}
        uses: peter-evans/repository-dispatch@v2.0.1
        with:
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          event-type: 'Deploy to loadtesting'
          client-payload: |
            {
              "ref": "${{ github.ref }}",
              "env": "${{ needs.pre-deploy.outputs.env }}",
              "image_tag":"${{ needs.build-social-service.outputs.image_tag }}",
              "origin_event_name":"${{ github.event_name }}"
            }
