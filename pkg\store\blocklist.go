package store

import (
	"context"
	"errors"
	"net/http"
	"strings"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/feature/dynamodb/attributevalue"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb"
	"github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

func (ds *DataStore) GetBlocklistWithLimit(ctx context.Context, userid, productid string, ost apipub.OnlineServiceType, limit int, next *string) (*[]*apipub.BlocklistResponse, *string, error) {
	query := buildGetBlocklistDynamoQuery(ctx, ds, userid, int32(limit), next)

	blocklistResults, returnNext, err := ds.QueryByPkSkWithLimitAndNext(ctx, query)
	if err != nil {
		return nil, nil, err
	}
	if len(blocklistResults) == 0 {
		return nil, nil, nil
	}
	if returnNext != nil {
		parts := strings.Split(*returnNext, "#")
		*returnNext = parts[len(parts)-1]
	}

	var blockedIds []*apipub.BlocklistResponse
	// process query for blocklists
	for _, item := range blocklistResults {
		var blocklistItem apipub.ItemPKSK
		err := attributevalue.UnmarshalMap(item, &blocklistItem)
		if err != nil {
			logger.FromContext(ctx).Error().Err(err).Msgf("UnmarshalMap failed to parse blocklist %v", item)
			continue
		}

		parts := strings.Split(blocklistItem.SK, "#")
		blockedid := parts[len(parts)-1]

		blocklist := apipub.BlocklistResponse{
			Userid:            userid,
			Blockedid:         blockedid,
			Productid:         productid,
			OnlineServiceType: ost,
		}

		blockedIds = append(blockedIds, &blocklist)
	}
	return &blockedIds, returnNext, nil
}

func buildGetBlocklistDynamoQuery(ctx context.Context, ds *DataStore, userId string, limit int32, next *string) dynamodb.QueryInput {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	pk := tenant + "#user#" + userId
	sk := tenant + "#blocklist#"
	toKey := sk + utils.GetMaxUTF8()
	query := dynamodb.QueryInput{
		TableName:              &ds.cfg.ProfileTable,
		KeyConditionExpression: aws.String("pk = :pk AND sk BETWEEN :sk AND :toKey"),
		ExpressionAttributeValues: map[string]types.AttributeValue{
			":pk":    &types.AttributeValueMemberS{Value: pk},
			":sk":    &types.AttributeValueMemberS{Value: sk},
			":toKey": &types.AttributeValueMemberS{Value: toKey},
		},
		ScanIndexForward: aws.Bool(true),
		Limit:            aws.Int32(limit),
	}

	if next != nil {
		query.ExclusiveStartKey = map[string]types.AttributeValue{
			"pk": &types.AttributeValueMemberS{Value: tenant + "#user#" + userId},
			"sk": &types.AttributeValueMemberS{Value: tenant + "#blocklist#" + *next},
		}
	}

	return query
}

func (ds *DataStore) ModifyBlocklist(ctx context.Context, userId string, blockedUsers *[]*apipub.BlocklistResponse, unblockedUsers *[]*apipub.BlocklistResponse) error {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	var blockedUserIds []string
	var ops []types.WriteRequest
	if blockedUsers != nil {
		//add blocked users
		for _, block := range *blockedUsers {
			if block.Userid == "" {
				continue
			}

			//collate blockedids for remove friends later.  also save userid.
			blockedUserIds = append(blockedUserIds, block.Blockedid)
			temp := types.WriteRequest{
				PutRequest: &types.PutRequest{
					Item: map[string]types.AttributeValue{
						"pk": &types.AttributeValueMemberS{Value: block.PK(tenant)},
						"sk": &types.AttributeValueMemberS{Value: block.SK(tenant)},
					},
				},
			}
			ops = append(ops, temp)
		}
	}

	// remove friendship from blocked users
	if len(blockedUserIds) > 0 {
		rfErr := removeFriendsBlocklist(ctx, ds, userId, &blockedUserIds)
		if rfErr != nil {
			return rfErr
		}
	}

	//remove unblocked users
	if unblockedUsers != nil {
		for _, unblock := range *unblockedUsers {
			if unblock.Userid == "" {
				continue
			}

			temp := types.WriteRequest{
				DeleteRequest: &types.DeleteRequest{
					Key: map[string]types.AttributeValue{
						"pk": &types.AttributeValueMemberS{Value: tenant + "#user#" + unblock.Userid},
						"sk": &types.AttributeValueMemberS{Value: tenant + "#blocklist#" + unblock.Blockedid},
					},
				},
			}

			ops = append(ops, temp)
		}
	}

	if len(ops) > 0 {
		var sendOps []types.WriteRequest

		for i := 0; i < len(ops); i++ {
			sendOps = append(sendOps, ops[i])

			// Send in batches of 20 operations
			if i%20 == 19 || i == len(ops)-1 {
				input := &dynamodb.BatchWriteItemInput{
					RequestItems: map[string][]types.WriteRequest{
						ds.cfg.ProfileTable: sendOps,
					},
				}

				//Todo
				// OpenTelemetry span (replace with actual tracing if necessary)
				//ctx, span := tracer.Start(ctx, "ddb.BatchWriteItem")
				//defer span.End()

				// Example function to set PK/SK from write request for tracing (replace with actual logic)
				//setPkSkFromWriteReq(span, sendOps)

				// Perform the BatchWriteItem operation
				_, err := ds.ddb.BatchWriteItem(ctx, input)
				if err != nil {
					logger.FromContext(ctx).Error().Err(err).Msgf("BatchWriteItem() for blocklist for %s ", userId)
				}

				// Reset sendOps for the next batch
				sendOps = nil
			}
		}
	}

	return nil
}

func (ds *DataStore) DoesExceedMaxBlockCount(ctx context.Context, userId string, add int) (bool, error) {
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	count, err := ds.QueryItemCount(ctx, tenant+"#user#"+userId, tenant+"#blocklist#")
	if err != nil {
		return true, err
	}
	if count != nil && int(*count)+add > ds.cfg.MaxBlocks {
		return true, nil
	}
	return false, nil
}

func removeFriendsBlocklist(ctx context.Context, ds *DataStore, userid string, blockedIds *[]string) error {
	var requests []types.WriteRequest
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	for _, blockedId := range *blockedIds {
		friend1 := apipub.FriendResponse{
			Userid:   userid,
			Friendid: blockedId,
		}
		friend2 := apipub.FriendResponse{
			Userid:   blockedId,
			Friendid: userid,
		}

		requests = append(requests, types.WriteRequest{
			DeleteRequest: &types.DeleteRequest{
				Key: map[string]types.AttributeValue{
					"pk": &types.AttributeValueMemberS{Value: friend1.PK(tenant)},
					"sk": &types.AttributeValueMemberS{Value: friend1.SK(tenant)},
				},
			},
		})
		requests = append(requests, types.WriteRequest{
			DeleteRequest: &types.DeleteRequest{
				Key: map[string]types.AttributeValue{
					"pk": &types.AttributeValueMemberS{Value: friend2.PK(tenant)},
					"sk": &types.AttributeValueMemberS{Value: friend2.SK(tenant)},
				},
			},
		})
	}

	err := ds.BatchWriteItems(ctx, &requests)
	if err != nil {
		for _, blockedId := range *blockedIds {
			unsubscribePresence(ctx, ds, userid, blockedId)
		}
	}

	return err
}

func unsubscribePresence(ctx context.Context, ds *DataStore, userid, blockedUserId string) {
	messenger.Unsubscribe(ctx, ds.cfg, userid, "presence/"+blockedUserId)
	messenger.Unsubscribe(ctx, ds.cfg, blockedUserId, "presence/"+userid)
}

func (ds *DataStore) DoesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error) {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, ds.id)
	pk := tenant + "#user#" + blockerid
	sk := tenant + "#blocklist#" + blockeeid
	var blockItem *apipub.BlocklistResponse
	res, err := ds.GetItem(ctx, pk, sk, blockItem)
	if err == nil {
		return res, nil
	}
	log.Error().Err(err).Str("event", "BlockerBlockBlockee store error").Msg("BlockerBlockBlockee store error")
	var castToErrs *errs.Error
	ok := errors.As(errs.SanitizeDynamoDBException(err), &castToErrs)
	if ok {
		return res, castToErrs
	}
	return res, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed)
}
