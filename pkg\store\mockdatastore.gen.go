// Code generated by MockGen. DO NOT EDIT.
// Source: pkg/store/datastore.go

// Package store is a generated GoMock package.
package store

import (
	context "context"
	reflect "reflect"

	dynamodb "github.com/aws/aws-sdk-go-v2/service/dynamodb"
	s3 "github.com/aws/aws-sdk-go-v2/service/s3"
	gomock "go.uber.org/mock/gomock"
	apipub "github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	apitrusted "github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	errs "github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
)

// MockDataStoreItem is a mock of DataStoreItem interface.
type MockDataStoreItem struct {
	ctrl     *gomock.Controller
	recorder *MockDataStoreItemMockRecorder
}

// MockDataStoreItemMockRecorder is the mock recorder for MockDataStoreItem.
type MockDataStoreItemMockRecorder struct {
	mock *MockDataStoreItem
}

// NewMockDataStoreItem creates a new mock instance.
func NewMockDataStoreItem(ctrl *gomock.Controller) *MockDataStoreItem {
	mock := &MockDataStoreItem{ctrl: ctrl}
	mock.recorder = &MockDataStoreItemMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataStoreItem) EXPECT() *MockDataStoreItemMockRecorder {
	return m.recorder
}

// PK mocks base method.
func (m *MockDataStoreItem) PK(tenant string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PK", tenant)
	ret0, _ := ret[0].(string)
	return ret0
}

// PK indicates an expected call of PK.
func (mr *MockDataStoreItemMockRecorder) PK(tenant interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PK", reflect.TypeOf((*MockDataStoreItem)(nil).PK), tenant)
}

// SK mocks base method.
func (m *MockDataStoreItem) SK(tenant string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SK", tenant)
	ret0, _ := ret[0].(string)
	return ret0
}

// SK indicates an expected call of SK.
func (mr *MockDataStoreItemMockRecorder) SK(tenant interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SK", reflect.TypeOf((*MockDataStoreItem)(nil).SK), tenant)
}

// MockDataStoreInterface is a mock of DataStoreInterface interface.
type MockDataStoreInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDataStoreInterfaceMockRecorder
}

// MockDataStoreInterfaceMockRecorder is the mock recorder for MockDataStoreInterface.
type MockDataStoreInterfaceMockRecorder struct {
	mock *MockDataStoreInterface
}

// NewMockDataStoreInterface creates a new mock instance.
func NewMockDataStoreInterface(ctrl *gomock.Controller) *MockDataStoreInterface {
	mock := &MockDataStoreInterface{ctrl: ctrl}
	mock.recorder = &MockDataStoreInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataStoreInterface) EXPECT() *MockDataStoreInterfaceMockRecorder {
	return m.recorder
}

// DelTsClientId mocks base method.
func (m *MockDataStoreInterface) DelTsClientId(ctx context.Context, clientid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DelTsClientId", ctx, clientid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DelTsClientId indicates an expected call of DelTsClientId.
func (mr *MockDataStoreInterfaceMockRecorder) DelTsClientId(ctx, clientid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DelTsClientId", reflect.TypeOf((*MockDataStoreInterface)(nil).DelTsClientId), ctx, clientid)
}

// DeleteDiscovery mocks base method.
func (m *MockDataStoreInterface) DeleteDiscovery(ctx context.Context, productid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDiscovery", ctx, productid)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDiscovery indicates an expected call of DeleteDiscovery.
func (mr *MockDataStoreInterfaceMockRecorder) DeleteDiscovery(ctx, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDiscovery", reflect.TypeOf((*MockDataStoreInterface)(nil).DeleteDiscovery), ctx, productid)
}

// DeleteItemByPkSk mocks base method.
func (m *MockDataStoreInterface) DeleteItemByPkSk(ctx context.Context, pk, sk string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteItemByPkSk", ctx, pk, sk)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteItemByPkSk indicates an expected call of DeleteItemByPkSk.
func (mr *MockDataStoreInterfaceMockRecorder) DeleteItemByPkSk(ctx, pk, sk interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteItemByPkSk", reflect.TypeOf((*MockDataStoreInterface)(nil).DeleteItemByPkSk), ctx, pk, sk)
}

// DoesBlockerBlockBlockee mocks base method.
func (m *MockDataStoreInterface) DoesBlockerBlockBlockee(ctx context.Context, blockerid, blockeeid string) (bool, *errs.Error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoesBlockerBlockBlockee", ctx, blockerid, blockeeid)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(*errs.Error)
	return ret0, ret1
}

// DoesBlockerBlockBlockee indicates an expected call of DoesBlockerBlockBlockee.
func (mr *MockDataStoreInterfaceMockRecorder) DoesBlockerBlockBlockee(ctx, blockerid, blockeeid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoesBlockerBlockBlockee", reflect.TypeOf((*MockDataStoreInterface)(nil).DoesBlockerBlockBlockee), ctx, blockerid, blockeeid)
}

// DoesExceedMaxBlockCount mocks base method.
func (m *MockDataStoreInterface) DoesExceedMaxBlockCount(ctx context.Context, userId string, add int) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DoesExceedMaxBlockCount", ctx, userId, add)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DoesExceedMaxBlockCount indicates an expected call of DoesExceedMaxBlockCount.
func (mr *MockDataStoreInterfaceMockRecorder) DoesExceedMaxBlockCount(ctx, userId, add interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DoesExceedMaxBlockCount", reflect.TypeOf((*MockDataStoreInterface)(nil).DoesExceedMaxBlockCount), ctx, userId, add)
}

// GetBlocklistWithLimit mocks base method.
func (m *MockDataStoreInterface) GetBlocklistWithLimit(ctx context.Context, userid, productid string, ost apipub.OnlineServiceType, limit int, next *string) (*[]*apipub.BlocklistResponse, *string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlocklistWithLimit", ctx, userid, productid, ost, limit, next)
	ret0, _ := ret[0].(*[]*apipub.BlocklistResponse)
	ret1, _ := ret[1].(*string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetBlocklistWithLimit indicates an expected call of GetBlocklistWithLimit.
func (mr *MockDataStoreInterfaceMockRecorder) GetBlocklistWithLimit(ctx, userid, productid, ost, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlocklistWithLimit", reflect.TypeOf((*MockDataStoreInterface)(nil).GetBlocklistWithLimit), ctx, userid, productid, ost, limit, next)
}

// GetDiscovery mocks base method.
func (m *MockDataStoreInterface) GetDiscovery(ctx context.Context, productid string) (*[]apipub.DiscoveryResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiscovery", ctx, productid)
	ret0, _ := ret[0].(*[]apipub.DiscoveryResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDiscovery indicates an expected call of GetDiscovery.
func (mr *MockDataStoreInterfaceMockRecorder) GetDiscovery(ctx, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiscovery", reflect.TypeOf((*MockDataStoreInterface)(nil).GetDiscovery), ctx, productid)
}

// GetEndorsements mocks base method.
func (m *MockDataStoreInterface) GetEndorsements(ctx context.Context, userid, productid string) (*[]*apipub.EndorsementResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEndorsements", ctx, userid, productid)
	ret0, _ := ret[0].(*[]*apipub.EndorsementResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEndorsements indicates an expected call of GetEndorsements.
func (mr *MockDataStoreInterfaceMockRecorder) GetEndorsements(ctx, userid, productid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEndorsements", reflect.TypeOf((*MockDataStoreInterface)(nil).GetEndorsements), ctx, userid, productid)
}

// GetFriend mocks base method.
func (m *MockDataStoreInterface) GetFriend(ctx context.Context, userid, friendid string) (*apipub.FriendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFriend", ctx, userid, friendid)
	ret0, _ := ret[0].(*apipub.FriendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFriend indicates an expected call of GetFriend.
func (mr *MockDataStoreInterfaceMockRecorder) GetFriend(ctx, userid, friendid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFriend", reflect.TypeOf((*MockDataStoreInterface)(nil).GetFriend), ctx, userid, friendid)
}

// GetFriends mocks base method.
func (m *MockDataStoreInterface) GetFriends(ctx context.Context, userid string, status *apipub.FriendStatus, limit int, next *string) (*[]*apipub.FriendResponse, *string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFriends", ctx, userid, status, limit, next)
	ret0, _ := ret[0].(*[]*apipub.FriendResponse)
	ret1, _ := ret[1].(*string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetFriends indicates an expected call of GetFriends.
func (mr *MockDataStoreInterfaceMockRecorder) GetFriends(ctx, userid, status, limit, next interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFriends", reflect.TypeOf((*MockDataStoreInterface)(nil).GetFriends), ctx, userid, status, limit, next)
}

// GetFriendsCount mocks base method.
func (m *MockDataStoreInterface) GetFriendsCount(ctx context.Context, userid string) (*int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFriendsCount", ctx, userid)
	ret0, _ := ret[0].(*int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFriendsCount indicates an expected call of GetFriendsCount.
func (mr *MockDataStoreInterfaceMockRecorder) GetFriendsCount(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFriendsCount", reflect.TypeOf((*MockDataStoreInterface)(nil).GetFriendsCount), ctx, userid)
}

// GetFullFriendList mocks base method.
func (m *MockDataStoreInterface) GetFullFriendList(ctx context.Context, userid string) (*[]*apipub.FriendResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFullFriendList", ctx, userid)
	ret0, _ := ret[0].(*[]*apipub.FriendResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFullFriendList indicates an expected call of GetFullFriendList.
func (mr *MockDataStoreInterfaceMockRecorder) GetFullFriendList(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFullFriendList", reflect.TypeOf((*MockDataStoreInterface)(nil).GetFullFriendList), ctx, userid)
}

// GetTotalFriendsCount mocks base method.
func (m *MockDataStoreInterface) GetTotalFriendsCount(ctx context.Context, status string) (*int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalFriendsCount", ctx, status)
	ret0, _ := ret[0].(*int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalFriendsCount indicates an expected call of GetTotalFriendsCount.
func (mr *MockDataStoreInterfaceMockRecorder) GetTotalFriendsCount(ctx, status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalFriendsCount", reflect.TypeOf((*MockDataStoreInterface)(nil).GetTotalFriendsCount), ctx, status)
}

// GetTotalUserCount mocks base method.
func (m *MockDataStoreInterface) GetTotalUserCount(ctx context.Context) (*int32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTotalUserCount", ctx)
	ret0, _ := ret[0].(*int32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTotalUserCount indicates an expected call of GetTotalUserCount.
func (mr *MockDataStoreInterfaceMockRecorder) GetTotalUserCount(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTotalUserCount", reflect.TypeOf((*MockDataStoreInterface)(nil).GetTotalUserCount), ctx)
}

// GetTsClientId mocks base method.
func (m *MockDataStoreInterface) GetTsClientId(ctx context.Context, clientid string) (*apitrusted.TsClientIdInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTsClientId", ctx, clientid)
	ret0, _ := ret[0].(*apitrusted.TsClientIdInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTsClientId indicates an expected call of GetTsClientId.
func (mr *MockDataStoreInterfaceMockRecorder) GetTsClientId(ctx, clientid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTsClientId", reflect.TypeOf((*MockDataStoreInterface)(nil).GetTsClientId), ctx, clientid)
}

// GetUserProfile mocks base method.
func (m *MockDataStoreInterface) GetUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfile", ctx, userid)
	ret0, _ := ret[0].(*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfile indicates an expected call of GetUserProfile.
func (mr *MockDataStoreInterfaceMockRecorder) GetUserProfile(ctx, userid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfile", reflect.TypeOf((*MockDataStoreInterface)(nil).GetUserProfile), ctx, userid)
}

// GetUserProfiles mocks base method.
func (m *MockDataStoreInterface) GetUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUserProfiles", ctx, userids)
	ret0, _ := ret[0].(*[]*apipub.UserProfileResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserProfiles indicates an expected call of GetUserProfiles.
func (mr *MockDataStoreInterfaceMockRecorder) GetUserProfiles(ctx, userids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserProfiles", reflect.TypeOf((*MockDataStoreInterface)(nil).GetUserProfiles), ctx, userids)
}

// IncrementEndorsement mocks base method.
func (m *MockDataStoreInterface) IncrementEndorsement(ctx context.Context, userid, productid string, endorsement *apipub.EndorsementResponse, count int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementEndorsement", ctx, userid, productid, endorsement, count)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrementEndorsement indicates an expected call of IncrementEndorsement.
func (mr *MockDataStoreInterfaceMockRecorder) IncrementEndorsement(ctx, userid, productid, endorsement, count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementEndorsement", reflect.TypeOf((*MockDataStoreInterface)(nil).IncrementEndorsement), ctx, userid, productid, endorsement, count)
}

// MakeFriend mocks base method.
func (m *MockDataStoreInterface) MakeFriend(ctx context.Context, userid, friendid, message string, isUserBlocked bool, userOST apipub.OnlineServiceType) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeFriend", ctx, userid, friendid, message, isUserBlocked, userOST)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MakeFriend indicates an expected call of MakeFriend.
func (mr *MockDataStoreInterfaceMockRecorder) MakeFriend(ctx, userid, friendid, message, isUserBlocked, userOST interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeFriend", reflect.TypeOf((*MockDataStoreInterface)(nil).MakeFriend), ctx, userid, friendid, message, isUserBlocked, userOST)
}

// MakeUnfriend mocks base method.
func (m *MockDataStoreInterface) MakeUnfriend(ctx context.Context, userid, friendid string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MakeUnfriend", ctx, userid, friendid)
	ret0, _ := ret[0].(error)
	return ret0
}

// MakeUnfriend indicates an expected call of MakeUnfriend.
func (mr *MockDataStoreInterfaceMockRecorder) MakeUnfriend(ctx, userid, friendid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MakeUnfriend", reflect.TypeOf((*MockDataStoreInterface)(nil).MakeUnfriend), ctx, userid, friendid)
}

// ModifyBlocklist mocks base method.
func (m *MockDataStoreInterface) ModifyBlocklist(ctx context.Context, userId string, add, remove *[]*apipub.BlocklistResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModifyBlocklist", ctx, userId, add, remove)
	ret0, _ := ret[0].(error)
	return ret0
}

// ModifyBlocklist indicates an expected call of ModifyBlocklist.
func (mr *MockDataStoreInterfaceMockRecorder) ModifyBlocklist(ctx, userId, add, remove interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModifyBlocklist", reflect.TypeOf((*MockDataStoreInterface)(nil).ModifyBlocklist), ctx, userId, add, remove)
}

// PutItemInProfileTable mocks base method.
func (m *MockDataStoreInterface) PutItemInProfileTable(ctx context.Context, dsItem DataStoreItem) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutItemInProfileTable", ctx, dsItem)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutItemInProfileTable indicates an expected call of PutItemInProfileTable.
func (mr *MockDataStoreInterfaceMockRecorder) PutItemInProfileTable(ctx, dsItem interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutItemInProfileTable", reflect.TypeOf((*MockDataStoreInterface)(nil).PutItemInProfileTable), ctx, dsItem)
}

// PutTsClientId mocks base method.
func (m *MockDataStoreInterface) PutTsClientId(ctx context.Context, info *apitrusted.TsClientIdInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutTsClientId", ctx, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutTsClientId indicates an expected call of PutTsClientId.
func (mr *MockDataStoreInterfaceMockRecorder) PutTsClientId(ctx, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutTsClientId", reflect.TypeOf((*MockDataStoreInterface)(nil).PutTsClientId), ctx, info)
}

// PutUserProfile mocks base method.
func (m *MockDataStoreInterface) PutUserProfile(ctx context.Context, profile *apipub.UserProfileResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutUserProfile", ctx, profile)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutUserProfile indicates an expected call of PutUserProfile.
func (mr *MockDataStoreInterfaceMockRecorder) PutUserProfile(ctx, profile interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutUserProfile", reflect.TypeOf((*MockDataStoreInterface)(nil).PutUserProfile), ctx, profile)
}

// PutUserProfiles mocks base method.
func (m *MockDataStoreInterface) PutUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutUserProfiles", ctx, profiles)
	ret0, _ := ret[0].(error)
	return ret0
}

// PutUserProfiles indicates an expected call of PutUserProfiles.
func (mr *MockDataStoreInterfaceMockRecorder) PutUserProfiles(ctx, profiles interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutUserProfiles", reflect.TypeOf((*MockDataStoreInterface)(nil).PutUserProfiles), ctx, profiles)
}

// RemoveEndorsement mocks base method.
func (m *MockDataStoreInterface) RemoveEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveEndorsement", ctx, userid, productid, endorsementName)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveEndorsement indicates an expected call of RemoveEndorsement.
func (mr *MockDataStoreInterfaceMockRecorder) RemoveEndorsement(ctx, userid, productid, endorsementName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveEndorsement", reflect.TypeOf((*MockDataStoreInterface)(nil).RemoveEndorsement), ctx, userid, productid, endorsementName)
}

// ResetEndorsement mocks base method.
func (m *MockDataStoreInterface) ResetEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResetEndorsement", ctx, userid, productid, endorsementName)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResetEndorsement indicates an expected call of ResetEndorsement.
func (mr *MockDataStoreInterfaceMockRecorder) ResetEndorsement(ctx, userid, productid, endorsementName interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResetEndorsement", reflect.TypeOf((*MockDataStoreInterface)(nil).ResetEndorsement), ctx, userid, productid, endorsementName)
}

// SaveChatMessage mocks base method.
func (m *MockDataStoreInterface) SaveChatMessage(ctx context.Context, message *apipub.ChatMessage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveChatMessage", ctx, message)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveChatMessage indicates an expected call of SaveChatMessage.
func (mr *MockDataStoreInterfaceMockRecorder) SaveChatMessage(ctx, message interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveChatMessage", reflect.TypeOf((*MockDataStoreInterface)(nil).SaveChatMessage), ctx, message)
}

// SetDiscovery mocks base method.
func (m *MockDataStoreInterface) SetDiscovery(ctx context.Context, productid string, list *[]apitrusted.DiscoveryResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDiscovery", ctx, productid, list)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDiscovery indicates an expected call of SetDiscovery.
func (mr *MockDataStoreInterfaceMockRecorder) SetDiscovery(ctx, productid, list interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDiscovery", reflect.TypeOf((*MockDataStoreInterface)(nil).SetDiscovery), ctx, productid, list)
}

// UpdateFriends mocks base method.
func (m *MockDataStoreInterface) UpdateFriends(ctx context.Context, friends *[]*apipub.FriendResponse) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateFriends", ctx, friends)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateFriends indicates an expected call of UpdateFriends.
func (mr *MockDataStoreInterfaceMockRecorder) UpdateFriends(ctx, friends interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateFriends", reflect.TypeOf((*MockDataStoreInterface)(nil).UpdateFriends), ctx, friends)
}

// MockDynamoDBInterface is a mock of DynamoDBInterface interface.
type MockDynamoDBInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDynamoDBInterfaceMockRecorder
}

// MockDynamoDBInterfaceMockRecorder is the mock recorder for MockDynamoDBInterface.
type MockDynamoDBInterfaceMockRecorder struct {
	mock *MockDynamoDBInterface
}

// NewMockDynamoDBInterface creates a new mock instance.
func NewMockDynamoDBInterface(ctrl *gomock.Controller) *MockDynamoDBInterface {
	mock := &MockDynamoDBInterface{ctrl: ctrl}
	mock.recorder = &MockDynamoDBInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDynamoDBInterface) EXPECT() *MockDynamoDBInterfaceMockRecorder {
	return m.recorder
}

// BatchGetItem mocks base method.
func (m *MockDynamoDBInterface) BatchGetItem(ctx context.Context, params *dynamodb.BatchGetItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.BatchGetItemOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchGetItem", varargs...)
	ret0, _ := ret[0].(*dynamodb.BatchGetItemOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetItem indicates an expected call of BatchGetItem.
func (mr *MockDynamoDBInterfaceMockRecorder) BatchGetItem(ctx, params interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetItem", reflect.TypeOf((*MockDynamoDBInterface)(nil).BatchGetItem), varargs...)
}

// BatchWriteItem mocks base method.
func (m *MockDynamoDBInterface) BatchWriteItem(ctx context.Context, params *dynamodb.BatchWriteItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.BatchWriteItemOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BatchWriteItem", varargs...)
	ret0, _ := ret[0].(*dynamodb.BatchWriteItemOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchWriteItem indicates an expected call of BatchWriteItem.
func (mr *MockDynamoDBInterfaceMockRecorder) BatchWriteItem(ctx, params interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchWriteItem", reflect.TypeOf((*MockDynamoDBInterface)(nil).BatchWriteItem), varargs...)
}

// CreateTable mocks base method.
func (m *MockDynamoDBInterface) CreateTable(ctx context.Context, input *dynamodb.CreateTableInput, optFns ...func(*dynamodb.Options)) (*dynamodb.CreateTableOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTable", varargs...)
	ret0, _ := ret[0].(*dynamodb.CreateTableOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTable indicates an expected call of CreateTable.
func (mr *MockDynamoDBInterfaceMockRecorder) CreateTable(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTable", reflect.TypeOf((*MockDynamoDBInterface)(nil).CreateTable), varargs...)
}

// DeleteItem mocks base method.
func (m *MockDynamoDBInterface) DeleteItem(ctx context.Context, input *dynamodb.DeleteItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.DeleteItemOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteItem", varargs...)
	ret0, _ := ret[0].(*dynamodb.DeleteItemOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteItem indicates an expected call of DeleteItem.
func (mr *MockDynamoDBInterfaceMockRecorder) DeleteItem(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteItem", reflect.TypeOf((*MockDynamoDBInterface)(nil).DeleteItem), varargs...)
}

// DeleteTable mocks base method.
func (m *MockDynamoDBInterface) DeleteTable(ctx context.Context, input *dynamodb.DeleteTableInput, optFns ...func(*dynamodb.Options)) (*dynamodb.DeleteTableOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteTable", varargs...)
	ret0, _ := ret[0].(*dynamodb.DeleteTableOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteTable indicates an expected call of DeleteTable.
func (mr *MockDynamoDBInterfaceMockRecorder) DeleteTable(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteTable", reflect.TypeOf((*MockDynamoDBInterface)(nil).DeleteTable), varargs...)
}

// GetItem mocks base method.
func (m *MockDynamoDBInterface) GetItem(ctx context.Context, input *dynamodb.GetItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.GetItemOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetItem", varargs...)
	ret0, _ := ret[0].(*dynamodb.GetItemOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetItem indicates an expected call of GetItem.
func (mr *MockDynamoDBInterfaceMockRecorder) GetItem(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetItem", reflect.TypeOf((*MockDynamoDBInterface)(nil).GetItem), varargs...)
}

// ListTables mocks base method.
func (m *MockDynamoDBInterface) ListTables(ctx context.Context, input *dynamodb.ListTablesInput, optFns ...func(*dynamodb.Options)) (*dynamodb.ListTablesOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListTables", varargs...)
	ret0, _ := ret[0].(*dynamodb.ListTablesOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTables indicates an expected call of ListTables.
func (mr *MockDynamoDBInterfaceMockRecorder) ListTables(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTables", reflect.TypeOf((*MockDynamoDBInterface)(nil).ListTables), varargs...)
}

// PutItem mocks base method.
func (m *MockDynamoDBInterface) PutItem(ctx context.Context, input *dynamodb.PutItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.PutItemOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutItem", varargs...)
	ret0, _ := ret[0].(*dynamodb.PutItemOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutItem indicates an expected call of PutItem.
func (mr *MockDynamoDBInterfaceMockRecorder) PutItem(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutItem", reflect.TypeOf((*MockDynamoDBInterface)(nil).PutItem), varargs...)
}

// Query mocks base method.
func (m *MockDynamoDBInterface) Query(ctx context.Context, input *dynamodb.QueryInput, optFns ...func(*dynamodb.Options)) (*dynamodb.QueryOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Query", varargs...)
	ret0, _ := ret[0].(*dynamodb.QueryOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *MockDynamoDBInterfaceMockRecorder) Query(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*MockDynamoDBInterface)(nil).Query), varargs...)
}

// Scan mocks base method.
func (m *MockDynamoDBInterface) Scan(ctx context.Context, input *dynamodb.ScanInput, optFns ...func(*dynamodb.Options)) (*dynamodb.ScanOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Scan", varargs...)
	ret0, _ := ret[0].(*dynamodb.ScanOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Scan indicates an expected call of Scan.
func (mr *MockDynamoDBInterfaceMockRecorder) Scan(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockDynamoDBInterface)(nil).Scan), varargs...)
}

// UpdateItem mocks base method.
func (m *MockDynamoDBInterface) UpdateItem(ctx context.Context, params *dynamodb.UpdateItemInput, optFns ...func(*dynamodb.Options)) (*dynamodb.UpdateItemOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, params}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateItem", varargs...)
	ret0, _ := ret[0].(*dynamodb.UpdateItemOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateItem indicates an expected call of UpdateItem.
func (mr *MockDynamoDBInterfaceMockRecorder) UpdateItem(ctx, params interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, params}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateItem", reflect.TypeOf((*MockDynamoDBInterface)(nil).UpdateItem), varargs...)
}

// MockS3Interface is a mock of S3Interface interface.
type MockS3Interface struct {
	ctrl     *gomock.Controller
	recorder *MockS3InterfaceMockRecorder
}

// MockS3InterfaceMockRecorder is the mock recorder for MockS3Interface.
type MockS3InterfaceMockRecorder struct {
	mock *MockS3Interface
}

// NewMockS3Interface creates a new mock instance.
func NewMockS3Interface(ctrl *gomock.Controller) *MockS3Interface {
	mock := &MockS3Interface{ctrl: ctrl}
	mock.recorder = &MockS3InterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockS3Interface) EXPECT() *MockS3InterfaceMockRecorder {
	return m.recorder
}

// DeleteObject mocks base method.
func (m *MockS3Interface) DeleteObject(ctx context.Context, input *s3.DeleteObjectInput, optFns ...func(*s3.Options)) (*s3.DeleteObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteObject", varargs...)
	ret0, _ := ret[0].(*s3.DeleteObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteObject indicates an expected call of DeleteObject.
func (mr *MockS3InterfaceMockRecorder) DeleteObject(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteObject", reflect.TypeOf((*MockS3Interface)(nil).DeleteObject), varargs...)
}

// GetObject mocks base method.
func (m *MockS3Interface) GetObject(ctx context.Context, input *s3.GetObjectInput, optFns ...func(*s3.Options)) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetObject", varargs...)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockS3InterfaceMockRecorder) GetObject(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockS3Interface)(nil).GetObject), varargs...)
}

// HeadBucket mocks base method.
func (m *MockS3Interface) HeadBucket(ctx context.Context, input *s3.HeadBucketInput, optFns ...func(*s3.Options)) (*s3.HeadBucketOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HeadBucket", varargs...)
	ret0, _ := ret[0].(*s3.HeadBucketOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HeadBucket indicates an expected call of HeadBucket.
func (mr *MockS3InterfaceMockRecorder) HeadBucket(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadBucket", reflect.TypeOf((*MockS3Interface)(nil).HeadBucket), varargs...)
}

// PutObject mocks base method.
func (m *MockS3Interface) PutObject(ctx context.Context, input *s3.PutObjectInput, optFns ...func(*s3.Options)) (*s3.PutObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, input}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutObject", varargs...)
	ret0, _ := ret[0].(*s3.PutObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutObject indicates an expected call of PutObject.
func (mr *MockS3InterfaceMockRecorder) PutObject(ctx, input interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, input}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutObject", reflect.TypeOf((*MockS3Interface)(nil).PutObject), varargs...)
}
