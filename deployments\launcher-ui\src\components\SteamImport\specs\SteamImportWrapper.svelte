<script lang="ts">
  import { setContext } from 'svelte';
  import {
    CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT,
    INITIAL_LANGUAGE,
  } from '../../../constant';
  import { initI18nContext, SocialServicesContext } from '../../../context';
  import { useLinkedSteamId } from '../../../hooks';
  import SteamImport from '../SteamImport.svelte';

  export let context: SocialServicesContext;
  export let steamAccountLinked = false;
  export let linkedSteamAccountId = '';

  const linkedSteamId = useLinkedSteamId();

  setContext(CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT, context);
  initI18nContext(INITIAL_LANGUAGE);

  if (linkedSteamAccountId) {
    linkedSteamId.set(linkedSteamAccountId);
  }
</script>

<SteamImport bind:steamAccountLinked />
