-module(t2gp_social_lager).
-export([
    format/2, format/3,
    dd_meta/0,
    init/0
]).

-include_lib("opencensus/include/opencensus.hrl").
-include_lib("lager/include/lager.hrl").

-define(stack_trace(), unicode:characters_to_binary(list_to_binary(lager:pr_stacktrace(t2gp_social_apm:stack_trace())), unicode)).

-spec init() -> ok.
init() ->
    Level = case lager:get_loglevel(lager_console_backend) of
        {error, _} -> info;
        L -> L
    end,
    application:set_env(lager, reverse_pretty_stacktrace, false),
    application:set_env(lager, handlers, [
        {lager_console_backend,[{level,info}, {formatter, t2gp_social_lager}]},
        {lager_file_backend,[{file,"./log/error.log"},
                        {level,error},
                        {size,10485760},
                        {date,"$D0"},
                        {count,5}]}]),
    % restart lager to pick up configuration
    application:stop(lager),
    application:ensure_all_started(lager),
    lager:set_loglevel(lager_console_backend, Level),
    ok.

%% store stack trace in lager meta for later processing
-spec dd_meta() -> [{atom(), any()}].
dd_meta() ->
    [{stack, ?stack_trace()}].

-spec format(lager_msg:lager_msg(),list(), list()) -> any().
format(Msg, Config, _Colors) -> format(Msg, Config).

-spec format(lager_msg:lager_msg(),list()) -> any().
format(Msg, Config) ->
    JSONMap = msg_to_map(Msg),
    JSONMap1 = add_static_config(JSONMap, proplists:get_value(static, Config)),
    JSON = jsx:encode(JSONMap1),
    Sep = proplists:get_value(separator, Config, <<"\n">>),
    [JSON, Sep].

-spec add_static_config(map(), undefined | map()) -> map().
add_static_config(JSONMap, undefined) ->
    JSONMap;
add_static_config(JSONMap, Static) ->
    JSONMap#{static => Static}.

-spec msg_to_map(lager_msg:lager_msg()) -> map().
msg_to_map(Msg) ->
    Severity = severity(Msg),
    Message = message(Msg),
    Default = #{
        message => Message,
        severity => Severity,
        timestamp => timestamp(Msg),
        metadata => metadata(Msg)
    },
    Stack = case Severity of
        error ->
            MD = lager_msg:metadata(Msg),
            DD = proplists:get_value(dd, MD, []),
            S = proplists:get_value(stack, DD, <<>>),
            % this can't be done on the emit which is where we're at here. it has to be done on the logging
            % t2gp_social_apm:tags(#{'error.stack' => S, 'error.message' => Message, error => 1}),
            #{'error.stack' => S};
        _ -> #{}
    end,
    Maps = [Default, datadog('dd.span_id', Msg), datadog('dd.trace_id', Msg), Stack],
    lists:foldl(fun(M, Acc) -> maps:merge(Acc, M) end, #{}, Maps).

%% store stack trace in lager meta for later processing
-spec datadog(atom(), lager:message()) -> map().
datadog(Key, Msg) ->
    IsDD = fun ({K, _}) -> K == Key end,
    Meta = lists:filter(IsDD, lager_msg:metadata(Msg)),
    maps:from_list(Meta).

-spec message(lager_msg:lager_msg()) -> binary().
message(Msg) ->
    unicode:characters_to_binary(lager_msg:message(Msg), unicode).

-spec severity(lager_msg:lager_msg()) -> atom().
severity(Msg) ->
    lager_msg:severity(Msg).

-spec timestamp(lager_msg:lager_msg()) -> binary().
timestamp(Msg) ->
    {MegaSec, Sec, MicroSec} = lager_msg:timestamp(Msg),
    USec = MegaSec * 1000000000000 + Sec * 1000000 + MicroSec,
    {ok, TimeStamp} = rfc3339:format(USec, micro_seconds),
    TimeStamp.

-spec metadata(lager_msg:lager_msg()) -> list().
metadata(Msg) ->
    SkipKeys = ['dd.span_id', 'dd.trace_id', 'dd'],
    IsNotDD = fun ({K,_}) -> lists:search(fun(SK) -> SK == K end, SkipKeys) == false end,
    Meta = lists:filter(IsNotDD, lager_msg:metadata(Msg)),
    case Meta of
        [] -> [{}];
        Else -> lists:map(fun printable/1, Else)
    end.

%% can't naively encode `File` or `Pid` as json as jsx see them as lists
%% of integers
-spec printable({atom(), any()}) -> binary().
printable({file, File}) ->
    {file, unicode:characters_to_binary(File, unicode)};
printable({pid, Pid}) ->
    {pid, pid_list(Pid)};
%% if a value is expressable in json use it directly, otherwise
%% try to get a printable representation and express it as a json
%% string
printable({Key, Value}) when is_atom(Key); is_binary(Key) ->
    case jsx:is_term(Value) of
        true -> {Key, Value};
        false -> {Key, unicode:characters_to_binary(io_lib:format("~p", [Value]), unicode)}
    end.

-spec pid_list(pid()) -> binary().
pid_list(Pid) ->
    try unicode:characters_to_binary(Pid, unicode) of
        Pid0 -> Pid0
    catch
        error:badarg ->
            unicode:characters_to_binary(hd(io_lib:format("~p", [Pid])), unicode)
    end.
