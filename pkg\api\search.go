package api

import (
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// Search2KUsers searchs 2K full accounts.
func (api *SocialPublicAPI) Search2KUsers(w http.ResponseWriter, r *http.Request, params apipub.Search2KUsersParams) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	if params.DisplayName == "" {
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EDnaSearchAccountsFailed))
		return
	}

	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	userid := token.Claims.Subject
	appid := token.Claims.Issuer
	productid := token.Claims.ProductID

	additionalInfo := make(map[string]string)
	api.Tele.SendFriendEvent(r.Context(), telemetry.BuildFriendTeleMeta(telemetry.KSearchDisplayName, productid, userid, ost, []string{params.DisplayName}, &appid, &additionalInfo))

	criterion := apipub.SearchAccountCriteria{
		DisplayName: &params.DisplayName,
	}

	searchType := "fullAccountByDisplayName"
	searchRequest := &apipub.SearchAccountRequest{
		Type:      (*apipub.SearchAccountRequestType)(&searchType),
		Criterias: &[]apipub.SearchAccountCriteria{criterion},
	}

	searchResponse, err := api.Id.SearchAccounts(r.Context(), searchRequest)
	if err != nil {
		log.Error().Err(err).Msg("Seach failed")
		errCode := errs.EDnaSearchAccountsFailed
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errCode))
		return
	}

	var ret apipub.Search2kUserResponse

	searchResults := []apipub.TwoKSearch{}

	if searchResponse != nil {
		for _, account := range *searchResponse {
			var result apipub.TwoKSearch
			//skip if no parent accountid.  full accounts required to make friends.
			if account.Type == nil || !(*account.Type == apipub.AccountTypeDNAFULL) {
				continue
			}
			//remove DOB and Email from return
			account.Dob = nil
			account.Email = nil
			//filter account links
			identity.FilterLinksByOST(account.Links, ost)
			id := ""
			if account.Id != nil {
				id = *account.Id
			}

			var simplePresence []apipub.SimplePresenceResponse
			presences, err2 := api.Cache.GetUserPresences(r.Context(), id, productid)
			if err2 != nil {
				log.Error().Err(err2).Str("event", "failed to get user presence").Str("accountid", id).Msg("failed to get user presence")
			}
			if presences != nil && len(*presences) > 0 {
				//iterate through all user presnces and filter priority params and always also include user defined and launcher set
				for _, presence := range *presences {
					if presence != nil {
						if presence.Priority == apipub.PresesencePriorityUserSet || presence.Priority == apipub.PresencePriorityLauncherAutomated || (presence.Priority >= apipub.PresencePriorityGameSetStart && presence.Priority <= apipub.PresencePriorityGameSetEnd) {
							simpPresence := &apipub.SimplePresenceResponse{
								Userid:            presence.Userid,
								Status:            presence.Status,
								GameName:          &presence.GameName,
								OnlineServiceType: &(presence.OnlineServiceType),
							}

							simplePresence = append(simplePresence, *simpPresence)
						}
					}
				}
				result.SimplePresence = &simplePresence
			}
			result.Name = account.DisplayName
			result.Userid = id
			result.Links = account.Links
			searchResults = append(searchResults, result)
		}
	}

	ret.Items = searchResults

	ReturnOK(w, r, ret)
}
