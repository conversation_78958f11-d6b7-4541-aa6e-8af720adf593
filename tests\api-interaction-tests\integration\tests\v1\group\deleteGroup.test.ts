import { StatusCodes } from 'http-status-codes';
import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../../integration/lib/config';

let usersTwok: TwokAccounts;

beforeEach(async () => {
  usersTwok = new TwokAccounts(2, ["leader", "member1"]);
  await usersTwok.loginAll({});
});

afterEach(async done => {
  await usersTwok.logoutAll({});
  done();
});

describe('[public v1]', () => {
  let groupId: string;

  beforeEach(async () => {
    let r = await socialApi.createGroupV1(
      usersTwok.acct["leader"],
      {
        maxMembers: 6,
        joinRequestAction: 'auto-approve',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    // user joins the auto-approve group
    const resp = await socialApi.requestToJoinV1(
      usersTwok.acct["member1"],
      groupId,
      {
        memberid: usersTwok.acct["member1"].publicId,
        status: 'requested',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, resp);
  });

  afterEach(async () => {
    await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
  });

  it('Leader can disband his/her group[happy trusted]', async () => {
    // Leader delete own group
    const actualDeletedInfo = await socialApi.deleteGroup(usersTwok.acct["leader"], groupId);
    socialApi.testStatus(StatusCodes.OK, actualDeletedInfo);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["leader"],
      groupId
    );
    // Expect the leader deleted the group successfully. The group does not exist.
    socialApi.testStatus(StatusCodes.NOT_FOUND, actualGroupInfo);
  });

  it('Member cannot disband group', async () => {
    let testCase = {
      description: "member deletes a group",
      expected: "the group still exists"
    };

    // Member deleting a group returns error
    const actualDeletedInfo = await socialApi.deleteGroup(usersTwok.acct["member1"], groupId);
    socialApi.testStatus(StatusCodes.FORBIDDEN, actualDeletedInfo);

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersTwok.acct["member1"],
      groupId
    );

    // Expect The group still exists, and the member does not have permission to delete the group.
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: { groupid: groupId },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "the group does not exist"
        }
      }
    );
  });
});

describe('', () => {
  it('Leader disbands non-existing group returns error[public v1 trusted]', async () => {
    // The leader disbands a nonexistent group.
    const mockGroup = '01FS0YF8G91688T9VHZF4T3FZ0';
    const actualDeletedInfo = await socialApi.deleteGroup(usersTwok.acct["leader"], mockGroup);

    // Expect the leader cannot disband the deleted group.
    socialApi.testStatus(StatusCodes.NOT_FOUND, actualDeletedInfo);
  });
});
