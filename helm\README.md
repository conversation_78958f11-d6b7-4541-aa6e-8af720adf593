# Helm Package Outline

## Overview

Referring to the architecture diagram
![architecture](../docs/architecture.png)

We've got 2 components to deploy: **API** and **MQTT**
The social helm chart is similarily divided into two subcharts: `social-api` and `social-mqtt`
Components live under `social-service` namespace. Corresponding configmaps: [non-production](https://github.com/take-two-t2gp/d2c-config-mgmt/tree/non-production/src/social) and [production](https://github.com/take-two-t2gp/d2c-config-mgmt/tree/production/src/social)

## social-api

This is similar to all other API projects in T2GP.

Broken down to
|Component|Description|
|-|-|
|`deployment`| spawns the pods on any applicable node|
|`hpa`| creates scaling policies |
|`ingress-private`| creates an ALB+R53 record that points to private API|
|`ingress`| creates an ingress that binds to ALB+R53 record that points to public API |
|`pdb`| forces an PodDisruptionBudget (so one pod can always live when rebalancing is needed) |
|`service`| creates a service in k8s to route traffic to pods|
|`serviceaccount`| creates a ServiceAccount that associates to an IAM role for DynamoDB + S3 Access|

## social-mqtt

Spun as a Statefulset, this subcomponent setups the MQTT broker for API to interact.
Every pod in the statefulset has an EBS volume attached, in order to store t2gp-plugin. This is enabled by the EBS CSI driver installed on the cluster. ([non-production](https://github.com/take-two-t2gp/t2gp-eks-manifests/blob/master/shared_manifests/kube-system/eks/aws_ebs_csi_driver_release.yaml) and [production](https://github.com/take-two-t2gp/t2gp-eks/blob/production/aws_ebs_csi_driver.tf))
Because the component is designed such that pod disruption should be minimal, **updates to the statefulset will not propagate unless a manual restart of the pods is issued**. The plugin that sits on EBS, however, is hotswapped everytime a deploy happens.
aaa
Broken down to
|Component|Description|
|-|-|
|`statefulset`(STS)| spawns the pods on any **mqtt tainted** nodes|
|`ingress`| creates an ingress that binds to ALB+R53 record that points to the MQTT broker |
|`service-sd`| creates a service for statefulset's self-service discovery |
|`service-ext`| creates a service in k8s to route traffic to pods|
|`serviceaccount`| creates a ServiceAccount that associates to an IAM role for DynamoDB + S3 Access|
|`role`| creates a role to allow pods in the STS to list and describe other pods in the same namespace (required for self-service-discovery)
|`rolebinding`| binds the `role` to `serviceaccount`|

## dynamodb (extra)

A self-hosted dynamodb can be setup and used if we want to limit traffic to AWS. This can be toggled through enabling the follow in the environment values file.
```yaml
global:
...
  selfDynamoDb:
    enabled: true
```
Enabling this flag will cause both API and MQTT (if enabled) to use self-hosted dynamodb.
If sandbox, the PR comments will contain the link to admin the dynamodb.