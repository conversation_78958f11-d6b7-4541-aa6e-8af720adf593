import requests
import os
import time
from collections import defaultdict
import boto3
from botocore.exceptions import ClientError
from datetime import datetime


slack_webhook_url = os.environ.get("SLACK_WEBHOOK_URL")

head_commit_id = os.environ.get("HEAD_COMMIT_ID") or "deadbeef"
head_commit_url = os.environ.get("HEAD_COMMIT_URL")
head_commit_message = os.environ.get("HEAD_COMMIT_MESSAGE") or "dummy commit message"

pr_title = os.environ.get("PR_TITLE")
pr_head_sha = os.environ.get("PR_HEAD_SHA")
pr_html_url = os.environ.get("PR_HTML_URL")

event_name = os.environ.get("EVENT_NAME") or "push"
actor = os.environ.get("ACTOR")
triggering_actor = os.environ.get("TRIGGERING_ACTOR")

social_API_access_level = os.environ.get("SOCIAL_API_ACCESS_LEVEL")
social_service_environment = os.environ.get("SOCIAL_SERVICE_ENVIRONMENT")
social_curr_api_version = os.environ.get("SOCIAL_API_VERSION")
social_curr_api_url = os.environ.get("PUBLIC_SERVICE_API_URL") if social_API_access_level == "public" else os.environ.get("TRUSTED_SERVER_API_URL")
social_curr_mqtt_url = os.environ.get("PUBLIC_SERVICE_MQTT_URL") if social_API_access_level == "public" else os.environ.get("TRUSTED_SERVER_MQTT_URL")

github_run_id = os.environ.get("GITHUB_RUN_ID")
github_server_url = os.environ.get("GITHUB_SERVER_URL")
github_repository = os.environ.get("GITHUB_REPOSITORY")

# Loop type
loop_type = os.environ.get("LOOP_TYPE")

# Run count
run_cnt = os.environ.get("RUN_CNT")
run_max = os.environ.get("RUN_MAX")

note = os.environ.get("NOTE")

mandatory_tag = os.environ.get("MANDATORY_TAG")
inclusion_tag = os.environ.get("OPTIONAL_INCLUSION_TAG")
exclusion_tag = os.environ.get("OPTIONAL_EXCLUSION_TAG")

test_suite = os.environ.get("TEST_SUITE")
test_case = os.environ.get("TEST_CASE")

#
class StringPaginator:
    def __init__(self, page_size):
        self.page_size = page_size
        self.str_list = []
        self.idx = -1

    # note that if new str is greater than page size, it's not broken up.
    def add_str(self, new_str):
        if len(self.str_list) == 0:
            self.str_list = [""]
            self.idx = 0

        curr_str = self.str_list[self.idx]
        if len(curr_str) + len(new_str) > self.page_size:
            self.idx += 1
            self.str_list.append("")
        self.str_list[self.idx] += new_str

    def get_str(self):
        return self.str_list

# should be used as a singleton
class TestCaseRemark:
    _remark_file_name = "ci-report-remarks.txt"
    _remark_file_path = "./{}".format(_remark_file_name)
    _bucket = "t2gp-social-develop"
    _remark_file_key = "t2gp-social-ci-test-report/{}".format(_remark_file_name)

    _remark = {}

    #
    def __init__(self):
        pass

    # download a local copy of the remote remark file
    # create a new empty file if remote doesn't exist
    def refresh_remark_file(self):
        s3 = boto3.client('s3')
        try:
            s3.download_file(Bucket=self._bucket, Key=self._remark_file_key, Filename=self._remark_file_path)
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                print("remote remark file NOT FOUND! This should happen only during initialization. Report this error if it's unexpected")

                # create an empty local remark file
                with open(self._remark_file_path, "x") as f:
                    pass

                # upload remark file to remote
                s3.upload_file(Filename=self._remark_file_path, Bucket=self._bucket, Key=self._remark_file_key)
                print("a new empty remark file created")
            else:
                raise e

    # load remark file from a local file to memory
    def load_remark_file(self):
        self.refresh_remark_file()

        with open(self._remark_file_path) as f:
            for line in f:
                field_list = line.split('~')

                # sanity check
                if len(field_list) < 2:
                    print("warning: invalid line [{}]".format(line))
                    continue

                if field_list[0] in self._remark.keys():
                    print("warning: duplicated test case name [{}]".format(field_list[0]))

                self._remark[field_list[0]] = field_list[1]

    #
    def get_remark(self, test_id):
        remark = ""
        if test_id in self._remark.keys():
            remark = self._remark[test_id]
        return remark

# should be used as a singleton
class TestResultHistory:
    _history_file_name = "result-history"
    _history_file_path = "./{}".format(_history_file_name)
    _bucket = "t2gp-social-develop"
    _history_file_key = "t2gp-social-ci-test-report/{}".format(_history_file_name)

    #
    _result_history = None

    #
    def __init__(self):
        pass

    #
    def init(self, timestamp, head_commit_url, log_url):
        self._result_history = defaultdict(lambda:dict(
                                            timestamp=timestamp,
                                            head_commit_url=head_commit_url,
                                            log_url=log_url,
                                            pass_num=0,
                                            fail_num=0
                                        ))

    # download a local copy of the remote history file
    # create a new empty file if remote doesn't exist
    def refresh_history_file(self):
        s3 = boto3.client('s3')
        try:
            s3.download_file(Bucket=self._bucket, Key=self._history_file_key, Filename=self._history_file_path)
        except ClientError as e:
            if e.response["Error"]["Code"] == "404":
                print("remote history file NOT FOUND! This should happen only during initialization. Report this error if it's unexpected")

                # create an empty local history file
                with open(self._history_file_path, "x") as f:
                    pass

                # upload history file to remote
                s3.upload_file(Filename=self._history_file_path, Bucket=self._bucket, Key=self._history_file_key)
                print("a new empty history file created")
            else:
                raise e

    # upload the local history file to remote
    def commit_history_file(self):
        s3 = boto3.client('s3')
        s3.upload_file(Filename=self._history_file_path, Bucket=self._bucket, Key=self._history_file_key)

    # load history file from a local file to memory
    def load_history_file(self):
        self.refresh_history_file()

        with open(self._history_file_path) as f:
            for line in f:
                # skip comments
                if line.startswith("#"):
                    continue

                property_list = [x.strip() for x in line.split("|")]

                # test id consists of test suite name and test case name
                test_id   = property_list[0]
                # timestamp of the first fail occurrence
                timestamp = int(property_list[1])
                # commit URL of the first fail occurrence
                head_commit_url  = property_list[2]
                # test run log URL of the first fail occurrence
                log_url  = property_list[3]
                # number of passes since first fail
                pass_num  = int(property_list[4])
                # number of fails since first pass
                fail_num  = int(property_list[5])

                self._result_history[test_id] = {
                    "timestamp": timestamp,
                    "head_commit_url": head_commit_url,
                    "log_url": log_url,
                    "pass_num": pass_num,
                    "fail_num": fail_num
                }

    # save history file from memory to a local file
    def save_history_file(self):
        with open(self._history_file_path, "w") as f:
            for test_id, history in self._result_history.items():
                timestamp       = history["timestamp"]
                head_commit_url = history["head_commit_url"]
                log_url         = history["log_url"]
                pass_num        = history["pass_num"]
                fail_num        = history["fail_num"]

                f.write("|".join([test_id,
                                  str(timestamp),
                                  head_commit_url,
                                  log_url,
                                  str(pass_num),
                                  str(fail_num)]) + "\n")

    #
    def update_history(self, test_id, result):
        if result == "p":
            if test_id in self._result_history.keys():
                self._result_history[test_id]["pass_num"] += 1
        elif result == "f":
            self._result_history[test_id]["fail_num"] += 1
        else:
            raise ValueError("unexpected result [{}]".format(result))

    #
    def get_duration_since_first_fail(self, test_id):
        first_fail_time = datetime.fromtimestamp(self._result_history[test_id]["timestamp"])
        current_time = datetime.fromtimestamp(int(time.time()))
        return current_time - first_fail_time

    #
    def get_head_commit_url(self, test_id):
        return self._result_history[test_id]["head_commit_url"]

    #
    def get_log_url(self, test_id):
        return self._result_history[test_id]["log_url"]

    #
    def get_fail_rate(self, test_id):
        total = self._result_history[test_id]["pass_num"] + self._result_history[test_id]["fail_num"]
        return int((self._result_history[test_id]["fail_num"] / total) * 100)

    #
    def get_history_str(self, test_id):
        if test_id in self._result_history.keys():
            history_str = "fail rate: {}% | failed since {} ago | <{}|commit> | <{}|log>".format(
                            self.get_fail_rate(test_id),
                            self.get_duration_since_first_fail(test_id),
                            self.get_head_commit_url(test_id),
                            self.get_log_url(test_id)
                        )
        else:
            history_str = None

        return history_str


# test_results schema:
#{
#    "tr_name":test_run_name,
#    "total_duration":total_duration,
#    "completion_time":completion_time,
#    "ts_list":[
#        {
#            "ts_name":test_suite_name,
#            "tc_list":[
#                {
#                    "tc_name":test_case_name,
#                    "result":status           # "pass" or "fail"
#                },
#                ...
#            ]
#        },
#        ...
#    ]
#}
# Post results to a Slack channel
def post_results(test_results):
    if slack_webhook_url == None or slack_webhook_url == "":
        print("SLACK_WEBHOOK_URL not defined. Results are not posted to a Slack channel")
        return

    #
    current_build_url="{}/{}/actions/runs/{}".format(github_server_url,
                                                     github_repository,
                                                     github_run_id)

    # get the test case remark
    test_case_remark = TestCaseRemark()
    test_case_remark.load_remark_file()

    # get the test result history
    test_result_history = TestResultHistory()
    test_result_history.init(int(time.time()), head_commit_url, current_build_url)
    test_result_history.load_history_file()

    # Process test results and collect data to post to Slack
    # Slack max length of a text field is 3000 chars
    test_results_paginated = StringPaginator(3000)
    fail_cnt = 0
    total_tc_cnt = 0
    for ts in test_results["ts_list"]:
        test_results_paginated.add_str("*======  Test Suite: {}  ======*\n".format(ts["ts_name"]))
        total_tc_cnt += len(ts["tc_list"])
        for tc in ts["tc_list"]:
            # get remark and history with test suite name and test case name as key
            test_id = "{} {}".format(ts["ts_name"], tc["tc_name"])

            # add test case name with status and history
            if tc["result"] == "fail":
                fail_cnt += 1

                test_result_history.update_history(test_id, "f")

                result_str = ":red_circle: {}\n".format(tc["tc_name"])
                history_str = test_result_history.get_history_str(test_id)
                if history_str != None:
                    result_str += "      >>> {}\n".format(history_str)

                test_results_paginated.add_str(result_str)

            elif tc["result"] == "pass":
                test_result_history.update_history(test_id, "p")

                result_str = ":large_green_circle: {}\n".format(tc["tc_name"])
                history_str = test_result_history.get_history_str(test_id)
                if history_str != None:
                    result_str += "      >>> {}\n".format(history_str)

                test_results_paginated.add_str(result_str)

            elif tc["result"] == "skip":
                test_results_paginated.add_str(":black_circle: {}\n".format(tc["tc_name"]))

            # add remark if exists
            remark = test_case_remark.get_remark(test_id)
            if len(remark) > 0:
                test_results_paginated.add_str("      >>> {}\n".format(remark))
        test_results_paginated.add_str("\n")

    # set the emoji for fail count
    if fail_cnt == 0:
        fail_cnt_emoji = ":bananadance:"
    else:
        fail_cnt_emoji = ""

    #
    test_result_history.save_history_file()
    test_result_history.commit_history_file()

    # No test results; print error messages
    if len(test_results["ts_list"]) == 0:
        test_results_paginated.add_str("""\
:scream::scream::scream:  *Test results are not available.*  :scream::scream::scream:
Something might have gone wrong before tests were run.
Or tests were run but the result file wasn't generated.
""")

    # Construct header_text
    header_text = "{} - {}".format(test_results["tr_name"], test_results["completion_time"])

    # Construct test_trigger_info_text
    if event_name == "push":
        test_trigger_info_text = """\
Test Trigger: push
Code Committer: {}
Current Test Triggered By: {}
Head Commit: <{}|{}> | {}\
""".format(actor,
           triggering_actor,
           head_commit_url, head_commit_id[:7], head_commit_message[:70])

    if event_name == "pull_request":
        test_trigger_info_text = """\
Test Trigger: pull request
Code Committer: {}
Current Test Triggered By: {}
Title: <{}|{}>
Head Commit: <{}/commits/{}|{}>\
""".format(actor,
           triggering_actor,
           pr_html_url, pr_title,
           pr_html_url, pr_head_sha, pr_head_sha[:7])

    elif event_name == "schedule":
        test_trigger_info_text = """\
Test Trigger: schedule
Current Test Triggered By: {}\
""".format(triggering_actor)

    elif event_name == "workflow_dispatch":
        test_trigger_info_text = """\
Test Trigger: manual
Note: {}
Original Test Triggered By: {}
Current Test Triggered By: {}\
""".format(note,
           actor,
           triggering_actor)

    # Construct test_execution_info_text
    # extra space for API to line up API and MQTT URLs
    test_execution_info_text = """\
Social API Access Level: {}
Environment: {}
API Version: {}
API:    {}
MQTT: {}
Mandatory Tag: {}
Inclusion Tag: {}
Exclusion Tag: {}
Test Suite: {}
Test Case: {}
Loop Type: {}
Runs: {}/{}
*{} out of {} Tests Failed* {}
Exectution Time: {:.1f} secs
Full Log: <{}|{}>\
""".format(social_API_access_level,
           social_service_environment,
           social_curr_api_version,
           social_curr_api_url,
           social_curr_mqtt_url,
           mandatory_tag,
           inclusion_tag,
           exclusion_tag,
           test_suite,
           test_case,
           loop_type,
           run_cnt, run_max,
           fail_cnt, total_tc_cnt, fail_cnt_emoji,
           test_results["total_duration"],
           current_build_url, github_run_id)

    # Construct general_remark_text
    general_remark_text = """\
General Remark: {}
    """.format(test_case_remark.get_remark("General Remark"))

    # Compose test report message and post to Slack
    headers = {
        "Content-Type" : "application/json"
    }

    data = {
        "blocks" : [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": header_text,
                    "emoji": True
                }
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": test_trigger_info_text
                }
            },
            {
                "type": "divider"
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": test_execution_info_text
                }
            },
            {
                "type": "divider"
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": general_remark_text
                }
            },
            {
                "type": "divider"
            }
        ]
    }
    
    for s in test_results_paginated.get_str():
        data["blocks"].append(
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": s
                }
            }
        )
    
    response = requests.post(slack_webhook_url, headers=headers, json=data)
    if response.status_code != 200:
        print("Failed to post test results to a Slack channel. Reason: {}".format(response.text))