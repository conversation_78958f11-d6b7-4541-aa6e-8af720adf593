<script lang="ts">
  export let title = undefined;
  export let open = false;
  export let containerClassName = '';
  export let contentClassName = '';
  export let key = '';
  export let mode = '';
</script>

<div
  data-testid="accordion-section-mock"
  data-title="{title}"
  data-key="{key}"
  data-containerclassname="{containerClassName}"
  data-open="{open}"
  data-contentclassname="{contentClassName}"
  data-mode="{mode}"
>
  <slot />
</div>
