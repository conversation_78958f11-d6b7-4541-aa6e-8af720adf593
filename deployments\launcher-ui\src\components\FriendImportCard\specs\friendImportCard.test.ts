import { render } from '@testing-library/svelte';
import SVGSteamMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import AvatarMock from '../../Avatar/__mock__/Avatar.svelte';
import CustomInputMock from '../../CustomInput/__mock__/CustomInput.svelte';
import FriendImportCard from '../FriendImportCard.svelte';

jest.mock('../../Avatar', () => ({
  Avatar: AvatarMock,
}));

jest.mock('../../CustomInput', () => ({
  CustomInput: CustomInputMock,
}));

jest.mock('../../../assets/icons', () => ({
  SVGSteam: SVGSteamMock,
}));

describe('FriendImportCard', () => {
  it('should render UI', () => {
    expect(() => render(FriendImportCard)).not.toThrow();
  });
});
