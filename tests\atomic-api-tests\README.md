# Social Atomic API Tests

1. Files

   `run_test.py`  
   This is where the test functions, test suites, and the main entry point are.

   `testdb.py`  
   A library of common test patterns.

   `config.py`  
   Configuration data.

   `api.py`  
   Wrapper functions for the backend API.


2. Running the tests
   
   Before running the tests, prepare a 2K Account, and set the email and
   password in the environment variables `ATOMIC_API_TEST_2K_EMAIL` and `ATOMIC_API_TEST_2K_PASSWORD`.  
   Please **DO NOT** run the scripts with the default email/password.
   
   Also make sure you have AWS credentials set in the environment variables
   to access AWS services.  These can include `AWS_ACCESS_KEY_ID`, `AWS_SESSION_TOKEN`, and `AWS_SESSION_TOKEN`.

   The reason is that the test data in dynamodb are associated with each 2K
   account.  If multiple instances of the scripts are running with the same 2K
   account, results would be unexpected.

   Install python prerequisites with the following command.  
   ```
   python -m pip install -r requirements.txt
   ```

   Run the scripts by exceuting `run_test.py` in a Linux environment. Additional
   Python modules might need to be installed.

3. Test cases

   In general, the tests follow the following pattern:

   a) create test data  
   b) run test  
   c) verify results  
   d) destroy test data  

   The test functions in `run_test.py`, therefore, create test data and define
   verification function, and pass them into the test function to run. 
