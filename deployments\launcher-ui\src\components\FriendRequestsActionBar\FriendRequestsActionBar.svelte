<script lang="ts">
  import { SVGArrowLeft, SVGBell } from '../../assets/icons';
  import { ROUTE_FRIENDS_LIST } from '../../constant';
  import { useTranslator } from '../../hooks';
  import { NavLink } from '../NavLink';

  const t = useTranslator();
</script>

<style>
  .action-bar {
    box-sizing: border-box;
    height: 3.75rem;
    background-color: var(
      --social-bg-color-action-bar,
      var(--default-bg-color-action-bar)
    );
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 0.75rem 0.875rem;
  }

  .action-bar .nav-icon {
    display: flex;
    margin-inline-end: 1.25rem;
  }

  .action-bar .icon {
    display: flex;
    margin-inline-end: 0.625rem;
  }

  .action-bar .title {
    color: var(--default-color, var(--social-color));
    font-weight: 700;
    font-size: 1rem;
    line-height: 1.25rem;
  }
</style>

<div class="action-bar">
  <NavLink to="{ROUTE_FRIENDS_LIST}">
    <span class="nav-icon">
      <SVGArrowLeft />
    </span>
  </NavLink>

  <span class="icon">
    <SVGBell />
  </span>
  <span class="title"> {$t('Requests')} </span>
</div>
