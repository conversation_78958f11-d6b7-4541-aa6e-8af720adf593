#!/bin/bash

set -x

# This container does nothing if the pvc is already loaded
if [ ! -d "/mountpoint/t2gp-social" ]; then
  RESP=$(aws --region ${AWS_REGION} ssm get-parameter --name /social/mqtt/${STACK_ENV}/t2gp-plugin-version)
  if [ $? -ne 0 ]; then
    echo "Corresponding parameter not found on AWS. Creating one..."
    aws ssm --region ${AWS_REGION} put-parameter --type String  --name /social/mqtt/${STACK_ENV}/t2gp-plugin-version  --value ${DEFAULT_PLUGIN_VERSION}
    PLUGIN_VERSION=${DEFAULT_PLUGIN_VERSION}
  else
    PLUGIN_VERSION=$( echo $RESP | jq -r .Parameter.Value)
  fi
  aws --region ${VERNEMQ_PLUGIN_BUCKET_REGION} s3 cp s3://t2gp-social-vernemq-plugin/${PLUGIN_VERSION}.tar.gz ./
  mkdir t2gp-social
  tar xvf ./${PLUGIN_VERSION}.tar.gz --directory=t2gp-social
  rsync -avu --delete t2gp-social /mountpoint/
  chown -R 10000:10000 /mountpoint/
fi

exit 0