import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccounts, SteamAccounts, EpicAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
let usersSteam: SteamAccounts;
let usersEpic: EpicAccounts;
let groupId: string;

beforeAll(async () => {
  usersTwok = new TwokAccounts(2, ["leader", "member"]);

  usersSteam = new SteamAccounts(1, ["leader"]);
  await usersSteam.loginAll({});

  await usersSteam.acct["leader"].linkParent(usersTwok.acct["leader"]);

  usersEpic = new EpicAccounts(1, ["member"]);
  await usersEpic.loginAll({});

  await usersEpic.acct["member"].linkParent(usersTwok.acct["member"]);

  // steam account login again
  await usersSteam.acct["leader"].login({});

  // epic account login again
  await usersEpic.acct["member"].login({});
});

afterAll(async () => {
  await usersSteam.acct["leader"].unlinkParent();
  await usersEpic.acct["member"].unlinkParent();

  await usersSteam.logoutAll();
  await usersEpic.logoutAll();
});

describe(`cross play[public v1]${_ds}`, () => {
  afterEach(async () => {
    await socialApi.deleteGroup(usersSteam.acct["leader"], groupId);
  });

  /* test subject functions */
  async function joinReqApproved(groupCPSetting: boolean, userCPSetting: boolean) {
    let r = await socialApi.createGroupV1(
      usersSteam.acct["leader"],
      {
        joinRequestAction: 'manual',
        canCrossPlay: groupCPSetting
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    r = await socialApi.requestToJoinV1(usersEpic.acct["member"], groupId, { canCrossPlay: userCPSetting });
    if (groupCPSetting == false || userCPSetting == false) {
      socialApi.testStatus(StatusCodes.FORBIDDEN, r);
    } else { 
      socialApi.testStatus(StatusCodes.CREATED, r);
    }

    r = await socialApi.approveRequestV1(usersSteam.acct["leader"], groupId, usersTwok.acct["member"].publicId, {});
    if (groupCPSetting == false || userCPSetting == false) {
      socialApi.testStatus(StatusCodes.BAD_REQUEST, r);
    } else {
      socialApi.testStatus(StatusCodes.OK, r);
    }
  }

  async function inviteAndAccept(groupCPSetting: boolean, userCPSetting: boolean) {
    let r = await socialApi.createGroupV1(
      usersSteam.acct["leader"],
      {
        joinRequestAction: 'manual',
        canCrossPlay: groupCPSetting
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);
    groupId = socialApi.getGroupId(r);

    r = await socialApi.inviteV1(usersSteam.acct["leader"], groupId, { memberid: usersTwok.acct["member"].publicId });
    socialApi.testStatus(StatusCodes.CREATED, r);

    r = await socialApi.acceptInviteV1(
      usersEpic.acct["member"],
      groupId,
      {
        approverid: usersTwok.acct["leader"].publicId,
        canCrossPlay: userCPSetting
      }
    );
    if (groupCPSetting == false || userCPSetting == false) {
      socialApi.testStatus(StatusCodes.FORBIDDEN, r);
    } else {
      socialApi.testStatus(StatusCodes.OK, r);
    }
  }

  it.each`
    testSubjectFunc    | scenario                                                                                           | groupCPSetting | userCPSetting
    ${joinReqApproved} | ${`happy cases${_ds}can join the group with crossPlay on when group has crossPlay enabled`}        | ${true}        | ${true}
    ${inviteAndAccept} | ${`happy cases${_ds}can accept an invite with crossPlay on when group has crossPlay enabled`}      | ${true}        | ${true}
    ${joinReqApproved} | ${`corner cases${_ds}can't join the group with crossPlay on when group has crossPlay disabled`}    | ${false}       | ${true}
    ${inviteAndAccept} | ${`corner cases${_ds}can't accept an invite with crossPlay on when group has crossPlay disabled`}  | ${false}       | ${true}
    ${joinReqApproved} | ${`corner cases${_ds}can't join the group with crossPlay off when group has crossPlay enabled`}    | ${true}        | ${false}
    ${inviteAndAccept} | ${`corner cases${_ds}can't accept the group with crossPlay off when group has crossPlay enabled`}  | ${true}        | ${false}
    ${joinReqApproved} | ${`corner cases${_ds}can't join the group with crossPlay off when group has crossPlay disabled`}   | ${false}       | ${false}
    ${inviteAndAccept} | ${`corner cases${_ds}can't accept an invite with crossPlay off when group has crossPlay disabled`} | ${false}       | ${false}
  `(`$scenario`, async ({testSubjectFunc, groupCPSetting, userCPSetting}) => {
    let testCase = {
      description: `leader creates a group with canCrossPlay = ${groupCPSetting}; 
user tries to${testSubjectFunc.name == 'joinReqApproved' ? 'join the group' : 'accept an invite'} with canCrossPlay = ${userCPSetting};
get group info`,
      expected: `user ${groupCPSetting == true && userCPSetting == true ? 'is' : 'is not'} in the group`,
    };

    // main test subject
    await testSubjectFunc(groupCPSetting, userCPSetting);

    // create an expected array of group member
    let memberArray = [
      expect.objectContaining({
        groupid: groupId,
        role: "member",
        userid: usersTwok.acct["member"].publicId
      })
    ];

    let groupMembers;
    if (groupCPSetting == false || userCPSetting == false) {
      groupMembers = { members: expect.not.arrayContaining(memberArray) };
    } else {
      groupMembers = { members: expect.arrayContaining(memberArray) };
    }

    const actualGroupInfo: request.Response = await socialApi.getGroupInfo(
      usersSteam.acct["leader"],
      groupId
    );

    /**
     * crossplay-enabled group: user joins the group with "canCrossPlay = true";
     * crossplay-enabled group: user doesn't join the group with "canCrossPlay = false";
     * crossplay-disabled group: user doesn't join the group with "canCrossPlay = true/false".
     */
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: groupMembers
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected group member info"
        }
      }
    );
  });
})