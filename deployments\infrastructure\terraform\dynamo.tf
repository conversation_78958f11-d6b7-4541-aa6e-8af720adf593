# TODO:
# Revisit the appropriate of billing mode after collecting and reviewing real traffic data

locals {
  tbl_rw_units               = local.envvars[terraform.workspace]["dynamo_billing_mode"] == "PAY_PER_REQUEST" ? 0 : 20
  index_rw_units             = local.envvars[terraform.workspace]["dynamo_billing_mode"] == "PAY_PER_REQUEST" ? 0 : 1
  env_ver_mapping_rsc_prefix = "t2gp-social-env-ver-mapping"
}

resource "aws_dynamodb_table" "social" {
  name           = "t2gp-${local.resource_prefix}-social"
  billing_mode   = local.envvars[terraform.workspace]["dynamo_billing_mode"]
  read_capacity  = local.tbl_rw_units
  write_capacity = local.tbl_rw_units
  hash_key       = "pk"
  range_key      = "sk"
  stream_enabled = false
  point_in_time_recovery {
    enabled = "true"
  }

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  attribute {
    name = "gii"
    type = "S"
  }

  attribute {
    name = "memberid"
    type = "S"
  }

  attribute {
    name = "groupid"
    type = "S"
  }
  attribute {
    name = "clientid"
    type = "S"
  }
  attribute {
    name = "userid"
    type = "S"
  }

  global_secondary_index {
    name            = "gii-groupid-index"
    hash_key        = "gii"
    range_key       = "groupid"
    write_capacity  = local.index_rw_units
    read_capacity   = local.index_rw_units
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "memberships_by_member"
    hash_key        = "memberid"
    range_key       = "sk"
    write_capacity  = local.index_rw_units
    read_capacity   = local.index_rw_units
    projection_type = "ALL"
  }

  global_secondary_index {
    name            = "clientid-userid-index"
    hash_key        = "clientid"
    range_key       = "userid"
    write_capacity  = local.index_rw_units
    read_capacity   = local.index_rw_units
    projection_type = "ALL"
  }

  ttl {
    enabled        = true
    attribute_name = "ttl"
  }

  tags = merge({
    Name = "t2gp-${local.resource_prefix}-social",

  }, local.tags)
}

resource "aws_dynamodb_table" "chat_messages" {
  name           = "t2gp-${local.resource_prefix}-chat-messages"
  billing_mode   = local.envvars[terraform.workspace]["dynamo_billing_mode"]
  read_capacity  = local.tbl_rw_units
  write_capacity = local.tbl_rw_units
  hash_key       = "pk"
  range_key      = "sk"
  point_in_time_recovery {
    enabled = "true"
  }

  attribute {
    name = "pk"
    type = "S"
  }

  attribute {
    name = "sk"
    type = "S"
  }

  ttl {
    enabled        = true
    attribute_name = "ttl"
  }

  tags = merge({
    Name = "t2gp-${local.resource_prefix}-chat-messages"
  }, local.tags)
}

resource "aws_dynamodb_table" "env_ver_mapping" {
  count        = terraform.workspace == "develop" ? 1 : 0
  name         = local.env_ver_mapping_rsc_prefix
  billing_mode = "PAY_PER_REQUEST"
  hash_key     = "env_label"
  attribute {
    name = "env_label"
    type = "S"
  }
  tags = merge(local.tags, {
    Name  = "t2gp-social-env-ver-mapping",
    "env" = "global"
  })
}
