%% @doc set the jwt key to validate the JWT

%% @doc allow alg=none to be a valid JWT
{mapping, "t2gp_social.jwt_allow_alg_none", "t2gp_social.jwt_allow_alg_none", [
                                                            {datatype, flag},
                                                            {default, on}
                                                        ]}.

%% @doc set aws access key
{mapping, "t2gp_social.aws_access_key", "t2gp_social.aws_access_key", [
                                                            {datatype, string}
                                                        ]}.

%% @doc set aws secret
{mapping, "t2gp_social.aws_secret", "t2gp_social.aws_secret", [
                                                            {datatype, string}
                                                        ]}.

%% @doc set dynamodb endpoint
{mapping, "t2gp_social.dynamodb_endpoint", "t2gp_social.dynamodb_endpoint", [
                                                            {datatype, string}
                                                        ]}.

%% @doc set social table
{mapping, "t2gp_social.profile_table", "t2gp_social.profile_table", [
                                                            {datatype, string}
                                                        ]}.

%% @doc set api key
{mapping, "t2gp_social.api_key", "t2gp_social.api_key", [
                                                            {datatype, string}
                                                        ]}.

%% @doc DNA basic auth
{mapping, "t2gp_social.discovery_url", "t2gp_social.discovery_url", [
                                                            {datatype, string}
                                                        ]}.

%% @doc DNA basic auth
{mapping, "t2gp_social.app_basic_auth", "t2gp_social.app_basic_auth", [
                                                            {datatype, string}
                                                        ]}.

%% @doc DNA app id
{mapping, "t2gp_social.app_id", "t2gp_social.app_id", [
                                                            {datatype, string}
                                                        ]}.

%% @doc DNA app secret
{mapping, "t2gp_social.app_secret", "t2gp_social.app_secret", [
                                                            {datatype, string}
                                                        ]}.

%% @doc PD private url
{mapping, "t2gp_social.pd_private_url", "t2gp_social.pd_private_url", [
                                                            {datatype, string}
                                                        ]}.

%% @doc elasticache url
{mapping, "t2gp_social.elasticache_url", "t2gp_social.elasticache_url", [
                                                            {datatype, string}
                                                        ]}.

%% @doc redis primary node url
{mapping, "t2gp_social.redis_primary_node_url", "t2gp_social.redis_primary_node_url", [
                                                            {datatype, string}
                                                        ]}.

%% @doc redis replica node url
{mapping, "t2gp_social.redis_replica_node_url", "t2gp_social.redis_replica_node_url", [
                                                            {datatype, string}
                                                        ]}.

{mapping, "t2gp_social.clique_lead_line", "t2gp_social.clique_lead_line",
 [{datatype, string},
  hidden,
  {default, "    t2gp         Manage T2GP Social Services\n"}]}.
