package api

//
//func TestSendMembershipRequestForGroupServer(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateRandomDNAID()
//	userID := utils.GenerateRandomDNAID()
//	userID2 := utils.GenerateRandomDNAID()
//	userID3 := utils.GenerateRandomDNAID()
//	leaderUserID := utils.GenerateRandomDNAID()
//
//	joinRequest := apipub.MembershipRequest{
//		Groupid:      groupID,
//		Productid:    &productID,
//		Status:       apipub.Requested,
//		Approverid:   &userID2,
//		CanCrossPlay: aws.Bool(true),
//	}
//
//	invitation := apipub.MembershipRequest{
//		Groupid:      groupID,
//		Productid:    &productID,
//		Status:       apipub.Invited,
//		CanCrossPlay: aws.Bool(true),
//		Memberid:     &userID3,
//	}
//
//	gmiReq := apitrusted.GroupMembershipRequest{
//		Memberid:     &userID3,
//		Status:       apitrusted.Invited,
//		Approverid:   &userID2,
//		CanCrossPlay: aws.Bool(true),
//	}
//
//	invitationRequestServer := apitrusted.ServerGroupMembershipRequest{
//		GroupMembershipRequest: gmiReq,
//		UserId:                 userID,
//		OnlineServiceType:      24,
//	}
//
//	var requests []apipub.MembershipRequest
//	requests = append(requests, invitation)
//	requests = append(requests, joinRequest)
//
//	member := apipub.GroupMember{
//		Userid: userID,
//		Role:   apipub.Member,
//	}
//
//	leader := apipub.GroupMember{
//		Userid: leaderUserID,
//		Role:   apipub.Leader,
//	}
//
//	var members []apipub.GroupMember
//	members = append(members, member, leader)
//
//	//Member is in group
//	group := apipub.Group{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("ServerSendMembershipRequestForGroup", func() {
//		g.It("should return bad request with db error", func() {
//
//			//get group returns error.  Server returns 500 server error..
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
//			w, r := LoginBasic(productID, "pass")
//			jsonbytes, _ := json.Marshal(invitationRequestServer)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should return not found", func() {
//
//			//get group returns nil, nil.  Server returns 404 not found.
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			w, r := LoginBasic(productID, "pass")
//			jsonbytes, _ := json.Marshal(invitationRequestServer)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusNotFound)
//		})
//
//		g.It("empty memberid on invitation error", func() {
//
//			//For Invitation, same userID was added to both the group member and the invitation.
//			//This will return err 400.
//			invitationRequestServer2 := apitrusted.ServerGroupMembershipRequest{
//				GroupMembershipRequest: apitrusted.GroupMembershipRequest{
//					Status:       apitrusted.Invited,
//					Approverid:   &userID2,
//					CanCrossPlay: aws.Bool(true),
//				},
//				UserId:            userID,
//				OnlineServiceType: 24,
//			}
//			member3 := apipub.GroupMember{
//				Userid: "b287e655461f4b3085c8f244e394ff7e",
//				Role:   apipub.Member,
//			}
//			var newMembers []apipub.GroupMember
//			newMembers = append(newMembers, leader, member3)
//			group.Members = &newMembers
//			w, r := LoginBasic(productID, "pass")
//			jsonbytes, _ := json.Marshal(invitationRequestServer2)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("invite exists", func() {
//
//			w, r := LoginBasic(productID, "pass")
//
//			var members2 []apipub.GroupMember
//			members2 = append(members2, leader)
//			requests = []apipub.MembershipRequest{}
//
//			//Member is in group
//			group = apipub.Group{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members2,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invitation2 := apitrusted.GroupMembershipRequest{
//				Approverid:   &leader.Userid,
//				Status:       apitrusted.Invited,
//				CanCrossPlay: aws.Bool(true),
//				Memberid:     &userID3,
//			}
//			invitationRequestServer2 := apitrusted.ServerGroupMembershipRequest{
//				UserId:                 leader.Userid,
//				OnlineServiceType:      24,
//				GroupMembershipRequest: invitation2,
//			}
//
//			jsonbytes, _ := json.Marshal(invitationRequestServer2)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(true)
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//
//		g.It("should get bad request for final send request call", func() {
//			w, r := LoginBasic(productID, "pass")
//
//			var members2 []apipub.GroupMember
//			members2 = append(members2, leader)
//			requests = []apipub.MembershipRequest{}
//
//			//Member is in group
//			group = apipub.Group{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members2,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invitation2 := apitrusted.GroupMembershipRequest{
//				Approverid:   &leader.Userid,
//				Status:       apitrusted.Invited,
//				CanCrossPlay: aws.Bool(true),
//				Memberid:     &userID3,
//			}
//			invitationRequestServer2 := apitrusted.ServerGroupMembershipRequest{
//				UserId:                 leader.Userid,
//				OnlineServiceType:      24,
//				GroupMembershipRequest: invitation2,
//			}
//
//			jsonbytes, _ := json.Marshal(invitationRequestServer2)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfile{Userid: userID}, nil)
//			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
//			//mock.rc.EXPECT().SetMembership(gomock.Any(), gomock.Any(), gomock.Any()).Return(errs.New(http.StatusInternalServerError, errs.EGroupsGroupMemberModifyFailed))
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusInternalServerError)
//		})
//
//		g.It("should successfully create invitation", func() {
//			w, r := LoginBasic(productID, "pass")
//
//			var members2 []apipub.GroupMember
//			members2 = append(members2, leader)
//			requests = []apipub.MembershipRequest{}
//
//			//Member is in group
//			group = apipub.Group{
//				Groupid:            groupID,
//				Productid:          productID,
//				Members:            &members2,
//				MaxMembers:         100,
//				MembershipRequests: &requests,
//				CanCrossPlay:       aws.Bool(true),
//			}
//
//			invitation3 := apitrusted.GroupMembershipRequest{
//				Approverid:   &leader.Userid,
//				Memberid:     &userID3,
//				Status:       apitrusted.Invited,
//				CanCrossPlay: aws.Bool(true),
//			}
//
//			invitationRequestServer3 := apitrusted.ServerGroupMembershipRequest{
//				UserId:                 leader.Userid,
//				OnlineServiceType:      24,
//				GroupMembershipRequest: invitation3,
//			}
//
//			//Send an invitation - happy path
//			jsonbytes, _ := json.Marshal(invitationRequestServer3)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().InviteExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), nil).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.ds.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.id.EXPECT().SyncUserProfile(gomock.Any(), gomock.Any()).Return(&apipub.UserProfile{Userid: userID}, nil)
//			mock.ds.EXPECT().PutUserProfile(gomock.Any(), gomock.Any())
//			mock.rc.EXPECT().SetUserProfile(gomock.Any(), gomock.Any(), gomock.Any())
//			//mock.rc.EXPECT().SetMembership(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().SetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusCreated)
//		})
//
//		g.It("empty userid error", func() {
//			requestEmptyUser := apitrusted.ServerGroupMembershipRequest{
//				GroupMembershipRequest: apitrusted.GroupMembershipRequest{
//					Status: apitrusted.Requested,
//				},
//				OnlineServiceType: 24,
//			}
//
//			w, r := LoginBasic(productID, "pass")
//			jsonbytes, _ := json.Marshal(requestEmptyUser)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusBadRequest)
//		})
//
//		g.It("should successfully create joinrequest and autojoin", func() {
//			g.Timeout(10 * time.Second)
//			//Join requet uses a different user ID so it should pass that point.
//			//Send a join request - happy path
//			joinRequest3 := apitrusted.GroupMembershipRequest{
//				Approverid:   &leaderUserID,
//				Status:       apitrusted.Requested,
//				CanCrossPlay: aws.Bool(true),
//			}
//
//			joinRequestServer3 := apitrusted.ServerGroupMembershipRequest{
//				UserId:                 userID3,
//				OnlineServiceType:      24,
//				GroupMembershipRequest: joinRequest3,
//			}
//
//			actType := identity.DnaAccountTypeFull
//			var links []apipub.AccountLinkDNA
//			links = append(links, apipub.AccountLinkDNA{
//				AccountType: &actType,
//			})
//
//			user3Name := "user3"
//			userProfile := apipub.UserProfile{
//				Userid:      userID3,
//				Links:       &links,
//				DisplayName: &user3Name,
//			}
//			leaderName := "leader"
//			leaderProfile := apipub.UserProfile{
//				Userid:      leaderUserID,
//				DisplayName: &leaderName,
//			}
//
//			w, r := LoginBasic(productID, "pass")
//			jsonbytes, _ := json.Marshal(joinRequestServer3)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&userProfile, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&leaderProfile, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&userProfile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), userID3, productID, groupID)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//func TestFirstPartyInviteJoinRequestServer(t *testing.T) {
//	g := goblin.Goblin(t)
//	mock := NewMockSocialTrustedAPI(t)
//	defer mock.ctrl.Finish()
//
//	groupID := utils.GenerateNewULID()
//	productID := utils.GenerateNewULID()
//	userID := utils.GenerateRandomDNAID()
//	approverID := utils.GenerateRandomDNAID()
//
//	_, r := LoginBasic(productID, "pass")
//
//	invitation := apipub.MembershipRequest{
//		Groupid:            groupID,
//		Productid:          &productID,
//		Approverid:         &approverID,
//		Memberid:           &userID,
//		Status:             apipub.Invited,
//		CanCrossPlay:       aws.Bool(true),
//		IsFirstPartyInvite: aws.Bool(true),
//	}
//
//	var requests []apipub.MembershipRequest
//	requests = append(requests, invitation)
//
//	leader := apipub.GroupMember{
//		Userid: approverID,
//		Role:   apipub.Leader,
//	}
//
//	var members []apipub.GroupMember
//	members = append(members, leader)
//
//	group := apipub.Group{
//		Groupid:            groupID,
//		Productid:          productID,
//		Members:            &members,
//		MaxMembers:         100,
//		MembershipRequests: &requests,
//		CanCrossPlay:       aws.Bool(true),
//	}
//
//	actType := identity.DnaAccountTypeFull
//	var links []apipub.AccountLinkDNA
//	links = append(links, apipub.AccountLinkDNA{
//		AccountType: &actType,
//	})
//
//	userProfile := apipub.UserProfile{
//		Userid: userID,
//		Links:  &links,
//	}
//
//	gmrReq := apitrusted.GroupMembershipRequest{
//		Approverid:         &approverID,
//		Memberid:           &userID,
//		Status:             apitrusted.Requested,
//		CanCrossPlay:       aws.Bool(true),
//		IsFirstPartyInvite: aws.Bool(true),
//	}
//
//	joinRequest := apitrusted.ServerGroupMembershipRequest{
//		GroupMembershipRequest: gmrReq,
//		UserId:                 userID,
//		OnlineServiceType:      3,
//	}
//
//	tsInfo := apitrusted.TsClientIdInfo{
//		ClientId:  productID,
//		ProductId: productID,
//		TenantId:  "dna",
//		Hash:      TrustedPassHash,
//	}
//
//	g.Describe("FirstPartyInviteJoinRequest", func() {
//		RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })
//		g.It("should successfully join group", func() {
//			g.Timeout(10 * time.Second)
//			jsonbytes, _ := json.Marshal(joinRequest)
//			r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))
//			mock.rc.EXPECT().GetTsClientId(gomock.Any(), gomock.Any()).Return(&tsInfo, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&userProfile, nil)
//			mock.rc.EXPECT().UserBlocklistExistsInCache(gomock.Any(), gomock.Any()).Return(false)
//			mock.ds.EXPECT().DoesBlockerBlockBlockee(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
//			mock.rc.EXPECT().JoinRequestExistsInCache(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&userProfile, nil)
//			mock.rc.EXPECT().GetInvite(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), aws.Bool(true)).Return(&invitation, nil)
//			mock.rc.EXPECT().GetGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&group, nil)
//			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(&userProfile, nil)
//			mock.rc.EXPECT().GetPresence(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
//			mock.rc.EXPECT().AddGroupMember(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			//mock.rc.EXPECT().SetUserGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.ds.EXPECT().AddChatMessageGroup(gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().RemoveMembershipRequestFromGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
//			mock.rc.EXPECT().ClearAllMemberships(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
//			mock.tele.EXPECT().NewEvent(telemetry.KGroupJoin, gomock.Any()).Return(&statsd.Event{})
//			mock.tele.EXPECT().Event(gomock.Any())
//			w := httptest.NewRecorder()
//
//			mock.apiTrusted.ServerSendMembershipRequestForGroup(w, r, groupID)
//			g.Assert(w.Code).Equal(http.StatusOK)
//		})
//	})
//}
//
//// func TestCheckOwnership(t *testing.T) {
//
//// 	testCases := []struct {
//// 		name                 string
//// 		userid               string
//// 		prepareExpections    func(*store.MockDataStoreInterface)
//// 		expectResponseResult bool
//// 	}{
//// 		{
//// 			name:   "1. OK - successful check ownership of platform account",
//// 			userid: "3e35d862d7564edbba3b91b41f3c206c",
//// 			prepareExpections: func(Ds *store.MockDataStoreInterface) {
//// 				accounts := apipub.SearchAccountResponse{
//// 					{
//// 						FirstPartyId:      aws.String("12345"),
//// 						Id:                aws.String("3e35d862d7564edbba3b91b41f3c206c"),
//// 						OnlineServiceType: aws.Int(3),
//// 					},
//// 				}
//
//// 				Ds.EXPECT().SearchAccountsDNA(gomock.Any(), gomock.Any()).Return(&accounts, nil)
//// 			},
//// 			expectResponseResult: true,
//// 		},
//// 		{
//// 			name:   "2. OK - successful check ownership of parent account",
//// 			userid: "b4efe7bab4ef4a9099fe666356ef34fb",
//// 			prepareExpections: func(Ds *store.MockDataStoreInterface) {
//// 				accounts := apipub.SearchAccountResponse{
//// 					{
//// 						FirstPartyId:      aws.String("12345"),
//// 						Id:                aws.String("baduserid"),
//// 						ParentAccountId:   aws.String("b4efe7bab4ef4a9099fe666356ef34fb"),
//// 						OnlineServiceType: aws.Int(3),
//// 					},
//// 				}
//
//// 				Ds.EXPECT().SearchAccountsDNA(gomock.Any(), gomock.Any()).Return(&accounts, nil)
//// 			},
//// 			expectResponseResult: true,
//// 		},
//// 		{
//// 			name:   "3. FAIL - unsuccessful",
//// 			userid: "baduserid",
//// 			prepareExpections: func(Ds *store.MockDataStoreInterface) {
//// 				accounts := apipub.SearchAccountResponse{
//// 					{
//// 						FirstPartyId:      aws.String("12345"),
//// 						Id:                aws.String("3e35d862d7564edbba3b91b41f3c206c"),
//// 						ParentAccountId:   aws.String("b4efe7bab4ef4a9099fe666356ef34fb"),
//// 						OnlineServiceType: aws.Int(3),
//// 					},
//// 				}
//
//// 				Ds.EXPECT().SearchAccountsDNA(gomock.Any(), gomock.Any()).Return(&accounts, nil)
//// 			},
//// 			expectResponseResult: false,
//// 		},
//// 	}
//
//// 	g := goblin.Goblin(t)
//// 	g.Describe("CheckOwnership", func() {
//// 		for i := range testCases {
//// 			testCase := testCases[i]
//// 			g.It(testCase.name, func() {
//// 				mockCtrl := gomock.NewController(t)
//// 				defer mockCtrl.Finish()
//
//// 				Ds := store.NewMockDataStoreInterface(mockCtrl)
//// 				gAPI := NewMockSocialPublicAPI(cfg, Ds)
//
//// 				testCase.prepareExpections(Ds)
//// 				result := checkOwnership(context.Background(), gAPI, testCase.userid, apipub.MembershipRequest{
//// 					Memberid:           "12345",
//// 					OnlineServiceType:  (*apipub.OnlineServiceType)(aws.Int(3)),
//// 					IsFirstPartyInvite: aws.Bool(true),
//// 				})
//
//// 				g.Assert(result).Equal(testCase.expectResponseResult)
//// 			})
//// 		}
//// 	})
//// }
