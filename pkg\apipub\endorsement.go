package apipub

import (
	"fmt"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// RedisKey redis key
//func (endorsement *EndorsementResponse) RedisKey(tenant, productid string) string {
//	return fmt.Sprintf("%s:prod:%s:%s:endorsement:%s", tenant, productid, utils.GetEnvironment(), endorsement.EndorsementName)
//}

func BuildEndorsementRedisKey(tenant, userid, productid, endorsementName string) string {
	return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:endorsement:%s", tenant, productid, utils.GetEnvironment(), userid, endorsementName)
}

// PK partition key
func (endorsement *EndorsementResponse) PK(tenant, userid string) string {
	return tenant + "#user#" + userid
}

// SK sort key
func (endorsement *EndorsementResponse) SK(productid string) string {
	return productid + "#endorsement#" + endorsement.EndorsementName
}
