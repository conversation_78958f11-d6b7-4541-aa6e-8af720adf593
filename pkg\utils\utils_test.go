package utils

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/oklog/ulid/v2"
	"github.com/segmentio/encoding/iso8601"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

const BearerAuthScopes = "bearerAuth.Scopes"

func TestGenerateNewULID(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GenerateNewULID", func() {
		g.It("should create a valid ULID", func() {
			value := GenerateNewULID()
			verr := validation.Validate.Var(value, "uppercase,alphanum")
			g.Assert(verr).IsNil()

			ulidValue, err := ulid.Parse(value)
			g.<PERSON>ser<PERSON>(err).IsNil()

			ts := ulidValue.Time()
			now := uint64(time.Now().UnixMilli())
			g.<PERSON>(t).IsNotZero()
			g.Assert(ts <= now).IsTrue()

			// should fail w/ max timestamp value
			max := time.Unix(1<<63-1, 999999999)
			value = GenerateULID(max)
			g.Assert(value).Equal("")
		})
	})
}

func TestGenerateRandomDNAID(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GenerateRandomDNAID", func() {
		g.It("should return valid DNA ID", func() {
			id := GenerateRandomDNAID()
			err := validation.Validate.Var(id, validation.KValidateUserID)
			g.Assert(err).IsNil()
		})
	})
}

func TestContains(t *testing.T) {
	arr := []string{"one", "two"}
	b1 := ArrayContainsString(arr, "two")
	if b1 != true {
		t.Fatalf("should contain two")
	}

	b2 := ArrayContainsString(arr, "three")
	if b2 != false {
		t.Fatalf("should not contain three")
	}
}

func TestContainSubstr(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("ContainsSubstr", func() {
		str := "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
		g.It("with single string should return true", func() {
			result := StringContainsSubstr(str, "reprehenderit")
			g.Assert(result).Equal(true)
		})

		g.It("with multiple strings should return true", func() {
			result := StringContainsSubstr(str, "foo", "bar", "reprehenderit")
			g.Assert(result).Equal(true)
		})

		g.It("should return false", func() {
			result := StringContainsSubstr(str, "pellentesque")
			g.Assert(result).Equal(false)
		})
	})
}

func TestUniqueStringArray(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("UniqueStringArray", func() {
		g.It("should return unique array", func() {
			in := []string{
				"foo", "bar", "fizz", "buzz", "dead", "beef", "foo", "beer", "beef",
			}
			out := []string{
				"foo", "bar", "fizz", "buzz", "dead", "beef", "beer",
			}
			result := UniqueStringArray(in)
			g.Assert(result).Equal(out)
		})
	})
}

func TestGetEnvironment(t *testing.T) {
	g := goblin.Goblin(t)

	currentEnv, found := os.LookupEnv("DD_ENV")
	if found {
		defer func() {
			os.Setenv("DD_ENV", currentEnv)
		}()
	}

	g.Describe("GetEnvironment", func() {
		g.It("should get current env", func() {
			os.Setenv("DD_ENV", "test")
			g.Assert(GetEnvironment()).Equal("test")
		})
		g.It("should get current env", func() {
			os.Unsetenv("DD_ENV")
			g.Assert(GetEnvironment()).Equal("develop")
		})
	})

	g.Describe("Environment Check Functions", func() {
		g.It("IsProduction() should get value correctly", func() {
			os.Setenv("DD_ENV", "production")
			g.Assert(IsProduction()).Equal(true)
			os.Setenv("DD_ENV", "not-production")
			g.Assert(IsProduction()).Equal(false)
		})

		g.It("IsStaging() should get value correctly", func() {
			os.Setenv("DD_ENV", "staging")
			g.Assert(IsStaging()).Equal(true)
			os.Setenv("DD_ENV", "not-staging")
			g.Assert(IsStaging()).Equal(false)
		})

		g.It("IsDevelop() should get value correctly", func() {
			os.Setenv("DD_ENV", "develop")
			g.Assert(IsDevelop()).Equal(true)
			os.Setenv("DD_ENV", "not-develop")
			g.Assert(IsDevelop()).Equal(false)
		})

		g.It("IsLocal() should get value correctly", func() {
			os.Setenv("DD_ENV", "local")
			g.Assert(IsLocal()).Equal(true)
			os.Setenv("DD_ENV", "not-local")
			g.Assert(IsLocal()).Equal(false)
		})

		g.It("IsDevOrLocal() should get value correctly", func() {
			os.Setenv("DD_ENV", "local")
			g.Assert(IsDevOrLocal()).Equal(true)
			os.Setenv("DD_ENV", "develop")
			g.Assert(IsDevOrLocal()).Equal(true)
			os.Setenv("DD_ENV", "not-local")
			g.Assert(IsDevOrLocal()).Equal(false)
		})
	})
}

func TestGetStack(t *testing.T) {
	g := goblin.Goblin(t)

	currentEnv, found := os.LookupEnv("DD_ENV")
	if found {
		defer func() {
			os.Setenv("DD_ENV", currentEnv)
		}()
	}
	g.Describe("GetStack", func() {
		g.It("should return stack in develop", func() {
			os.Setenv("DD_ENV", "develop")
			s := GetStack()
			g.Assert(s).IsNotNil()
			g.Assert(len(*s)).IsNotNil()
		})

		g.It("should not return stack in other environments", func() {
			os.Setenv("DD_ENV", "production")
			s := GetStack()
			g.Assert(s).IsNil()
		})
	})
}

type TestStruct struct {
	Foo string `json:"foo"`
}

func TestJsonResponse(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("JsonResponse", func() {
		g.It("should return json response correctly", func() {
			reqID := "hostname/FluEX8lEVc-000013"
			ctx := context.Background()
			ctx = context.WithValue(ctx, middleware.RequestIDKey, reqID)
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/foo", nil)

			w := httptest.NewRecorder()
			body := TestStruct{Foo: "bar"}
			WriteJsonResponse(w, r, http.StatusOK, &body)

			response := w.Body.String()
			g.Assert(response).Equal("{\"foo\":\"bar\"}\n")
			g.Assert(w.Header().Get("x-request-id")).Equal(reqID)
		})
	})
}

func TestEncodeJson(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("EncodeJson", func() {
		g.It("should encode object correctly", func() {
			obj := TestStruct{Foo: "baz"}
			str := EncodeJson(obj)
			g.Assert(str).Equal("{\"foo\":\"baz\"}")
		})

		g.It("should return empty string on marshal failure", func() {
			obj := map[string]interface{}{
				"foo": make(chan int),
			}
			j := EncodeJson(&obj)
			g.Assert(j).Equal("")
		})
	})
}

func TestMiscUtils(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("EscapeString", func() {
		g.It("should escape correctly", func() {
			escapedStr := EscapeString("\"foobar\"")
			g.Assert(escapedStr).Equal("\\\"foobar\\\"")
			escapedStr = EscapeString("\"\r\nfoobar\r\n\"")
			g.Assert(escapedStr).Equal("\\\"\\r\\nfoobar\\r\\n\\\"")
		})
	})

	g.Describe("NowISOString", func() {
		g.It("should return valid time", func() {
			now := NowISOString()
			g.Assert(iso8601.Valid(now, iso8601.Strict)).IsTrue()
		})
	})

	g.Describe("SortKeyToGroupID", func() {
		g.It("should parse group key properly", func() {
			gid := SortKeyToGroupID("group#productid#groupid")
			g.Assert(gid).Equal("groupid")

			gid = SortKeyToGroupID("group#groupid")
			g.Assert(gid).Equal("groupid")

			gid = SortKeyToGroupID("foo#groupid")
			g.Assert(gid).Equal("")
		})
	})

	g.Describe("HashPassword", func() {
		g.It("should hash password", func() {
			hashed := HashPassword(nil)
			g.Assert(hashed).IsNil()

			pw := "foobar"
			hashed = HashPassword(&pw)
			g.Assert(hashed).IsNotNil()
			g.Assert(*hashed != pw).IsTrue()
		})

		g.It("should validate hashed password", func() {
			pw := "foobar"
			hashed := HashPassword(&pw)
			g.Assert(hashed).IsNotNil()
			g.Assert(*hashed != pw).IsTrue()

			ok := CheckPassword(*hashed, pw)
			g.Assert(ok).IsTrue()

			ok = CheckPassword(*hashed, "wrong")
			g.Assert(ok).IsFalse()
		})
	})

	g.Describe("IgnoreRequest", func() {
		ctx := context.Background()
		g.It("should ignore urls properly", func() {
			urls := []string{
				"http://example.com/health",
				"http://example.com/favicon.ico",
			}
			for _, url := range urls {
				r, _ := http.NewRequestWithContext(ctx, "GET", url, nil)
				ok := IgnoreRequest(r)
				g.Assert(ok).IsTrue()
			}
		})

		g.It("should ignore user agents properly", func() {

			uas := []string{
				"ELB-HealthChecker",
				"kube-probe",
			}

			for _, ua := range uas {
				r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/", nil)
				r.Header.Set("User-Agent", ua)
				ok := IgnoreRequest(r)
				g.Assert(ok).IsTrue()
			}
		})

		g.It("should not ignore normal url", func() {
			r, _ := http.NewRequestWithContext(ctx, "GET", "http://example.com/", nil)
			ok := IgnoreRequest(r)
			g.Assert(ok).IsFalse()
		})
	})
}

// type XSSTestStruct struct {
// 	IntField    int
// 	StringField string
// 	StringPtr   *string
// }

// func TestEscapeXSS(t *testing.T) {
// 	g := goblin.Goblin(t)
// 	g.Describe("TestEscapeXSS", func() {
// 		g.It("should return expected values", func() {
// 			s := XSSTestStruct{
// 				IntField:    42,
// 				StringField: "<script>alert('foo');</script>",
// 				StringPtr:   aws.String("<script>alert('foo');</script>"),
// 			}

// 			EscapeXSS(&s)
// 			expected := "&lt;script&gt;alert(&#39;foo&#39;);&lt;/script&gt;"
// 			g.Assert(s.StringField).Equal(expected)
// 			g.Assert(*s.StringPtr).Equal(expected)
// 		})

// 		g.It("should escape meta in group", func() {
// 			type UpdateGroupRequest struct {
// 				MaxMembers *int                    `json:"maxMembers,omitempty"`
// 				Meta       *map[string]interface{} `json:"meta,omitempty"`
// 			}

// 			meta := map[string]interface{}{
// 				"foo1": "<script>alert('foo');</script>",
// 				"foo2": aws.String("<script>alert('foo');</script>"),
// 			}
// 			obj := UpdateGroupRequest{
// 				Meta: &meta,
// 			}
// 			EscapeXSS(&obj)
// 			expected := "&lt;script&gt;alert(&#39;foo&#39;);&lt;/script&gt;"
// 			g.Assert((*obj.Meta)["foo1"].(string)).Equal(expected)
// 			g.Assert(*(*obj.Meta)["foo2"].(*string)).Equal(expected)

// 		})

// 	})
// }

type XSSTestStruct struct {
	IntField    int
	StringField string
	StringPtr   *string
}

func TestEscapeXSS(t *testing.T) {
	g := goblin.Goblin(t)
	g.Describe("TestEscapeXSS", func() {
		g.It("should return expected values", func() {

			s := XSSTestStruct{
				IntField:    42,
				StringField: "<script>alert('foo');</script>",
				StringPtr:   aws.String("<script>alert('foo');</script>"),
			}

			EscapeXSS(&s)
			expected := "&lt;script&gt;alert(&#39;foo&#39;);&lt;/script&gt;"
			g.Assert(s.StringField).Equal(expected)
			g.Assert(*s.StringPtr).Equal(expected)
		})
	})

}
