variable "project_code" {
  type    = string
  default = "social"
}

variable "env_ver_mapping_version" {
  type    = string
  default = ""
}
variable "redis_repgrp_num" {
  default = 1
}
variable "redis_repgrp_replica_count" {
  default = 0
}

variable "kinesis_shard_count" {
  description = "The number of shards that the stream will use."
  default     = "4"
}

variable "kinesis_retention_period" {
  description = "The number of days to retain data."
  default     = "28"
}

locals {
  REDIS_PORT      = 6379
  resource_prefix = "${var.project_code}-${local.env}"
  env             = terraform.workspace

  cluster_name = lookup(
    { "develop" = "t2gp-non-production", "production" = "t2gp-production" },
    local.env,
    ""
  )
  private_api_whitelist = [
    "*************/32" # hb studio
  ]
  tags = {
    "project"      = "social-service"
    "billing_code" = "d2c"
    "env"          = local.env
    terraform      = true
  }
}
