// Package validation for general purpose validation functions
package validation

import (
	"context"
	"regexp"
	"strings"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/go-playground/validator/v10"
)

const (
	KValidateUserID = "dnaid|uuid|number|testid"
	KValidateDNAID  = "dnaid"
	KValidateTestID = "testid"
)

var Validate = CreateValidator() // input validator to be used anywhere

// CreateValidator create validator
func CreateValidator() *validator.Validate {
	v := validator.New()
	v.RegisterValidation("dnaid", ValidateDNAID)
	v.RegisterValidation("testid", ValidateTestID)
	return v
}

func ValidateDNAID(fl validator.FieldLevel) bool {
	val := fl.Field().String()
	len32 := len(val) == 32
	lowercase := strings.ToLower(val) == val
	alphanum := regexp.MustCompile(`^[a-z0-9]*$`).MatchString(val)
	return len32 && lowercase && alphanum
}

func ValidateTestID(fl validator.FieldLevel) bool {
	val := fl.Field().String()
	return strings.HasPrefix(val, "t2gptest")
}

func IsDnaId(ctx context.Context, id string) bool {
	err := Validate.Var(id, KValidateDNAID)
	return err == nil
}

func IsFullSocialAccount(ctx context.Context, token *jwt.Token) bool {
	return IsDNAFullAccountId(ctx, token)
}

func IsDNAFullAccountId(ctx context.Context, token *jwt.Token) bool {
	userid := token.Claims.Subject
	dnaFullAccountId := ""

	//DNA jwt only has full acount in DnaFullAccountID ('pai') field if platform account.
	//if DnaAccountType = 3 (full account) the id is just the 'sub'
	if token.Claims.DnaAccountType == jwt.PlatformAccount {
		dnaFullAccountId = token.Claims.DnaFullAccountID
	} else if token.Claims.DnaAccountType == jwt.FullAccount {
		dnaFullAccountId = userid
	} else {
		return false
	}

	err := Validate.VarCtx(ctx, dnaFullAccountId, KValidateDNAID)
	return err == nil
}

func ValidateIdOrFirstPartyId(userid string) error {
	err := Validate.Var(userid, KValidateUserID)
	if err == nil {
		return nil
	}

	//validate Nintendo id
	idParts := strings.Split(userid, "-")
	if len(idParts) == 3 {
		//DNA Nintendo ID format is "Environment-NintendoServiceAccountId-NintendoAccountId
		return nil
	}

	//validate GameCenter
	idParts = strings.Split(userid, ":")
	if len(idParts) >= 2 {
		err = Validate.Var(idParts[1], KValidateUserID)
	}

	return err
}

// IsNintendoIdSame checks to see if the first 2 parts of the DNA Nintendo Id are the same
func IsNintendoIdSame(ctx context.Context, id *string, ost *int64, id2 *string, ost2 *int64) bool {
	if ost == nil || *ost != 11 ||
		ost2 == nil || *ost2 != 11 ||
		*ost != *ost2 {
		return false
	}

	if id == nil || id2 == nil {
		return false
	}

	idParts := strings.Split(*id, "-")
	id2Parts := strings.Split(*id2, "-")

	//DNA Nintendo ID format is "Environment-NintendoServiceAccountId-NintendoAccountId
	if len(idParts) != 3 || len(id2Parts) != 3 {
		return false
	}

	if idParts[0] == id2Parts[0] && idParts[1] == id2Parts[1] {
		return true
	}

	return false

}
