import PubSub from 'pubsub-js';

interface eventService {
  send: (eventName: string, payload?: unknown) => void;
  on: (eventName: string, subscriber: Function) => string;
  once: (eventName: string, subscriber: Function) => void;
  off: (eventName: string) => void;
  unbind: (token: string) => void;
}

export const EventService: eventService = {
  send: (eventName, payload) => {
    PubSub.publish(eventName, payload);
  },
  on: (eventName, subscriber) => {
    return PubSub.subscribe(eventName, subscriber);
  },
  once: (eventName, subscriber) => {
    PubSub.subscribeOnce(eventName, subscriber);
  },
  unbind: token => {
    PubSub.unsubscribe(token);
  },
  off: eventName => {
    PubSub.unsubscribe(eventName);
  },
};
