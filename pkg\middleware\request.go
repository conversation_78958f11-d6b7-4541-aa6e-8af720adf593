package middleware

import (
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
	"net/http"
)

func LimitRequestBody(maxBytesToRead int64) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			r.Body = http.MaxBytesReader(w, r.Body, maxBytesToRead)
			next.ServeHTTP(w, r)
		}
		return http.HandlerFunc(fn)
	}
}

func AddCustomHeadersToRootSpan() func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			ctx := r.Context()
			span, ok := tracer.SpanFromContext(ctx)
			if ok {
				span.SetTag("client.engine_identifier", r.Header.Get("engine-identifier"))
				span.SetTag("client.engine_version", r.Header.Get("engine-version"))
				span.SetTag("client.plugin_version", r.Header.Get("t2gp-plugin-version"))
				span.SetTag("client.platform_identifier", r.Header.Get("platform-identifier"))
				span.SetTag("client.client_version", r.Header.Get("client-version"))
			}
			next.ServeHTTP(w, r)
		}
		return http.HandlerFunc(fn)
	}
}
