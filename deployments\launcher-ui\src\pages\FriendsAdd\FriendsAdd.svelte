<script lang="ts">
  import { onMount } from 'svelte';
  import backgroundImage from '../../assets/images/social-background.jpg';
  import {
    FriendsSearch,
    SteamImport,
    Tab,
    TabList,
    TabPanel,
    Tabs,
    TaskBar,
  } from '../../components';
  import {
    EVENT_FRIENDS_ADD_WINDOW_CLOSE,
    EVENT_FRIENDS_ADD_WINDOW_MINIMIZE,
    EVENT_PENDING_FRIENDS_FETCH_RESULT,
  } from '../../constant';
  import {
    usePendingFriendsQuery,
    useTranslator,
    useTransportService,
  } from '../../hooks';
  import type { PendingFriend, QueryResult } from '../../services';
  import type { socialTheme } from '../../utils';
  import { themefunc } from '../../utils';

  export let theme: socialTheme = {
    color: '',
    bgColorTopBar: '',
    bgColorActionBar: '',
    bgColorButton: '',
  };

  $: style = themefunc(theme);

  const transportService = useTransportService();
  const t = useTranslator();

  const onCloseButtonClicked = () => {
    transportService.publishEvent(EVENT_FRIENDS_ADD_WINDOW_CLOSE);
  };

  const onMinimizedButtonClicked = () => {
    transportService.publishEvent(EVENT_FRIENDS_ADD_WINDOW_MINIMIZE);
  };

  const pendingFriendsQueryResult = usePendingFriendsQuery();

  onMount(() => {
    transportService.subscribeEvent(
      EVENT_PENDING_FRIENDS_FETCH_RESULT,
      (_: string, result: QueryResult<PendingFriend[]>) => {
        pendingFriendsQueryResult.set(result);
      }
    );
  });
</script>

<style>
  .container {
    height: 100%;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }
  .content {
    position: relative;
    padding: 2.5rem 2rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
  }

  .title {
    text-transform: capitalize;
    font-weight: bold;
    font-size: 1.5rem;
    line-height: 125%;
    color: var(--social-color, var(--default-color));
    margin-bottom: 1.5rem;
  }

  .container :global(.task-bar) {
    background-color: transparent;
  }
</style>

<div class="container" style="{`background-image: url("${backgroundImage}")`}">
  <TaskBar
    className="task-bar"
    on:close="{onCloseButtonClicked}"
    on:minimize="{onMinimizedButtonClicked}"
  />
  <div class="content" style="{style}">
    <div class="title">{$t('add friends')}</div>
    <Tabs>
      <TabList>
        <Tab>Search</Tab>
        <Tab>Steam</Tab>
        <Tab>Epic</Tab>
      </TabList>

      <TabPanel>
        <FriendsSearch />
      </TabPanel>

      <TabPanel>
        <SteamImport />
      </TabPanel>

      <TabPanel>
        <h2>Epic Import</h2>
      </TabPanel>
    </Tabs>
  </div>
</div>
