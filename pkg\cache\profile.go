package cache

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/constants"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// GetUserProfile get user's profile from redis
func (rc *RedisCache) GetUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := apipub.BuildUserRedisKey(tenant, userid)
	profile, err := getCachedObject[apipub.UserProfileResponse](ctx, rc, key)
	if err != nil {
		return nil, err
	}
	if profile != nil {
		return profile, nil
	}
	return nil, nil
}

// SetUserProfile set user's profile to redis
func (rc *RedisCache) SetUserProfile(ctx context.Context, profile *apipub.UserProfileResponse, ttl time.Duration) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	profile.Dob = nil
	profile.Email = nil
	return setCachedObject(ctx, rc, profile, profile.RedisKey(tenant), ttl)
}

func (rc *RedisCache) SetUserProfileTtl(ctx context.Context, userid string, ttl time.Duration) (bool, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := apipub.BuildUserRedisKey(tenant, userid)
	return rc.expire(ctx, key, ttl).Result()
}

// SetUserProfiles set multiple user profiles to redis
func (rc *RedisCache) SetUserProfiles(ctx context.Context, profiles *[]*apipub.UserProfileResponse, ttl time.Duration) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	keys := make([]string, 0, len(*profiles))
	for i, profile := range *profiles {
		(*profiles)[i].Dob = nil
		(*profiles)[i].Email = nil
		keys = append(keys, profile.RedisKey(tenant))
	}
	return setCachedObjects(ctx, rc, profiles, keys, ttl)
}

func (rc *RedisCache) DeleteUserProfile(ctx context.Context, userid string) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := apipub.BuildUserRedisKey(tenant, userid)
	return rc.DeleteCachedObj(ctx, key)
}

func (rc *RedisCache) GetUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	if len(userids) == 0 {
		return nil, nil
	}
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	keys := make([]string, len(userids))
	profiles := make([]*apipub.UserProfileResponse, 0, len(userids))
	for i, userid := range userids {
		keys[i] = apipub.BuildUserRedisKey(tenant, userid)
	}
	var profile *apipub.UserProfileResponse
	var err error
	for _, key := range keys {
		if profile, err = getCachedObject[apipub.UserProfileResponse](ctx, rc, key); err != nil {
			return nil, err
		}
		//if profile is not found in cache profile is nil
		if profile != nil {
			profiles = append(profiles, profile)
		}
	}
	return &profiles, nil

}

// GetRecentlyPlayed get recently played users from redis
func (rc *RedisCache) GetRecentlyPlayed(ctx context.Context, userid string, productid string, limit *int64, next *string) (*[]*apipub.RecentlyPlayedUserResponse, string, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	playedWithKey := userSubject.PlayedWithKey()
	if playedWithKey == nil {
		return nil, "", errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	idx := index.NewSecondaryIndex(*playedWithKey, "")
	idx.SetScore(1)

	items, str, err := getObjsFromSecondaryIndex[apipub.RecentlyPlayedUserResponse](ctx, rc, idx, limit, next, true)

	//clear hanging recently played users from list if a redis object is missing
	if err != nil && errs.IsEqual(err, errs.ERedisObjectMissing) {
		err = nil
		log := logger.FromContext(ctx)
		log.Debug().Str("tenant", tenant).Str("productid", productid).Str("userid", userid).Str("playedWithKey", *playedWithKey).Msg("Clear old recently played")
		go cleanRecentlyPlayed(ctx, rc, idx, playedWithKey)
	}

	return items, str, err
}

// SetRecentlyPlayedUser set a recently played user to redis
func (rc *RedisCache) SetRecentlyPlayedUser(ctx context.Context, productid string, recentlyPlayed *apipub.RecentlyPlayedUserResponse, ttl time.Duration) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	if recentlyPlayed == nil || recentlyPlayed.ForUserid == nil {
		return nil
	}
	userid := *recentlyPlayed.ForUserid
	withUserid := recentlyPlayed.Userid
	recentPlayedKey := apipub.BuildRecentlyPlayedRedisKey(tenant, productid, userid, withUserid)

	timestamp := time.Now().UTC()

	if recentlyPlayed.LastPlayed == nil || recentlyPlayed.LastPlayed.IsZero() {
		recentlyPlayed.LastPlayed = &timestamp
	}

	//set Object
	err := setCachedObject(ctx, rc, recentlyPlayed, recentPlayedKey, ttl)
	if err != nil {
		log.Error().Err(err).Str("userid", userid).Str("withUserid", withUserid).Msg("failed to set recently played")
		return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
	}

	score := float64(recentlyPlayed.LastPlayed.UTC().UnixMilli())
	if recentlyPlayed.Weight != nil && *recentlyPlayed.Weight > 0 {
		score += float64(*recentlyPlayed.Weight)
	}

	// set played with index.
	userSubject := index.NewUserSubject(tenant, productid, userid)
	if userSubject == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	playedWithKey := userSubject.PlayedWithKey()
	if playedWithKey == nil {
		return errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}
	idx := index.NewSecondaryIndex(*playedWithKey, recentPlayedKey)
	if score != float64(-1) {
		idx.SetScore(score)
	}

	return rc.setSecondaryIndex(ctx, idx)

}

// SetRecentlyPlayedUsers set recently played users to redis
func (rc *RedisCache) SetRecentlyPlayedUsers(ctx context.Context, productid string, recentlyPlayedUsers *[]*apipub.RecentlyPlayedUserResponse, ttl time.Duration) {
	log := logger.FromContext(ctx)
	if recentlyPlayedUsers == nil {
		return
	}
	for _, recentlyPlayedUser := range *recentlyPlayedUsers {
		if recentlyPlayedUser.ForUserid == nil {
			continue
		}
		err := rc.SetRecentlyPlayedUser(ctx, productid, recentlyPlayedUser, ttl)
		if err != nil {
			log.Error().Err(err).Msgf("failed to set recently played user in SetRecentlyPlayedUsers %s", err)
			continue
		}
	}
}

// DelRecentlyPlayedUsers set recently played users to redis
func (rc *RedisCache) DelRecentlyPlayedUsers(ctx context.Context, productid, userid string) {

	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	playedUsers, _, err := rc.GetRecentlyPlayed(ctx, userid, productid, aws.Int64(0), aws.String(""))

	if playedUsers != nil && len(*playedUsers) > 0 && err == nil {
		for _, recentlyPlayedUser := range *playedUsers {
			recentPlayedKey := apipub.BuildRecentlyPlayedRedisKey(tenant, productid, userid, recentlyPlayedUser.Userid)
			rc.DeleteCachedObj(ctx, recentPlayedKey)
			// set played with index.
			userSubject := index.NewUserSubject(tenant, productid, userid)
			if userSubject == nil {
				continue
			}
			playedWithKey := userSubject.PlayedWithKey()
			if playedWithKey == nil {
				continue
			}
			idx := index.NewSecondaryIndex(*playedWithKey, recentPlayedKey)
			rc.delSecondaryIndex(ctx, idx)
		}
		log.Info().Str("userid", userid).Str("event", "deleted recently played list").Msg("deleted recently played list")
	}
	if err != nil {
		log.Err(err).Str("userid", userid).Str("event", "error getting list to delete recently played list").Msg("error getting list to delete recently played list")
	}
}

func (rc *RedisCache) UpdateUserTtls(ctx context.Context, userid string, ttl time.Duration) ([]bool, error) {
	var bArr []bool
	for i := 0; i < 3; i++ {
		bArr = append(bArr, true)
	}
	//log := logger.FromContext(ctx)

	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	userKey := apipub.BuildUserRedisKey(tenant, userid)
	//log.Info().Msgf("userKey: %v", userKey)
	bRet, err := rc.expire(ctx, userKey, ttl).Result()
	if !bRet {
		for i := 0; i < 3; i++ {
			bArr[i] = false
		}
		return bArr, err
	}

	userSub := index.NewUserRelationSubject(tenant, userid)
	if userSub == nil {
		for i := 0; i < 3; i++ {
			bArr[i] = false
		}
		return bArr, errs.New(http.StatusNotFound, errs.ESubjectNotFound)
	}

	bInitialMeta := false
	userMeta, metaErr := rc.GetUserCacheMeta(ctx, userid)
	if metaErr != nil {
		log := logger.FromContext(ctx)
		log.Error().Err(metaErr).Str("userid", userid).Msg("get userMeta err")
	}
	if userMeta == nil {
		bInitialMeta = true
		//assume we have friends, pending, and blocks if no meta object exists
		userMeta = &apipub.UserCacheMeta{
			Friends: true,
			Pending: true,
			Blocks:  true,
		}
	}
	bOldFriends := userMeta.Friends
	bOldPending := userMeta.Pending
	bOldBlocks := userMeta.Blocks
	bNewFirstParty := rc.upsertMetaFirstPartyKeyTtls(ctx, userid, ttl, userMeta)

	//friend keys
	key := userSub.FriendsListKey()
	bRet, _ = rc.updateKeyTtl(ctx, key, ttl, false, nil)
	if !bRet && (bOldFriends || bOldPending) {
		bArr[1] = false
	}
	//these subkey objects are the same as in the main friends list
	//update sub objects in these for metadata
	key = userSub.FriendsStatusKey("pending")
	bRet, _ = rc.updateKeyTtl(ctx, key, ttl, true, userMeta)
	if !bRet && userMeta.Pending {
		bArr[1] = false
	}
	key = userSub.FriendsStatusKey("friend")
	bRet, _ = rc.updateKeyTtl(ctx, key, ttl, true, userMeta)
	if !bRet && userMeta.Friends {
		bArr[1] = false
	}

	//blocklist keys
	key = userSub.BlockedKey()
	bRet, _ = rc.updateKeyTtl(ctx, key, ttl, true, userMeta)
	if !bRet && userMeta.Blocks {
		bArr[2] = false
	}
	key = userSub.BlockedByKey()
	rc.updateKeyTtl(ctx, key, ttl, true, nil)
	//blockedBy subkey objects are other user's profiles, so we don't need to update the blocklist return

	//if something changed, update user metadata
	if bInitialMeta || bOldFriends != userMeta.Friends || bOldPending != userMeta.Pending || bOldBlocks != userMeta.Blocks || bNewFirstParty {
		metaErr = rc.SetUserCacheMeta(ctx, userid, userMeta, ttl)
		if metaErr != nil {
			log := logger.FromContext(ctx)
			log.Error().Err(metaErr).Str("userid", userid).Msg("get userMeta err")
		}
	} else { //update the ttl
		rc.UpdateUserCacheMetaTtl(ctx, userid, ttl)
	}

	return bArr, err
}

// GetUserFirstPartyToken cache function to get first party token used for session syncing
func (rc *RedisCache) GetUserFirstPartyToken(ctx context.Context, firstPartyid string, ost int) (string, error) {
	if firstPartyid == "" {
		return "", errs.New(http.StatusNotFound, errs.EInvalidUserID)
	}
	return rc.get(ctx, buildFirstPartyTokenKey(firstPartyid, ost)).Result()
}

// GetUserFirstPartyRefresh cache function to get first party refresh token for session syncing
func (rc *RedisCache) GetUserFirstPartyRefresh(ctx context.Context, firstPartyid string, ost int) (string, error) {
	if firstPartyid == "" {
		return "", errs.New(http.StatusNotFound, errs.EInvalidUserID)
	}
	return rc.get(ctx, buildFirstPartyRefreshKey(firstPartyid, ost)).Result()
}

// SetUserFirstPartyToken cache function to write first party token and refresh tokens with ttl
func (rc *RedisCache) SetUserFirstPartyToken(ctx context.Context, firstPartyid string, ost int, token string, ttl time.Duration, refreshToken string, refreshTokenTtl time.Duration) error {
	if firstPartyid == "" {
		return errs.New(http.StatusNotFound, errs.EInvalidUserID)
	}
	tokenKey := buildFirstPartyTokenKey(firstPartyid, ost)
	if ttl <= 0 {
		ttl = time.Second * time.Duration(rc.cfg.TtlFirstPartyToken)
	}
	err := rc.set(ctx, tokenKey, token, ttl)

	if refreshToken != "" {
		refreshKey := buildFirstPartyRefreshKey(firstPartyid, ost)
		if refreshTokenTtl <= 0 {
			refreshTokenTtl = time.Second * time.Duration(rc.cfg.TtlFirstPartyRefresh)
		}
		err = rc.set(ctx, refreshKey, refreshToken, refreshTokenTtl)
	}
	if err != nil {
		return errs.New(http.StatusBadRequest, errs.ERedisCacheSetFailed)
	}
	return nil
}

func (rc *RedisCache) updateKeyTtl(ctx context.Context, key *string, ttl time.Duration, bUpdateObjs bool, userMeta *apipub.UserCacheMeta) (bool, error) {
	if key != nil {
		bRet, err := rc.expire(ctx, *key, ttl).Result()
		if bRet && bUpdateObjs {
			idx := index.NewSecondaryIndex(*key, "")
			bRet, err = rc.updateObjectTtls(ctx, idx, ttl, userMeta)
		}
		return bRet, err
	}
	return true, nil
}

func (rc *RedisCache) updateObjectTtls(ctx context.Context, idx *index.SecondaryIndex, ttl time.Duration, userMeta *apipub.UserCacheMeta) (bool, error) {
	log := logger.FromContext(ctx)
	limit64 := int64(100)
	var next *string

	var valKeys *[]string
	var retNext string
	var err error
	bKeyRet := true
	bRet := true
	retNext = "more"

	for retNext != "" {
		valKeys, retNext, err = getKeysFromSecondaryIndex(ctx, rc, idx, &limit64, next, false)

		if err == nil && valKeys != nil {
			if len(*valKeys) > 0 && userMeta != nil {
				if strings.HasSuffix(idx.IdxKey(), "friend") {
					userMeta.Friends = true
				} else if strings.HasSuffix(idx.IdxKey(), "pending") {
					userMeta.Pending = true
				} else if strings.HasSuffix(idx.IdxKey(), "blocked") {
					userMeta.Blocks = true
				}
			}
			for i := 0; i < len(*valKeys); i++ {
				bKeyRet, err = rc.expire(ctx, (*valKeys)[i], ttl).Result()
				if !bKeyRet || err != nil {
					log.Info().Err(err).Str("key", (*valKeys)[i]).Msg("failed to update redis object ttl")
					bRet = false
				}
			}
			if retNext != "" {
				next = &retNext
			}
		}
	}

	return bRet, err
}

func (rc *RedisCache) upsertMetaFirstPartyKeyTtls(ctx context.Context, userid string, ttl time.Duration, userMeta *apipub.UserCacheMeta) bool {
	bNewFirstParty := false

	if len(userMeta.FirstParty) == 0 {
		//if no first party keys, try to get them from profile
		profile, _ := rc.GetUserProfile(ctx, userid)
		if profile.Links != nil && len(*profile.Links) > 0 {
			for _, link := range *profile.Links {
				if link.AccountType != nil && *link.AccountType == apipub.AccountTypeDNA(2) &&
					link.FirstPartyid != nil && link.OnlineServiceType != nil {
					key, err := rc.SetFirstPartyLookup(ctx, *link.FirstPartyid, *link.OnlineServiceType, profile.Userid, ttl)
					if err == nil {
						userMeta.FirstParty = append(userMeta.FirstParty, key)
						bNewFirstParty = true
					}
				}
			}
		}
	} else {
		//update ttl in first party keys
		for _, key := range userMeta.FirstParty {
			rc.expire(ctx, key, ttl)
		}
	}

	return bNewFirstParty
}

// cleanRecentlyPlayed delete any recently played users for objects that expired, but are still in user played list
func cleanRecentlyPlayed(ctx context.Context, rc *RedisCache, idx *index.SecondaryIndex, playedWithKey *string) {
	newctx := context.WithoutCancel(ctx)
	valKeys, _, err := getKeysFromSecondaryIndex(newctx, rc, idx, nil, nil, false)
	if err == nil && valKeys != nil {

		for _, playedKey := range *valKeys {
			keyParts := strings.Split(playedKey, ":")
			if len(keyParts) >= 8 && keyParts[4] == "user" && keyParts[6] == "played" {
				tenant := keyParts[0]
				newctx = context.WithValue(newctx, constants.T2GPCtxTenant, tenant)
				productid := keyParts[2]
				userid := utils.RemoveCurlyBrackets(keyParts[5])
				playedWithId := keyParts[7]

				//check for existing object in redis. if not found, delete
				_, err2 := getCachedObjects[apipub.RecentlyPlayedUserResponse](newctx, rc, &[]string{playedKey})
				if err2 != nil && errs.IsEqual(err2, errs.ERedisObjectMissing) {
					idx2 := index.NewSecondaryIndex(*playedWithKey, apipub.BuildRecentlyPlayedRedisKey(tenant, productid, userid, playedWithId))
					rc.delSecondaryIndex(newctx, idx2)
				}
			}
		}
	}
}

func buildFirstPartyTokenKey(firstPartyid string, ost int) string {
	return fmt.Sprintf("firstPartyid:{%s}:{%d}:token", firstPartyid, ost)
}

func buildFirstPartyRefreshKey(firstPartyid string, ost int) string {
	return fmt.Sprintf("firstPartyid:{%s}:{%d}:refresh", firstPartyid, ost)
}
