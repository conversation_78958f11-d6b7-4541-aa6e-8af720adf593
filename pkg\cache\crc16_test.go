package cache

import (
	"testing"

	"github.com/franela/goblin"
	gomock "go.uber.org/mock/gomock"
)

func Test_Crc16(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	group1 := "group:productid:{groupid1}"
	group2 := "group:productid:{groupid2}"
	nohashkey := "group:productid:groupid3"
	badhashkey := "group:productid:{groupid3"
	var keyslot1 uint16
	var keyslot2 uint16

	g.Describe("get crc16 hashes", func() {

		g.It("get group1 slot info", func() {
			hash1 := GetHashTag(group1)
			g.Assert(hash1).Equal("groupid1")
			slot1 := GetHashSlot(hash1)
			keyslot1 = GetKeySlot(group1)
			g.Assert(slot1).Equal(keyslot1)
		})
		g.It("get group2 slot info", func() {
			hash2 := GetHashTag(group2)
			g.Assert(hash2).Equal("groupid2")
			slot2 := GetHashSlot(hash2)
			keyslot2 = GetKeySlot(group2)
			g.<PERSON>sert(slot2).Equal(keyslot2)
		})
		g.It("group1 and group2 have different slots", func() {
			g.Assert(keyslot1 == keyslot2).IsFalse()
		})
		g.It("get slot info with no hash", func() {
			hash := GetHashTag(nohashkey)
			g.Assert(hash).Equal("")
			slot := GetHashSlot(nohashkey)
			keyslot := GetKeySlot(nohashkey)
			g.Assert(slot).Equal(keyslot)
		})
		g.It("get slot info with bad hash format", func() {
			hash := GetHashTag(badhashkey)
			g.Assert(hash).Equal("")
			slot := GetHashSlot(badhashkey)
			keyslot := GetKeySlot(badhashkey)
			g.Assert(slot).Equal(keyslot)
		})

	})
}
