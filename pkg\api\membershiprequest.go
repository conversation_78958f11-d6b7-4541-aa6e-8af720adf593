package api

import (
	"context"
	"net/http"
	"time"

	"github.com/rs/zerolog/log"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// checkOwnership takes a makes sure that a membership request belongs to the userid by checking if the userid is the full account of the Id in the membershiprequest
func checkOwnership(ctx context.Context, api *SocialPublicAPI, userid string, memberid string, firstPartyId string, onlineServiceType int64, isFirstPartyInvite bool) bool {
	log := logger.FromContext(ctx)
	if !isFirstPartyInvite {
		return true
	}

	//check redis for user profile and check links.
	profile, _ := api.getUserProfile(ctx, userid, true)
	if profile != nil {
		if profile.Userid == memberid {
			return true
		} else if profile.Links != nil {
			for _, link := range *profile.Links {
				if (link.AccountId != nil && *link.AccountId == memberid && link.OnlineServiceType != nil) ||
					(link.FirstPartyid != nil && *link.FirstPartyid == memberid &&
						link.OnlineServiceType != nil && int64(*link.OnlineServiceType) == onlineServiceType) ||
					validation.IsNintendoIdSame(ctx, &memberid, aws.Int64(onlineServiceType), link.FirstPartyid, aws.Int64(int64(*link.OnlineServiceType))) {
					return true
				}
			}
		}
	}

	searchType := apipub.AccountsByFirstPartyId
	req := &apipub.SearchAccountRequest{
		Type: &searchType,
		Criterias: &[]apipub.SearchAccountCriteria{
			{
				FirstPartyId:      &firstPartyId,
				OnlineServiceType: aws.Int(int(onlineServiceType)),
			},
		},
	}

	// the accounts returned from search are only platform accounts
	searchResponse, searchErr := api.Id.SearchAccounts(ctx, req)
	if searchErr != nil {
		log.Error().Err(searchErr).Str("firstPartyId", firstPartyId).Int64("ost", onlineServiceType).Msgf("Failed to DNA user search")
		return false
	}

	if searchResponse != nil {
		for _, account := range *searchResponse {
			// we check if userid matches the platform account or the parent account Id
			// for the full account to support both account types
			// or is a Nintendo account
			if (account.Id != nil && *account.Id == userid) ||
				(account.ParentAccountId != nil && *account.ParentAccountId == userid && account.OnlineServiceType != nil) ||
				validation.IsNintendoIdSame(ctx, &userid, aws.Int64(onlineServiceType), account.Id, aws.Int64(int64(*account.OnlineServiceType))) {
				return true
			}
		}
	}
	return false
}

// JoinGroupFinalizeHelper helper function to wrap up the actions that happen when a user successfully joins a group.
// Includes saving the GroupMember, Saving the Group.  Subscribing to group topic.  Sending MQTT message. etc.
// appid and token are separate because token is needed for a lot of claims but appid is sent by trusted API if no token exists
func (api *SocialPublicAPI) JoinGroupFinalizeHelper(r *http.Request, group *apipub.GroupResponse, appid string, ost apipub.OnlineServiceType, token *jwt.Token, memberid string, approverid string, teleMeta apipub.TelemetryMetaData) (*apipub.GroupResponse, error) {
	ctx := r.Context()
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	log := logger.Get(r)
	if group == nil {
		return nil, errs.New(http.StatusUnprocessableEntity, errs.EGroupsInvalidGroupID)
	}

	groupid := group.Groupid
	productid := group.Productid
	groupTopic := group.Topic(tenant)
	userid := ""
	sessionid := ""
	createdTime := int64(0)
	expiresTime := int64(0)
	if token != nil {
		userid = token.Claims.Subject
		sessionid = token.Claims.SessionID
		createdTime = token.Claims.CreatedTime
		expiresTime = token.Claims.ExpiresTime
	}

	member := apipub.GroupMemberResponse{
		Productid: group.Productid,
		Role:      apipub.Member,
		Userid:    memberid,
		Name:      aws.String(""),
	}

	//get group at the start of finalize to make sure we have the newest version.
	group, err := api.Cache.GetGroup(ctx, productid, groupid)
	if err != nil || group == nil {
		log.Error().Err(err).Str("groupid", groupid).Str("productid", productid).Msg("could not get group")
		return nil, errs.New(http.StatusUnprocessableEntity, errs.EDynamodbReadFailed)

	}

	//Add displayname to member if nil
	if member.Name == nil || *member.Name == "" {
		profile, _ := api.getUserProfile(r.Context(), member.Userid, true)
		if profile != nil && profile.DisplayName != nil {
			member.Name = profile.DisplayName
		}
	}

	presence, err := api.Cache.GetPresence(ctx, member.Userid, productid, "")
	if err != nil {
		log.Error().Err(err).Str("userid", member.Userid).Str("productid", productid).Msg("could not get presence")
	}
	if presence != nil {
		member.Presence = presence
	}

	err = api.Cache.AddGroupMember(ctx, group, &member)
	if err != nil {
		log.Error().Err(err).Msgf("put group member in group failed")
		return nil, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed)
	}
	// update group sizes in other group members active group records if this group is their active group
	if len(*group.Members) > 0 {
		for _, checkMemberActive := range *group.Members {
			if checkMemberActive.Userid != userid {
				api.Cache.SetActiveGroup(ctx, group, productid, appid, sessionid, checkMemberActive.Userid, createdTime, expiresTime, true)
			}
		}
	}

	// subscribe to group topic
	subErr := messenger.Subscribe(ctx, api.Cfg, memberid, groupTopic)
	if subErr != nil {
		log.Error().Err(subErr).Msgf("failed to subscribe to %s for %s", groupTopic, memberid)
	}

	// send joined message
	msgType := messenger.MqttMessageTypeGroupMembersModified
	data := apipub.MqttGroupMemberModified{
		Action:   "joined",
		Userid:   memberid,
		Reason:   "joined",
		PreRole:  apipub.Nonmember,
		PostRole: apipub.Member,
		Groupid:  groupid,
	}
	messenger.SendMqttMessage(ctx, api.Cfg, group.Topic(tenant), msgType, &data)

	// process telemeta if exists for telemetry
	additionalInfo := make(map[string]string)
	if teleMeta != nil {
		utils.ConvertMapInterfaceToMapString(teleMeta, &additionalInfo)
	}

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupJoin, memberid, ost, []string{approverid}, &appid, &additionalInfo))

	return group, nil
}

func (api *SocialPublicAPI) validCrossPlayRequest(group apipub.GroupResponse, membershipRequest apipub.MembershipRequest) bool {
	if group.CanCrossPlay == nil || membershipRequest.CanCrossPlay == nil {
		return false
	}
	//group is cross play.  user cannot cross play.  false
	if *group.CanCrossPlay && !*membershipRequest.CanCrossPlay {
		return false
	}
	//group is not crossplay.  platforms do not match.  false.
	if !*group.CanCrossPlay && *group.OnlineServiceType != *membershipRequest.OnlineServiceType {
		return false
	}

	return true
}

// deletes the membershipRequest record as well as removes it from the group.
func (api *SocialPublicAPI) deletePreviousMembershipRequestFull(ctx context.Context, group *apipub.GroupResponse, membershipRequest apipub.MembershipRequest) error {
	if group == nil {
		return nil
	}
	prevMembershipRequest := membershipRequest

	if membershipRequest.Status == apipub.Accepted || membershipRequest.Status == apipub.Revoked || membershipRequest.Status == apipub.Declined {
		prevMembershipRequest.Status = apipub.Invited
	}
	if membershipRequest.Status == apipub.Approved || membershipRequest.Status == apipub.Rejected {
		prevMembershipRequest.Status = apipub.Requested
	}

	return api.Cache.RemoveMembershipRequestFromGroup(ctx, group, prevMembershipRequest)
}

func (api *SocialPublicAPI) processJoinRequestActions(r *http.Request, group *apipub.GroupResponse, bTSForce bool, appid string, ost apipub.OnlineServiceType, token *jwt.Token, memberid string, approverid string, password string, displayName string, teleMeta apipub.TelemetryMetaData) *errs.Error {
	log := logger.Get(r)
	var err error
	userid := memberid
	productid := group.Productid

	// pre-process telemeta if exists for telemetry
	additionalInfo := make(map[string]string)
	if teleMeta != nil {
		utils.ConvertMapInterfaceToMapString(teleMeta, &additionalInfo)
	}

	// starting v2 supplying correct password works for all join request actions and remove authenticate type
	ostType := apipub.OnlineServiceType(int64(ost))
	if group.Password != nil && password != "" && group.JoinRequestAction != apipub.AutoApprove {
		if utils.CheckPassword(*group.Password, password) {
			// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
			originalOST := group.OnlineServiceType
			group.OnlineServiceType = &ostType

			//clear any existing membership requests/invites for user since password succeeded and the join will happen.
			api.Cache.ClearAllMemberships(r.Context(), userid, productid, group.Groupid)
			group.ClearMemberships(userid, string(apipub.Requested))

			api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupAuthenticate, userid, ost, []string{userid}, &appid, &additionalInfo))
			group.OnlineServiceType = originalOST
			_, err = api.JoinGroupFinalizeHelper(r, group, appid, ost, token, memberid, approverid, nil)
			if err != nil {
				log.Error().Err(err).Msgf("processJoinRequestActions failed")
				return err.(*errs.Error)
			}
			return nil
		} else {
			//if password is wrong, clear out the request because it failed.
			api.Cache.ClearAllMemberships(r.Context(), userid, productid, group.Groupid)
			group.ClearMemberships(userid, string(apipub.Requested))
			log.Error().Err(err).Msgf("pw incorrect")
			return errs.New(http.StatusForbidden, errs.EGroupsPasswordIncorrect)
		}
	}
	originalOST := group.OnlineServiceType
	newOst := apipub.OnlineServiceType(int64(ost))
	if bTSForce {

		//clear any existing membership requests/invites for user since this is being forced through by trusted API.
		api.Cache.ClearAllMemberships(r.Context(), userid, productid, group.Groupid)
		group.ClearMemberships(userid, string(apipub.Requested))

		// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
		group.OnlineServiceType = &ostType
		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupApprove, approverid, ost, []string{userid}, &appid, &additionalInfo))
		group.OnlineServiceType = originalOST

		_, err = api.JoinGroupFinalizeHelper(r, group, appid, ost, token, memberid, approverid, nil)
		if err != nil {
			log.Error().Err(err).Msgf("processJoinRequestActions failed")
			return err.(*errs.Error)
		}
		return nil
	}

	switch group.JoinRequestAction {
	case apipub.AutoReject:
		log.Info().Msg("closed groups does not accept join requests")
		tmpJoinRequest := apipub.MembershipRequest{
			Memberid:   memberid,
			Approverid: approverid,
			Status:     apipub.Rejected,
			Productid:  &group.Productid,
			Groupid:    group.Groupid,
		}

		api.deletePreviousMembershipRequestFull(r.Context(), group, tmpJoinRequest)

		// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
		group.OnlineServiceType = &newOst

		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupReject, approverid, ost, []string{userid}, &appid, &additionalInfo))
		group.OnlineServiceType = originalOST

		return errs.New(http.StatusForbidden, errs.EGroupsDoesNotAcceptJoinRequests)
	case apipub.AutoApprove:

		// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
		group.OnlineServiceType = &newOst

		//clear any existing membership requests/invites for user
		api.Cache.ClearAllMemberships(r.Context(), userid, productid, group.Groupid)
		group.ClearMemberships(userid, string(apipub.Requested))

		api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupApprove, approverid, ost, []string{userid}, &appid, &additionalInfo))
		group.OnlineServiceType = originalOST

		_, err = api.JoinGroupFinalizeHelper(r, group, appid, ost, token, memberid, approverid, nil)
		if err != nil {
			log.Error().Err(err).Msgf("processJoinRequestActions failed")
			return err.(*errs.Error)
		}
		return nil
	case apipub.Manual:

		tenant := identity.GetTenantFromCtx(r.Context(), api.Id)

		topic := tenant + "/user/" + approverid
		msgType := messenger.MqttMessageTypeGroupJoinRequest
		data := apipub.MqttJoinRequest{
			Groupid:    group.Groupid,
			Productid:  group.Productid,
			Userid:     memberid,
			Status:     apipub.Requested,
			Name:       &displayName,
			Approverid: &approverid,
		}
		messenger.SendMqttMessage(r.Context(), api.Cfg, topic, msgType, &data)
	}

	// set group ost to membershiprequest ost but don't save it.  this is purely for telemetry reasons which pulls the ost for the event from the group for group telemetry events.
	group.OnlineServiceType = &newOst

	api.Tele.SendGroupEvent(r.Context(), telemetry.BuildGroupTeleMeta(group, telemetry.KGroupRequestToJoin, memberid, ost, []string{approverid}, &appid, &additionalInfo))
	group.OnlineServiceType = originalOST

	return nil
}

// IsUserIdFullAccount  This function is mainly to verify that a userid that we know about is a full account.  It secondarily also checks the token instead if there is one, but it's not always there.
func (api *SocialPublicAPI) IsUserIdFullAccount(ctx context.Context, token *jwt.Token, userId string) bool {
	//if we have a token, check token
	if token != nil {
		return validation.IsFullSocialAccount(ctx, token)
	}

	if utils.GetValueFromContext(ctx, constants.T2GPCtxTenant) != "dna" {
		return true
	}

	//if trusted server made the call, the Id is considered a full account Id
	if userId == constants.TrustedServer {
		return true
	}

	//get user profile and check links for full account type
	//full account with no links is possible
	profile, _ := api.getUserProfile(ctx, userId, true)
	if profile != nil {
		if profile.Links != nil && len(*profile.Links) > 0 {
			for _, link := range *profile.Links {
				if link.AccountType != nil && *link.AccountType == apipub.AccountTypeDNAFULL {
					return true
				}
			}
			//if have links and none are parent accout type, return false
			return false
		} else if profile.DisplayName != nil && len(*profile.DisplayName) > 0 {
			//if no links and have a display name return true
			return true
		} else { //no display name = first party
			return false
		}
	}

	//no profile found
	return false
}

// GetParentAccountId Try to get the 2K parent account for MQTT invite topic if isFirstPartyInvite.
// Otherwise return memberId back if not isFirstPartyInvite.
func (api *SocialPublicAPI) GetParentAccountId(ctx context.Context, memberId string, ost apipub.OnlineServiceType, isFirstPartyInvite *bool) string {
	parentId := memberId

	if isFirstPartyInvite != nil && *isFirstPartyInvite {
		log := logger.FromContext(ctx)

		//check Cache for first party Id first
		cacheParent, pErr := api.Cache.GetFirstPartyLookup(ctx, memberId, ost)
		if pErr != nil {
			log.Error().Err(pErr).Str("event", "failed to getFirstPartyLookup").Str("Id", memberId).Int("ost", int(ost)).Send()
		}
		if cacheParent != nil && *cacheParent != "" {
			msg := "cache parent"
			log.Debug().Str("firstpartyid", memberId).Str("fullAccountId", *cacheParent).Str("event", msg).Msg(msg)
			return *cacheParent
		}

		// search DNA for platfrom friends
		var criterias []apipub.SearchAccountCriteria
		criterias = append(criterias, apipub.SearchAccountCriteria{
			FirstPartyId:      aws.String(memberId),
			OnlineServiceType: aws.Int(int(ost)),
		})

		searchRequestType := apipub.AccountsByFirstPartyId
		searchRequest := &apipub.SearchAccountRequest{
			Type:      &searchRequestType,
			Criterias: &criterias,
		}
		var searchResponse *apipub.SearchAccountResponseList
		searchResponse, err := api.Id.SearchAccounts(ctx, searchRequest)
		if err != nil {
			log.Error().Err(err).Str("event", "failed to search accounts").Str("Id(s)", memberId).Send()
			return parentId
		}

		if searchResponse != nil {
			for _, act := range *searchResponse {
				//if first party Id matches what is returned, or it is a Nintendo account
				if (act.FirstPartyId != nil && *act.FirstPartyId == memberId && act.OnlineServiceType != nil && *act.OnlineServiceType == ost) ||
					validation.IsNintendoIdSame(ctx, &memberId, aws.Int64(int64(ost)), act.FirstPartyId, aws.Int64(int64(*act.OnlineServiceType))) {
					if act.ParentAccountId != nil {
						parentId = *act.ParentAccountId
						log.Debug().Str("memberid", memberId).Str("parentid", parentId).Msg("Found Parent Account for MQTT")
					} else if act.Id != nil {
						parentId = *act.Id
						log.Debug().Str("memberid", memberId).Str("platformid", parentId).Msg("Found Platform Account for MQTT")
					}

					//update userCacheMetadata
					userMeta, metaErr := api.Cache.GetUserCacheMeta(ctx, parentId)
					if metaErr != nil {
						log.Error().Err(metaErr).Str("userid", parentId).Msg("get userMeta err")
					}
					if userMeta == nil {
						//assume we have friends, pending, and blocks if no meta object exists
						userMeta = &apipub.UserCacheMeta{
							Friends: true,
							Pending: true,
							Blocks:  true,
						}
					}

					ttl := time.Duration(api.Cfg.TtlProfile) * time.Second
					key, err := api.Cache.SetFirstPartyLookup(ctx, memberId, ost, parentId, ttl)
					if err == nil {
						userMeta.FirstParty = append(userMeta.FirstParty, key)
					}

					api.Cache.SetUserCacheMeta(ctx, parentId, userMeta, ttl)

					break
				}
			}
		}
	}
	msg := "dna search parent"
	log.Debug().Str("firstpartyid", memberId).Str("fullAccountId", parentId).Str("event", msg).Msg(msg)
	return parentId
}
