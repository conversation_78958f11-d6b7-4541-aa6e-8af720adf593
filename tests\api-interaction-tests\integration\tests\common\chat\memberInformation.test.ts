import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let tokenHost: string;
let tokenInvited: string;
let roomId: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  tokenInvited = await socialApi.loginIn(
    config.invitedUsername,
    config.invitedPassword
  );
});

afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.deleteRoom(tokenInvited, roomId, config.invitedUserId);
  await socialApi.loginOut(tokenHost);
  await socialApi.loginOut(tokenInvited);
});
describe('', () => {
  /**
   * Checking member information
   * - A Create room
   * - B joinRoon
   * - Get member information
   */
  it('member information', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.joinRoom(tokenInvited, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({});
    resp = await socialApi.getMemberInformation(
      tokenHost,
      roomId,
      config.inviteUserId
    );
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.groupid).toEqual(roomId);
  });
});
