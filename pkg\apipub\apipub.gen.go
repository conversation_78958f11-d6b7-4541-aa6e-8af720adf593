// Package apipub provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.3.0 DO NOT EDIT.
package apipub

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
)

const (
	BearerAuthScopes = "bearerAuth.Scopes"
)

// Defines values for AccountLinkDNALinkType.
const (
	Epic     AccountLinkDNALinkType = "epic"
	Nintendo AccountLinkDNALinkType = "nintendo"
	Parent   AccountLinkDNALinkType = "parent"
	Psn      AccountLinkDNALinkType = "psn"
	Steam    AccountLinkDNALinkType = "steam"
	Xbl      AccountLinkDNALinkType = "xbl"
)

// Defines values for AccountTypeDNA.
const (
	AccountTypeDNAANONYMOUS                 AccountTypeDNA = 1
	AccountTypeDNAFULL                      AccountTypeDNA = 3
	AccountTypeDNANEWSLETTER                AccountTypeDNA = 5
	AccountTypeDNAPLATFORM                  AccountTypeDNA = 2
	AccountTypeDNAPRIVACYPOLICYACCEPTEDONLY AccountTypeDNA = 7
	AccountTypeDNATOOLS                     AccountTypeDNA = 4
	AccountTypeDNAUNDISCLOSED               AccountTypeDNA = 6
	AccountTypeDNAUNKNOWN                   AccountTypeDNA = 0
)

// Defines values for ChatMessageType.
const (
	Group ChatMessageType = "group"
	N1on1 ChatMessageType = "1on1"
)

// Defines values for DiscoveryURLResponseType.
const (
	Http    DiscoveryURLResponseType = "http"
	Mqtt    DiscoveryURLResponseType = "mqtt"
	Trusted DiscoveryURLResponseType = "trusted"
)

// Defines values for FriendStatus.
const (
	Friend  FriendStatus = "friend"
	Pending FriendStatus = "pending"
)

// Defines values for GroupMemberRole.
const (
	Leader    GroupMemberRole = "leader"
	Member    GroupMemberRole = "member"
	Nonmember GroupMemberRole = "nonmember"
)

// Defines values for JoinRequestAction.
const (
	AutoApprove JoinRequestAction = "auto-approve"
	AutoReject  JoinRequestAction = "auto-reject"
	Manual      JoinRequestAction = "manual"
)

// Defines values for MembershipStatus.
const (
	Accepted  MembershipStatus = "accepted"
	Approved  MembershipStatus = "approved"
	Declined  MembershipStatus = "declined"
	Invited   MembershipStatus = "invited"
	Rejected  MembershipStatus = "rejected"
	Requested MembershipStatus = "requested"
	Revoked   MembershipStatus = "revoked"
)

// Defines values for MessageBodySegementType.
const (
	DeepLink  MessageBodySegementType = "deepLink"
	HyperLink MessageBodySegementType = "hyperLink"
	Text      MessageBodySegementType = "text"
)

// Defines values for OnlineServiceType.
const (
	OnlineServiceTypeAPPLE                    OnlineServiceType = 22
	OnlineServiceTypeCALICO                   OnlineServiceType = 10
	OnlineServiceTypeDEVICE                   OnlineServiceType = 21
	OnlineServiceTypeEPIC                     OnlineServiceType = 15
	OnlineServiceTypeFACEBOOK                 OnlineServiceType = 17
	OnlineServiceTypeGAMECENTER               OnlineServiceType = 12
	OnlineServiceTypeGOOGLE                   OnlineServiceType = 18
	OnlineServiceTypeGOOGLEPLAY               OnlineServiceType = 6
	OnlineServiceTypeLEGACYGAMECENTER         OnlineServiceType = 5
	OnlineServiceTypeNINTENDO                 OnlineServiceType = 11
	OnlineServiceTypeSONYENTERTAINMENTNETWORK OnlineServiceType = 2
	OnlineServiceTypeSTADIA                   OnlineServiceType = 16
	OnlineServiceTypeSTEAM                    OnlineServiceType = 3
	OnlineServiceTypeT2GP                     OnlineServiceType = 24
	OnlineServiceTypeTWITCH                   OnlineServiceType = 20
	OnlineServiceTypeTWITTER                  OnlineServiceType = 19
	OnlineServiceTypeUNKNOWN                  OnlineServiceType = 0
	OnlineServiceTypeVORTEX                   OnlineServiceType = 14
	OnlineServiceTypeWEB                      OnlineServiceType = 4
	OnlineServiceTypeWEGAME                   OnlineServiceType = 13
	OnlineServiceTypeWINDOWSDEVELOPER         OnlineServiceType = 99
	OnlineServiceTypeWINDOWSPHONE             OnlineServiceType = 9
	OnlineServiceTypeXBOXLIVE                 OnlineServiceType = 1
	OnlineServiceTypeZENDESK                  OnlineServiceType = 23
)

// Defines values for PresenceStatus.
const (
	Authenticating PresenceStatus = "authenticating"
	Away           PresenceStatus = "away"
	Chat           PresenceStatus = "chat"
	Custom         PresenceStatus = "custom"
	Dnd            PresenceStatus = "dnd"
	Offline        PresenceStatus = "offline"
	Online         PresenceStatus = "online"
	Playing        PresenceStatus = "playing"
)

// Defines values for ReportingCategory.
const (
	AdultSexualHarassment          ReportingCategory = "Adult_Sexual_Harassment"
	AdultSexuallyExplicitContent   ReportingCategory = "Adult_Sexually_Explicit_Content"
	BullyingThreatsHarassment      ReportingCategory = "Bullying_Threats_Harassment"
	Fraud                          ReportingCategory = "Fraud"
	HacksMods                      ReportingCategory = "Hacks_Mods"
	HateSpeechDiscrimination       ReportingCategory = "Hate_Speech_Discrimination"
	MinorAbuseOrChildSexualContent ReportingCategory = "Minor_Abuse_or_Child_Sexual_Content"
	OtherIllegalContent            ReportingCategory = "Other_Illegal_Content"
	PlayerBoostingCheating         ReportingCategory = "Player_Boosting_Cheating"
	PrivacyDoxing                  ReportingCategory = "Privacy_Doxing"
	RealisticGoreOrViolence        ReportingCategory = "Realistic_Gore_or_Violence"
	Spam                           ReportingCategory = "Spam"
	SuicideOrSelfHarm              ReportingCategory = "Suicide_or_Self_Harm"
	TerrorismOrViolentExtremism    ReportingCategory = "Terrorism_or_Violent_Extremism"
)

// AbuseReturn Abuse report SNS message id
type AbuseReturn struct {
	MessageId string `json:"messageId"`
}

// AccountLinkDNA First party DNA account links.  This will be filtered by the current user's OST.
type AccountLinkDNA struct {
	// AccountId an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	AccountId *Dnaid `json:"accountId,omitempty"`

	// AccountType The type of Account according to DNA.  Notable is 3 for full.
	AccountType *AccountTypeDNA `json:"accountType,omitempty"`

	// FirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
	FirstPartyid *FirstPartyid           `json:"firstPartyid,omitempty"`
	LinkType     *AccountLinkDNALinkType `json:"linkType,omitempty"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType,omitempty"`
}

// AccountLinkDNALinkType defines model for AccountLinkDNA.LinkType.
type AccountLinkDNALinkType string

// AccountTypeDNA The type of Account according to DNA.  Notable is 3 for full.
type AccountTypeDNA uint32

// AccountsNext defines model for accountsNext.
type AccountsNext struct {
	Items  []SearchAccountResponse `json:"items"`
	Nextid *Nextid                 `json:"nextid"`
}

// ActiveGroupResponse defines model for activeGroupResponse.
type ActiveGroupResponse struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// CanRequestJoin in v2, this boolean is basically just a check on if the group is full.  it used to check group join request action but now any group can be joined using a password even as long as it's not full.
	CanRequestJoin bool `json:"canRequestJoin"`

	// CurrentMemberCount count of current members of the group
	CurrentMemberCount int `json:"currentMemberCount"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers MaxMembers `json:"maxMembers"`
}

// Approverid The approverid of the request.  "leader" can be used to use the group leader as default.
type Approverid = string

// AuthCode authCode to use for OAuth with First Party for session syncing
type AuthCode = string

// BlocklistResponse defines model for blocklistResponse.
type BlocklistResponse struct {
	Blockedid         Dnaid             `dynamodbav:"blockedid" json:"blockedid"`
	Created           Created           `dynamodbav:"created" json:"created"`
	Links             *Links            `dynamodbav:"links" json:"links"`
	Name              *Name             `dynamodbav:"name" json:"name"`
	OnlineServiceType OnlineServiceType `dynamodbav:"onlineServiceType" json:"onlineServiceType"`
	Productid         Productid         `dynamodbav:"productid" json:"productid"`
	Userid            Dnaid             `dynamodbav:"userid" json:"userid"`
}

// BlocklistsNext defines model for blocklistsNext.
type BlocklistsNext struct {
	Items  []BlocklistResponse `json:"items"`
	Nextid *Nextid             `json:"nextid"`
}

// CanCrossPlay Does the user sending this have cross play enabled on their local system
type CanCrossPlay = bool

// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
type CanMembersInvite = bool

// ChatMessage Chat message objet
type ChatMessage struct {
	// Body The body of the message supports rich text. The sender's client should tokenize the raw text into a messageBodySegment array. The recipient's client can then render a rich text message from the messageBodySegment array.
	Body []MessageBodySegement `json:"body"`

	// PostedTime timstamp of the event
	PostedTime Timestamp `json:"postedTime"`

	// ProductId the id of the product. validates as a dnaid.
	ProductId Productid `json:"productId"`

	// ShardId The shard id of a chat room.
	ShardId *string `json:"shardId,omitempty"`

	// SubjectId The user id of the user sending the message
	SubjectId string `json:"subjectId"`

	// TargetId The id of message receipent. For 1on1 this is the userid. For group this is the groupid.
	TargetId string `json:"targetId"`

	// Type The type of chat message. 1on1 is a direct message between two users. Group is a message sent to a group.
	Type ChatMessageType `json:"type"`
}

// ChatMessageType The type of chat message. 1on1 is a direct message between two users. Group is a message sent to a group.
type ChatMessageType string

// Clientid client id of mqtt connection for mqtt presence
type Clientid = string

// Created timestamp that this record was created.
type Created = time.Time

// CurrentEndorsementCount how many endorsements have been received since the last time the endorsement was reset.
type CurrentEndorsementCount = int

// CustomStatus defines model for customStatus.
type CustomStatus = string

// DiscoveryResponse defines model for discoveryResponse.
type DiscoveryResponse struct {
	// CanList if true, this discovery will be returned in the discovery list response
	CanList     *bool  `json:"canList"`
	Description string `json:"description"`

	// Id must be unique.  can be any string identifier. env/guid/etc.
	Id   string                 `json:"id"`
	Urls []DiscoveryURLResponse `json:"urls"`
}

// DiscoveryURLResponse defines model for discoveryURLResponse.
type DiscoveryURLResponse struct {
	// Fragment optional piece of uri
	Fragment *string `json:"fragment"`

	// Host optional piece of uri
	Host *string `json:"host"`

	// Path optional piece of uri
	Path *string `json:"path"`

	// Port optional piece of uri
	Port *string `json:"port"`

	// Query optional piece of uri
	Query *string `json:"query"`

	// Scheme optional piece of uri
	Scheme *string                  `json:"scheme"`
	Type   DiscoveryURLResponseType `json:"type"`

	// Url string url with port included with domain if needed
	Url string `json:"url"`
}

// DiscoveryURLResponseType defines model for DiscoveryURLResponse.Type.
type DiscoveryURLResponseType string

// DnaDisplayName 2k display name with optional 5 digit discrimnating hash
type DnaDisplayName = string

// Dnaid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type Dnaid = string

// Dob The date of birth for the specified user. mm/dd/yyyy format. since this is PII, it is never returned.  do not depend on this field's value.
type Dob = string

// Email email of user.  since this is PII, it is rarely returned.  do not depend on this field's value.
type Email = string

// EmptyObject Empty object
type EmptyObject = map[string]interface{}

// EndorsementListResponse defines model for endorsementListResponse.
type EndorsementListResponse struct {
	Items []EndorsementResponse `json:"items"`
}

// EndorsementName the name of the endorsement to be acted upon
type EndorsementName = string

// EndorsementResponse defines model for endorsementResponse.
type EndorsementResponse struct {
	CurrentEndorsementCount CurrentEndorsementCount `dynamodbav:"currentEndorsementCount" json:"currentEndorsementCount"`
	EndorsementName         EndorsementName         `dynamodbav:"endorsementName" json:"endorsementName"`
	IsPositive              IsPositive              `dynamodbav:"isPositive" json:"isPositive"`
	IsPrivate               IsPrivate               `dynamodbav:"isPrivate" json:"isPrivate"`
	TotalEndorsementCount   TotalEndorsementCount   `dynamodbav:"totalEndorsementCount" json:"totalEndorsementCount"`
}

// Error defines model for error.
type Error struct {
	// Code HTTP error code
	Code uint32 `json:"code"`

	// ErrorCode error code.  list of errors on docsite.
	ErrorCode ErrorCode `json:"errorCode"`

	// Message error message
	Message string `json:"message"`

	// Stack Stack trace of the error (will be only returned in dev environment)
	Stack *string `json:"stack,omitempty"`
}

// ErrorCode error code.  list of errors on docsite.
type ErrorCode = uint32

// FirstPartySessionid The First Party Session Id of the specified.
type FirstPartySessionid = string

// FirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
type FirstPartyid = string

// FriendResponse defines model for friendResponse.
type FriendResponse struct {
	Friendid   Dnaid              `dynamodbav:"friendid" json:"friendid"`
	Invitee    Dnaid              `dynamodbav:"invitee" json:"invitee"`
	InviterOST *OnlineServiceType `dynamodbav:"inviterOST" json:"inviterOST"`
	Links      *Links             `dynamodbav:"links" json:"links"`

	// Message message to include with friend request
	Message  *string             `dynamodbav:"message" json:"message"`
	Name     *DnaDisplayName     `dynamodbav:"name" json:"name"`
	Presence *[]PresenceResponse `dynamodbav:"presence" json:"presence"`
	Status   FriendStatus        `dynamodbav:"status" json:"status"`
	Userid   Dnaid               `dynamodbav:"userid" json:"userid"`
	Viewed   *bool               `dynamodbav:"viewed" json:"viewed"`
}

// FriendStatus defines model for friendStatus.
type FriendStatus string

// FriendsNext defines model for friendsNext.
type FriendsNext struct {
	Items  []FriendResponse `json:"items"`
	Nextid *Nextid          `json:"nextid"`
}

// GameData free form field for games to send additional presence information for their internal use.
type GameData = string

// GameName pre-localized game name.
type GameName = string

// GetInviteResponse schema for response for a user's invites
type GetInviteResponse struct {
	// Approverid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Approverid   Dnaid          `json:"approverid"`
	CanCrossPlay *bool          `json:"canCrossPlay"`
	DisplayName  DnaDisplayName `json:"displayName"`
	FirstPartyid *FirstPartyid  `json:"firstPartyid"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid            Groupid             `json:"groupid"`
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite"`

	// Memberid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Memberid Dnaid `json:"memberid"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`
}

// GroupMemberResponse defines model for groupMemberResponse.
type GroupMemberResponse struct {
	Links *Links `json:"links"`
	Meta  *Meta  `json:"meta"`

	// MetaLastUpdated Represents the time when an update request is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
	MetaLastUpdated *uint64           `json:"metaLastUpdated"`
	Name            *Name             `json:"name"`
	Presence        *PresenceResponse `json:"presence"`

	// Productid the id of the product. validates as a dnaid.
	Productid Productid `json:"productid"`

	// Role the role of a group member
	Role GroupMemberRole `json:"role"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`
}

// GroupMemberRole the role of a group member
type GroupMemberRole string

// GroupResponse Productid only set as required due to codegen issues.
type GroupResponse struct {
	CanCrossPlay     *CanCrossPlay     `json:"canCrossPlay"`
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite"`
	Created          *Created          `json:"created"`

	// FirstPartySessionid The First Party Session Id of the specified.
	FirstPartySessionid FirstPartySessionid `json:"firstPartySessionid"`

	// GroupCompositionId incrementing counter for all group member changes.  requested for telemetry purposes.  similar to a version field.
	GroupCompositionId *int64 `json:"groupCompositionId"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers         MaxMembers             `json:"maxMembers"`
	Members            *[]GroupMemberResponse `json:"members,omitempty"`
	MembershipRequests *[]MembershipRequest   `json:"membershipRequests"`

	// Meta free form map (json format) to store metadata for this object.
	Meta              *Meta              `json:"meta"`
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType"`
	Password          *Password          `json:"password"`

	// Productid the id of the product. validates as a dnaid.
	Productid Productid `json:"productid"`
}

// Groupid lexigraphicaly sorted unique identifier
type Groupid = Ulid

// GroupsNext defines model for groupsNext.
type GroupsNext struct {
	Items  []GroupResponse `json:"items"`
	Nextid *Nextid         `json:"nextid"`
}

// HealthResponse defines model for healthResponse.
type HealthResponse struct {
	Generated     *string                 `json:"generated"`
	Name          *string                 `json:"name"`
	OverallStatus *string                 `json:"overall-status"`
	Services      *map[string]interface{} `json:"services"`
	Version       *string                 `json:"version"`
}

// ImportBlocklistResponse defines model for importBlocklistResponse.
type ImportBlocklistResponse struct {
	Blockedids []string `json:"blockedids"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`
}

// IncrementValue how many endorsements
type IncrementValue = int

// InvitesNext defines model for invitesNext.
type InvitesNext struct {
	Items  []GetInviteResponse `json:"items"`
	Nextid *Nextid             `json:"nextid"`
}

// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
type IsFirstPartyInvite = bool

// IsPositive is this custom endorsement considered to be positive (or is it something like a downvote).  this is a permanent value for the endorsement and is not meant to reduce the increment.  the value of the first instance of this endorsement is preserved.  these "negative" values only increase and are meant to be used by game teams if they want to formulate a behavior score or something similar.
type IsPositive = bool

// IsPrivate do not allow this endorsement be queried by users other than self.  this can be used to only allow other users to query positive endorsements and not see "downvotes"  but still store them to use for behavior score computations.  this is a permanent value of the endorsement and cannot be changed after the initial value is set.
type IsPrivate = bool

// JoinContext Context used to join a game session
type JoinContext struct {
	LaunchGameArgs string `json:"launchGameArgs"`
	Sessionid      string `json:"sessionid"`
}

// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
type JoinRequestAction string

// JoinRequestResponse schema for a join request response
type JoinRequestResponse struct {
	// Approverid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Approverid  Dnaid          `json:"approverid"`
	DisplayName DnaDisplayName `json:"displayName"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// Memberid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Memberid Dnaid `json:"memberid"`
}

// Links Linked accounts. Filtered to current OST.
type Links = []AccountLinkDNA

// Locale the locale of the user.
type Locale = string

// LoginResponse Successful authentication response
type LoginResponse struct {
	// AccessToken The access token
	AccessToken string `json:"accessToken"`

	// AccessTokenExpiresIn The number of seconds until the access token expires
	AccessTokenExpiresIn uint32 `json:"accessTokenExpiresIn"`

	// AccountId an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	AccountId Dnaid `json:"accountId"`

	// AccountType The type of the account that was authenticated
	AccountType uint32 `json:"accountType"`

	// RefreshToken The refresh token
	RefreshToken string `json:"refreshToken"`

	// RefreshTokenExpiresIn The number of seconds until the refresh token expires
	RefreshTokenExpiresIn uint32 `json:"refreshTokenExpiresIn"`
}

// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
type MaxMembers = int

// MembersNext defines model for membersNext.
type MembersNext struct {
	Items  []GroupMemberResponse `json:"items"`
	Nextid *Nextid               `json:"nextid"`
}

// MembershipRequest this schema defines a membership request.  which can be either a join request or an invite using the status field as a determiner.
type MembershipRequest struct {
	// Approverid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Approverid Dnaid `json:"approverid"`

	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay *CanCrossPlay `json:"canCrossPlay,omitempty"`

	// FirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
	FirstPartyid *FirstPartyid `json:"firstPartyid,omitempty"`

	// FromDisplayName the display name of the user that this request is from.  provided to display in the UI of the invite.
	FromDisplayName *DnaDisplayName `json:"fromDisplayName,omitempty"`

	// Groupid the id of the group.  validates ULID pattern.
	Groupid Groupid `json:"groupid"`

	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// Memberid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Memberid Dnaid `json:"memberid"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType,omitempty"`

	// Productid the id of the product. validates as a dnaid.
	Productid *Productid `json:"productid,omitempty"`

	// Status The membership status of the invite or request to join state. * requested - a request to join flow initiated * approved - group join request has been approved * rejected - group join request has been rejected * invited - the user has been invited to the group * accepted - the user has accepted the invite to the group * declined - the invite has been declined * revoked - the invite has been revoked
	Status MembershipStatus `json:"status"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`
}

// MembershipStatus The membership status of the invite or request to join state. * requested - a request to join flow initiated * approved - group join request has been approved * rejected - group join request has been rejected * invited - the user has been invited to the group * accepted - the user has accepted the invite to the group * declined - the invite has been declined * revoked - the invite has been revoked
type MembershipStatus string

// MessageBodySegement Message body segement
type MessageBodySegement struct {
	// ObjectDescription The description of the object being linked to. It's required for deepLink type.
	ObjectDescription *string `json:"objectDescription,omitempty"`

	// ObjectId The id of the object being linked to. It's required for deepLink type.
	ObjectId *string `json:"objectId,omitempty"`

	// ObjectType The type of the object being linked to. It's required for deepLink type.
	ObjectType *string `json:"objectType,omitempty"`

	// Text The text to be displayed in the message.
	Text string                  `json:"text"`
	Type MessageBodySegementType `json:"type"`
}

// MessageBodySegementType defines model for MessageBodySegement.Type.
type MessageBodySegementType string

// Meta free form map (json format) to store metadata for this object.
type Meta = map[string]interface{}

// Name The name associated with this user. If friend name is empty string, it is possible that it is not a full 2K account.
type Name = string

// Nextid next value to be used for requesting next page
type Nextid = string

// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
type OnlineServiceType int

// Password password for the join request
type Password = string

// PlayedPlayersNext defines model for playedPlayersNext.
type PlayedPlayersNext struct {
	Items  []RecentlyPlayedUserResponse `json:"items"`
	Nextid *Nextid                      `json:"nextid"`
}

// PresenceNext defines model for presenceNext.
type PresenceNext struct {
	Items  []PresenceResponse `json:"items"`
	Nextid *Nextid            `json:"nextid"`
}

// PresenceResponse a presence record
type PresenceResponse struct {
	ActiveGroup *ActiveGroupResponse `json:"activeGroup"`

	// ActiveSessionid id of active login session.
	ActiveSessionid Dnaid         `json:"activeSessionid"`
	Clientid        *Clientid     `json:"clientid"`
	CustomStatus    *CustomStatus `json:"customStatus"`
	GameData        *GameData     `json:"gameData"`

	// GameName pre-localized game name.
	GameName    GameName     `json:"gameName"`
	JoinContext *JoinContext `json:"joinContext"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// OnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// Platformid DNA only. at the time the presence is set, if the token has a pai claim.  this will be the sub claim, which should be the platformid.  we do not guarantee this value will be returned since full account tokens will not have it.
	Platformid *Dnaid `json:"platformid"`

	// Priority Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
	Priority Priority `json:"priority"`

	// Productid the id of the product. validates as a dnaid.
	Productid    Productid     `json:"productid"`
	RichPresence *RichPresence `json:"richPresence"`

	// Status the status of the presence record for the user
	Status PresenceStatus `json:"status"`

	// Timestamp timstamp of the event
	Timestamp Timestamp `json:"timestamp"`

	// Ttl How long in seconds before this presence will be considered offline if no presence Heartbeat is made.   | This is an optional value for those who are not using our MQTT.  People using our MQTT will have this functionality via our mqtt plugin.  Timeout set to 5 minutes for auto drop from group.
	Ttl *Ttl `json:"ttl"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`
}

// PresenceStatus the status of the presence record for the user
type PresenceStatus string

// Priority Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
type Priority = int

// Productid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type Productid = Dnaid

// RecentlyPlayedUserResponse a recently played user record
type RecentlyPlayedUserResponse struct {
	// Context Used by the game to store any context for the recenly played user.
	Context        *map[string]interface{}   `json:"context"`
	ForUserid      *Dnaid                    `json:"forUserid"`
	LastPlayed     *Timestamp                `json:"lastPlayed"`
	Links          *Links                    `json:"links"`
	Name           *Name                     `json:"name"`
	Productid      *Productid                `json:"productid"`
	SimplePresence *[]SimplePresenceResponse `json:"simplePresence"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`

	// Weight Sorting is done by last played unix time stamp in milliseconds + weight descending.
	Weight *int `json:"weight"`
}

// RecentlyPlayedUsers schema for sending recently played users in requests
type RecentlyPlayedUsers struct {
	// Context Used by the game to store any context for the recenly played user.
	Context *map[string]interface{} `json:"context,omitempty"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`

	// Weight Sorting is done by last played unix time stamp in milliseconds + weight descending.  Weight can be used to make sorting not strictly by datetime.
	Weight *int `json:"weight,omitempty"`
}

// ReportingCategory Reporting category
type ReportingCategory string

// RichPresence string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
type RichPresence = string

// Search2kUserResponse defines model for search2kUserResponse.
type Search2kUserResponse struct {
	Items []TwoKSearch `json:"items"`
}

// SearchAccountResponse schema for DNA search results
type SearchAccountResponse struct {
	// DisplayName The display name
	DisplayName *string `json:"displayName"`
	Dob         *Dob    `json:"dob"`
	Email       *Email  `json:"email"`

	// FirstPartyAlias The account first Party Alias
	FirstPartyAlias *string       `json:"firstPartyAlias"`
	FirstPartyId    *FirstPartyid `json:"firstPartyId"`

	// Id The account Id
	Id                *string            `json:"id"`
	Links             *Links             `json:"links"`
	Locale            *Locale            `json:"locale"`
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType"`
	ParentAccountId   *Dnaid             `json:"parentAccountId"`
	Type              *AccountTypeDNA    `json:"type"`
}

// SetFriendResponse defines model for setFriendResponse.
type SetFriendResponse struct {
	Status FriendStatus `json:"status"`
}

// SetPlayedRequest request schema for set recetnly played users
type SetPlayedRequest struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`

	// Users Array of recently played users
	Users []RecentlyPlayedUsers `json:"users"`
}

// SimplePresenceResponse very basic presence information for possibly showing some limited presence information in certain lists like search where full presence details would definitely not be shared. game teams can use their discretion on whether they would like to display any of this information in their UI in places that it's offered.  currently only user search results and recently played.
type SimplePresenceResponse struct {
	// GameName Possibly pull this from somewhere for localization in the future. Empty string is possible.
	GameName          *string            `json:"gameName"`
	OnlineServiceType *OnlineServiceType `json:"onlineServiceType"`

	// Status the status of the presence record for the user
	Status PresenceStatus `json:"status"`

	// Userid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	Userid Dnaid `json:"userid"`
}

// TelemetryMetaData Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
type TelemetryMetaData = map[string]interface{}

// Timestamp timstamp of the event
type Timestamp = time.Time

// TotalEndorsementCount how many endorsements have been received since the first time this endorsement was received.  does not get reset when the endorsement is reset.
type TotalEndorsementCount = int

// Ttl time in seconds until the object expires
type Ttl = int64

// TwoKSearch defines model for twoKSearch.
type TwoKSearch struct {
	// Links Linked accounts. Filtered to current OST.
	Links          *[]AccountLinkDNA         `json:"links"`
	Name           *string                   `json:"name"`
	SimplePresence *[]SimplePresenceResponse `json:"simplePresence"`
	Userid         string                    `json:"userid"`
}

// Ulid lexigraphicaly sorted unique identifier
type Ulid = string

// UpdateGroupMemberRequest request schema for update group member
type UpdateGroupMemberRequest struct {
	// Role the role of a group member
	Role GroupMemberRole `json:"role"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// UpdateGroupRequest request schema for update group
type UpdateGroupRequest struct {
	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction *JoinRequestAction `json:"joinRequestAction,omitempty"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// UserProfileResponse a user's profile record.
type UserProfileResponse struct {
	// AgeGroup This is currently, only available for DNA accounts. The age group of the specified Platform Account.
	// List of types for reference:
	// For United States: * 0: Unknown * 1: 0 ↔ 12 (Child) * 2: 13 ↔ 17 (Teen) * 3: 18 ↔ 24 (Adult) * 4: 25 ↔ 34 (Adult) * 5: 35 ↔ N (Adult) * 6: 13 ↔ N (Adult)
	// For Europe: * 7: 0 ↔ 16 (Teen) * 8: 16 ↔ 24 (Adult) * 9: 24 ↔ N (Adult)
	AgeGroup    *int            `dynamodbav:"ageGroup" json:"ageGroup"`
	Created     *Created        `dynamodbav:"created" json:"created"`
	DisplayName *DnaDisplayName `dynamodbav:"displayName" json:"displayName"`
	Dob         *Dob            `dynamodbav:"dob" json:"dob"`
	Email       *Email          `dynamodbav:"email" json:"email"`

	// LastLogin Placeholder. Not currently used.
	LastLogin         *Timestamp         `dynamodbav:"lastLogin" json:"lastLogin"`
	Links             *Links             `dynamodbav:"links" json:"links"`
	Locale            *Locale            `dynamodbav:"locale" json:"locale"`
	OnlineServiceType *OnlineServiceType `dynamodbav:"onlineServiceType" json:"onlineServiceType"`
	ParentAccountId   *Dnaid             `dynamodbav:"parentAccountId" json:"parentAccountId"`
	Presence          *PresenceResponse  `dynamodbav:"presence" json:"presence"`
	Userid            Dnaid              `dynamodbav:"userid" json:"userid"`
}

// Userid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
type Userid = string

// VersionResponse defines model for versionResponse.
type VersionResponse struct {
	BuildDate string `json:"buildDate"`
	GitHash   string `json:"gitHash"`
	Version   string `json:"version"`
}

// DiscoveryPid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type DiscoveryPid = Dnaid

// Discoveryid The id of the discovery set desired.  '?id=' will return all sets for given product.
type Discoveryid = string

// DisplayName 2k display name with optional 5 digit discrimnating hash
type DisplayName = DnaDisplayName

// Healthid The id of the identity provider desired. No parameter will return health check without identity info.
type Healthid = string

// Limit defines model for limit.
type Limit = int

// Next defines model for next.
type Next = string

// PApproverid The approverid of the request.  "leader" can be used to use the group leader as default.
type PApproverid = Approverid

// PFirstPartyApproverid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
type PFirstPartyApproverid = FirstPartyid

// PFirstPartyid The First Party Id of the specified Platform Account. For Device Accounts, the Device Id will be displayed. no real validation because different first parties have vastly different ids.
type PFirstPartyid = FirstPartyid

// PFriendid an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
type PFriendid = Dnaid

// PGroupid the id of the group.  validates ULID pattern.
type PGroupid = Groupid

// PMemberid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
type PMemberid = Userid

// POnlineServiceType basicaly translates to platform ecosystem. i.e. all of xbox, all of playstation, nintendo, etc.
type POnlineServiceType = OnlineServiceType

// PRoomid the id of the group.  validates ULID pattern.
type PRoomid = Groupid

// PSessionid The First Party Session Id of the specified.
type PSessionid = FirstPartySessionid

// PUserid The userid can be a DNA id or a first party id depending.  No real validation because first party ids have different patterns.
type PUserid = Userid

// Status defines model for status.
type Status = FriendStatus

// N200empty Empty object
type N200empty = EmptyObject

// N400 defines model for 400.
type N400 = Error

// N401 defines model for 401.
type N401 = Error

// N403 defines model for 403.
type N403 = Error

// N404 defines model for 404.
type N404 = Error

// N409 defines model for 409.
type N409 = Error

// N429 defines model for 429.
type N429 = Error

// N500 defines model for 500.
type N500 = Error

// AcceptInviteBySessionRequestBody schema for accepting invites by session information
type AcceptInviteBySessionRequestBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// AcceptInviteRequestBody schema for accepting invites
type AcceptInviteRequestBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// AddBlocklistRequestBody defines model for addBlocklistRequestBody.
type AddBlocklistRequestBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ApproveJoinRequestBody schema for approving join requests
type ApproveJoinRequestBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// CreateGroupRequestBody defines model for createGroupRequestBody.
type CreateGroupRequestBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay *CanCrossPlay `json:"canCrossPlay,omitempty"`

	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ImportBlocklistRequestBody defines model for importBlocklistRequestBody.
type ImportBlocklistRequestBody struct {
	// IsFirstParty optional flag to add the platform blocklist users to the main blocklist.
	IsFirstParty *bool `json:"isFirstParty,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Userids an array of user ids to add to blocklist.  set isFirstParty = true if adding first party ids.  must all be same ost as requestor.
	Userids []Userid `json:"userids"`
}

// IncrementEndorsementRequestBody schema for accepting invites by session information
type IncrementEndorsementRequestBody struct {
	// EndorsementName only include a name if you would like to increment a custom endorsement.  if no endorsement name is included, the count will be incremented to the default endorsement/commend bucket
	EndorsementName *EndorsementName `json:"endorsementName,omitempty"`

	// IncrementValue how many endorsements
	IncrementValue IncrementValue `json:"incrementValue"`

	// IsPositive is this custom endorsement considered to be positive (or is it something like a downvote).  this is a permanent value for the endorsement and is not meant to reduce the increment.  the value of the first instance of this endorsement is preserved.  these "negative" values only increase and are meant to be used by game teams if they want to formulate a behavior score or something similar.
	IsPositive *IsPositive `json:"isPositive,omitempty"`

	// IsPrivate do not allow this endorsement be queried by users other than self.  this can be used to only allow other users to query positive endorsements and not see "downvotes"  but still store them to use for behavior score computations.  this is a permanent value of the endorsement and cannot be changed after the initial value is set.
	IsPrivate *IsPrivate `json:"isPrivate,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// LoginRequestBody defines model for loginRequestBody.
type LoginRequestBody struct {
	// AppId an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	AppId *Dnaid `json:"appId,omitempty"`

	// Email email of user.  since this is PII, it is rarely returned.  do not depend on this field's value.
	Email Email `json:"email"`

	// Locale the locale of the user.
	Locale Locale `dynamodbav:"locale" json:"locale"`

	// Password password for the join request
	Password Password `json:"password"`
}

// LogoutRequestBody defines model for logoutRequestBody.
type LogoutRequestBody struct {
	// RefreshToken Refresh token
	RefreshToken *string `json:"refreshToken,omitempty"`
}

// RefreshRequestBody defines model for refreshRequestBody.
type RefreshRequestBody struct {
	// Locale the locale of the user.
	Locale Locale `dynamodbav:"locale" json:"locale"`

	// RefreshToken Refresh token
	RefreshToken string `json:"refreshToken"`
}

// RequestJoinRequestBody schema for sending join requests
type RequestJoinRequestBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// SendChatMessageRequestBody defines model for sendChatMessageRequestBody.
type SendChatMessageRequestBody struct {
	// Message Chat message objet
	Message ChatMessage `json:"message"`
}

// SendControlMessageRequestBody defines model for sendControlMessageRequestBody.
type SendControlMessageRequestBody struct {
	// Event a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
	Event   *string `json:"event,omitempty"`
	Payload string  `json:"payload"`

	// Senderid sender should be a dna userid or a dna server instance id.
	Senderid *Dnaid `json:"senderid,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Timestamp timstamp of the event
	Timestamp *Timestamp `json:"timestamp,omitempty"`
}

// SendInviteRequestBody schema for sending an invite
type SendInviteRequestBody struct {
	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`
}

// SetActiveGroupRequestBody defines model for setActiveGroupRequestBody.
type SetActiveGroupRequestBody struct {
	// ActiveGroupid the id of the group.  validates ULID pattern.
	ActiveGroupid Groupid `json:"activeGroupid"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// SetFriendViewedRequestBody defines model for setFriendViewedRequestBody.
type SetFriendViewedRequestBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Viewed   *bool              `json:"viewed,omitempty"`
}

// SetFriendsRequestBody defines model for setFriendsRequestBody.
type SetFriendsRequestBody struct {
	Message *string `json:"message,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// SetPlayedRequestBody request schema for set recetnly played users
type SetPlayedRequestBody = SetPlayedRequest

// SetPresenceRequestBody defines model for setPresenceRequestBody.
type SetPresenceRequestBody struct {
	CustomStatus *CustomStatus `json:"customStatus,omitempty"`

	// GameData free form field for games to send additional presence information for their internal use.
	GameData *GameData `json:"gameData,omitempty"`

	// GameName pre-localized game name.
	GameName GameName `json:"gameName"`

	// JoinContext Context used to join a game session
	JoinContext *JoinContext `json:"joinContext,omitempty"`

	// Meta Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
	Meta *Meta `json:"meta,omitempty"`

	// RichPresence string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
	RichPresence *RichPresence `json:"richPresence,omitempty"`

	// Status the status of the presence record for the user
	Status PresenceStatus `json:"status"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Ttl How long in seconds before this connection will be considered offline if no futher presence update is made.   | NOTE - this value MUST be at MINIMUM several seconds longer than the rate of your heartbeat timer, or your presence will always expire. | We recommend having your keep alive allow for at least 3 heartbeats to occur in the window (espepecially on mobile), | but it's entirely up to you how much of a grace period you want.  And for how a long droppped connection can be recovered. | The minimum is set to 35s because our plugin heartbeat is 30s.  We do NOT recommend using 35s.  Default is 5m.
	Ttl *Ttl `json:"ttl,omitempty"`
}

// SetReportRequestBody defines model for setReportRequestBody.
type SetReportRequestBody struct {
	// GameSessionInfo The additional information for a report in a key-value pair format.
	GameSessionInfo *map[string]interface{} `json:"gameSessionInfo,omitempty"`
	Os              *string                 `json:"os,omitempty"`
	Platform        string                  `json:"platform"`

	// ReportMessage The user's specified report message. Limit the size to 5 KB.
	ReportMessage string `json:"reportMessage"`

	// ReportingCategory Reporting category
	ReportingCategory ReportingCategory `json:"reportingCategory"`

	// ReportingContentType The type of reporting content.
	ReportingContentType *string `json:"reportingContentType,omitempty"`

	// ReportingUserId The reporting user's full account id.
	ReportingUserId string `json:"reportingUserId"`

	// ReportingUserLocale The reporting user's locale.
	ReportingUserLocale string `json:"reportingUserLocale"`

	// SubjectTitle The subject title of a report.
	SubjectTitle string `json:"subjectTitle"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// VersionNumber The version number of a game.
	VersionNumber *string `json:"versionNumber,omitempty"`
}

// UpdateGroupMemberMetaRequestBody defines model for updateGroupMemberMetaRequestBody.
type UpdateGroupMemberMetaRequestBody struct {
	// Meta A group member's latest metadata.
	Meta map[string]interface{} `json:"meta"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Timestamp Represents the client time when the meta update is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
	Timestamp uint64 `json:"timestamp"`
}

// UpdateGroupMemberRequestBody request schema for update group member
type UpdateGroupMemberRequestBody = UpdateGroupMemberRequest

// UpdateGroupRequestBody request schema for update group
type UpdateGroupRequestBody = UpdateGroupRequest

// UpsertSessionAuthRequestBody send auth code for user/ost combo for session syncing auth that requires user first party token
type UpsertSessionAuthRequestBody struct {
	// AuthCode authCode to use for OAuth with First Party for session syncing
	AuthCode AuthCode `json:"authCode"`
}

// LoginJSONBody defines parameters for Login.
type LoginJSONBody struct {
	// AppId an id formatted in the way of a dnaid which is a lowercase uuid with no dashes and all lowercase, but we accept dashes in formatting just in case
	AppId *Dnaid `json:"appId,omitempty"`

	// Email email of user.  since this is PII, it is rarely returned.  do not depend on this field's value.
	Email Email `json:"email"`

	// Locale the locale of the user.
	Locale Locale `dynamodbav:"locale" json:"locale"`

	// Password password for the join request
	Password Password `json:"password"`
}

// LogoutJSONBody defines parameters for Logout.
type LogoutJSONBody struct {
	// RefreshToken Refresh token
	RefreshToken *string `json:"refreshToken,omitempty"`
}

// RefreshTokenJSONBody defines parameters for RefreshToken.
type RefreshTokenJSONBody struct {
	// Locale the locale of the user.
	Locale Locale `dynamodbav:"locale" json:"locale"`

	// RefreshToken Refresh token
	RefreshToken string `json:"refreshToken"`
}

// SendChatMessageJSONBody defines parameters for SendChatMessage.
type SendChatMessageJSONBody struct {
	// Message Chat message objet
	Message ChatMessage `json:"message"`
}

// GetDiscoveryParams defines parameters for GetDiscovery.
type GetDiscoveryParams struct {
	// DiscoveryPid productid to filter by.
	DiscoveryPid *DiscoveryPid `form:"discoveryPid,omitempty" json:"discoveryPid,omitempty"`

	// Discoveryid Get specific discovery endpoint based on id.  Must also send Authorization header
	Discoveryid *Discoveryid `form:"discoveryid,omitempty" json:"discoveryid,omitempty"`
}

// IncrementEndorsementJSONBody defines parameters for IncrementEndorsement.
type IncrementEndorsementJSONBody struct {
	// EndorsementName only include a name if you would like to increment a custom endorsement.  if no endorsement name is included, the count will be incremented to the default endorsement/commend bucket
	EndorsementName *EndorsementName `json:"endorsementName,omitempty"`

	// IncrementValue how many endorsements
	IncrementValue IncrementValue `json:"incrementValue"`

	// IsPositive is this custom endorsement considered to be positive (or is it something like a downvote).  this is a permanent value for the endorsement and is not meant to reduce the increment.  the value of the first instance of this endorsement is preserved.  these "negative" values only increase and are meant to be used by game teams if they want to formulate a behavior score or something similar.
	IsPositive *IsPositive `json:"isPositive,omitempty"`

	// IsPrivate do not allow this endorsement be queried by users other than self.  this can be used to only allow other users to query positive endorsements and not see "downvotes"  but still store them to use for behavior score computations.  this is a permanent value of the endorsement and cannot be changed after the initial value is set.
	IsPrivate *IsPrivate `json:"isPrivate,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ListFriendsParams defines parameters for ListFriends.
type ListFriendsParams struct {
	Status *Status `form:"status,omitempty" json:"status,omitempty"`

	// Limit How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
	Limit *Limit `form:"limit,omitempty" json:"limit,omitempty"`

	// Next Next value used for pagination.  Empty value accepted for first page.
	Next *Next `form:"next,omitempty" json:"next,omitempty"`
}

// ImportPlatformFriendsParams defines parameters for ImportPlatformFriends.
type ImportPlatformFriendsParams struct {
	// Id Comma separated platform id to be queried (limit 100)
	Id string `form:"id" json:"id"`
}

// UpdateFriendStatusJSONBody defines parameters for UpdateFriendStatus.
type UpdateFriendStatusJSONBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Viewed   *bool              `json:"viewed,omitempty"`
}

// MakeFriendJSONBody defines parameters for MakeFriend.
type MakeFriendJSONBody struct {
	Message *string `json:"message,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// GetGroupsParams defines parameters for GetGroups.
type GetGroupsParams struct {
	// Limit How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
	Limit *Limit `form:"limit,omitempty" json:"limit,omitempty"`

	// Next Next value used for pagination.  Empty value accepted for first page.
	Next *Next `form:"next,omitempty" json:"next,omitempty"`
}

// CreateGroupJSONBody defines parameters for CreateGroup.
type CreateGroupJSONBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay *CanCrossPlay `json:"canCrossPlay,omitempty"`

	// CanMembersInvite Should all members be allowed to invite other users, not just the leader?
	CanMembersInvite *CanMembersInvite `json:"canMembersInvite,omitempty"`

	// JoinRequestAction The group join request type. * manual - A member of the group is prompted to accept the user (usually the leader). * auto-approve - Any authenticated user within the product can join if they know the group id. * auto-reject - Only the group leader (or members if permitted) can invite people to the group.  All request to joins will be rejected.
	JoinRequestAction JoinRequestAction `json:"joinRequestAction"`

	// MaxMembers default value is 2. max value is 100.  this cannot be reduced lower than the number of current group members.
	MaxMembers *MaxMembers `json:"maxMembers,omitempty"`

	// Meta free form map (json format) to store metadata for this object.
	Meta *Meta `json:"meta"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// SendControlMessageJSONBody defines parameters for SendControlMessage.
type SendControlMessageJSONBody struct {
	// Event a field that can optionally be used to differentiate the control message so that the payload can be marshalled/deserialized accordingly.
	Event   *string `json:"event,omitempty"`
	Payload string  `json:"payload"`

	// Senderid sender should be a dna userid or a dna server instance id.
	Senderid *Dnaid `json:"senderid,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Timestamp timstamp of the event
	Timestamp *Timestamp `json:"timestamp,omitempty"`
}

// KickMemberFromGroupParams defines parameters for KickMemberFromGroup.
type KickMemberFromGroupParams struct {
	// Reason reason for removing a group member
	Reason *string `form:"reason,omitempty" json:"reason,omitempty"`
}

// UpdateGroupMemberMetaJSONBody defines parameters for UpdateGroupMemberMeta.
type UpdateGroupMemberMetaJSONBody struct {
	// Meta A group member's latest metadata.
	Meta map[string]interface{} `json:"meta"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Timestamp Represents the client time when the meta update is submitted as a UNIX Timestamp in Milliseconds.  There will be some sanity checking on this timestamp so please make sure you use MILLIseconds since Epoch.
	Timestamp uint64 `json:"timestamp"`
}

// GetHealthParams defines parameters for GetHealth.
type GetHealthParams struct {
	// Id Get specific identity provider for health check based on id.
	Id *Healthid `form:"id,omitempty" json:"id,omitempty"`
}

// AcceptInviteJSONBody defines parameters for AcceptInvite.
type AcceptInviteJSONBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// SendInviteJSONBody defines parameters for SendInvite.
type SendInviteJSONBody struct {
	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
	Ttl      *Ttl               `json:"ttl,omitempty"`
}

// GetInvitesParams defines parameters for GetInvites.
type GetInvitesParams struct {
	// Limit How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
	Limit *Limit `form:"limit,omitempty" json:"limit,omitempty"`

	// Next Next value used for pagination.  Empty value accepted for first page.
	Next *Next `form:"next,omitempty" json:"next,omitempty"`
}

// SendJoinRequestJSONBody defines parameters for SendJoinRequest.
type SendJoinRequestJSONBody struct {
	// CanCrossPlay Does the user sending this have cross play enabled on their local system
	CanCrossPlay CanCrossPlay `json:"canCrossPlay"`

	// Password password for the join request
	Password *Password `json:"password,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ApproveJoinRequestJSONBody defines parameters for ApproveJoinRequest.
type ApproveJoinRequestJSONBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// AcceptInviteByFirstPartySessionIdsJSONBody defines parameters for AcceptInviteByFirstPartySessionIds.
type AcceptInviteByFirstPartySessionIdsJSONBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// UpsertSessionAuthJSONBody defines parameters for UpsertSessionAuth.
type UpsertSessionAuthJSONBody struct {
	// AuthCode authCode to use for OAuth with First Party for session syncing
	AuthCode AuthCode `json:"authCode"`
}

// Search2KUsersParams defines parameters for Search2KUsers.
type Search2KUsersParams struct {
	// DisplayName The full account DNA displayname to be searched for.
	DisplayName DisplayName `form:"displayName" json:"displayName"`
}

// GetBlocklistParams defines parameters for GetBlocklist.
type GetBlocklistParams struct {
	// Limit How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
	Limit *Limit `form:"limit,omitempty" json:"limit,omitempty"`

	// Next Next value used for pagination.  Empty value accepted for first page.
	Next *Next `form:"next,omitempty" json:"next,omitempty"`
}

// ImportBlocklistJSONBody defines parameters for ImportBlocklist.
type ImportBlocklistJSONBody struct {
	// IsFirstParty optional flag to add the platform blocklist users to the main blocklist.
	IsFirstParty *bool `json:"isFirstParty,omitempty"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Userids an array of user ids to add to blocklist.  set isFirstParty = true if adding first party ids.  must all be same ost as requestor.
	Userids []Userid `json:"userids"`
}

// AddBlocklistJSONBody defines parameters for AddBlocklist.
type AddBlocklistJSONBody struct {
	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// GetRecentlyPlayedParams defines parameters for GetRecentlyPlayed.
type GetRecentlyPlayedParams struct {
	// Limit How many items to return at one time.  Omit this parameter entirely or set limit=0 to get all records.  Max items per page = 100 if paginating.
	Limit *Limit `form:"limit,omitempty" json:"limit,omitempty"`

	// Next Next value used for pagination.  Empty value accepted for first page.
	Next *Next `form:"next,omitempty" json:"next,omitempty"`
}

// SetUserPresenceJSONBody defines parameters for SetUserPresence.
type SetUserPresenceJSONBody struct {
	CustomStatus *CustomStatus `json:"customStatus,omitempty"`

	// GameData free form field for games to send additional presence information for their internal use.
	GameData *GameData `json:"gameData,omitempty"`

	// GameName pre-localized game name.
	GameName GameName `json:"gameName"`

	// JoinContext Context used to join a game session
	JoinContext *JoinContext `json:"joinContext,omitempty"`

	// Meta Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
	Meta *Meta `json:"meta,omitempty"`

	// RichPresence string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
	RichPresence *RichPresence `json:"richPresence,omitempty"`

	// Status the status of the presence record for the user
	Status PresenceStatus `json:"status"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// Ttl How long in seconds before this connection will be considered offline if no futher presence update is made.   | NOTE - this value MUST be at MINIMUM several seconds longer than the rate of your heartbeat timer, or your presence will always expire. | We recommend having your keep alive allow for at least 3 heartbeats to occur in the window (espepecially on mobile), | but it's entirely up to you how much of a grace period you want.  And for how a long droppped connection can be recovered. | The minimum is set to 35s because our plugin heartbeat is 30s.  We do NOT recommend using 35s.  Default is 5m.
	Ttl *Ttl `json:"ttl,omitempty"`
}

// SetActiveGroupJSONBody defines parameters for SetActiveGroup.
type SetActiveGroupJSONBody struct {
	// ActiveGroupid the id of the group.  validates ULID pattern.
	ActiveGroupid Groupid `json:"activeGroupid"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`
}

// ReportUserAbuseJSONBody defines parameters for ReportUserAbuse.
type ReportUserAbuseJSONBody struct {
	// GameSessionInfo The additional information for a report in a key-value pair format.
	GameSessionInfo *map[string]interface{} `json:"gameSessionInfo,omitempty"`
	Os              *string                 `json:"os,omitempty"`
	Platform        string                  `json:"platform"`

	// ReportMessage The user's specified report message. Limit the size to 5 KB.
	ReportMessage string `json:"reportMessage"`

	// ReportingCategory Reporting category
	ReportingCategory ReportingCategory `json:"reportingCategory"`

	// ReportingContentType The type of reporting content.
	ReportingContentType *string `json:"reportingContentType,omitempty"`

	// ReportingUserId The reporting user's full account id.
	ReportingUserId string `json:"reportingUserId"`

	// ReportingUserLocale The reporting user's locale.
	ReportingUserLocale string `json:"reportingUserLocale"`

	// SubjectTitle The subject title of a report.
	SubjectTitle string `json:"subjectTitle"`

	// TeleMeta Optional additional information to be included with telemetry meta data.  Must be JSON format.  ALL values must be STRING format. Max 1024 bytes. | NAMING KEYS - AWS s3 parquet converts all names to lowercase and is case sensitive; it's suggested that your map 'keys' be all lower case with underscores rather than camel case. see example.
	TeleMeta *TelemetryMetaData `json:"teleMeta"`

	// VersionNumber The version number of a game.
	VersionNumber *string `json:"versionNumber,omitempty"`
}

// LoginJSONRequestBody defines body for Login for application/json ContentType.
type LoginJSONRequestBody LoginJSONBody

// LogoutJSONRequestBody defines body for Logout for application/json ContentType.
type LogoutJSONRequestBody LogoutJSONBody

// RefreshTokenJSONRequestBody defines body for RefreshToken for application/json ContentType.
type RefreshTokenJSONRequestBody RefreshTokenJSONBody

// SendChatMessageJSONRequestBody defines body for SendChatMessage for application/json ContentType.
type SendChatMessageJSONRequestBody SendChatMessageJSONBody

// IncrementEndorsementJSONRequestBody defines body for IncrementEndorsement for application/json ContentType.
type IncrementEndorsementJSONRequestBody IncrementEndorsementJSONBody

// UpdateFriendStatusJSONRequestBody defines body for UpdateFriendStatus for application/json ContentType.
type UpdateFriendStatusJSONRequestBody UpdateFriendStatusJSONBody

// MakeFriendJSONRequestBody defines body for MakeFriend for application/json ContentType.
type MakeFriendJSONRequestBody MakeFriendJSONBody

// CreateGroupJSONRequestBody defines body for CreateGroup for application/json ContentType.
type CreateGroupJSONRequestBody CreateGroupJSONBody

// UpdateGroupJSONRequestBody defines body for UpdateGroup for application/json ContentType.
type UpdateGroupJSONRequestBody = UpdateGroupRequest

// SendControlMessageJSONRequestBody defines body for SendControlMessage for application/json ContentType.
type SendControlMessageJSONRequestBody SendControlMessageJSONBody

// UpdateGroupMemberJSONRequestBody defines body for UpdateGroupMember for application/json ContentType.
type UpdateGroupMemberJSONRequestBody = UpdateGroupMemberRequest

// UpdateGroupMemberMetaJSONRequestBody defines body for UpdateGroupMemberMeta for application/json ContentType.
type UpdateGroupMemberMetaJSONRequestBody UpdateGroupMemberMetaJSONBody

// AcceptInviteJSONRequestBody defines body for AcceptInvite for application/json ContentType.
type AcceptInviteJSONRequestBody AcceptInviteJSONBody

// SendInviteJSONRequestBody defines body for SendInvite for application/json ContentType.
type SendInviteJSONRequestBody SendInviteJSONBody

// SendJoinRequestJSONRequestBody defines body for SendJoinRequest for application/json ContentType.
type SendJoinRequestJSONRequestBody SendJoinRequestJSONBody

// ApproveJoinRequestJSONRequestBody defines body for ApproveJoinRequest for application/json ContentType.
type ApproveJoinRequestJSONRequestBody ApproveJoinRequestJSONBody

// AcceptInviteByFirstPartySessionIdsJSONRequestBody defines body for AcceptInviteByFirstPartySessionIds for application/json ContentType.
type AcceptInviteByFirstPartySessionIdsJSONRequestBody AcceptInviteByFirstPartySessionIdsJSONBody

// UpsertSessionAuthJSONRequestBody defines body for UpsertSessionAuth for application/json ContentType.
type UpsertSessionAuthJSONRequestBody UpsertSessionAuthJSONBody

// ImportBlocklistJSONRequestBody defines body for ImportBlocklist for application/json ContentType.
type ImportBlocklistJSONRequestBody ImportBlocklistJSONBody

// AddBlocklistJSONRequestBody defines body for AddBlocklist for application/json ContentType.
type AddBlocklistJSONRequestBody AddBlocklistJSONBody

// UpdateRecentlyPlayedJSONRequestBody defines body for UpdateRecentlyPlayed for application/json ContentType.
type UpdateRecentlyPlayedJSONRequestBody = SetPlayedRequest

// SetUserPresenceJSONRequestBody defines body for SetUserPresence for application/json ContentType.
type SetUserPresenceJSONRequestBody SetUserPresenceJSONBody

// SetActiveGroupJSONRequestBody defines body for SetActiveGroup for application/json ContentType.
type SetActiveGroupJSONRequestBody SetActiveGroupJSONBody

// ReportUserAbuseJSONRequestBody defines body for ReportUserAbuse for application/json ContentType.
type ReportUserAbuseJSONRequestBody ReportUserAbuseJSONBody

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Login to DNA account
	// (POST /auth/login)
	Login(w http.ResponseWriter, r *http.Request)
	// Logout user
	// (POST /auth/logout)
	Logout(w http.ResponseWriter, r *http.Request)
	// Refresh token
	// (POST /auth/refresh)
	RefreshToken(w http.ResponseWriter, r *http.Request)
	// Send a message to a chat room
	// (POST /chatRoom/{pRoomid}/message)
	SendChatMessage(w http.ResponseWriter, r *http.Request, pRoomid PRoomid)
	// Find endpoints to use.  Nil productid or id will return default discovery. Empty '?id=' parameter will return all for given productid with canList=true.
	// (GET /discovery)
	GetDiscovery(w http.ResponseWriter, r *http.Request, params GetDiscoveryParams)
	// get endorsements for self
	// (GET /endorsements/users/me)
	GetEndorsementsForSelf(w http.ResponseWriter, r *http.Request)
	// get endorsement for other user
	// (GET /endorsements/users/{pUserid})
	GetEndorsementsForUser(w http.ResponseWriter, r *http.Request, pUserid PUserid)
	// increment endorsement for user
	// (POST /endorsements/users/{pUserid})
	IncrementEndorsement(w http.ResponseWriter, r *http.Request, pUserid PUserid)
	// List friends for user
	// (GET /friends)
	ListFriends(w http.ResponseWriter, r *http.Request, params ListFriendsParams)
	// Find common platforms users that have DNA accounts
	// (GET /friends/accounts/search)
	ImportPlatformFriends(w http.ResponseWriter, r *http.Request, params ImportPlatformFriendsParams)
	// Delete a friend, reject invite, or revoke invite
	// (DELETE /friends/{pFriendid})
	DeleteFriend(w http.ResponseWriter, r *http.Request, pFriendid PFriendid)
	// Get a single friend by friendid
	// (GET /friends/{pFriendid})
	GetFriend(w http.ResponseWriter, r *http.Request, pFriendid PFriendid)
	// Update friend `viewed` flag
	// (PATCH /friends/{pFriendid})
	UpdateFriendStatus(w http.ResponseWriter, r *http.Request, pFriendid PFriendid)
	// Invite a user to become friends or accept invite
	// (POST /friends/{pFriendid})
	MakeFriend(w http.ResponseWriter, r *http.Request, pFriendid PFriendid)
	// Get groups that the user belongs to
	// (GET /groups)
	GetGroups(w http.ResponseWriter, r *http.Request, params GetGroupsParams)
	// Create a Group
	// (POST /groups)
	CreateGroup(w http.ResponseWriter, r *http.Request)
	// Disband/Delete a Group
	// (DELETE /groups/{pGroupid})
	DeleteGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Get a Group
	// (GET /groups/{pGroupid})
	GetGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Update a group's properties.  Valid properties are: canMembersInvite, joinRequestAction, maxMembers, meta, password. It is recommended that you only submit fields that should be modified.
	// (PATCH /groups/{pGroupid})
	UpdateGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Send a control message (json or binary)
	// (POST /groups/{pGroupid}/control)
	SendControlMessage(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Get a detailed list of member information for a specific group
	// (GET /groups/{pGroupid}/members)
	GetGroupMembers(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Leave from group
	// (DELETE /groups/{pGroupid}/members/me)
	LeaveGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid)
	// Kick a member from group
	// (DELETE /groups/{pGroupid}/members/{pMemberid})
	KickMemberFromGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid, params KickMemberFromGroupParams)
	// Get member in group
	// (GET /groups/{pGroupid}/members/{pMemberid})
	GetGroupMember(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Update member of group
	// (PATCH /groups/{pGroupid}/members/{pMemberid})
	UpdateGroupMember(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Update the metadata of a specified group member. The API accepts an update request only if the submitted time is later than the member's existing metadata. Values in the request body will be added to the member's metadata if their keys do not already exist. Otherwise, the values will overwrite the existing ones.
	// (PUT /groups/{pGroupid}/members/{pMemberid}/meta)
	UpdateGroupMemberMeta(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Get the server health status
	// (GET /health)
	GetHealth(w http.ResponseWriter, r *http.Request, params GetHealthParams)
	// Decline user's invite to group from inviter
	// (DELETE /memberships/invites/groups/{pGroupid}/approvers/{pApproverid})
	DeclineInvites(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pApproverid PApproverid)
	// Accept invite to a group.
	// (PATCH /memberships/invites/groups/{pGroupid}/approvers/{pApproverid})
	AcceptInvite(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pApproverid PApproverid)
	// Revoke an invite
	// (DELETE /memberships/invites/groups/{pGroupid}/members/{pMemberid})
	RevokeInvite(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Invite a user to the group
	// (POST /memberships/invites/groups/{pGroupid}/members/{pMemberid})
	SendInvite(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Get user's invitations
	// (GET /memberships/invites/me)
	GetInvites(w http.ResponseWriter, r *http.Request, params GetInvitesParams)
	// Request to join group
	// (POST /memberships/requests/groups/{pGroupid}/approvers/{pApproverid})
	SendJoinRequest(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pApproverid PApproverid)
	// Reject a group join request
	// (DELETE /memberships/requests/groups/{pGroupid}/members/{pMemberid})
	RejectJoinRequest(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// Approve a group join request
	// (PATCH /memberships/requests/groups/{pGroupid}/members/{pMemberid})
	ApproveJoinRequest(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid)
	// request a sync from 1P session data to T2GP group
	// (GET /platform/{pOnlineServiceType}/groups/{pGroupid}/sync)
	SyncSessionToGroup(w http.ResponseWriter, r *http.Request, pOnlineServiceType POnlineServiceType, pGroupid PGroupid)
	// Accept invite to a first party session.
	// (PATCH /platform/{pOnlineServiceType}/memberships/invites/session/{pSessionid}/approvers/{pFirstPartyApproverid})
	AcceptInviteByFirstPartySessionIds(w http.ResponseWriter, r *http.Request, pOnlineServiceType POnlineServiceType, pSessionid PSessionid, pFirstPartyApproverid PFirstPartyApproverid)
	// send platform auth code to server for user to use in Session Syncing
	// (PUT /platform/{pOnlineServiceType}/user/{pFirstPartyid}/auth)
	UpsertSessionAuth(w http.ResponseWriter, r *http.Request, pOnlineServiceType POnlineServiceType, pFirstPartyid PFirstPartyid)
	// This search API is to search for full 2K accounts by full account name.
	// (GET /search/2KUsers)
	Search2KUsers(w http.ResponseWriter, r *http.Request, params Search2KUsersParams)
	// clear user's entire block list.
	// (DELETE /user/blocklist)
	DelBlocklist(w http.ResponseWriter, r *http.Request)
	// Get user's block list.
	// (GET /user/blocklist)
	GetBlocklist(w http.ResponseWriter, r *http.Request, params GetBlocklistParams)
	// Batch import users to blocklist.  Must submit no more than 20 userids per request.
	// (POST /user/blocklist)
	ImportBlocklist(w http.ResponseWriter, r *http.Request)
	// remove user from blocklist
	// (DELETE /user/blocklist/{pUserid})
	RemoveBlocklist(w http.ResponseWriter, r *http.Request, pUserid PUserid)
	// Add a user to blocklist
	// (POST /user/blocklist/{pUserid})
	AddBlocklist(w http.ResponseWriter, r *http.Request, pUserid PUserid)
	// clear user's entire recently played list
	// (DELETE /user/played)
	DeleteRecentlyPlayed(w http.ResponseWriter, r *http.Request)
	// Get list of recently played with users.
	// (GET /user/played)
	GetRecentlyPlayed(w http.ResponseWriter, r *http.Request, params GetRecentlyPlayedParams)
	// Update recently played list of users
	// (POST /user/played)
	UpdateRecentlyPlayed(w http.ResponseWriter, r *http.Request)
	// Clear user presence for the productid provided in the user's JWT
	// (DELETE /user/presence)
	ClearPresence(w http.ResponseWriter, r *http.Request)
	// Get all user presence objects for productid in the user's JWT. Parameters are deprecated.
	// (GET /user/presence)
	GetUserPresence(w http.ResponseWriter, r *http.Request)
	// Set presence for user
	// (POST /user/presence)
	SetUserPresence(w http.ResponseWriter, r *http.Request)
	// Clear the active group for the user
	// (DELETE /user/presence/active)
	ClearActiveGroup(w http.ResponseWriter, r *http.Request)
	// Set the active group for the user
	// (POST /user/presence/active)
	SetActiveGroup(w http.ResponseWriter, r *http.Request)
	// Heartbeat presence for user
	// (POST /user/presence/heartbeat)
	PresenceHeartBeat(w http.ResponseWriter, r *http.Request)
	// Get current logged in user profile
	// (GET /user/profile)
	GetUserProfile(w http.ResponseWriter, r *http.Request)
	// Force a sync profile w/ DNA values. The SSO sync service makes this API unnecessary unless access to profile data is needed *immediately* after an profile update.  SSO sync usually happens within seconds. We recommend NOT using this endpoint and letting it sync on its own as it can be slow to return and our back end sync is real time.
	// (POST /user/profile/sync)
	SyncUserProfile(w http.ResponseWriter, r *http.Request)
	// Report a user for abuse
	// (POST /user/{pUserid}/report)
	ReportUserAbuse(w http.ResponseWriter, r *http.Request, pUserid PUserid)
	// Get the server version
	// (GET /version)
	GetVersion(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// Login to DNA account
// (POST /auth/login)
func (_ Unimplemented) Login(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Logout user
// (POST /auth/logout)
func (_ Unimplemented) Logout(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Refresh token
// (POST /auth/refresh)
func (_ Unimplemented) RefreshToken(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Send a message to a chat room
// (POST /chatRoom/{pRoomid}/message)
func (_ Unimplemented) SendChatMessage(w http.ResponseWriter, r *http.Request, pRoomid PRoomid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Find endpoints to use.  Nil productid or id will return default discovery. Empty '?id=' parameter will return all for given productid with canList=true.
// (GET /discovery)
func (_ Unimplemented) GetDiscovery(w http.ResponseWriter, r *http.Request, params GetDiscoveryParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// get endorsements for self
// (GET /endorsements/users/me)
func (_ Unimplemented) GetEndorsementsForSelf(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// get endorsement for other user
// (GET /endorsements/users/{pUserid})
func (_ Unimplemented) GetEndorsementsForUser(w http.ResponseWriter, r *http.Request, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// increment endorsement for user
// (POST /endorsements/users/{pUserid})
func (_ Unimplemented) IncrementEndorsement(w http.ResponseWriter, r *http.Request, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// List friends for user
// (GET /friends)
func (_ Unimplemented) ListFriends(w http.ResponseWriter, r *http.Request, params ListFriendsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Find common platforms users that have DNA accounts
// (GET /friends/accounts/search)
func (_ Unimplemented) ImportPlatformFriends(w http.ResponseWriter, r *http.Request, params ImportPlatformFriendsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Delete a friend, reject invite, or revoke invite
// (DELETE /friends/{pFriendid})
func (_ Unimplemented) DeleteFriend(w http.ResponseWriter, r *http.Request, pFriendid PFriendid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get a single friend by friendid
// (GET /friends/{pFriendid})
func (_ Unimplemented) GetFriend(w http.ResponseWriter, r *http.Request, pFriendid PFriendid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update friend `viewed` flag
// (PATCH /friends/{pFriendid})
func (_ Unimplemented) UpdateFriendStatus(w http.ResponseWriter, r *http.Request, pFriendid PFriendid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Invite a user to become friends or accept invite
// (POST /friends/{pFriendid})
func (_ Unimplemented) MakeFriend(w http.ResponseWriter, r *http.Request, pFriendid PFriendid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get groups that the user belongs to
// (GET /groups)
func (_ Unimplemented) GetGroups(w http.ResponseWriter, r *http.Request, params GetGroupsParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Create a Group
// (POST /groups)
func (_ Unimplemented) CreateGroup(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Disband/Delete a Group
// (DELETE /groups/{pGroupid})
func (_ Unimplemented) DeleteGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get a Group
// (GET /groups/{pGroupid})
func (_ Unimplemented) GetGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update a group's properties.  Valid properties are: canMembersInvite, joinRequestAction, maxMembers, meta, password. It is recommended that you only submit fields that should be modified.
// (PATCH /groups/{pGroupid})
func (_ Unimplemented) UpdateGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Send a control message (json or binary)
// (POST /groups/{pGroupid}/control)
func (_ Unimplemented) SendControlMessage(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get a detailed list of member information for a specific group
// (GET /groups/{pGroupid}/members)
func (_ Unimplemented) GetGroupMembers(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Leave from group
// (DELETE /groups/{pGroupid}/members/me)
func (_ Unimplemented) LeaveGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Kick a member from group
// (DELETE /groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) KickMemberFromGroup(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid, params KickMemberFromGroupParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get member in group
// (GET /groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) GetGroupMember(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update member of group
// (PATCH /groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) UpdateGroupMember(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update the metadata of a specified group member. The API accepts an update request only if the submitted time is later than the member's existing metadata. Values in the request body will be added to the member's metadata if their keys do not already exist. Otherwise, the values will overwrite the existing ones.
// (PUT /groups/{pGroupid}/members/{pMemberid}/meta)
func (_ Unimplemented) UpdateGroupMemberMeta(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get the server health status
// (GET /health)
func (_ Unimplemented) GetHealth(w http.ResponseWriter, r *http.Request, params GetHealthParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Decline user's invite to group from inviter
// (DELETE /memberships/invites/groups/{pGroupid}/approvers/{pApproverid})
func (_ Unimplemented) DeclineInvites(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pApproverid PApproverid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Accept invite to a group.
// (PATCH /memberships/invites/groups/{pGroupid}/approvers/{pApproverid})
func (_ Unimplemented) AcceptInvite(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pApproverid PApproverid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Revoke an invite
// (DELETE /memberships/invites/groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) RevokeInvite(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Invite a user to the group
// (POST /memberships/invites/groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) SendInvite(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get user's invitations
// (GET /memberships/invites/me)
func (_ Unimplemented) GetInvites(w http.ResponseWriter, r *http.Request, params GetInvitesParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Request to join group
// (POST /memberships/requests/groups/{pGroupid}/approvers/{pApproverid})
func (_ Unimplemented) SendJoinRequest(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pApproverid PApproverid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Reject a group join request
// (DELETE /memberships/requests/groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) RejectJoinRequest(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Approve a group join request
// (PATCH /memberships/requests/groups/{pGroupid}/members/{pMemberid})
func (_ Unimplemented) ApproveJoinRequest(w http.ResponseWriter, r *http.Request, pGroupid PGroupid, pMemberid PMemberid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// request a sync from 1P session data to T2GP group
// (GET /platform/{pOnlineServiceType}/groups/{pGroupid}/sync)
func (_ Unimplemented) SyncSessionToGroup(w http.ResponseWriter, r *http.Request, pOnlineServiceType POnlineServiceType, pGroupid PGroupid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Accept invite to a first party session.
// (PATCH /platform/{pOnlineServiceType}/memberships/invites/session/{pSessionid}/approvers/{pFirstPartyApproverid})
func (_ Unimplemented) AcceptInviteByFirstPartySessionIds(w http.ResponseWriter, r *http.Request, pOnlineServiceType POnlineServiceType, pSessionid PSessionid, pFirstPartyApproverid PFirstPartyApproverid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// send platform auth code to server for user to use in Session Syncing
// (PUT /platform/{pOnlineServiceType}/user/{pFirstPartyid}/auth)
func (_ Unimplemented) UpsertSessionAuth(w http.ResponseWriter, r *http.Request, pOnlineServiceType POnlineServiceType, pFirstPartyid PFirstPartyid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// This search API is to search for full 2K accounts by full account name.
// (GET /search/2KUsers)
func (_ Unimplemented) Search2KUsers(w http.ResponseWriter, r *http.Request, params Search2KUsersParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// clear user's entire block list.
// (DELETE /user/blocklist)
func (_ Unimplemented) DelBlocklist(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get user's block list.
// (GET /user/blocklist)
func (_ Unimplemented) GetBlocklist(w http.ResponseWriter, r *http.Request, params GetBlocklistParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Batch import users to blocklist.  Must submit no more than 20 userids per request.
// (POST /user/blocklist)
func (_ Unimplemented) ImportBlocklist(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// remove user from blocklist
// (DELETE /user/blocklist/{pUserid})
func (_ Unimplemented) RemoveBlocklist(w http.ResponseWriter, r *http.Request, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Add a user to blocklist
// (POST /user/blocklist/{pUserid})
func (_ Unimplemented) AddBlocklist(w http.ResponseWriter, r *http.Request, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// clear user's entire recently played list
// (DELETE /user/played)
func (_ Unimplemented) DeleteRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get list of recently played with users.
// (GET /user/played)
func (_ Unimplemented) GetRecentlyPlayed(w http.ResponseWriter, r *http.Request, params GetRecentlyPlayedParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update recently played list of users
// (POST /user/played)
func (_ Unimplemented) UpdateRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Clear user presence for the productid provided in the user's JWT
// (DELETE /user/presence)
func (_ Unimplemented) ClearPresence(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get all user presence objects for productid in the user's JWT. Parameters are deprecated.
// (GET /user/presence)
func (_ Unimplemented) GetUserPresence(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Set presence for user
// (POST /user/presence)
func (_ Unimplemented) SetUserPresence(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Clear the active group for the user
// (DELETE /user/presence/active)
func (_ Unimplemented) ClearActiveGroup(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Set the active group for the user
// (POST /user/presence/active)
func (_ Unimplemented) SetActiveGroup(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Heartbeat presence for user
// (POST /user/presence/heartbeat)
func (_ Unimplemented) PresenceHeartBeat(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get current logged in user profile
// (GET /user/profile)
func (_ Unimplemented) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Force a sync profile w/ DNA values. The SSO sync service makes this API unnecessary unless access to profile data is needed *immediately* after an profile update.  SSO sync usually happens within seconds. We recommend NOT using this endpoint and letting it sync on its own as it can be slow to return and our back end sync is real time.
// (POST /user/profile/sync)
func (_ Unimplemented) SyncUserProfile(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Report a user for abuse
// (POST /user/{pUserid}/report)
func (_ Unimplemented) ReportUserAbuse(w http.ResponseWriter, r *http.Request, pUserid PUserid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get the server version
// (GET /version)
func (_ Unimplemented) GetVersion(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// Login operation middleware
func (siw *ServerInterfaceWrapper) Login(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Login(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// Logout operation middleware
func (siw *ServerInterfaceWrapper) Logout(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Logout(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// RefreshToken operation middleware
func (siw *ServerInterfaceWrapper) RefreshToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RefreshToken(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SendChatMessage operation middleware
func (siw *ServerInterfaceWrapper) SendChatMessage(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pRoomid" -------------
	var pRoomid PRoomid

	err = runtime.BindStyledParameterWithOptions("simple", "pRoomid", chi.URLParam(r, "pRoomid"), &pRoomid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pRoomid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SendChatMessage(w, r, pRoomid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetDiscovery operation middleware
func (siw *ServerInterfaceWrapper) GetDiscovery(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetDiscoveryParams

	// ------------- Optional query parameter "discoveryPid" -------------

	err = runtime.BindQueryParameter("form", true, false, "discoveryPid", r.URL.Query(), &params.DiscoveryPid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "discoveryPid", Err: err})
		return
	}

	// ------------- Optional query parameter "discoveryid" -------------

	err = runtime.BindQueryParameter("form", true, false, "discoveryid", r.URL.Query(), &params.Discoveryid)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "discoveryid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDiscovery(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetEndorsementsForSelf operation middleware
func (siw *ServerInterfaceWrapper) GetEndorsementsForSelf(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetEndorsementsForSelf(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetEndorsementsForUser operation middleware
func (siw *ServerInterfaceWrapper) GetEndorsementsForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetEndorsementsForUser(w, r, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// IncrementEndorsement operation middleware
func (siw *ServerInterfaceWrapper) IncrementEndorsement(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.IncrementEndorsement(w, r, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ListFriends operation middleware
func (siw *ServerInterfaceWrapper) ListFriends(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params ListFriendsParams

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", r.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "status", Err: err})
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "next" -------------

	err = runtime.BindQueryParameter("form", true, false, "next", r.URL.Query(), &params.Next)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "next", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ListFriends(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ImportPlatformFriends operation middleware
func (siw *ServerInterfaceWrapper) ImportPlatformFriends(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params ImportPlatformFriendsParams

	// ------------- Required query parameter "id" -------------

	if paramValue := r.URL.Query().Get("id"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ImportPlatformFriends(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DeleteFriend operation middleware
func (siw *ServerInterfaceWrapper) DeleteFriend(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pFriendid" -------------
	var pFriendid PFriendid

	err = runtime.BindStyledParameterWithOptions("simple", "pFriendid", chi.URLParam(r, "pFriendid"), &pFriendid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pFriendid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteFriend(w, r, pFriendid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetFriend operation middleware
func (siw *ServerInterfaceWrapper) GetFriend(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pFriendid" -------------
	var pFriendid PFriendid

	err = runtime.BindStyledParameterWithOptions("simple", "pFriendid", chi.URLParam(r, "pFriendid"), &pFriendid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pFriendid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetFriend(w, r, pFriendid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateFriendStatus operation middleware
func (siw *ServerInterfaceWrapper) UpdateFriendStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pFriendid" -------------
	var pFriendid PFriendid

	err = runtime.BindStyledParameterWithOptions("simple", "pFriendid", chi.URLParam(r, "pFriendid"), &pFriendid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pFriendid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateFriendStatus(w, r, pFriendid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// MakeFriend operation middleware
func (siw *ServerInterfaceWrapper) MakeFriend(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pFriendid" -------------
	var pFriendid PFriendid

	err = runtime.BindStyledParameterWithOptions("simple", "pFriendid", chi.URLParam(r, "pFriendid"), &pFriendid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pFriendid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.MakeFriend(w, r, pFriendid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetGroups operation middleware
func (siw *ServerInterfaceWrapper) GetGroups(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetGroupsParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "next" -------------

	err = runtime.BindQueryParameter("form", true, false, "next", r.URL.Query(), &params.Next)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "next", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetGroups(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// CreateGroup operation middleware
func (siw *ServerInterfaceWrapper) CreateGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.CreateGroup(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DeleteGroup operation middleware
func (siw *ServerInterfaceWrapper) DeleteGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetGroup operation middleware
func (siw *ServerInterfaceWrapper) GetGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateGroup operation middleware
func (siw *ServerInterfaceWrapper) UpdateGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SendControlMessage operation middleware
func (siw *ServerInterfaceWrapper) SendControlMessage(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SendControlMessage(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetGroupMembers operation middleware
func (siw *ServerInterfaceWrapper) GetGroupMembers(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetGroupMembers(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// LeaveGroup operation middleware
func (siw *ServerInterfaceWrapper) LeaveGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.LeaveGroup(w, r, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// KickMemberFromGroup operation middleware
func (siw *ServerInterfaceWrapper) KickMemberFromGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params KickMemberFromGroupParams

	// ------------- Optional query parameter "reason" -------------

	err = runtime.BindQueryParameter("form", true, false, "reason", r.URL.Query(), &params.Reason)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "reason", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.KickMemberFromGroup(w, r, pGroupid, pMemberid, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetGroupMember operation middleware
func (siw *ServerInterfaceWrapper) GetGroupMember(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetGroupMember(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateGroupMember operation middleware
func (siw *ServerInterfaceWrapper) UpdateGroupMember(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateGroupMember(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateGroupMemberMeta operation middleware
func (siw *ServerInterfaceWrapper) UpdateGroupMemberMeta(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateGroupMemberMeta(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetHealth operation middleware
func (siw *ServerInterfaceWrapper) GetHealth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetHealthParams

	// ------------- Optional query parameter "id" -------------

	err = runtime.BindQueryParameter("form", true, false, "id", r.URL.Query(), &params.Id)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetHealth(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DeclineInvites operation middleware
func (siw *ServerInterfaceWrapper) DeclineInvites(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pApproverid" -------------
	var pApproverid PApproverid

	err = runtime.BindStyledParameterWithOptions("simple", "pApproverid", chi.URLParam(r, "pApproverid"), &pApproverid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pApproverid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeclineInvites(w, r, pGroupid, pApproverid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// AcceptInvite operation middleware
func (siw *ServerInterfaceWrapper) AcceptInvite(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pApproverid" -------------
	var pApproverid PApproverid

	err = runtime.BindStyledParameterWithOptions("simple", "pApproverid", chi.URLParam(r, "pApproverid"), &pApproverid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pApproverid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AcceptInvite(w, r, pGroupid, pApproverid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// RevokeInvite operation middleware
func (siw *ServerInterfaceWrapper) RevokeInvite(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RevokeInvite(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SendInvite operation middleware
func (siw *ServerInterfaceWrapper) SendInvite(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SendInvite(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetInvites operation middleware
func (siw *ServerInterfaceWrapper) GetInvites(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetInvitesParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "next" -------------

	err = runtime.BindQueryParameter("form", true, false, "next", r.URL.Query(), &params.Next)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "next", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetInvites(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SendJoinRequest operation middleware
func (siw *ServerInterfaceWrapper) SendJoinRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pApproverid" -------------
	var pApproverid PApproverid

	err = runtime.BindStyledParameterWithOptions("simple", "pApproverid", chi.URLParam(r, "pApproverid"), &pApproverid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pApproverid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SendJoinRequest(w, r, pGroupid, pApproverid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// RejectJoinRequest operation middleware
func (siw *ServerInterfaceWrapper) RejectJoinRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RejectJoinRequest(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ApproveJoinRequest operation middleware
func (siw *ServerInterfaceWrapper) ApproveJoinRequest(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	// ------------- Path parameter "pMemberid" -------------
	var pMemberid PMemberid

	err = runtime.BindStyledParameterWithOptions("simple", "pMemberid", chi.URLParam(r, "pMemberid"), &pMemberid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pMemberid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ApproveJoinRequest(w, r, pGroupid, pMemberid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SyncSessionToGroup operation middleware
func (siw *ServerInterfaceWrapper) SyncSessionToGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pOnlineServiceType" -------------
	var pOnlineServiceType POnlineServiceType

	err = runtime.BindStyledParameterWithOptions("simple", "pOnlineServiceType", chi.URLParam(r, "pOnlineServiceType"), &pOnlineServiceType, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pOnlineServiceType", Err: err})
		return
	}

	// ------------- Path parameter "pGroupid" -------------
	var pGroupid PGroupid

	err = runtime.BindStyledParameterWithOptions("simple", "pGroupid", chi.URLParam(r, "pGroupid"), &pGroupid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pGroupid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SyncSessionToGroup(w, r, pOnlineServiceType, pGroupid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// AcceptInviteByFirstPartySessionIds operation middleware
func (siw *ServerInterfaceWrapper) AcceptInviteByFirstPartySessionIds(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pOnlineServiceType" -------------
	var pOnlineServiceType POnlineServiceType

	err = runtime.BindStyledParameterWithOptions("simple", "pOnlineServiceType", chi.URLParam(r, "pOnlineServiceType"), &pOnlineServiceType, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pOnlineServiceType", Err: err})
		return
	}

	// ------------- Path parameter "pSessionid" -------------
	var pSessionid PSessionid

	err = runtime.BindStyledParameterWithOptions("simple", "pSessionid", chi.URLParam(r, "pSessionid"), &pSessionid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pSessionid", Err: err})
		return
	}

	// ------------- Path parameter "pFirstPartyApproverid" -------------
	var pFirstPartyApproverid PFirstPartyApproverid

	err = runtime.BindStyledParameterWithOptions("simple", "pFirstPartyApproverid", chi.URLParam(r, "pFirstPartyApproverid"), &pFirstPartyApproverid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pFirstPartyApproverid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AcceptInviteByFirstPartySessionIds(w, r, pOnlineServiceType, pSessionid, pFirstPartyApproverid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpsertSessionAuth operation middleware
func (siw *ServerInterfaceWrapper) UpsertSessionAuth(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pOnlineServiceType" -------------
	var pOnlineServiceType POnlineServiceType

	err = runtime.BindStyledParameterWithOptions("simple", "pOnlineServiceType", chi.URLParam(r, "pOnlineServiceType"), &pOnlineServiceType, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pOnlineServiceType", Err: err})
		return
	}

	// ------------- Path parameter "pFirstPartyid" -------------
	var pFirstPartyid PFirstPartyid

	err = runtime.BindStyledParameterWithOptions("simple", "pFirstPartyid", chi.URLParam(r, "pFirstPartyid"), &pFirstPartyid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pFirstPartyid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpsertSessionAuth(w, r, pOnlineServiceType, pFirstPartyid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// Search2KUsers operation middleware
func (siw *ServerInterfaceWrapper) Search2KUsers(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params Search2KUsersParams

	// ------------- Required query parameter "displayName" -------------

	if paramValue := r.URL.Query().Get("displayName"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "displayName"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "displayName", r.URL.Query(), &params.DisplayName)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "displayName", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.Search2KUsers(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DelBlocklist operation middleware
func (siw *ServerInterfaceWrapper) DelBlocklist(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DelBlocklist(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetBlocklist operation middleware
func (siw *ServerInterfaceWrapper) GetBlocklist(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBlocklistParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "next" -------------

	err = runtime.BindQueryParameter("form", true, false, "next", r.URL.Query(), &params.Next)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "next", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetBlocklist(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ImportBlocklist operation middleware
func (siw *ServerInterfaceWrapper) ImportBlocklist(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ImportBlocklist(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// RemoveBlocklist operation middleware
func (siw *ServerInterfaceWrapper) RemoveBlocklist(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.RemoveBlocklist(w, r, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// AddBlocklist operation middleware
func (siw *ServerInterfaceWrapper) AddBlocklist(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.AddBlocklist(w, r, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DeleteRecentlyPlayed operation middleware
func (siw *ServerInterfaceWrapper) DeleteRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteRecentlyPlayed(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetRecentlyPlayed operation middleware
func (siw *ServerInterfaceWrapper) GetRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetRecentlyPlayedParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", r.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "limit", Err: err})
		return
	}

	// ------------- Optional query parameter "next" -------------

	err = runtime.BindQueryParameter("form", true, false, "next", r.URL.Query(), &params.Next)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "next", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetRecentlyPlayed(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateRecentlyPlayed operation middleware
func (siw *ServerInterfaceWrapper) UpdateRecentlyPlayed(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateRecentlyPlayed(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ClearPresence operation middleware
func (siw *ServerInterfaceWrapper) ClearPresence(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ClearPresence(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetUserPresence operation middleware
func (siw *ServerInterfaceWrapper) GetUserPresence(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserPresence(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SetUserPresence operation middleware
func (siw *ServerInterfaceWrapper) SetUserPresence(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SetUserPresence(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ClearActiveGroup operation middleware
func (siw *ServerInterfaceWrapper) ClearActiveGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ClearActiveGroup(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SetActiveGroup operation middleware
func (siw *ServerInterfaceWrapper) SetActiveGroup(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SetActiveGroup(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// PresenceHeartBeat operation middleware
func (siw *ServerInterfaceWrapper) PresenceHeartBeat(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.PresenceHeartBeat(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetUserProfile operation middleware
func (siw *ServerInterfaceWrapper) GetUserProfile(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserProfile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SyncUserProfile operation middleware
func (siw *ServerInterfaceWrapper) SyncUserProfile(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SyncUserProfile(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// ReportUserAbuse operation middleware
func (siw *ServerInterfaceWrapper) ReportUserAbuse(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "pUserid" -------------
	var pUserid PUserid

	err = runtime.BindStyledParameterWithOptions("simple", "pUserid", chi.URLParam(r, "pUserid"), &pUserid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "pUserid", Err: err})
		return
	}

	ctx = context.WithValue(ctx, BearerAuthScopes, []string{})

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.ReportUserAbuse(w, r, pUserid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetVersion operation middleware
func (siw *ServerInterfaceWrapper) GetVersion(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetVersion(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/login", wrapper.Login)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/logout", wrapper.Logout)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/auth/refresh", wrapper.RefreshToken)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/chatRoom/{pRoomid}/message", wrapper.SendChatMessage)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/discovery", wrapper.GetDiscovery)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/endorsements/users/me", wrapper.GetEndorsementsForSelf)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/endorsements/users/{pUserid}", wrapper.GetEndorsementsForUser)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/endorsements/users/{pUserid}", wrapper.IncrementEndorsement)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/friends", wrapper.ListFriends)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/friends/accounts/search", wrapper.ImportPlatformFriends)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/friends/{pFriendid}", wrapper.DeleteFriend)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/friends/{pFriendid}", wrapper.GetFriend)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/friends/{pFriendid}", wrapper.UpdateFriendStatus)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/friends/{pFriendid}", wrapper.MakeFriend)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/groups", wrapper.GetGroups)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/groups", wrapper.CreateGroup)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/groups/{pGroupid}", wrapper.DeleteGroup)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/groups/{pGroupid}", wrapper.GetGroup)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/groups/{pGroupid}", wrapper.UpdateGroup)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/groups/{pGroupid}/control", wrapper.SendControlMessage)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/groups/{pGroupid}/members", wrapper.GetGroupMembers)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/groups/{pGroupid}/members/me", wrapper.LeaveGroup)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/groups/{pGroupid}/members/{pMemberid}", wrapper.KickMemberFromGroup)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/groups/{pGroupid}/members/{pMemberid}", wrapper.GetGroupMember)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/groups/{pGroupid}/members/{pMemberid}", wrapper.UpdateGroupMember)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/groups/{pGroupid}/members/{pMemberid}/meta", wrapper.UpdateGroupMemberMeta)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/health", wrapper.GetHealth)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/memberships/invites/groups/{pGroupid}/approvers/{pApproverid}", wrapper.DeclineInvites)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/memberships/invites/groups/{pGroupid}/approvers/{pApproverid}", wrapper.AcceptInvite)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/memberships/invites/groups/{pGroupid}/members/{pMemberid}", wrapper.RevokeInvite)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/memberships/invites/groups/{pGroupid}/members/{pMemberid}", wrapper.SendInvite)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/memberships/invites/me", wrapper.GetInvites)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/memberships/requests/groups/{pGroupid}/approvers/{pApproverid}", wrapper.SendJoinRequest)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/memberships/requests/groups/{pGroupid}/members/{pMemberid}", wrapper.RejectJoinRequest)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/memberships/requests/groups/{pGroupid}/members/{pMemberid}", wrapper.ApproveJoinRequest)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/platform/{pOnlineServiceType}/groups/{pGroupid}/sync", wrapper.SyncSessionToGroup)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/platform/{pOnlineServiceType}/memberships/invites/session/{pSessionid}/approvers/{pFirstPartyApproverid}", wrapper.AcceptInviteByFirstPartySessionIds)
	})
	r.Group(func(r chi.Router) {
		r.Put(options.BaseURL+"/platform/{pOnlineServiceType}/user/{pFirstPartyid}/auth", wrapper.UpsertSessionAuth)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/search/2KUsers", wrapper.Search2KUsers)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/user/blocklist", wrapper.DelBlocklist)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/blocklist", wrapper.GetBlocklist)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/blocklist", wrapper.ImportBlocklist)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/user/blocklist/{pUserid}", wrapper.RemoveBlocklist)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/blocklist/{pUserid}", wrapper.AddBlocklist)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/user/played", wrapper.DeleteRecentlyPlayed)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/played", wrapper.GetRecentlyPlayed)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/played", wrapper.UpdateRecentlyPlayed)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/user/presence", wrapper.ClearPresence)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/presence", wrapper.GetUserPresence)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/presence", wrapper.SetUserPresence)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/user/presence/active", wrapper.ClearActiveGroup)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/presence/active", wrapper.SetActiveGroup)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/presence/heartbeat", wrapper.PresenceHeartBeat)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/user/profile", wrapper.GetUserProfile)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/profile/sync", wrapper.SyncUserProfile)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/user/{pUserid}/report", wrapper.ReportUserAbuse)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/version", wrapper.GetVersion)
	})

	return r
}
