import { render, waitFor } from '@testing-library/svelte';
import FriendRequestsMock from '../../../components/FriendRequests/__mock__/FriendRequests.svelte';
import FriendsListMock from '../../../components/FriendsList/__mock__/FriendsList.svelte';
import TaskBarMock from '../../../components/TaskBar/__mock__/TaskBar.svelte';
import TopBarMock from '../../../components/TopBar/__mock__/TopBar.svelte';
import { SocialServices } from '../../../services';
import {
  friendsServiceMock,
  transportServiceMock,
} from '../../../services/__mocks__';
import FriendsWrapper from './FriendsWrapper.svelte';

jest.mock('../../../components', () => ({
  FriendsList: FriendsListMock,
  FriendRequests: FriendRequestsMock,
  TopBar: TopBarMock,
  TaskBar: TaskBarMock,
}));

const socialServicesMock = new SocialServices({
  transportService: transportServiceMock,
  friendsService: friendsServiceMock,
});

describe('Friends Page', () => {
  it('should render Friends List', async () => {
    const { getByTestId } = render(FriendsWrapper, {
      props: {
        context: socialServicesMock,
      },
    });

    await waitFor(() => {
      expect(getByTestId('friends-list-mock')).not.toBeNull();
    });
  });

  it('should render Friend Requests', async () => {
    const { getByTestId } = render(FriendsWrapper, {
      props: {
        context: socialServicesMock,
        url: 'friendRequests',
      },
    });

    await waitFor(() => {
      expect(getByTestId('friend-requests-mock')).not.toBeNull();
    });
  });
});
