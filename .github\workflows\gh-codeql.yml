name: "GitHub Advanced Security - CodeQL"

on:
  push:
    branches: [ "*" ]
  workflow_dispatch:
  pull_request:
    branches:
      - develop
  schedule:
    - cron: '0 15 * * 0' # Runs at 15:00, only on Sunday

jobs:
  analyze:
    name: Analyze
    runs-on: [t2gp-arc-linux]
    timeout-minutes: 360
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'go' ]
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Use Node.js 
      uses: actions/setup-node@v4
      with:
        node-version: 22.x
        registry-url: https://npm.pkg.github.com/
        scope: take-two-t2gp
    
    - name: Install redocly
      run: npm install @redocly/cli -g

    - name: Setup Go 1.20
      if: ${{ matrix.language == 'go' }}
      uses: actions/setup-go@v5
      with:
        go-version-file: 'go.mod'
        cache-dependency-path: "go.sum"

    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
        #queries: security-extended,security-and-quality

    - name: Build
      if: ${{ matrix.language == 'go' }}
      run: |
        GOPRIVATE=github.com/take-two-t2gp,github.com/2kg-coretech
        git config --global url.https://${{secrets.SERVICE_ACCOUNT_GH_PAT}}:<EMAIL>/.insteadOf https://github.com/
        make build APP_VERSION=$(git rev-parse --short=8 ${{ github.sha }}) GOOS=linux GOARCH=amd64 CGO_ENABLED=0

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"
