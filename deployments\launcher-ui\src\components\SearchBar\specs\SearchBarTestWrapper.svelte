<script lang="ts">
  import { setContext } from 'svelte';
  import { readable } from 'svelte/store';
  import { CONTEXT_KEY_I18N } from '../../../constant';
  import { TType } from '../../../context';
  import SearchBar from '../SearchBar.svelte';

  export let placeholder = '';
  setContext(CONTEXT_KEY_I18N, {
    t: readable<TType>(null, function start(set) {
      return set((key: string) => key);
    }),
  });
</script>

<div>
  <SearchBar placeholder="{placeholder}" />
</div>
