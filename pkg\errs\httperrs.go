package errs

import (
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// HttpErrorHandler http handler to simulate errors for /v1/dev/error/{errorID}
func HttpErrorHandler(w http.ResponseWriter, r *http.Request) {
	errorIDStr := chi.URLParam(r, "errorID")
	errorID, err := strconv.Atoi(errorIDStr)
	if err != nil {
		errorID = 200
	}
	utils.WriteJsonResponse(w, r, errorID, map[string]string{})
}
