import en from '@take-two-t2gp/t2gp-strings/dist/en/translation.json';
import fr from '@take-two-t2gp/t2gp-strings/dist/fr/translation.json';
import type { i18n, Resource } from 'i18next';

const translations = {
  en: {
    translation: {
      ...en,
    },
  },
  fr: {
    translation: {
      ...fr,
    },
  },
};

export class I18nService {
  // expose i18next
  i18n: i18n;
  initialLanguage: string;

  constructor(instance: i18n, initialLanguage: string) {
    this.i18n = instance;
    this.initialLanguage = initialLanguage;
    this.initialize();
  }

  // Our translation function
  t(key: string, replacements?: Record<string, unknown>): string {
    return this.i18n.t(key, replacements);
  }

  // Initializing i18n
  initialize() {
    this.i18n.init({
      lng: this.initialLanguage,
      fallbackLng: 'en',
      debug: false,
      preload: [this.initialLanguage],
      load: 'currentOnly',
      interpolation: {
        escapeValue: false,
      },
      keySeparator: false,
      resources: translations as Resource,
    });
  }

  changeLanguage(language: string) {
    this.i18n.changeLanguage(language);
  }
}
