package telemetry

import (
	"testing"
	time "time"

	statsd "github.com/DataDog/datadog-go/v5/statsd"
	"github.com/franela/goblin"
)

func TestNullClient(t *testing.T) {
	g := goblin.Goblin(t)

	g.<PERSON>cribe("NullClient", func() {
		g.It("should return no errors", func() {
			c := &NullStatsdClient{}
			g.<PERSON>(c.<PERSON>au<PERSON>("name", 1, nil, 1)).IsNil()
			g.<PERSON>(c.<PERSON>("name", 1, nil, 1)).IsNil()
			g.<PERSON><PERSON><PERSON>(c.Histogram("name", 1, nil, 1)).IsNil()
			g.<PERSON><PERSON><PERSON>(c.Distribution("name", 1, nil, 1)).IsNil()
			g.<PERSON>(c.Decr("name", nil, 1)).IsNil()
			g.<PERSON>sert(c.Incr("name", nil, 1)).IsNil()
			g.<PERSON>(c.Set("name", "value", nil, 1)).IsNil()
			g.<PERSON><PERSON><PERSON>(c.<PERSON>("name", time.Second, nil, 1)).IsNil()
			g.<PERSON>(c.TimeInMilliseconds("name", 1000, nil, 1)).IsNil()
			g.Assert(c.Event(nil)).IsNil()
			g.Assert(c.SimpleEvent("title", "text")).IsNil()
			g.Assert(c.ServiceCheck(nil)).IsNil()
			g.Assert(c.SimpleServiceCheck("service", statsd.Ok)).IsNil()
			g.Assert(c.Close()).IsNil()
			g.Assert(c.Flush()).IsNil()
			g.Assert(c.IsClosed()).IsTrue()
			g.Assert(c.GetTelemetry()).Equal(statsd.Telemetry{})
		})
	})
}
