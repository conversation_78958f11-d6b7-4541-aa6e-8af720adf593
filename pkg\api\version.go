package api

import (
	"net/http"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
)

// GetVersion get the version of the server
func (api *SocialPublicAPI) GetVersion(w http.ResponseWriter, r *http.Request) {

	var version apipub.VersionResponse
	version.Version = api.Cfg.Version.Version
	version.GitHash = api.Cfg.Version.GitHash
	version.BuildDate = api.Cfg.Version.BuildDate
	ReturnOK(w, r, version)
}
