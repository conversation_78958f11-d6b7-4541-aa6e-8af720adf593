-module(t2gp_social_metrics).

-define(METRIC_SOCKET_OPEN, socket_open).
-define(METRIC_SOCKET_CLOSE, socket_close).

-export([
    gauge_session_count/0,
    metrics/0
]).

-spec gauge_session_count() -> integer().
gauge_session_count() ->
    SocketOpen = vmq_metrics:counter_val(?METRIC_SOCKET_OPEN),
    SocketClose = vmq_metrics:counter_val(?METRIC_SOCKET_CLOSE),
    TotalConnections = SocketOpen - SocketClose,
    TotalConnections.

-spec metrics() -> list().
metrics() ->    
    [
        {gauge, [], t2gp_social_metrics_session_count, t2gp_social_session_count, <<"Session count">>, gauge_session_count()}
    ].
