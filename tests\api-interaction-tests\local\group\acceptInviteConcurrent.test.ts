import request from 'supertest';
import { config } from '../../integration/lib/config';
import * as socialApi from '../../integration/lib/social-api';
import { StatusCodes } from 'http-status-codes';
import jwt_decode, { JwtPayload } from "jwt-decode";

describe('group-acceptInviteConcurrent', () => {
  let groupId: string;

  /* PLEASE NOTE *********
   * Before running the script, fill in 2k account credentials for group leader
   * and group members below. Fill in as many group members as needed.
   * Adjust sleepMs as needed; 0 means no delay between calls.
   * 
   * TODO: input these params from command line or a data file.
   */
  // Time in ms to sleep between the async invite accept calls.
  const sleepMs: number = 0;

  // login/pass for the group leader.
  const groupLeaderAccount = {
      "name" : "",
      "pass" : ""
  };
  const groupLeaderCred = {uid:"", token:""};

  // login/pass for group members. Add more if needed.
  const groupMemberAccountList = [
    {
      "name" : "",
      "pass" : ""
    },
    {
      "name" : "",
      "pass" : ""
    }
  ];
  const groupMemberCredList: {uid: string, token: string}[] = [];

  beforeEach(async () => {
    // Login and get credential for group leader
    groupLeaderCred.token = await socialApi.loginIn(
      groupLeaderAccount.name,
      groupLeaderAccount.pass
    );
    const dLeader: JwtPayload = jwt_decode(groupLeaderCred.token);
    groupLeaderCred.uid = dLeader.sub as string;

    // create group
    const r = await socialApi.createGroup(groupLeaderCred.token, null, 'manual', null, null, true);
    groupId = socialApi.getGroupId(r);

    // Login and get credentials for group members
    for (const m of groupMemberAccountList) {
      const t = await socialApi.loginIn(
          m["name"], m["pass"]
      );
      const d: JwtPayload = jwt_decode(t);
      groupMemberCredList.push({uid:d.sub as string, token:t});
    }
  });

  afterEach(async () => {
    // delete group
    await socialApi.deleteGroup(groupLeaderCred.token, groupId);
    // logout for group leader
    await socialApi.loginOut(groupLeaderCred.token);
    // logout for group members
    for (const c of groupMemberCredList) {
      await socialApi.loginOut(c.token);
    }
  });

  /**
   *
   */
  it('accept invite concurrently', async () => {
    // for time stamps
    const start_time = Date.now();

    // sanity check
    expect(groupMemberCredList.length).toEqual(groupMemberAccountList.length);

    // sending invites serially
    console.log("Group leader sending invites serially...")
    for (const m of groupMemberCredList) {
      console.log(`time stamp: ${Date.now() - start_time}ms`);
      const inviteResp: request.Response = await socialApi.inviteOld(groupLeaderCred.token,
                                                               groupId,
                                                               m.uid);
      expect(inviteResp.status).toEqual(StatusCodes.CREATED);
    }

    // acceping invites concurrently
    console.log("Group members concurrently accepting invites...")
    const acceptInvitePromiseList = [];
    for (const m of groupMemberCredList) {
      console.log(`time stamp: ${Date.now() - start_time}ms`);
      acceptInvitePromiseList.push(
        socialApi.acceptInvite(m.token, groupId, m.uid)
      );
      // sleep between calls, to see the interval that would cause issue
      await new Promise(resolve => setTimeout(resolve, sleepMs));
    }
    for (const p of acceptInvitePromiseList) {
      p.then(resp => {
        expect(resp.status).toEqual(StatusCodes.OK);
        expect(resp.body).toEqual({});
      }).catch(err => console.log(err));
    }

    // get member list to see if all invite acceptances are successful
    // need some time before retrieving the member list, to avoid race condition
    await new Promise(resolve => setTimeout(resolve, 500));
    const resp: request.Response = await request(config.socialEndpoints.current.api)
      .get(`/v1/groups/${groupId}/members`)
      .set('Authorization', 'Bearer ' + groupLeaderCred.token)
    console.log(`Member list: \n${JSON.stringify(resp.body.items, null, 2)}`)
    expect(resp.status).toEqual(StatusCodes.OK);
    // plus 1 for group leader
    expect(resp.body.items.length).toEqual(groupMemberAccountList.length+1);
  });
});
