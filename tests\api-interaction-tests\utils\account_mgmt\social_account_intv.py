import social_account_mgmt
import account_intv

class SocialTwokAccountInteractive(account_intv.AccountInteractive):
    def __init__(self, server_type):
        self._server_type = server_type
        super().__init__()

    def health_check(self, search_by, q, replace_bad_accounts):
        self._account_manager.health_check(search_by, q, replace_bad_accounts)

    def create_account_manager(self):
        return social_account_mgmt.SocialTwokAccountManager("test-account-mgmt", f"twok_{self._server_type}",
                                                            "test-account-mgmt", f"twok_{self._server_type}",
                                                            self._server_type)

class SocialPdAccountInteractive(account_intv.AccountInteractive):
    # TODO: health check

    def create_account_manager(self):
        return social_account_mgmt.SocialPdAccountManager("test-account-mgmt", "pd", "test-account-mgmt", "pd")

class SocialSteamPAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialSteamPAccountManager("test-account-mgmt", "steamp", "test-account-mgmt", "steamp")

class SocialEpicPAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialEpicPAccountManager("test-account-mgmt", "epicp", "test-account-mgmt", "epicp")

class SocialSwitchPAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialSwitchPAccountManager("test-account-mgmt", "switchp", "test-account-mgmt", "switchp")

class SocialXbxPAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialXbxPAccountManager("test-account-mgmt", "xbxp", "test-account-mgmt", "xbxp")

class SocialXb1PAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialXb1PAccountManager("test-account-mgmt", "xb1p", "test-account-mgmt", "xb1p")

class SocialPs4PAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialPs4PAccountManager("test-account-mgmt", "ps4p", "test-account-mgmt", "ps4p")

class SocialPs5PAccountInteractive(account_intv.AccountInteractive):
    def create_account_manager(self):
        return social_account_mgmt.SocialPs5PAccountManager("test-account-mgmt", "ps5p", "test-account-mgmt", "ps5p")