name: Trigger MQTT Failover

on:
  workflow_dispatch:
    inputs:
      target_env:
        required: true
        type: choice
        options:
          - "sre-sandbox"
        description: "env to failover mqtt"
      target_set:
        required: true
        type: choice
        options:
          - "blue"
          - "green"
        description: 'Which MQTT STS to failover to'

permissions:
  actions: write
  id-token: write
  contents: write

jobs:
  failover:
    name: 'Failover MQTT'
    runs-on: [t2gp-arc-linux]
    env:
      CLUSTER: t2gp-non-production
      TARGET_ENV: ${{ github.event.inputs.target_env }}
      TARGET_SET: ${{ github.event.inputs.target_set }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::354767525209:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Install kubectl
        uses: azure/setup-kubectl@v1
      - name: MQTT Service Endpoint update
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/mqtt-failover.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/mqtt-failover.sh"