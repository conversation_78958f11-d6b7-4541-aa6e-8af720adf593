import { render } from '@testing-library/svelte';
import SVGIconMock from '../../../assets/icons/__mock__/SVGIconMock.svelte';
import { SocialServices } from '../../../services';
import { transportServiceMock } from '../../../services/__mocks__';
import BadgeMock from '../../Badge/__mock__/Badge.svelte';
import NavLinkMock from '../../NavLink/__mock__/NavLink.svelte';
import SearchBarMock from '../../SearchBar/__mock__/SearchBar.svelte';
import FriendsListActionBarWrapper from './FriendsListActionBarWrapper.svelte';

jest.mock('../../SearchBar', () => ({
  SearchBar: SearchBarMock,
}));

jest.mock('../../NavLink', () => ({
  NavLink: NavLinkMock,
}));

jest.mock('../../../assets/icons', () => ({
  SVGBell: SVGIconMock,
  SVGPersonAdd: SVGIconMock,
}));

jest.mock('../../Badge', () => ({
  Badge: BadgeMock,
}));

describe('FriendsListActionBar', () => {
  it('should render UI', () => {
    const socialServicesMock = new SocialServices({
      transportService: transportServiceMock,
    });
    expect(() =>
      render(FriendsListActionBarWrapper, {
        props: {
          context: socialServicesMock,
        },
      })
    ).not.toThrow();
  });
});
