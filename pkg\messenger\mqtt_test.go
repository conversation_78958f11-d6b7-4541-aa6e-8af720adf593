package messenger

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/franela/goblin"
	"github.com/jarcoal/httpmock"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"net/http"
	"testing"
	"time"
)

func TestNewMqttMessage(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("NewMqttMessage", func() {
		g.It("should succeed", func() {
			data := &map[string]string{}
			m := NewMqttMessage(MqttMessageTypePresence, data)
			g.Assert(m).IsNotNil()
			g.<PERSON>sert(m.Type).Equal(MqttMessageTypePresence)
			g.Assert(m.Data).Equal(data)
		})
	})
}

// SendMqttMessage send mqtt message
func TestSendMqttMessage(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("SendMqttMessage", func() {
		g.It("should succeed", func() {
			g.Timeout(45 * time.Second)
			httpmock.ActivateNonDefault(http.DefaultClient)
			defer httpmock.DeactivateAndReset()
			User1JWT := "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJleHAiOjE2MzU3NTg4ODYsImlhdCI6MTYzNTc1NTI4NiwianRpIjoiMzU2MTY0NzBiOTBjNDM3MDlkZWUzNTAyOGZhMmY4MzUiLCJ0dHkiOjAsInBpZCI6IjQwMjlhNmZmZTk5MjRmOTY5OTU1YWEyZTFjMDc4MmFhIiwiZ2lkIjoiYzdkY2Q2MjJjMmE2NGI2ODgyM2NjNTNmNDliYjEzYjkiLCJsb2MiOiJlbi1VUyIsImN0eSI6IkFzaGJ1cm4iLCJjdHIiOiJVUyIsImxhdCI6MzkuMDQ2OSwibG9uIjotNzcuNDkwMywicnRpIjoiZDlkMTBhYzhkMDhhNDRkMDhmYjgyM2E5ODcwNzUxNDUiLCJyZXgiOjE2MzU3NjI0ODYsImlzcyI6ImUzYzY0ZWU5MGI4MDQ0ZDJiYTM1ZjEyZWExNjFmYWU0Iiwic3ViIjoiYjI4N2U2NTU0NjFmNGIzMDg1YzhmMjQ0ZTM5NGZmN2UiLCJhdHkiOjMsImFncCI6NSwic2lkIjoiMjI0MmExOTAzZmUwNGE5Njg4N2M2YmJhMWI0NzRkMzIiLCJ2ZXIiOnRydWUsImFnciI6MTA3MCwiZG9iIjoiM2sweGxwK2FZUWtIVlBvcmlMT3V5Zz09In0."

			// mock request
			topic := "dna/user/foobar"
			message := `{"foo":"bar"}`
			msgType := MqttMessageTypePresence
			buf := &bytes.Buffer{}
			enc := json.NewEncoder(buf)
			enc.SetEscapeHTML(false)
			msg := NewMqttMessage(msgType, &message)
			enc.Encode(msg)
			bytes.TrimRight(buf.Bytes(), `%0A`)

			key := MockVMQApiRequest("t2gp", "publish", Params{
				Param{Key: "topic", Value: topic},
				Param{Key: "message", Value: buf.String()},
				Param{Key: "userid", Value: "b287e655461f4b3085c8f244e394ff7e"},
			}, http.StatusOK, `{"text":"Done","type":"text"}`)

			ctx := context.Background()
			token, _ := jwt.ParseJWTTokenWithoutValidation(User1JWT)
			ctx = context.WithValue(ctx, constants.BearerAuthJWT, token)
			ctx = context.WithValue(ctx, constants.BearerAuthString, User1JWT)

			err := SendMqttMessage(ctx, cfg, topic, msgType, &message)
			g.Assert(err).IsNil()

			info := httpmock.GetCallCountInfo()
			g.Assert(info[key]).Equal(1)
		})
	})
}
