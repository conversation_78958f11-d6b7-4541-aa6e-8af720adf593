package apipub

import (
	"testing"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
)

func TestBlocklist(t *testing.T) {
	g := goblin.Goblin(t)
	bl := BlocklistResponse{
		Userid:    "b287e655461f4b3085c8f244e394ff7e",
		Blockedid: "2017e9305ccc4e5781d076403c1b6725",
		Name:      aws.String("user#18515"),
	}

	g.Describe("ExistsInBlocklist", func() {
		g.It("should return expected values", func() {
			list := &[]*BlocklistResponse{
				&bl,
			}
			g.<PERSON><PERSON>(ExistsInBlocklistArr(nil, "userid")).IsFalse()
			g.<PERSON><PERSON><PERSON>(ExistsInBlocklistArr(list, "2017e9305ccc4e5781d076403c1b6725")).IsTrue()
			g.<PERSON><PERSON><PERSON>(ExistsInBlocklistArr(list, "baduserid")).IsFalse()
		})
	})
}
