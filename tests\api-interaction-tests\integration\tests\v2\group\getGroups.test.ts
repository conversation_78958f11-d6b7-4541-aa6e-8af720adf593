/* eslint-disable @typescript-eslint/no-explicit-any */
import * as socialApi from '../../../lib/social-api';
import { TwokAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let group1Id: string;
let usersTwok: TwokAccounts;

beforeEach(async () => {
  usersTwok = new TwokAccounts(3, ["leader", "member1", "member2"]);
  await usersTwok.loginAll({});

  let r = await socialApi.createGroup(
    usersTwok.acct["leader"],
    {
      maxMembers: 6,
      joinRequestAction: 'auto-approve',
      canCrossPlay: true
    }
  );
  socialApi.testStatus(StatusCodes.CREATED, r);
  group1Id = socialApi.getGroupId(r);

  // member1 joins the auto-approve group
  const resp = await socialApi.requestToJoin(
    usersTwok.acct["member1"],
    group1Id,
    {
      canCrossPlay: true
    }
  );
  socialApi.testStatus(StatusCodes.OK, resp);
});

afterEach(async done => {
  await socialApi.deleteGroup(usersTwok.acct["leader"], group1Id);
  await usersTwok.logoutAll({});
  done();
});

// eslint-disable-next-line max-lines-per-function
describe('[public v2]', () => {
  let group2Id: string;

  afterEach(async done => {
    await socialApi.deleteGroup(usersTwok.acct["member2"], group2Id);
    done();
  });

  it('Users exist as different roles in different groups[happy]', async () => {
    let testCase = {
      description: "user creates a group as leader; user joins other group as a member",
      expected: "user has different roles in different groups"
    };

    // Member2 create a group.
    let r = await socialApi.createGroup(
      usersTwok.acct["member2"],
      {
        maxMembers: 6,
        joinRequestAction: 'manual',
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.CREATED, r);

    group2Id = socialApi.getGroupId(r);

    // Member2 sends an invitation to leader.
    r = await socialApi.invite(
      usersTwok.acct["member2"],
      group2Id,
      {},
      usersTwok.acct["leader"].publicId
    );
    socialApi.testStatus(StatusCodes.OK, r);
    // Leader accepts the invitation.
    r = await socialApi.acceptInvite(
      usersTwok.acct["leader"],
      group2Id,
      usersTwok.acct["member2"].publicId,
      {
        canCrossPlay: true
      }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    const actualGroupInfo = await socialApi.getGroupsInfo(usersTwok.acct["leader"]);

    // Expect:leaderUser
    // group1: role leader
    // group2: role member
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        items: expect.arrayContaining([
          expect.objectContaining({
            members: expect.arrayContaining([
              expect.objectContaining({
                // groupid: group1Id,
                role: 'leader',
                userid: usersTwok.acct["leader"].publicId,
              }),
              expect.objectContaining({
                // groupid: group1Id,
                role: 'member',
                userid: usersTwok.acct["member1"].publicId,
              }),
            ]),
          }),

          expect.objectContaining({
            members: expect.arrayContaining([
              expect.objectContaining({
                // groupid: group2Id,
                role: 'leader',
                userid: usersTwok.acct["member2"].publicId,
              }),
              expect.objectContaining({
                // groupid: group2Id,
                role: 'member',
                userid: usersTwok.acct["leader"].publicId,
              }),
            ]),
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected groups or roles"
        }
      }
    );
  });

  it('the logged-in user is a member of the group, group info returned[happy trusted]', async () => {
    let testCase = {
      description: "get group info of the group the logged-in user is in",
      expected: "the group information is present"
    };

    // Leader get group information
    const actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["leader"], group1Id);

    // Expect:
    // leaderUser: role leader
    // member1User: role member
    const expectedGroupInfo = {
      status: StatusCodes.OK,
      body: {
        groupid: group1Id,
        joinRequestAction: 'auto-approve',
        members: expect.arrayContaining([
          expect.objectContaining({
            // groupid: group1Id,
            role: 'leader',
            userid: usersTwok.acct["leader"].publicId,
          }),
          expect.objectContaining({
            // groupid: group1Id,
            role: 'member',
            userid: usersTwok.acct["member1"].publicId,
          }),
        ]),
      },
    };
    socialApi.expectMore(
      () => {expect(actualGroupInfo).toMatchObject(expectedGroupInfo)},
      testCase,
      {
        resp: actualGroupInfo,
        additionalInfo: {
          "fail reason": "unexpected group information"
        }
      }
    );
  });
});

describe('[public v2]', () => {
  it('user is not a member of existing groups', async () => {
    // Member2 get group information
    const actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["member2"], group1Id);

    // The user is not a member of an existing group and cannot view group information
    socialApi.testStatus(StatusCodes.FORBIDDEN, actualGroupInfo);
  });

  it('user is a member of nonexisting groups[trusted]', async () => {
    // The leader looked at a non-existent group.
    const groupIdM = '01FS0YF8G91688T9VHZF4T3FZ0';
    const actualGroupInfo = await socialApi.getGroupInfo(usersTwok.acct["member1"], groupIdM);

    //Expect the leader cannot view a non-existent group.
    socialApi.testStatus(StatusCodes.NOT_FOUND, actualGroupInfo);
  });
});
