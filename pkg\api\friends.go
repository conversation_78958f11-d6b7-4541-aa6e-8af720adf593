package api

import (
	"context"
	"errors"
	"html"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/Philipp15b/go-steamapi"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/segmentio/encoding/json"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/messenger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/authheader"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils/validation"
)

// UpdateFriendStatus update friend status to block/friend only
func (api *SocialPublicAPI) UpdateFriendStatus(w http.ResponseWriter, r *http.Request, friendID string) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}

	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	// parse incoming friend
	defer r.Body.Close()
	body, _ := io.ReadAll(r.Body)
	var requestBody apipub.UpdateFriendStatusJSONRequestBody
	err := json.Unmarshal(body, &requestBody)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to unmarshal json").Send()
		errs.Return(w, r, errs.New(http.StatusBadRequest, errs.EJsonParse))
		return
	}

	var friend *apipub.FriendResponse
	friend, err = api.Cache.GetFriend(r.Context(), userid, friendID)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to get friend").Str("friendid", friendID).Send()
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EDynamodbReadFailed))
		return
	}

	if requestBody.Viewed != nil {
		friend.Viewed = requestBody.Viewed
		err = api.setFriendWithCache(r.Context(), friend, time.Duration(api.Cfg.TtlFriend)*time.Second)
		if err != nil {
			log.Error().Err(err).Str("event", "failed to put friend").Str("friendid", friendID).Send()
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbPutFailed))
			return
		}
	}

	friendProfile, _ := api.getUserProfile(r.Context(), friend.Friendid, true)
	//links filtering needs to be done all the time regardless of name existing or not.
	if friendProfile != nil {
		if friendProfile.Links != nil {
			friend.Links = friendProfile.Links
			identity.FilterLinksByOST(friend.Links, ost)
		}
		if friendProfile.DisplayName != nil {
			friend.Name = friendProfile.DisplayName
		}
	}

	//friendRet, err := utils.TypeConverter[apipub.SetFriendResponse](friend)
	//if err != nil {
	//	log.Error().Err(err).Interface("friend", friend).Msg("Error converting apipub.Friend to apipub.FriendViewReturn")
	//}

	ReturnEmptyOK(w, r)
}

func (api *SocialPublicAPI) GetFriend(w http.ResponseWriter, r *http.Request, friendID string) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	friend, err := api.Cache.GetFriend(r.Context(), userid, friendID)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to get friend from Cache checking dynamo").Str("userid", userid).Str("friendid", friendID).Send()
		friend, err = api.Ds.GetFriend(r.Context(), userid, friendID)
		errCode := 0
		var socErr *errs.Error
		if errors.As(err, &socErr) {
			errCode = socErr.GetHttpErrorCode()
		}
		if errCode == http.StatusNotFound {
			errs.Return(w, r, errs.New(http.StatusNotFound, errs.EFriendsNotFound))
			return
		} else if err != nil {
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EFriendsNotFound))
			return
		}
	}
	if friend == nil {
		errs.Return(w, r, errs.New(http.StatusNotFound, errs.EFriendsNotFound))
		return
	}

	friendProfile, _ := api.getUserProfile(r.Context(), friend.Friendid, true)
	//links filtering needs to be done all the time regardless of name existing or not.
	if friendProfile != nil {
		if friendProfile.Links != nil {
			friend.Links = friendProfile.Links
			identity.FilterLinksByOST(friend.Links, ost)
		}
		if friendProfile.DisplayName != nil {
			friend.Name = friendProfile.DisplayName
		}
	}

	var friendPresence []apipub.PresenceResponse
	if friend.Status != apipub.Pending {
		presences, err := api.Cache.GetUserPresences(r.Context(), friend.Friendid, productid)
		if err != nil {
			log.Error().Err(err).Str("event", "failed to get user presence").Str("friendid", friend.Friendid).Send()
		}
		if presences != nil && len(*presences) > 0 {
			//iterate through all user presnces and filter priority params and always also include user defined and launcher set
			for _, presence := range *presences {
				if presence != nil {
					friendPresence = append(friendPresence, *presence)
				}
			}
			friend.Presence = &friendPresence
		}
	}

	ReturnOK(w, r, friend)
}

// ListFriends list friend of the user
func (api *SocialPublicAPI) ListFriends(w http.ResponseWriter, r *http.Request, params apipub.ListFriendsParams) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	limit := api.Cfg.ListFriendsLimit
	if params.Limit != nil && *params.Limit >= 1 {
		limit = *params.Limit
	}
	limit64 := int64(limit)

	userid := token.Claims.Subject
	productid := token.Claims.ProductID
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	if params.Status != nil &&
		*params.Status != apipub.Friend &&
		*params.Status != apipub.Pending {
		log.Error().Str("event", "Invalid status").Str("status", string(*params.Status)).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidStatus))
		return
	}

	var friends *[]*apipub.FriendResponse
	next := aws.String("")

	var err error
	if api.Cache.FriendlistExistsInCache(r.Context(), userid) {
		friends, *next, err = api.Cache.GetFriends(r.Context(), userid, params.Status, &limit64, params.Next)
		if err != nil {
			if errs.IsEqual(err, errs.ERedisObjectMissing) {
				friends, next, _ = getFriends(api, r, userid, params.Status, limit, params.Next)
			} else {
				log.Error().Err(err).Str("event", "failed to get friends with status and limit").Send()
				errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
				return
			}
		}
	} else {
		friends, next, err = getFriends(api, r, userid, params.Status, limit, params.Next)
		if err != nil {
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed))
			return
		}
	}

	//Populate presence and display name if able.
	//Allocate empty friend with presence so itesms is always sent.
	friendsWithPresence := make([]apipub.FriendResponse, 0)
	if friends != nil {

		friendids := make([]string, 0, len(*friends))
		for _, friend := range *friends {
			friendids = append(friendids, friend.Friendid)
		}
		friendProfiles, _ := api.GetUserProfiles(r.Context(), friendids, false)
		var friendProfile *apipub.UserProfileResponse

		for _, friend := range *friends {
			friendProfile = nil
			if friendProfiles != nil {
				for _, p := range *friendProfiles {
					if p.Userid == friend.Friendid {
						friendProfile = p
						break
					}
				}
			}
			//links filtering needs to be done all the time regardless of name existing or not.
			if friendProfile != nil {
				if friendProfile.Links != nil {
					friend.Links = friendProfile.Links
					identity.FilterLinksByOST(friend.Links, ost)
				}
				if friendProfile.DisplayName != nil {
					friend.Name = friendProfile.DisplayName
				}
			}

			var friendPresence []apipub.PresenceResponse
			if friend.Status != apipub.Pending {
				presences, err := api.Cache.GetUserPresences(r.Context(), friend.Friendid, productid)

				if err != nil {
					log.Error().Err(err).Str("event", "failed to get user presence").Str("friendid", friend.Friendid).Send()
				}
				if presences != nil && len(*presences) > 0 {
					//iterate through all user presnces and filter priority params and always also include user defined and launcher set
					for _, presence := range *presences {
						if presence != nil {
							if presence.Priority == apipub.PresesencePriorityUserSet || presence.Priority == apipub.PresencePriorityLauncherAutomated || (presence.Priority >= apipub.PresencePriorityGameSetStart && presence.Priority <= apipub.PresencePriorityGameSetEnd) {
								friendPresence = append(friendPresence, *presence)
							}
						}
					}
					friend.Presence = &friendPresence
				}
			}
			friendsWithPresence = append(friendsWithPresence, *friend)
		}
	}
	var response apipub.FriendsNext
	response.Items = friendsWithPresence
	if next != nil && *next != "" {
		response.Nextid = next
	}
	ReturnOK(w, r, response)
}

// MakeFriend invite or accepts friendship
func (api *SocialPublicAPI) MakeFriend(w http.ResponseWriter, r *http.Request, pFriendid apipub.PFriendid) {
	tenant := identity.GetTenantFromCtx(r.Context(), api.Id)
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer
	productid := token.Claims.ProductID

	// decode request
	var makeFriend apipub.SetFriendsRequestBody
	if !DecodeBody(w, r, &makeFriend) {
		return
	}
	friendid := pFriendid

	// cannot friend self
	if friendid == userid {
		log.Info().Str("event", "cannot friend self").Str("userid", userid).Send()
		ReturnEmptyOK(w, r)
		return
	}

	// check friend userid is valid
	err := validation.Validate.VarCtx(r.Context(), friendid, validation.KValidateUserID)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to validate friend user Id").Str("userid", userid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsInvalidFriendID))
		return
	}

	profile, _ := api.getUserProfile(r.Context(), userid, true)

	// Check friend limit before continuing
	count, err := api.Cache.CountFriendlistMembers(r.Context(), userid)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to get friend count for").Str("userid", userid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsCannotGetCount))
		return
	}
	if count >= int64(api.Cfg.MaxFriends) {
		log.Error().Err(err).Str("event", "user has maximum friends").Int("maxFriends", api.Cfg.MaxFriends).Str("userid", userid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsMaxCountReached))
		return
	}

	// check friend's max friend limit
	count, err = api.Cache.CountFriendlistMembers(r.Context(), friendid)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to get friend count").Str("userid", friendid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsCannotGetCount))
		return
	}
	if count >= int64(api.Cfg.MaxFriends) {
		log.Error().Err(err).Str("event", "user has maximum friends").Int("maxFriends", api.Cfg.MaxFriends).Str("userid", friendid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsMaxCountReached))
		return
	}

	// check friend already friends
	dupeFriend, err := api.Cache.GetFriend(r.Context(), userid, friendid)
	if err != nil && !errs.IsEqual(err, errs.EFriendsNotFound) {
		log.Info().Err(err).Str("event", "Cache GetFriend failed").Str("userid", userid).Str("friendid", friendid).Send()
	}
	if dupeFriend != nil && dupeFriend.Status == apipub.Friend {
		log.Info().Err(err).Str("event", "user is already friends").Str("userid", userid).Str("friendid", friendid).Send()

		// no longer return error on already friends.  just return empty ok.
		ReturnEmptyOK(w, r)
		return
	}

	// check users blocklist
	isFriendBlocked, err2 := api.doesBlockerBlockBlockee(r.Context(), userid, friendid)
	if err2 != nil {
		log.Error().Err(err2).Str("event", "failed check blocker blocks blockee").Str("userid", userid).Str("friendid", friendid).Send()
		errs.Return(w, r, err2)
		return
	}
	if isFriendBlocked {
		log.Error().Str("event", "cant friend someone blocked").Str("userid", userid).Str("friendid", friendid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsCannotFriendFromBlocklist))
		return
	}
	message := ""
	if makeFriend.Message != nil {
		message = strings.TrimSpace(*makeFriend.Message)
	}
	message = html.EscapeString(message)
	var status string
	//if user blocked by friend
	isUserBlocked, err2 := api.doesBlockerBlockBlockee(r.Context(), friendid, userid)

	status, err = api.makeFriendWithCache(r.Context(), userid, friendid, message, isUserBlocked, ost)
	if err != nil {
		log.Error().Err(err2).Str("event", "failed to make friend").Str("userid", userid).Str("friendid", friendid).Send()
		errs.Return(w, r, errs.New(http.StatusUnprocessableEntity, errs.EFriendsCannotMakeFriend))
		return
	}

	switch apipub.FriendStatus(status) {
	case apipub.Pending:
		additionalInfo := make(map[string]string)
		if makeFriend.TeleMeta != nil {
			utils.ConvertMapInterfaceToMapString(*makeFriend.TeleMeta, &additionalInfo)
		}
		api.Tele.SendFriendEvent(r.Context(), telemetry.BuildFriendTeleMeta(telemetry.KFriendInvite, productid, userid, ost, []string{friendid}, &appid, &additionalInfo))

	case apipub.Friend:
		additionalInfo := make(map[string]string)
		if makeFriend.TeleMeta != nil {
			utils.ConvertMapInterfaceToMapString(*makeFriend.TeleMeta, &additionalInfo)
		}
		api.Tele.SendFriendEvent(r.Context(), telemetry.BuildFriendTeleMeta(telemetry.KFriendAccept, productid, userid, ost, []string{friendid}, &appid, &additionalInfo))
	}

	// subscribe to each other's presence
	if status == string(apipub.Friend) {

		userPresence := apipub.PresenceResponse{
			Userid:    userid,
			Productid: token.Claims.ProductID,
		}
		friendPresence := apipub.PresenceResponse{
			Userid:    friendid,
			Productid: token.Claims.ProductID,
		}

		messenger.Subscribe(r.Context(), api.Cfg, userid, friendPresence.Topic(tenant))
		messenger.Subscribe(r.Context(), api.Cfg, friendid, userPresence.Topic(tenant))
	}

	response := apipub.SetFriendResponse{
		Status: apipub.FriendStatus(status),
	}

	// Send Invite if invitee does not block inviter
	if !isUserBlocked {
		displayName := ""
		if profile != nil && profile.DisplayName != nil {
			displayName = *profile.DisplayName
		}
		api.sendFriendNotification(r.Context(), userid, friendid, status, message, &displayName, ost)
	}

	utils.WriteJsonResponse(w, r, http.StatusOK, response)
}

// DeleteFriend break frienship
func (api *SocialPublicAPI) DeleteFriend(w http.ResponseWriter, r *http.Request, friendid string) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	userid := token.Claims.Subject
	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)
	appid := token.Claims.Issuer
	productid := token.Claims.ProductID

	friend, err := api.Cache.GetFriend(r.Context(), userid, friendid)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to get friend from Cache checking dynamo").Str("userid", userid).Str("friendid", friendid).Send()
		friend, err = api.Ds.GetFriend(r.Context(), userid, friendid)
		errCode := 0
		var socErr *errs.Error
		if errors.As(err, &socErr) {
			errCode = socErr.GetHttpErrorCode()
		}
		if errCode == http.StatusNotFound {
			errs.Return(w, r, errs.New(http.StatusNotFound, errs.EFriendsNotFound))
			return
		} else if err != nil {
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EFriendsNotFound))
			return
		}
	}

	if friend != nil {
		// send notification before unfriend because the the subscription will be removed
		api.sendUnFriendNotification(r.Context(), userid, friendid)

		// track friend telemetry
		switch friend.Status {
		case apipub.Pending:
			additionalInfo := make(map[string]string)
			api.Tele.SendFriendEvent(r.Context(), telemetry.BuildFriendTeleMeta(telemetry.KFriendDecline, productid, userid, ost, []string{friendid}, &appid, &additionalInfo))
		case apipub.Friend:
			additionalInfo := make(map[string]string)
			api.Tele.SendFriendEvent(r.Context(), telemetry.BuildFriendTeleMeta(telemetry.KFriendRemove, productid, userid, ost, []string{friendid}, &appid, &additionalInfo))
		}

		err = api.makeUnFriendWithCache(r.Context(), userid, friendid)
		if err != nil {
			log.Error().Err(err).Str("event", "failed to unfriend").Str("userid", userid).Str("friendid", friendid).Send()
			errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.EFriendsGeneric))
			return
		}
	}
	ReturnEmptyOK(w, r)
}

// SteamDNAFriend tuple of steam profile and 2k profile
type SteamDNAFriend struct {
	ProfileSteam *steamapi.PlayerSummary       `json:"profileSteam"`
	ProfileDNA   *apipub.SearchAccountResponse `json:"profileDNA"`
}

// ImportPlatformFriends imports steam friends and matches them to 2K users
func (api *SocialPublicAPI) ImportPlatformFriends(w http.ResponseWriter, r *http.Request, params apipub.ImportPlatformFriendsParams) {
	log := logger.FromContext(r.Context())
	token, tErr := authheader.ParseJWTFromRequest(r)
	if tErr != nil {
		errs.Return(w, r, tErr)
		return
	}
	if !validation.IsFullSocialAccount(r.Context(), token) {
		errs.Return(w, r, errs.New(http.StatusForbidden, errs.EFullAccountRequired))
		return
	}

	ost := apipub.OnlineServiceType(token.Claims.OnlinePlatformType)

	// limit ids to 100
	ids := strings.Split(params.Id, ",")
	if len(ids) > 100 {
		ids = ids[0:100]
	}
	// remove duplicates
	ids = utils.UniqueStringArray(ids)

	// search DNA for platfrom friends
	var criterias []apipub.SearchAccountCriteria
	for _, platformID := range ids {
		criterias = append(criterias, apipub.SearchAccountCriteria{
			FirstPartyId:      aws.String(platformID),
			OnlineServiceType: aws.Int(int(ost)),
		})
	}
	searchRequestType := apipub.AccountsByFirstPartyId
	searchRequest := &apipub.SearchAccountRequest{
		Type:      &searchRequestType,
		Criterias: &criterias,
	}
	var searchResponse *apipub.SearchAccountResponseList
	searchResponse, err := api.Id.SearchAccounts(r.Context(), searchRequest)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to search accounts").Str("Id(s)", params.Id).Send()
		errs.Return(w, r, errs.New(http.StatusInternalServerError, errs.ERedisCacheGetFailed))
		return
	}

	searchResult := apipub.AccountsNext{
		Items: []apipub.SearchAccountResponse{},
	}

	// merge steam player summaries & 2k friend results
	if searchResponse != nil {
		for _, account := range *searchResponse {
			//skip if no parent accountid.  full accounts required to make friends.
			if account.ParentAccountId == nil || *account.ParentAccountId == "" {
				continue
			}
			//remove DOB and Email from return
			account.Dob = nil
			account.Email = nil
			//filter account links
			identity.FilterLinksByOST(account.Links, ost)

			searchResult.Items = append(searchResult.Items, account)
		}
	}

	ReturnOK(w, r, searchResult)
}

// sendFriendNotification send invite notification.  Added a parameter for display name to reduce calls to DNA if we already have the information
func (api *SocialPublicAPI) sendFriendNotification(ctx context.Context, fromid string, toid string, status string, message string, inviterDisplayName *string, inviterOST apipub.OnlineServiceType) {
	tenant := identity.GetTenantFromCtx(ctx, api.Id)
	invite := &apipub.MqttFriendStatusChange{
		Inviter:    &fromid,
		Message:    &message,
		Status:     &status,
		Name:       inviterDisplayName,
		InviterOST: &inviterOST,
		Inviteeid:  &toid,
	}

	if inviterDisplayName == nil {
		profile, _ := api.getUserProfile(ctx, fromid, true)
		if profile != nil {
			invite.Name = profile.DisplayName
		}
	}

	inviteeUser := apipub.UserProfileResponse{
		Userid: toid,
	}
	messenger.SendMqttMessage(ctx, api.Cfg, inviteeUser.Topic(tenant), messenger.MqttMessageTypeFriendInvite, invite)
}

// sendUnFriendNotification send friend removed notification.
func (api *SocialPublicAPI) sendUnFriendNotification(ctx context.Context, fromid string, toid string) {
	tenant := identity.GetTenantFromCtx(ctx, api.Id)
	invite := &apipub.MqttFriendStatusChange{
		Inviter:   &fromid,
		Status:    aws.String("unfriend"),
		Inviteeid: &toid,
	}

	profile, _ := api.getUserProfile(ctx, fromid, true)
	if profile != nil {
		invite.Name = profile.DisplayName

		messenger.SendMqttMessage(ctx, api.Cfg, profile.Topic(tenant), messenger.MqttMessageTypeFriendRemoved, invite)
	}
}

func (api *SocialPublicAPI) makeFriendWithCache(ctx context.Context, userid, friendid, message string, isUserBlocked bool, userOST apipub.OnlineServiceType) (string, error) {
	log := logger.FromContext(ctx)

	// save the friend to the store
	status, err := api.Ds.MakeFriend(ctx, userid, friendid, message, isUserBlocked, userOST)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to save friend to database").Str("userid", userid).Str("friendid", friendid).Send()
		return "", err
	}

	// save the friend to the Cache
	err = api.Cache.MakeFriend(ctx, userid, friendid, message, isUserBlocked, userOST, time.Duration(api.Cfg.TtlFriend)*time.Second)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to save friend to Cache").Str("userid", userid).Str("friendid", friendid).Send()
		return "", err
	}

	return status, nil
}

func (api *SocialPublicAPI) makeUnFriendWithCache(ctx context.Context, userid, unfriendid string) error {
	log := logger.FromContext(ctx)

	// save the unfriend to the store
	err := api.Ds.MakeUnfriend(ctx, userid, unfriendid)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to unfriend in database").Str("userid1", userid).Str("userid2", unfriendid).Send()
		return err
	}

	// save the unfriend to the Cache.  Do not return an
	err2 := api.Cache.MakeUnfriend(ctx, userid, unfriendid)
	if err2 != nil {
		log.Error().Err(err2).Str("event", "failed to unfriend in redis").Str("userid1", userid).Str("userid2", unfriendid).Send()
	}

	return nil
}

func (api *SocialPublicAPI) setFriendWithCache(ctx context.Context, friend *apipub.FriendResponse, ttl time.Duration) error {
	log := logger.FromContext(ctx)

	// save the friend to the store
	err := api.Ds.PutItemInProfileTable(ctx, friend)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to save friend to db").Str("userid", friend.Userid).Str("friendid", friend.Friendid).Send()
		return err
	}

	// save the friend to the Cache
	err = api.Cache.SetFriend(ctx, friend, ttl)
	if err != nil {
		log.Error().Err(err).Str("event", "failed to save block list to Cache").Str("userid", friend.Userid).Str("friendid", friend.Friendid).Send()
		return err
	}

	return nil
}

func getFriends(api *SocialPublicAPI, r *http.Request, userid string, status *apipub.FriendStatus, limit int, next *string) (*[]*apipub.FriendResponse, *string, error) {

	log := logger.FromContext(r.Context())
	results, next, err := api.Ds.GetFriends(r.Context(), userid, status, limit, next)
	if err != nil {
		statusStr := ""
		if status != nil {
			statusStr = string(*status)
		}
		log.Error().Err(err).Str("event", "failed to get friends from dynamo").Str("userid", userid).Str("status", statusStr).Send()
		return nil, nil, err
	}

	// Note: a race condition may happen depending on the speed of quering all friends from DynamoDb and
	// caching such a list in Redis. If a follow up pagenation request arrives prior to the completion of
	// caching, another full list sync will be triggered.
	// TODO: move the sync to backend service.
	go SyncFullFriendList(r, api, userid)
	return results, next, nil
}

// SyncFullFriendList function to sync friendslist from dynamo to redis
func SyncFullFriendList(r *http.Request, api *SocialPublicAPI, userid string) {
	newCtx := context.WithoutCancel(context.Background())
	newLog := logger.FromContext(newCtx)

	lockKey := "user:" + userid + ":lock:friends"
	lockTTL := time.Duration(api.Cfg.RedisLockDuration) * time.Second
	lock := api.Cache.GetSyncLock(newCtx, lockKey, lockTTL)
	if lock == nil {
		newLog.Warn().Str("event", "Friend list sync already started for user").Str("userid", userid).Send()
		return
	}
	defer lock.Release(newCtx)

	//clear friendlist in Cache
	err := api.Cache.ClearFriendlist(newCtx, userid)
	if err != nil {
		newLog.Error().Err(err).Str("userid", userid).Str("event", "failed to clear friend list in Cache").Send()
		return
	}

	// get a full list before setting the Cache.
	results, dbErr := api.Ds.GetFullFriendList(newCtx, userid)
	if dbErr != nil {
		newLog.Error().Err(dbErr).Str("userid", userid).Str("event", "failed to get friends from dynamo").Send()
		return
	}

	// FixPendingFriends flag only used by SSO sync tool
	if results != nil && api.Cfg.FixPendingFriends {
		var updateFriends []*apipub.FriendResponse
		for i, friend := range *results {
			//if user sent an invite and the friend is pending, check friend to see if their status is friend and update friendlist
			//resolves hanging pending friends in dynamo t2gp-2770
			if friend.Invitee != userid && friend.Status == apipub.Pending {
				revFriend, revErr := api.Ds.GetFriend(newCtx, friend.Friendid, userid)
				if revErr != nil {
					newLog.Error().Err(revErr).Str("userid", userid).Str("friendid", friend.Friendid).Str("event", "failed to get friend from dynamo").Send()
					continue
				}
				if revFriend != nil {
					if revFriend.Status == apipub.Friend {
						//update the incorrect friendship
						(*results)[i].Status = apipub.Friend
						updateFriends = append(updateFriends, (*results)[i])
					}
				}
			}
		}

		if len(updateFriends) > 0 {
			//update any friends in dynamo who have incorrect friendships
			err = api.Ds.UpdateFriends(newCtx, &updateFriends)
			if err != nil {
				newLog.Error().Err(err).Str("userid", userid).Str("event", "failed to update friends in dynamo").Send()
			}
		}
	}

	api.Cache.SetFriends(newCtx, results, time.Duration(api.Cfg.TtlFriend)*time.Second)
	newLog.Info().Str("event", "Friend list has been sycned").Str("userid", userid).Send()

	//update userCacheMetadata
	userMeta, metaErr := api.Cache.GetUserCacheMeta(newCtx, userid)
	if metaErr != nil {
		newLog.Error().Err(metaErr).Str("userid", userid).Msg("get userMeta err")
	}
	bInitialMeta := false
	if userMeta == nil {
		bInitialMeta = true
		userMeta = &apipub.UserCacheMeta{
			Friends: false,
			Pending: false,
			Blocks:  true,
		}
	}
	bOldFriends := userMeta.Friends
	bOldPending := userMeta.Pending

	if results != nil && len(*results) > 0 {
		bFoundFriend := false
		bFoundPending := false

		for _, friend := range *results {
			if friend.Status == apipub.Friend {
				bFoundFriend = true
			} else if friend.Status == apipub.Pending {
				bFoundPending = true
			}
		}

		userMeta.Friends = bFoundFriend
		userMeta.Pending = bFoundPending
	} else {
		//no friends or pending
		userMeta.Friends = false
		userMeta.Pending = false
	}

	if bInitialMeta || bOldFriends != userMeta.Friends || bOldPending != userMeta.Pending {
		metaErr = api.Cache.SetUserCacheMeta(newCtx, userid, userMeta, time.Duration(api.Cfg.TtlProfile)*time.Second)
		if metaErr != nil {
			newLog.Error().Err(metaErr).Str("userid", userid).Msg("save userMeta err")
		}
	}
}
