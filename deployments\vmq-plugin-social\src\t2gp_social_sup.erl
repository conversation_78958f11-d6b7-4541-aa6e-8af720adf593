%%====================================================================
%% @doc t2gp_social_plugin top level supervisor.
%% @end
%%====================================================================

-module(t2gp_social_sup).

-include_lib("t2gp_social.hrl").

-behaviour(supervisor).

-export([start_link/0]).

-export([init/1]).

-define(SERVER, ?MODULE).
-define(CHILD(Id, Mod, Type, Args), {Id, {Mod, start_link, Args}, permanent, 5000, Type, [Mod]}).

-spec start_link() -> supervisor:startlink_ret().
start_link() ->
    supervisor:start_link({local, ?SERVER}, ?MODULE, []).

%% sup_flags() = #{strategy => strategy(),         % optional
%%                 intensity => non_neg_integer(), % optional
%%                 period => pos_integer()}        % optional
%% child_spec() = #{id => child_id(),       % mandatory
%%                  start => mfargs(),      % mandatory
%%                  restart => restart(),   % optional
%%                  shutdown => shutdown(), % optional
%%                  type => worker(),       % optional
%%                  modules => modules()}   % optional
-spec init(term()) -> {ok, {supervisor:sup_flags(), [supervisor:child_spec()]}}.
init([]) ->
    SupFlags =
        #{strategy => one_for_one, intensity => 1000, period => 10},
    ChildSpecs = [
        #{
            id => t2gp_social_db,
            start => {t2gp_social_db, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_db]
        },
        #{
            id => t2gp_social_dna,
            start => {t2gp_social_dna, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_dna]
        },
        #{
            id => t2gp_social_pd,
            start => {t2gp_social_pd, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_pd]
        },
        #{
            id => t2gp_social_rsg,
            start => {t2gp_social_rsg, start_link, []},
            restart => permanent,
            type => worker,
            modules => [t2gp_social_rsg]
        }
    ],
    {ok, {SupFlags, ChildSpecs}}.
%% internal functions
