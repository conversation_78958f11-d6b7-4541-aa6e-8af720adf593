// Package authheader in utils is for utility functions of getting auth information from headers
package authheader

import (
	"net/http"
	"strings"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/aws/aws-sdk-go-v2/aws"
	jwtgo "github.com/golang-jwt/jwt/v4"

	"github.com/lestrrat-go/jwx/jwk"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// PDClaims jwt claims for private divition
//
//	"aud": "761e734e-29db-4d24-8b87-9beaecfd896a",
//	"exp": 1656006136,
//	"iat": 1656002536,
//	"iss": "privatedivision.com",
//	"sub": "aa7393be-778c-4404-a0ad-6c06e9a9ac2c",
//	"jti": "56f1f91f-de4f-4e57-b9d0-53489ec754a4",
//	"authenticationType": "PASSWORD",
//	"email": "<EMAIL>",
//	"email_verified": true,
//	"preferred_username": "yulius",
//	"applicationId": "761e734e-29db-4d24-8b87-9beaecfd896a",
//	"scope": "offline_access",
//	"roles": [],
//	"auth_time": 1656002536,
//	"tid": "71d68c88-a4f1-44e9-bdc8-a57cd4001f3f"
type PDClaims struct {
	Audience           string   `json:"aud"`
	Expire             int      `json:"exp"`
	IssuedAt           int      `json:"iat"`
	Issuer             string   `json:"iss"`
	Subject            string   `json:"sub"`
	JwtID              string   `json:"jti"`
	AuthenticationType string   `json:"authenticationType"`
	Email              string   `json:"email"`
	EmailVerified      bool     `json:"email_verified"`
	PreferredUsername  string   `json:"preferred_username"`
	AuthTime           int      `json:"auth_time"`
	TransactionID      string   `json:"tid"`
	ApplicationId      string   `json:"applicationId"`
	Roles              []string `json:"roles"`
	Scope              string   `json:"scope"`
	Platform           string   `json:"platform"`
}

func (c *PDClaims) Valid() error {
	return nil
}

type RSClaims struct {
	NameID          string `json:"nameid"`
	TokenStorageTTL string `json:"scAuth.TokenStorageTtl"`
	AvatarURL       string `json:"scAuth.AvatarUrl"`
	Nickname        string `json:"scAuth.Nickname"`
	IsMinor         string `json:"scAuth.IsAMinor"`
	NotBefore       int    `json:"nbf"`
	Scope           string `json:"scope"`
	ExpiresIn       int    `json:"exp"`
	IssuedAt        int    `json:"iat"`
	Issuer          string `json:"iss"`
}

func (c *RSClaims) Valid() error {
	return nil
}

// GetAuthorizationHeaderStr get value of Authorization header of request
func GetAuthorizationHeaderStr(r *http.Request) (*string, *errs.Error) {
	log := logger.Get(r)
	auth := r.Header.Get("Authorization")
	if len(auth) <= 0 {
		log.Error().Err(errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)).Msg("no Authorization header provided")
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)
	}
	return aws.String(auth), nil
}

// GetAuthHeaderArr get array split (2) of Authorization header.  Used to make sure the Auth header has a splitable value for parsing.
func GetAuthHeaderArr(r *http.Request) ([]string, *errs.Error) {
	log := logger.FromContext(r.Context())
	auth, err := GetAuthorizationHeaderStr(r)
	if err != nil {
		return nil, err
	}
	authSplit := strings.Split(*auth, " ")
	if len(authSplit) != 2 {
		if auth == nil {
			auth = aws.String("")
		}
		log.Error().Err(errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)).Str("auth", *auth).Msg("Authorization has invalid format")
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)
	}
	return authSplit, nil
}

// ParseJWTFromRequest parses the Authorization header and returns the JWT if it is in Bearer <value> format.
func ParseJWTFromRequest(r *http.Request) (*jwt.Token, *errs.Error) {
	log := logger.Get(r)
	auth, err := GetAuthHeaderArr(r)
	if err != nil {
		return nil, err
	}
	if len(auth) == 2 && auth[0] == "Bearer" {
		token, err := jwt.ParseJWTTokenWithoutValidation(auth[1])
		if err != nil {
			log.Error().Err(err).Msgf("JWT parse error %s", auth[1])
			return nil, errs.New(http.StatusUnauthorized, errs.EInvalidJWT)
		}
		if utils.StringContainsSubstr(token.Claims.Issuer, "privatedivision") {
			claims := PDClaims{}
			if _, _, err = jwtgo.NewParser(jwtgo.WithoutClaimsValidation()).ParseUnverified(auth[1], &claims); err == nil {
				CopyClaimsFromPdToDnaToken(token, claims)
			} else {
				log.Error().Err(err).Str("token", auth[1]).Str("event", "error parsing pdid token").Msg("error parsing pdid token")
			}
		}
		// promote platform id to full account if account has been linked
		if token.Claims.DnaFullAccountID != "" {
			token.Claims.Subject = token.Claims.DnaFullAccountID
		}
		return token, nil
	}
	log.Error().Err(errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)).Strs("auth", auth).Msg("Authorization has invalid format")
	return nil, errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)
}

func ParseServerJWTFromRequest(r *http.Request, jwks jwk.Set) (*jwt.Claims, *errs.Error) {
	log := logger.Get(r)
	auth, err := GetAuthHeaderArr(r)
	if err != nil {
		return nil, err
	}
	if len(auth) == 2 && auth[0] == "Bearer" {
		claims, err := jwt.ValidateJWTTokenRS256(auth[1], jwks, false)
		if err != nil || claims == nil {
			log.Error().Err(err).Msgf("JWT parse error %s", auth[1])
			return nil, errs.New(http.StatusUnauthorized, errs.EInvalidJWT)
		}

		// The token type of server JWT is 3
		// https://2kgames.atlassian.net/wiki/spaces/TS/pages/*********/SSO+JWTs
		if claims.TokenType != 3 {
			log.Error().Err(err).Msgf("Token type %d is not Server", claims.TokenType)
			return nil, errs.New(http.StatusUnauthorized, errs.EInvalidJWT)
		}

		return claims, nil
	}
	log.Error().Err(errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)).Strs("auth", auth).Msg("Authorization has invalid format")
	return nil, errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)
}

func CopyClaimsFromPdToDnaToken(token *jwt.Token, pdClaims PDClaims) {
	token.Claims.ProductID = pdClaims.Audience
	token.Claims.OnlinePlatformType = PdGetOst(pdClaims.Platform)
}

// PdGetOst converts platform from pd token to dna ost
// JWTs coming from a first party should have the platform field filled out.
// Values should correspond to list for authenticationType which is not guaranteed
// https://fusionauth.io/docs/v1/tech/oauth/tokens
func PdGetOst(platform string) int {
	switch strings.ToLower(platform) {
	case "xbox":
		return 1
	case "sonypsn":
		return 2
	case "steam":
		return 3
	case "nintendo":
		return 11
	case "epicgames":
		return 15
	case "twitch":
		return 20
	case "password":
		fallthrough //do we want to use T2GP type here?
	case "refresh_token":
		fallthrough
	default:
		return 0
	}
}
