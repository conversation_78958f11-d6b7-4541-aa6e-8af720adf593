{"name": "@take-two-t2gp/t2gp-social-frontend", "version": "1.19.14", "svelte": "src/index.ts", "module": "dist/index.mjs.js", "main": "dist/index.umd.min.js", "types": "dist/index.d.ts", "files": ["dist/**/*"], "scripts": {"build": "rm -rf dist && rollup -c && tsc --project tsconfig.declaration.json --resolveJsonModule --declaration --emitDeclarationOnly --outDir dist", "dev": "npm run validate && rollup -c -w", "start": "sirv public -s", "validate": "svelte-check", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "npm test -- --watch", "lint:all": "eslint --max-warnings 0 \"src/**/*.{js,svelte}\"", "lint:patch": "patch-package"}, "devDependencies": {"@babel/core": "^7.12.3", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.1", "@babel/preset-typescript": "^7.12.1", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^11.0.0", "@commitlint/lint": "^11.0.0", "@rollup/plugin-alias": "^3.1.1", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^14.0.0", "@rollup/plugin-image": "^2.0.5", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^8.0.0", "@rollup/plugin-typescript": "^6.0.0", "@take-two-t2gp/d2c-ui-library": "^75.1.8", "@take-two-t2gp/eslint-config-t2gp": "^5.0.0", "@take-two-t2gp/t2gp-strings": "^7.13.1", "@testing-library/jest-dom": "^5.11.5", "@testing-library/svelte": "^3.0.0", "@tsconfig/svelte": "^1.0.0", "@types/jest": "^26.0.15", "@types/lodash.debounce": "^4.0.6", "@types/node": "^14.14.21", "@types/pubsub-js": "^1.8.1", "@typescript-eslint/eslint-plugin": "^4.14.2", "@typescript-eslint/parser": "^4.14.2", "babel-plugin-macros": "^2.8.0", "cross-env": "^7.0.2", "danger": "^10.5.0", "eslint": "^7.19.0", "eslint-plugin-local-rules": "^0.1.1", "eslint-plugin-svelte3": "^3.0.0", "eslint-svelte3-preprocess": "0.0.4", "filesize": "^6.1.0", "jest": "^26.6.0", "jest-transform-stub": "^2.0.0", "patch-package": "^6.2.2", "prettier": "^2.2.1", "prettier-plugin-svelte": "^2.1.1", "rollup": "^2.3.4", "rollup-plugin-analyzer": "^3.3.0", "rollup-plugin-css-only": "^3.1.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-replace": "^2.2.0", "rollup-plugin-size-snapshot": "^0.12.0", "rollup-plugin-svelte": "^7.1.0", "rollup-plugin-svelte-svg": "^0.2.3", "rollup-plugin-terser": "^7.0.0", "sirv-cli": "^1.0.0", "stylelint": "^13.9.0", "svelte": "^3.48.0", "svelte-check": "^1.1.32", "svelte-jester": "^1.3.0", "svelte-preprocess": "^4.6.1", "ts-jest": "^26.4.2", "tslib": "^2.0.0", "twin.macro": "^2.8.2", "typescript": "^4.1.3"}, "dependencies": {"@babel/runtime": "^7.12.1", "@sveltestack/svelte-query": "^1.0.4", "emotion": "^10.0.27", "i18next": "^19.8.3", "lodash.debounce": "^4.0.8", "mqtt": "4.3.7", "pubsub-js": "^1.9.1", "svelte-routing": "^1.5.0"}, "browserslist": ["defaults", "not IE 11", "not IE_Mob 11", "maintained node versions"], "release": {"plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/changelog", {"changelogFile": "CHANGELOG.md"}], "@semantic-release/npm", ["@semantic-release/git", {"assets": ["CHANGELOG.md", "package.json", "package-lock.json"]}], "@semantic-release/github"]}, "repository": {"type": "git", "url": "**************:take-two-t2gp/t2gp-social-frontend.git"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}}