#!/usr/bin/env python3
import re
import api
import testdb
import config
import json
from functools import partial
import traceback
import time
import os
import sys
sys.path.append("../reporting")
import post_to_slack
from datetime import datetime
import pytz


# TODO: add "link" as user profile/friend common attribute

# TODO: unicode char in names?

# TODO
# Auth test cases:
# tests regarding tokens e.g. when log out, token becomes invalid

# TODO: put this in config; resolve circular import
# TODO: access DNA server directly instead of using the login API
# Get account ID.
def getAccountId(email, password):
    resp = api.post_auth_login(email, password)
    api.post_auth_logout(resp.json()['accessToken'])
    return resp.json()['accountId']


# TODO: use this on everything (not just get_user_profile_no_change())
# Delete everything related to a user profile (namely user#/user# and
# email#/email#), and let login recreates them. Returns an access token.
def clean_login():
    keys_list = [("email#{}".format(email), "email#{}".format(email)),
                 ("user#{}".format(account_id), "user#{}".format(account_id))]
    testdb.delete_account_items(keys_list)
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']
    return accessToken


# TODO: add "group" mode
# "f" mode (friend): mode_param is a tuple of user account id and
#  friend id starting number
# "u" mode (user profile): mode_param is user id starting number.
# "e" mode (user email): mode_param is email
# "b" mode (blocklist): mode_param is user account id
def create_dynamodb_item_list(mode, mode_param, attr_list, multiple):
    if mode == "f":
        uid, fid_start_num = mode_param
        pk_prefix = "user#"
        sk_prefix = "friend#"
        def pk_num(cnt):
            return uid
        def sk_num(cnt):
            try:
                sk_num = str(fid_start_num + cnt).zfill(32)
            except TypeError:
                # assume complete id number as str
                sk_num = fid_start_num
            return sk_num
    elif mode == "u":
        uid_start_num = mode_param
        pk_prefix = "user#"
        sk_prefix = "user#"
        def pk_num(cnt):
            return str(uid_start_num + cnt).zfill(32)
        def sk_num(cnt):
            return str(uid_start_num + cnt).zfill(32)
    elif mode == "e":
        email = mode_param
        pk_prefix = "email#"
        sk_prefix = "email#"
        def pk_num(cnt):
            return email
        def sk_num(cnt):
            return email
    elif mode == "b":
        uid = mode_param
        pk_prefix = "user#"
        sk_prefix = "blocklist#"
        def pk_num(cnt):
            return uid
        def sk_num(cnt):
            return uid

    item_list = []
    counter = 0
    for attr in attr_list:
        for i in range(multiple):
            item_list.append(
                ("{}{}".format(pk_prefix, pk_num(counter)),
                 "{}{}".format(sk_prefix, sk_num(counter)),
                 attr)
            )
            counter += 1
    return item_list


################################################################################
# POST /auth/login: profile not exists
#
# verify that if user profile doesn't exist, a new one is created.
################################################################################
def post_auth_login_profile_not_exists():
    keys_list = [("email#{}".format(email), "email#{}".format(email)),
                 ("user#{}".format(account_id), "user#{}".format(account_id))]

    @testdb.test_create(keys_list)
    def test(account_info):
        return api.post_auth_login(account_info["email"],
                                   account_info["password"])

    test(config.dna_account_info)


################################################################################
# POST /auth/login: profile exists
#
# verify that if user profile exists, selected attr are updated.
################################################################################
def post_auth_login_profile_exists():
    user_values_changed = {
        'email' : 'u_dummy_email',
        'lastLogin' : '2022-02-22T22:22:22Z',
        'locale' : 'u_dummy_locale',
        'type' : 'u_dummy_type',
        'userid' : 'u_dummy_userid'
    }

    user_values_unchanged = {
        'created' : '2011-11-11T11:11:11Z',
        'name' : 'u_dummy_name',
        'presence' : {"timestamp" : "2012-12-12T12:12:12Z",
                      "status" : "u_dummy_status"}
    }

    email_values_changed = {
        'email' : 'e_dummy_email',
        'timestamp' : 'e_dummy_timestamp',
        'type' : 'e_dummy_type',
        'userid' : 'e_dummy_userid'
    }

    email_values_unchanged = {
    }

    keys_list = [("email#{}".format(email),
                  "email#{}".format(email),
                  email_values_changed,
                  email_values_unchanged),
                 ("user#{}".format(account_id),
                  "user#{}".format(account_id),
                  user_values_changed,
                  user_values_unchanged)]

    @testdb.test_modify(keys_list)
    def test(account_info):
        return api.post_auth_login(account_info["email"],
                                   account_info["password"])

    test(config.dna_account_info)


################################################################################
# POST /auth/logout: no change
#
# verify no changes to profile
################################################################################
def post_auth_logout_no_change():
    accessToken = clean_login()

    keys_list = [("email#{}".format(email), "email#{}".format(email)),
                 ("user#{}".format(account_id), "user#{}".format(account_id))]

    @testdb.test_no_change(keys_list)
    def test(accessToken):
        return api.post_auth_logout(accessToken)

    test(accessToken)


################################################################################
# POST /auth/refresh: no change
#
# verify no changes to profile
################################################################################
def post_auth_refresh_no_change():
    response = api.post_auth_login(email, password)
    refreshToken = response.json()['refreshToken']

    keys_list = [("email#{}".format(email), "email#{}".format(email)),
                 ("user#{}".format(account_id), "user#{}".format(account_id))]

    @testdb.test_no_change(keys_list)
    def test(refreshToken):
        return api.post_auth_refresh(refreshToken)

    test(refreshToken)


################################################################################
# GET /user/profile: no change
#
# verify no changes to profile
################################################################################
def get_user_profile_no_change():
    accessToken = clean_login()

    keys_list = [("email#{}".format(email), "email#{}".format(email)),
                 ("user#{}".format(account_id), "user#{}".format(account_id))]

    @testdb.test_no_change(keys_list)
    def test(accessToken):
        return api.get_user_profile(accessToken)

    test(accessToken)


################################################################################
# PATCH /user/profile: update attr
#
# Verify the user profile attributes are updated correctly.
################################################################################
def patch_user_profile_update_attr():
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    user_values_patch = {
        'email' : '<EMAIL>',
        'lastLogin' : '3022-02-22T22:22:22Z',
        'locale' : 'p_dummy_locale',
        'created' : '3011-11-11T11:11:11Z',
        'name' : 'p_dummy_name',
        'presence' : {"timestamp" : "3010-10-10T10:10:10Z",
                      "status" : "p_dummy_status"},
        'type' : 'p_dummy_type',
        'userid' : 'p_dummy_userid'
    }

    user_values_changed = {
        # note: not checking timestamp since it's updated to the current time.
        'presence' : {"status" : "dummy_status"}
    }

    user_values_unchanged = {
        'email' : '<EMAIL>',
        'lastLogin' : '2022-02-22T22:22:22Z',
        'locale' : 'dummy_locale',
        'created' : '2011-11-11T11:11:11Z',
        'name' : 'dummy_name',
        'type' : 'user',       # must use 'user'
        'userid' : account_id  # must use real id
    }

    keys_list = [("user#{}".format(account_id),
                  "user#{}".format(account_id),
                  user_values_changed,
                  user_values_unchanged)]

    @testdb.test_modify(keys_list)
    def test(accessToken, changes):
        return api.patch_user_profile(accessToken, changes)

    test(accessToken, user_values_patch)


################################################################################
# GET /friends: friend updated
#
# Verify that, with no unmarshal errors for the friend and user profile, a
# friend profile is updated with selected attributes from the user profile.
# Verify that presence info is reported only for "friend" status
################################################################################
def get_friends_friend_updated(multiple):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    # create 2 friends - one with status friend and the other not.
    friend = config.friend_attr.copy()
    friend["status"] = "friend"
    fattr_list = [friend]

    friend = config.friend_attr.copy()
    fattr_list += [friend]

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, multiple)

    # create 2 user profiles for the 2 friends
    uattr_list = [config.user_profile_attr] * 2
    ulist = create_dynamodb_item_list("u", 1, uattr_list, multiple)

    items_list = flist + ulist

    def verification(response):
        res_dict = json.loads(response.text)
        f_match_cnt = 0
        nf_match_cnt = 0
        for friend in res_dict["items"]:
            if friend["status"] == "friend":
                for k in config.common_attr:
                    if friend[k] == config.user_profile_attr[k]:
                        f_match_cnt += 1
            else:
                for k in config.common_attr:
                    if k == "presence":
                        if friend[k]["status"] == config.user_profile_attr[k]["status"]:
                            raise testdb.TestFail("presence reported for non-friend ", response)
                    else:
                        if friend[k] == config.user_profile_attr[k]:
                            nf_match_cnt += 1

        if f_match_cnt != len(config.common_attr)*multiple:
            raise testdb.TestFail("(friend) not all attr updated", response)

        if nf_match_cnt != (len(config.common_attr)-1)*multiple:
            raise testdb.TestFail("(non-friend) not all attr updated", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, None)

    test(accessToken)


################################################################################
# GET /friends: bad friend
#
# Verify that, with unmarshal errors for a friend record, the erroneous friend
# record is not listed, while other correct ones are.
################################################################################
def get_friends_bad_friend(multiple):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    attr_list = []
    # create one friend record with an attr with the wrong data type.
    friend = config.friend_attr.copy()
    # use string instead of bool, to purposely inject the wrong data type.
    friend["viewed"] = "False"
    # marking this record as "wrong_data_type". "message" has no significance.
    # Any attribute that isn't overwritten by User Profile is fine.
    friend["message"] = "wrong_data_type"
    attr_list.append(friend)

    # create a few other friend records that are normal (no wrong data types).
    right_friend_record_cnt = 5
    for i in range(right_friend_record_cnt):
        friend = config.friend_attr.copy()
        friend["message"] = "right_data_type"
        attr_list.append(friend)

    flist = create_dynamodb_item_list("f", (account_id, 1), attr_list, multiple)

    # create associated user profiles
    attr_list = [config.user_profile_attr] * (1+right_friend_record_cnt)
    ulist = create_dynamodb_item_list("u", 1, attr_list, multiple)

    items_list = flist + ulist

    def verification(response):
        res_dict = json.loads(response.text)
        right_cnt = 0
        wrong_cnt = 0
        for friend in res_dict["items"]:
            if friend["message"] == "right_data_type":
                right_cnt += 1
            elif friend["message"] == "wrong_data_type":
                wrong_cnt += 1
        if (right_cnt != right_friend_record_cnt*multiple or
            wrong_cnt != 0):
            raise testdb.TestFail("right:{}|wrong:{}".format(right_cnt,
                                                             wrong_cnt),
                                                             response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, None)

    test(accessToken)


################################################################################
# GET /friends: bad user profile
#
# Verify that, with unmarshal errors for a user profile record, the associated
# friend record is not listed, while other correct ones are.
################################################################################
def get_friends_bad_user_profile(multiple):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    fattr_list = []
    uattr_list = []
    # create one user profile record with an attr with the wrong data type.
    friend = config.friend_attr.copy()
    user_profile = config.user_profile_attr.copy()
    # use bool instead of string, to purposely inject the wrong data type.
    # need to use one of the common attributes between friend and user profile.
    user_profile["name"] = False
    # marking this record as "wrong_data_type". "message" has no significance.
    # Any attribute that isn't overwritten by User Profile is fine.
    friend["message"] = "wrong_data_type"
    # status has to be friend for presence to be copied from user profile.
    friend["status"] = "friend"
    fattr_list.append(friend)
    uattr_list.append(user_profile)

    # create a few other friend records that are normal (no wrong data types).
    right_friend_record_cnt = 5
    for i in range(right_friend_record_cnt):
        friend = config.friend_attr.copy()
        user_profile = config.user_profile_attr
        friend["message"] = "right_data_type"
        friend["status"] = "friend"
        fattr_list.append(friend)
        uattr_list.append(user_profile)

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, multiple)
    ulist = create_dynamodb_item_list("u", 1, uattr_list, multiple)

    items_list = flist + ulist

    def verification(response):
        res_dict = json.loads(response.text)
        right_cnt = 0
        wrong_cnt = 0
        for friend in res_dict["items"]:
            if friend["message"] == "right_data_type":
                for k in config.common_attr:
                    if friend[k] == config.user_profile_attr[k]:
                        right_cnt += 1
            elif friend["message"] == "wrong_data_type":
                for k in config.common_attr:
                    if friend[k] == config.friend_attr[k]:
                        wrong_cnt += 1
        if (right_cnt != right_friend_record_cnt*len(config.common_attr)*multiple or
            wrong_cnt != 1*len(config.common_attr)*multiple):
            raise testdb.TestFail("right:{}|wrong:{}".format(right_cnt,
                                                             wrong_cnt),
                                                             response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, None)

    test(accessToken)


################################################################################
# GET /friends: Test no filter.
#
# with no "status" filter
#  defined and not-defined status names, 1 each
#  defined and not-defined status names, >1 each
#
# verify the friend list appear correctly
################################################################################
def get_friends_no_filter(multiple):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    fattr_list = []
    uattr_list = []
    for status in config.status_list:
        friend = config.friend_attr.copy()
        user_profile = config.user_profile_attr
        friend["status"] = status
        fattr_list.append(friend)
        uattr_list.append(user_profile)

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, multiple)
    ulist = create_dynamodb_item_list("u", 1, uattr_list, multiple)

    items_list = flist + ulist

    # verify that statuses are in the results.
    def verification(response):
        res_dict = json.loads(response.text)
        res_status_list = [friend['status'] for friend in res_dict['items']]
        if sorted(res_status_list) != sorted(config.status_list*multiple):
            raise testdb.TestFail("wrong status list", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, None)

    test(accessToken)


################################################################################
# GET /friends: Test status.
#
# with "status" filter, with each one of the statuses
#  defined status names and not-defined status names, 1 each
#  defined status names and not-defined status names, >1 each
#
# verify the friend list appear correctly
################################################################################
def get_friends_status(multiple):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    fattr_list = []
    uattr_list = []
    for status in config.status_list:
        friend = config.friend_attr.copy()
        user_profile = config.user_profile_attr
        friend["status"] = status
        fattr_list.append(friend)
        uattr_list.append(user_profile)

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, multiple)
    ulist = create_dynamodb_item_list("u", 1, uattr_list, multiple)

    items_list = flist + ulist

    for status in config.status_list:
        # verify that statuses are in the results.
        def verification(response):
            res_dict = json.loads(response.text)
            res_status_list = [friend['status'] for friend in res_dict['items']]
            veri_status_list = [status]*multiple
            if sorted(res_status_list) != sorted(veri_status_list):
                raise testdb.TestFail("status [{}] failed".format(status),
                                      response)

        @testdb.test_expected_output(items_list, verification)
        def test(accessToken):
            import urllib.parse
            status_query = urllib.parse.quote_plus(status)
            return api.get_friends(accessToken, {"status":status_query})

        test(accessToken)


################################################################################
# GET /friends: Test invalid limit values.
#
# invalid limit values - all should return 400
################################################################################
def get_friends_invalid_limit(invalid_limit_val):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    # Creates no data.
    items_list = []

    def verification(response):
        if response.status_code != 400:
            raise testdb.TestFail("expect status code 400", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, {"limit":invalid_limit_val})

    test(accessToken)


################################################################################
# GET /friends: Test valid limit values.
#
# 2 cases:
# friends <= limit: page item number == limit, and no "next"
# friends >  limit: page item number == limit, and "next"
################################################################################
def get_friends_valid_limit(item_cnt, limit_param):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    # Valid "limit" range: >= 1 and <= 100.
    # Int outside of range default to 100.
    if limit_param >= 1 and limit_param <= 100:
        limit_val = limit_param
    else:
        limit_val = 100

    fattr_list = [config.friend_attr]
    uattr_list = [config.user_profile_attr]

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, item_cnt)
    ulist = create_dynamodb_item_list("u", 1, uattr_list, item_cnt)

    items_list = flist + ulist

    def verification(response):
        res_dict = json.loads(response.text)
        if item_cnt <= limit_val:
            actual_item_cnt = len(res_dict["items"])
            if (actual_item_cnt != item_cnt or "next" in res_dict.keys()):
                raise testdb.TestFail("actual:{}".format(actual_item_cnt),
                                      response)
        elif item_cnt > limit_val:
            actual_item_cnt = len(res_dict["items"])
            if (actual_item_cnt != limit_val or "next" not in res_dict.keys()):
                raise testdb.TestFail("actual:{}".format(actual_item_cnt),
                                      response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, {"limit":limit_param})

    test(accessToken)


################################################################################
# GET /friends: Test valid next values.
#
# test cases:
# given N items,
# if next < N, returned items should be between next+1 to N
# if next == N, return empty list
# if next > N, return empty list
################################################################################
def get_friends_next(item_cnt, nextval):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    fattr_list = []
    uattr_list = []
    for i in range(item_cnt):
        friend = config.friend_attr.copy()
        friend["friendid"] = i+1
        user_profile = config.user_profile_attr
        fattr_list.append(friend)
        uattr_list.append(user_profile)

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, 1)
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    items_list = flist + ulist

    def verification(response):
        if nextval < item_cnt:
            res_dict = json.loads(response.text)
            friendid_list = [friend["friendid"] for friend in res_dict["items"]]

            flist_size_ex = item_cnt - nextval
            flist_size_ac = len(friendid_list)
            if flist_size_ex != flist_size_ac:
                raise testdb.TestFail("ex:{} ac:{}".format(flist_size_ex,
                                                           flist_size_ac),
                                                           response)
            try:
                flist_ex = [i for i in range(nextval+1, item_cnt+1)]

                # if any item can't be coverted to int, expection would occur.
                flist_ac = [int(i) for i in friendid_list]
                flist_ac = sorted(flist_ac)

                if flist_ex != flist_ac:
                    raise testdb.TestFail("ex:{} ac:{}".format(flist_ex,
                                                               flist_ac),
                                                               response)
            except Exception as e:
                raise testdb.TestFail(e)
        else:
            res_dict = json.loads(response.text)
            # expect the returned list to be empty
            friend_list_size = len(res_dict["items"])

            if friend_list_size != 0:
                raise testdb.TestFail("ex:0 ac:{}".format(friend_list_size),
                                                          response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_friends(accessToken, {"next":str(nextval).zfill(32)})

    test(accessToken)


#TODO: rework this case so that:
# - a param of which page to verify is added
# - wrapped test function returns response, verification receives response,
#   and api call occurs only once.  Just like all other instances.
# In other words, each run of this function should call api only once, and
# verify only one page.
################################################################################
# GET /friends: Test friend list pagination.
#
# pagination - test limit and next together.
# 2 cases:
# item count % limit count == 0
# item count % limit count != 0
# in both cases, verify items from all pages equal the actual friend list.
################################################################################
def get_friends_pagination(item_cnt, limit):
    response = api.post_auth_login(email, password)
    accessToken = response.json()['accessToken']

    fattr_list = []
    uattr_list = []
    for i in range(item_cnt):
        friend = config.friend_attr.copy()
        friend["friendid"] = i+1
        user_profile = config.user_profile_attr
        fattr_list.append(friend)
        uattr_list.append(user_profile)

    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, 1)
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    items_list = flist + ulist

    def verification(friendid_list):
        friend_cnt_ex = item_cnt
        friend_cnt_ac = len(friendid_list)
        if friend_cnt_ex != friend_cnt_ac:
            raise testdb.TestFail("ex:{} ac:{}".format(friend_cnt_ex,
                                                       friend_cnt_ac))
        try:
            friend_list_ex = [i for i in range(1, item_cnt+1)]
            # if any item can't be coverted to int, expection would occur.
            friend_list_ac = [int(i) for i in friendid_list]
            friend_list_ac = sorted(friend_list_ac)
            if friend_list_ex != friend_list_ac:
                raise testdb.TestFail("ex:{} ac:{}".format(friend_list_ex,
                                                           friend_list_ac))
        except Exception as e:
            raise testdb.TestFail(e)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        friendid_list = []
        nextval = None
        params = {"limit":limit}
        while True:
            resp = api.get_friends(accessToken, params)
            res_dict = json.loads(resp.text)
            friendid_list += [friend["friendid"] for friend in res_dict["items"]]
            if "next" not in res_dict.keys():
                break
            else:
                nextval = res_dict["next"]
                params = {"limit":limit, "next":nextval}
        return friendid_list

    test(accessToken)


################################################################################
# POST /friends: max friend count, inviter/invitee
#
# Verify if the inviter or invitee reached max friend count (of status
# "friend"), friendship cannot be made.
################################################################################
def post_friends_max_fcnt(param):
    accessToken = clean_login()

    # change this per implementation
    friend_limit = 1000

    inviter_id = account_id
    invitee_id = str(friend_limit+1).zfill(32)

    # create friend_limit friends of status "friend" for inviter/invitee.
    # friend of id "(friend_limit+1)" is the invitee.
    friend = config.friend_attr.copy()
    friend["status"] = "friend"

    fattr_list = [friend] * friend_limit
    uattr_list = [config.user_profile_attr]

    if param == "inviter":
        flist = create_dynamodb_item_list("f", (inviter_id, 1), fattr_list, 1)
    elif param == "invitee":
        flist = create_dynamodb_item_list("f", (invitee_id, 1), fattr_list, 1)
    else:
        raise ValueError("bad param: {}".format(param))

    ulist = create_dynamodb_item_list("u", 1, uattr_list, friend_limit+1)

    items_list = flist + ulist

    def verification(response):
        fail_info = ""
        if response.status_code == 200:
            fail_info += "wrong status code|"

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(inviter_id),
                                          "sk":"friend#{}".format(invitee_id)})
        if "Item" in resp.keys():
            fail_info += "inviter has invitee as friend|"

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(invitee_id),
                                          "sk":"friend#{}".format(inviter_id)})
        if "Item" in resp.keys():
            fail_info += "invitee has inviter as friend"

        if fail_info != "":
            raise testdb.TestFail(fail_info, response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, userid):
        return api.post_friends(accessToken, userid)

    test(accessToken, invitee_id)


################################################################################
# POST /friends: friend with self
#
# Verify one cannot friend with self.
################################################################################
def post_friends_self():
    accessToken = clean_login()

    # Creates no data.
    items_list = []

    def verification(response):
        fail_info = ""
        if response.status_code == 200:
            fail_info += "wrong status code|"

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(account_id),
                                          "sk":"friend#{}".format(account_id)})
        if "Item" in resp.keys():
            fail_info += "inviter has self as friend"

        if fail_info != "":
            raise testdb.TestFail(fail_info, response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, userid):
        return api.post_friends(accessToken, userid)

    test(accessToken, account_id)


################################################################################
# POST /friends: pending
#
# 3 cases:
# With a "pending" friendship,
# - invitee sends request, friend status becomes "friend".
# - inviter sends request, friend status remains "pending".
# With a one-sided "pending" friendship,
# - inviter sends request, friend status becomes "pending" (both sides).
################################################################################
def post_friends_pending(requester):
    accessToken = clean_login()

    sender_id = account_id
    sendee_id = "1".zfill(32)

    if requester == "invitee":
        invitee = sender_id
        expected_status = "friend"
    elif requester == "inviter" or requester == "inviter_oneside":
        invitee = sendee_id
        expected_status = "pending"
    else:
        raise ValueError("bad param: {}".format(requester))

    uattr_list = [config.user_profile_attr]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    friend = config.friend_attr.copy()
    friend["status"] = "pending"
    friend["invitee"] = invitee
    friend["userid"] = sender_id
    friend["friendid"] = sendee_id
    fattr_list = [friend]
    flist = create_dynamodb_item_list("f", (sender_id, sendee_id), fattr_list, 1)

    if requester != "inviter_oneside":
        friend2 = friend.copy()
        friend2["userid"] = sendee_id
        friend2["friendid"] = sender_id
        fattr_list = [friend2]
        flist += create_dynamodb_item_list("f", (sendee_id, sender_id), fattr_list, 1)

    items_list = flist + ulist

    def verification(response):
        fail_info = ""
        if response.status_code != 200:
            fail_info += "wrong status code {}|".format(response.status_code)

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(sender_id),
                                          "sk":"friend#{}".format(sendee_id)})
        if resp["Item"]["status"] != expected_status:
            fail_info += "user#{} friend#{} status {}|".format(sender_id,
                             sendee_id, resp["Item"]["status"])

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(sendee_id),
                                          "sk":"friend#{}".format(sender_id)})
        if resp["Item"]["status"] != expected_status:
            fail_info += "user#{} friend#{} status {}|".format(sendee_id,
                             sender_id, resp["Item"]["status"])

        if fail_info != "":
            raise testdb.TestFail(fail_info, response)

    ecl = [("user#{}".format(sendee_id), "friend#{}".format(sender_id))]

    @testdb.test_expected_output(items_list, verification, extra_cleanup_list=ecl)
    def test(accessToken, userid):
        return api.post_friends(accessToken, userid)

    test(accessToken, sendee_id)


################################################################################
# POST /friends: no existing friendship
#
# With no blocklist:
#  - with no existing friendship, a request establishes a "pending" friendship.
# if sender has a blocklist with sendee on it:
#  - not allowed to send request
# if sendee has a blocklist with sender on it:
#  - a one-sided pending friendship is formed
################################################################################
def post_friends_none(blocklist_param):
    accessToken = clean_login()

    sender_id = account_id
    sendee_id = "1".zfill(32)

    uattr_list = [config.user_profile_attr]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    items_list = ulist

    if blocklist_param == "sender":
        blocklist = config.block_list_attr.copy()
        blocklist["userid"] = sender_id
        blocklist["blockedUserIds"] = {sendee_id:True}
        battr_list = [blocklist]
        blist = create_dynamodb_item_list("b", sender_id, battr_list, 1)

        items_list += blist

    elif blocklist_param == "sendee":
        blocklist = config.block_list_attr.copy()
        blocklist["userid"] = sendee_id
        blocklist["blockedUserIds"] = {sender_id:True}
        battr_list = [blocklist]
        blist = create_dynamodb_item_list("b", sendee_id, battr_list, 1)

        items_list += blist

    def verification(response):
        if blocklist_param == "sender":
            if response.status_code != 403:
                raise testdb.TestFail("wrong status code {}|".format(response.status_code),
                                      response)
        else:
            fail_info = ""
            if response.status_code != 200:
                fail_info += "wrong status code {}|".format(response.status_code)

            resp = testdb.table.get_item(Key={"pk":"user#{}".format(sender_id),
                                              "sk":"friend#{}".format(sendee_id)})
            r = resp["Item"]
            if (r["status"]   != "pending" or
                r["invitee"]  != sendee_id or
                r["message"]  != api.friend_request_message or
                r["userid"]   != sender_id or
                r["friendid"] != sendee_id):
                fail_info += "user#{} friend#{} [{}]|".format(sender_id,
                                                              sendee_id, r)

            resp = testdb.table.get_item(Key={"pk":"user#{}".format(sendee_id),
                                              "sk":"friend#{}".format(sender_id)})

            if blocklist_param == "sendee":
                if "Item" in resp.keys():
                    fail_info += "user#{} friend#{} [{}]|".format(sendee_id,
                                                                  sender_id, r)
            else:
                r = resp["Item"]
                if (r["status"]   != "pending" or
                    r["invitee"]  != sendee_id or
                    r["message"]  != api.friend_request_message or
                    r["userid"]   != sendee_id or
                    r["friendid"] != sender_id):
                    fail_info += "user#{} friend#{} [{}]|".format(sendee_id,
                                                                  sender_id, r)

            if fail_info != "":
                raise testdb.TestFail(fail_info, response)

    ecl = [("user#{}".format(sender_id), "friend#{}".format(sendee_id)),
           ("user#{}".format(sendee_id), "friend#{}".format(sender_id))]

    @testdb.test_expected_output(items_list, verification, extra_cleanup_list=ecl)
    def test(accessToken, userid):
        return api.post_friends(accessToken, userid)

    test(accessToken, sendee_id)


################################################################################
# POST /friends: "friend" friendship
#
# with a "friend" friendship, a request does not change the friendship.
################################################################################
def post_friends_friend():
    accessToken = clean_login()

    sender_id = account_id
    sendee_id = "1".zfill(32)

    uattr_list = [config.user_profile_attr]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    friend = config.friend_attr.copy()
    friend["status"] = "friend"
    friend["userid"] = sender_id
    friend["friendid"] = sendee_id
    fattr_list = [friend]
    flist = create_dynamodb_item_list("f", (sender_id, sendee_id), fattr_list, 1)

    friend2 = friend.copy()
    friend2["userid"] = sendee_id
    friend2["friendid"] = sender_id
    fattr_list = [friend2]
    flist += create_dynamodb_item_list("f", (sendee_id, sender_id), fattr_list, 1)

    items_list = flist + ulist

    def verification(response):
        fail_info = ""
        if response.status_code != 200:
            fail_info += "wrong status code {}|".format(response.status_code)

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(sender_id),
                                          "sk":"friend#{}".format(sendee_id)})
        r = resp["Item"]
        if (r["status"] != "friend"):
            fail_info += "user#{} friend#{} [{}]|".format(sender_id,
                                                          sendee_id, r)

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(sendee_id),
                                          "sk":"friend#{}".format(sender_id)})
        r = resp["Item"]
        if (r["status"] != "friend"):
            fail_info += "user#{} friend#{} [{}]|".format(sendee_id,
                                                          sender_id, r)

        if fail_info != "":
            raise testdb.TestFail(fail_info, response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, userid):
        return api.post_friends(accessToken, userid)

    test(accessToken, sendee_id)


################################################################################
# GET /friends/search: email, valid
#
# Search for friend with a valid email returns expected results.
################################################################################
def get_friends_search_email_valid():
    accessToken = clean_login()

    email_search_str = "<EMAIL>"

    user_profile = config.user_profile_attr.copy()
    user_profile["email"] = "<EMAIL>"
    user_profile["name"] = "name_verification"
    user_profile["userid"] = "userid_verification"
    uattr_list = [user_profile]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    email_attr = config.user_email_attr.copy()
    email_attr["userid"] = "1".zfill(32)
    eattr_list = [email_attr]
    elist = create_dynamodb_item_list("e", email_search_str, eattr_list, 1)

    items_list = ulist + elist

    def verification(response):
        if response.status_code != 200:
            raise testdb.TestFail("wrong status code", response)

        res_dict = json.loads(response.text)
        if len(res_dict["items"]) != 1:
            raise testdb.TestFail("more than 1 item returned", response)

        r = res_dict["items"][0]
        if (r["email"] != "<EMAIL>" or
            r["name"] != "name_verification" or
            r["userid"] != "userid_verification"):
            raise testdb.TestFail("search failed", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, platform, q):
        return api.get_friends_search(accessToken, platform, q)

    test(accessToken, "", email_search_str)


################################################################################
# GET /friends/search: platform - Steam, valid
#
# Searching for friend by platform of Steam with a valid alias returns expected
# results.
#
# This test uses hardcoded account data found here:
# https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/001/vaj3w3pvqxbjy52dfo55ocwgwy
################################################################################
def get_friends_search_platform_steam_valid():
    accessToken = clean_login()

    # Creates no data.
    items_list = []

    def verification(response):
        if response.status_code != 200:
            raise testdb.TestFail("wrong status code", response)

        res_dict = json.loads(response.text)
        if len(res_dict["items"]) != 1:
            raise testdb.TestFail("more than 1 item returned", response)

        r = res_dict["items"][0]
        if (r["name"] != "d2cfriendstest002" or
            r["userid"] != "4e22d7a70ba540758471a6d7bc8a9ed2"):
            raise testdb.TestFail("search failed", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, platform, q):
        return api.get_friends_search(accessToken, platform, q)

    test(accessToken, "steam", "d2cfriendstest002")


################################################################################
# GET /friends/import: find platform users with 2K accounts
#
# Searching valid steam IDs for 2K accounts returns expected results.
#
# This test uses hardcoded account data found here:
# https://my.1password.com/vaults/6usb4dx3n22xx7npzk3u7isypq/001/vaj3w3pvqxbjy52dfo55ocwgwy
################################################################################
def get_friends_import_platform_steam_valid():
    accessToken = clean_login()

    # Steam aliases and IDs
    steam_account_list = [
        ("d2cfriendstesting001", "*****************"),
        ("d2cfriendstest002", "*****************"),
        ("d2cfriendstest003", "*****************"),
        ("d2cfriendstest004", "*****************")
    ]

    # Comma separated steam IDs
    query_string = ",".join([i[1] for i in steam_account_list])

    # Creates no data.
    items_list = []

    def verification(response):
        if response.status_code != 200:
            raise testdb.TestFail("wrong status code", response)

        res_dict = json.loads(response.text)
        result = []
        for i in res_dict["items"]:
            result.append((i["firstPartyAlias"], i["firstPartyId"]))

        if sorted(steam_account_list) != sorted(result):
            raise testdb.TestFail("search failed", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, platform, ids):
        return api.get_friends_import(accessToken, platform, ids)

    test(accessToken, "steam", query_string)


################################################################################
# PATCH /friends/{friendID}: update viewed flag
#
# verify viewed flag is updated
################################################################################
def patch_friends_id_viewed(viewed):
    if viewed != True and viewed != False:
        raise ValueError("bad param: {}".format(viewed))

    accessToken = clean_login()

    fattr_list = []
    friend = config.friend_attr.copy()
    friend["viewed"] = not viewed
    friend["userid"] = account_id
    friend["friendid"] = "1".zfill(32)
    fattr_list.append(friend)
    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, 1)

    uattr_list = [config.user_profile_attr]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    items_list = flist + ulist

    def verification(response):
        res_dict = json.loads(response.text)
        if res_dict["viewed"] != viewed:
            raise testdb.TestFail("viewed not updated", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, friend_id, viewed):
        return api.patch_friends_id(accessToken, friend_id, viewed)

    test(accessToken, "1".zfill(32), viewed)


################################################################################
# PATCH /friends/{friendID}: invalid friend id
#
# verify with an invalid friend id, 404 is returned
################################################################################
def patch_friends_id_invalid():
    accessToken = clean_login()

    invalid_friend_id = "12345"

    # Creates no data.
    items_list = []

    def verification(response):
        if response.status_code != 404:
            raise testdb.TestFail("wrong status code", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, friend_id, viewed):
        return api.patch_friends_id(accessToken, friend_id, viewed)

    test(accessToken, invalid_friend_id, True)


################################################################################
# DELETE /friends/{friendID}: break friendship
#
# verify friendship is deleted
################################################################################
def delete_friends_id_break_friendship():
    accessToken = clean_login()

    fattr_list = [config.friend_attr]
    flist = create_dynamodb_item_list("f", (account_id, 1), fattr_list, 1)
    uattr_list = [config.user_profile_attr]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)
    items_list = flist + ulist

    def verification(response):
        resp = testdb.table.get_item(Key={"pk":"user#{}".format(account_id),
                                          "sk":"friend#{}".format("1".zfill(32))})
        if "Item" in resp.keys():
            raise testdb.TestFail("pk:{}/sk:{}".format(account_id, "1".zfill(32)),
                                                       response)

        resp = testdb.table.get_item(Key={"pk":"user#{}".format("1".zfill(32)),
                                          "sk":"friend#{}".format(account_id)})
        if "Item" in resp.keys():
            raise testdb.TestFail("pk:{}/sk:{}".format("1".zfill(32), account_id),
                                                       response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, friend_id):
        return api.delete_friends_id(accessToken, friend_id)

    test(accessToken, "1".zfill(32))


################################################################################
# GET /user/blocklist: get list
#
# verify block list contents are correctly retrieved
################################################################################
def get_user_blocklist_get_list(uidlist):
    accessToken = clean_login()

    blocklist = config.block_list_attr.copy()
    blocklist["userid"] = account_id
    blocklist["blockedUserIds"] = {uid:True for uid in uidlist}
    battr_list = [blocklist]
    blist = create_dynamodb_item_list("b", account_id, battr_list, 1)

    items_list = blist

    def verification(response):
        res_dict = json.loads(response.text)
        result_list = res_dict["blockedUserIds"]
        expected_list = blocklist["blockedUserIds"].keys()
        if (res_dict["userid"] != blocklist["userid"] or
            sorted(result_list) != sorted(expected_list)):
            raise testdb.TestFail("fail to get blocklist", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken):
        return api.get_user_blocklist(accessToken)

    test(accessToken)


################################################################################
# POST /user/blocklist: modify a non-existing list (create new)
#
# verify block list is correctly modified if there's no existing list.
################################################################################
def post_user_blocklist_modify_nonexisting_list():
    accessToken = clean_login()

    add_list = ["123", "456", "789", "999"]
    remove_list = ["456"]
    expected_list = ["123", "789", "999"]

    # ensure blocklist doesn't exist before the test
    keys_list = [("user#{}".format(account_id), "blocklist#{}".format(account_id))]
    testdb.delete_account_items(keys_list)

    # Creates no data.
    items_list = []

    def verification(response):
        res_dict = json.loads(response.text)
        result_list = res_dict["blockedUserIds"]
        if sorted(result_list) != sorted(expected_list):
            raise testdb.TestFail("modify existing list failed", response)

    ecl = [("user#{}".format(account_id), "blocklist#{}".format(account_id))]

    @testdb.test_expected_output(items_list, verification, extra_cleanup_list=ecl)
    def test(accessToken, alist, rlist):
        return api.post_user_blocklist(accessToken, alist, rlist)

    test(accessToken, add_list, remove_list)


################################################################################
# POST /user/blocklist: modify block list
#
# verify block list is correctly modified given the add and/or remove list.
################################################################################
def post_user_blocklist_modify_list(existing_list, add_list, remove_list,
                                    expected_list):
    accessToken = clean_login()

    blocklist = config.block_list_attr.copy()
    blocklist["blockedUserIds"] = {uid:True for uid in existing_list}
    battr_list = [blocklist]
    blist = create_dynamodb_item_list("b", account_id, battr_list, 1)

    items_list = blist

    def verification(response):
        res_dict = json.loads(response.text)

        if (len(existing_list or "") + len(add_list or "")
                                     - len(remove_list or "") >= 100):
            if response.status_code != 400:
                raise testdb.TestFail("wrong status code", response)
        else:
            result_list = res_dict["blockedUserIds"]
            if sorted(result_list) != sorted(expected_list):
                raise testdb.TestFail("modify block list failed", response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, alist, rlist):
        return api.post_user_blocklist(accessToken, alist, rlist)

    test(accessToken, add_list, remove_list)


################################################################################
# POST /user/blocklist: friendship removed for uid on the add list
#
# verify friendship is removed for user ids to be added on the blocklist
################################################################################
def post_user_blocklist_friendship_removed():
    accessToken = clean_login()

    fid1 = account_id
    fid2 = "1".zfill(32)

    uattr_list = [config.user_profile_attr]
    ulist = create_dynamodb_item_list("u", 1, uattr_list, 1)

    friend = config.friend_attr.copy()
    friend["userid"] = fid1
    friend["friendid"] = fid2
    fattr_list = [friend]
    flist = create_dynamodb_item_list("f", (fid1, fid2), fattr_list, 1)

    friend2 = friend.copy()
    friend2["userid"] = fid2
    friend2["friendid"] = fid1
    fattr_list = [friend2]
    flist += create_dynamodb_item_list("f", (fid2, fid1), fattr_list, 1)

    # create an empty blocklist so it would get cleaned up
    blocklist = config.block_list_attr.copy()
    blocklist["blockedUserIds"] = {}
    battr_list = [blocklist]
    blist = create_dynamodb_item_list("b", account_id, battr_list, 1)

    items_list = flist + ulist + blist

    def verification(response):
        fail_info = ""
        resp = testdb.table.get_item(Key={"pk":"user#{}".format(fid1),
                                          "sk":"friend#{}".format(fid2)})
        if "Item" in resp.keys():
            fail_info += "{} has {} as friend|".format(fid1, fid2)

        resp = testdb.table.get_item(Key={"pk":"user#{}".format(fid2),
                                          "sk":"friend#{}".format(fid1)})
        if "Item" in resp.keys():
            fail_info += "{} has {} as friend".format(fid2, fid1)

        if fail_info != "":
            raise testdb.TestFail(fail_info, response)

    @testdb.test_expected_output(items_list, verification)
    def test(accessToken, alist, rlist):
        return api.post_user_blocklist(accessToken, alist, rlist)

    test(accessToken, [fid2], None)


## Init
email = config.dna_account_info["email"]
password = config.dna_account_info["password"]

# Check if 2K credentials are present
if (email == None or password == None or
    email == "" or password == ""):
    sys.exit("2K credentials are not defined in environment variables "
             "ATOMIC_API_TEST_2K_EMAIL and ATOMIC_API_TEST_2K_PASSWORD")

config.dna_account_info["account_id"] = getAccountId(email, password)
account_id = config.dna_account_info["account_id"]

# Check if AWS credentials are present
if (config.aws_access_key_id == None or config.aws_secret_access_key == None or
    config.aws_access_key_id == "" or config.aws_secret_access_key == ""):
    sys.exit("AWS credentials are not defined in environment variables "
             "AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY")

## Test suites
auth_ts = {
    "ts_name" : "Authentication",
    "tc_list" : [
        {
        "name" : "POST /auth/login profile not exists",
        "func" : partial(post_auth_login_profile_not_exists)
        },
        {
        "name" : "POST /auth/login profile exists",
        "func" : partial(post_auth_login_profile_exists)
        },
        {
        "name" : "POST /auth/logout no change",
        "func" : partial(post_auth_logout_no_change)
        },
        {
        "name" : "POST /auth/refresh no change",
        "func" : partial(post_auth_refresh_no_change)
        }
    ]
}

profile_ts = {
    "ts_name" : "Profile",
    "tc_list" : [
        {
        "name" : "GET /user/profile no change",
        "func" : partial(get_user_profile_no_change)
        },
        {
        "name" : "PATCH /user/profile update attr",
        "func" : partial(patch_user_profile_update_attr)
        }
    ]
}

friends_ts = {
    "ts_name" : "Friends",
    "tc_list" : [
        {
        "name" : "GET /friends friend updated x1",
        "func" : partial(get_friends_friend_updated, 1)
        },
        {
        "name" : "GET /friends friend updated x5",
        "func" : partial(get_friends_friend_updated, 5)
        },

        {
        "name" : "GET /friends bad friend x1",
        "func" : partial(get_friends_bad_friend, 1)
        },
        {
        "name" : "GET /friends bad friend x5",
        "func" : partial(get_friends_bad_friend, 5)
        },

        {
        "name" : "GET /friends bad user profile x1",
        "func" : partial(get_friends_bad_user_profile, 1)
        },
        {
        "name" : "GET /friends bad user profile x5",
        "func" : partial(get_friends_bad_user_profile, 5)
        },


        {
        "name" : "GET /friends no filter x1",
        "func" : partial(get_friends_no_filter, 1)
        },
        {
        "name" : "GET /friends no filter x5",
        "func" : partial(get_friends_no_filter, 5)
        },

        {
        "name" : "GET /friends status x1",
        "func" : partial(get_friends_status, 1)
        },
        {
        "name" : "GET /friends status x5",
        "func" : partial(get_friends_status, 5)
        },

        {
        "name" : "GET /friends limit within valid range | item:10 limit:10",
        "func" : partial(get_friends_valid_limit, 10, 10)
        },
        {
        "name" : "GET /friends limit within valid range | item:11 limit:10",
        "func" : partial(get_friends_valid_limit, 11, 10)
        },
        {
        "name" : "GET /friends limit outside valid range | item:100 limit:-1",
        "func" : partial(get_friends_valid_limit, 100, -1)
        },
        {
        "name" : "GET /friends limit outside valid range | item:101 limit:-1",
        "func" : partial(get_friends_valid_limit, 101, -1)
        },
        {
        "name" : "GET /friends limit outside valid range | item:100 limit:0",
        "func" : partial(get_friends_valid_limit, 100, 0)
        },
        {
        "name" : "GET /friends limit outside valid range | item:101 limit:0",
        "func" : partial(get_friends_valid_limit, 101, 0)
        },
        {
        "name" : "GET /friends limit outside valid range | item:100 limit:101",
        "func" : partial(get_friends_valid_limit, 100, 101)
        },
        {
        "name" : "GET /friends limit outside valid range | item:101 limit:101",
        "func" : partial(get_friends_valid_limit, 101, 101)
        },

        {
        "name" : "GET /friends invalid limit | limit:1.234",
        "func" : partial(get_friends_invalid_limit, 1.234)
        },
        {
        "name" : "GET /friends invalid limit | limit:\"asdf\"",
        "func" : partial(get_friends_invalid_limit, "asdf")
        },

        {
        "name" : "GET /friends next item:10 next:5",
        "func" : partial(get_friends_next, 10, 5)
        },
        {
        "name" : "GET /friends next item:10 next:10",
        "func" : partial(get_friends_next, 10, 10)
        },
        {
        "name" : "GET /friends next item:10 next:15",
        "func" : partial(get_friends_next, 10, 15)
        },

        {
        "name" : "GET /friends pagination item:50 limit:5",
        "func" : partial(get_friends_pagination, 50, 5)
        },
        {
        "name" : "GET /friends pagination item:51 limit:5",
        "func" : partial(get_friends_pagination, 51, 5)
        },

        {
        "name" : "POST /friends max friend count, inviter",
        "func" : partial(post_friends_max_fcnt, "inviter")
        },
        {
        "name" : "POST /friends max friend count, invitee",
        "func" : partial(post_friends_max_fcnt, "invitee")
        },

        {
        "name" : "POST /friends self",
        "func" : partial(post_friends_self)
        },

        {
        "name" : "POST /friends pending, requester is inviter",
        "func" : partial(post_friends_pending, requester="inviter")
        },
        {
        "name" : "POST /friends pending, requester is invitee",
        "func" : partial(post_friends_pending, requester="invitee")
        },
        {
        "name" : "POST /friends pending, requester is inviter with onesided pending",
        "func" : partial(post_friends_pending, requester="inviter_oneside")
        },

        {
        "name" : "POST /friends no existing friendship, no blocklist",
        "func" : partial(post_friends_none, blocklist_param="none")
        },
        {
        "name" : "POST /friends no existing friendship, sender has blocklist",
        "func" : partial(post_friends_none, blocklist_param="sender")
        },
        {
        "name" : "POST /friends no existing friendship, sendee has blocklist",
        "func" : partial(post_friends_none, blocklist_param="sendee")
        },

        {
        "name" : "POST /friends friend friendship",
        "func" : partial(post_friends_friend)
        },

        {
        "name" : "GET /friends/search email, valid",
        "func" : partial(get_friends_search_email_valid)
        },

        {
        "name" : "GET /friends/search platform:steam, valid",
        "func" : partial(get_friends_search_platform_steam_valid)
        },

        {
        "name" : "GET /friends/import platform:steam, valid",
        "func" : partial(get_friends_import_platform_steam_valid)
        },

        {
        "name" : "PATCH /friends/{friendID} update viewed flag to True",
        "func" : partial(patch_friends_id_viewed, viewed=True)
        },
        {
        "name" : "PATCH /friends/{friendID} update viewed flag to False",
        "func" : partial(patch_friends_id_viewed, viewed=False)
        },
        {
        "name" : "PATCH /friends/{friendID} invalid friend id",
        "func" : partial(patch_friends_id_invalid)
        },

        {
        "name" : "DELETE /friends/{friendID} break friendship",
        "func" : partial(delete_friends_id_break_friendship)
        },

        {
        "name" : "GET /user/blocklist uid list: [\"123\", \"456\", \"789\"]",
        "func" : partial(get_user_blocklist_get_list, uidlist=["123", "456", "789"])
        },
        {
        "name" : "GET /user/blocklist uid list: []",
        "func" : partial(get_user_blocklist_get_list, uidlist=[])
        },
        {
        "name" : "GET /user/blocklist uid list: [\"123\"]",
        "func" : partial(get_user_blocklist_get_list, uidlist=["123"])
        },

        {
        "name" : "POST /user/blocklist modify a non-existing list",
        "func" : partial(post_user_blocklist_modify_nonexisting_list)
        },

        # add non-existing item
        {
        "name" : "POST /user/blocklist add non-existing item",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=["4"],
                     remove_list=None,
                     expected_list=["1", "2", "3", "4"])
        },

        # add existing item
        {
        "name" : "POST /user/blocklist add existing item",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=["2"],
                     remove_list=None,
                     expected_list=["1", "2", "3"])
        },

        # add empty list
        {
        "name" : "POST /user/blocklist add empty list",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=[],
                     remove_list=None,
                     expected_list=["1", "2", "3"])
        },

        # remove non-existing item
        {
        "name" : "POST /user/blocklist remove non-existing item",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=None,
                     remove_list=["4"],
                     expected_list=["1", "2", "3"])
        },

        # remove existing item
        {
        "name" : "POST /user/blocklist remove existing item",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=None,
                     remove_list=["2"],
                     expected_list=["1", "3"])
        },

        # remove empty list
        {
        "name" : "POST /user/blocklist remove empty list",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=None,
                     remove_list=[],
                     expected_list=["1", "2", "3"])
        },

        # add remove both null list
        {
        "name" : "POST /user/blocklist add remove both null list",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=["1", "2", "3"],
                     add_list=None,
                     remove_list=None,
                     expected_list=["1", "2", "3"])
        },

        # resulting list size < limit
        {
        "name" : "POST /user/blocklist resulting list size < limit",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=[str(i) for i in range(1, 99)],
                     add_list=["1000"],
                     remove_list=None,
                     expected_list=[str(i) for i in range(1, 99)]+["1000"])
        },

        # resulting list size >= limit
        {
        "name" : "POST /user/blocklist resulting list size >= limit",
        "func" : partial(post_user_blocklist_modify_list,
                     existing_list=[str(i) for i in range(1, 100)],
                     add_list=["1000"],
                     remove_list=None,
                     expected_list=[str(i) for i in range(1, 100)])
        },

        {
        "name" : "POST /user/blocklist friendship removed for uid on the add list",
        "func" : partial(post_user_blocklist_friendship_removed)
        }
    ]
}

dev = {
    "ts_name" : "dev",
    "tc_list" : [
    ]
}

run_list = [auth_ts, profile_ts, friends_ts]
#run_list = [dev]

## Test execution
#TODO: create a class in post_to_slack
test_results = {
    "tr_name" : "Social Atomic API Test Result",
    "total_duration" : 0,
    "ts_list" : []
}

for ts in run_list:
    ts_result = {
        "ts_name" : ts["ts_name"],
        "tc_list" : []
    }
    for tc in ts["tc_list"]:
        start = time.time()
        tc_result = {"tc_name":tc["name"]}
        try:
            # report if a db is in a clean slate; clean up if not.
            testdb.check_clean_slate(account_id, cleanup=True)
            # run a test
            print("= {} ==========".format(tc["name"]))
            tc["func"]()
            #
            print("test passed.")
            tc_result["result"] = "pass"
        except testdb.TestFail as tf:
            print("test FAIL! [{}]".format(tf.message))
            tc_result["result"] = "fail"
            if tf.response is not None:
                testdb.print_response(tf.response)
        except testdb.OtherFail as of:
            print("something unexpected happened during "
                  "test execution/verification. [{}]".format(of.message))
            testdb.print_traceback(traceback.format_exc())
            tc_result["result"] = "fail"
            if of.response is not None:
                testdb.print_response(of.response)
        except Exception as e:
            print("something unexpected happened. [{}]".format(e))
            testdb.print_traceback(traceback.format_exc())
            tc_result["result"] = "fail"
        end = time.time()
        print("test took {:.1f} secs".format(end - start))
        test_results["total_duration"] += end - start
        ts_result["tc_list"].append(tc_result)
        print("")
    test_results["ts_list"].append(ts_result)

# Create timestamp for test completion
now = datetime.now(pytz.timezone("Canada/Pacific"))
test_results["completion_time"] = now.strftime("%Y/%m/%d %H:%M:%S %Z")

post_to_slack.post_results(test_results)
