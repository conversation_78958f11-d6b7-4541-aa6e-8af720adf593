FROM erlang:25 AS builder

WORKDIR /build

# do build
COPY . /build
RUN ./rebar3 compile

# turn all symlinks to files
RUN find -L _build/default/lib -regex ".*\/\(plugins\|ebin\|priv\)\/.*" -print0 | tar -cvf bundle.tar -T-
RUN mkdir bundle && cd bundle && tar -xvf ../bundle.tar

FROM erlang:25 AS builder-healthcheck-patch

WORKDIR /build
RUN apt-get update && apt-get -y install libsnappy-dev
COPY t2gp-patch/ /build
RUN ./rebar3 compile

# copy build into new image
FROM vernemq/vernemq:1.13.0

ENV DOCKER_VERNEMQ_ACCEPT_EULA="yes"
ENV DOCKER_VERNEMQ_ALLOW_ANONYMOUS="off"
ENV DOCKER_VERNEMQ_PLUGINS__T2GP_SOCIAL="on"
ENV DOCKER_VERNEMQ_PLUGINS__T2GP_SOCIAL__PATH="/vernemq/plugins/t2gp-social"
ENV DOCKER_VERNEMQ_REG_VIEWS="[vmq_reg_trie]"
ENV DOCKER_VERNEMQ_DEFAULT_REG_VIEW="vmq_reg_trie"
ENV DOCKER_VERNEMQ_SYSTREE_REG_VIEW="vmq_reg_trie"

COPY --from=builder --chown=vernemq:vernemq /build/bundle/_build/default /vernemq/plugins/t2gp-social
COPY --from=builder-healthcheck-patch --chown=vernemq:vernemq /build/_build/default/lib/vmq_server/ebin/vmq_health_http.beam /vernemq/lib/vmq_server-1.13.0/ebin/

# patch beam files
COPY ./docker/patches/vmq_metrics.beam /vernemq/lib/vmq_server-1.13.0/ebin/vmq_metrics.beam
COPY ./docker/patches/vmq_parser_mqtt5.beam /vernemq/lib/vmq_commons-1.13.0/ebin/vmq_parser_mqtt5.beam

# Modified start script so STS removes pod from cluster regardless of updateStrategy
COPY scripts/vernemq.sh /usr/sbin/start_vernemq
USER root
RUN chmod +x /usr/sbin/start_vernemq
RUN apt update && \
  apt install sudo && \
  apt-get clean autoclean && \
  apt-get autoremove --yes && \
  rm -rf /var/lib/{apt,dpkg,cache,log}/
RUN echo "vernemq ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

USER vernemq
