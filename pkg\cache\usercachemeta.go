// usercachemeta.go is used to store user metadata in the cache for the DNA SSO syncing for when we update redis ttls (UpdateUserTtls)
// We keep track of if a user has friends, pending friends, or a blocklist so we don't query dynamodb to repopulate cache if we don't need to.
package cache

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
)

func BuildUserCacheMetaRedisKey(tenant, userid string) string {
	return fmt.Sprintf("%s:user:{%s}:meta", tenant, userid)
}

func (rc *RedisCache) GetUserCacheMeta(ctx context.Context, userid string) (*apipub.UserCacheMeta, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := BuildUserCacheMetaRedisKey(tenant, userid)
	return getCachedObject[apipub.UserCacheMeta](ctx, rc, key)
}

func (rc *RedisCache) SetUserCacheMeta(ctx context.Context, userid string, meta *apipub.UserCacheMeta, ttl time.Duration) error {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := BuildUserCacheMetaRedisKey(tenant, userid)
	return setCachedObject(ctx, rc, meta, key, ttl)
}

func (rc *RedisCache) UpdateUserCacheMetaTtl(ctx context.Context, userid string, ttl time.Duration) (bool, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := BuildUserCacheMetaRedisKey(tenant, userid)
	return rc.expire(ctx, key, ttl).Result()
}

func BuildFirstPartyRedisKey(tenant string, fpid string, ost apipub.OnlineServiceType) string {
	fpid = nintendoIdFormat(tenant, fpid, ost)
	return fmt.Sprintf("%s:user:firstparty:%s:ost:%v", tenant, fpid, ost)
}

func (rc *RedisCache) GetFirstPartyLookup(ctx context.Context, fpid string, ost apipub.OnlineServiceType) (*string, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := BuildFirstPartyRedisKey(tenant, fpid, ost)
	return getCachedObject[string](ctx, rc, key)
}

func (rc *RedisCache) SetFirstPartyLookup(ctx context.Context, fpid string, ost apipub.OnlineServiceType, parentid string, ttl time.Duration) (string, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	key := BuildFirstPartyRedisKey(tenant, fpid, ost)
	err := setCachedObject(ctx, rc, &parentid, key, ttl)
	return key, err
}

func (rc *RedisCache) UpdateFirstPartyLookupTtl(ctx context.Context, key string, ttl time.Duration) (bool, error) {
	return rc.expire(ctx, key, ttl).Result()
}

func nintendoIdFormat(tenant string, fpid string, ost apipub.OnlineServiceType) string {
	//if nintendo, we'll ignore the third part of the id
	if tenant == "dna" && ost == 11 {
		idParts := strings.Split(fpid, "-")
		if len(idParts) > 2 {
			fpid = idParts[0] + "-" + idParts[1]
		}
	}
	return fpid
}
