import type { QueryResult } from '../services';

export const QUERY_RESULT_STATUS = {
  IDEL: 'idle',
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
};

export const isQueryIdel: (
  queryResult: QueryResult
) => boolean = queryResult => {
  return queryResult?.status === QUERY_RESULT_STATUS.IDEL;
};

export const isQueryLoaded: (
  queryResult: QueryResult
) => boolean = queryResult => {
  return queryResult?.status === QUERY_RESULT_STATUS.SUCCESS;
};

export const isQueryLoading: (
  queryResult: QueryResult
) => boolean = queryResult => {
  return queryResult?.status === QUERY_RESULT_STATUS.LOADING;
};

export const isQueryError: (
  queryResult: QueryResult
) => boolean = queryResult => {
  return queryResult?.status === QUERY_RESULT_STATUS.ERROR;
};
