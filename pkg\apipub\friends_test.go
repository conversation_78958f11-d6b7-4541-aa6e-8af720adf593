package apipub

import (
	"testing"

	"github.com/franela/goblin"
)

func TestFriend(t *testing.T) {
	g := goblin.Goblin(t)
	g.<PERSON>cribe("Friend", func() {
		g.It("should return expected values", func() {

			user1 := "b287e655461f4b3085c8f244e394ff7e"
			user2 := "2017e9305ccc4e5781d076403c1b6725"
			f := &FriendResponse{
				Userid:   user1,
				Friendid: user2,
			}

			key := BuildFriendRedisKey("test", user1, user2)

			g.<PERSON>ser<PERSON>(f.PK("test")).Equal("test#user#" + user1)
			g.<PERSON>(f.<PERSON>("test")).Equal("test#friend#" + user2)
			g.<PERSON>(f.<PERSON>("test")).Equal(key)
		})
	})
}
