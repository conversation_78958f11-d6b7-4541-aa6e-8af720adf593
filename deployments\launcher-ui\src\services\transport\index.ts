import { ILogService, LogService } from '../log';

export type SubscriberFunction = (eventName?: string, data?: unknown) => void;
export abstract class TransportService {
  logService: ILogService;

  constructor(logService?: ILogService) {
    this.logService = logService || new LogService();
  }

  abstract publishEvent(eventName: string, data?: unknown): void;
  abstract subscribeEvent(
    eventName: string,
    subscriber: SubscriberFunction
  ): string;
  abstract subscribeEventOnce(
    eventName: string,
    subscriber: SubscriberFunction
  ): void;
  abstract unsubscribe(eventName: string): void;
  abstract unbind(token: string): void;
}
