import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { config } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';
import { MqttWrapper } from '../../../lib/mqttWrapper';

// eslint-disable-next-line max-lines-per-function
let tokenHost: string;
let tokenInvited: string;
let tokenRestricted: string;
let roomId: string;

beforeEach(async () => {
  tokenHost = await socialApi.loginIn(
    config.inviteUsername,
    config.invitePassword
  );
  tokenInvited = await socialApi.loginIn(
    config.invitedUsername,
    config.invitedPassword
  );
  tokenRestricted = await socialApi.loginIn(
    config.restrictedUsername,
    config.restrictedPassword
  );
});

afterEach(async () => {
  await socialApi.deleteRoom(tokenHost, roomId, config.inviteUserId);
  await socialApi.deleteRoom(tokenInvited, roomId, config.invitedUserId);
  await socialApi.loginOut(tokenHost);
  await socialApi.loginOut(tokenInvited);
  await socialApi.loginOut(tokenRestricted);
});
// eslint-disable-next-line max-lines-per-function
describe('', () => {
  /**
   * Checking join Room
   * - Create room
   * - Get roomMember=1
   * - joinRoon
   * - Get roomMember=2
   */
  it('join private Room if password is right', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(1);
    resp = await socialApi.joinRoom(tokenInvited, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({});
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(2);
  });

  it('join Room the 3rd user cannot join the maxMembers=2', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.joinRoom(tokenInvited, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({});
    resp = await socialApi.getRoom(tokenHost, roomId);
    //console.log(resp.body);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    resp = await socialApi.joinRoom(tokenRestricted, roomId);
    //console.log(11, resp.status, resp.body);
    expect(resp.status).toEqual(StatusCodes.FORBIDDEN);
    expect(resp.body).toEqual({
      code: 403,
      message: 'Exceeded maximum',
    });
    resp = await socialApi.getRoom(tokenHost, roomId);
    //console.log(resp.body);
    expect(resp.body).toHaveProperty('maxMembers', 2);
  });
  it('join private Room if password is null ', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(1);
    resp = await request(config.socialEndpoints.current.api)
      .post(`/chat/rooms/${roomId}/members`)
      .set('Authorization', 'Bearer ' + tokenInvited)
      .send({});
    expect(resp.status).toEqual(StatusCodes.FORBIDDEN);
    expect(resp.body).toEqual({
      code: 403,
      message: 'password is null',
    });
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(2);
  });
  it('join private Room if password is error ', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(1);
    resp = await request(config.socialEndpoints.current.api)
      .post(`/chat/rooms/${roomId}/members`)
      .set('Authorization', 'Bearer ' + tokenInvited)
      .send({ password: '11' });
    expect(resp.status).toEqual(StatusCodes.FORBIDDEN);
    expect(resp.body).toEqual({
      code: 403,
      message: 'password is error',
    });
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(2);
  });
  it('join public Room', async () => {
    let resp: request.Response = await request(config.socialEndpoints.current.api)
      .post('/chat/rooms')
      .set('Authorization', 'Bearer ' + tokenHost)
      .send({
        maxMembers: 2,
        public: true,
      });
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(1);
    resp = await request(config.socialEndpoints.current.api)
      .post(`/chat/rooms/${roomId}/members`)
      .set('Authorization', 'Bearer ' + tokenInvited)
      .send({});
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({});
    resp = await socialApi.getRoomMembers(tokenHost, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body.items).toHaveLength(2);
  });
  it('join private Room if Join two rooms', async () => {
    let resp: request.Response = await socialApi.createRoom(tokenHost, 2);
    expect(resp.status).toEqual(StatusCodes.CREATED);
    expect(resp.body).toHaveProperty('maxMembers', 2);
    roomId = resp.body.groupid;
    resp = await socialApi.joinRoom(tokenInvited, roomId);
    expect(resp.status).toEqual(StatusCodes.OK);
    expect(resp.body).toEqual({});
    resp = await socialApi.getRooms(tokenInvited);
    const lengt = resp.body.items.length;
    resp = await socialApi.createRoom(tokenHost, 2);
    const room = resp.body.groupid;
    resp = await socialApi.joinRoom(tokenInvited, room);
    resp = await socialApi.getRooms(tokenInvited);
    expect(resp.body.items).toHaveLength(lengt + 1);
    await socialApi.deleteRoom(tokenHost, room, config.inviteUserId);
    await socialApi.deleteRoom(tokenInvited, room, config.invitedUserId);
  });
});
