version: '3.7'
services:
  erlang:
    image: t2gp/erlang:********
    build:
      context: .
      dockerfile: ./Dockerfile-erlang
    depends_on:
      - dynamodb
      - redis
      - httpbin
    volumes:
      - ..:/app
    working_dir: /app
    command: sh
    env_file:
      - ../.env
    stdin_open: true # docker run -i
    tty: true # docker run -t
    hostname: erlang
    networks:
      devcontainer:
        ipv4_address: *************

  dynamodb:
    image: amazon/dynamodb-local
    command: ['-jar', 'DynamoDBLocal.jar', '-sharedDb']
    hostname: dynamodb
    networks:
      devcontainer:
        ipv4_address: *************

  # redis:
  #   image: "redis:alpine"
  #   logging:
  #     options:
  #       max-size: "10m"
  #       max-file: "3"
  redis:
    image: 'redislabs/redismod:latest'
    logging:
      options:
        max-size: '10m'
        max-file: '3'
    volumes:
      - '.:/usr/local/etc/redis'
    entrypoint:
      - '/bin/bash'
      - '/usr/local/etc/redis/create-redis-cluster.sh'
    hostname: redis
    networks:
      devcontainer:
        ipv4_address: *************
    ports:
      - '6379:6379'
      - '6380:6380'
      - '6381:6381'

  httpbin:
    image: kennethreitz/httpbin
    hostname: httpbin
    networks:
      devcontainer:
        ipv4_address: *************

networks:
  devcontainer:
    ipam:
      driver: default
      config:
        - subnet: '*************/24'
          gateway: '*************'
