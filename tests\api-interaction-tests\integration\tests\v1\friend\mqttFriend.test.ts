import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { MqttClientManager } from '../../../../integration/lib/mqttWrapper';
import { TwokAccounts } from '../../../../integration/lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;
let mqttClientMgr: MqttClientManager;
const message: string = 'T2GP Social Automated Testing';

beforeAll(async () => {
  usersTwok = new TwokAccounts(2);
  await usersTwok.loginAll({});

  mqttClientMgr = new MqttClientManager(usersTwok.acct);
  await mqttClientMgr.waitConnectAll();
});

afterAll(async () => {
  mqttClientMgr.stopAll();

  await usersTwok.logoutAll({});
});

afterEach(async () => {
  mqttClientMgr.removeAllListenersAll('message');
});

describe(`friendInvite${_ds}`, () => {
  let expectedMqttMsgType = "friendInvite";

  afterEach(async () => {
    await socialApi.deleteFriend(usersTwok.acct["001"], usersTwok.acct["002"].publicId);
  });

  /* test subject functions */
  async function sendFriendInvite() {
    let r = await socialApi.makeFriendsV1(
      usersTwok.acct["001"],
      { userid: usersTwok.acct["002"].publicId, message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each`
    testSubjectFunc     | scenario
    ${sendFriendInvite} | ${"send friend invite[public v1]"}
  `('[happy]$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `send a user a friend invitation; check ${expectedMqttMsgType} mqtt message`,
      expected: `the user receives the ${expectedMqttMsgType} message; the content of the message is correct;
the number of the message is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          inviter: usersTwok.acct["001"].publicId,
          status: 'pending'
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserTopic(usersTwok.acct["002"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["002"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

describe(`friendInvite${_ds}`, () => {
  let expectedMqttMsgType = "friendInvite";

  afterEach(async () => {
    await socialApi.deleteFriend(usersTwok.acct["001"], usersTwok.acct["002"].publicId);
    await socialApi.updateBlockListV1(usersTwok.acct["002"], {remove: [usersTwok.acct["001"].publicId]});
  });

  /* test subject functions */
  async function sendBlockerFriendInvite() {
    let r;
    r = await socialApi.updateBlockListV1(usersTwok.acct["002"], {add: [usersTwok.acct["001"].publicId]});
    socialApi.testStatus(StatusCodes.OK, r);
    r = await socialApi.makeFriendsV1(
      usersTwok.acct["001"],
      { userid: usersTwok.acct["002"].publicId, message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each`
    testSubjectFunc            | scenario
    ${sendBlockerFriendInvite} | ${"send friend invite to blocker[public v1]"}
  `('[happy]$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `send blocker a friend invitation; check ${expectedMqttMsgType} mqtt message`,
      expected: `the blocker does not receive the ${expectedMqttMsgType} message; the number of message is zero`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          inviter: usersTwok.acct["001"].publicId,
          status: 'pending'
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserTopic(usersTwok.acct["002"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["002"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // note: passing scenario is the notification never comes and wait loop exits with max timeout.
    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt == 0, 5, 1000);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(0)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

describe(`friendRemoved${_ds}`, () => {
  let expectedMqttMsgType = "friendRemoved";

  /* test subject functions */
  async function removeFriend() {
    let r;
    r = await socialApi.makeFriendsV1(
      usersTwok.acct["001"],
      { userid: usersTwok.acct["002"].publicId, message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);
    r = await socialApi.deleteFriend(usersTwok.acct["001"], usersTwok.acct["002"].publicId);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each`
    testSubjectFunc | scenario
    ${removeFriend} | ${"remove friend[public v1]"}
  `('[happy]$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `delete a friend; check ${expectedMqttMsgType} mqtt message`,
      expected: `deleter gets the ${expectedMqttMsgType} message; the content of the message is correct; the number of the message is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}"`),
      {
        data: {
          inviter: usersTwok.acct["001"].publicId,
          status: 'unfriend'
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserTopic(usersTwok.acct["001"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["001"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected number of the ${expectedMqttMsgType} message`
        }
      }
    );
  });
});

describe(`presence[public v1]${_ds}`, () => {
  let expectedMqttMsgType = "presence";

  beforeAll(async () => {
    let r = await socialApi.makeFriendsV1(
      usersTwok.acct["001"],
      { userid: usersTwok.acct["002"].publicId, message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);

    r = await socialApi.makeFriendsV1(
      usersTwok.acct["002"],
      { userid: usersTwok.acct["001"].publicId, message: message }
    );
    socialApi.testStatus(StatusCodes.OK, r);
  })

  afterAll(async () => {
    await socialApi.deleteFriend(usersTwok.acct["001"], usersTwok.acct["002"].publicId);
  });

  /* test subject functions */
  async function friend1SetPresence(status: string) {
    let r = await socialApi.setPresenceV1(
      usersTwok.acct["001"],
      {status: status, gameName: "Automated API Tests Game Name"}
    );
    socialApi.testStatus(StatusCodes.OK, r);
  }

  async function friend1ClearPresence() {
    let r = await socialApi.clearPresence(usersTwok.acct["001"]);
    socialApi.testStatus(StatusCodes.OK, r);
  }

  it.each`
    testSubjectFunc       | scenario                                  | status
    ${friend1SetPresence} | ${"friend1 sets presence online"}         | ${"online"}
    ${friend1SetPresence} | ${"friend1 sets presence offline"}        | ${"offline"}
    ${friend1SetPresence} | ${"friend1 sets presence playing"}        | ${"playing"}
    ${friend1SetPresence} | ${"friend1 sets presence custom"}         | ${"custom"}
    ${friend1SetPresence} | ${"friend1 sets presence away"}           | ${"away"}
    ${friend1SetPresence} | ${"friend1 sets presence dnd"}            | ${"dnd"}
    ${friend1SetPresence} | ${"friend1 sets presence chat"}           | ${"chat"}
    ${friend1SetPresence} | ${"friend1 sets presence authenticating"} | ${"authenticating"}
  `('[happy]$scenario', async ({testSubjectFunc, status}) => {
    let testCase = {
      description: `friend1 sets presence status to be ${status}, check ${expectedMqttMsgType} mqtt message`,
      expected: `friend2 gets the ${expectedMqttMsgType} message; the content of the messages is correct;
the number of the messages is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}`),
      {
        data: {
          userid: usersTwok.acct["001"].publicId,
          status: status
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserPresenceTopic(usersTwok.acct["001"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["002"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc(status);

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of ${expectedMqttMsgType} message`
        },
      }
    );
  });

  it.each`
    testSubjectFunc         | scenario
    ${friend1ClearPresence} | ${"friend1 clears presence"}
  `('[happy]$scenario', async ({testSubjectFunc}) => {
    let testCase = {
      description: `friend1 clears presence, check the ${expectedMqttMsgType} mqtt message`,
      expected: `friend2 get the ${expectedMqttMsgType} message; the content of the messages is correct;
the number of the messages is correct`
    };

    // verify message contents
    let occurCount = {cnt: 0};
    let vmc = socialApi.makeVmc(
      new RegExp(String.raw`"type":"${expectedMqttMsgType}`),
      {
        data: {
          userid: "",
          status: ""
        },
        type: expectedMqttMsgType
      },
      socialApi.getUserPresenceTopic(usersTwok.acct["001"].publicId),
      occurCount
    );

    // add listener
    mqttClientMgr.getClients()["002"].getClient().on('message', vmc);

    // main test subject
    await testSubjectFunc();

    // hang around for a while in case notifications are late
    await socialApi.waitWhile(async () => occurCount.cnt < 1, 5000, 1);

    // verify occurrence
    socialApi.expectMore(
      () => {expect(occurCount.cnt).toBe(1)},
      testCase,
      {
        additionalInfo: {
          "fail reason": `unexpected numbers of ${expectedMqttMsgType} message`
        },
      }
    );
  });
});