package apipub

import (
	"time"
)

// Defines values for MqttMessageType.
const (
	MqttMessageTypeChatMessageDM        MqttMessageType = "chatMessageDM"
	MqttMessageTypeChatMessageGroup     MqttMessageType = "chatMessageGroup"
	MqttMessageTypeFriendInvite         MqttMessageType = "friendInvite"
	MqttMessageTypeFriendRemoved        MqttMessageType = "friendRemoved"
	MqttMessageTypeGroupControlMessage  MqttMessageType = "groupControlMessage"
	MqttMessageTypeGroupInviteReceived  MqttMessageType = "groupInviteReceived"
	MqttMessageTypeGroupJoinRequest     MqttMessageType = "groupJoinRequest"
	MqttMessageTypeGroupMembersModified MqttMessageType = "groupMembersModified"
	MqttMessageTypeGroupModified        MqttMessageType = "groupModified"
	MqttMessageTypePresence             MqttMessageType = "presence"
)

// MqttControlMessage defines model for mqttControlMessage.
type MqttControlMessage struct {
	Event *string `json:"event,omitempty"`

	// Groupid DEPRECATED.  redundant with groupid in url.
	Groupid *string `json:"groupid,omitempty"`

	// Payload Payload should consist of only single byte ASCII characters.  We can not guarantee length or behavior of the social service if non-ascii characters are used.
	Payload string `json:"payload"`

	// Senderid DEPRECATED.  pulled from user JWT.
	Senderid *string `json:"senderid,omitempty"`
}

// MqttFriendStatusChange defines model for mqttFriendStatusChange.
type MqttFriendStatusChange struct {
	Inviteeid *string `json:"inviteeid,omitempty"`
	Inviter   *string `json:"inviter,omitempty"`

	// InviterOST The online service type of the specified Platform Account.
	//
	// List of types for reference:
	// * 0 - UNKNOWN
	// * 1 - XBOX LIVE
	// * 2 - SONY ENTERTAINMENT NETWORK
	// * 3 - STEAM
	// * 4 - WEB
	// * 5 - LEGACY GAME CENTER
	// * 6 - GOOGLE PLAY
	// * 9 - WINDOWS PHONE
	// * 10 - CALICO
	// * 11 - NINTENDO
	// * 12 - GAME CENTER
	// * 13 - WEGAME
	// * 14 - VORTEX
	// * 15 - EPIC
	// * 16 - STADIA
	// * 17 - FACEBOOK
	// * 18 - GOOGLE
	// * 19 - TWITTER
	// * 20 - TWITCH
	// * 21 - DEVICE
	// * 22 - APPLE
	// * 23 - ZENDESK
	// * 24 - T2GP
	// * 99 - WINDOWS DEVELOPER
	InviterOST *OnlineServiceType `json:"inviterOST,omitempty"`
	Message    *string            `json:"message,omitempty"`
	Name       *string            `json:"name,omitempty"`
	Status     *string            `json:"status,omitempty"`
}

// MqttGroupMemberModified defines model for mqttGroupMemberModified.
type MqttGroupMemberModified struct {
	Action          MqttGroupMemberModifiedAction `json:"action"`
	Groupid         string                        `json:"groupid"`
	PostRole        GroupMemberRole               `json:"postRole"`
	PreRole         GroupMemberRole               `json:"preRole"`
	Reason          string                        `json:"reason"`
	Userid          string                        `json:"userid"`
	MetaLastUpdated *uint64                       `json:"metaLastUpdated"`
}

// MqttGroupMemberModifiedAction defines model for MqttGroupMemberModified.Action.
type MqttGroupMemberModifiedAction string

// MqttGroupMemberPresence defines model for mqttGroupMemberPresence.
type MqttGroupMemberPresence struct {
	ActiveGroup *ActiveGroupResponse `json:"activeGroup,omitempty"`

	// Clientid client id of mqtt connection for mqtt presence
	Clientid     *string `json:"clientid,omitempty"`
	CustomStatus *string `json:"customStatus,omitempty"`

	// GameData structure for games to send additional presence information for internal use.
	GameData *string `json:"gameData,omitempty"`

	// GameName Possibly pull this from somewhere for localization in the future.
	GameName string `json:"gameName"`
	Groupid  string `json:"groupid"`

	// JoinContext Context used to join a game session
	JoinContext *JoinContext `json:"joinContext,omitempty"`

	// KeepAliveFor How long in seconds before this presence will be considered offline if no presence Heartbeat is made.   | This is an optional value for those who are not using our MQTT.  People using our MQTT will have this functionality via our mqtt plugin.  Timeout set to 5 minutes for auto drop from group.
	Ttl *int `json:"ttl,omitempty"`

	// Meta Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
	Meta *map[string]interface{} `json:"meta"`

	// OnlineServiceType The online service type of the specified Platform Account.
	//
	// List of types for reference:
	// * 0 - UNKNOWN
	// * 1 - XBOX LIVE
	// * 2 - SONY ENTERTAINMENT NETWORK
	// * 3 - STEAM
	// * 4 - WEB
	// * 5 - LEGACY GAME CENTER
	// * 6 - GOOGLE PLAY
	// * 9 - WINDOWS PHONE
	// * 10 - CALICO
	// * 11 - NINTENDO
	// * 12 - GAME CENTER
	// * 13 - WEGAME
	// * 14 - VORTEX
	// * 15 - EPIC
	// * 16 - STADIA
	// * 17 - FACEBOOK
	// * 18 - GOOGLE
	// * 19 - TWITTER
	// * 20 - TWITCH
	// * 21 - DEVICE
	// * 22 - APPLE
	// * 23 - ZENDESK
	// * 24 - T2GP
	// * 99 - WINDOWS DEVELOPER
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// Platformid at the time the presence is set, if the token has a pai claim.  this will be the sub claim, which should be the platformid.  we do not guarantee this value will be returned since full account tokens will not have it.
	Platformid *string `json:"platformid,omitempty"`

	// Priority Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc).| 40000 = mqtt server(connected/disconnected).  offline will remove from list.
	Priority int `json:"priority"`

	// Productid pulled from JWT
	Productid string `json:"productid"`

	// RichPresence string to be displayed for rich presence.  eventually will support templating for localization.
	RichPresence *string        `json:"richPresence,omitempty"`
	Status       PresenceStatus `json:"status"`

	// Timestamp timestamp set by server.
	Timestamp time.Time `json:"timestamp"`
	Userid    string    `json:"userid"`
}

// MqttGroupModified defines model for mqttGroupModified.
type MqttGroupModified struct {
	Action  string `json:"action"`
	Groupid string `json:"groupid"`

	// Property what property changed.  space delimited if multipe properties change.
	Property *string `json:"property,omitempty"`
}

// MqttInviteReceived schema for response for a user's invites
type MqttInviteReceived struct {
	// Approverid the userid of who either approved the join request or invited the user.
	Approverid Dnaid `json:"approverid"`

	// DisplayName display name of either requester (join requests) or inviter (invites).  this is provided so that it can be displayed in the UI.
	DisplayName *DisplayName `json:"displayName,omitempty"`

	// FirstPartyid use to track original first party id when invite was sent (internal use)
	FirstPartyid *FirstPartyid `json:"firstPartyid,omitempty"`

	// Groupid the id of the group
	Groupid Groupid `json:"groupid"`

	// IsFirstPartyInvite a flag to indicate whether this invite should be processed as a first party invite
	IsFirstPartyInvite *IsFirstPartyInvite `json:"isFirstPartyInvite,omitempty"`

	// Memberid the userid of the who will be joining the group
	Memberid Dnaid `json:"memberid"`

	// OnlineServiceType The online service type of the specified Platform Account.
	//
	// List of types for reference:
	// * 0 - UNKNOWN
	// * 1 - XBOX LIVE
	// * 2 - SONY ENTERTAINMENT NETWORK
	// * 3 - STEAM
	// * 4 - WEB
	// * 5 - LEGACY GAME CENTER
	// * 6 - GOOGLE PLAY
	// * 9 - WINDOWS PHONE
	// * 10 - CALICO
	// * 11 - NINTENDO
	// * 12 - GAME CENTER
	// * 13 - WEGAME
	// * 14 - VORTEX
	// * 15 - EPIC
	// * 16 - STADIA
	// * 17 - FACEBOOK
	// * 18 - GOOGLE
	// * 19 - TWITTER
	// * 20 - TWITCH
	// * 21 - DEVICE
	// * 22 - APPLE
	// * 23 - ZENDESK
	// * 24 - T2GP
	// * 99 - WINDOWS DEVELOPER
	OnlineServiceType OnlineServiceType `json:"onlineServiceType"`

	// Productid the id of the product
	Productid *Productid `json:"productid,omitempty"`

	// Status The membership status of the invite or request to join state.
	// * requested - a request to join flow initiated
	// * approved - group join request has been approved
	// * rejected - group join request has been rejected
	// * invited - the user has been invited to the group
	// * accepted - the user has accepted the invite to the group
	// * declined - the invite has been declined
	// * revoked - the invite has been revoked
	Status MembershipStatus `json:"status"`

	// Ttl time to live of the invite
	Ttl *Ttl `json:"ttl"`
}

// MqttJoinRequest defines model for mqttJoinRequest.
type MqttJoinRequest struct {
	Approverid *string `json:"approverid,omitempty"`
	Groupid    string  `json:"groupid"`
	Name       *string `json:"name,omitempty"`
	Productid  string  `json:"productid"`

	// Status The membership status of the invite or request to join state.
	// * requested - a request to join flow initiated
	// * approved - group join request has been approved
	// * rejected - group join request has been rejected
	// * invited - the user has been invited to the group
	// * accepted - the user has accepted the invite to the group
	// * declined - the invite has been declined
	// * revoked - the invite has been revoked
	Status MembershipStatus `json:"status"`
	Userid string           `json:"userid"`
}

// MqttEndorsementReceived defines model for endorsement mqtt message
type MqttEndorsementReceived struct {
	EndorsementName         string `json:"endorsementName,omitempty"`
	CurrentEndorsementCount int    `json:"currentEndorsementCount"`
	TotalEndorsementCount   int    `json:"totalEndorsementCount,omitempty"`
	IsPositive              bool   `json:"isPositive,omitempty"`
	IsPrivate               bool   `json:"isPrivate,omitempty"`
}

//// MqttMessage defines model for mqttMessage.
//type MqttMessage struct {
//	Data MqttMessage_Data `json:"data"`
//	Type MqttMessageType  `json:"type"`
//}
//
//// MqttMessage_Data defines model for MqttMessage.Data.
//type MqttMessage_Data struct {
//	union json.RawMessage
//}

// MqttMessageType defines model for mqttMessageType.
type MqttMessageType string
