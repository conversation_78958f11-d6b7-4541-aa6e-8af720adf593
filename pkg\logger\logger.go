// Package logger log utilities for zerolog and datadog
package logger

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"io"
	"net/http"
	"runtime/debug"
	"strconv"
	"strings"
	"time"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
	"github.com/rs/zerolog/pkgerrors"

	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

const BearerAuthJWT = "bearerAuth.JWT"

// SetupLogger sets up zerolog
func SetupLogger(debugLog bool) {
	zerolog.SetGlobalLevel(zerolog.InfoLevel)
	if debugLog {
		zerolog.SetGlobalLevel(zerolog.DebugLevel)
	}

	zerolog.TimeFieldFormat = zerolog.TimeFormatUnix
	zerolog.TimestampFieldName = "ts"
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack
}

// NewLogger creates a new logger
func NewLogger() func(next http.Handler) http.Handler {
	return middleware.RequestLogger(&StructuredLogger{&log.Logger})
}

// StructuredLogger zero logger struct
type StructuredLogger struct {
	Logger *zerolog.Logger
}

// NewLogEntry creates a new log entry
func (l *StructuredLogger) NewLogEntry(r *http.Request) middleware.LogEntry {
	ctx := l.Logger.With().Timestamp().Caller().Stack()

	if reqID := middleware.GetReqID(r.Context()); reqID != "" {
		ctx = ctx.Str("request_id", reqID)
	}

	span, _ := tracer.SpanFromContext(r.Context())
	span.SetTag(ext.ResourceName, fmt.Sprintf("%s %s", r.Method, r.URL.Path))
	span.SetTag("request_id", middleware.GetReqID(r.Context()))
	span.SetTag("remote_ip", r.RemoteAddr)
	if r.URL.RawQuery != "" {
		span.SetTag("http.query_string", r.URL.RawQuery)
	}

	var gitRevision string
	buildInfo, ok := debug.ReadBuildInfo()
	if ok {
		for _, v := range buildInfo.Settings {
			if v.Key == "vcs.revision" {
				gitRevision = v.Value
				break
			}
		}
	}
	ctx = ctx.Str("git_revision", gitRevision)

	v := r.Context().Value(BearerAuthJWT)
	if v != nil {
		if token, ok := v.(*jwt.Token); ok && token.Claims != nil {
			ctx = ctx.RawJSON("jwt", []byte(utils.EncodeJson(token.Claims)))
		}
	}

	if r.Body != nil && !strings.Contains(strings.ToLower(r.RequestURI), "login") {
		bodyBytes, _ := io.ReadAll(r.Body)
		r.Body.Close()
		r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

		if len(bodyBytes) > 0 {
			if strings.EqualFold(r.Header.Get("content-type"), "application/json") {
				buffer := new(bytes.Buffer)
				err := json.Compact(buffer, bodyBytes)
				if err == nil {
					ctx = ctx.RawJSON("body", buffer.Bytes())
				} else {
					ctx = ctx.RawJSON("body", bodyBytes)
				}
			} else {
				ctx = ctx.Str("body", string(bodyBytes))
			}
		}
	}

	scheme := "http"
	if r.TLS != nil {
		scheme = "https"
	}
	defaultFields := map[string]interface{}{
		"scheme":              scheme,
		"proto":               r.Proto,
		"method":              r.Method,
		"remote_ip":           r.RemoteAddr,
		"user_agent":          r.UserAgent(),
		"uri":                 fmt.Sprintf("%s://%s%s", scheme, r.Host, r.RequestURI),
		"dd.trace_id":         span.Context().TraceID(),
		"dd.span_id":          span.Context().SpanID(),
		"plugin_version":      r.Header.Get("t2gp-plugin-version"),
		"engine_version":      r.Header.Get("engine-version"),
		"engine_identifier":   r.Header.Get("engine-identifier"),
		"platform_identifier": r.Header.Get("platform-identifier"),
		"client_version":      r.Header.Get("client-version"),
	}
	ctx = ctx.Fields(defaultFields)

	//Burst sampling to reduce repetitive logging.  3/s
	ctxlog := ctx.Logger().
		Sample(&zerolog.BurstSampler{
			Burst:  3,
			Period: 1 * time.Second,
		})
	entry := &StructuredLoggerEntry{ctxlogger: &ctxlog, r: r}
	return entry
}

// StructuredLoggerEntry struct for log entry
type StructuredLoggerEntry struct {
	ctxlogger *zerolog.Logger
	r         *http.Request
}

// Write writes to the log file
func (l *StructuredLoggerEntry) Write(status, bytes int, header http.Header, elapsed time.Duration, extra interface{}) {
	span, _ := tracer.SpanFromContext(l.r.Context())
	span.SetTag(ext.HTTPCode, strconv.Itoa(status))
	defer span.Finish()

	r := l.r

	e := l.ctxlogger.Info()

	if utils.IgnoreRequest(r) {
		return
	}

	e.Fields(map[string]interface{}{
		"status":     status,
		"bytes_out":  bytes,
		"elapsed_ms": float64(elapsed.Nanoseconds()) / 1000000.0,
	})

	e.Str("path", l.r.RequestURI).Str("event", fmt.Sprintf("%s %s", l.r.Method, l.r.RequestURI)).Msgf("%s %s", l.r.Method, l.r.RequestURI)
	l.r = nil
	l.ctxlogger = nil
}

// Panic creates a panic stack trace
func (l *StructuredLoggerEntry) Panic(v interface{}, stack []byte) {
	e := l.ctxlogger.Error()

	e.Fields(map[string]interface{}{
		"stack":      string(stack),
		"panic":      fmt.Sprintf("%+v", v),
		"request_id": middleware.GetReqID(l.r.Context()),
	}).Msg("!!! Panic !!!")
}

// Get fetches a zero logger object
func Get(r *http.Request) *zerolog.Logger {
	if r != nil {
		l := middleware.GetLogEntry(r)
		if l != nil {
			entry := l.(*StructuredLoggerEntry)
			if entry != nil {
				return entry.ctxlogger
			}
		}
	}
	return &log.Logger
}

func FromContext(ctx context.Context) *zerolog.Logger {
	l := ctx.Value(middleware.LogEntryCtxKey)
	if l != nil {
		entry := l.(*StructuredLoggerEntry)
		if entry != nil {
			return entry.ctxlogger
		}
	}
	return &log.Logger
}
