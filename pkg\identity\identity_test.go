package identity

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/franela/goblin"
	"github.com/joho/godotenv"
	"github.com/rs/zerolog"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	gomock "go.uber.org/mock/gomock"
)

var cfg *config.Config
var ctx context.Context = context.Background()
var sm *health.ServiceMonitor
var id *Identity

type MockIdentity struct {
	ctrl        *gomock.Controller
	cfg         *config.Config
	sm          *health.MockServiceMonitorInterface
	ids         *MockIdentityServiceInterface
	id          *Identity
	testTimeout time.Duration
}

func NewMockIdentity(t *testing.T) *MockIdentity {
	mockCtrl := gomock.NewController(t)
	cfg := config.ConfigForTests()
	ids := NewMockIdentityServiceInterface(mockCtrl)
	sm := health.NewMockServiceMonitorInterface(mockCtrl)
	sm.EXPECT().AddDependentService(gomock.Any(), gomock.Any()).AnyTimes()
	id := NewIdentity(ctx, cfg, sm)
	id.providers = map[IdentityService]IdentityServiceInterface{
		IdentityServiceDNA:             ids,
		IdentityServicePrivateDivision: ids,
		IdentityServiceRockstar:        ids,
	}
	return &MockIdentity{
		ctrl:        mockCtrl,
		cfg:         cfg,
		sm:          sm,
		ids:         ids,
		id:          id,
		testTimeout: time.Duration(30 * time.Second),
	}
}

func TestNewIdentity(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockIdentity(t)
	defer mock.ctrl.Finish()

	g.Describe("NewIdentity", func() {
		g.It("should succeed", func() {
			g.Timeout(45 * time.Second)
			id = NewIdentity(ctx, cfg, sm)
			id.timeout = time.Second
			// test stopping nil ticker doesn't crash
			// does nothing since ticker is nil
			id.Stop()

			id.Start(context.Background())
			g.Assert(id).IsNotNil()

			// sleep to allow go routine to start
			time.Sleep(100 * time.Millisecond)
			t := id.ticker.Load()
			g.Assert(t).IsNotNil()

			time.Sleep(2 * time.Second)

			id.Stop()
			t = id.ticker.Load()
			g.Assert(t).IsNil()
		})
	})

}

func TestMain(m *testing.M) {
	zerolog.SetGlobalLevel(zerolog.FatalLevel)
	setup()
	code := m.Run()
	teardown()
	os.Exit(code)
}

func setup() {
	godotenv.Load("../../.env")
	cfg = config.ConfigForTests()
	sm = health.NewServiceMonitor("test-social", cfg.Version.Version)

	// disable log
	zerolog.SetGlobalLevel(zerolog.FatalLevel)
}

func teardown() {
}

func TestGetIdentityService(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GetIdentityService", func() {
		g.It("should should succeed", func() {
			g.Timeout(45 * time.Second)
			mock := NewMockIdentity(t)
			defer mock.ctrl.Finish()

			ids := mock.id.GetIdentityService(IdentityServicePrivateDivision)
			g.Assert(ids).Equal(mock.ids)

			ids = mock.id.GetIdentityService(IdentityService(IdentityServiceUnknown))
			g.Assert(ids).IsNil()
		})
	})
}

func TestGetIdentityServiceFromContext(t *testing.T) {
	g := goblin.Goblin(t)

	g.Describe("GetIdentityServiceFromContext", func() {
		g.It("should should succeed", func() {
			g.Timeout(45 * time.Second)
			mock := NewMockIdentity(t)
			defer mock.ctrl.Finish()

			// no label defaults to DNA

			ids := mock.id.GetIdentityServiceFromContext(ctx)
			g.Assert(ids).Equal(mock.id.providers[IdentityServiceDNA])

			ctxRsg := context.WithValue(ctx, constants.T2GPCtxTenant, "rsg")
			mock.id.GetIdentityService(IdentityServiceRockstar)
			ids = mock.id.GetIdentityServiceFromContext(ctxRsg)
			g.Assert(ids).Equal(mock.id.providers[IdentityServiceRockstar])

			ctxPd := context.WithValue(ctx, constants.T2GPCtxTenant, "pdi")
			mock.id.GetIdentityService(IdentityServicePrivateDivision)
			ids = mock.id.GetIdentityServiceFromContext(ctxPd)
			g.Assert(ids).Equal(mock.id.providers[IdentityServicePrivateDivision])

			// unknown should default to DNA
			ctxUnknown := context.WithValue(ctx, constants.T2GPCtxTenant, "ukn")
			mock.id.GetIdentityService(IdentityServiceUnknown)
			ids = mock.id.GetIdentityServiceFromContext(ctxUnknown)
			g.Assert(ids).Equal(mock.id.providers[IdentityServiceDNA])
		})
	})
}

func TestIdentityServiceFunctions(t *testing.T) {
	g := goblin.Goblin(t)
	mock := NewMockIdentity(t)
	defer mock.ctrl.Finish()
	g.Describe("IdentityServiceFunctions", func() {
		g.It("SearchAccounts should succeed", func() {
			g.Timeout(mock.testTimeout)

			mock.ids.EXPECT().SearchAccounts(ctx, nil).Return(nil, nil)
			mock.id.SearchAccounts(ctx, nil)
		})

		g.It("SearchAccountsByUserID should succeed", func() {
			g.Timeout(mock.testTimeout)

			userid := "1234"
			mock.ids.EXPECT().SearchAccountsByUserID(ctx, userid).Return(nil, nil)
			mock.id.SearchAccountsByUserID(ctx, userid)
		})

		g.It("GetUserProfileAccountLinks should succeed", func() {
			g.Timeout(mock.testTimeout)

			userid := "1234"
			mock.ids.EXPECT().GetUserProfileAccountLinks(ctx, userid).Return(nil, nil)
			mock.id.GetUserProfileAccountLinks(ctx, userid)
		})

		g.It("Authenticate should succeed", func() {
			g.Timeout(mock.testTimeout)
			mock.ids.EXPECT().Authenticate(ctx, nil, false).Return(nil, nil)
			mock.id.Authenticate(ctx, nil, false)
		})

		g.It("Login should succeed", func() {
			g.Timeout(mock.testTimeout)

			email := "<EMAIL>"
			pw := "p@ssw0rd"
			locale := "en-US"
			appid := "appid"
			mock.ids.EXPECT().Login(ctx, email, pw, locale, appid).Return(nil, nil)
			mock.id.Login(ctx, email, pw, locale, appid)
		})

		g.It("RefreshToken should succeed", func() {
			g.Timeout(mock.testTimeout)

			token := "token"
			locale := "en-US"
			mock.ids.EXPECT().RefreshToken(ctx, token, locale).Return(nil, nil)
			mock.id.RefreshToken(ctx, token, locale)
		})

		g.It("Logout should succeed", func() {
			g.Timeout(mock.testTimeout)

			token := "token"
			mock.ids.EXPECT().Logout(ctx, token).Return(nil)
			mock.id.Logout(ctx, token)
		})
	})
}
