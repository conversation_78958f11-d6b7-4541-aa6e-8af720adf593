<script lang="ts">
  import { setContext } from 'svelte';
  import {
    CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT,
    INITIAL_LANGUAGE,
  } from '../../../constant';
  import { initI18nContext, SocialServicesContext } from '../../../context';
  import { useSearchTerm } from '../../../hooks/useSearchTerm';
  import FriendsSearch from '../FriendsSearch.svelte';

  export let context: SocialServicesContext;
  export let initSearchTerm = '';
  const searchTerm = useSearchTerm();

  initI18nContext(INITIAL_LANGUAGE);
  setContext(CONTEXT_KEY_SOCIAL_SERVICES_CONTEXT, context);

  if (initSearchTerm) {
    searchTerm.set(initSearchTerm);
  }
</script>

<FriendsSearch />
