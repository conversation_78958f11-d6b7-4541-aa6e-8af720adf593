package api

import (
	"net/http"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	. "github.com/onsi/gomega"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	gomock "go.uber.org/mock/gomock"
)

func TestModifyBlocklist(t *testing.T) {
	g := goblin.Goblin(t)

	userid2 := "e12c3df480984141b2f385646b2024fa"
	//userid3 := "2017e9305ccc4e5781d076403c1b6725"
	//userids := []string{userid2, userid3}

	badUserId1 := "console.log(hello)  "

	//name := "user2"
	//name2 := "user3"
	accountid := "accountid1"
	accountid2 := "accountid2"

	var link1 apipub.AccountLinkDNA
	var link2 apipub.AccountLinkDNA
	var links1 []apipub.AccountLinkDNA
	var links2 []apipub.AccountLinkDNA
	//var profile1 apipub.UserProfileResponse
	//var profile2 apipub.UserProfileResponse
	//var profiles []*apipub.UserProfileResponse
	//var profilesPtr *[]*apipub.UserProfileResponse

	//special hook for gomega
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	g.Describe("AddBlocklist", func() {

		g.BeforeEach(func() {
			links1 = make([]apipub.AccountLinkDNA, 1)
			links2 = make([]apipub.AccountLinkDNA, 1)
			link1 = apipub.AccountLinkDNA{
				AccountId: &accountid,
			}
			links1[0] = link1
			link2 = apipub.AccountLinkDNA{
				AccountId: &accountid2,
			}
			links2[0] = link2

			//profile1 = apipub.UserProfileResponse{
			//	Userid:      userid2,
			//	DisplayName: &name,
			//}
			//profile2 = apipub.UserProfileResponse{
			//	Userid:      userid3,
			//	DisplayName: &name2,
			//}
			//profiles = []*apipub.UserProfileResponse{&profile1, &profile2}
			//profilesPtr = &profiles
		})

		mock.redisMock.ExpectationsWereMet()

		g.It("with bad id should fail", func() {

			w, r := AddBodyToRequest("", User1JWT)
			mock.ds.EXPECT().DoesExceedMaxBlockCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Return(nil)
			mock.ds.EXPECT().ModifyBlocklist(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			mock.api.AddBlocklist(w, r, badUserId1)

			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)

			w, r = AddBodyToRequest("", User1JWT)
			mock.rc.EXPECT().RemoveFromUserBlockList(gomock.Any(), gomock.Any()).Return(nil)
			mock.api.RemoveBlocklist(w, r, badUserId1)
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
		})

		g.It("with invalid token should get unauthorized", func() {
			w, r := AddBodyToRequest("", BadJWT)
			mock.api.AddBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusUnauthorized)

			w, r = AddBodyToRequest("", BadJWT)
			mock.api.RemoveBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("with platform token should get forbidden", func() {
			w, r := AddBodyToRequest("", UserPlatformUnlinkedJWT)
			mock.api.AddBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusForbidden)

			w, r = AddBodyToRequest("", UserPlatformUnlinkedJWT)
			mock.api.RemoveBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})
		g.It("with add should return ok", func() {
			w, r := AddBodyToRequest(nil, User1JWT)
			mock.ds.EXPECT().DoesExceedMaxBlockCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Return(nil)
			mock.ds.EXPECT().ModifyBlocklist(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.tele.EXPECT().SendBlocklistEvent(gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().AddToUserBlockList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			mock.api.AddBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusOK)
		})

		g.It("with add and get userprofile from dynamo should return ok", func() {
			w, r := AddBodyToRequest(nil, User1JWT)

			mock.rc.EXPECT().AddToUserBlockList(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.ds.EXPECT().DoesExceedMaxBlockCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Return(nil)
			mock.ds.EXPECT().ModifyBlocklist(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mock.tele.EXPECT().SendBlocklistEvent(gomock.Any(), gomock.Any()).Return(nil)
			mock.api.AddBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusOK)
		})

		g.It("can't block more user because of exceeding max count", func() {
			w, r := AddBodyToRequest(nil, User1JWT)
			mock.ds.EXPECT().DoesExceedMaxBlockCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.ds.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().SyncUserProfiles(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).Return(nil)
			mock.ds.EXPECT().ModifyBlocklist(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			mock.api.AddBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)
		})

		g.It("with remove should return ok", func() {
			w, r := AddBodyToRequest(nil, User1JWT)

			mock.rc.EXPECT().RemoveFromUserBlockList(gomock.Any(), gomock.Any()).Return(nil)
			mock.api.RemoveBlocklist(w, r, userid2)
			g.Assert(w.Code).Equal(http.StatusOK)
		})
	})
}

func TestModifyBlocklistWithExcessiveId(t *testing.T) {
	g := goblin.Goblin(t)

	userid := "e12c3df480984141b2f385646b2024fa"
	mock := NewMockAPI(t)

	g.Describe("ModifyBlocklist", func() {
		g.It("excessive number of ids to add or remove will fail", func() {
			add := []string{}

			count := 0
			for count < 21 {
				add = append(add, userid)
				count++
			}

			addBody := apipub.ImportBlocklistRequestBody{
				Userids: add,
			}

			mock.ds.EXPECT().DoesExceedMaxBlockCount(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil)
			w, r := AddBodyToRequest(addBody, User1JWT)
			mock.api.ImportBlocklist(w, r)
			g.Assert(w.Code).Equal(http.StatusUnprocessableEntity)

		})
	})
}

func TestGetBlocklist(t *testing.T) {
	g := goblin.Goblin(t)

	// userid for jwt1
	userId := "b287e655461f4b3085c8f244e394ff7e"

	//special hook for gomega
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	params := apipub.GetBlocklistParams{Limit: nil, Next: nil}
	ost := apipub.OnlineServiceTypeSTEAM
	profile := &apipub.UserProfileResponse{
		Userid:            "blockedid",
		DisplayName:       aws.String("name"),
		OnlineServiceType: &ost,
	}
	var profiles []*apipub.UserProfileResponse
	profiles = append(profiles, profile)

	g.Describe("GetBlocklist", func() {
		g.It("should fail with bad JWT", func() {

			w, r := Login(BadJWT)
			mock.api.GetBlocklist(w, r, params)
			g.Assert(w.Code).Equal(http.StatusUnauthorized)
			Ω(w.Body.String()).Should(ContainSubstring(errs.New(http.StatusUnauthorized, errs.EInvalidJWT).Error()))
		})

		g.It("with invalid token should get unauthorized", func() {

			w, r := AddBodyToRequest("", BadJWT)
			mock.api.GetBlocklist(w, r, params)
			g.Assert(w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("with platform token should get forbidden", func() {
			w, r := AddBodyToRequest("", UserPlatformUnlinkedJWT)
			mock.api.GetBlocklist(w, r, params)
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})

		g.It("with blocklist exists false", func() {
			w, r := AddBodyToRequest("", User1JWT)
			blocked := apipub.BlocklistResponse{Blockedid: "blockedid", Created: time.Now().UTC(), Name: aws.String("name"), OnlineServiceType: 3, Userid: "userid"}
			ret := []*apipub.BlocklistResponse{&blocked}
			mock.rc.EXPECT().GetUserBlocklist(gomock.Any(), userId, gomock.Any(), gomock.Any()).Return(nil, "", nil).Times(1)
			mock.rc.EXPECT().GetSyncLock(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mock.ds.EXPECT().GetBlocklistWithLimit(gomock.Any(), userId, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ret, nil, nil).Times(1)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(profile, nil).AnyTimes()
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&profiles, nil).AnyTimes()
			mock.api.GetBlocklist(w, r, params)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`"userid":"` + userId + `"`))
			Ω(response).Should(ContainSubstring(`"name":"name"`))
			Ω(response).Should(ContainSubstring(`"blockedid":"blockedid"`))
		})

		g.It("with db error return bad request", func() {
			w, r := AddBodyToRequest("", User1JWT)
			mock.rc.EXPECT().GetUserBlocklist(gomock.Any(), userId, gomock.Any(), gomock.Any()).Return(nil, "", nil).Times(1)
			mock.ds.EXPECT().GetBlocklistWithLimit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, errs.New(http.StatusInternalServerError, errs.EDynamodbReadFailed)).Times(1)
			mock.rc.EXPECT().GetUserProfile(gomock.Any(), gomock.Any()).Return(profile, nil).AnyTimes()
			mock.api.GetBlocklist(w, r, params)
			g.Assert(w.Code).Equal(http.StatusInternalServerError)
		})

		g.It("with returned blocked ok", func() {
			w, r := AddBodyToRequest("", User1JWT)
			blocked := apipub.BlocklistResponse{Blockedid: "blockedid", Created: time.Now().UTC(), Name: aws.String("name"), OnlineServiceType: 3, Userid: "userid"}
			ret := []*apipub.BlocklistResponse{&blocked}
			mock.rc.EXPECT().GetUserBlocklist(r.Context(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ret, "", nil)
			mock.rc.EXPECT().GetUserProfiles(gomock.Any(), gomock.Any()).Return(&profiles, nil).AnyTimes()
			mock.api.GetBlocklist(w, r, params)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`"userid":"` + userId + `"`))
			Ω(response).Should(ContainSubstring(`"name":"name"`))
			Ω(response).Should(ContainSubstring(`"blockedid":"blockedid"`))
		})

		g.It("with returned blocked ok with correct limit", func() {
			p := params
			limit := apipub.Limit(2)
			p.Limit = &limit
			w, r := AddBodyToRequest("", User1JWT)
			blocked := apipub.BlocklistResponse{Blockedid: "blockedid", Created: time.Now().UTC(), Name: aws.String("name"), OnlineServiceType: apipub.OnlineServiceTypeSTEAM, Userid: "userid"}
			ret := []*apipub.BlocklistResponse{&blocked}
			mock.rc.EXPECT().GetUserBlocklist(r.Context(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ret, "", nil)
			mock.api.GetBlocklist(w, r, p)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring(`"userid":"` + userId + `"`))
			Ω(response).Should(ContainSubstring(`"name":"name"`))
			Ω(response).Should(ContainSubstring(`"blockedid":"blockedid"`))
		})

		g.It("with empty blocklist", func() {
			p := params
			limit := apipub.Limit(2)
			p.Limit = &limit
			w, r := AddBodyToRequest("", User1JWT)
			userMeta := apipub.UserCacheMeta{
				Friends: true,
				Pending: true,
				Blocks:  false,
			}
			mock.rc.EXPECT().GetUserBlocklist(r.Context(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, "", nil)
			mock.ds.EXPECT().GetBlocklistWithLimit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, nil)
			mock.rc.EXPECT().GetUserCacheMeta(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SetUserCacheMeta(gomock.Any(), gomock.Any(), &userMeta, gomock.Any()).Return(nil)
			mock.api.GetBlocklist(w, r, p)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring("{\"items\":[],\"nextid\":null}"))
		})
	})
}

func TestDelBlocklist(t *testing.T) {
	g := goblin.Goblin(t)

	// userid for jwt1
	userId := "b287e655461f4b3085c8f244e394ff7e"

	//special hook for gomega
	RegisterFailHandler(func(m string, _ ...int) { g.Fail(m) })

	mock := NewMockAPI(t)
	defer mock.ctrl.Finish()

	userMeta := apipub.UserCacheMeta{
		Friends: true,
		Pending: true,
		Blocks:  false,
	}

	g.Describe("Clear Blocklist", func() {

		g.It("with bad token should get unauthorized", func() {
			w, r := AddBodyToRequest("", BadJWT)
			mock.api.DelBlocklist(w, r)
			g.Assert(w.Code).Equal(http.StatusUnauthorized)
		})

		g.It("with platform token should get forbidden", func() {
			w, r := AddBodyToRequest("", UserPlatformUnlinkedJWT)
			mock.api.DelBlocklist(w, r)
			g.Assert(w.Code).Equal(http.StatusForbidden)
		})

		g.It("with empty blocklist", func() {
			w, r := AddBodyToRequest("", User1JWT)
			mock.rc.EXPECT().GetUserBlocklist(r.Context(), userId, gomock.Any(), gomock.Any()).Return(nil, "", nil)
			mock.ds.EXPECT().GetBlocklistWithLimit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil, nil)
			mock.rc.EXPECT().GetUserCacheMeta(gomock.Any(), gomock.Any()).Return(nil, nil)
			mock.rc.EXPECT().SetUserCacheMeta(gomock.Any(), gomock.Any(), &userMeta, gomock.Any()).Return(nil)
			mock.api.DelBlocklist(w, r)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring("{}"))
		})

		g.It("with blocklist item to remove", func() {
			w, r := AddBodyToRequest("", User1JWT)
			blocked := apipub.BlocklistResponse{Blockedid: "blockedid", Created: time.Now().UTC(), Name: aws.String("name"), OnlineServiceType: 3, Userid: "userid"}
			ret := []*apipub.BlocklistResponse{&blocked}
			mock.rc.EXPECT().GetUserBlocklist(r.Context(), userId, gomock.Any(), gomock.Any()).Return(&ret, "", nil)
			mock.ds.EXPECT().ModifyBlocklist(gomock.Any(), userId, gomock.Any(), gomock.Any()).Return(nil)
			mock.rc.EXPECT().RemoveFromUserBlockList(gomock.Any(), gomock.Any()).Return(nil)
			mock.id.EXPECT().GetIdentityServiceFromContext(gomock.Any()).AnyTimes()
			mock.api.DelBlocklist(w, r)
			response := w.Body.String()
			Expect(w.Code).Should(Equal(http.StatusOK))
			Ω(response).Should(ContainSubstring("{}"))
		})
	})
}
