#!/bin/bash

if [[ -z "${PAT}" ]]; then
    echo "PAT is not set. Set it to the PAT with permission to the d2c-automation repo."
    exit 1
fi

if [[ -z "${VENV_DIR}" ]]; then
    echo "VENV_DIR is not set. Set it to the dir where the Python virtual env is."
    exit 1
fi

if [[ ! -f "${VENV_DIR}/bin/pip" ]]; then
    echo "VENV_DIR [${VENV_DIR}] doesn't exist or pip path is incorrect."
    exit 1
fi

./${VENV_DIR}/bin/pip install git+https://${PAT}@github.com/take-two-t2gp/d2c-automation.git#subdirectory=account-mgmt
./${VENV_DIR}/bin/pip install git+https://${PAT}@github.com/take-two-t2gp/d2c-automation.git#subdirectory=account-intv
./${VENV_DIR}/bin/pip install git+https://${PAT}@github.com/take-two-t2gp/d2c-automation.git#subdirectory=s3-lock

echo ""
echo ""
echo "Run [./${VENV_DIR}/bin/pip list] to check package versions."
echo ""
echo "Run [./${VENV_DIR}/bin/pip freeze > requirements.txt] to update requirements.txt."