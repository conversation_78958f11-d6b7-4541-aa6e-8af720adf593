IsRebar3OrMix = case application:get_env(rebar, vsn) of
    {ok, VSN} ->
      [Major|_] = string:tokens(VSN, "."),
      (list_to_integer(Major) >= 3);

    undefined ->
      %% mix is used?
      lists:keymember(mix, 1, application:loaded_applications())
  end,

GitDeps =
    [ {base64url, ".*", {git, "https://github.com/dvv/base64url", {tag, "v1.0"}}}
    , {jsx, ".*", {git, "https://github.com/talentdeficit/jsx", {tag, "2.8.0"}}}
    ],

Config = case IsRebar3OrMix of
  true -> CONFIG;
  _ -> lists:keyreplace(deps, 1, CONFIG, {deps, GitDeps})
end,

ConfigCI = case os:getenv("CI") of
    "true" ->
        Plugins = [rebar3_lint | proplists:get_value(plugins, Config, [])],
        lists:keystore(plugins, 1, Config, {plugins, Plugins});
    _ ->
        Config
end,

case os:getenv("TRAVIS") of
  "true" ->
    JobId = os:getenv("TRAVIS_JOB_ID"),
    lists:keystore(coveralls_service_job_id, 1, ConfigCI, {coveralls_service_job_id, JobId});
  _ ->
    ConfigCI
end.
