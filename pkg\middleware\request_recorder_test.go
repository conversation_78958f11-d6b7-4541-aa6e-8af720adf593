package middleware

//
//import (
//	"bytes"
//	"context"
//	"io"
//	"net/http"
//	"net/http/httptest"
//	"testing"
//
//	"github.com/aws/aws-sdk-go-v2/aws"
//	"github.com/aws/aws-sdk-go-v2/service/kinesis"
//	"github.com/franela/goblin"
//	chimiddleware "github.com/go-chi/chi/v5/middleware"
//
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
//	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
//)
//
//func TestRequestEntry(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	g.Describe("RequestEntry", func() {
//		g.It("should parse from request correctly", func() {
//			mock := mockRequest(t, "/foo/bar")
//			mock.r.Proto = "HTTP/1.0"
//			mock.r.Header.Add("x-test", "value")
//			body := []byte("{\"data\":true}")
//			reqEntry := &RequestEntry{}
//			reqEntry.FromReq(mock.r, &body)
//			g.Assert(reqEntry.Method).Equal("GET")
//			g.Assert(reqEntry.URL).Equal("http://example.com/foo/bar")
//			g.Assert(reqEntry.Header.Get("x-test")).Equal("value")
//			g.Assert(utils.EncodeJson(reqEntry.Body)).Equal("{\"data\":true}")
//		})
//	})
//}
//
//func TestResponseEntry(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	g.Describe("ResponseEntry", func() {
//		g.It("should parse from response correctly", func() {
//			body := bytes.NewBuffer([]byte("{\"respBody\":true}"))
//			w := &httptest.ResponseRecorder{
//				Code:      0,
//				HeaderMap: http.Header{},
//			}
//			ww := chimiddleware.NewWrapResponseWriter(w, 1)
//			ww.WriteHeader(404)
//			ww.Header().Add("x-test", "value")
//			respEntry := &ResponseEntry{}
//			respEntry.FromResp(ww, body)
//
//			g.Assert(respEntry.StatusCode).Equal(404)
//			g.Assert(respEntry.Header.Get("x-test")).Equal("value")
//			g.Assert(utils.EncodeJson(respEntry.Body)).Equal("{\"respBody\":true}")
//		})
//	})
//}
//
//type nextHandler struct{}
//
//func (h *nextHandler) ServeHTTP(w http.ResponseWriter, r *http.Request) {
//	utils.WriteJsonResponse(w, r, http.StatusOK, map[string]string{"foo": "bar"})
//}
//
//type mockKinesis struct {
//	puts []*kinesis.PutRecordInput
//}
//
//func (k *mockKinesis) PutRecord(ctx context.Context, params *kinesis.PutRecordInput, optFns ...func(*kinesis.Options)) (*kinesis.PutRecordOutput, error) {
//	k.puts = append(k.puts, params)
//	return &kinesis.PutRecordOutput{
//		SequenceNumber: aws.String("1234"),
//		ShardId:        aws.String("shardId-000000000001"),
//	}, nil
//}
//
//func TestRequestRecorder(t *testing.T) {
//	g := goblin.Goblin(t)
//
//	g.Describe("RequestRecorder", func() {
//		g.It("should create handler correctly", func() {
//			cfg := config.Config{
//				RequestRecorderStream:  "t2gp-social-api-traffic-test",
//				RequestRecorderEnabled: true,
//			}
//			k := &mockKinesis{}
//			fn := RequestRecorder(context.Background(), &cfg, k)
//			g.Assert(fn).IsNotNil()
//
//			handler := fn(&nextHandler{})
//
//			mock := mockRequest(t, "/foo/bar")
//			mock.r.Body = io.NopCloser(bytes.NewBuffer([]byte("")))
//			w := &httptest.ResponseRecorder{
//				Code:      0,
//				HeaderMap: http.Header{},
//			}
//			handler.ServeHTTP(w, mock.r)
//			g.Assert(len(k.puts)).Equal(1)
//		})
//	})
//}
