// Package middleware chi HTTP server middleware
package middleware

import (
	"fmt"
	"net/http"
	"runtime/debug"
	"strconv"

	chi "github.com/go-chi/chi/v5/middleware"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/ext"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// Recoverer is a middleware that recovers from panics, logs the panic (and a
// backtrace), and returns a HTTP 500 (Internal Server Error) status if
// possible. Recoverer prints a request ID if one is provided.
//
// Alternatively, look at https://github.com/pressly/lg middleware pkgs.
func Recoverer(next http.Handler) http.Handler {
	fn := func(w http.ResponseWriter, r *http.Request) {
		defer func() {
			if rvr := recover(); rvr != nil && rvr != http.ErrAbortHandler {
				// ask datadog to stop the tracer
				stack := debug.Stack()
				reqID := chi.GetReqID(r.Context())
				pluginVersion := r.Header.Get("t2gp-plugin-version")
				engineVersion := r.Header.Get("engine-version")
				engineIdentifier := r.Header.Get("engine-identifier")
				platformIdentifier := r.Header.Get("platform-identifier")
				span, _ := tracer.SpanFromContext(r.Context())
				span.SetTag(ext.ResourceName, fmt.Sprintf("%s %s", r.Method, r.URL.Path))
				span.SetTag("request_id", reqID)
				span.SetTag("remote_ip", r.RemoteAddr)
				span.SetTag(ext.Error, "true")
				span.SetTag(ext.ErrorStack, string(stack))
				span.SetTag(ext.HTTPCode, strconv.Itoa(http.StatusInternalServerError))
				span.SetTag("plugin_version", pluginVersion)
				span.SetTag("engine_version", engineVersion)
				span.SetTag("engine_identifier", engineIdentifier)
				span.SetTag("platform_identifier", platformIdentifier)
				defer span.Finish()

				scheme := "http"
				if r.TLS != nil {
					scheme = "https"
				}

				log := logger.Get(r)
				log.Error().Fields(map[string]interface{}{
					"scheme":              scheme,
					"proto":               r.Proto,
					"method":              r.Method,
					"remote_ip":           r.RemoteAddr,
					"user_agent":          r.UserAgent(),
					"uri":                 fmt.Sprintf("%s://%s%s", scheme, r.Host, r.RequestURI),
					"dd.trace_id":         span.Context().TraceID(),
					"dd.span_id":          span.Context().SpanID(),
					"request_id":          reqID,
					"status":              http.StatusInternalServerError,
					"plugin_version":      pluginVersion,
					"engine_version":      engineVersion,
					"engine_identifier":   engineIdentifier,
					"platform_identifier": platformIdentifier,
					"stack":               string(stack),
					"panic":               fmt.Sprintf("%+v", rvr),
				}).Msg("!!! Panic !!!")

				if rvrErr, ok := rvr.(error); ok {
					e := errs.NewMessage(http.StatusInternalServerError, rvrErr.Error(), errs.EPanic)
					span.SetTag(ext.ErrorType, "panic")
					span.SetTag(ext.ErrorMsg, rvrErr.Error())
					utils.WriteJsonResponse(w, r, http.StatusInternalServerError, e)
				} else {
					e := errs.Panic()
					span.SetTag(ext.ErrorType, "panic")
					utils.WriteJsonResponse(w, r, http.StatusInternalServerError, e)
				}
			}
		}()

		next.ServeHTTP(w, r)
	}

	return http.HandlerFunc(fn)
}
