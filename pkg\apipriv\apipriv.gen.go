// Package apipriv provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.3.0 DO NOT EDIT.
package apipriv

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/oapi-codegen/runtime"
)

// Defines values for GroupMemberRole.
const (
	Leader    GroupMemberRole = "leader"
	Member    GroupMemberRole = "member"
	Nonmember GroupMemberRole = "nonmember"
)

// Defines values for JoinRequestAction.
const (
	Authenticate JoinRequestAction = "authenticate"
	AutoApprove  JoinRequestAction = "auto-approve"
	AutoReject   JoinRequestAction = "auto-reject"
	Manual       JoinRequestAction = "manual"
)

// Defines values for MembershipStatus.
const (
	Approved      MembershipStatus = "approved"
	Authenticated MembershipStatus = "authenticated"
	Declined      MembershipStatus = "declined"
	Invited       MembershipStatus = "invited"
	Joined        MembershipStatus = "joined"
	Rejected      MembershipStatus = "rejected"
	Requested     MembershipStatus = "requested"
	Revoked       MembershipStatus = "revoked"
)

// Defines values for OnlineServiceType.
const (
	APPLE                    OnlineServiceType = 22
	CALICO                   OnlineServiceType = 10
	DEVICE                   OnlineServiceType = 21
	EPIC                     OnlineServiceType = 15
	FACEBOOK                 OnlineServiceType = 17
	GAMECENTER               OnlineServiceType = 12
	GOOGLE                   OnlineServiceType = 18
	GOOGLEPLAY               OnlineServiceType = 6
	LEGACYGAMECENTER         OnlineServiceType = 5
	NINTENDO                 OnlineServiceType = 11
	SONYENTERTAINMENTNETWORK OnlineServiceType = 2
	STADIA                   OnlineServiceType = 16
	STEAM                    OnlineServiceType = 3
	T2GP                     OnlineServiceType = 24
	TWITCH                   OnlineServiceType = 20
	TWITTER                  OnlineServiceType = 19
	UNKNOWN                  OnlineServiceType = 0
	VORTEX                   OnlineServiceType = 14
	WEB                      OnlineServiceType = 4
	WEGAME                   OnlineServiceType = 13
	WINDOWSDEVELOPER         OnlineServiceType = 99
	WINDOWSPHONE             OnlineServiceType = 9
	XBOXLIVE                 OnlineServiceType = 1
	ZENDESK                  OnlineServiceType = 23
)

// Defines values for PresenceStatus.
const (
	Authenticating PresenceStatus = "authenticating"
	Away           PresenceStatus = "away"
	Chat           PresenceStatus = "chat"
	Custom         PresenceStatus = "custom"
	Dnd            PresenceStatus = "dnd"
	Offline        PresenceStatus = "offline"
	Online         PresenceStatus = "online"
	Playing        PresenceStatus = "playing"
)

// UpdateUserPresenceStatusRequest defines model for UpdateUserPresenceStatusRequest.
type UpdateUserPresenceStatusRequest struct {
	// Ttl How long in seconds before this presence will be considered offline if no presence Heartbeat is made.
	Ttl         *int         `json:"Ttl,omitempty"`
	ActiveGroup *ActiveGroup `json:"activeGroup,omitempty"`

	// GameData structure for games to send additional presence information for internal use.
	GameData *string `json:"gameData,omitempty"`

	// GameName Possibly pull this from somewhere for localization in the future.
	GameName string `json:"gameName"`

	// Meta Used to send additional information.  Maximum size of 1024 bytes.  Will be used in particular for rich presence interpolating in the future.
	Meta *map[string]interface{} `json:"meta,omitempty"`

	// Priority Internal use.  Do not send. 10000 = user set(forced setting).  20000-29999 set by games ordered presence activity. 30000 = launcher automated (idle,ingame,etc). offline will remove from list.
	Priority int `json:"priority"`

	// Productid pulled from JWT
	Productid string `json:"productid"`

	// RichPresence string to be displayed for rich presence.  eventually will support templating for localization.
	RichPresence *string        `json:"richPresence,omitempty"`
	Status       PresenceStatus `json:"status"`
	Timestamp    DateTime       `json:"timestamp"`
	Userid       string         `json:"userid"`
}

// ActiveGroup defines model for activeGroup.
type ActiveGroup struct {
	CanCrossPlay       bool   `json:"canCrossPlay"`
	CanRequestJoin     bool   `json:"canRequestJoin"`
	CurrentMemberCount int    `json:"currentMemberCount"`
	Groupid            string `json:"groupid"`
	MaxMembers         int    `json:"maxMembers"`
}

// Config defines model for config.
type Config = map[string]interface{}

// ControlMessage defines model for controlMessage.
type ControlMessage struct {
	Payload   *string   `json:"payload,omitempty"`
	Timestamp *DateTime `json:"timestamp,omitempty"`
}

// DateTime defines model for dateTime.
type DateTime = time.Time

// Error defines model for error.
type Error struct {
	// Code A code representing the error that occurred
	Code uint32 `json:"code"`

	// Message A string describing the error that occurred
	Message string `json:"message"`
}

// FirstPartyRefreshResponse defines model for firstPartyRefreshResponse.
type FirstPartyRefreshResponse struct {
	FirstPartyid *string `json:"firstPartyid,omitempty"`
	RefreshToken *string `json:"refreshToken,omitempty"`
}

// FirstPartyTokenRequest defines model for firstPartyTokenRequest.
type FirstPartyTokenRequest struct {
	FirstPartyid *string `json:"firstPartyid,omitempty"`
	RefreshToken *string `json:"refreshToken,omitempty"`

	// RefreshTokenTtl ttl of optional refresh token in seconds
	RefreshTokenTtl *int   `json:"refreshTokenTtl,omitempty"`
	Token           string `json:"token"`

	// Ttl ttl of token in seconds
	Ttl int `json:"ttl"`
}

// FirstPartyTokenResponse defines model for firstPartyTokenResponse.
type FirstPartyTokenResponse struct {
	FirstPartyid *string `json:"firstPartyid,omitempty"`
	Token        *string `json:"token,omitempty"`
}

// Group ProductID only set as required due to codegen issues.
type Group struct {
	ControlMessages    *[]ControlMessage    `json:"controlMessages,omitempty"`
	Created            *DateTime            `json:"created,omitempty"`
	Groupid            string               `json:"groupid"`
	JoinRequestAction  JoinRequestAction    `json:"joinRequestAction"`
	MaxMembers         int                  `json:"maxMembers"`
	Members            *[]GroupMember       `json:"members,omitempty"`
	MembershipRequests *[]MembershipRequest `json:"membershipRequests,omitempty"`

	// Meta Maximum size of 1024 bytes.
	Meta *map[string]interface{} `json:"meta,omitempty"`

	// Password NOT required.  needed to put required for codegen issues.
	Password  *string `json:"password,omitempty"`
	Productid *string `json:"productid,omitempty"`
	Type      *string `json:"type,omitempty"`
}

// GroupCleanupResponse defines model for groupCleanupResponse.
type GroupCleanupResponse struct {
	// Cleanuped a list of group id have been removed
	Cleanuped *[]string `json:"cleanuped,omitempty"`

	// Count the number of group deleted
	Count *int `json:"count,omitempty"`

	// Errors list of errors that might have occurred
	Errors *[]string `json:"errors,omitempty"`

	// ExecutionTime the number of secords clean up job running
	ExecutionTime *int `json:"executionTime,omitempty"`
}

// GroupMember ProductID only set as required due to codegen issues.
type GroupMember struct {
	Groupid   string          `json:"groupid"`
	Productid string          `json:"productid"`
	Role      GroupMemberRole `json:"role"`
	Userid    string          `json:"userid"`
}

// GroupMemberRole defines model for groupMemberRole.
type GroupMemberRole string

// JoinRequestAction defines model for joinRequestAction.
type JoinRequestAction string

// ListResponse defines model for listResponse.
type ListResponse struct {
	Items []ListResponse_Items_Item `json:"items"`
	Next  *string                   `json:"next,omitempty"`
}

// ListResponse_Items_Item defines model for listResponse.items.Item.
type ListResponse_Items_Item struct {
	union json.RawMessage
}

// MembershipRequest defines model for membershipRequest.
type MembershipRequest struct {
	// Approverid for join group request when need approval
	Approverid *string   `json:"approverid,omitempty"`
	Expires    *DateTime `json:"expires,omitempty"`

	// Groupid the group id
	Groupid string `json:"groupid"`

	// Memberid userid or first party id
	Memberid string `json:"memberid"`

	// OnlineServiceType The online service type of the specified Platform Account.
	OnlineServiceType *OnlineServiceType `dynamodbav:"onlineServiceType" json:"onlineServiceType,omitempty"`

	// Password password for join the group.
	Password *string `json:"password,omitempty"`

	// Productid in reponse body, it extracts from JWT token
	Productid *string          `json:"productid,omitempty"`
	Status    MembershipStatus `json:"status"`
}

// MembershipStatus defines model for membershipStatus.
type MembershipStatus string

// OnlineServiceType The online service type of the specified Platform Account.
type OnlineServiceType int

// PresenceStatus defines model for presenceStatus.
type PresenceStatus string

// Subscription defines model for subscription.
type Subscription struct {
	ClientId *string `json:"clientId,omitempty"`
	Topic    *string `json:"topic,omitempty"`
}

// TrustedLoginCreateRequest defines model for trustedLoginCreateRequest.
type TrustedLoginCreateRequest struct {
	// Password Password. If not set, it will be randomly generated.
	Password *string `json:"password,omitempty"`

	// ProductId Product id
	ProductId string `json:"productId"`

	// TenantId Tenant id
	TenantId string `json:"tenantId"`
}

// TrustedLoginResponse defines model for trustedLoginResponse.
type TrustedLoginResponse struct {
	// ClientId Client id
	ClientId string `json:"clientId"`

	// Password Password
	Password string `json:"password"`
}

// TrustedLoginUpdateRequest defines model for trustedLoginUpdateRequest.
type TrustedLoginUpdateRequest struct {
	// Password Password. If not set, it will be randomly generated.
	Password *string `json:"password,omitempty"`
}

// UserPresenceRequest defines model for userPresenceRequest.
type UserPresenceRequest struct {
	// RichPresence string to be displayed for rich presence.  T2GP will eventually support interpolating and localization.
	RichPresence *string        `json:"richPresence,omitempty"`
	Status       PresenceStatus `json:"status"`
	Userid       string         `json:"userid"`
}

// ClearPresence defines model for clearPresence.
type ClearPresence = bool

// Clientid defines model for clientid.
type Clientid = string

// Errorid defines model for errorid.
type Errorid = string

// FirstPartyid defines model for firstPartyid.
type FirstPartyid = string

// Id1 defines model for id1.
type Id1 = string

// Id2 defines model for id2.
type Id2 = string

// Platform The online service type of the specified Platform Account.
type Platform = OnlineServiceType

// Priority defines model for priority.
type Priority = int

// SearchPlatform The online service type of the specified Platform Account.
type SearchPlatform = OnlineServiceType

// SearchQuery defines model for searchQuery.
type SearchQuery = string

// SearchType defines model for searchType.
type SearchType = string

// Ulid defines model for ulid.
type Ulid = string

// Userid defines model for userid.
type Userid = string

// XT2gpLabel defines model for xT2gpLabel.
type XT2gpLabel = string

// N400 defines model for 400.
type N400 = Error

// N401 defines model for 401.
type N401 = Error

// N403 defines model for 403.
type N403 = Error

// N404 defines model for 404.
type N404 = Error

// N406 defines model for 406.
type N406 = Error

// N500 defines model for 500.
type N500 = Error

// UpdateConfigJSONBody defines parameters for UpdateConfig.
type UpdateConfigJSONBody = map[string]interface{}

// MakeFriendParams defines parameters for MakeFriend.
type MakeFriendParams struct {
	// Id1 Userid1
	Id1 Id1 `form:"id1" json:"id1"`

	// Id2 Userid2
	Id2 Id2 `form:"id2" json:"id2"`
}

// MakeUnfriendParams defines parameters for MakeUnfriend.
type MakeUnfriendParams struct {
	// Id1 Userid1
	Id1 Id1 `form:"id1" json:"id1"`

	// Id2 Userid2
	Id2 Id2 `form:"id2" json:"id2"`
}

// UserSearchDNAParams defines parameters for UserSearchDNA.
type UserSearchDNAParams struct {
	// Q Search query
	Q SearchQuery `form:"q" json:"q"`

	// SearchPlatform Search platform
	SearchPlatform *SearchPlatform `form:"searchPlatform,omitempty" json:"searchPlatform,omitempty"`

	// Type Search type
	Type *SearchType `form:"type,omitempty" json:"type,omitempty"`
}

// UpdateUserPresenceParams defines parameters for UpdateUserPresence.
type UpdateUserPresenceParams struct {
	// ClearPresence Flag to clear user defined presence record. if set to true, will clear the presence ignoring presence in body except userid.
	ClearPresence *ClearPresence `form:"clearPresence,omitempty" json:"clearPresence,omitempty"`
}

// SetUserDefinedPresenceParams defines parameters for SetUserDefinedPresence.
type SetUserDefinedPresenceParams struct {
	// ClearPresence Flag to clear user defined presence record. if set to true, will clear the presence ignoring presence in body except userid.
	ClearPresence *ClearPresence `form:"clearPresence,omitempty" json:"clearPresence,omitempty"`
}

// UpdateConfigJSONRequestBody defines body for UpdateConfig for application/json ContentType.
type UpdateConfigJSONRequestBody = UpdateConfigJSONBody

// SetFirstPartyTokenJSONRequestBody defines body for SetFirstPartyToken for application/json ContentType.
type SetFirstPartyTokenJSONRequestBody = FirstPartyTokenRequest

// TrustedCreateJSONRequestBody defines body for TrustedCreate for application/json ContentType.
type TrustedCreateJSONRequestBody = TrustedLoginCreateRequest

// TrustedUpdateJSONRequestBody defines body for TrustedUpdate for application/json ContentType.
type TrustedUpdateJSONRequestBody = TrustedLoginUpdateRequest

// UpdateUserPresenceJSONRequestBody defines body for UpdateUserPresence for application/json ContentType.
type UpdateUserPresenceJSONRequestBody = UpdateUserPresenceStatusRequest

// SetUserDefinedPresenceJSONRequestBody defines body for SetUserDefinedPresence for application/json ContentType.
type SetUserDefinedPresenceJSONRequestBody = UserPresenceRequest

// AsGroup returns the union data inside the ListResponse_Items_Item as a Group
func (t ListResponse_Items_Item) AsGroup() (Group, error) {
	var body Group
	err := json.Unmarshal(t.union, &body)
	return body, err
}

// FromGroup overwrites any union data inside the ListResponse_Items_Item as the provided Group
func (t *ListResponse_Items_Item) FromGroup(v Group) error {
	b, err := json.Marshal(v)
	t.union = b
	return err
}

// MergeGroup performs a merge with any union data inside the ListResponse_Items_Item, using the provided Group
func (t *ListResponse_Items_Item) MergeGroup(v Group) error {
	b, err := json.Marshal(v)
	if err != nil {
		return err
	}

	merged, err := runtime.JSONMerge(t.union, b)
	t.union = merged
	return err
}

func (t ListResponse_Items_Item) MarshalJSON() ([]byte, error) {
	b, err := t.union.MarshalJSON()
	return b, err
}

func (t *ListResponse_Items_Item) UnmarshalJSON(b []byte) error {
	err := t.union.UnmarshalJSON(b)
	return err
}

// ServerInterface represents all server handlers.
type ServerInterface interface {

	// (GET /dev/config)
	GetConfig(w http.ResponseWriter, r *http.Request)

	// (PATCH /dev/config)
	UpdateConfig(w http.ResponseWriter, r *http.Request)

	// (GET /dev/dynamodb/{userid})
	GetDynamoDBUserData(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/error/{errorid})
	GetDevError(w http.ResponseWriter, r *http.Request, errorid Errorid)
	// Returns the details of the server's health
	// (GET /dev/health.json)
	GetHealthDetails(w http.ResponseWriter, r *http.Request)

	// (GET /dev/make-friend)
	MakeFriend(w http.ResponseWriter, r *http.Request, params MakeFriendParams)

	// (GET /dev/make-unfriend)
	MakeUnfriend(w http.ResponseWriter, r *http.Request, params MakeUnfriendParams)
	// Get all stats
	// (GET /dev/stats)
	GetAllStats(w http.ResponseWriter, r *http.Request)

	// (DELETE /dev/subscriptions/{userid})
	DeleteVmqSubscriptions(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/subscriptions/{userid})
	GetVmqSubscriptions(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/sync/firstParty/{firstPartyid}/platform/{platform}/refresh)
	GetFirstPartyRefreshToken(w http.ResponseWriter, r *http.Request, firstPartyid FirstPartyid, platform Platform)

	// (GET /dev/sync/firstParty/{firstPartyid}/platform/{platform}/token)
	GetFirstPartyToken(w http.ResponseWriter, r *http.Request, firstPartyid FirstPartyid, platform Platform)

	// (POST /dev/sync/firstParty/{firstPartyid}/platform/{platform}/token)
	SetFirstPartyToken(w http.ResponseWriter, r *http.Request, firstPartyid FirstPartyid, platform Platform)
	// Create a Social Trusted API login for the product
	// (POST /dev/trustedlogin)
	TrustedCreate(w http.ResponseWriter, r *http.Request)
	// Delete the Social Trusted API login
	// (DELETE /dev/trustedlogin/{clientid})
	TrustedDel(w http.ResponseWriter, r *http.Request, clientid Clientid)
	// Update the password for the Social Trusted API login
	// (PATCH /dev/trustedlogin/{clientid})
	TrustedUpdate(w http.ResponseWriter, r *http.Request, clientid Clientid)

	// (GET /dev/ulids/{ulid})
	DecodeUlid(w http.ResponseWriter, r *http.Request, ulid Ulid)

	// (GET /dev/user/dna/search)
	UserSearchDNA(w http.ResponseWriter, r *http.Request, params UserSearchDNAParams)

	// (GET /dev/user/dna/{userid}/links)
	GetUserLinksDNA(w http.ResponseWriter, r *http.Request, userid Userid)

	// (PATCH /dev/user/presence)
	UpdateUserPresence(w http.ResponseWriter, r *http.Request, params UpdateUserPresenceParams)

	// (POST /dev/user/presence/self)
	SetUserDefinedPresence(w http.ResponseWriter, r *http.Request, params SetUserDefinedPresenceParams)

	// (GET /dev/user/{userid}/friends)
	GetFriendsForUser(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/user/{userid}/groups)
	GetAllGroupsForUser(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/user/{userid}/presence)
	GetPresenceForUser(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/user/{userid}/sync)
	SyncUserProfile(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/user/{userid}/voip)
	GetVoipRoomsForUser(w http.ResponseWriter, r *http.Request, userid Userid)

	// (GET /dev/vmq/sessions)
	GetVmqSessions(w http.ResponseWriter, r *http.Request)

	// (GET /dev/vmq/status)
	GetVmqStatus(w http.ResponseWriter, r *http.Request)

	// (GET /dev/vmq/version)
	GetVmqVersion(w http.ResponseWriter, r *http.Request)
}

// Unimplemented server implementation that returns http.StatusNotImplemented for each endpoint.

type Unimplemented struct{}

// (GET /dev/config)
func (_ Unimplemented) GetConfig(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (PATCH /dev/config)
func (_ Unimplemented) UpdateConfig(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/dynamodb/{userid})
func (_ Unimplemented) GetDynamoDBUserData(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/error/{errorid})
func (_ Unimplemented) GetDevError(w http.ResponseWriter, r *http.Request, errorid Errorid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Returns the details of the server's health
// (GET /dev/health.json)
func (_ Unimplemented) GetHealthDetails(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/make-friend)
func (_ Unimplemented) MakeFriend(w http.ResponseWriter, r *http.Request, params MakeFriendParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/make-unfriend)
func (_ Unimplemented) MakeUnfriend(w http.ResponseWriter, r *http.Request, params MakeUnfriendParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Get all stats
// (GET /dev/stats)
func (_ Unimplemented) GetAllStats(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (DELETE /dev/subscriptions/{userid})
func (_ Unimplemented) DeleteVmqSubscriptions(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/subscriptions/{userid})
func (_ Unimplemented) GetVmqSubscriptions(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/sync/firstParty/{firstPartyid}/platform/{platform}/refresh)
func (_ Unimplemented) GetFirstPartyRefreshToken(w http.ResponseWriter, r *http.Request, firstPartyid FirstPartyid, platform Platform) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/sync/firstParty/{firstPartyid}/platform/{platform}/token)
func (_ Unimplemented) GetFirstPartyToken(w http.ResponseWriter, r *http.Request, firstPartyid FirstPartyid, platform Platform) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (POST /dev/sync/firstParty/{firstPartyid}/platform/{platform}/token)
func (_ Unimplemented) SetFirstPartyToken(w http.ResponseWriter, r *http.Request, firstPartyid FirstPartyid, platform Platform) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Create a Social Trusted API login for the product
// (POST /dev/trustedlogin)
func (_ Unimplemented) TrustedCreate(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Delete the Social Trusted API login
// (DELETE /dev/trustedlogin/{clientid})
func (_ Unimplemented) TrustedDel(w http.ResponseWriter, r *http.Request, clientid Clientid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// Update the password for the Social Trusted API login
// (PATCH /dev/trustedlogin/{clientid})
func (_ Unimplemented) TrustedUpdate(w http.ResponseWriter, r *http.Request, clientid Clientid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/ulids/{ulid})
func (_ Unimplemented) DecodeUlid(w http.ResponseWriter, r *http.Request, ulid Ulid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/dna/search)
func (_ Unimplemented) UserSearchDNA(w http.ResponseWriter, r *http.Request, params UserSearchDNAParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/dna/{userid}/links)
func (_ Unimplemented) GetUserLinksDNA(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (PATCH /dev/user/presence)
func (_ Unimplemented) UpdateUserPresence(w http.ResponseWriter, r *http.Request, params UpdateUserPresenceParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (POST /dev/user/presence/self)
func (_ Unimplemented) SetUserDefinedPresence(w http.ResponseWriter, r *http.Request, params SetUserDefinedPresenceParams) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/{userid}/friends)
func (_ Unimplemented) GetFriendsForUser(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/{userid}/groups)
func (_ Unimplemented) GetAllGroupsForUser(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/{userid}/presence)
func (_ Unimplemented) GetPresenceForUser(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/{userid}/sync)
func (_ Unimplemented) SyncUserProfile(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/user/{userid}/voip)
func (_ Unimplemented) GetVoipRoomsForUser(w http.ResponseWriter, r *http.Request, userid Userid) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/vmq/sessions)
func (_ Unimplemented) GetVmqSessions(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/vmq/status)
func (_ Unimplemented) GetVmqStatus(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// (GET /dev/vmq/version)
func (_ Unimplemented) GetVmqVersion(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusNotImplemented)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandlerFunc   func(w http.ResponseWriter, r *http.Request, err error)
}

type MiddlewareFunc func(http.Handler) http.Handler

// GetConfig operation middleware
func (siw *ServerInterfaceWrapper) GetConfig(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetConfig(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateConfig operation middleware
func (siw *ServerInterfaceWrapper) UpdateConfig(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateConfig(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetDynamoDBUserData operation middleware
func (siw *ServerInterfaceWrapper) GetDynamoDBUserData(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDynamoDBUserData(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetDevError operation middleware
func (siw *ServerInterfaceWrapper) GetDevError(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "errorid" -------------
	var errorid Errorid

	err = runtime.BindStyledParameterWithOptions("simple", "errorid", chi.URLParam(r, "errorid"), &errorid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "errorid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetDevError(w, r, errorid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetHealthDetails operation middleware
func (siw *ServerInterfaceWrapper) GetHealthDetails(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetHealthDetails(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// MakeFriend operation middleware
func (siw *ServerInterfaceWrapper) MakeFriend(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params MakeFriendParams

	// ------------- Required query parameter "id1" -------------

	if paramValue := r.URL.Query().Get("id1"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id1"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id1", r.URL.Query(), &params.Id1)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id1", Err: err})
		return
	}

	// ------------- Required query parameter "id2" -------------

	if paramValue := r.URL.Query().Get("id2"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id2"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id2", r.URL.Query(), &params.Id2)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id2", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.MakeFriend(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// MakeUnfriend operation middleware
func (siw *ServerInterfaceWrapper) MakeUnfriend(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params MakeUnfriendParams

	// ------------- Required query parameter "id1" -------------

	if paramValue := r.URL.Query().Get("id1"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id1"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id1", r.URL.Query(), &params.Id1)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id1", Err: err})
		return
	}

	// ------------- Required query parameter "id2" -------------

	if paramValue := r.URL.Query().Get("id2"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "id2"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "id2", r.URL.Query(), &params.Id2)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "id2", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.MakeUnfriend(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetAllStats operation middleware
func (siw *ServerInterfaceWrapper) GetAllStats(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetAllStats(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DeleteVmqSubscriptions operation middleware
func (siw *ServerInterfaceWrapper) DeleteVmqSubscriptions(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DeleteVmqSubscriptions(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetVmqSubscriptions operation middleware
func (siw *ServerInterfaceWrapper) GetVmqSubscriptions(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetVmqSubscriptions(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetFirstPartyRefreshToken operation middleware
func (siw *ServerInterfaceWrapper) GetFirstPartyRefreshToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "firstPartyid" -------------
	var firstPartyid FirstPartyid

	err = runtime.BindStyledParameterWithOptions("simple", "firstPartyid", chi.URLParam(r, "firstPartyid"), &firstPartyid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "firstPartyid", Err: err})
		return
	}

	// ------------- Path parameter "platform" -------------
	var platform Platform

	err = runtime.BindStyledParameterWithOptions("simple", "platform", chi.URLParam(r, "platform"), &platform, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "platform", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetFirstPartyRefreshToken(w, r, firstPartyid, platform)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetFirstPartyToken operation middleware
func (siw *ServerInterfaceWrapper) GetFirstPartyToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "firstPartyid" -------------
	var firstPartyid FirstPartyid

	err = runtime.BindStyledParameterWithOptions("simple", "firstPartyid", chi.URLParam(r, "firstPartyid"), &firstPartyid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "firstPartyid", Err: err})
		return
	}

	// ------------- Path parameter "platform" -------------
	var platform Platform

	err = runtime.BindStyledParameterWithOptions("simple", "platform", chi.URLParam(r, "platform"), &platform, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "platform", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetFirstPartyToken(w, r, firstPartyid, platform)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SetFirstPartyToken operation middleware
func (siw *ServerInterfaceWrapper) SetFirstPartyToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "firstPartyid" -------------
	var firstPartyid FirstPartyid

	err = runtime.BindStyledParameterWithOptions("simple", "firstPartyid", chi.URLParam(r, "firstPartyid"), &firstPartyid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "firstPartyid", Err: err})
		return
	}

	// ------------- Path parameter "platform" -------------
	var platform Platform

	err = runtime.BindStyledParameterWithOptions("simple", "platform", chi.URLParam(r, "platform"), &platform, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "platform", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SetFirstPartyToken(w, r, firstPartyid, platform)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// TrustedCreate operation middleware
func (siw *ServerInterfaceWrapper) TrustedCreate(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.TrustedCreate(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// TrustedDel operation middleware
func (siw *ServerInterfaceWrapper) TrustedDel(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "clientid" -------------
	var clientid Clientid

	err = runtime.BindStyledParameterWithOptions("simple", "clientid", chi.URLParam(r, "clientid"), &clientid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "clientid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.TrustedDel(w, r, clientid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// TrustedUpdate operation middleware
func (siw *ServerInterfaceWrapper) TrustedUpdate(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "clientid" -------------
	var clientid Clientid

	err = runtime.BindStyledParameterWithOptions("simple", "clientid", chi.URLParam(r, "clientid"), &clientid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "clientid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.TrustedUpdate(w, r, clientid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// DecodeUlid operation middleware
func (siw *ServerInterfaceWrapper) DecodeUlid(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "ulid" -------------
	var ulid Ulid

	err = runtime.BindStyledParameterWithOptions("simple", "ulid", chi.URLParam(r, "ulid"), &ulid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "ulid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.DecodeUlid(w, r, ulid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UserSearchDNA operation middleware
func (siw *ServerInterfaceWrapper) UserSearchDNA(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params UserSearchDNAParams

	// ------------- Required query parameter "q" -------------

	if paramValue := r.URL.Query().Get("q"); paramValue != "" {

	} else {
		siw.ErrorHandlerFunc(w, r, &RequiredParamError{ParamName: "q"})
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "q", r.URL.Query(), &params.Q)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "q", Err: err})
		return
	}

	// ------------- Optional query parameter "searchPlatform" -------------

	err = runtime.BindQueryParameter("form", true, false, "searchPlatform", r.URL.Query(), &params.SearchPlatform)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "searchPlatform", Err: err})
		return
	}

	// ------------- Optional query parameter "type" -------------

	err = runtime.BindQueryParameter("form", true, false, "type", r.URL.Query(), &params.Type)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "type", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UserSearchDNA(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetUserLinksDNA operation middleware
func (siw *ServerInterfaceWrapper) GetUserLinksDNA(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetUserLinksDNA(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// UpdateUserPresence operation middleware
func (siw *ServerInterfaceWrapper) UpdateUserPresence(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params UpdateUserPresenceParams

	// ------------- Optional query parameter "clearPresence" -------------

	err = runtime.BindQueryParameter("form", true, false, "clearPresence", r.URL.Query(), &params.ClearPresence)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "clearPresence", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.UpdateUserPresence(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SetUserDefinedPresence operation middleware
func (siw *ServerInterfaceWrapper) SetUserDefinedPresence(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params SetUserDefinedPresenceParams

	// ------------- Optional query parameter "clearPresence" -------------

	err = runtime.BindQueryParameter("form", true, false, "clearPresence", r.URL.Query(), &params.ClearPresence)
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "clearPresence", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SetUserDefinedPresence(w, r, params)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetFriendsForUser operation middleware
func (siw *ServerInterfaceWrapper) GetFriendsForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetFriendsForUser(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetAllGroupsForUser operation middleware
func (siw *ServerInterfaceWrapper) GetAllGroupsForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetAllGroupsForUser(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetPresenceForUser operation middleware
func (siw *ServerInterfaceWrapper) GetPresenceForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetPresenceForUser(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// SyncUserProfile operation middleware
func (siw *ServerInterfaceWrapper) SyncUserProfile(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.SyncUserProfile(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetVoipRoomsForUser operation middleware
func (siw *ServerInterfaceWrapper) GetVoipRoomsForUser(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	var err error

	// ------------- Path parameter "userid" -------------
	var userid Userid

	err = runtime.BindStyledParameterWithOptions("simple", "userid", chi.URLParam(r, "userid"), &userid, runtime.BindStyledParameterOptions{ParamLocation: runtime.ParamLocationPath, Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandlerFunc(w, r, &InvalidParamFormatError{ParamName: "userid", Err: err})
		return
	}

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetVoipRoomsForUser(w, r, userid)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetVmqSessions operation middleware
func (siw *ServerInterfaceWrapper) GetVmqSessions(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetVmqSessions(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetVmqStatus operation middleware
func (siw *ServerInterfaceWrapper) GetVmqStatus(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetVmqStatus(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

// GetVmqVersion operation middleware
func (siw *ServerInterfaceWrapper) GetVmqVersion(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	handler := http.Handler(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		siw.Handler.GetVmqVersion(w, r)
	}))

	for _, middleware := range siw.HandlerMiddlewares {
		handler = middleware(handler)
	}

	handler.ServeHTTP(w, r.WithContext(ctx))
}

type UnescapedCookieParamError struct {
	ParamName string
	Err       error
}

func (e *UnescapedCookieParamError) Error() string {
	return fmt.Sprintf("error unescaping cookie parameter '%s'", e.ParamName)
}

func (e *UnescapedCookieParamError) Unwrap() error {
	return e.Err
}

type UnmarshalingParamError struct {
	ParamName string
	Err       error
}

func (e *UnmarshalingParamError) Error() string {
	return fmt.Sprintf("Error unmarshaling parameter %s as JSON: %s", e.ParamName, e.Err.Error())
}

func (e *UnmarshalingParamError) Unwrap() error {
	return e.Err
}

type RequiredParamError struct {
	ParamName string
}

func (e *RequiredParamError) Error() string {
	return fmt.Sprintf("Query argument %s is required, but not found", e.ParamName)
}

type RequiredHeaderError struct {
	ParamName string
	Err       error
}

func (e *RequiredHeaderError) Error() string {
	return fmt.Sprintf("Header parameter %s is required, but not found", e.ParamName)
}

func (e *RequiredHeaderError) Unwrap() error {
	return e.Err
}

type InvalidParamFormatError struct {
	ParamName string
	Err       error
}

func (e *InvalidParamFormatError) Error() string {
	return fmt.Sprintf("Invalid format for parameter %s: %s", e.ParamName, e.Err.Error())
}

func (e *InvalidParamFormatError) Unwrap() error {
	return e.Err
}

type TooManyValuesForParamError struct {
	ParamName string
	Count     int
}

func (e *TooManyValuesForParamError) Error() string {
	return fmt.Sprintf("Expected one value for %s, got %d", e.ParamName, e.Count)
}

// Handler creates http.Handler with routing matching OpenAPI spec.
func Handler(si ServerInterface) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{})
}

type ChiServerOptions struct {
	BaseURL          string
	BaseRouter       chi.Router
	Middlewares      []MiddlewareFunc
	ErrorHandlerFunc func(w http.ResponseWriter, r *http.Request, err error)
}

// HandlerFromMux creates http.Handler with routing matching OpenAPI spec based on the provided mux.
func HandlerFromMux(si ServerInterface, r chi.Router) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseRouter: r,
	})
}

func HandlerFromMuxWithBaseURL(si ServerInterface, r chi.Router, baseURL string) http.Handler {
	return HandlerWithOptions(si, ChiServerOptions{
		BaseURL:    baseURL,
		BaseRouter: r,
	})
}

// HandlerWithOptions creates http.Handler with additional options
func HandlerWithOptions(si ServerInterface, options ChiServerOptions) http.Handler {
	r := options.BaseRouter

	if r == nil {
		r = chi.NewRouter()
	}
	if options.ErrorHandlerFunc == nil {
		options.ErrorHandlerFunc = func(w http.ResponseWriter, r *http.Request, err error) {
			http.Error(w, err.Error(), http.StatusBadRequest)
		}
	}
	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandlerFunc:   options.ErrorHandlerFunc,
	}

	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/config", wrapper.GetConfig)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/dev/config", wrapper.UpdateConfig)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/dynamodb/{userid}", wrapper.GetDynamoDBUserData)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/error/{errorid}", wrapper.GetDevError)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/health.json", wrapper.GetHealthDetails)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/make-friend", wrapper.MakeFriend)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/make-unfriend", wrapper.MakeUnfriend)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/stats", wrapper.GetAllStats)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/dev/subscriptions/{userid}", wrapper.DeleteVmqSubscriptions)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/subscriptions/{userid}", wrapper.GetVmqSubscriptions)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/sync/firstParty/{firstPartyid}/platform/{platform}/refresh", wrapper.GetFirstPartyRefreshToken)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/sync/firstParty/{firstPartyid}/platform/{platform}/token", wrapper.GetFirstPartyToken)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dev/sync/firstParty/{firstPartyid}/platform/{platform}/token", wrapper.SetFirstPartyToken)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dev/trustedlogin", wrapper.TrustedCreate)
	})
	r.Group(func(r chi.Router) {
		r.Delete(options.BaseURL+"/dev/trustedlogin/{clientid}", wrapper.TrustedDel)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/dev/trustedlogin/{clientid}", wrapper.TrustedUpdate)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/ulids/{ulid}", wrapper.DecodeUlid)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/dna/search", wrapper.UserSearchDNA)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/dna/{userid}/links", wrapper.GetUserLinksDNA)
	})
	r.Group(func(r chi.Router) {
		r.Patch(options.BaseURL+"/dev/user/presence", wrapper.UpdateUserPresence)
	})
	r.Group(func(r chi.Router) {
		r.Post(options.BaseURL+"/dev/user/presence/self", wrapper.SetUserDefinedPresence)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/{userid}/friends", wrapper.GetFriendsForUser)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/{userid}/groups", wrapper.GetAllGroupsForUser)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/{userid}/presence", wrapper.GetPresenceForUser)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/{userid}/sync", wrapper.SyncUserProfile)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/user/{userid}/voip", wrapper.GetVoipRoomsForUser)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/vmq/sessions", wrapper.GetVmqSessions)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/vmq/status", wrapper.GetVmqStatus)
	})
	r.Group(func(r chi.Router) {
		r.Get(options.BaseURL+"/dev/vmq/version", wrapper.GetVmqVersion)
	})

	return r
}
