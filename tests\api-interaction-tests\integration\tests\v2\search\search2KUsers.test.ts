import request from 'supertest';
import * as socialApi from '../../../lib/social-api';
import { describeSep as _ds } from '../../../lib/social-api';
import { TwokAccounts, SteamAccounts, EpicAccounts } from '../../../lib/config';
import { StatusCodes } from 'http-status-codes';

let usersTwok: TwokAccounts;

describe(`search full 2K accounts [public v2]${_ds}`, () => {
  describe(`happy cases${_ds}`, () => {
    beforeAll(async () => {
      usersTwok = new TwokAccounts(2, ["searcher", "searchee"]);
      await usersTwok.loginAll({});
    });

    afterAll(async () => {
      await usersTwok.logoutAll({});
    });

    it.each`
      scenario
      ${"can search 2k user by displayName"}
    `('$scenario', async ({}) => {
      let testCase = {
        description: 'search user by displayName',
        expected: "the matched user is present in the result list"
      }

      let actualSearchResult: request.Response;

      actualSearchResult = await socialApi.search2Kusers(
        usersTwok.acct["searcher"],
        { displayName: usersTwok.acct['searchee'].displayName }
      );

      const expectedSearchResult = {
        status: StatusCodes.OK,
        body: {
          items: [
            {
              name: usersTwok.acct['searchee'].displayName,
              userid: usersTwok.acct['searchee'].publicId,
            },
          ]
        },
      };
      socialApi.expectMore(
        () => {expect(actualSearchResult).toMatchObject(expectedSearchResult)},
        testCase,
        {
          resp: actualSearchResult,
          additionalInfo: {
            "fail reason": "expected user did not appear in the search result"
          }
        }
      );
    });
  });

  describe(`corner cases${_ds}`, () => {
    beforeAll(async () => {
      usersTwok = new TwokAccounts(1, ["searcher"]);
      await usersTwok.loginAll({});
    });

    afterAll(async () => {
      usersTwok.logoutAll({});
    });

    it.each`
      scenario                                        | keyValue
      ${"can't do search with empty name"}            | ${{ "displayName": "" }}
      ${"can't do search without required parameter"} | ${{}}
    `('$scenario', async ({ keyValue }) => {
      let testCase = {
        description: `search user with malformed input ${JSON.stringify(keyValue)}`,
        expected: "4xx status"
      };

      // search
      const searchResp = await socialApi.search2Kusers(usersTwok.acct['searcher'], keyValue)

      // can't do search with malformed key value
      socialApi.expectMore(
        () => {expect(searchResp.status).toBe(StatusCodes.UNPROCESSABLE_ENTITY)},
        testCase,
        {
          resp: searchResp,
          additionalInfo: {
            "failReason": "unexpected status code"
          }
        }
      );
    });
  });
});