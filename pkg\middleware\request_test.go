package middleware

import (
	"bytes"
	"io"
	"testing"

	"github.com/franela/goblin"
	"github.com/rs/zerolog"
)

func TestLimitRequestBody(t *testing.T) {
	g := goblin.Goblin(t)
	zerolog.SetGlobalLevel(zerolog.FatalLevel)

	g.<PERSON><PERSON>("LimitRequestBody", func() {
		g.It("should limit large request body", func() {
			mock := mockRequest(t, "")
			defer mock.ctrl.Finish()

			jsonbytes := []byte("{\"foo\":\"bar\"}")
			mock.r.Body = io.NopCloser(bytes.NewBuffer(jsonbytes))

			handler := LimitRequestBody(2)

			nextHandler := &TestHandler{CallCount: 0}
			handler(nextHandler).ServeHTTP(mock.w, mock.r)
			g.Assert(nextHandler.CallCount).Equal(1)

			respBody, err := io.ReadAll(nextHandler.Request.Body)
			g.<PERSON><PERSON><PERSON>(err).IsNotNil()
			g.<PERSON><PERSON><PERSON>(err.Error()).Equal("http: request body too large")
			g.<PERSON><PERSON><PERSON>(len(respBody)).Equal(2)

		})
	})
}
