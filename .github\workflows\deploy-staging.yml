name: Deploy staging
run-name: Deploy ${{ github.event.inputs.version }} to staging

on:
  workflow_dispatch:
    inputs:
      version:
        required: true
        description: 'Release image tag (https://go.aws/37MtoXh) with githash ex: 1.0.0-0a1b2c3d'
      deploy_key:
        required: true
        description: 'Deploy Key: SECRETS.DEPLOY_KEY (https://bit.ly/3KfxAx1)'
      skip_plugin_swap:
        type: boolean
        default: false
        description: 'Check this to skip plugin swap on this release'

permissions:
  actions: write
  id-token: write
  contents: write
  pull-requests: write
  deployments: write

jobs:
  run-helm:
    name: 'Run Helm Deploy'
    runs-on: [t2gp-arc-linux]
    env:
      AWS_DEFAULT_REGION: us-east-1
      VERNEMQ_PLUGIN_BUCKET: t2gp-social-vernemq-plugin
      CLUSTER: t2gp-production
      ENV_VER_MAPPING_TABLE: social-env-ver-mapping
      TARGET_ENV: staging
    outputs:
      image_tag: ${{ steps.output_info.outputs.image_tag }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          token: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
          submodules: recursive
          persist-credentials: false
      - name: AWS Assume Role
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::************:role/github_actions_admin
          role-session-name: GHActionSession
          aws-region: us-east-1
      - name: Environment Variables (Manual)
        if: github.event.inputs.version != ''
        run: |
          ver=${{github.event.inputs.version}}
          git_sha=$(git rev-parse ${ver: -8})
          echo "GIT_SHA=${git_sha}" >> $GITHUB_ENV
          echo "IMAGE_TAG=${{ github.event.inputs.version }}" >> $GITHUB_ENV
          echo DEPLOY_KEY=${{ secrets.DEPLOY_KEY }} >> $GITHUB_ENV
          echo "SUBMODULE_HASH=$(git ls-tree ${git_sha} deployments/vmq-plugin-social | awk '{print $3}' | cut -c1-8)" >> $GITHUB_ENV
      - name: Check Deploy Key
        if: ${{ env.DEPLOY_KEY != github.event.inputs.deploy_key }}
        run: |
          exit 1
      - name: Helm Deploy (staging)
        id: helm_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-service
          environment: ${{ env.TARGET_ENV }}-v2
          raise-pr: false
          helm-values: 'social-api.groupsApi.image.tag="${{ env.IMAGE_TAG }}",social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Helm Trusted Deploy (staging)
        id: helm_trusted_deploy
        uses: take-two-t2gp/app-charts-commit@v0.8
        env:
          GITHUB_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        with:
          cluster: ${{ env.CLUSTER }}
          service: social-trusted-api
          environment: ${{ env.TARGET_ENV }}-v2
          raise-pr: false
          helm-values: 'social-api.groupsApi.image.tag="${{ env.IMAGE_TAG }}",social-api.groupsApi.commitSha=${{env.GIT_SHA}},social-mqtt.pluginLoader.defaultPluginVersion=${{ env.SUBMODULE_HASH }}'
      - name: Get plugin version on AWS Parameter Store
        run: |
          set -ex
          CURRENTLY_STORED=$(aws ssm get-parameter --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --output text --query 'Parameter.Value')
          if [ "$CURRENTLY_STORED" == "${{ env.SUBMODULE_HASH }}" ]; then
            echo "UPDATE_PLUGIN=false" >> $GITHUB_ENV
          else
            echo "UPDATE_PLUGIN=true" >> $GITHUB_ENV
          fi
      - name: Swap VernemQ Plugin
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        working-directory: deployments
        run: |
          chmod +x "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
          "${GITHUB_WORKSPACE}/.github/scripts/vmq-plugin-swap.sh"
      - name: Update plugin version to AWS Parameter Store
        if: ${{ env.UPDATE_PLUGIN == 'true' && github.event.inputs.skip_plugin_swap == 'false' }}
        run: |
          aws ssm put-parameter --overwrite --name /social/mqtt/${{ env.TARGET_ENV }}/t2gp-plugin-version --value ${{ env.SUBMODULE_HASH }}
      - name: Update env-ver-mapping table
        id: env_ver_mapping_upsert
        uses: mooyoul/dynamodb-actions@v1.2.1
        with:
          operation: put
          region: us-east-1
          table: ${{ env.ENV_VER_MAPPING_TABLE }}
          item: '{ "env_label": "${{ env.TARGET_ENV }}-v2", "version": "${{env.IMAGE_TAG}}", "api_url":"https://social-service-${{ env.TARGET_ENV }}.d2dragon.net/v2", "api_private_url":"https://social-service-${{ env.TARGET_ENV }}-private.d2dragon.net", "mqtt_url":"wss://social-service-${{ env.TARGET_ENV }}.d2dragon.net/mqtt" }'
      - name: Dispatch Loadtesting
        env:
          GH_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        run: |
          gh workflow run deploy-loadtesting.yml --ref develop --raw-field version=${{github.event.inputs.version}} --raw-field run_loadtest=true
      - name: Report to Slack Success
        if: success() && github.event.inputs.version != ''
        uses: tokorom/action-slack-incoming-webhook@main
        env:
          INCOMING_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_RELEASE }}
        with:
          attachments: |
            [
              {
                "color": "#2c6e49",
                "blocks": [
                  {
                    "type": "header",
                    "text": {
                      "type": "plain_text",
                      "text": "🎉Social API Release - ${{ env.IMAGE_TAG }}🎉",
                      "emoji": true
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Environment*\n<https://social-service-${{ env.TARGET_ENV }}-api-private.d2dragon.net/|${{ env.TARGET_ENV }}>"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Source Code*\n<https://github.com/${{ github.repository }}/releases/tag/${{ env.IMAGE_TAG }}|Release Notes>\n<https://github.com/${{ github.repository }}/tree/${{ env.IMAGE_TAG }}|Browse>"
                    }
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*Github Action*\n<https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}|${{ github.run_id }}>"
                    }
                  }
                ]
              }
            ]
      - name: Publish docs to ${{ env.TARGET_ENV }}
        run: |
          ver=${{ github.event.inputs.version }}
          aws s3 cp --recursive s3://t2gp-docs/social-service/release-${ver:0:-9}/ s3://t2gp-docs/social-service/${{ env.TARGET_ENV }}-v2/
          aws s3 cp --recursive s3://t2gp-docs/openapi/social-service/release-${ver:0:-9}/ s3://t2gp-docs/openapi/social-service/${{ env.TARGET_ENV }}-v2/
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22.x
          registry-url: https://npm.pkg.github.com/
          scope: take-two-t2gp
      - name: Deploy docs
        run: npx @take-two-t2gp/t2gp-docs-upload@v1.6.8 -v staging -s -h -a ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
        env:
          NODE_AUTH_TOKEN: ${{ secrets.SERVICE_ACCOUNT_GH_PAT }}
      - name: Output Info
        id: output_info
        run: |
          echo "image_tag=${{ env.IMAGE_TAG }}" >> $GITHUB_OUTPUT

  post-deploy-update:
    needs: [run-helm]
    uses: ./.github/workflows/_post-deploy-notifs.yml
    with:
      environment_name: staging-v2
      version: ${{ needs.run-helm.outputs.image_tag }}
      parent_ghaction_run_id: '${{ github.run_id }}'
      skip_notification: false
      api_test_note: 'deploy staging'
    secrets: inherit
