import boto3
from boto3.dynamodb.conditions import Key
import config

#TODO: TEST
import json
import pprint
pp = pprint.PrettyPrinter(indent=4)


dynamodb = boto3.resource(
    'dynamodb',
    region_name='us-east-1',
    # boto3 will pick up the credentials from the environment variables
    # aws_access_key_id=config.aws_access_key_id,
    # aws_secret_access_key=config.aws_secret_access_key
)


table = dynamodb.Table('t2gp-social-develop-social')


# TestFail exception, for when a test fails.
class TestFail(Exception):
    def __init__(self, message, response=None):
        self.message = message
        self.response = response #requests.Response object

# OtherFail exception, for anything unexpected during test execution and
# verification.
class OtherFail(Exception):
    def __init__(self, message, response=None):
        self.message = message
        self.response = response #requests.Response object


#
def delete_account_items(keys_list):
    for keys in keys_list:
        table.delete_item(Key={"pk":keys[0], "sk":keys[1]})


# For POST
# test for "create new"
# Verify new relevant records are created after the API call.
def test_create(keys_list):
    def dw(func):
        def w(*args, **kwargs):
            # Setup
            delete_account_items(keys_list)
            try:
                # Test
                r = func(*args, **kwargs)
                if r.status_code != 200:
                    raise TestFail("api returned non-200", r)
                # Verification
                # TODO: use get_item_list?
                item_list = []
                for keys in keys_list:
                    resp = table.get_item(Key={"pk":keys[0], "sk":keys[1]})
                    item_list.append(resp.get('Item'))
                if None in item_list:
                    raise TestFail("item_list: {}".format(item_list))
            except TestFail as f:
                raise
            except Exception as e:
                raise OtherFail(str(e), r)
            finally:
                # Cleanup
                delete_account_items(keys_list)
        return w
    return dw


# For DELETE
# TODO: test for "remove"
# Verify that relevant data are deleted after the API call.
def test_remove():
    pass


# TODO: takes in one more dict as a verification that the changed items are correct
#       useful for: patch_user_profile_test_1
# For PUT and PATCH, or any others that have a side effect of modified data.
# test for "modify"
# Verify that relevant data are modified or unmodified, per expectation.
# TODO: Verify the returned output is expected.
def test_modify(keys_list):
    def dw(func):
        def w(*args, **kwargs):
            # Setup
            for keys in keys_list:
                table.put_item(
                    Item={
                        'pk':keys[0],
                        'sk':keys[1],
                        **keys[2],  # changed
                        **keys[3]   # unchanged
                    }
                )
            try:
                # Test
                r = func(*args, **kwargs)
                if r.status_code != 200:
                    raise TestFail("api returned non-200", r)
                # Verification
                fail_info = ""
                for keys in keys_list:
                    resp = table.get_item(Key={"pk":keys[0], "sk":keys[1]})
                    item = resp.get('Item')
                    changed = keys[2]
                    unchanged = keys[3]
                    for k, v in changed.items():
                        if v == item[k]:
                            fail_info += "[changed|{}]".format(v)
                    for k, v in unchanged.items():
                        if v != item[k]:
                            fail_info += "[unchanged|{} {}]".format(v, item[k])
                if fail_info != "":
                    raise TestFail(fail_info)
            except TestFail as f:
                raise
            except Exception as e:
                raise OtherFail(str(e), r)
            finally:
                # Cleanup
                delete_account_items(keys_list)
        return w
    return dw


# Mainly for "GET", or others that incur no db changes
# test for "no change"
# Verify the relevant data, whatever they are, didn't change after the API
# call.
def test_no_change(keys_list):
    def dw(func):
        def w(*args, **kwargs):
            def get_item_list(keys_list):
                item_list = []
                for keys in keys_list:
                    resp = table.get_item(Key={"pk":keys[0], "sk":keys[1]})
                    if resp.get('Item') == None:
                        print("Warning: keys {} return no Item".format(keys))
                    item_list.append(resp.get('Item'))
                return item_list

            items_list_before = get_item_list(keys_list)

            # Test
            r = func(*args, **kwargs)
            if r.status_code != 200:
                raise TestFail("api returned non-200", r)

            items_list_after = get_item_list(keys_list)

            if items_list_before != items_list_after:
                raise TestFail("before:{}|after:{}".format(items_list_before,
                                                           items_list_after))
        return w
    return dw


# For "GET"
# test for "expected output"
# Given the set of data, verify that the output returned by GET is expected.
# keys_list is a list of tuple of 3 items: pk, sk, and attributes
# extra_cleanup_list is a list of tuple of 2 items: pk and sk. It may be needed
# for API that generate additional data.
def test_expected_output(keys_list, verification, extra_cleanup_list=None):
    def dw(func):
        def w(*args, **kwargs):
            # Setup
            for keys in keys_list:
                table.put_item(
                    Item={
                        'pk':keys[0],
                        'sk':keys[1],
                        **keys[2]
                    }
                )
            try:
                # Test
                r = func(*args, **kwargs)

                # TODO: remove instance check when "get_friends_pagination"
                # rework is done
                import requests
                if isinstance(r, requests.models.Response):
                    if r.status_code == 200:
                        try:
                            json.loads(r.text)
                        except json.decoder.JSONDecodeError:
                            raise TestFail("malformed JSON response", r)

                # Verify test output
                verification(r)
            except TestFail as f:
                raise
            except Exception as e:
                raise OtherFail(str(e), r)
            finally:
                # Cleanup
                delete_account_items(keys_list)
                if extra_cleanup_list is not None:
                    delete_account_items(extra_cleanup_list)
        return w
    return dw


# TODO: use me
def put_items(keys_list):
    for keys in keys_list:
        table.put_item(
            Item={
                'pk':keys[0],
                'sk':keys[1],
                **keys[2]
            }
        )


# utility funciton - use with caution
# Given a user id, delete all friends and the user profiles of the friends.
def delete_friends_and_user_profiles(user_id):
    resp = table.query(
        KeyConditionExpression=Key("pk").eq(user_id) &
            Key("sk").begins_with("friend#")
    )

#    pp.pprint(resp["Items"])

    for i in resp["Items"]:
        print("Deleting pk:{} | sk:{}".format(i["pk"], i["sk"]))
        table.delete_item(Key={"pk":i["pk"], "sk":i["sk"]})

        # the user profile of the friend
        f_user_id = i["sk"].replace("friend", "user")
        print("Deleting pk:{} | sk:{}\n".format(f_user_id, f_user_id))
        table.delete_item(Key={"pk":f_user_id, "sk":f_user_id})


# query data relevant to the main user account, and the mock user accounts.
def query_data(user_id):
    # query for any records other than the main user profile
    resp_main_up = table.query(
        KeyConditionExpression=Key("pk").eq("user#{}".format(user_id))
    )

    # scan for all records begins with user#0000000 which are the mock user profiles
    scan_kwargs = {
        "FilterExpression": Key("pk").begins_with("user#**********")
    }

    resp_mock_up_list = []
    done = False
    start_key = None
    while not done:
        if start_key:
            scan_kwargs["ExclusiveStartKey"] = start_key
        resp_mock_up = table.scan(**scan_kwargs)
        resp_mock_up_list.append(resp_mock_up)
        start_key = resp_mock_up.get("LastEvaluatedKey", None)
        done = start_key is None

    return (resp_main_up, resp_mock_up_list)


# utility function - check if db is in a clean slate, with option to cleanup.
def check_clean_slate(user_id, cleanup=False):
    resp_main_up, resp_mock_up_list = query_data(user_id)

    if resp_main_up["Count"] > 1:
        print("there are records other than the user profile pk/sk user#{}/user#{}".format(user_id, user_id))
        for i in resp_main_up["Items"]:
            if i["sk"] != "user#{}".format(user_id):
                print(i["pk"])
                print(i["sk"])
                if cleanup == True:
                    print("cleaning up...")
                    table.delete_item(Key={"pk":i["pk"], "sk":i["sk"]})



    if resp_mock_up_list[0]["Count"] != 0:
        print("there are residual mock user profiles and associated records.")

    for resp in resp_mock_up_list:
        for i in resp["Items"]:
            print(i["pk"])
            print(i["sk"])
            if cleanup == True:
                print("cleaning up...")
                table.delete_item(Key={"pk":i["pk"], "sk":i["sk"]})


# utility function - print out records relevant to the main user profile
def report_current_data(user_id):
    resp_main_up, resp_mock_up_list = query_data(user_id)

    for i in resp_main_up["Items"]:
        print(">>> pk:{}".format(i["pk"]))
        print(">>> sk:{}".format(i["sk"]))
        pp.pprint(i)
        print()

    for resp in resp_mock_up_list:
        for i in resp["Items"]:
            print(">>> pk:{}".format(i["pk"]))
            print(">>> sk:{}".format(i["sk"]))
            pp.pprint(i)
            print()


def print_response(response):
    print()
    print("##### BEGIN Printing Response ###########################")
    print("url: {}".format(response.url))
    print("response code: {}".format(response.status_code))
    print("response body:")
    try:
        pp.pprint(json.loads(response.text))
    except json.decoder.JSONDecodeError:
        print("(*** warning *** malformed JSON)")
        print(response.text)
    print("##### END Printing Response #############################")
    print()


def print_traceback(traceback):
    print()
    print(">>>>> BEGIN Traceback >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>")
    print(traceback, end='')
    print("<<<<< END Traceback <<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<")
    print()
