// Package pubApi implementation for interfaces from oapi-codegen
package api

import (
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/go-chi/chi/v5"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/notification"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/store"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

// SocialPublicAPI social pubApi struct
type SocialPublicAPI struct {
	Ds         store.DataStoreInterface
	Cfg        *config.Config
	Cache      cache.RedisCacheInterface
	monitor    *health.ServiceMonitor
	Tele       telemetry.TelemetryInterface
	HttpClient utils.HTTPClientInterface
	Id         identity.IdentityInterface
	Sns        notification.SNS
}

// NewSocialPublicAPI create social pubApi object
func NewSocialPublicAPI(
	cfg *config.Config,
	ds store.DataStoreInterface,
	rc cache.RedisCacheInterface,
	sm *health.ServiceMonitor,
	t telemetry.TelemetryInterface,
	id identity.IdentityInterface,
	sns notification.SNS) *SocialPublicAPI {

	pubapi := &SocialPublicAPI{
		Cfg:        cfg,
		Ds:         ds,
		Cache:      rc,
		monitor:    sm,
		HttpClient: http.DefaultClient,
		Tele:       t,
		Id:         id,
		Sns:        sns,
	}

	return pubapi
}

// StaticRoutes static routes
func StaticRoutes(r chi.Router) {
	// Create a route along /files that will serve contents from
	// the ./data/ folder.
	cwd, _ := os.Getwd()
	websiteDir := http.Dir(filepath.Join(cwd, "web"))
	log.Info().Msgf("Service files from %s", websiteDir)
	FileServer(r, "/", websiteDir)
}

// FileServer sets a static http server
func FileServer(r chi.Router, path string, root http.FileSystem) {
	if strings.ContainsAny(path, "{}*") {
		log.Fatal().Msgf("FileServer does not permit any URL parameters.")
	}

	if path != "/" && path[len(path)-1] != '/' {
		r.Get(path, http.RedirectHandler(path+"/", http.StatusMovedPermanently).ServeHTTP)
		path += "/"
	}
	path += "*"

	r.Get(path, func(w http.ResponseWriter, r *http.Request) {
		rctx := chi.RouteContext(r.Context())
		pathPrefix := strings.TrimSuffix(rctx.RoutePattern(), "/*")
		fs := http.StripPrefix(pathPrefix, http.FileServer(root))
		if strings.HasSuffix(r.URL.Path, ".js") {
			w.Header().Add("Cache-Control", "no-Cache, no-store, must-revalidate")
		}
		fs.ServeHTTP(w, r)
	})
}

func (api *SocialPublicAPI) GetHealth(w http.ResponseWriter, r *http.Request, params apipub.GetHealthParams) {
	sm := api.monitor

	//we return overall health if failed
	h := sm.GetOverallHealth(params.Id)
	if h != health.OK {
		w.WriteHeader(http.StatusInternalServerError)
	} else if params.Id != nil { //else if param is not valid, we return 404
		*(params.Id) = strings.ToLower(*(params.Id))
		if *(params.Id) != "dna" && *(params.Id) != "pdi" && *(params.Id) != "rsg" {
			w.WriteHeader(http.StatusNotFound)
		}
	} //else return ok

	w.Header().Set(constants.KContentType, constants.KApplicationJson)
	w.Write(sm.GetHealthReport(health.HealthReportPublic, params.Id).Bytes())
}
