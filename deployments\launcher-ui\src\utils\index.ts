import { PRESENCE_STATUS } from '../constant';

export * from './friend';
export * from './query';
export { themefunc } from './theme';
export type { socialTheme } from './theme';
export * from './time';

export const isOnline: (status: string) => boolean = status => {
  return status === PRESENCE_STATUS.online;
};

export const isPlaying: (status: string) => boolean = status => {
  return status === PRESENCE_STATUS.playing;
};

export const isAway: (status: string) => boolean = status => {
  return status === PRESENCE_STATUS.away;
};

export const isOffline: (status: string) => boolean = status => {
  return status === PRESENCE_STATUS.offline;
};
