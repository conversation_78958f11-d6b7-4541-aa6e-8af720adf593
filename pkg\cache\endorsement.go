package cache

import (
	"context"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/cache/index"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/identity"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
)

func (rc *RedisCache) GetEndorsement(ctx context.Context, userid, productid, endorsementName string) (*apipub.EndorsementResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	key := apipub.BuildEndorsementRedisKey(tenant, userid, productid, endorsementName)

	endorsement, err := getCachedObject[apipub.EndorsementResponse](ctx, rc, key)
	if err != nil {
		return nil, err
	}

	return endorsement, nil
}

// GetEndorsements Fetch endorsements for user
func (rc *RedisCache) GetEndorsements(ctx context.Context, userid, productid string) (*[]*apipub.EndorsementResponse, error) {
	tenant := identity.GetTenantFromCtx(ctx, rc.id)

	user := index.NewUserSubject(tenant, productid, userid)
	if user == nil {
		return nil, errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	key := user.EndorsementListKey()
	if key == nil {
		return nil, errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	idx := index.NewSecondaryIndex(*key, "")

	endorsements, _, err := getObjsFromSecondaryIndex[apipub.EndorsementResponse](ctx, rc, idx, aws.Int64(0), aws.String(""), false)
	if err != nil {
		return nil, err
	}

	return endorsements, nil
}

func (rc *RedisCache) IncrementEndorsement(ctx context.Context, userid, productid string, endorsement *apipub.EndorsementResponse, incrementValue int) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	if endorsement.EndorsementName == "" {
		endorsement.EndorsementName = "default"
	}

	endorsementKey := apipub.BuildEndorsementRedisKey(tenant, userid, productid, endorsement.EndorsementName)

	user := index.NewUserSubject(tenant, productid, userid)
	if user == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	idxKey := user.EndorsementListKey()
	if idxKey == nil {
		return errs.New(http.StatusNotFound, errs.EGroupsNotFound)
	}
	idx := index.NewSecondaryIndex(*idxKey, endorsementKey)

	endorsements, _, _ := getObjsFromSecondaryIndex[apipub.EndorsementResponse](ctx, rc, idx, aws.Int64(0), aws.String(""), false)

	ttl := time.Duration(rc.cfg.TtlDefault) * time.Second

	found := false
	if endorsements != nil {
		for _, anEndorsement := range *endorsements {
			if strings.EqualFold(anEndorsement.EndorsementName, endorsement.EndorsementName) {
				found = true
			}
		}
	}

	if !found {
		endorsement.CurrentEndorsementCount = incrementValue
		endorsement.TotalEndorsementCount = incrementValue
	} else {
		endorsement.CurrentEndorsementCount = endorsement.CurrentEndorsementCount + incrementValue
		endorsement.TotalEndorsementCount = endorsement.TotalEndorsementCount + incrementValue
	}

	//set Object
	err2 := setCachedObject(ctx, rc, endorsement, endorsementKey, ttl)
	if err2 != nil {
		log.Error().Err(err2).Str("userid", userid).Str("endorsementName", endorsement.EndorsementName).Msg("failed to set recently played")
		return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
	}

	rc.setSecondaryIndex(ctx, idx)
	return nil
}

func (rc *RedisCache) ResetEndorsement(ctx context.Context, userid, productid, endorsementName string) error {
	log := logger.FromContext(ctx)
	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	if endorsementName == "" {
		endorsementName = "default"
	}

	endorsementKey := apipub.BuildEndorsementRedisKey(tenant, userid, productid, endorsementName)

	user := index.NewUserSubject(tenant, productid, userid)
	if user == nil {
		return nil
	}
	idxKey := user.EndorsementListKey()
	if idxKey == nil {
		return nil
	}
	idx := index.NewSecondaryIndex(*idxKey, endorsementKey)

	endorsements, _, _ := getObjsFromSecondaryIndex[apipub.EndorsementResponse](ctx, rc, idx, aws.Int64(0), aws.String(""), false)

	ttl := time.Duration(rc.cfg.TtlDefault) * time.Second

	if endorsements != nil {
		for _, anEndorsement := range *endorsements {
			if strings.EqualFold(anEndorsement.EndorsementName, endorsementName) {
				anEndorsement.CurrentEndorsementCount = 0
				//set Object
				err2 := setCachedObject(ctx, rc, anEndorsement, endorsementKey, ttl)
				if err2 != nil {
					log.Error().Err(err2).Str("userid", userid).Str("endorsementName", endorsementName).Msg("failed to set recently played")
					return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
				}
				rc.setSecondaryIndex(ctx, idx)
			}
		}
	}

	return nil
}

func (rc *RedisCache) /**/ RemoveEndorsement(ctx context.Context, userid, productid, endorsementName string) error {

	tenant := identity.GetTenantFromCtx(ctx, rc.id)
	if endorsementName == "" {
		endorsementName = "default"
	}

	endorsementKey := apipub.BuildEndorsementRedisKey(tenant, userid, productid, endorsementName)

	user := index.NewUserSubject(tenant, productid, userid)
	if user == nil {
		return nil
	}
	idxKey := user.EndorsementListKey()
	if idxKey == nil {
		return nil
	}
	idx := index.NewSecondaryIndex(*idxKey, endorsementKey)

	rc.delSecondaryIndex(ctx, idx)
	rc.del(ctx, endorsementKey)

	return nil
}

//
//// Increment transactionally increments the key using GET and SET commands.
//func (rdb *RedisCache) incrementKeyBy(ctx context.Context, key string, count int) error {
//	// Transactional function.
//	txf := func(tx *redis.Tx) error {
//		// get the current value or zero.
//		n, err := tx.Get(ctx, key).Int()
//		if err != nil && err != redis.Nil {
//			return err
//		}
//
//		// Actual operation (local in optimistic lock).
//		n = n + count
//
//		// Operation is commited only if the watched keys remain unchanged.
//		_, err = tx.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
//			pipe.Set(ctx, key, n, 0)
//			return nil
//		})
//		return err
//	}
//
//	// Retry if the key has been changed.
//	for i := 0; i < rdb.cfg.RedisTransactionMaxRetry; i++ {
//		err := rdb.ecWriteClient.Watch(ctx, txf, key)
//		if err == nil {
//			// Success.
//			return nil
//		}
//		if err == redis.TxFailedErr {
//			// Optimistic lock lost. Retry.
//			continue
//		}
//		// Return any other error.
//		return err
//	}
//
//	return errs.New(http.StatusInternalServerError, errs.ERedisCacheSetFailed)
//}
