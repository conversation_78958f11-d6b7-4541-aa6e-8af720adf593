package identity

import (
	"compress/gzip"
	"context"
	"crypto/rsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"net/http"
	"net/url"
	"strings"
	"sync/atomic"
	"time"

	"github.com/2kg-coretech/dna-common/pkg/authn"
	jwt2k "github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/smithy-go/middleware"
	jwtgo "github.com/golang-jwt/jwt/v4"
	"github.com/pascaldekloe/jwt"
	"github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/telemetry"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

type RockstarEnv struct {
	publicKey *rsa.PublicKey
	rosURL    string
}

// RSService rockstar identity service
type RSService struct {
	cfg           config.Config
	serviceStatus *health.ServiceStatus
	httpClient    utils.HTTPClientInterface
	s3Client      *s3.Client
	s3Bucket      string
	s3Key         string
	pk            atomic.Value
}

type RSClaims struct {
	NameID          string `json:"nameid"`
	TokenStorageTTL string `json:"scAuth.TokenStorageTtl"`
	AvatarURL       string `json:"scAuth.AvatarUrl"`
	Nickname        string `json:"scAuth.Nickname"`
	IsMinor         string `json:"scAuth.IsAMinor"`
	NotBefore       int    `json:"nbf"`
	Scope           string `json:"scope"`
	ExpiresIn       int    `json:"exp"`
	IssuedAt        int    `json:"iat"`
	Issuer          string `json:"iss"`
}

func (c *RSClaims) Valid() error {
	return nil
}

type RSServiceInterface interface {
	health.DependentService
	SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error)
	SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error)
	GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error)
	Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error)
	GetIdentityServiceStr() IdentityService
}

func NewRSService(ctx context.Context, cfg *config.Config) *RSService {
	url, err := url.Parse(cfg.RSConfigURL)
	if err != nil {
		log.Error().Err(err).Msgf("failed to parse rockstar config url %s", cfg.RSConfigURL)
		return nil
	}
	if url.Scheme != "s3" {
		log.Error().Err(err).Msgf("url scheme is not s3 %s", cfg.RSConfigURL)
		return nil
	}

	status := &health.ServiceStatus{
		Instances: []*health.InstanceInfo{
			{
				Id:     cfg.RSConfigURL,
				Status: health.UNKNOWN,
			},
		},
		Status: health.UNKNOWN,
	}

	config := aws.Config{
		Region:      cfg.S3BucketRegion,
		Credentials: utils.GetV2CredentialProvider(ctx),
	}

	config.APIOptions = append(config.APIOptions, func(s *middleware.Stack) error {
		return s.Finalize.Add(&telemetry.OnComplete{}, middleware.After)
	})

	s3Client := s3.NewFromConfig(config)

	svc := &RSService{
		cfg:           *cfg,
		serviceStatus: status,
		httpClient:    http.DefaultClient,
		s3Client:      s3Client,
		s3Bucket:      url.Host,
		s3Key:         url.Path,
	}

	// download config from s3
	svc.UpdateConfig(ctx)

	return svc
}

// Authenticate rockstar JWT
//
// JWT example:
//
//	{
//		"alg": "RS256",
//		"kid": "d4e41bcc-9bc0-4916-8a0d-a28b1004e7e7",
//		"typ": "JWT"
//	}
//
//	{
//		"nameid": "185401030",
//		"scAuth.TokenStorageTtl": "0",
//		"scAuth.AvatarUrl": "http://a.rsg.sc/n/yulius-take2/n",
//		"scAuth.Nickname": "yulius-take2",
//		"scAuth.IsAMinor": "False",
//		"nbf": **********,
//		"aud": [
//		  "https://store.rockstargames.com",
//		  "https://scapi.rockstargames.com"
//		],
//		"scope": "scapi:profile/checkoutdata",
//		"exp": 1656002577,
//		"iat": **********,
//		"iss": "https://signin.rockstargames.com"
//	}
func (s *RSService) Authenticate(ctx context.Context, authHeader []string, skipRevokedTokenValidation bool) (*authn.AuthenticationData, error) {
	if len(authHeader) != 2 || authHeader[0] != "Bearer" {
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidBearerToken)
	}

	jwtString := authHeader[1]

	token, err := jwt2k.ParseJWTTokenWithoutValidation(jwtString)
	if err != nil {
		log.Error().Err(err).Msgf("jwt parse failed")
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidBearerToken)
	}

	pk := s.pk.Load().(map[string]*RockstarEnv)

	envName := "t2gp-rs"
	env, ok := pk[envName]
	if !ok {
		log.Error().Msgf("rockstar env [%s] not supported", envName)
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidBearerToken)
	}
	publicKey := env.publicKey
	claims, err := jwt.RSACheck([]byte(jwtString), publicKey)
	if err != nil {
		log.Error().Err(err).Msg("jwt auth failed")
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidBearerToken)
	}
	if !claims.Valid(time.Now()) {
		log.Error().Msg("jwt expired")
		return nil, errs.New(http.StatusUnauthorized, errs.EInvalidBearerToken)
	}

	token.Claims.Subject = claims.Set["nameid"].(string)

	data := &authn.AuthenticationData{
		AuthType: authn.UserAuth,
		Claims:   token.Claims,
	}

	return data, nil
}

func (s *RSService) GetIdentityServiceStr() IdentityService {
	return IdentityServiceRockstar
}

func (s *RSService) SearchAccounts(ctx context.Context, request *apipub.SearchAccountRequest) (*apipub.SearchAccountResponseList, error) {
	return nil, errs.New(http.StatusUnprocessableEntity, errs.ENotImplemented)
}

func (s *RSService) SearchAccountsByUserID(ctx context.Context, userid string) (*apipub.SearchAccountResponseList, error) {
	parser := jwtgo.Parser{SkipClaimsValidation: true}
	claims := RSClaims{}
	token := ctx.Value(constants.BearerAuthString).(string)
	_, _, err := parser.ParseUnverified(token, &claims)
	if err == nil && claims.NameID == userid {
		return &apipub.SearchAccountResponseList{
			apipub.SearchAccountResponse{
				Id:          &claims.NameID,
				DisplayName: &claims.Nickname,
			},
		}, nil
	}

	return nil, errs.New(http.StatusUnprocessableEntity, errs.ENotImplemented)
}

func (s *RSService) GetUserProfileAccountLinks(ctx context.Context, userid string) (*[]apipub.AccountLinkDNA, error) {
	return nil, errs.New(http.StatusUnprocessableEntity, errs.ENotImplemented)
}

func (s *RSService) Login(ctx context.Context, username string, password string, locale string, appID string) (*apipub.LoginResponse, error) {
	return nil, errs.New(http.StatusUnprocessableEntity, errs.ENotImplemented)
}

func (s *RSService) RefreshToken(ctx context.Context, refreshToken string, locale string) (*apipub.LoginResponse, error) {
	return nil, errs.New(http.StatusUnprocessableEntity, errs.ENotImplemented)
}

func (s *RSService) Logout(ctx context.Context, token string) error {
	return nil
}

// health service

func (s *RSService) IsCritical() bool {
	return false
}

func (s *RSService) CheckHealth() bool {
	svc := s.serviceStatus.Instances[0]
	status := health.FAIL
	start := time.Now()

	// placeholder until we get more infomration on status checks
	url := "https://signin.rockstargames.com/"
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		health.SetInstance(svc, health.FAIL, start, 0, "")
		return false
	}
	resp, err := s.httpClient.Do(req)

	elapsed := time.Since(start).Milliseconds()

	if err != nil || resp.StatusCode != http.StatusOK {
		health.SetInstance(svc, health.FAIL, start, elapsed, "")
		return false
	}

	defer resp.Body.Close()

	status = health.OK
	health.SetInstance(svc, status, start, elapsed, "")

	s.serviceStatus.Status = status
	return status == health.OK
}

func (s *RSService) LastStatus() *health.ServiceStatus {
	return s.serviceStatus
}

func (s *RSService) getConfig(ctx context.Context) map[string]*RockstarEnv {
	input := &s3.GetObjectInput{
		Bucket: aws.String(s.s3Bucket),
		Key:    aws.String(strings.TrimPrefix(s.s3Key, "/")),
	}

	resp, err := s.s3Client.GetObject(ctx, input)
	if err != nil {
		log.Error().Err(err).Msgf("failed to get S3 object s://%s/%s", s.s3Bucket, s.s3Key)
		return map[string]*RockstarEnv{}
	}
	// Make sure to always close the response Body when finished
	defer resp.Body.Close()
	gzipReader, err := gzip.NewReader(resp.Body)
	if err != nil {
		log.Error().Err(err).Msgf("Failed to create gzip reader: %v", err)
	}
	defer gzipReader.Close()
	var rsEnvs map[string]struct {
		RosURL string `json:"cb"`
		PemKey string `json:"pk"`
	}
	err = json.NewDecoder(gzipReader).Decode(&rsEnvs)
	if err != nil {
		log.Error().Err(err).Msgf("failed to decode rockstar env")
		return map[string]*RockstarEnv{}
	}
	ret := make(map[string]*RockstarEnv, len(rsEnvs))
	for env, obj := range rsEnvs {
		p := []byte(obj.PemKey)
		block, _ := pem.Decode(p)
		if block == nil {
			log.Error().Msgf("could not parse pem public key: %s", p)
			return map[string]*RockstarEnv{}
		}
		_pk, err := x509.ParsePKIXPublicKey(block.Bytes)
		if err != nil {
			log.Error().Err(err).Msgf("public key parrse failed")
			return map[string]*RockstarEnv{}
		}
		pk := _pk.(*rsa.PublicKey)
		ret[env] = &RockstarEnv{
			publicKey: pk,
			rosURL:    obj.RosURL,
		}
	}
	return ret
}

func (s *RSService) UpdateConfig(ctx context.Context) error {
	pk := s.getConfig(ctx)
	s.pk.Store(pk)
	return nil
}

func (s *RSService) SyncUserProfile(ctx context.Context, userid string) (*apipub.UserProfileResponse, error) {
	return nil, nil
}

func (s *RSService) SyncUserProfiles(ctx context.Context, userids []string) (*[]*apipub.UserProfileResponse, error) {
	return nil, nil
}

func (s *RSService) SetAgeGroupFromDob(ctx context.Context, profile *apipub.UserProfileResponse) {
	//Probably not needed for RS.  We won't be using age group like we did for DNA.
}

func (s *RSService) GetProductIdFromAppId(ctx context.Context, appId string) (*string, error) {
	return nil, nil
}
