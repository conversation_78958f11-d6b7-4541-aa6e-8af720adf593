package api

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/2kg-coretech/dna-common/pkg/jwt"
	"github.com/google/uuid"
	"github.com/take-two-t2gp/t2gp-social-service/constants"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/api"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apitrusted"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

type refreshCredentials struct {
	Type         string `json:"type"`
	RefreshToken string `json:"refreshToken"`
	InstanceId   string `json:"instanceId"`
}

type serverCredentials struct {
	Type       string `json:"type"`
	InstanceId string `json:"instanceId"`
}

type serverLoginRequest struct {
	AccountType string            `json:"accountType"`
	Credentials serverCredentials `json:"credentials"`
}

type serverRefreshRequest struct {
	AccountType string             `json:"accountType"`
	Credentials refreshCredentials `json:"credentials"`
}

const (
	BasicAuth  = "basicAuth.Scopes"
	BearerAuth = "bearerAuth.Scopes"
)

func GetAuthScope(ctx context.Context, authScope string) string {
	var scopes []string
	if authScope == BasicAuth || authScope == BearerAuth {
		scopes = ctx.Value(authScope).([]string)
		if len(scopes) == 0 {
			return ""
		}
		return scopes[0]
	}

	return ""
}

// ValidateTS vaidates the Trusted API login and updates the request context
func (tApi *SocialTrustedAPI) ValidateTS(r *http.Request, jwt *jwt.Token) (*http.Request, error) {

	ctx := r.Context()
	log := logger.FromContext(ctx)
	checkBasic := ctx.Value(apitrusted.BearerAuthScopes)
	if checkBasic == nil {
		log.Error().Msgf("API spec does not have basic authentication requirement")
		return r, errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)
	}

	clientId, clientSecret, ok := r.BasicAuth()
	if !ok {
		log.Error().Msgf("API spec does not have basic authentication requirement")
		return r, errs.New(http.StatusUnauthorized, errs.EInvalidAuthHeader)
	}

	//query redis/dynamo for data related to clientId
	tsCid, err := tApi.SocialApi.Cache.GetTsClientId(ctx, clientId)
	if tsCid == nil {
		log.Error().Err(err).Msgf("Failed to get TS clientId from cache")
		tsCid, err = tApi.SocialApi.Ds.GetTsClientId(ctx, clientId)

		if tsCid == nil {
			log.Error().Err(err).Msgf("Failed to get TS clientId from db")
			//not found in db either
			return r, errs.New(http.StatusUnauthorized, errs.EInvalidLogin)
		} else {
			//store in cache
			err = tApi.SocialApi.Cache.SetTsClientId(ctx, tsCid)
			if err != nil {
				log.Error().Err(err).Msgf("TS Cache set fail")
			}
		}
	}

	if !utils.CheckPassword(tsCid.Hash, clientSecret) {
		return r, errs.New(http.StatusUnauthorized, errs.EInvalidLogin)
	}

	productId := tsCid.ProductId
	tenantId := tsCid.TenantId
	ctx = context.WithValue(ctx, constants.T2GPCtxTenant, tenantId)
	ctx = context.WithValue(ctx, constants.T2GPCtxProductId, productId)
	ctx = context.WithValue(ctx, constants.T2GPCtxTrustedId, clientId)
	ctx = context.WithValue(ctx, constants.T2GPCtxTrustedHash, tsCid.Hash)

	//add productid to span
	span, ok := tracer.SpanFromContext(ctx)
	if ok {
		//copied this to cache library, not going to pull it if not in cache here
		pname, _ := tApi.SocialApi.Cache.GetProductIdToName(ctx, tsCid.TenantId, productId)
		span.SetTag("id.product", pname)
		span.SetTag("id.pid", productId)
		span.SetTag("id.iss", productId)
		span.SetTag("id.userid", clientId)
	}

	return r.WithContext(ctx), nil
}

func (tApi *SocialTrustedAPI) ServerGetToken(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	log := logger.FromContext(ctx)
	path := "/auth/tokens"
	url := tApi.cfg.SsoURL + path
	auth := r.Header.Get("Authorization")

	if auth == "" {
		errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EDnaLoginFailed))
		return
	}

	// Todo: Figure out how to authenticate different product ids
	// r, err := tApi.ValidateTS(r)
	// if err != nil {
	// 	errs.Return(w, r, err.(*errs.Error))
	// 	return
	// }

	headers := map[string]string{
		constants.KContentType: constants.KApplicationJson,
		"Authorization":        auth,
	}

	instanceId := strings.ReplaceAll(uuid.New().String(), "-", "")
	serverCredentials := serverCredentials{Type: "server", InstanceId: instanceId}
	loginRequest := serverLoginRequest{AccountType: "server", Credentials: serverCredentials}

	bytes, err := json.Marshal(loginRequest)
	if err != nil {
		log.Error().Err(err).Msgf("failed to login")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	span, _ := tracer.StartSpanFromContext(ctx, "trustedServer.login", tracer.ResourceName(path))
	defer span.Finish()

	responseBytes, err := sendRequest(ctx, "POST", url, headers, bytes)
	if err != nil {
		log.Error().Err(err).Msgf("failed to login")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	var response apitrusted.ServerLoginResponse
	err = json.Unmarshal(responseBytes, &response)
	if err != nil {
		log.Error().Err(err).Msgf("failed to login")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	response.InstanceId = instanceId
	utils.WriteJsonResponse(w, r, http.StatusOK, response)
}

func (tApi *SocialTrustedAPI) ServerRefreshToken(w http.ResponseWriter, r *http.Request) {
	var refreshTokenRequest apitrusted.ServerRefreshTokenRequestBody
	if !api.DecodeBody(w, r, &refreshTokenRequest) {
		return
	}

	ctx := r.Context()
	log := logger.FromContext(ctx)
	path := "/auth/tokens"
	url := tApi.cfg.SsoURL + path
	auth := r.Header.Get("Authorization")

	if auth == "" {
		errs.Return(w, r, errs.New(http.StatusUnauthorized, errs.EDnaRefreshTokenFailed))
		return
	}

	headers := map[string]string{
		constants.KContentType: constants.KApplicationJson,
		"Authorization":        auth,
	}

	refreshCredentials := refreshCredentials{Type: "server", RefreshToken: refreshTokenRequest.RefreshToken, InstanceId: refreshTokenRequest.InstanceId}
	refreshRequest := serverRefreshRequest{AccountType: "server", Credentials: refreshCredentials}

	bytes, err := json.Marshal(refreshRequest)
	if err != nil {
		log.Error().Err(err).Msgf("failed to refresh")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	span, _ := tracer.StartSpanFromContext(ctx, "trusted.refresh", tracer.ResourceName(path))
	defer span.Finish()

	responseBytes, err := sendRequest(ctx, "POST", url, headers, bytes)
	if err != nil {
		log.Error().Err(err).Msgf("failed to refresh")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	var response apitrusted.ServerLoginResponse
	err = json.Unmarshal(responseBytes, &response)
	if err != nil {
		log.Error().Err(err).Msgf("failed to refresh")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	utils.WriteJsonResponse(w, r, http.StatusOK, response)
}

func (tApi *SocialTrustedAPI) ServerLogout(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	log := logger.FromContext(ctx)
	path := "/auth/tokens/logout"
	url := tApi.cfg.SsoURL + path

	tokenStr := ""
	bearer := ctx.Value(constants.BearerAuthString)
	if bearer != nil {
		tokenStr = bearer.(string)
	}

	headers := map[string]string{
		"Authorization": fmt.Sprintf("Bearer %s", tokenStr),
	}

	span, _ := tracer.StartSpanFromContext(ctx, "trustedServer.logout", tracer.ResourceName(path))
	defer span.Finish()
	var responseBytes []byte

	_, err := sendRequest(ctx, "POST", url, headers, responseBytes)
	if err != nil {
		log.Error().Err(err).Msgf("failed to logout")
		errs.Return(w, r, errs.ToError(err))
		return
	}

	utils.WriteJsonResponse(w, r, http.StatusOK, map[string]string{})
}

func sendRequest(ctx context.Context, method, url string, headers map[string]string, body []byte) ([]byte, error) {
	log := logger.FromContext(ctx)
	req, err := http.NewRequest(method, url, bytes.NewBuffer(body))
	if err != nil {
		log.Error().Err(err).Msgf("failed to create request")
		return nil, errs.New(http.StatusInternalServerError, errs.EDnaLoginFailed)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.Error().Err(err).Msg("failed to send request")
		return nil, errs.New(http.StatusInternalServerError, errs.EDnaLoginFailed)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Error().Err(err).Msg("failed to read response body")
		return nil, errs.New(http.StatusInternalServerError, errs.EDnaLoginFailed)
	}

	if resp.StatusCode != 200 {
		log.Error().Err(err).Str("body", string(respBody)).Msg("request failed")
		return nil, errs.New(resp.StatusCode, errs.EDnaLoginFailed)
	}

	return respBody, nil
}
