package telemetry

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/kinesis"
	"github.com/aws/smithy-go/middleware"
	zlog "github.com/rs/zerolog/log"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/config"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/errs"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/health"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/logger"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	"gopkg.in/DataDog/dd-trace-go.v1/ddtrace/tracer"
)

// KinesisObj Type constraints for kinesis Telemetry using generics
type KinesisObj interface {
	GroupTelemetryMeta | FriendTelemetryMeta | BlocklistTelemetryMeta | ReportTelemetryMeta | EndorsementTelemetryMeta | GenericTelemetryMeta
}

var _ health.DependentService = &KinesisWithMonitoring{}

type KinesisWithMonitoring struct {
	KinesisInterface
	serviceStatus    *health.ServiceStatus
	kinesisStreamArn string
}

// KinesisInterface used to allow overriding while testing
type KinesisInterface interface {
	PutRecord(ctx context.Context, params *kinesis.PutRecordInput, optFns ...func(*kinesis.Options)) (*kinesis.PutRecordOutput, error)
	ListShards(ctx context.Context, params *kinesis.ListShardsInput, optFns ...func(options *kinesis.Options)) (*kinesis.ListShardsOutput, error)
}

func NewKinesisClient(ctx context.Context, cfg *config.Config) KinesisInterface {
	log := logger.FromContext(ctx)
	awsconfig := aws.Config{
		Region:      cfg.KinesisRegion,
		Credentials: utils.GetV2CredentialProvider(ctx),
	}

	awsconfig.APIOptions = append(awsconfig.APIOptions, func(s *middleware.Stack) error {
		return s.Serialize.Add(&OnBuild{}, middleware.After)
	})

	awsconfig.APIOptions = append(awsconfig.APIOptions, func(s *middleware.Stack) error {
		return s.Finalize.Add(&OnComplete{}, middleware.After)
	})
	var client *kinesis.Client
	for attempt := 1; attempt <= cfg.AwsRequestMaxRetryAttempt && client == nil; attempt++ {
		client = kinesis.NewFromConfig(awsconfig, func(o *kinesis.Options) {
			o.Retryer = utils.GetDefaultRetryStandard(cfg.AwsRequestMaxRetryAttempt)
			o.BaseEndpoint = aws.String("https://kinesis.us-east-1.amazonaws.com")
		})

		log.Error().Msg("Failed to create a Kinesis client. Will retry.")
		time.Sleep(time.Second * time.Duration(attempt))
	}

	if client != nil {
		streamArn := aws.String(cfg.KinesisStreamArn)

		// check we have access to the arn
		_, err := client.ListShards(ctx, &kinesis.ListShardsInput{StreamARN: streamArn})
		if err != nil {
			log.Error().Err(err).Msgf("Kinesis stream %s is not reachable", cfg.KinesisStreamArn)
			return nil
		}
		return client
	}

	return nil
}

func NewKinesis(ctx context.Context, cfg *config.Config) *KinesisWithMonitoring {
	kinesisClient := NewKinesisClient(ctx, cfg)

	status := &health.ServiceStatus{
		Instances: []*health.InstanceInfo{
			{
				Id:     cfg.KinesisStreamArn,
				Status: health.UNKNOWN,
			},
		},
		Status: health.UNKNOWN,
	}
	return &KinesisWithMonitoring{
		KinesisInterface: kinesisClient,
		serviceStatus:    status,
		kinesisStreamArn: cfg.KinesisStreamArn,
	}
}

func (ki *KinesisWithMonitoring) IsCritical() bool {
	return true
}

func (ki *KinesisWithMonitoring) CheckHealth() bool {
	if ki == nil || ki.KinesisInterface == nil {
		return false
	}
	svc := ki.serviceStatus.Instances[0]
	status := health.FAIL
	start := time.Now()

	shardInput := &kinesis.ListShardsInput{
		StreamARN: aws.String(ki.kinesisStreamArn),
	}

	if !utils.IsLocal() {
		_, err := ki.KinesisInterface.ListShards(context.Background(), shardInput)
		elapsed := time.Since(start).Milliseconds()
		if err != nil {
			health.SetInstance(svc, status, start, elapsed, err.Error())
			zlog.Err(err).Str("streamArn", ki.kinesisStreamArn).Msg("Kinesis Service unreachable")

		} else {
			status = health.OK
			health.SetInstance(svc, status, start, elapsed, "one kinesis streams")
		}
		ki.serviceStatus.Status = status
	}
	return status == health.OK
}

func (ki *KinesisWithMonitoring) LastStatus() *health.ServiceStatus {
	return ki.serviceStatus
}

// BuildKinesisStreamName builds kinesis stream name to write to
func BuildKinesisStreamName() string {
	env := utils.GetEnvironment()
	if utils.IsReleaseCantidate() || utils.IsPullRequest() {
		env = "develop"
	}
	return fmt.Sprintf("t2gp-social-telemetry-%s", env)
}

// sendToKinesis send tele event to kinesis
func sendToKinesis[kiObj KinesisObj](ctx context.Context, t *Telemetry, item *kiObj) error {
	if !utils.IsLocal() {
		log := logger.FromContext(ctx)
		msg := "should kinesis"
		log.Debug().Bool("shouldKinesis", t.cfg.ShouldKinesis).Str("event", msg).Msg(msg)
		if t.cfg.ShouldKinesis {
			if item == nil {
				val := "nil kinesis record"
				log.Error().Str("event", val).Msg(val)
				return errs.New(http.StatusNotFound, errs.EKinesisPutFailed)
			}

			data, err := json.Marshal(item)
			if err != nil {
				val := "failed to marshal kinesis record"
				log.Error().Err(err).Str("event", val).Msg(val)
				return errs.New(http.StatusBadRequest, errs.EKinesisMarshalFailed)
			}
			if data == nil {
				val := "kinesis record marshal empty"
				log.Error().Err(err).Str("event", val).Msg(val)
				return errs.New(http.StatusNotFound, errs.EKinesisMarshalFailed)
			}

			itemStr := string(data)
			val := "sending item to kinesis"
			log.Info().Str("item", itemStr).Str("event", val).Msg(val)

			partitionKey := fmt.Sprintf("%T%s", *new(kiObj), utils.GenerateNewULID())

			pr := &kinesis.PutRecordInput{
				StreamARN:    aws.String(t.cfg.KinesisStreamArn),
				Data:         data,
				PartitionKey: &partitionKey,
			}

			span, _ := tracer.StartSpanFromContext(ctx, "telemetry.PutRecord", tracer.ServiceName("aws.Kinesis"), tracer.ResourceName("SendToKinesis"))
			out, err := t.ki.PutRecord(ctx, pr)
			span.Finish()
			if err != nil {
				log.Error().Err(err).Str("kinesisStreamArn", t.cfg.KinesisStreamArn).Str("item", itemStr).Str("event", "failed to put kinesis record").Msg("failed to put kinesis record")
				return errs.New(http.StatusInternalServerError, errs.EKinesisPutFailed)
			}

			outStr := ""
			if out != nil && out.ShardId != nil && out.SequenceNumber != nil {
				outStr = fmt.Sprintf("shardid=%s sequenceNumber=%s ", *out.ShardId, *out.SequenceNumber)
			}
			log.Debug().Str("deliveryStream", t.cfg.KinesisStreamArn).Str("output", outStr).Str("event", "kinesis put record").Msg("kinesis put record")

		}
	}
	return nil
}
