package cache

import (
	"testing"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/franela/goblin"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/apipub"
	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
	gomock "go.uber.org/mock/gomock"
)

func UserProfile(t *testing.T) {
	g := goblin.Goblin(t)
	mockCtrl := gomock.NewController(t)
	defer mockCtrl.Finish()

	var userid, platformid, fpid, userid2, productid string
	var profile, profile2 *apipub.UserProfileResponse
	var links []apipub.AccountLinkDNA
	var recent1, recent2 *apipub.RecentlyPlayedUserResponse
	email := "<EMAIL>"
	created := time.Now()
	actType := apipub.AccountTypeDNAFULL
	actType2 := apipub.AccountTypeDNAPLATFORM
	linkType := apipub.Parent
	linkType2 := apipub.Steam
	var err error
	var bOk bool
	ttl := time.Duration(60) * time.Second

	g.Describe("Test UserProfile", func() {

		g.Before(func() {
			userid = utils.GenerateRandomDNAID()
			platformid = utils.GenerateRandomDNAID()
			fpid = utils.GenerateRandomDNAID()
			userid2 = utils.GenerateRandomDNAID()
			productid = utils.GenerateRandomDNAID()

			links = append(links, apipub.AccountLinkDNA{
				AccountId:   &userid,
				AccountType: &actType,
				LinkType:    &linkType,
			})

			links = append(links, apipub.AccountLinkDNA{
				AccountId:    &platformid,
				AccountType:  &actType2,
				FirstPartyid: &fpid,
				LinkType:     &linkType2,
			})

			profile = &apipub.UserProfileResponse{
				AgeGroup:    aws.Int(5),
				Created:     &created,
				DisplayName: aws.String("displayName"),
				Dob:         aws.String("01/01/1950"),
				Email:       &email,
				Links:       &links,
				Locale:      aws.String("en-US"),
				Userid:      userid,
			}

			recent1 = &apipub.RecentlyPlayedUserResponse{
				ForUserid:  aws.String(userid),
				LastPlayed: &created,
				Name:       aws.String("displayName"),
				Productid:  &productid,
				Userid:     userid2,
			}
			recent2 = &apipub.RecentlyPlayedUserResponse{
				ForUserid:  aws.String(userid),
				LastPlayed: &created,
				Name:       aws.String("displayName"),
				Productid:  &productid,
				Userid:     platformid,
			}
		})

		g.It("set UserProfile", func() {
			err = rc.SetUserProfile(ctx, profile, ttl)
			g.Assert(err).IsNil()
		})

		g.It("get UserProfile", func() {
			profile2, err = rc.GetUserProfile(ctx, userid)
			g.Assert(err).IsNil()
			g.Assert(profile2).IsNotNil()
			g.Assert(profile2.Userid).Equal(userid)
		})

		g.It("set UserProfile Ttl", func() {
			bOk, err = rc.SetUserProfileTtl(ctx, userid, time.Duration(5)*time.Second)
			g.Assert(err).IsNil()
			g.Assert(bOk).Equal(true)
		})

		g.It("Delete UserProfile", func() {
			err = rc.DeleteUserProfile(ctx, userid)
			g.Assert(err).IsNil()
		})

		g.It("set UserProfiles", func() {
			profiles := make([]*apipub.UserProfileResponse, 0, 2)
			profiles = append(profiles, profile)
			profile2.Userid = userid2
			profiles = append(profiles, profile2)

			err = rc.SetUserProfiles(ctx, &profiles, ttl)
			g.Assert(err).IsNil()
		})

		g.It("get UserProfiles", func() {
			userids := []string{userid, userid2}
			var profiles *[]*apipub.UserProfileResponse
			profiles, err = rc.GetUserProfiles(ctx, userids)
			g.Assert(err).IsNil()
			g.Assert(profiles).IsNotNil()
			g.Assert(len(*profiles)).Equal(2)
			g.Assert((*profiles)[0].Userid).Equal(userid)
			g.Assert((*profiles)[0].Userid).Equal(userid2)
		})

		g.It("set RecentlyPlayedUser", func() {
			err = rc.SetRecentlyPlayedUser(ctx, productid, recent1, ttl)
			g.Assert(err).IsNil()
		})

		g.It("get RecentlyPlayedUser", func() {
			limit := int64(2)
			next := ""
			var rpusers *[]*apipub.RecentlyPlayedUserResponse
			rpusers, next, err = rc.GetRecentlyPlayed(ctx, userid, productid, &limit, &next)
			g.Assert(err).IsNil()
			g.Assert(next).Equal("")
			g.Assert(rpusers).IsNotNil()
			g.Assert(len(*rpusers)).Equal(1)
			g.Assert(*((*rpusers)[0].ForUserid)).Equal(*recent1.ForUserid)
			g.Assert((*rpusers)[0].Userid).Equal(recent1.Userid)
		})

		g.It("set/get RecentlyPlayedUsers", func() {
			rpusersSet := make([]*apipub.RecentlyPlayedUserResponse, 0, 2)
			rpusersSet = append(rpusersSet, recent1)
			rpusersSet = append(rpusersSet, recent2)
			rc.SetRecentlyPlayedUsers(ctx, productid, &rpusersSet, ttl)

			limit := int64(2)
			next := ""
			var rpusers *[]*apipub.RecentlyPlayedUserResponse
			rpusers, next, err = rc.GetRecentlyPlayed(ctx, userid, productid, &limit, &next)
			g.Assert(err).IsNil()
			g.Assert(next).Equal("")
			g.Assert(rpusers).IsNotNil()
			g.Assert(len(*rpusers)).Equal(2)
			g.Assert(*((*rpusers)[0].ForUserid)).Equal(*recent1.ForUserid)
			g.Assert((*rpusers)[0].Userid).Equal(recent1.Userid)
			g.Assert(*((*rpusers)[0].ForUserid)).Equal(*recent2.ForUserid)
			g.Assert((*rpusers)[0].Userid).Equal(recent2.Userid)
		})

		g.It("UpdateUserTtls", func() {
			bArr, ttlErr := rc.UpdateUserTtls(ctx, userid, ttl)
			g.Assert(ttlErr).IsNil()
			g.Assert(len(bArr)).Equal(3)
		})

		g.It("get UserCacheMeta", func() {
			//check that updateUserTtls populated the meta
			userMeta, mErr := rc.GetUserCacheMeta(ctx, userid)
			g.Assert(mErr).IsNil()
			g.Assert(userMeta).IsNotNil()
			g.Assert(userMeta.Blocks).Equal(true)
			g.Assert(userMeta.Friends).Equal(true)
			g.Assert(userMeta.Pending).Equal(true)
			g.Assert(len(userMeta.FirstParty)).Equal(1)
		})

		g.It("Update UserCacheMeta ttl", func() {
			bOk, err = rc.UpdateUserCacheMetaTtl(ctx, userid, time.Duration(5)*time.Second)
			g.Assert(err).IsNil()
			g.Assert(bOk).Equal(true)
		})

		g.It("Delete UserProfile", func() {
			err = rc.DeleteUserProfile(ctx, userid)
			g.Assert(err).IsNil()
		})

		g.It("Delete UserProfile2", func() {
			err = rc.DeleteUserProfile(ctx, userid2)
			g.Assert(err).IsNil()
		})

		g.It("UpdateUserTtls User Not Found", func() {
			bArr, ttlErr := rc.UpdateUserTtls(ctx, userid, ttl)
			g.Assert(ttlErr).IsNotNil()
			g.Assert(len(bArr)).Equal(3)
			g.Assert(bArr[0]).Equal(false)
			g.Assert(bArr[1]).Equal(false)
			g.Assert(bArr[2]).Equal(false)
		})

	})
}
