package apipub

import (
	"fmt"
	"slices"
	"strings"

	"github.com/take-two-t2gp/t2gp-social-service/pkg/utils"
)

const PresesencePriorityUserSet = 10000
const PresencePriorityGameSetStart = 20000
const PresencePriorityGameSetEnd = 29999
const PresencePriorityLauncherAutomated = 30000
const PresencePriorityUnknown = 99999

// RedisKey redis key
func (presence *PresenceResponse) RedisKey(tenant string) string {
	// TODO: [SPOP] - revert the commenting out
	//return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:presence:{%s}", tenant, presence.Productid, utils.GetEnvironment(), presence.Userid, presence.ActiveSessionid)
	return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:presence", tenant, presence.Productid, strings.TrimSuffix(utils.GetEnvironment(), "-v2"), presence.Userid)
}

func BuildPresenceRedisKey(tenant, productid, userid, sessionid string) string {
	// TODO: [SPOP] - revert the commenting out
	//return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:presence:{%s}", tenant, productid, utils.GetEnvironment(), userid, sessionid)
	return fmt.Sprintf("%s:prod:%s:%s:user:{%s}:presence", tenant, productid, strings.TrimSuffix(utils.GetEnvironment(), "-v2"), userid)
}

func (presence *PresenceResponse) Topic(tenant string) string {
	return fmt.Sprintf("%s/prod/%s/%s/user/%s/presence", tenant, presence.Productid, strings.TrimSuffix(utils.GetEnvironment(), "-v2"), presence.Userid)
}

// IsValidPresenceStatus enum validation for presence status
func IsValidPresenceStatus(status string) bool {
	return slices.Contains([]string{"authenticating", "away", "chat", "custom", "dnd", "offline", "online", "playing"}, status)
}
