openapi: '3.0.0'
info:
  title: SSO Service - Trusted Server
  description: SSO Service endpoints for Trusted Servers.  All IDs are assumed to be for DNA services unless otherwise noted.  Properties denoted with a red asterisk in the models are required to send the specified request.  Non-denoted properties are either optional or read-only.
  version: 'Current Release Version: 1.50.0'
 
servers:
- url: https://sso.api.2kcoretech.online/sso/v2.0
 
paths:
  '/app/products/{productId}':
    get:
      description: "Retrieves the specified Product by its ID, which can be obtained from the 'Location' response header when the Product is created.\n\n
       <b>Privileged ACLs</b>: <i>aclAppAdmin</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Products
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/productId'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetProductResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/app/apps/':
    get:
      description: "Retrieves a list of Applications based on the Application IDs specified in the path query.  If none are provided, all existing Applications will be displayed.  If at least one is provided, the 'offset' and 'limit' parameters will be ignored.\n\n
       <b>Privileged ACLs</b>: <i>aclAppGetById</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Applications
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/appIds'
        - $ref: '#/components/parameters/offset'
        - $ref: '#/components/parameters/limit'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetApplicationResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/app/apps/{appId}':
    get:
      description: "Retrieves the specified Application by its ID.\n\n
       <b>Privileged ACLs</b>: <i>aclAppGetById, aclAppGetByIdConstrained (optional)</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - Applications
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/appId'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetApplicationResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/{accountId}':
    get:
      description: "Retrieves details for the specified Account ID.\n\n
       <b>Privileged ACLs</b>: <i>aclAccountGetById</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/accountId'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetAccountResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/{accountId}/emails/pending':
    patch:
      description: "Updates the Account pending emails for the current user.\n\n
       <b>Privileged ACLs</b>: <i>aclUserAdmin</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/accountId'
      requestBody:
        description: The Account pending email update request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateEmailRequest'
      responses:
        204:
          description: No Content
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
    delete:
      description: "Deletes the pending emails to cancel a pending email update for the current user.\n\n
       <b>Privileged ACLs</b>: <i>aclUserAdmin</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/accountId'
        - $ref: '#/components/parameters/historyAccount'
        - $ref: '#/components/parameters/historyReason'
      responses:
        204:
          description: No Content
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/{accountId}/app-data':
    get:
      description: "Retrieves a list of Applications the current user has logged into along with the number of logins and the last login for each Application.\n\n
       <b>Privileged ACLs</b>: <i>aclUserAppDataGet</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/accountId'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetAccountAppDataResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/{accountId}/responses':
    post:
      description: "Updates the response to one or more Legal Documents for the specified user.\n\n
        <b>Privileged ACLs</b>: <i>aclUserAdmin</i>\n\n
        <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - appCredentials: []
      parameters:
      - $ref: '#/components/parameters/accountId'
      requestBody:
        description: The Legal Document response list.
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/LegalResponseRequest'
      responses:
        204:
          description: No Content
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        409:
          $ref: '#/components/responses/409'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/search':
    post:
      description: "Retrieves details for a list of specified Accounts.\n\n
       <b>Privileged ACLs</b>: <i>aclUserAccountsGet</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - accessToken: []
      requestBody:
        description: The Account search request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SearchAccountRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SearchAccountResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/user/accounts/batch/first-party':
    post:
      description: "Retrieves First-Party information for a specified list of Platform or Device Account IDs. If a Full Account ID is provided, the First-Party information for its linked Platform and Device Accounts will be displayed.\n\n
       <b>Privileged ACLs</b>: <i>aclUserAccountsGetFirstParty</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Management
      security:
        - appCredentials: []
      requestBody:
        description: The First Party Batch search request.
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GetBatchFirstPartyResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/{accessToken}':
    get:
      description: "Retrieves details for the specified Legacy Access or Refresh Token (SSO 1.0).\n\n
       <b>Privileged ACLs</b>: <i>aclAuthTokensValidate</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/accessToken'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetLegacyTokenResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        404:
          $ref: '#/components/responses/404'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/revoked/{jwtId}':
    get:
      description: "Retrieves details for the specified Access or Refresh Token (SSO 2.0).\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/jwtId'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetTokenResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/revoked':
    get:
      description: "Retrieves the state for the specified Access or Refresh Token (SSO 2.0).\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      security:
        - appCredentials: []
      parameters:
        - $ref: '#/components/parameters/tokenAccountId'
        - $ref: '#/components/parameters/tokenId'
        - $ref: '#/components/parameters/createdOn'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetRevokedTokenStateResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/tokens/zendesk':
    post:
      description: "Issues an Access Token for Zendesk-based Accounts.\n\n
       <b>Privileged ACLs</b>: <i>None</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Tokens
      parameters:
        - $ref: '#/components/parameters/zendeskUserToken'
        - $ref: '#/components/parameters/zendeskName'
        - $ref: '#/components/parameters/zendeskEmail'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GetZendeskTokenResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/crm/emails':
    post:
      description: "Sends an email to the specified user based on the email template and variables provided.\n\n
       <b>Privileged ACLs</b>: <i>aclTriggerEmail</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - User Account Emails
      security:
        - appCredentials: []
      requestBody:
        description: The send email request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AccountEmailSendRequest'
      responses:
        200:
          description: OK
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
  '/auth/device_authorization':
    patch:
      description: "Updates the status of device authorization to authorized or denied.\n\n
       <b>Privileged ACLs</b>: <i>aclDeviceConsoleUpdate</i>\n\n
       <b>Application ACLs</b>: <i>None</i>"
      tags:
        - 'Device Authorization'
      security:
        - appCredentials: []
      requestBody:
        description: The Device Code status update request.
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeviceAuthorizationPatchRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ValidateUserCodeResponse'
        400:
          $ref: '#/components/responses/400'
        401:
          $ref: '#/components/responses/401'
        403:
          $ref: '#/components/responses/403'
        415:
          $ref: '#/components/responses/415'
        429:
          $ref: '#/components/responses/429'
        500:
          $ref: '#/components/responses/500'
 
components:
  securitySchemes:
    appCredentials:
      type: http
      scheme: basic
    accessToken:
      type: oauth2
      flows:
        implicit:
           authorizationUrl: https://tbd.2k.com
           scopes: {}
 
  schemas:
    GetProductResponse:
      type: object
      properties:
        id:
          description: The Product ID, which was generated when the specified Product was created.
          type: string
          format: ID
        name:
          description: The name of the specified Product.
          type: string
        shortId:
          description: The first seven characters of the specified Product ID, and this string is to be unique with all Product IDs.
          type: string
        createdOn:
          description: The UNIX timestamp that indicates when the specified Product was created.
          type: integer
          format: uint64
        updatedOn:
          description: The UNIX timestamp that indicates when the specified Product was last modified.
          type: integer
          format: uint64
    GetApplicationResponse:
      type: object
      properties:
        id:
          description: The Application ID, which was generated when the specified Application was created.
          type: string
          format: ID
        privateId:
          description: The Private ID, which is a unique 32-character string used for encryption.
          type: string
          format: ID
        secret:
          description: The Application Secret, which is a unique string that when combined with the Application ID forms the Basic Authorization value for the Application.
          type: string
          format: ID
        productId:
          description: The Product ID of the specified Application.
          type: string
          format: ID
        groupId:
          description: The Application Group ID of the specified Application.
          type: string
          format: ID
        nameTranslations:
          description: The list of localized titles for the specified Application.
          type: object
          additionalProperties:
            type: string
          example:
            'en-US': 'en-US Localized Title'
        name:
          description: The name of the specified Application.
          type: string
        type:
          description: "The specified Application type.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - BASE GAME\n\n
            2 - WEBSITE\n\n
            3 - COMPANION APPLICATION"
          type: integer
          format: uint32
        deviceType:
          description: "The device type of the specified Application.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - XBOX ONE\n\n
            2 - PLAYSTATION 4\n\n
            3 - WINDOWS 7\n\n
            4 - WEB\n\n
            5 - iOS\n\n
            6 - ANDROID\n\n
            7 - OSX\n\n
            8 - SURFACE RT\n\n
            9 - WINDOWS PHONE\n\n
            10 - LINUX\n\n
            11 - WINDOWS 8+\n\n
            12 - SWITCH\n\n
            13 - PLAYSTATION 5\n\n
            14 - XBOX SERIES X"
          type: integer
          format: uint32
        onlineServiceType:
          description: "The online service type of the Application.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - XBOX LIVE\n\n
            2 - SONY ENTERTAINMENT NETWORK\n\n
            3 - STEAM\n\n
            4 - WEB\n\n
            5 - LEGACY GAME CENTER\n\n
            6 - GOOGLE PLAY\n\n
            9 - WINDOWS PHONE\n\n
            10 - CALICO\n\n
            11 - NINTENDO\n\n
            12 - GAME CENTER\n\n
            13 - WEGAME\n\n
            14 - VORTEX\n\n
            15 - EPIC\n\n
            16 - STADIA\n\n
            17 - FACEBOOK\n\n
            18 - GOOGLE\n\n
            19 - TWITTER\n\n
            20 - TWITCH\n\n
            21 - DEVICE\n\n
            22 - APPLE\n\n
            23 - ZENDESK\n\n
            24 - T2GP\n\n
            99 - WINDOWS DEVELOPER"
          type: integer
          format: uint32
        acls:
          description: The Application access control list, which limits actions to those logged into the Application (Bearer Token) or an unauthenticated Application.
          type: array
          items:
            type: string
        privilegedAcls:
          description: The Privileged access control list, which limits actions to those authenticated with Basic Authorization for Admin and Trusted Servers.
          type: array
          items:
            type: string
        accessTokenTtl:
          description: The number of seconds (TTL) until the issued access token expires.
          type: integer
          format: uint32
        refreshTokenTtl:
          description: The number of seconds (TTL) until the issued refresh token expires.
          type: integer
          format: uint32
        jwtSupportVersion:
          description: The version at which JWT support starts for the specified Application.
          type: string
        jwtEncryptionKey:
          description: The JWT Encryption Key for the specified Application.
          type: string
          format: ID
        jwsAlgorithm:
          description: The JWS Algorithm type used to sign and verify the token for the specified Application.
          type: string
          enum: [HS256, RS256]
        firstPartyId:
          description: The First-Party ID for the specified Application.
          type: string
        firstPartyValidationSupportVersion:
          description: The version at which First-Party validation support starts for the specified Application.
          type: string
        firstPartyValidationKey:
          description: The secret key used for First-Party validation.
          type: string
        redirectDomains:
          description: The URL to redirect to specific domains.
          type: array
          items:
            type: string
        useSteamPurchaseSandbox:
          description: Determines if the 'useSteamPurchaseSandbox' functionality is active for Steam-based Applications.
          type: boolean
          enum: [true, false]
        steamWebApiKey:
          description: The Web API key used for Steam-based Applications.
          type: string
        railAccessKeyId:
          description: The Rail Access Key ID used for WeGame-based Applications.
          type: string
        issuePlayFabTicket:
          description: Whether a PlayFab ticket is issued when logging into the specified Application.
          type: boolean
          enum: [true, false]
        playFabTitleId:
          description: The PlayFab Title ID used for Applications with PlayFab integration.
          type: string
        nintendoCredentials:
          description: A mapping of Nintendo Client ID and Secret for each Nintendo environment in the specified Nintendo-based Application.
          type: object
          properties:
            environmentName:
              $ref: '#/components/schemas/nintendoCredential'
        onlineAuthTypes:
          description: A mapping of the Online Service Type to the respective First-Party credentials ('firstPartyId', 'firstPartyValidationKey', 'firstPartyValidationSupportVersion') for authentication purposes.
          type: object
          properties:
            onlineServiceType:
              $ref: '#/components/schemas/clientCredential'
        loginFlows:
          description: Mandatory tag support for login workflow in OAuth2.0-based Portal Accounts.
          type: object
          properties:
            displayName:
              description: A boolean value whether to enforce Display Name checking in OAuth2.0-based login flow.
              type: boolean
              enum: [true, false]
            tosPp:
              description: A boolean value whether to enforce Terms of Service and Privacy Policy checking in OAuth2.0-based login flow.
              type: boolean
              enum: [true, false]
        emailBaseUrl:
          description: Supports the ability to override the base URL when creating a link for email user notification such as Account verification, reset password, etc.
          type: string
        shortId:
          description: The first seven characters of the specified Application ID, and this string is to be unique with all Application IDs.
          type: string
        isHidden:
          description: Determines if the specified Application is hidden, which is only applicable to SSO 1.0-based Applications.
          type: boolean
          enum: [true, false]
        crossProgression:
          description: Enables cross-progression on a per DNA Application basis to offer cross-progression capabilities for selected platforms/clients within a Title/Product.
          type: boolean
          enum: [true, false]
        createdOn:
          description: The UNIX timestamp that indicates when the specified Application was created.
          type: integer
          format: uint64
        updatedOn:
          description: The UNIX timestamp that indicates when the specified Application was last modified.
          type: integer
          format: uint64
    clientCredential:
      type: object
      description: The Client credentials to which the specified Application belongs.
      properties:
        firstPartyId:
          description: The First-Party ID for the specified Application.
          type: string
        firstPartyValidationSupportVersion:
          description: The version at which First-Party validation support starts for the specified Application.
          type: string
        firstPartyValidationKey:
          description: The secret key used for First-Party validation.
          type: string
        railAccessKeyId:
          description: The Rail Access Key ID used for WeGame-based Applications.
          type: string
        useSteamPurchaseSandbox:
          description: Determines if the 'useSteamPurchaseSandbox' functionality is active for Steam-based Applications.
          type: boolean
          enum: [true, false]
        steamWebApiKey:
          description: The Web API key used for Steam-based Applications.
          type: string
        isFirstPartyValidationEnforced:
          description: Flag to enforce validation of first party auth for an app regardless of its user agent.
          type: boolean
          default: false
          enum: [true, false]
    nintendoCredential:
      type: object
      description: "The Nintendo environment to which this Application belongs. Replace 'environmentName' with one of the following values:\n\n\n
        lp1 - RETAIL (GENERAL USERS)\n\n
        dd1 - DEV HARDWARE (APP DEVELOPERS)\n\n
        sp1 - RETAIL (NINTENDO QA TEAM)\n\n
        dp1 - RETAIL (SPECIAL USE CASE)\n\n
        sd1 - DEV HARDWARE (NINTENDO QA TEAM)"
      properties:
        clientId:
          description: The Nintendo Client ID being used for the specified Application.
          type: string
        clientSecret:
          description: The Nintendo Client Secret being used for the specified Application.
          type: string
    GetAccountResponse:
      type: object
      properties:
        id:
          description: The Account ID, which was generated when the specified Account was created.
          type: string
          format: ID
        type:
          description: "The type of Account.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - ANONYMOUS\n\n
            2 - PLATFORM\n\n
            3 - FULL\n\n
            4 - TOOLS\n\n
            5 - NEWSLETTER\n\n
            6 - UNDISCLOSED\n\n
            7 - PRIVACY POLICY ACCEPTED ONLY"
          type: integer
          format: uint32
        dob:
          description: The date of birth for the specified user.
          type: string
          format: mm/dd/yyyy
        isDobVerified:
          description: Whether the date of birth for the specified user has been verified.
          type: boolean
          enum: [true, false]
        ageGroup:
          description: "The Age Group of the specified user.\n\n\n
            List of groups for reference:\n\n
            0 - UNKNOWN\n\n
            1 - UNDER 13 YEARS\n\n
            2 - 13-17 YEARS\n\n
            3 - 18-24 YEARS\n\n
            4 - 25-34 YEARS\n\n
            5 - 35 YEARS AND ABOVE\n\n
            6 - 13 YEARS AND ABOVE"
          type: integer
          format: uint32
        ageRating:
          type: object
          properties:
            ratingId:
              description: The unique ID of the Age Rating used to determine the age group of the specified user.
              type: integer
              example: 101
            description:
              description: The description of the specified Age Rating.
              type: string
            country:
              description: The country which the specified Age Rating is applied to.
              type: string
              example: 'US'
            minAge:
              description: The minimum age of the specified Age Rating.
              type: integer
              example: 10
            ratingCode:
              description: The Age Rating code which consists of a two-letter country code and the minimum age.
              type: string
              example: 'US10'
        legalResponses:
          description: The list of Legal Documents and the response status of each document for the specified user.
          type: array
          items:
            type: object
            properties:
              documentId:
                description: The Legal Document ID, which was generated when the Legal Document was created.
                type: string
                format: ID
              isAccepted:
                description: Whether the specified user has accepted the Legal Document.
                type: boolean
                enum: [true, false]
              type:
                description: "The Legal Document type.\n\n\n
                  List of types for reference:\n\n
                  0 - UNKNOWN\n\n
                  1 - TERMS OF SERVICE\n\n
                  2 - PRIVACY POLICY\n\n
                  3 - END USER LICENSE AGREEMENT\n\n
                  4 - DEMO\n\n
                  5 - OPTIONAL\n\n
                  6 - CUSTOM\n\n
                  7 - RESERVED"
                type: integer
                format: uint32
              appId:
                description: The Application ID associated with the Legal Document.
                type: string
                format: ID
        locale:
          description: The locale for the specified user.
          type: string
          format: xx-XX
          example: 'en-US'
        signUpAppId:
          description: The Application ID that was used to create the Account for the specified user.
          type: string
          format: ID
        lastSuccessfulLogin:
          description: The UNIX timestamp that indicates when the last successful login occurred.
          type: integer
          format: uint64
        consecutiveFailedLogins:
          description: The number of consecutive failed logins for the specified user.
          type: integer
          format: uint32
        isGloballyBanned:
          description: Whether the specified user is banned from accessing all available Applications.
          type: boolean
          enum: [true, false]
        bannedAppIds:
          description: The list of Application IDs the specified user is unable to access.
          type: array
          items:
            type: string
        createdOn:
          description: The UNIX timestamp that indicates when the specified Account was created.
          type: integer
          format: uint64
        modifiedOn:
          description: The UNIX timestamp that indicates when the specified Account was last modified.
          type: integer
          format: uint64
        email:
          description: The email address of the specified user.
          type: string
          format: email
        isEmailVerified:
          description: Whether the email address for the specified user has been verified.
          type: boolean
          enum: [true, false]
        emailVerificationContext:
          description: A 32-character string that is used to verify the email being used for the Account.
          type: string
          format: ID
        pendingEmail:
          description: The target email of a pending change email request.
          type: string
          format: email
        changeEmailContext:
          description: A 32-character string that is used to verify an email change.
          type: string
          format: ID
        passwordVersion:
          description: The current version of the password stored on the Account of the current user.
          type: integer
          format: uint32
        passwordContext:
          description: A 32-character string that is used to verify a password reset change.
          type: string
          format: ID
        firstName:
          description: The first name of the specified user.
          type: string
        lastName:
          description: The last name of the specified user.
          type: string
        displayName:
          description: The display name for the specified user.
          type: string
          pattern: '^[a-zA-Z]{3,25}$'
        subscribedNewsletters:
          description: The list of newsletters the specified user is subscribed to.
          type: array
          items:
            type: string
        pendingSubscribedNewsletters:
          description: The list of pending newsletters subscriptions for the specified user.
          type: array
          items:
            type: string
        defaultTwoFaType:
          description: The default Two-Factor Authentication method for the specified user.
          type: string
          example: sms
        twoFaCredentials:
          description: The Two-Factor Authentication credentials associated with the Two-Factor Authentication type being used for the Account of the specified user.
          type: object
          properties:
            twoFaType:
              type: object
              properties:
                credential:
                  description: The Two-Factor Authentication credentials.
                  type: string
                isVerified:
                  description: Whether the credentials have been verified.
                  type: boolean
                  enum: [true, false]
        country:
          description: The country for the specified user.
          type: string
          format: XX
          example: 'US'
        onlineServiceType:
          description: "The online service type of the specified Platform Account.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - XBOX LIVE\n\n
            2 - SONY ENTERTAINMENT NETWORK\n\n
            3 - STEAM\n\n
            4 - WEB\n\n
            5 - LEGACY GAME CENTER\n\n
            6 - GOOGLE PLAY\n\n
            9 - WINDOWS PHONE\n\n
            10 - CALICO\n\n
            11 - NINTENDO\n\n
            12 - GAME CENTER\n\n
            13 - WEGAME\n\n
            14 - VORTEX\n\n
            15 - EPIC\n\n
            16 - STADIA\n\n
            17 - FACEBOOK\n\n
            18 - GOOGLE\n\n
            19 - TWITTER\n\n
            20 - TWITCH\n\n
            21 - DEVICE\n\n
            22 - APPLE\n\n
            23 - ZENDESK\n\n
            24 - T2GP\n\n
            99 - WINDOWS DEVELOPER"
          type: integer
          format: uint32
        anonymousId:
          description: The Anonymous ID of the Platform Account if the Legal Documents have not been agreed to for the Account (Account Type is 1).
          type: string
          format: ID
        firstPartyId:
          description: The First Party ID of the specified Platform Account. For Device Accounts, the Device ID will be displayed.
          type: string
        firstPartyAlias:
          description: The First Party alias of the specified Platform Account. For Device Accounts, the Device Name will be displayed. Full Accounts will not have an alias.
          type: string
        parentAccountId:
          description: The Parent Account ID of the specified Platform Account if it is linked.
          type: string
          format: ID
    LegalResponseRequest:
      type: object
      required:
      - documentId
      - isAccepted
      properties:
        documentId:
          description: The Legal Document ID, which can be obtained from the 'Location' response header when the Legal Document is created.
          type: string
          format: ID
        isAccepted:
          description: Whether the current user has accepted the Legal Document.
          type: boolean
          enum: [true, false]
        type:
          description: "The Legal Document type.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - TERMS OF SERVICE\n\n
            2 - PRIVACY POLICY\n\n
            3 - END USER LICENSE AGREEMENT\n\n
            4 - DEMO\n\n
            5 - OPTIONAL\n\n
            6 - CUSTOM\n\n
            7 - RESERVED"
          type: integer
          format: uint32
        appId:
          description: The Application ID associated with the Legal Document.
          type: string
          format: ID
    SearchAccountRequest:
      type: object
      required:
        - type
        - criterias
      properties:
        type:
          description: "The type of criteria to be searched.\n\n\n
            List of types for reference:\n\n
            - accountsById - Retrieves a list of Accounts by Account ID.\n\n
            - accountsByFirstPartyId - Retrieves a list of Accounts by First-Party ID.\n\n
            - fullAccountIdByFirstPartyId - Retrieves a list of Full Accounts that are linked to the provided First-Party IDs.\n\n
            - accountsByParentAccountId - Retrieves a list of Full Accounts and all platform account that are linked to the provided Parent Account IDs.\n\n
            - accountsByFirstPartyAlias - Retrieves a list of Accounts by First-Party Alias."
          type: string
          enum: [accountsById, accountsByFirstPartyId, fullAccountIdByFirstPartyId, accountsByParentAccountId, accountsByFirstPartyAlias]
        criterias:
          description: The criteria to be searched.
          type: array
          items:
            type: object
            properties:
              accountId:
                description: The Account ID to search with, which can be obtained from either the Account Login response body or the 'Location' response header when the Account was created and required when using the 'accountsById' search type.
                type: string
              firstPartyId:
                description: The First-Party ID to search with, which can be obtained from the Account details if the Account Type is 2 and required when using the 'accountsByFirstPartyId' or 'fullAccountIdByFirstPartyId'search type.
                type: string
              onlineServiceType:
                description: The Online Service Type to search with, which can be obtained from the Account details, and required when using the 'accountsByFirstPartyId', 'accountsByFirstPartyAlias' or 'fullAccountIdByFirstPartyId'search type.
                type: integer
              parentAccountId:
                description: The Parent Account ID to search with, which can be obtained from the Account details and required when using the 'accountsByParentAccountId'.
                type: string
              firstPartyAlias:
                description: The First-Party Alias to search with, which can be obtained from the Account details if the Account Type is 2.
                type: string
    SearchAccountResponse:
      type: object
      properties:
        accountList:
          description: The list of Accounts when the 'accountsById', 'accountsByFirstPartyId', 'accountsByParentAccountId',  or  'accountsByFirstPartyAlias' search type is requested.
          type: array
          items:
            type: object
            properties:
              id:
                description: The Account ID, which was generated when the listed Account was created.
                type: string
                format: ID
              type:
                description: "The type of Account.\n\n\n
                  List of types for reference:\n\n
                  0 - UNKNOWN\n\n
                  1 - ANONYMOUS\n\n
                  2 - PLATFORM\n\n
                  3 - FULL\n\n
                  4 - TOOLS\n\n
                  5 - NEWSLETTER\n\n
                  6 - UNDISCLOSED\n\n
                  7 - PRIVACY POLICY ACCEPTED ONLY"
                type: integer
                format: uint32
              dob:
                description: The date of birth for the listed Account.
                type: string
                format: mm/dd/yyyy
              locale:
                description: The locale for the listed Account.
                type: string
                format: xx-XX
                example: 'en-US'
              createdOn:
                description: The UNIX timestamp that indicates when the listed Account was created.
                type: integer
                format: uint64
              email:
                description: The email address of the listed Account.
                type: string
                format: email
              onlineServiceType:
                description: "The online service type of the specified Platform Account.\n\n\n
                  List of types for reference:\n\n
                  0 - UNKNOWN\n\n
                  1 - XBOX LIVE\n\n
                  2 - SONY ENTERTAINMENT NETWORK\n\n
                  3 - STEAM\n\n
                  4 - WEB\n\n
                  5 - LEGACY GAME CENTER\n\n
                  6 - GOOGLE PLAY\n\n
                  9 - WINDOWS PHONE\n\n
                  10 - CALICO\n\n
                  11 - NINTENDO\n\n
                  12 - GAME CENTER\n\n
                  13 - WEGAME\n\n
                  14 - VORTEX\n\n
                  15 - EPIC\n\n
                  16 - STADIA\n\n
                  17 - FACEBOOK\n\n
                  18 - GOOGLE\n\n
                  19 - TWITTER\n\n
                  20 - TWITCH\n\n
                  21 - DEVICE\n\n
                  22 - APPLE\n\n
                  23 - ZENDESK\n\n
                  24 - T2GP\n\n
                  99 - WINDOWS DEVELOPER"
                type: integer
                format: uint32
              firstPartyId:
                description: The First Party ID of the listed Platform Account. For Device Accounts, the Device ID will be displayed.
                type: string
              firstPartyAlias:
                description: The First Party alias of the listed Platform Account. For Device Accounts, the Device Name will be displayed. Full Accounts will not have an alias.
                type: string
              parentAccountId:
                description: The Parent Account ID of the listed Platform Account.
                type: string
                format: ID
        fullAccountIdMap:
          description: An object map of First-Party IDs and the Full Account each one is linked to, and this response is given when the 'fullAccountIdByFirstPartyId' search type is requested.
          type: object
          additionalProperties:
            type: string
          example:
            'firstPatyId12345': '00000000000000000000000000000001'
    GetBatchFirstPartyResponse:
      type: object
      properties:
        id:
          description: The Account ID, which was generated when the specified Account was created.
          type: string
          format: ID
        type:
          description: "The online service type of the specified Platform Account.\n\n\n
            List of types for reference:\n\n
            0 - UNKNOWN\n\n
            1 - XBOX LIVE\n\n
            2 - SONY ENTERTAINMENT NETWORK\n\n
            3 - STEAM\n\n
            4 - WEB\n\n
            5 - LEGACY GAME CENTER\n\n
            6 - GOOGLE PLAY\n\n
            9 - WINDOWS PHONE\n\n
            10 - CALICO\n\n
            11 - NINTENDO\n\n
            12 - GAME CENTER\n\n
            13 - WEGAME\n\n
            14 - VORTEX\n\n
            15 - EPIC\n\n
            16 - STADIA\n\n
            17 - FACEBOOK\n\n
            18 - GOOGLE\n\n
            19 - TWITTER\n\n
            20 - TWITCH\n\n
            21 - DEVICE\n\n
            22 - APPLE\n\n
            23 - ZENDESK\n\n
            24 - T2GP\n\n
            99 - WINDOWS DEVELOPER"
          type: integer
          format: uint32
        firstPartyId:
          description: The First Party ID of the specified Platform Account. For Device Accounts, the Device ID will be displayed.
          type: string
        alias:
          description: The First Party alias of the specified Platform Account. For Device Accounts, the Device Name will be displayed.
          type: string
        linkedPlatformAccounts:
          description: The list of linked Platform and Device Accounts for the specified Full Account ID.
          type: array
          items:
              type: object
              properties:
                id:
                  description: The Account ID, which was generated when the specified Account was created.
                  type: string
                  format: ID
                type:
                  description: "The online service type of the specified Platform Account.\n\n\n
                    List of types for reference:\n\n
                    0 - UNKNOWN\n\n
                    1 - XBOX LIVE\n\n
                    2 - SONY ENTERTAINMENT NETWORK\n\n
                    3 - STEAM\n\n
                    4 - WEB\n\n
                    5 - LEGACY GAME CENTER\n\n
                    6 - GOOGLE PLAY\n\n
                    9 - WINDOWS PHONE\n\n
                    10 - CALICO\n\n
                    11 - NINTENDO\n\n
                    12 - GAME CENTER\n\n
                    13 - WEGAME\n\n
                    14 - VORTEX\n\n
                    15 - EPIC\n\n
                    16 - STADIA\n\n
                    17 - FACEBOOK\n\n
                    18 - GOOGLE\n\n
                    19 - TWITTER\n\n
                    20 - TWITCH\n\n
                    21 - DEVICE\n\n
                    22 - APPLE\n\n
                    23 - ZENDESK\n\n
                    24 - T2GP\n\n
                    99 - WINDOWS DEVELOPER"
                  type: integer
                  format: uint32
                firstPartyId:
                  description: The First Party ID of the specified Platform Account. For Device Accounts, the Device ID will be displayed.
                  type: string
                alias:
                  description: The First Party alias of the specified Platform Account. For Device Accounts, the Device Name will be displayed.
                  type: string
    GetAccountAppDataResponse:
      type: object
      properties:
        appId:
          description: The Application ID associated with the data, which was generated when the Application was created.
          type: string
          format: ID
        accountId:
          description: The Account ID associated with the data, which was generated when the Account was created.
          type: string
          format: ID
        numLogins:
          description: The total number of logins performed under this Application for the specified user.
          type: number
          format: uint32
        lastLogin:
          description: The UNIX timestamp that indicates when the last successful login occurred.
          type: integer
          format: uint64
    GetLegacyTokenResponse:
      type: object
      properties:
        accessToken:
          description: The value of the specified Access or Refresh Token, which was generated and issued upon successful login.
          type: string
          format: ID
        createdOn:
          description: The UNIX timestamp that indicates when the specified Access or Refresh Token was generated.
          type: integer
          format: uint64
        expiresOn:
          description: The UNIX timestamp that indicates when the specified Access or Refresh Token expires.
          type: integer
          format: uint64
        appId:
          description: The Application ID that issued the specified token.
          type: string
          format: ID
        appGroupId:
          description: The Application Group that issued the specified token.
          type: string
          format: ID
        accountId:
          description: The Account ID to which the specified token was issued.
          type: string
          format: ID
        parentAccountId:
          description: The Parent Account of the Account associated with the specified token if applicable.
          type: string
          format: ID
        acls:
          description: The ACLs assigned to the specified token.
          type: array
          items:
            type: string
    GetTokenResponse:
      type: object
      properties:
        state:
          description: The state of the specified token.
          type: string
          example: 'revoked'
        jti:
          description: The Access Token or Refresh Token ID that was generated when the specified token was created.
          type: string
          format: ID
    GetRevokedTokenStateResponse:
        type: object
        properties:
          revoked:
            description: The state of the specified revoked token.
            type: boolean
            enum: [true, false]
    GetZendeskTokenResponse:
      type: object
      properties:
        jwt:
          description: The JWT that has been issued to the specified user.
          type: string
          format: jwt
    AccountEmailSendRequest:
      type: object
      properties:
        template:
          description: The email template to be used in the request.
          type: string
        locale:
          description: Localizes the email based on the specified locale.
          type: string
          format: xx-XX
          example: 'en-US'
        toAddress:
          description: The email address to which the email will be sent.
          type: string
          format: email
        toAccountId:
          description: The Account ID to be used to look up the email address to send the email to, which can be obtained from either the Account Login response body or the 'Location' response header when the Account was created.
          type: string
          format: ID
        variables:
          description: A mapping of variable names to values which will be injected into the email template.
          type: object
        gamertagVariables:
          description: A mapping of variable names to Account IDs which will be used to look up First-Party Gamertags, and subsequently injected into the email template.
          type: object
    UpdateEmailRequest:
      type: object
      required:
        - appId
        - pendingEmail
      properties:
        pendingEmail:
          description: The email address the current user has requested to change to.
          type: string
          format: email
        emailBaseUrl:
          description: Supports the ability to override the base URL when creating a link for email user notification such as Account verification, reset password, update email etc.
          type: string
        additionalQueryParams:
          description: A map of query parameters that will be added to email URL.
          example: {'client_id' : '12345', 'state' : 'state'}
        appId:
          description: The Application ID that was used to login the Account for the specified user.
          type: string
          format: ID
    DeviceAuthorizationPatchRequest:
      type: object
      properties:
        userCode:
          description: The end-user verification code.
          type: string
          example: 'BDWP-HQPK'
        accountId:
          description: The Account ID of the Device Account associated with the current user.
          type: string
        status:
          description: The status of device authorizations.
          enum: [authorized, denied]
          example: 'authorized'
    ValidateUserCodeResponse:
      type: object
      properties:
        clientId:
          description: The Application ID associated with the Device console login/registration.
          type: string
 
    ErrorResponse:
      type: object
      required:
        - code
        - message
      properties:
        code:
          description: A code representing the error that occurred.
          type: integer
          format: uint32
        message:
          description: A string describing the error that occurred.
          type: string
 
  parameters:
    offset:
      name: offset
      in: query
      description: The pagination offset to use.
      required: false
      schema:
        type: integer
        format: uint32
    limit:
      name: limit
      in: query
      description: The pagination limit to use.
      required: false
      schema:
        type: integer
        format: uint32
    historyAccount:
      name: X-2k-History-Account-ID
      in: header
      description: The Account ID that made the change.
      required: false
      schema:
        type: string
    historyReason:
      name: X-2k-History-Reason
      in: header
      description: The reason the change is being made.
      required: false
      schema:
        type: string
    productId:
      name: productId
      in: path
      required: true
      description: The Product ID, which can be obtained from the 'Location' response header when the specified Product was created.
      schema:
        type: string
        format: ID
        example: 0267c399ba2d43f5a1d0514666a1e120
    appIds:
      name: appIds
      in: query
      description: The comma-separated list of Application IDs to search with, and these can be obtained from the 'Location' response header when the Application was created.
      required: false
      schema:
        type: array
        items:
          type: string
          format: ID
          example: 378204e6d75143179dc7e42f0cac0954, 87ef231ab74d4bafafd6f28a0b445e41
    appId:
      name: appId
      in: path
      required: true
      description: The Application ID, which can be obtained from the 'Location' response header when the specified Application was created.
      schema:
        type: string
        format: ID
        example: 378204e6d75143179dc7e42f0cac0954
    accountId:
      name: accountId
      in: path
      description: The Account ID, which can be obtained from either the Account Login response body or the 'Location' response header when the Account was created.
      required: true
      schema:
        type: string
        format: ID
        example: a1acf868a9d34bcc95ded15498e5a507
    tokenAccountId:
      name: accountId
      in: query
      description: The Account ID to be used in the query, which can be obtained from either the Account Login response body or the 'Location' response header when the Account was created.
      required: true
      schema:
        type: string
        format: ID
        example: a1acf868a9d34bcc95ded15498e5a507
    accessToken:
      name: accessToken
      in: path
      description: The Legacy Access Token or Refresh Token that was generated and issued upon successful login.
      required: true
      schema:
        type: string
        format: ID
        example: f053654a30c647f0920221b00893337c
    jwtId:
      name: jwtId
      in: path
      description: The Access Token or Refresh Token ID that was generated when the token was created, which can be found in the 'jti' field when viewing a JWT.
      required: true
      schema:
        type: string
        format: ID
        example: 7ab56f1aaed4488794182ca98a7e1089
    tokenId:
      name: token
      in: query
      description: The Access Token or Refresh Token ID that was generated when the token was created, which can be found in the 'jti' field when viewing a JWT.
      required: true
      schema:
        type: string
        format: ID
        example: 89b7dc5a699a41ab8658f13e3c0d1f99
    createdOn:
      name: createdOn
      in: query
      required: true
      description: The UNIX timestamp that indicates when the Access or Refresh Token was first issued, which can be found in the 'iat' field when viewing a JWT.
      schema:
        type: integer
        format: uint64
    zendeskUserToken:
      name: user_token
      in: query
      description: The Zendesk User ID for the current user.
      required: true
      content:
        application/x-www-form-urlencoded:
          schema:
            type: string
    zendeskName:
      name: name
      in: query
      description: The Zendesk Name for the current user.
      required: true
      content:
        application/x-www-form-urlencoded:
          schema:
            type: string
    zendeskEmail:
      name: email
      in: query
      description: The Zendesk Email for the current user.
      required: true
      content:
        application/x-www-form-urlencoded:
          schema:
           type: string
 
  responses:
    '400':
      description: Bad Request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '401':
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '403':
      description: Forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '404':
      description: Not Found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '409':
      description: Conflict
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '415':
      description: Unsupported Media Type
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '429':
      description: Too Many Requests
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    '500':
      description: Internal Server Error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
