<script>
  import { getContext } from 'svelte';
  import { CONTEXT_KEY_TABS } from '../../constant';

  const tab = {};
  const { registerTab, selectTab, selectedTab } = getContext(CONTEXT_KEY_TABS);

  const onTabClick = () => {
    selectTab(tab);
  };

  registerTab(tab);
</script>

<style>
  button {
    display: block;
    padding: 1rem;
    margin: 0;
    text-align: start;
    box-sizing: border-box;
    color: rgba(255, 255, 255, 0.6);
    font-weight: 900;
    font-size: 0.875rem;
    line-height: 125%;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    border: none;
    border-left: 0.25rem solid transparent;
    background-color: transparent;
  }

  button:active {
    background-color: transparent;
  }

  button:hover {
    border-radius: 0.25rem;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
  }

  .selected {
    color: rgba(255, 255, 255, 1);
    font-weight: 900;
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 0.25rem solid var(--social-color, var(--default-color));
  }
</style>

<button class:selected="{$selectedTab === tab}" on:click="{onTabClick}">
  <slot />
</button>
